# ========== 资源管理 ==========
build --local_resources=cpu=HOST_CPUS*.8
build --local_resources=memory=HOST_RAM*.8
build --enable_platform_specific_config

# ========== 构建配置 ==========
build:macos --host_cxxopt="-mmacosx-version-min=10.13"
build --stamp
build --workspace_status_command=./bazel/tools/workspace_status.sh
build --symlink_prefix=./bazel/out/
build --jobs=auto
build --color=yes
build:linux --features=fully_static_link
build --action_env=CGO_ENABLED=0
build --incompatible_strict_action_env
build --experimental_inmemory_dotd_files
build --experimental_inmemory_jdeps_files

# ========== gazelle ==========
# 减少 Gazelle 运行时的输出噪音
build:gazelle --noshow_progress
build:gazelle --noshow_loading_progress
build:gazelle --ui_event_filters=-info,-debug

# ========== 缓存优化 ==========
build --disk_cache=~/.cache/bazel
build --repository_cache=~/.cache/bazel-repo
build --remote_cache_compression
# 防止多个 Bazel 进程同时修改相同的输出文件
build --guard_against_concurrent_changes
# 优化远程缓存性能（仅当使用远程缓存时有效）
build --experimental_remote_merkle_tree_cache

# ========== go 工具链 ==========
build --@io_bazel_rules_go//go/config:pure=true
build --@io_bazel_rules_go//go/config:static=true
build --@io_bazel_rules_go//go/config:tags=jsoniter,sonic
build --@io_bazel_rules_go//go/config:race=false

# ========== Python 工具链 ==========
# 解决 CI 环境中 root 用户问题
build --action_env=RULES_PYTHON_IGNORE_ROOT_USER_ERROR=1 
# ========== 沙盒系统 ==========
build --experimental_reuse_sandbox_directories
build --nobuild_runfile_links

# ========== 测试配置 ==========
test --test_output=errors             # 仅显示错误详情
test --test_verbose_timeout_warnings
test --test_strategy=exclusive        # 隔离测试环境
test --test_arg=-test.parallel=8 
# ========== 覆盖率 ==========
# coverage --instrumentation_filter=//backend/app/...  # 限定插桩范围
coverage --test_arg=-test.parallel=8  #
coverage --combined_report=lcov
coverage --coverage_report_generator=@bazel_tools//tools/test/CoverageOutputGenerator/java/com/google/devtools/coverageoutputgenerator:Main
coverage --experimental_fetch_all_coverage_outputs
