syntax = "proto3";

// This is a fake go_package that would be ignored when specifying
// ignore_go_package_option.
option go_package = "foo.com/bar/blabla;blu";

// Mixing imports from google and local repository will work just fine.
import "google/protobuf/any.proto";
import "tests/legacy/proto_ignore_go_package_option/a.proto";

package bar;

message Bar {
  foo.Foo foo = 1;
  google.protobuf.Any a = 2;
}
