load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

test_suite(
    name = "importmap",
)

go_library(
    name = "lib_a",
    srcs = ["lib.go"],
    importmap = "a/lib",
    importpath = "lib",
    x_defs = {"Value": "ValueA"},
)

go_library(
    name = "lib_b",
    srcs = ["lib.go"],
    importmap = "b/lib",
    importpath = "lib",
    x_defs = {"Value": "ValueB"},
)

go_library(
    name = "a",
    srcs = ["import.go"],
    importpath = "a",
    deps = [":lib_a"],
)

go_library(
    name = "b",
    srcs = ["import.go"],
    importpath = "b",
    deps = [":lib_b"],
)

go_test(
    name = "importmap_test",
    size = "small",
    srcs = ["importmap_test.go"],
    deps = [
        ":a",
        ":b",
    ],
)
