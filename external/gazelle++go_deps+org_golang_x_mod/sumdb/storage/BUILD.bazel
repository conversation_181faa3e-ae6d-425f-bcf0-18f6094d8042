load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "storage",
    srcs = [
        "mem.go",
        "storage.go",
        "test.go",
    ],
    importpath = "golang.org/x/mod/sumdb/storage",
    visibility = ["//visibility:public"],
)

alias(
    name = "go_default_library",
    actual = ":storage",
    visibility = ["//visibility:public"],
)

go_test(
    name = "storage_test",
    srcs = ["mem_test.go"],
    embed = [":storage"],
)
