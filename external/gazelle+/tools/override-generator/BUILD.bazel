load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library", "go_test")

go_binary(
    name = "override-generator",
    embed = [":override-generator_lib"],
    visibility = ["//visibility:public"],
)

go_test(
    name = "override-generator_test",
    srcs = ["main_test.go"],
    embed = [":override-generator_lib"],
)

go_library(
    name = "override-generator_lib",
    srcs = ["main.go"],
    importpath = "github.com/bazelbuild/bazel-gazelle/tools/override-generator",
    visibility = ["//visibility:private"],
    deps = [
        "//repo",
        "//rule",
        "@com_github_bazelbuild_buildtools//build",
    ],
)

filegroup(
    name = "all_files",
    testonly = True,
    srcs = [
        "BUILD.bazel",
        "README.md",
        "main.go",
        "main_test.go",
    ],
    visibility = ["//visibility:public"],
)
