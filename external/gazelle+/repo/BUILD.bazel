load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "repo",
    srcs = [
        "remote.go",
        "repo.go",
    ],
    importpath = "github.com/bazelbuild/bazel-gazelle/repo",
    visibility = ["//visibility:public"],
    deps = [
        "//label",
        "//pathtools",
        "//rule",
        "@org_golang_x_mod//modfile",
        "@org_golang_x_tools_go_vcs//:vcs",
    ],
)

go_test(
    name = "repo_test",
    srcs = [
        "remote_test.go",
        "repo_test.go",
        "stubs_test.go",
    ],
    embed = [":repo"],
    deps = [
        "//pathtools",
        "//rule",
        "//testtools",
        "@org_golang_x_tools_go_vcs//:vcs",
    ],
)

filegroup(
    name = "all_files",
    testonly = True,
    srcs = [
        "BUILD.bazel",
        "remote.go",
        "remote_test.go",
        "repo.go",
        "repo_test.go",
        "stubs_test.go",
    ],
    visibility = ["//visibility:public"],
)

alias(
    name = "go_default_library",
    actual = ":repo",
    visibility = ["//visibility:public"],
)
