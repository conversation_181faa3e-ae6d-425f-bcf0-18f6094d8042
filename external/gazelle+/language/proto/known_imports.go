// Generated by language/proto/gen/gen_known_imports.go
// From language/proto/proto.csv

package proto

import "github.com/bazelbuild/bazel-gazelle/label"

var knownImports = map[string]label.Label{

	"google/protobuf/any.proto":             label.Label{Repo: "com_google_protobuf", Pkg: "", Name: "any_proto"},
	"google/protobuf/api.proto":             label.Label{Repo: "com_google_protobuf", Pkg: "", Name: "api_proto"},
	"google/protobuf/compiler/plugin.proto": label.Label{Repo: "com_google_protobuf", Pkg: "", Name: "compiler_plugin_proto"},
	"google/protobuf/descriptor.proto":      label.Label{Repo: "com_google_protobuf", Pkg: "", Name: "descriptor_proto"},
	"google/protobuf/duration.proto":        label.Label{Repo: "com_google_protobuf", Pkg: "", Name: "duration_proto"},
	"google/protobuf/empty.proto":           label.Label{Repo: "com_google_protobuf", Pkg: "", Name: "empty_proto"},
	"google/protobuf/field_mask.proto":      label.Label{Repo: "com_google_protobuf", Pkg: "", Name: "field_mask_proto"},
	"google/protobuf/source_context.proto":  label.Label{Repo: "com_google_protobuf", Pkg: "", Name: "source_context_proto"},
	"google/protobuf/struct.proto":          label.Label{Repo: "com_google_protobuf", Pkg: "", Name: "struct_proto"},
	"google/protobuf/timestamp.proto":       label.Label{Repo: "com_google_protobuf", Pkg: "", Name: "timestamp_proto"},
	"google/protobuf/type.proto":            label.Label{Repo: "com_google_protobuf", Pkg: "", Name: "type_proto"},
	"google/protobuf/wrappers.proto":        label.Label{Repo: "com_google_protobuf", Pkg: "", Name: "wrappers_proto"},
}
