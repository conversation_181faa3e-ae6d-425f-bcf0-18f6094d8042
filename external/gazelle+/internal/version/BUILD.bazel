load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "version",
    srcs = ["version.go"],
    importpath = "github.com/bazelbuild/bazel-gazelle/internal/version",
    visibility = ["//:__subpackages__"],
)

go_test(
    name = "version_test",
    srcs = ["version_test.go"],
    embed = [":version"],
)

filegroup(
    name = "all_files",
    testonly = True,
    srcs = [
        "BUILD.bazel",
        "version.go",
        "version_test.go",
    ],
    visibility = ["//visibility:public"],
)

alias(
    name = "go_default_library",
    actual = ":version",
    visibility = ["//:__subpackages__"],
)
