load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "html2article_lib",
    srcs = ["conv.go"],
    importpath = "golang.org/x/tools/cmd/html2article",
    visibility = ["//visibility:private"],
    deps = [
        "@org_golang_x_net//html",
        "@org_golang_x_net//html/atom",
    ],
)

go_binary(
    name = "html2article",
    embed = [":html2article_lib"],
    visibility = ["//visibility:public"],
)
