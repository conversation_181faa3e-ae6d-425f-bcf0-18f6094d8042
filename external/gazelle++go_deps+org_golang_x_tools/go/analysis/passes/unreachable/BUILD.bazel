load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "unreachable",
    srcs = [
        "doc.go",
        "unreachable.go",
    ],
    embedsrcs = ["doc.go"],
    importpath = "golang.org/x/tools/go/analysis/passes/unreachable",
    visibility = ["//visibility:public"],
    deps = [
        "//go/analysis",
        "//go/analysis/passes/inspect",
        "//go/analysis/passes/internal/analysisutil",
        "//go/ast/inspector",
    ],
)

alias(
    name = "go_default_library",
    actual = ":unreachable",
    visibility = ["//visibility:public"],
)

go_test(
    name = "unreachable_test",
    srcs = ["unreachable_test.go"],
    deps = [
        ":unreachable",
        "//go/analysis/analysistest",
    ],
)
