v2;
package aliases;
prefix go;
package aliases go.aliases go.aliases;
type <type 1 "A0" = <type 2 "T0" <type 3 [10 ] <type -11>>
 func (? <esc:0x1> <type 2>) .go.aliases.m1 ();
 func (? <esc:0x1> <type 1>) .go.aliases.m2 ();
 func (? <esc:0x1> <type 4 "V1" = <type 5 "V2" = <type 6 "V3" = <type 2>>>>) .go.aliases.n ();
>>;
type <type 7 "A1" = <type 8 "T1" <type 9 [] <type -20>>>>;
type <type 10 "A10" = <type 11 [10 ] <type -11>>>;
type <type 12 "A11" = <type 13 [] <type -20>>>;
type <type 14 "A12" = <type 15 struct { .go.aliases.x <type -11>; }>>;
type <type 16 "A13" = <type 17 interface { .go.aliases.m () <type 18 "A2" = <type 19 "T2" <type 20 struct { .go.aliases.x <type -11>; }>>>; }>>;
type <type 21 "A14" = <type 22 (? <type -11>, ? <type 1>) <type 23 chan <type 18>>>>;
type <type 18>;
type <type 24 "A3" = <type 25 "T3" <type 26 interface { .go.aliases.m () <type 19>; }>>>;
type <type 27 "A4" = <type 28 "T4" <type 29 (? <type -11>, ? <type 2>) <type 30 chan <type 19>>>>>;
type <type 31 "Ai" = <type -11>>;
type <type 32 "C0" <type 33 struct { .go.aliases.f1 <type 34 "C1" <type 35 *<type 32>>>; .go.aliases.f2 <type 36 "C2" = <type 34>>; }>>;
type <type 34>;
type <type 36>;
type <type 37 "C5" <type 38 struct { .go.aliases.f <type 39 *<type 40 "C6" = <type 37>>>; }>>;
type <type 40>;
type <type 2>;
type <type 8>;
type <type 19>;
type <type 25>;
type <type 28>;
type <type 41 "V0" = <type 4>>;
type <type 4>;
type <type 5>;
type <type 6>;
