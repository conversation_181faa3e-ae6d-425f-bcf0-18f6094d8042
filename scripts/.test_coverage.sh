#!/bin/bash


# Purpose: 本脚本用于执行基于bazel的单元测试, 并生成覆盖率报告, 以及生成可视化的html文件
# 并且可以限制单测覆盖率

# <AUTHOR> <EMAIL>

set -euo pipefail

source scripts/common.sh
source scripts/.get_changed_apps.sh
# 限制单测覆盖率
COVERAGE_MIN=80

# 是否生成可视化html文件, 默认不生成
COVERAGE_HTML=1

# 可能有一些迁移到monorepo的目录没有做单测, 需要排除
EXCLUDE_DIRS=("temp" "tools" "gemini_mcp_client" "devops-auth")

# Function to format app directories
_remove_exclude() {
    local app_dirs_input="$1"
    local exclude_pattern="$2"
    echo "${app_dirs_input}" | tr ' ' '\n' | grep -v '^$' | grep -vE "${exclude_pattern}" | sed 's/^/\/\/backend\/app\//' | sed 's/$/\/logic\/.../' | tr '\n' ' ' || true
}

# 如果手动输入了目录, 则使用手动输入的目录
if [[ -n "${2:-}" ]]; then
    echo "use input dirs"
    APP_DIRS=${2}
else
    # 获取发生了变更的目录
    APP_DIRS=$(changed_apps backend)
    EXCLUDE_PATTERN=$(IFS='|'; echo "${EXCLUDE_DIRS[*]}")
    echo "[INFO] Exclude pattern: ${EXCLUDE_PATTERN}"
    echo "[INFO] Changed apps: ${APP_DIRS}"
    APP_DIRS=$(_remove_exclude "${APP_DIRS}" "${EXCLUDE_PATTERN}")
    echo "[INFO] APP_DIRS after exclusion: '${APP_DIRS}'"
    if [[ -z "$(echo "${APP_DIRS}" | tr -d '[:space:]')" ]]; then
        echo -e "${GREEN}[INFO] no changed apps after exclusion, skip test coverage check${NC}"
        exit 0
    fi
fi

# 检查 app_dirs中是否有targets，for循环
IGNORE_DIRS=""
for app_dir in ${APP_DIRS}; do
    # 检查是否存在 targets
    if ! ${BAZEL_COMMAND} query "${app_dir}" > /dev/null 2>&1; then
        echo -e "${YELLOW}[WARNING] ${app_dir} has no test targets${NC}"
        # 将app_dir放进empty target dirs
        IGNORE_DIRS="${IGNORE_DIRS} ${app_dir}"
    fi
done

for ignore_dir in ${IGNORE_DIRS}; do
    APP_DIRS=$(echo "${APP_DIRS}" | sed "s|${ignore_dir}||g")
done

echo -e "${GREEN}[INFO] APP_DIRS without IGNORE_DIRS:${NC}${APP_DIRS}"

if [[ -z "$(echo "${APP_DIRS}" | tr -d '[:space:]')" ]]; then
    echo -e "${GREEN}[INFO] no test targets in changed apps, skip test coverage check${NC}"
    exit 0
fi

echo ${BAZEL_COMMAND} coverage ${APP_DIRS}
${BAZEL_COMMAND} coverage ${APP_DIRS}

echo -e "${YELLOW}[INFO] coverage done${NC}"

# 统计覆盖率
coverage_summary=$(lcov --summary $(${BAZEL_COMMAND} info output_path)/_coverage/_coverage_report.dat)

line=$(echo "${coverage_summary}" | grep -E "lines")
function=$(echo "${coverage_summary}" | grep -E "functions")

line_coverage=$(echo "${line}" |grep -o '[0-9]\+\.[0-9]\+' | cut -d. -f1)
function_coverage=$(echo "${function}" |grep -o '[0-9]\+\.[0-9]\+' | cut -d. -f1|| echo 100)


echo -e "${YELLOW}[INFO]${line}${NC}"
echo -e "${YELLOW}[INFO]${function} ${NC}"

# 生成html 单测覆盖率报告
if [[ "${COVERAGE_HTML}" == "1" ]]; then
    genhtml --branch-coverage --output bazel/test-report $(${BAZEL_COMMAND} info output_path)/_coverage/_coverage_report.dat > /dev/null
    echo -e "${GREEN}[INFO] generate test report success, command: open bazel/test-report/index.html ${NC}"
fi

# 判断是否包含app=customer，如果是则跳过限制
if echo "${APP_DIRS}" | grep -q "customer"; then
    echo "[INFO] app=customer detected, skipping coverage limit check"
else
    # 限制覆盖率
    if [[ "${COVERAGE_MIN}" -gt "${line_coverage}" ]]; then
        echo -e "${RED}[ERROR] coverage is less than ${COVERAGE_MIN}%${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}[INFO] unit test pass ${STAR}${NC}"
