package grpc

import (
	"context"
	"fmt"
	"reflect"
	"strings"

	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/peer"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/transport"
)

type Handler interface {
	Handle(srv interface{}, ctx context.Context, dec func(interface{}) error,
		interceptor grpc.UnaryServerInterceptor) (out interface{}, err error)
}

type handlerImpl struct {
	Handler transport.Handler
}

func (h *handlerImpl) Handle(_ interface{}, ctx context.Context, dec func(interface{}) error, // nolint
	interceptor grpc.UnaryServerInterceptor) (out interface{}, err error) {
	ctx, msg := h.withNewMessage(ctx)
	defer codec.PutBackMessage(msg)

	registerInfo, ok := grpcRegisterInfo[msg.CalleeService()]
	if !ok {
		return nil, fmt.Errorf("service %s not found", msg.CalleeService())
	}
	methodInfo, ok := registerInfo.MethodsInfo[msg.CalleeMethod()]
	if !ok {
		return nil, fmt.Errorf("method %s not found", msg.CalleeMethod())
	}

	req := reflect.New(methodInfo.ReqType).Interface()
	if err = dec(req); err != nil {
		return nil, err
	}

	header := &Header{
		Req:         req,
		InMetaData:  metadata.MD{},
		OutMetaData: metadata.MD{},
	}
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		header.InMetaData = md
		msg.WithServerMetaData(codec.MetaData(md))
	}
	ctx = WithHeader(ctx, header)

	if interceptor != nil {
		out, err = interceptor(ctx, req, &grpc.UnaryServerInfo{
			Server:     nil,
			FullMethod: msg.ServerRPCName(),
		}, func(ctx context.Context, req interface{}) (interface{}, error) {
			return h.processRequest(ctx, msg, header)
		})

		if err != nil {
			return nil, err
		}
		return out, nil
	}
	return h.processRequest(ctx, msg, header)
}

func (h *handlerImpl) processRequest(ctx context.Context, msg codec.Msg, header *Header) (interface{}, error) {
	_, err := h.Handler.Handle(ctx, []byte{})
	if err != nil {
		return nil, err
	}

	if msg.ServerRspErr() != nil {
		return header.Rsp, msg.ServerRspErr()
	}

	if header.OutMetaData != nil {
		if err := grpc.SendHeader(ctx, header.OutMetaData); err != nil {
			return nil, err
		}
	}

	return header.Rsp, nil
}

func (h *handlerImpl) withNewMessage(ctx context.Context) (context.Context, codec.Msg) {
	ctx, msg := codec.WithNewMessage(ctx)

	method, _ := grpc.Method(ctx)
	msg.WithServerRPCName(method)

	if pr, ok := peer.FromContext(ctx); ok {
		if addr := pr.Addr.String(); addr != "" {
			msg.WithRemoteAddr(pr.Addr)
		}
	}

	serviceName, methodName, err := getGRPCServiceAndMethodName(method)
	if err != nil {
		msg.WithServerRspErr(err)
		return ctx, msg
	}
	msg.WithCalleeService(serviceName)
	msg.WithCalleeMethod(methodName)

	return ctx, msg
}

func getGRPCMethod(serviceName, methodName string) string {
	return fmt.Sprintf("/%s/%s", serviceName, methodName)
}

func getGRPCServiceAndMethodName(method string) (string, string, error) {
	index := strings.LastIndex(method, "/")
	if index < 0 {
		return "", "", fmt.Errorf("invalid method: %s", method)
	}
	return method[1:index], method[index+1:], nil
}
