package grpc

import (
	"context"
	"errors"
	"fmt"
	"net"
	"sync"

	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/transport"
)

func init() {
	transport.RegisterServerTransport("grpc", DefaultServerTransport)
}

var DefaultServerTransport = NewServerTransport()

type ServerStreamTransport struct {
	opts *transport.ServerTransportOptions
}

func NewServerTransport(opt ...transport.ServerTransportOption) transport.ServerTransport {
	opts := &transport.ServerTransportOptions{}
	for _, o := range opt {
		o(opts)
	}
	s := &ServerStreamTransport{
		opts: opts,
	}

	return s
}

func (t *ServerStreamTransport) ListenAndServe(_ context.Context, opt ...transport.ListenServeOption) error {
	opts := &transport.ListenServeOptions{
		Network: "tcp",
	}
	for _, o := range opt {
		o(opts)
	}
	if opts.Handler == nil {
		return errors.New("handler is nil")
	}

	s, err := getOrCreateGrpcServer(opts.Address)
	if err != nil {
		return err
	}
	registerServices(s, opts)
	if len(s.GetServiceInfo()) == len(grpcRegisterInfo) {
		o := onceMux[opts.Address]
		o.Do(func() {
			reflection.Register(s)
			lis, err := net.Listen(opts.Network, opts.Address)
			if err != nil {
				panic(fmt.Sprintf("failed to listen: %s", err))
			}
			go func() {
				if err := s.Serve(lis); err != nil {
					log.Errorf("failed to serve: %v", err)
				}
			}()
		})
	}

	return nil
}

func registerServices(s *grpc.Server, opts *transport.ListenServeOptions) {
	serverFuncIn := &handlerImpl{
		Handler: opts.Handler,
	}

	serviceInfo, ok := grpcRegisterInfo[opts.ServiceName]
	if !ok {
		panic(fmt.Sprintf("%s not registered", opts.ServiceName))
	}
	var methodDesc []grpc.MethodDesc
	for methodName := range serviceInfo.MethodsInfo {
		methodDesc = append(methodDesc, grpc.MethodDesc{
			MethodName: methodName,
			Handler:    serverFuncIn.Handle,
		})
	}
	s.RegisterService(&grpc.ServiceDesc{
		ServiceName: opts.ServiceName,
		HandlerType: (*Handler)(nil),
		Methods:     methodDesc,
		Metadata:    serviceInfo.Metadata,
	}, serverFuncIn)
}

var (
	grpcServers = make(map[string]*grpc.Server)
	onceMux     = make(map[string]*sync.Once)
	serverMux   sync.RWMutex
)

func getOrCreateGrpcServer(address string) (*grpc.Server, error) {
	serverMux.RLock()
	if grpcServer, ok := grpcServers[address]; ok {
		serverMux.RUnlock()
		return grpcServer, nil
	}
	serverMux.RUnlock()

	serverMux.Lock()
	defer serverMux.Unlock()

	if grpcServer, ok := grpcServers[address]; ok {
		return grpcServer, nil
	}

	s := grpc.NewServer()

	grpcServers[address] = s
	onceMux[address] = &sync.Once{}
	return s, nil
}
