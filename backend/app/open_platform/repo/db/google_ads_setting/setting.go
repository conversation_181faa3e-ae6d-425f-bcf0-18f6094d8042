package googleadssetting

import (
	"context"
	"time"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/open_platform/repo/db"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

// ReadWriter defines the interface for GoogleAdsSetting database operations.
type ReadWriter interface {
	Create(ctx context.Context, setting *GoogleAdsSetting) error
	List(ctx context.Context, datum *ListDatum) ([]*GoogleAdsSetting, error)
	Update(ctx context.Context, setting *GoogleAdsSetting) error
	Delete(ctx context.Context, id int64, staffID int64) error
}

type impl struct {
	db *gorm.DB
}

// New creates a new instance of the GoogleAdsSetting ReadWriter.
func New() ReadWriter {
	return &impl{
		db: db.GetDB(), // Assuming db.GetDB() provides the GORM DB instance
	}
}

// Create inserts a new GoogleAdsSetting record into the database.
func (i *impl) Create(ctx context.Context, setting *GoogleAdsSetting) error {
	if err := i.db.WithContext(ctx).Create(setting).Error; err != nil {
		log.ErrorContextf(ctx, "Create GoogleAdsSetting err, err:%+v", err)

		return err
	}

	return nil
}

// ListDatum defines the criteria for listing GoogleAdsSetting records.
type ListDatum struct {
	CompanyID *int64
	// Add other potential filter fields here if needed
}

// List retrieves GoogleAdsSetting records based on the provided criteria.
func (i *impl) List(ctx context.Context, datum *ListDatum) ([]*GoogleAdsSetting, error) {
	query := i.db.WithContext(ctx).Table("google_ads_setting").Where("deleted_at IS NULL")

	// filter
	if datum.CompanyID != nil {
		query = query.Where("company_id = ?", *datum.CompanyID)
	}

	var res []*GoogleAdsSetting
	if err := query.Order("id desc").Find(&res).Error; err != nil {
		log.ErrorContextf(ctx, "List GoogleAdsSetting err, err:%+v", err)

		return nil, err
	}

	return res, nil
}

func (i *impl) Update(ctx context.Context, setting *GoogleAdsSetting) error {
	if err := i.db.WithContext(ctx).Updates(setting).Error; err != nil {
		log.ErrorContextf(ctx, "Update GoogleAdsSetting err, setting:%+v, err:%+v", setting, err)

		return err
	}

	return nil
}

// Delete performs a soft delete on a GoogleAdsSetting record.
func (i *impl) Delete(ctx context.Context, id int64, staffID int64) error {
	if err := i.db.WithContext(ctx).Table("google_ads_setting").
		Where("id = ?", id).
		Update("deleted_by", staffID).
		Update("deleted_at", time.Now()).
		Error; err != nil {
		log.ErrorContextf(ctx, "Delete GoogleAdsSetting err, id:%d, staffID:%d, err:%v", id, staffID, err)

		return err
	}

	return nil
}
