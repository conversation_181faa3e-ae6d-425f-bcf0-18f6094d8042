package token

import (
	"context"
	"time"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/open_platform/repo/db"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ReadWriter interface {
	Create(ctx context.Context, token *OauthToken) error
	List(ctx context.Context, datum *ListDatum) ([]*OauthToken, error)
	Delete(ctx context.Context, id int64, staffID int64) error
}

type impl struct {
	db *gorm.DB
}

func New() ReadWriter {
	return &impl{
		db: db.GetDB(),
	}
}

func (i *impl) Create(ctx context.Context, token *OauthToken) error {
	if err := i.db.WithContext(ctx).Create(token).Error; err != nil {
		log.ErrorContextf(ctx, "Create OauthToken err, err:%+v", err)

		return err
	}

	return nil
}

type ListDatum struct {
	CompanyID *int64
	Type      *OauthTokenType
}

func (i *impl) List(ctx context.Context, datum *ListDatum) ([]*OauthToken, error) {
	query := i.db.WithContext(ctx).Table("oauth_tokens").Where("deleted_at IS NULL")

	// filter
	if datum.CompanyID != nil {
		query = query.Where("company_id = ?", *datum.CompanyID)
	}
	if datum.Type != nil {
		query = query.Where("type = ?", *datum.Type)
	}

	var res []*OauthToken
	if err := query.Order("id desc").Find(&res).Error; err != nil {
		log.ErrorContextf(ctx, "List OauthToken err, err:%+v", err)

		return nil, err
	}

	return res, nil
}

func (i *impl) Delete(ctx context.Context, id int64, staffID int64) error {
	if err := i.db.WithContext(ctx).Table("oauth_tokens").
		Where("id = ?", id).
		Update("deleted_by", staffID).
		Update("deleted_at", time.Now()).
		Error; err != nil {
		log.ErrorContextf(ctx, "Delete OauthToken err, id:%d, staffID:%d, err:%v", id, staffID, err)

		return err
	}

	return nil
}
