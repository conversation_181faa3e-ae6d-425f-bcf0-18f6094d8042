load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mock",
    srcs = [
        "google_mock.go",
        "grpc_conn_mock.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/open_platform/repo/google/mock",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_shenzhencenter_google_ads_pb//services",
        "@org_golang_google_api//oauth2/v2:oauth2",
        "@org_golang_x_oauth2//:oauth2",
        "@org_uber_go_mock//gomock",
    ],
)
