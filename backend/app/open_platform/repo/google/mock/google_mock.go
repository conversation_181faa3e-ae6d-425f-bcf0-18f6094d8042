// Code generated by MockGen. DO NOT EDIT.
// Source: ./google/google.go
//
// Generated by this command:
//
//	mockgen -source=./google/google.go -destination=./google/mock/google_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	services "github.com/shenzhencenter/google-ads-pb/services"
	gomock "go.uber.org/mock/gomock"
	oauth2 "golang.org/x/oauth2"
	oauth20 "google.golang.org/api/oauth2/v2"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// ExchangeCode mocks base method.
func (m *MockReadWriter) ExchangeCode(ctx context.Context, code string) (*oauth2.Token, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExchangeCode", ctx, code)
	ret0, _ := ret[0].(*oauth2.Token)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExchangeCode indicates an expected call of ExchangeCode.
func (mr *MockReadWriterMockRecorder) ExchangeCode(ctx, code any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExchangeCode", reflect.TypeOf((*MockReadWriter)(nil).ExchangeCode), ctx, code)
}

// GetGoogleAdsOAuth2Link mocks base method.
func (m *MockReadWriter) GetGoogleAdsOAuth2Link(ctx context.Context) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGoogleAdsOAuth2Link", ctx)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGoogleAdsOAuth2Link indicates an expected call of GetGoogleAdsOAuth2Link.
func (mr *MockReadWriterMockRecorder) GetGoogleAdsOAuth2Link(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGoogleAdsOAuth2Link", reflect.TypeOf((*MockReadWriter)(nil).GetGoogleAdsOAuth2Link), ctx)
}

// GetUserInfo mocks base method.
func (m *MockReadWriter) GetUserInfo(ctx context.Context, token *oauth2.Token) (*oauth20.Userinfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserInfo", ctx, token)
	ret0, _ := ret[0].(*oauth20.Userinfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserInfo indicates an expected call of GetUserInfo.
func (mr *MockReadWriterMockRecorder) GetUserInfo(ctx, token any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserInfo", reflect.TypeOf((*MockReadWriter)(nil).GetUserInfo), ctx, token)
}

// ListAccessibleCustomers mocks base method.
func (m *MockReadWriter) ListAccessibleCustomers(ctx context.Context, token *oauth2.Token) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAccessibleCustomers", ctx, token)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAccessibleCustomers indicates an expected call of ListAccessibleCustomers.
func (mr *MockReadWriterMockRecorder) ListAccessibleCustomers(ctx, token any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAccessibleCustomers", reflect.TypeOf((*MockReadWriter)(nil).ListAccessibleCustomers), ctx, token)
}

// Search mocks base method.
func (m *MockReadWriter) Search(ctx context.Context, token *oauth2.Token, loginCustomerID, query string) (*services.SearchGoogleAdsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Search", ctx, token, loginCustomerID, query)
	ret0, _ := ret[0].(*services.SearchGoogleAdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Search indicates an expected call of Search.
func (mr *MockReadWriterMockRecorder) Search(ctx, token, loginCustomerID, query any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Search", reflect.TypeOf((*MockReadWriter)(nil).Search), ctx, token, loginCustomerID, query)
}
