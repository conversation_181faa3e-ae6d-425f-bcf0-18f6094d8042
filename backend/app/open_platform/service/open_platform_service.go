package service

import (
	"context"

	"google.golang.org/protobuf/types/known/emptypb"

	"github.com/MoeGolibrary/moego/backend/app/open_platform/logic/oauth"
	openplatformpb "github.com/MoeGolibrary/moego/backend/proto/open_platform/v1"
)

type OpenPlatformService struct {
	ol *oauth.Logic
	openplatformpb.UnimplementedOpenPlatformServiceServer
}

func NewOpenPlatformService() *OpenPlatformService {
	return &OpenPlatformService{
		ol: oauth.New(),
	}
}

func (s *OpenPlatformService) GetGoogleAdsUserInfo(ctx context.Context,
	request *openplatformpb.GetGoogleAdsUserInfoRequest) (*openplatformpb.GetGoogleAdsUserInfoResponse, error) {
	datum, err := s.ol.GetGoogleAdsUserInfo(ctx, request.GetCompanyId())
	if err != nil {
		return nil, err
	}

	return &openplatformpb.GetGoogleAdsUserInfoResponse{
		UserInfo:   datum.UserInfo,
		AdsSetting: datum.AdsSetting,
	}, nil
}

func (s *OpenPlatformService) RevokeGoogleAdsOAuth(ctx context.Context,
	request *openplatformpb.RevokeGoogleAdsOAuthRequest) (*emptypb.Empty, error) {
	if err := s.ol.RevokeGoogleAdsOAuth(ctx, request.GetCompanyId(), request.StaffId); err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *OpenPlatformService) ListGoogleAdsAccounts(ctx context.Context,
	request *openplatformpb.ListGoogleAdsAccountsRequest) (*openplatformpb.ListGoogleAdsAccountsResponse, error) {
	customers, err := s.ol.ListGoogleAdsAccounts(ctx, request.GetCompanyId())
	if err != nil {
		return nil, err
	}

	return &openplatformpb.ListGoogleAdsAccountsResponse{
		Customers: customers,
	}, nil
}

func (s *OpenPlatformService) LinkGoogleAdsAccounts(ctx context.Context,
	request *openplatformpb.LinkGoogleAdsAccountsRequest) (*emptypb.Empty, error) {
	if err := s.ol.LinkGoogleAdsAccounts(ctx, &oauth.LinkGoogleAdsAccountsDatum{
		GoogleAdsAccountIDs: request.GoogleAdsCustomerIds,
		CompanyID:           request.GetCompanyId(),
		BusinessID:          request.GetBusinessId(),
		StaffID:             request.GetStaffId(),
	}); err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *OpenPlatformService) GetLinkSetting(ctx context.Context,
	request *openplatformpb.GetLinkSettingRequest) (*openplatformpb.GetLinkSettingResponse,
	error) {
	link, err := s.ol.GetLinkSetting(ctx, request.GetType())
	if err != nil {
		return nil, err
	}

	return &openplatformpb.GetLinkSettingResponse{
		Link: link,
	}, nil
}

func (s *OpenPlatformService) GetMetaAdsUserInfo(ctx context.Context,
	request *openplatformpb.GetMetaAdsUserInfoRequest) (*openplatformpb.GetMetaAdsUserInfoResponse,
	error) {
	datum, err := s.ol.GetMetaAdsUserInfo(ctx, request.GetCompanyId())
	if err != nil {
		return nil, err
	}

	return &openplatformpb.GetMetaAdsUserInfoResponse{
		UserInfo:   datum.UserInfo,
		AdsSetting: datum.AdsSetting,
	}, nil
}

func (s *OpenPlatformService) RevokeMetaAdsOAuth(ctx context.Context,
	request *openplatformpb.RevokeMetaAdsOAuthRequest) (*emptypb.
	Empty, error) {
	if err := s.ol.RevokeMetaAdsOAuth(ctx, request.GetCompanyId(), request.StaffId); err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *OpenPlatformService) ListMetaAdsAccounts(ctx context.Context,
	request *openplatformpb.ListMetaAdsAccountsRequest) (*openplatformpb.ListMetaAdsAccountsResponse, error) {
	accounts, err := s.ol.ListMetaAdsAccounts(ctx, request.GetCompanyId())
	if err != nil {
		return nil, err
	}

	return &openplatformpb.ListMetaAdsAccountsResponse{
		Accounts: accounts,
	}, nil
}

func (s *OpenPlatformService) LinkMetaAdsAccounts(ctx context.Context,
	request *openplatformpb.LinkMetaAdsAccountsRequest) (*emptypb.Empty, error) {
	if err := s.ol.LinkMetaAdsAccounts(ctx, &oauth.LinkMetaAdsAccountsDatum{
		MetaAdsAccountIDs: request.MetaAdsAccountIds,
		CompanyID:         request.GetCompanyId(),
		BusinessID:        request.GetBusinessId(),
		StaffID:           request.GetStaffId(),
	}); err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}
