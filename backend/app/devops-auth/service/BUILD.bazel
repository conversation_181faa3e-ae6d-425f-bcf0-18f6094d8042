load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "service",
    srcs = [
        "auth.go",
        "http.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/devops-auth/service",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/devops-auth/configinit",
        "@com_github_envoyproxy_go_control_plane_envoy//config/core/v3:core",
        "@com_github_envoyproxy_go_control_plane_envoy//service/auth/v3:auth",
        "@com_github_golang_jwt_jwt_v4//:jwt",
        "@org_golang_google_api//idtoken",
        "@org_golang_google_genproto_googleapis_rpc//status",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_protobuf//types/known/wrapperspb",
    ],
)

go_test(
    name = "service_test",
    srcs = [
        "auth_test.go",
        "http_test.go",
    ],
    embed = [":service"],
    deps = [
        "//backend/app/devops-auth/configinit",
        "@com_github_envoyproxy_go_control_plane_envoy//service/auth/v3:auth",
        "@com_github_golang_jwt_jwt_v4//:jwt",
        "@com_github_stretchr_testify//assert",
        "@org_golang_google_api//idtoken",
        "@org_golang_google_grpc//codes",
    ],
)
