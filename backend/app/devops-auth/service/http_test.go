package service

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http/httptest"
	"testing"
	"time"

	jwt "github.com/golang-jwt/jwt/v4"
	"github.com/stretchr/testify/assert"
	"google.golang.org/api/idtoken"
)

func TestDecodeLoginRequest(t *testing.T) {
	tests := []struct {
		name        string
		body        interface{}
		expectError bool
	}{
		{
			name:        "Valid request",
			body:        loginRequest{Credential: "test-credential"},
			expectError: false,
		},
		{
			name:        "Empty credential",
			body:        loginRequest{Credential: ""},
			expectError: true,
		},
		{
			name:        "Invalid JSON",
			body:        "not-json",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tbodyBytes, _ := json.Marshal(tt.body)
			req := httptest.NewRequest("POST", "/login", bytes.NewReader(tbodyBytes))
			_, err := decodeLoginRequest(req)
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestGenerateJWT(t *testing.T) {
	secret := "test-secret"
	claims := jwt.MapClaims{
		"email": "<EMAIL>",
		"exp":   time.Now().Add(time.Hour).Unix(),
	}

	tokenString, err := generateJWT(claims, secret)
	assert.NoError(t, err)
	assert.NotEmpty(t, tokenString)

	// Verify the token
	parsedToken, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		return []byte(secret), nil
	})
	assert.NoError(t, err)
	assert.True(t, parsedToken.Valid)

	parsedClaims, ok := parsedToken.Claims.(jwt.MapClaims)
	assert.True(t, ok)
	assert.Equal(t, claims["email"], parsedClaims["email"])
}

func TestSetAuthCookie(t *testing.T) {
	w := httptest.NewRecorder()
	token := "test-token"

	setAuthCookie(w, token)

	cookie := w.Result().Cookies()[0]
	assert.Equal(t, "auth_token", cookie.Name)
	assert.Equal(t, token, cookie.Value)
	assert.True(t, cookie.HttpOnly)
	assert.True(t, cookie.Secure)
	assert.Equal(t, "/", cookie.Path)
	assert.WithinDuration(t, time.Now().Add(time.Hour*24), cookie.Expires, time.Second)
}

func TestValidateHostedDomain(t *testing.T) {
	tests := []struct {
		name        string
		payload     *idtoken.Payload
		expectError bool
	}{
		{
			name: "Valid hosted domain",
			payload: &idtoken.Payload{
				Claims: map[string]interface{}{"hd": "moego.pet"},
			},
			expectError: false,
		},
		{
			name: "Missing hosted domain",
			payload: &idtoken.Payload{
				Claims: map[string]interface{}{},
			},
			expectError: true,
		},
		{
			name: "Incorrect hosted domain",
			payload: &idtoken.Payload{
				Claims: map[string]interface{}{"hd": "wrong.com"},
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateHostedDomain(tt.payload)
			if tt.expectError {
				assert.Error(t, err)
				assert.Equal(t, ErrInvalidHostedDomain, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Note: Testing verifyGoogleIDToken requires a live Google client ID and a valid token,
// which is not suitable for a standard unit test. This would typically be tested in an
// integration test with proper credentials and mocking of the Google API.

func TestVerifyGoogleIDToken_Manual(t *testing.T) {
	t.Skip("Skipping manual test for Google ID token verification. Uncomment to run.")

	// Replace with your actual Google Client ID and a valid credential
	clientID := "your-google-client-id"
	credential := "your-google-id-token-credential"

	payload, err := verifyGoogleIDToken(context.Background(), credential, clientID)

	assert.NoError(t, err)
	assert.NotNil(t, payload)
	assert.NotEmpty(t, payload.Claims["email"])
	t.Logf("Successfully verified token for email: %s", payload.Claims["email"])
}
