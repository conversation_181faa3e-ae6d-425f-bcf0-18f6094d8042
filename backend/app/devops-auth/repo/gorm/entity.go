// gorm/entity.go 包含了该repo层内会用到的所有实体, 比如数据库的表结构
// 请注意, 为了可迭代和可维护, 即使logic entity 和 repo entity 是同一个实体, 也请分开定义
package gorm

import "time"

type ToDo struct {
	ID        int       `json:"id" gorm:"column:id"`
	Name      string    `json:"name" gorm:"column:name"`
	Todo      string    `json:"todo" gorm:"column:todo"`
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at"`
	UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at"`
}

func (*ToDo) TableName() string {
	return "moego.public.todo"
}
