package main

import (
	"net/http"

	authv3 "github.com/envoyproxy/go-control-plane/envoy/service/auth/v3"
	"github.com/gorilla/mux"

	"github.com/MoeGolibrary/moego/backend/app/devops-auth/configinit"
	"github.com/MoeGolibrary/moego/backend/app/devops-auth/service"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/opentelemetry"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/recovery"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/validation"
	rpc "github.com/MoeGolibrary/moego/backend/common/rpc/framework"
	mhttp "github.com/MoeGolibrary/moego/backend/common/rpc/framework/http"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

func main() {
	cfg := configinit.Init("./config")

	s := rpc.NewServer()

	// 注册grpc服务
	grpc.Register(s, &authv3.Authorization_ServiceDesc, service.NewAuthService(cfg))

	// 注册http服务
	router := mux.NewRouter()
	router.HandleFunc("/devops/auth/google/login",
		service.NewHTTPService(cfg).LoginHandler).Methods(http.MethodPost)
	mhttp.RegisterNoProtocolServiceMux(s.Service("moego.devops.auth"), router)

	if err := s.Serve(); err != nil {
		log.Fatalf("failed to serve: %v", err)
	}
}
