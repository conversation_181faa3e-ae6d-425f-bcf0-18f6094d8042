package main

import (
	"github.com/MoeGolibrary/moego/backend/app/offering/service"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/opentelemetry"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/recovery"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/validation"
	rpc "github.com/MoeGolibrary/moego/backend/common/rpc/framework"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func main() {
	s := rpc.NewServer()

	// 注册grpc服务
	grpc.Register(s, &offeringpb.ServiceService_ServiceDesc, service.NewServiceService())
	grpc.Register(s, &offeringpb.CareTypeService_ServiceDesc, service.NewCareTypeService())
	grpc.Register(s, &offeringpb.ServiceCategoryService_ServiceDesc, service.NewServiceCategoryService())

	if err := s.Serve(); err != nil {
		log.Fatalf("failed to serve: %v", err)
	}
}
