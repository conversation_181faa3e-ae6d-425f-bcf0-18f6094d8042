load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "service",
    srcs = [
        "care_type.go",
        "service.go",
        "service_category.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/service",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/logic/caretype",
        "//backend/app/offering/logic/caretype/attribute",
        "//backend/app/offering/logic/service",
        "//backend/app/offering/logic/service/category",
        "//backend/proto/offering/v1:offering",
    ],
)
