package service

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/offering/logic/addon"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/addon/category"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// AddOnService implements the AddOn service interface.
type AddOnService struct {
	offeringpb.UnimplementedAddOnServiceServer
	logic         *addon.Logic
	categoryLogic *category.Logic
}

// NewAddOnService creates a new AddOn service.
func NewAddOnService() *AddOnService {
	return &AddOnService{
		logic:         addon.NewLogic(),
		categoryLogic: category.NewLogic(),
	}
}

// CreateAddOn creates a new addon.
func (s *AddOnService) CreateAddOn(
	ctx context.Context, req *offeringpb.CreateAddOnRequest) (*offeringpb.CreateAddOnResponse, error) {
	id, err := s.logic.CreateAddOn(ctx, req.GetAddOn())
	if err != nil {
		return nil, err
	}

	return &offeringpb.CreateAddOnResponse{
		Id: id,
	}, nil
}

// GetAddOn gets an addon by ID.
func (s *AddOnService) GetAddOn(
	ctx context.Context, req *offeringpb.GetAddOnRequest) (*offeringpb.GetAddOnResponse, error) {
	addonProto, err := s.logic.GetAddOn(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	return &offeringpb.GetAddOnResponse{
		AddOn: addonProto,
	}, nil
}

// UpdateAddOn updates an addon.
func (s *AddOnService) UpdateAddOn(
	ctx context.Context, req *offeringpb.UpdateAddOnRequest) (*offeringpb.UpdateAddOnResponse, error) {
	err := s.logic.UpdateAddOn(ctx, req.GetAddOn())
	if err != nil {
		return nil, err
	}

	return &offeringpb.UpdateAddOnResponse{}, nil
}

// DeleteAddOn deletes an addon.
func (s *AddOnService) DeleteAddOn(
	ctx context.Context, req *offeringpb.DeleteAddOnRequest) (*offeringpb.DeleteAddOnResponse, error) {
	err := s.logic.DeleteAddOn(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	return &offeringpb.DeleteAddOnResponse{}, nil
}

// ListAddOns lists addons.
func (s *AddOnService) ListAddOns(
	ctx context.Context, req *offeringpb.ListAddOnsRequest) (*offeringpb.ListAddOnsResponse, error) {
	resp, err := s.logic.ListAddOns(ctx, req)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

// BatchUpdateAddOns batch updates addons.
func (s *AddOnService) BatchUpdateAddOns(
	ctx context.Context, req *offeringpb.BatchUpdateAddOnsRequest) (*offeringpb.BatchUpdateAddOnsResponse, error) {
	resp, err := s.logic.BatchUpdateAddOns(ctx, req)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

// ListAddOnCategories lists addon categories.
func (s *AddOnService) ListAddOnCategories(
	ctx context.Context, req *offeringpb.ListAddOnCategoriesRequest) (*offeringpb.ListAddOnCategoriesResponse, error) {
	return s.categoryLogic.ListAddOnCategories(ctx, req)
}

// SaveAddOnCategories saves addon categories.
func (s *AddOnService) SaveAddOnCategories(
	ctx context.Context, req *offeringpb.SaveAddOnCategoriesRequest) (*offeringpb.SaveAddOnCategoriesResponse, error) {
	return s.categoryLogic.SaveAddOnCategories(ctx, req)
}
