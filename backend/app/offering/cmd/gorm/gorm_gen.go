package main

import (
	"path/filepath"
	"runtime"

	"gorm.io/driver/postgres"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

// Dynamic SQL
//type Querier interface {
//	// SELECT * FROM @@table WHERE name = @name{{if role !=""}} AND role = @role{{end}}
//	FilterWithNameAndRole(name, role string) ([]gen.T, error)
//}

func main() {
	// Get the directory of the current file (gorm_gen.go)
	// 获取当前文件 (gorm_gen.go) 的目录
	_, filename, _, _ := runtime.Caller(0)
	currentDir := filepath.Dir(filename)

	// Construct the absolute paths based on the current file's directory
	// 根据当前文件目录构建绝对路径
	outPath := filepath.Join(currentDir, "../../repo/db/query/")
	modelPkgPath := filepath.Join(currentDir, "../../repo/db/model/")

	config := gen.Config{
		OutPath:           outPath,
		ModelPkgPath:      modelPkgPath,
		FieldWithIndexTag: true, // Generate field with `gorm:"index"` tag for index columns
		FieldWithTypeTag:  true, // Generate field with `gorm:"type"` tag for data type columns
		FieldNullable:     true, // 数据库中的字段可为空，则生成struct字段为指针类型
		// 见：https://gorm.io/docs/create.html#Default-Values
		FieldCoverable: true, // 如果数据库中字段有默认值，则生成指针类型的字段，以避免零值（zero-value）问题
	}
	g := gen.NewGenerator(config)

	db, _ := gorm.Open(postgres.Open("postgresql://moego_developer_240310_eff7a0dc:G0MxI7NM_jX_f7Ky73vnrwej97xg1tly" +
		"@postgres.t2.moego.dev:40132/moego_offering?sslmode=disable"))
	// gormdb, _ := gorm.Open(mysql.Open("root:@(127.0.0.1:3306)/demo?charset=utf8mb4&parseTime=True&loc=Local"))
	g.UseDB(db) // reuse your gorm db

	// Generate basic type-safe DAO API for struct `model.User` following conventions
	careType := g.GenerateModel("care_type",
		gen.FieldType("organization_type", "organizationpb.OrganizationType"),
		gen.FieldType("care_category", "offeringpb.CareCategory"),
		getSerializerTag("organization_type", "proto_enum"),
		getSerializerTag("care_category", "proto_enum"),
	)
	careTypeAttribute := g.GenerateModel("care_type_attribute",
		gen.FieldType("attribute_key", "offeringpb.AttributeKey"),
		gen.FieldType("value_type", "offeringpb.ValueType"),
		getSerializerTag("attribute_key", "proto_enum"),
		getSerializerTag("value_type", "proto_enum"),
	)
	service := g.GenerateModel("service",
		gen.FieldType("organization_type", "organizationpb.OrganizationType"),
		gen.FieldType("source", "offeringpb.OfferingSource"),
		gen.FieldType("status", "offeringpb.Service_Status"),
		gen.FieldType("type", "offeringpb.Service_Type"),
		gen.FieldType("images", "[]string"),
		getSerializerTag("images", "json"),
		getSerializerTag("organization_type", "proto_enum"),
		getSerializerTag("source", "proto_enum"),
		getSerializerTag("status", "proto_enum"),
		getSerializerTag("type", "proto_enum"),
	)
	serviceAttributeValue := g.GenerateModel("service_attribute",
		gen.FieldType("attribute_key", "offeringpb.AttributeKey"),
		getSerializerTag("attribute_key", "proto_enum"),
	)
	serviceOBSetting := g.GenerateModel("service_ob_setting",
		gen.FieldType("organization_type", "organizationpb.OrganizationType"),
		getSerializerTag("organization_type", "proto_enum"),
	)
	serviceOBStaffBinding := g.GenerateModel("service_ob_staff_binding",
		gen.FieldType("organization_type", "organizationpb.OrganizationType"),
		getSerializerTag("organization_type", "proto_enum"),
	)

	serviceCatrgory := g.GenerateModel("service_category",
		gen.FieldType("organization_type", "organizationpb.OrganizationType"),
		gen.FieldType("type", "offeringpb.Service_Type"),
		getSerializerTag("organization_type", "proto_enum"),
		getSerializerTag("type", "proto_enum"),
	)

	serviceBusinessScope := g.GenerateModel("service_business_scope",
		gen.FieldType("available_business_ids", "[]int64"),
		getSerializerTag("available_business_ids", "json"),
	)
	serviceLodgingScope := g.GenerateModel("service_lodging_scope",
		gen.FieldType("available_lodging_type_ids", "[]int64"),
		getSerializerTag("available_lodging_type_ids", "json"),
	)
	serviceStaffScope := g.GenerateModel("service_staff_scope",
		gen.FieldType("available_staff_ids", "[]int64"),
		getSerializerTag("available_staff_ids", "json"),
	)
	serviceAutoRollover := g.GenerateModel("service_auto_rollover")
	serviceAssociation := g.GenerateModel("service_association",
		gen.FieldType("target_service_type", "offeringpb.Service_Type"),
		gen.FieldType("target_service_id", "int64"),
		gen.FieldType("target_care_type_id", "int64"),
		getSerializerTag("target_service_type", "proto_enum"),
	)
	lodgingType := g.GenerateModel("lodging_type",
		gen.FieldType("source", "offeringpb.OfferingSource"),
		getSerializerTag("source", "proto_enum"),
		gen.FieldType("allowed_pet_size_list", "pq.Int64Array"),
		gen.FieldType("photo", "[]string"), // 使用切片类型
		getSerializerTag("photo", "json"),  // 使用json序列化
	)
	lodgingUnit := g.GenerateModel("lodging_unit",
		gen.FieldType("source", "offeringpb.OfferingSource"),
	)
	servicePetAvailabilityScope := g.GenerateModel("service_pet_availability_scope",
		gen.FieldType("scope_type", "offeringpb.PetAvailabilityScopeType"),
		getSerializerTag("scope_type", "proto_enum"),
	)
	servicePetWeightRange := g.GenerateModel("service_pet_weight_range")
	serviceBusinessOverride := g.GenerateModel("service_business_override",
		gen.FieldType("overrides", "offeringpb.OverrideValues"),
		getSerializerTag("overrides", "json"),
	)
	serviceStaffOverride := g.GenerateModel("service_staff_override",
		gen.FieldType("overrides", "offeringpb.OverrideValues"),
		getSerializerTag("overrides", "json"),
	)
	servicePrerequisiteRule := g.GenerateModel("service_prerequisite_rule")

	g.ApplyBasic(
		careType, careTypeAttribute,
		service, serviceAttributeValue,
		serviceOBSetting,
		serviceOBStaffBinding,
		serviceCatrgory, serviceAttributeValue, serviceAutoRollover,
		serviceBusinessScope, serviceLodgingScope, serviceStaffScope,
		lodgingType, lodgingUnit,
		serviceAssociation, servicePetAvailabilityScope,
		servicePetWeightRange,
		serviceBusinessOverride, serviceStaffOverride,
		servicePrerequisiteRule,
	)

	// Generate Type Safe API with Dynamic SQL defined on Querier interface for `model.User` and `model.Company`
	// g.ApplyInterface(func(Querier) {}, model.User{}, model.Company{})

	// Generate the code
	g.Execute()
}

func getSerializerTag(columnName string, serializer string) gen.ModelOpt {
	return gen.FieldGORMTag(columnName, func(tag field.GormTag) field.GormTag {
		tag.Set("serializer", serializer)

		return tag
	})
}

//go:generate go run gorm_gen.go
