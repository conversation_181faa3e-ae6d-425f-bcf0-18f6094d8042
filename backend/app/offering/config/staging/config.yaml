server:
  filter:
    - opentelemetry
    - recovery
    - debuglog
    - validation
  service:
    - name: backend.proto.offering.v1.ServiceService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
    - name: backend.proto.offering.v1.AddOnService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
    - name: backend.proto.offering.v1.CareTypeService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
    - name: backend.proto.offering.v1.ServiceCategoryService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
client:
  network: tcp
  protocol: grpc
  filter:
    - opentelemetry
    - debuglog
  transport: grpc
  timeout: 5000
  service:
    - callee: postgres.moego_offering
      target: dsn://postgresql://${secret.datasource.postgres.moego_offering.username}:${secret.datasource.postgres.moego_offering.password}@${secret.datasource.postgres.url.master}:${secret.datasource.postgres.port}/moego_offering?sslmode=disable
      protocol: gorm
      transport: gorm
plugins:
  database:
    gorm:
      max_idle: 20
      max_open: 100
      max_lifetime: 180000 # millisecond
      service:
        - name: mysql.xxxx
          max_idle: 10
          max_open: 50
          max_lifetime: 180000
        - name: postgres.xxxx
          max_idle: 10
          max_open: 50
          max_lifetime: 180000
  telemetry:
    opentelemetry:
      traces:
        disable_trace_body: false
  auth:
    validation:
      enable_error_log: false
