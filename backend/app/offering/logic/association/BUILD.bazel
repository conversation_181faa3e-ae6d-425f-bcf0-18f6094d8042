load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "association",
    srcs = [
        "association.go",
        "converter.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/logic/association",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/repo/db/association",
        "//backend/app/offering/repo/db/model",
        "//backend/proto/offering/v1:offering",
        "@com_github_samber_lo//:lo",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
    ],
)

go_test(
    name = "association_test",
    srcs = [
        "association_test.go",
        "converter_test.go",
        "entity_test.go",
    ],
    embed = [":association"],
    deps = [
        "//backend/app/offering/repo/db/association/mocks",
        "//backend/app/offering/repo/db/model",
        "//backend/proto/offering/v1:offering",
        "@com_github_stretchr_testify//assert",
        "@org_uber_go_mock//gomock",
    ],
)
