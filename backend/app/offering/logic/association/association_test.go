package association

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	mock "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/association/mocks"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// testHelper 测试辅助结构体
type testHelper struct {
	ctrl                *gomock.Controller
	mockAssociationRepo *mock.MockRepository
	logic               *Logic
}

// newTestHelper 创建测试辅助实例
func newTestHelper(t *testing.T) *testHelper {
	ctrl := gomock.NewController(t)

	mockAssociationRepo := mock.NewMockRepository(ctrl)

	logic := NewLogicWithRepository(mockAssociationRepo)

	return &testHelper{
		ctrl:                ctrl,
		mockAssociationRepo: mockAssociationRepo,
		logic:               logic,
	}
}

// finish 清理测试资源
func (h *testHelper) finish() {
	h.ctrl.Finish()
}

func TestLogic_UpsertAssociations(t *testing.T) {
	t.Run("成功创建新的关联配置", func(t *testing.T) {
		helper := newTestHelper(t)
		defer helper.finish()

		// 准备测试数据
		agg := NewAggregate(123)
		agg.AddCareTypeTarget(1)
		agg.AddServiceIDTarget(456)
		agg.AddServiceTypeTarget(offeringpb.Service_ADD_ON)

		// 设置 mock 期望
		helper.mockAssociationRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(123)).Return(nil)
		helper.mockAssociationRepo.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil)

		// 执行测试
		err := helper.logic.UpsertAssociations(context.Background(), agg)

		// 验证结果
		assert.NoError(t, err)
	})

	t.Run("成功更新现有关联配置", func(t *testing.T) {
		helper := newTestHelper(t)
		defer helper.finish()

		// 准备测试数据
		agg := NewAggregate(123)
		agg.AddCareTypeTarget(2)
		agg.AddServiceIDTarget(789)

		// 设置 mock 期望
		helper.mockAssociationRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(123)).Return(nil)
		helper.mockAssociationRepo.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil)

		// 执行测试
		err := helper.logic.UpsertAssociations(context.Background(), agg)

		// 验证结果
		assert.NoError(t, err)
	})

	t.Run("空关联配置，删除所有现有配置", func(t *testing.T) {
		helper := newTestHelper(t)
		defer helper.finish()

		// 准备测试数据 - 空的关联配置
		agg := NewAggregate(123)
		// 添加一个目标以通过验证
		agg.AddCareTypeTarget(100)

		// 设置 mock 期望 - 删除现有配置
		helper.mockAssociationRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(123)).Return(nil)
		// 由于有目标，会尝试创建
		helper.mockAssociationRepo.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil)

		// 执行测试
		err := helper.logic.UpsertAssociations(context.Background(), agg)

		// 验证结果
		assert.NoError(t, err)
	})

	t.Run("入参为空", func(t *testing.T) {
		helper := newTestHelper(t)
		defer helper.finish()

		// 执行测试
		err := helper.logic.UpsertAssociations(context.Background(), nil)

		// 验证结果
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "association is required")
	})

	t.Run("无效的关联配置", func(t *testing.T) {
		helper := newTestHelper(t)
		defer helper.finish()

		// 准备测试数据 - 无效的关联配置（没有目标）
		agg := &ServiceAssociationAggregate{
			SourceServiceID:    123,
			TargetCareTypeIDs:  []int64{},
			TargetServiceIDs:   []int64{},
			TargetServiceTypes: []offeringpb.Service_Type{},
		}

		// 执行测试
		err := helper.logic.UpsertAssociations(context.Background(), agg)

		// 验证结果
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid association")
	})

	t.Run("删除现有配置失败", func(t *testing.T) {
		helper := newTestHelper(t)
		defer helper.finish()

		// 准备测试数据
		agg := NewAggregate(123)
		agg.AddCareTypeTarget(1)

		// 设置 mock 期望 - 删除失败
		expectedErr := errors.New("delete failed")
		helper.mockAssociationRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(123)).Return(expectedErr)

		// 执行测试
		err := helper.logic.UpsertAssociations(context.Background(), agg)

		// 验证结果
		assert.Error(t, err)
		assert.Equal(t, expectedErr, err)
	})

	t.Run("创建新配置失败", func(t *testing.T) {
		helper := newTestHelper(t)
		defer helper.finish()

		// 准备测试数据
		agg := NewAggregate(123)
		agg.AddCareTypeTarget(1)

		// 设置 mock 期望
		helper.mockAssociationRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(123)).Return(nil)
		expectedErr := errors.New("create failed")
		helper.mockAssociationRepo.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(expectedErr)

		// 执行测试
		err := helper.logic.UpsertAssociations(context.Background(), agg)

		// 验证结果
		assert.Error(t, err)
		assert.Equal(t, expectedErr, err)
	})
}

func TestLogic_GetAggregateByServiceID(t *testing.T) {
	t.Run("成功获取关联配置", func(t *testing.T) {
		helper := newTestHelper(t)
		defer helper.finish()

		// 准备测试数据
		sourceServiceID := int64(123)
		mockModels := []*model.ServiceAssociation{
			{
				ID:               1,
				SourceServiceID:  123,
				TargetCareTypeID: 1,
			},
			{
				ID:              2,
				SourceServiceID: 123,
				TargetServiceID: 456,
			},
		}

		// 设置 mock 期望
		helper.mockAssociationRepo.EXPECT().GetByServiceID(gomock.Any(), sourceServiceID).Return(mockModels, nil)

		// 执行测试
		result, err := helper.logic.GetAggregateByServiceID(context.Background(), sourceServiceID)

		// 验证结果
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, sourceServiceID, result.SourceServiceID)
		assert.Equal(t, []int64{1}, result.TargetCareTypeIDs)
		assert.Equal(t, []int64{456}, result.TargetServiceIDs)
		assert.Empty(t, result.TargetServiceTypes)
	})

	t.Run("服务没有关联配置", func(t *testing.T) {
		helper := newTestHelper(t)
		defer helper.finish()

		// 准备测试数据
		sourceServiceID := int64(123)
		mockModels := []*model.ServiceAssociation{}

		// 设置 mock 期望
		helper.mockAssociationRepo.EXPECT().GetByServiceID(gomock.Any(), sourceServiceID).Return(mockModels, nil)

		// 执行测试
		result, err := helper.logic.GetAggregateByServiceID(context.Background(), sourceServiceID)

		// 验证结果
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, sourceServiceID, result.SourceServiceID)
		assert.True(t, result.IsEmpty())
	})

	t.Run("数据库错误", func(t *testing.T) {
		helper := newTestHelper(t)
		defer helper.finish()

		// 准备测试数据
		sourceServiceID := int64(123)
		expectedErr := errors.New("database error")

		// 设置 mock 期望
		helper.mockAssociationRepo.EXPECT().GetByServiceID(gomock.Any(), sourceServiceID).Return(nil, expectedErr)

		// 执行测试
		result, err := helper.logic.GetAggregateByServiceID(context.Background(), sourceServiceID)

		// 验证结果
		assert.Error(t, err)
		assert.Equal(t, expectedErr, err)
		assert.Nil(t, result)
	})
}

func TestLogic_ListAggregatesByServiceIDs(t *testing.T) {
	t.Run("成功批量获取多个服务的关联配置", func(t *testing.T) {
		helper := newTestHelper(t)
		defer helper.finish()

		// 准备测试数据
		sourceServiceIDs := []int64{123, 456}
		mockModels := []*model.ServiceAssociation{
			{
				ID:               1,
				SourceServiceID:  123,
				TargetCareTypeID: 1,
			},
			{
				ID:              2,
				SourceServiceID: 456,
				TargetServiceID: 789,
			},
		}

		// 设置 mock 期望
		helper.mockAssociationRepo.EXPECT().ListByServiceIDs(gomock.Any(), sourceServiceIDs).Return(mockModels, nil)

		// 执行测试
		result, err := helper.logic.ListAggregatesByServiceIDs(context.Background(), sourceServiceIDs)

		// 验证结果
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result, 2)

		// 验证 service 123 的配置
		agg123, exists := result[123]
		assert.True(t, exists)
		assert.Equal(t, int64(123), agg123.SourceServiceID)
		assert.Equal(t, []int64{1}, agg123.TargetCareTypeIDs)

		// 验证 service 456 的配置
		agg456, exists := result[456]
		assert.True(t, exists)
		assert.Equal(t, int64(456), agg456.SourceServiceID)
		assert.Equal(t, []int64{789}, agg456.TargetServiceIDs)
	})

	t.Run("空的服务ID列表", func(t *testing.T) {
		helper := newTestHelper(t)
		defer helper.finish()

		// 准备测试数据
		sourceServiceIDs := []int64{}

		// 执行测试
		result, err := helper.logic.ListAggregatesByServiceIDs(context.Background(), sourceServiceIDs)

		// 验证结果
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Empty(t, result)
	})

	t.Run("部分服务没有关联配置", func(t *testing.T) {
		helper := newTestHelper(t)
		defer helper.finish()

		// 准备测试数据
		sourceServiceIDs := []int64{123, 456, 789}
		mockModels := []*model.ServiceAssociation{
			{
				ID:               1,
				SourceServiceID:  123,
				TargetCareTypeID: 1,
			},
			// service 456 没有配置
			// service 789 没有配置
		}

		// 设置 mock 期望
		helper.mockAssociationRepo.EXPECT().ListByServiceIDs(gomock.Any(), sourceServiceIDs).Return(mockModels, nil)

		// 执行测试
		result, err := helper.logic.ListAggregatesByServiceIDs(context.Background(), sourceServiceIDs)

		// 验证结果
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result, 3)

		// 验证 service 123 的配置
		agg123, exists := result[123]
		assert.True(t, exists)
		assert.Equal(t, int64(123), agg123.SourceServiceID)
		assert.Equal(t, []int64{1}, agg123.TargetCareTypeIDs)

		// 验证 service 456 和 789 都是空配置
		agg456, exists := result[456]
		assert.True(t, exists)
		assert.True(t, agg456.IsEmpty())

		agg789, exists := result[789]
		assert.True(t, exists)
		assert.True(t, agg789.IsEmpty())
	})

	t.Run("数据库错误", func(t *testing.T) {
		helper := newTestHelper(t)
		defer helper.finish()

		// 准备测试数据
		sourceServiceIDs := []int64{123, 456}
		expectedErr := errors.New("database error")

		// 设置 mock 期望
		helper.mockAssociationRepo.EXPECT().ListByServiceIDs(gomock.Any(), sourceServiceIDs).Return(nil, expectedErr)

		// 执行测试
		result, err := helper.logic.ListAggregatesByServiceIDs(context.Background(), sourceServiceIDs)

		// 验证结果
		assert.Error(t, err)
		assert.Equal(t, expectedErr, err)
		assert.Nil(t, result)
	})
}

func TestLogic_DeleteByServiceID(t *testing.T) {
	t.Run("成功删除服务的关联配置", func(t *testing.T) {
		helper := newTestHelper(t)
		defer helper.finish()

		// 准备测试数据
		sourceServiceID := int64(123)

		// 设置 mock 期望
		helper.mockAssociationRepo.EXPECT().DeleteByServiceID(gomock.Any(), sourceServiceID).Return(nil)

		// 执行测试
		err := helper.logic.DeleteByServiceID(context.Background(), sourceServiceID)

		// 验证结果
		assert.NoError(t, err)
	})

	t.Run("删除失败", func(t *testing.T) {
		helper := newTestHelper(t)
		defer helper.finish()

		// 准备测试数据
		sourceServiceID := int64(123)
		expectedErr := errors.New("delete failed")

		// 设置 mock 期望
		helper.mockAssociationRepo.EXPECT().DeleteByServiceID(gomock.Any(), sourceServiceID).Return(expectedErr)

		// 执行测试
		err := helper.logic.DeleteByServiceID(context.Background(), sourceServiceID)

		// 验证结果
		assert.Error(t, err)
		assert.Equal(t, expectedErr, err)
	})
}

func TestLogic_NewLogic(t *testing.T) {
	t.Run("创建默认实例", func(t *testing.T) {
		// 注意：这个测试会尝试连接真实数据库，在生产环境中应该跳过
		// 或者使用测试配置
		t.Skip("跳过真实数据库连接测试")

		logic := NewLogic()
		assert.NotNil(t, logic)
		assert.NotNil(t, logic.repo)
	})
}

func TestLogic_NewLogicWithRepository(t *testing.T) {
	t.Run("创建带有自定义repository的实例", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mock.NewMockRepository(ctrl)
		logic := NewLogicWithRepository(mockRepo)

		assert.NotNil(t, logic)
		assert.Equal(t, mockRepo, logic.repo)
	})
}
