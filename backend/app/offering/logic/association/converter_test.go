package association

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func TestModelsToAggregate(t *testing.T) {
	tests := []struct {
		name                  string
		sourceServiceID       int64
		models                []*model.ServiceAssociation
		expectedCount         int
		expectedCareTypeCount int
		expectedServiceCount  int
		expectedTypeCount     int
	}{
		{
			name:                  "空模型列表",
			sourceServiceID:       1,
			models:                []*model.ServiceAssociation{},
			expectedCount:         0,
			expectedCareTypeCount: 0,
			expectedServiceCount:  0,
			expectedTypeCount:     0,
		},
		{
			name:            "护理类型关联",
			sourceServiceID: 1,
			models: []*model.ServiceAssociation{
				{
					SourceServiceID:   1,
					TargetCareTypeID:  100,
					TargetServiceID:   0,
					TargetServiceType: offeringpb.Service_TYPE_UNSPECIFIED,
				},
				{
					SourceServiceID:   1,
					TargetCareTypeID:  200,
					TargetServiceID:   0,
					TargetServiceType: offeringpb.Service_TYPE_UNSPECIFIED,
				},
			},
			expectedCount:         2,
			expectedCareTypeCount: 2,
			expectedServiceCount:  0,
			expectedTypeCount:     0,
		},
		{
			name:            "服务ID关联",
			sourceServiceID: 1,
			models: []*model.ServiceAssociation{
				{
					SourceServiceID:   1,
					TargetCareTypeID:  0,
					TargetServiceID:   300,
					TargetServiceType: offeringpb.Service_TYPE_UNSPECIFIED,
				},
				{
					SourceServiceID:   1,
					TargetCareTypeID:  0,
					TargetServiceID:   400,
					TargetServiceType: offeringpb.Service_TYPE_UNSPECIFIED,
				},
			},
			expectedCount:         2,
			expectedCareTypeCount: 0,
			expectedServiceCount:  2,
			expectedTypeCount:     0,
		},
		{
			name:            "服务类型关联",
			sourceServiceID: 1,
			models: []*model.ServiceAssociation{
				{
					SourceServiceID:   1,
					TargetCareTypeID:  0,
					TargetServiceID:   0,
					TargetServiceType: offeringpb.Service_SERVICE,
				},
				{
					SourceServiceID:   1,
					TargetCareTypeID:  0,
					TargetServiceID:   0,
					TargetServiceType: offeringpb.Service_ADD_ON,
				},
			},
			expectedCount:         2,
			expectedCareTypeCount: 0,
			expectedServiceCount:  0,
			expectedTypeCount:     2,
		},
		{
			name:            "混合关联",
			sourceServiceID: 1,
			models: []*model.ServiceAssociation{
				{
					SourceServiceID:   1,
					TargetCareTypeID:  100,
					TargetServiceID:   0,
					TargetServiceType: offeringpb.Service_TYPE_UNSPECIFIED,
				},
				{
					SourceServiceID:   1,
					TargetCareTypeID:  0,
					TargetServiceID:   300,
					TargetServiceType: offeringpb.Service_TYPE_UNSPECIFIED,
				},
				{
					SourceServiceID:   1,
					TargetCareTypeID:  0,
					TargetServiceID:   0,
					TargetServiceType: offeringpb.Service_SERVICE,
				},
			},
			expectedCount:         3,
			expectedCareTypeCount: 1,
			expectedServiceCount:  1,
			expectedTypeCount:     1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			agg := ModelsToAggregate(tt.sourceServiceID, tt.models)

			assert.NotNil(t, agg)
			assert.Equal(t, tt.sourceServiceID, agg.SourceServiceID)
			assert.Equal(t, tt.expectedCount, agg.GetTargetCount())
			assert.Equal(t, tt.expectedCareTypeCount, len(agg.TargetCareTypeIDs))
			assert.Equal(t, tt.expectedServiceCount, len(agg.TargetServiceIDs))
			assert.Equal(t, tt.expectedTypeCount, len(agg.TargetServiceTypes))

			// 验证具体的值
			if tt.expectedCareTypeCount > 0 {
				for _, model := range tt.models {
					if model.TargetCareTypeID > 0 {
						assert.Contains(t, agg.TargetCareTypeIDs, model.TargetCareTypeID,
							"护理类型ID %d 应该在聚合对象中", model.TargetCareTypeID)
					}
				}
			}

			if tt.expectedServiceCount > 0 {
				for _, model := range tt.models {
					if model.TargetServiceID > 0 {
						assert.Contains(t, agg.TargetServiceIDs, model.TargetServiceID,
							"服务ID %d 应该在聚合对象中", model.TargetServiceID)
					}
				}
			}

			if tt.expectedTypeCount > 0 {
				for _, model := range tt.models {
					if model.TargetServiceType != offeringpb.Service_TYPE_UNSPECIFIED {
						assert.Contains(t, agg.TargetServiceTypes, model.TargetServiceType,
							"服务类型 %s 应该在聚合对象中", model.TargetServiceType.String())
					}
				}
			}
		})
	}
}

func TestAggregateToModels(t *testing.T) {
	tests := []struct {
		name                  string
		agg                   *ServiceAssociationAggregate
		expectedCount         int
		expectedCareTypeCount int
		expectedServiceCount  int
		expectedTypeCount     int
	}{
		{
			name:                  "nil 聚合对象",
			agg:                   nil,
			expectedCount:         0,
			expectedCareTypeCount: 0,
			expectedServiceCount:  0,
			expectedTypeCount:     0,
		},
		{
			name:                  "空聚合对象",
			agg:                   NewAggregate(1),
			expectedCount:         0,
			expectedCareTypeCount: 0,
			expectedServiceCount:  0,
			expectedTypeCount:     0,
		},
		{
			name: "护理类型关联",
			agg: func() *ServiceAssociationAggregate {
				agg := NewAggregate(1)
				agg.AddCareTypeTarget(100)
				agg.AddCareTypeTarget(200)
				return agg
			}(),
			expectedCount:         2,
			expectedCareTypeCount: 2,
			expectedServiceCount:  0,
			expectedTypeCount:     0,
		},
		{
			name: "服务ID关联",
			agg: func() *ServiceAssociationAggregate {
				agg := NewAggregate(1)
				agg.AddServiceIDTarget(300)
				agg.AddServiceIDTarget(400)
				return agg
			}(),
			expectedCount:         2,
			expectedCareTypeCount: 0,
			expectedServiceCount:  2,
			expectedTypeCount:     0,
		},
		{
			name: "服务类型关联",
			agg: func() *ServiceAssociationAggregate {
				agg := NewAggregate(1)
				agg.AddServiceTypeTarget(offeringpb.Service_SERVICE)
				agg.AddServiceTypeTarget(offeringpb.Service_ADD_ON)
				return agg
			}(),
			expectedCount:         2,
			expectedCareTypeCount: 0,
			expectedServiceCount:  0,
			expectedTypeCount:     2,
		},
		{
			name: "混合关联",
			agg: func() *ServiceAssociationAggregate {
				agg := NewAggregate(1)
				agg.AddCareTypeTarget(100)
				agg.AddServiceIDTarget(300)
				agg.AddServiceTypeTarget(offeringpb.Service_SERVICE)
				return agg
			}(),
			expectedCount:         3,
			expectedCareTypeCount: 1,
			expectedServiceCount:  1,
			expectedTypeCount:     1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			models := AggregateToModels(tt.agg)

			assert.Len(t, models, tt.expectedCount)

			if tt.expectedCount == 0 {
				return
			}

			// 验证每个模型都有正确的 SourceServiceID
			for _, model := range models {
				assert.Equal(t, tt.agg.SourceServiceID, model.SourceServiceID)
			}

			// 验证护理类型模型
			careTypeCount := 0
			for _, model := range models {
				if model.TargetCareTypeID > 0 {
					careTypeCount++
					assert.Equal(t, int64(0), model.TargetServiceID)
					assert.Equal(t, offeringpb.Service_TYPE_UNSPECIFIED, model.TargetServiceType)
				}
			}
			assert.Equal(t, tt.expectedCareTypeCount, careTypeCount)

			// 验证服务ID模型
			serviceIDCount := 0
			for _, model := range models {
				if model.TargetServiceID > 0 {
					serviceIDCount++
					assert.Equal(t, int64(0), model.TargetCareTypeID)
					assert.Equal(t, offeringpb.Service_TYPE_UNSPECIFIED, model.TargetServiceType)
				}
			}
			assert.Equal(t, tt.expectedServiceCount, serviceIDCount)

			// 验证服务类型模型
			serviceTypeCount := 0
			for _, model := range models {
				if model.TargetServiceType != offeringpb.Service_TYPE_UNSPECIFIED {
					serviceTypeCount++
					assert.Equal(t, int64(0), model.TargetCareTypeID)
					assert.Equal(t, int64(0), model.TargetServiceID)
				}
			}
			assert.Equal(t, tt.expectedTypeCount, serviceTypeCount)
		})
	}
}

func TestAggregateToModels_FieldValidation(t *testing.T) {
	t.Run("验证护理类型模型的字段", func(t *testing.T) {
		agg := NewAggregate(1)
		agg.AddCareTypeTarget(100)

		models := AggregateToModels(agg)
		assert.Len(t, models, 1)

		model := models[0]
		assert.Equal(t, int64(1), model.SourceServiceID)
		assert.Equal(t, int64(100), model.TargetCareTypeID)
		assert.Equal(t, int64(0), model.TargetServiceID)
		assert.Equal(t, offeringpb.Service_TYPE_UNSPECIFIED, model.TargetServiceType)
	})

	t.Run("验证服务ID模型的字段", func(t *testing.T) {
		agg := NewAggregate(1)
		agg.AddServiceIDTarget(200)

		models := AggregateToModels(agg)
		assert.Len(t, models, 1)

		model := models[0]
		assert.Equal(t, int64(1), model.SourceServiceID)
		assert.Equal(t, int64(0), model.TargetCareTypeID)
		assert.Equal(t, int64(200), model.TargetServiceID)
		assert.Equal(t, offeringpb.Service_TYPE_UNSPECIFIED, model.TargetServiceType)
	})

	t.Run("验证服务类型模型的字段", func(t *testing.T) {
		agg := NewAggregate(1)
		agg.AddServiceTypeTarget(offeringpb.Service_SERVICE)

		models := AggregateToModels(agg)
		assert.Len(t, models, 1)

		model := models[0]
		assert.Equal(t, int64(1), model.SourceServiceID)
		assert.Equal(t, int64(0), model.TargetCareTypeID)
		assert.Equal(t, int64(0), model.TargetServiceID)
		assert.Equal(t, offeringpb.Service_SERVICE, model.TargetServiceType)
	})
}
