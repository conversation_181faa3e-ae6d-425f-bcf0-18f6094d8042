package association

import (
	"context"

	"github.com/samber/lo"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/association"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
)

// Logic 处理 service association 相关的逻辑
type Logic struct {
	repo association.Repository
}

// NewLogic 创建 service association 处理器
func NewLogic() *Logic {
	return &Logic{
		repo: association.NewRepository(),
	}
}

// NewLogicWithRepository 创建带有自定义repository的service association处理器（用于测试）
func NewLogicWithRepository(repo association.Repository) *Logic {
	return &Logic{
		repo: repo,
	}
}

// UpsertAssociations 批量写入 service associations
func (l *Logic) UpsertAssociations(ctx context.Context, agg *ServiceAssociationAggregate) error {
	if agg == nil {
		return status.Error(codes.InvalidArgument, "association is required")
	}

	if err := agg.IsValid(); err != nil {
		return status.Errorf(codes.InvalidArgument, "invalid association: %v", err)
	}

	// 先删除现有配置
	if err := l.repo.DeleteByServiceID(ctx, agg.SourceServiceID); err != nil {
		return err
	}

	// 再保存新的配置
	models := AggregateToModels(agg)
	if len(models) > 0 {
		return l.repo.BatchCreate(ctx, models)
	}

	return nil
}

// GetAggregateByServiceID 获取服务的所有 service associations
func (l *Logic) GetAggregateByServiceID(
	ctx context.Context, sourceServiceID int64) (*ServiceAssociationAggregate, error) {
	associations, err := l.repo.GetByServiceID(ctx, sourceServiceID)
	if err != nil {
		return nil, err
	}

	return ModelsToAggregate(sourceServiceID, associations), nil
}

// ListAggregatesByServiceIDs 批量获取多个服务的 service associations
// 返回值：serviceID -> ServiceAssociationAggregate
func (l *Logic) ListAggregatesByServiceIDs(
	ctx context.Context, sourceServiceIDs []int64) (map[int64]*ServiceAssociationAggregate, error) {
	if len(sourceServiceIDs) == 0 {
		return make(map[int64]*ServiceAssociationAggregate), nil
	}

	// 批量查询配置
	associations, err := l.repo.ListByServiceIDs(ctx, sourceServiceIDs)
	if err != nil {
		return nil, err
	}

	// 按 sourceServiceID 分组
	associationsBySourceServiceID := lo.GroupBy(associations, func(association *model.ServiceAssociation) int64 {
		return association.SourceServiceID
	})

	// 构建结果映射
	result := make(map[int64]*ServiceAssociationAggregate, len(sourceServiceIDs))

	for _, sourceServiceID := range sourceServiceIDs {
		associations := associationsBySourceServiceID[sourceServiceID]
		result[sourceServiceID] = ModelsToAggregate(sourceServiceID, associations)
	}

	return result, nil
}

// DeleteByServiceID 删除服务的所有 service associations
func (l *Logic) DeleteByServiceID(ctx context.Context, sourceServiceID int64) error {
	return l.repo.DeleteByServiceID(ctx, sourceServiceID)
}
