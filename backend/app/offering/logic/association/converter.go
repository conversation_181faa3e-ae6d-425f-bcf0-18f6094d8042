package association

import (
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// ModelsToAggregate 将数据库模型转换为聚合对象
func ModelsToAggregate(sourceServiceID int64, associations []*model.ServiceAssociation) *ServiceAssociationAggregate {
	agg := NewAggregate(sourceServiceID)

	for _, association := range associations {
		if association.TargetServiceType != offeringpb.Service_TYPE_UNSPECIFIED {
			agg.AddServiceTypeTarget(association.TargetServiceType)
		} else if association.TargetCareTypeID > 0 {
			agg.AddCareTypeTarget(association.TargetCareTypeID)
		} else if association.TargetServiceID > 0 {
			agg.AddServiceIDTarget(association.TargetServiceID)
		}
	}

	return agg
}

// AggregateToModels 将聚合对象转换为数据库模型
func AggregateToModels(agg *ServiceAssociationAggregate) []*model.ServiceAssociation {
	if agg == nil {
		return []*model.ServiceAssociation{}
	}

	var models []*model.ServiceAssociation

	for _, careTypeID := range agg.TargetCareTypeIDs {
		models = append(models, &model.ServiceAssociation{
			SourceServiceID:  agg.SourceServiceID,
			TargetCareTypeID: careTypeID,
		})
	}

	for _, serviceID := range agg.TargetServiceIDs {
		models = append(models, &model.ServiceAssociation{
			SourceServiceID: agg.SourceServiceID,
			TargetServiceID: serviceID,
		})
	}

	for _, serviceType := range agg.TargetServiceTypes {
		models = append(models, &model.ServiceAssociation{
			SourceServiceID:   agg.SourceServiceID,
			TargetServiceType: serviceType,
		})
	}

	return models
}
