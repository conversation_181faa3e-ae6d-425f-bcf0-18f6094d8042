package association

import (
	"testing"

	"github.com/stretchr/testify/assert"

	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func TestServiceAssociationAggregate_NewAggregate(t *testing.T) {
	tests := []struct {
		name            string
		sourceServiceID int64
		expected        *ServiceAssociationAggregate
	}{
		{
			name:            "创建新的聚合对象",
			sourceServiceID: 123,
			expected: &ServiceAssociationAggregate{
				SourceServiceID:    123,
				TargetCareTypeIDs:  []int64{},
				TargetServiceIDs:   []int64{},
				TargetServiceTypes: []offeringpb.Service_Type{},
			},
		},
		{
			name:            "创建聚合对象，SourceServiceID 为 0",
			sourceServiceID: 0,
			expected: &ServiceAssociationAggregate{
				SourceServiceID:    0,
				TargetCareTypeIDs:  []int64{},
				TargetServiceIDs:   []int64{},
				TargetServiceTypes: []offeringpb.Service_Type{},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			agg := NewAggregate(tt.sourceServiceID)

			assert.NotNil(t, agg)
			assert.Equal(t, tt.expected.SourceServiceID, agg.SourceServiceID)
			assert.NotNil(t, agg.TargetCareTypeIDs)
			assert.NotNil(t, agg.TargetServiceIDs)
			assert.NotNil(t, agg.TargetServiceTypes)
			assert.Len(t, agg.TargetCareTypeIDs, 0)
			assert.Len(t, agg.TargetServiceIDs, 0)
			assert.Len(t, agg.TargetServiceTypes, 0)
		})
	}
}

func TestServiceAssociationAggregate_AddCareTypeTarget(t *testing.T) {
	t.Run("添加护理类型目标", func(t *testing.T) {
		agg := NewAggregate(1)

		// 添加第一个目标
		agg.AddCareTypeTarget(100)
		assert.Len(t, agg.TargetCareTypeIDs, 1)
		assert.Equal(t, int64(100), agg.TargetCareTypeIDs[0])

		// 添加第二个目标
		agg.AddCareTypeTarget(200)
		assert.Len(t, agg.TargetCareTypeIDs, 2)
		assert.Equal(t, int64(100), agg.TargetCareTypeIDs[0])
		assert.Equal(t, int64(200), agg.TargetCareTypeIDs[1])

		// 添加重复目标
		agg.AddCareTypeTarget(100)
		assert.Len(t, agg.TargetCareTypeIDs, 3)
		assert.Equal(t, int64(100), agg.TargetCareTypeIDs[0])
		assert.Equal(t, int64(200), agg.TargetCareTypeIDs[1])
		assert.Equal(t, int64(100), agg.TargetCareTypeIDs[2])
	})

	t.Run("添加零值护理类型目标", func(t *testing.T) {
		agg := NewAggregate(1)
		agg.AddCareTypeTarget(0)
		assert.Len(t, agg.TargetCareTypeIDs, 1)
		assert.Equal(t, int64(0), agg.TargetCareTypeIDs[0])
	})
}

func TestServiceAssociationAggregate_AddServiceIDTarget(t *testing.T) {
	t.Run("添加服务ID目标", func(t *testing.T) {
		agg := NewAggregate(1)

		// 添加第一个目标
		agg.AddServiceIDTarget(300)
		assert.Len(t, agg.TargetServiceIDs, 1)
		assert.Equal(t, int64(300), agg.TargetServiceIDs[0])

		// 添加第二个目标
		agg.AddServiceIDTarget(400)
		assert.Len(t, agg.TargetServiceIDs, 2)
		assert.Equal(t, int64(300), agg.TargetServiceIDs[0])
		assert.Equal(t, int64(400), agg.TargetServiceIDs[1])

		// 添加重复目标
		agg.AddServiceIDTarget(300)
		assert.Len(t, agg.TargetServiceIDs, 3)
		assert.Equal(t, int64(300), agg.TargetServiceIDs[0])
		assert.Equal(t, int64(400), agg.TargetServiceIDs[1])
		assert.Equal(t, int64(300), agg.TargetServiceIDs[2])
	})

	t.Run("添加零值服务ID目标", func(t *testing.T) {
		agg := NewAggregate(1)
		agg.AddServiceIDTarget(0)
		assert.Len(t, agg.TargetServiceIDs, 1)
		assert.Equal(t, int64(0), agg.TargetServiceIDs[0])
	})
}

func TestServiceAssociationAggregate_AddServiceTypeTarget(t *testing.T) {
	t.Run("添加服务类型目标", func(t *testing.T) {
		agg := NewAggregate(1)

		// 添加第一个目标
		agg.AddServiceTypeTarget(offeringpb.Service_SERVICE)
		assert.Len(t, agg.TargetServiceTypes, 1)
		assert.Equal(t, offeringpb.Service_SERVICE, agg.TargetServiceTypes[0])

		// 添加第二个目标
		agg.AddServiceTypeTarget(offeringpb.Service_ADD_ON)
		assert.Len(t, agg.TargetServiceTypes, 2)
		assert.Equal(t, offeringpb.Service_SERVICE, agg.TargetServiceTypes[0])
		assert.Equal(t, offeringpb.Service_ADD_ON, agg.TargetServiceTypes[1])

		// 添加重复目标
		agg.AddServiceTypeTarget(offeringpb.Service_SERVICE)
		assert.Len(t, agg.TargetServiceTypes, 3)
		assert.Equal(t, offeringpb.Service_SERVICE, agg.TargetServiceTypes[0])
		assert.Equal(t, offeringpb.Service_ADD_ON, agg.TargetServiceTypes[1])
		assert.Equal(t, offeringpb.Service_SERVICE, agg.TargetServiceTypes[2])
	})

	t.Run("添加未指定的服务类型目标", func(t *testing.T) {
		agg := NewAggregate(1)
		agg.AddServiceTypeTarget(offeringpb.Service_TYPE_UNSPECIFIED)
		assert.Len(t, agg.TargetServiceTypes, 1)
		assert.Equal(t, offeringpb.Service_TYPE_UNSPECIFIED, agg.TargetServiceTypes[0])
	})
}

func TestServiceAssociationAggregate_IsValid(t *testing.T) {
	tests := []struct {
		name        string
		agg         *ServiceAssociationAggregate
		expectedErr bool
		errorMsg    string
	}{
		{
			name: "有效的聚合对象 - 护理类型目标",
			agg: func() *ServiceAssociationAggregate {
				agg := NewAggregate(123)
				agg.AddCareTypeTarget(100)
				return agg
			}(),
			expectedErr: false,
		},
		{
			name: "有效的聚合对象 - 服务ID目标",
			agg: func() *ServiceAssociationAggregate {
				agg := NewAggregate(123)
				agg.AddServiceIDTarget(300)
				return agg
			}(),
			expectedErr: false,
		},
		{
			name: "有效的聚合对象 - 服务类型目标",
			agg: func() *ServiceAssociationAggregate {
				agg := NewAggregate(123)
				agg.AddServiceTypeTarget(offeringpb.Service_SERVICE)
				return agg
			}(),
			expectedErr: false,
		},
		{
			name: "有效的聚合对象 - 混合目标",
			agg: func() *ServiceAssociationAggregate {
				agg := NewAggregate(123)
				agg.AddCareTypeTarget(100)
				agg.AddServiceIDTarget(300)
				agg.AddServiceTypeTarget(offeringpb.Service_ADD_ON)
				return agg
			}(),
			expectedErr: false,
		},
		{
			name: "无效的聚合对象 - SourceServiceID 为 0",
			agg: func() *ServiceAssociationAggregate {
				agg := NewAggregate(0)
				agg.AddCareTypeTarget(100)
				return agg
			}(),
			expectedErr: true,
			errorMsg:    "source service ID cannot be 0",
		},
		{
			name: "无效的聚合对象 - 没有目标配置",
			agg: func() *ServiceAssociationAggregate {
				agg := NewAggregate(123)
				// 不添加任何目标
				return agg
			}(),
			expectedErr: true,
			errorMsg:    "at least one target must be configured",
		},
		{
			name: "无效的聚合对象 - 空的目标配置",
			agg: func() *ServiceAssociationAggregate {
				agg := NewAggregate(123)
				// 添加空的目标配置
				agg.TargetCareTypeIDs = []int64{}
				agg.TargetServiceIDs = []int64{}
				agg.TargetServiceTypes = []offeringpb.Service_Type{}
				return agg
			}(),
			expectedErr: true,
			errorMsg:    "at least one target must be configured",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.agg.IsValid()

			if tt.expectedErr {
				assert.Error(t, err)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestServiceAssociationAggregate_IsEmpty(t *testing.T) {
	tests := []struct {
		name     string
		agg      *ServiceAssociationAggregate
		expected bool
	}{
		{
			name:     "空聚合对象",
			agg:      NewAggregate(123),
			expected: true,
		},
		{
			name: "非空聚合对象 - 护理类型目标",
			agg: func() *ServiceAssociationAggregate {
				agg := NewAggregate(123)
				agg.AddCareTypeTarget(100)
				return agg
			}(),
			expected: false,
		},
		{
			name: "非空聚合对象 - 服务ID目标",
			agg: func() *ServiceAssociationAggregate {
				agg := NewAggregate(123)
				agg.AddServiceIDTarget(300)
				return agg
			}(),
			expected: false,
		},
		{
			name: "非空聚合对象 - 服务类型目标",
			agg: func() *ServiceAssociationAggregate {
				agg := NewAggregate(123)
				agg.AddServiceTypeTarget(offeringpb.Service_SERVICE)
				return agg
			}(),
			expected: false,
		},
		{
			name: "非空聚合对象 - 混合目标",
			agg: func() *ServiceAssociationAggregate {
				agg := NewAggregate(123)
				agg.AddCareTypeTarget(100)
				agg.AddServiceIDTarget(300)
				agg.AddServiceTypeTarget(offeringpb.Service_ADD_ON)
				return agg
			}(),
			expected: false,
		},
		{
			name: "边界情况 - 零值目标",
			agg: func() *ServiceAssociationAggregate {
				agg := NewAggregate(123)
				agg.AddCareTypeTarget(0)
				agg.AddServiceIDTarget(0)
				agg.AddServiceTypeTarget(offeringpb.Service_TYPE_UNSPECIFIED)
				return agg
			}(),
			expected: false, // 零值也被认为是非空的
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.agg.IsEmpty()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestServiceAssociationAggregate_GetTargetCount(t *testing.T) {
	tests := []struct {
		name     string
		agg      *ServiceAssociationAggregate
		expected int
	}{
		{
			name:     "空聚合对象",
			agg:      NewAggregate(123),
			expected: 0,
		},
		{
			name: "单个护理类型目标",
			agg: func() *ServiceAssociationAggregate {
				agg := NewAggregate(123)
				agg.AddCareTypeTarget(100)
				return agg
			}(),
			expected: 1,
		},
		{
			name: "多个护理类型目标",
			agg: func() *ServiceAssociationAggregate {
				agg := NewAggregate(123)
				agg.AddCareTypeTarget(100)
				agg.AddCareTypeTarget(200)
				agg.AddCareTypeTarget(300)
				return agg
			}(),
			expected: 3,
		},
		{
			name: "混合目标类型",
			agg: func() *ServiceAssociationAggregate {
				agg := NewAggregate(123)
				agg.AddCareTypeTarget(100)
				agg.AddServiceIDTarget(300)
				agg.AddServiceTypeTarget(offeringpb.Service_SERVICE)
				return agg
			}(),
			expected: 3,
		},
		{
			name: "大量目标",
			agg: func() *ServiceAssociationAggregate {
				agg := NewAggregate(123)
				// 添加 10 个护理类型目标
				for i := 1; i <= 10; i++ {
					agg.AddCareTypeTarget(int64(i))
				}
				// 添加 5 个服务ID目标
				for i := 100; i <= 104; i++ {
					agg.AddServiceIDTarget(int64(i))
				}
				// 添加 3 个服务类型目标
				agg.AddServiceTypeTarget(offeringpb.Service_SERVICE)
				agg.AddServiceTypeTarget(offeringpb.Service_ADD_ON)
				agg.AddServiceTypeTarget(offeringpb.Service_ADD_ON)
				return agg
			}(),
			expected: 18, // 10 + 5 + 3
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.agg.GetTargetCount()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestServiceAssociationAggregate_IsAllServices(t *testing.T) {
	tests := []struct {
		name     string
		agg      *ServiceAssociationAggregate
		expected bool
	}{
		{
			name:     "空聚合对象",
			agg:      NewAggregate(123),
			expected: false,
		},
		{
			name: "适用于所有服务 - 只有 SERVICE 类型",
			agg: func() *ServiceAssociationAggregate {
				agg := NewAggregate(123)
				agg.AddServiceTypeTarget(offeringpb.Service_SERVICE)
				return agg
			}(),
			expected: true,
		},
		{
			name: "不适用于所有服务 - 多个服务类型",
			agg: func() *ServiceAssociationAggregate {
				agg := NewAggregate(123)
				agg.AddServiceTypeTarget(offeringpb.Service_SERVICE)
				agg.AddServiceTypeTarget(offeringpb.Service_ADD_ON)
				return agg
			}(),
			expected: false,
		},
		{
			name: "不适用于所有服务 - 护理类型目标",
			agg: func() *ServiceAssociationAggregate {
				agg := NewAggregate(123)
				agg.AddCareTypeTarget(100)
				return agg
			}(),
			expected: false,
		},
		{
			name: "不适用于所有服务 - 服务ID目标",
			agg: func() *ServiceAssociationAggregate {
				agg := NewAggregate(123)
				agg.AddServiceIDTarget(300)
				return agg
			}(),
			expected: false,
		},
		{
			name: "不适用于所有服务 - 混合目标",
			agg: func() *ServiceAssociationAggregate {
				agg := NewAggregate(123)
				agg.AddServiceTypeTarget(offeringpb.Service_SERVICE)
				agg.AddCareTypeTarget(100)
				return agg
			}(),
			expected: true, // 根据当前实现，只要有 Service_SERVICE 就返回 true
		},
		{
			name: "不适用于所有服务 - 其他服务类型",
			agg: func() *ServiceAssociationAggregate {
				agg := NewAggregate(123)
				agg.AddServiceTypeTarget(offeringpb.Service_ADD_ON)
				return agg
			}(),
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.agg.IsAllServices()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestServiceAssociationAggregate_ComplexScenarios(t *testing.T) {
	t.Run("复杂场景 - 大量目标的管理", func(t *testing.T) {
		agg := NewAggregate(123)

		// 添加大量护理类型目标
		for i := 1; i <= 100; i++ {
			agg.AddCareTypeTarget(int64(i))
		}

		// 添加大量服务ID目标
		for i := 1000; i <= 1099; i++ {
			agg.AddServiceIDTarget(int64(i))
		}

		// 添加服务类型目标
		agg.AddServiceTypeTarget(offeringpb.Service_SERVICE)
		agg.AddServiceTypeTarget(offeringpb.Service_ADD_ON)

		// 验证总数
		assert.Equal(t, 202, agg.GetTargetCount()) // 100 + 100 + 2

		// 验证不为空
		assert.False(t, agg.IsEmpty())

		// 验证有效性
		err := agg.IsValid()
		assert.NoError(t, err)

		// 验证不适用于所有服务（因为有其他目标）
		assert.False(t, agg.IsAllServices())
	})

	t.Run("边界情况 - 零值和特殊值", func(t *testing.T) {
		agg := NewAggregate(0) // 故意使用无效的 SourceServiceID

		// 添加各种边界值
		agg.AddCareTypeTarget(0)
		agg.AddServiceIDTarget(0)
		agg.AddServiceTypeTarget(offeringpb.Service_TYPE_UNSPECIFIED)

		// 验证不为空（零值也被认为是非空的）
		assert.False(t, agg.IsEmpty())

		// 验证总数
		assert.Equal(t, 3, agg.GetTargetCount())

		// 验证无效（SourceServiceID 为 0）
		err := agg.IsValid()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "source service ID cannot be 0")
	})

	t.Run("性能测试 - 重复添加相同目标", func(t *testing.T) {
		agg := NewAggregate(123)

		// 重复添加相同的目标
		for i := 0; i < 1000; i++ {
			agg.AddCareTypeTarget(100)
			agg.AddServiceIDTarget(300)
			agg.AddServiceTypeTarget(offeringpb.Service_SERVICE)
		}

		// 验证总数（应该允许重复）
		assert.Equal(t, 3000, agg.GetTargetCount())

		// 验证不为空
		assert.False(t, agg.IsEmpty())

		// 验证有效性
		err := agg.IsValid()
		assert.NoError(t, err)
	})
}
