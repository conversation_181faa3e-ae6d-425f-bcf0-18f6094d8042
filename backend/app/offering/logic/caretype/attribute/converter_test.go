package attribute

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	pb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func TestProtoToEntity(t *testing.T) {
	// 创建Options和DefaultValue
	options, _ := structpb.NewStruct(map[string]interface{}{
		"option1": "value1",
		"option2": "value2",
	})
	defaultValue, _ := structpb.NewValue("default")

	label := "Duration"
	description := "Duration description"
	// 测试正常转换
	pbAttribute := &pb.CareTypeAttribute{
		Id:           1,
		CareTypeId:   100,
		AttributeKey: pb.AttributeKey_DURATION,
		Label:        &label,
		ValueType:    pb.ValueType_NUMBER,
		Options:      options,
		Description:  &description,
		IsRequired:   true,
		DefaultValue: defaultValue,
	}

	entity := ProtoToEntity(pbAttribute)

	assert.NotNil(t, entity)
	assert.Equal(t, int64(1), entity.ID)
	assert.Equal(t, int64(100), entity.CareTypeID)
	assert.Equal(t, pb.AttributeKey_DURATION, entity.AttributeKey)
	assert.Equal(t, "Duration", *entity.Label)
	assert.Equal(t, pb.ValueType_NUMBER, entity.ValueType)
	assert.Equal(t, options, entity.Options)
	assert.Equal(t, "Duration description", *entity.Description)
	assert.Equal(t, true, entity.IsRequired)
	assert.Equal(t, defaultValue, entity.DefaultValue)

	// 测试 nil 输入
	entity = ProtoToEntity(nil)
	assert.Nil(t, entity)
}

func TestEntityToProto(t *testing.T) {
	// 创建Options和DefaultValue
	options, _ := structpb.NewStruct(map[string]interface{}{
		"option1": "value1",
		"option2": "value2",
	})
	defaultValue, _ := structpb.NewValue("default")

	// 测试正常转换
	now := time.Now()
	deleteTime := now.Add(time.Hour)
	label := "Duration"
	description := "Duration description"
	entity := &CareTypeAttribute{
		ID:           1,
		CareTypeID:   100,
		AttributeKey: pb.AttributeKey_DURATION,
		Label:        &label,
		ValueType:    pb.ValueType_NUMBER,
		Options:      options,
		Description:  &description,
		IsRequired:   true,
		DefaultValue: defaultValue,
		CreateTime:   &now,
		UpdateTime:   &now,
		DeleteTime:   &deleteTime,
	}

	proto := EntityToProto(entity)

	assert.NotNil(t, proto)
	assert.Equal(t, int64(1), proto.Id)
	assert.Equal(t, int64(100), proto.CareTypeId)
	assert.Equal(t, pb.AttributeKey_DURATION, proto.AttributeKey)
	assert.Equal(t, "Duration", *proto.Label)
	assert.Equal(t, pb.ValueType_NUMBER, proto.ValueType)
	assert.Equal(t, options, proto.Options)
	assert.Equal(t, "Duration description", *proto.Description)
	assert.Equal(t, true, proto.IsRequired)
	assert.Equal(t, defaultValue, proto.DefaultValue)
	assert.NotNil(t, proto.CreateTime)
	assert.NotNil(t, proto.UpdateTime)
	assert.NotNil(t, proto.DeleteTime)
	assert.Equal(t, timestamppb.New(now).Seconds, proto.CreateTime.Seconds)
	assert.Equal(t, timestamppb.New(now).Seconds, proto.UpdateTime.Seconds)
	assert.Equal(t, timestamppb.New(deleteTime).Seconds, proto.DeleteTime.Seconds)

	// 测试无 DeleteTime
	entity.DeleteTime = nil
	proto = EntityToProto(entity)
	assert.NotNil(t, proto)
	assert.Nil(t, proto.DeleteTime)

	// 测试 nil 输入
	proto = EntityToProto(nil)
	assert.Nil(t, proto)
}

func TestEntityToModel(t *testing.T) {
	// 创建Options和DefaultValue
	options, _ := structpb.NewStruct(map[string]interface{}{
		"option1": "value1",
		"option2": "value2",
	})
	defaultValue, _ := structpb.NewValue("default")

	// 测试正常转换
	label := "Duration"
	description := "Duration description"
	entity := &CareTypeAttribute{
		ID:           1,
		CareTypeID:   100,
		AttributeKey: pb.AttributeKey_DURATION,
		Label:        &label,
		ValueType:    pb.ValueType_NUMBER,
		Options:      options,
		Description:  &description,
		IsRequired:   true,
		DefaultValue: defaultValue,
	}

	model, err := EntityToModel(entity)

	assert.NoError(t, err)
	assert.NotNil(t, model)
	assert.Equal(t, int64(1), model.ID)
	assert.Equal(t, int64(100), model.CareTypeID)
	assert.Equal(t, pb.AttributeKey_DURATION, model.AttributeKey)
	assert.Equal(t, "Duration", *model.Label)
	assert.Equal(t, pb.ValueType_NUMBER, model.ValueType)
	assert.NotNil(t, model.Options)
	assert.Equal(t, "Duration description", *model.Description)
	assert.Equal(t, true, model.IsRequired)
	assert.NotEmpty(t, model.DefaultValue)

	// 测试空字段
	entity.Label = nil
	entity.Description = nil
	entity.Options = nil
	entity.DefaultValue = nil

	model, err = EntityToModel(entity)
	assert.NoError(t, err)
	assert.NotNil(t, model)
	assert.Nil(t, model.Label)
	assert.Nil(t, model.Description)
	assert.Nil(t, model.Options)
	assert.Empty(t, model.DefaultValue)

	// 测试 nil 输入
	model, err = EntityToModel(nil)
	assert.NoError(t, err)
	assert.Nil(t, model)
}

func TestModelToEntity(t *testing.T) {
	// 准备测试数据
	now := time.Now()
	deleteTime := now.Add(time.Hour)
	label := "Duration"
	description := "Duration description"

	// 创建JSON字符串
	optionsJSON := `{"option1":"value1","option2":"value2"}`
	defaultValueJSON := `"default"`

	// 测试正常转换
	m := &model.CareTypeAttribute{
		ID:           1,
		CareTypeID:   100,
		AttributeKey: pb.AttributeKey_DURATION,
		Label:        &label,
		ValueType:    pb.ValueType_NUMBER,
		Options:      &optionsJSON,
		Description:  &description,
		IsRequired:   true,
		DefaultValue: defaultValueJSON,
		CreateTime:   &now,
		UpdateTime:   &now,
		DeleteTime:   &deleteTime,
	}

	entity, err := ModelToEntity(m)

	assert.NoError(t, err)
	assert.NotNil(t, entity)
	assert.Equal(t, int64(1), entity.ID)
	assert.Equal(t, int64(100), entity.CareTypeID)
	assert.Equal(t, pb.AttributeKey_DURATION, entity.AttributeKey)
	assert.Equal(t, "Duration", *entity.Label)
	assert.Equal(t, pb.ValueType_NUMBER, entity.ValueType)
	assert.NotNil(t, entity.Options)
	assert.Equal(t, "Duration description", *entity.Description)
	assert.Equal(t, true, entity.IsRequired)
	assert.NotNil(t, entity.DefaultValue)
	assert.Equal(t, &now, entity.CreateTime)
	assert.Equal(t, &now, entity.UpdateTime)
	assert.Equal(t, &deleteTime, entity.DeleteTime)

	// 测试空字段
	m = &model.CareTypeAttribute{
		ID:         1,
		CareTypeID: 100,
		IsRequired: false,
	}
	entity, err = ModelToEntity(m)
	assert.NoError(t, err)
	assert.NotNil(t, entity)
	assert.Nil(t, entity.Label)
	assert.Nil(t, entity.Description)
	assert.Nil(t, entity.Options)
	assert.Nil(t, entity.DefaultValue)
	assert.Nil(t, entity.CreateTime)
	assert.Nil(t, entity.UpdateTime)
	assert.Nil(t, entity.DeleteTime)

	// 测试 nil 输入
	entity, err = ModelToEntity(nil)
	assert.NoError(t, err)
	assert.Nil(t, entity)
}

func TestModelToEntityList(t *testing.T) {
	// 准备测试数据
	label1 := "Duration"
	label2 := "Required"

	models := []*model.CareTypeAttribute{
		{ID: 1, CareTypeID: 100, Label: &label1},
		{ID: 2, CareTypeID: 100, Label: &label2},
	}

	// 测试正常转换
	entities, err := ModelToEntityList(models)

	assert.NoError(t, err)
	assert.NotNil(t, entities)
	assert.Len(t, entities, 2)
	assert.Equal(t, int64(1), entities[0].ID)
	assert.Equal(t, "Duration", *entities[0].Label)
	assert.Equal(t, int64(2), entities[1].ID)
	assert.Equal(t, "Required", *entities[1].Label)

	// 测试空列表
	entities, err = ModelToEntityList([]*model.CareTypeAttribute{})
	assert.NoError(t, err)
	assert.Empty(t, entities)

	// 测试 nil 列表
	entities, err = ModelToEntityList(nil)
	assert.NoError(t, err)
	assert.Empty(t, entities)
}

func TestEntityToProtoList(t *testing.T) {
	// 准备测试数据
	now := time.Now()
	label1 := "Duration"
	label2 := "Required"
	entities := []*CareTypeAttribute{
		{ID: 1, Label: &label1, CreateTime: &now, UpdateTime: &now},
		{ID: 2, Label: &label2, CreateTime: &now, UpdateTime: &now},
	}

	// 测试正常转换
	protos := EntityToProtoList(entities)

	assert.NotNil(t, protos)
	assert.Len(t, protos, 2)
	assert.Equal(t, int64(1), protos[0].Id)
	assert.Equal(t, "Duration", *protos[0].Label)
	assert.Equal(t, int64(2), protos[1].Id)
	assert.Equal(t, "Required", *protos[1].Label)

	// 测试空列表
	protos = EntityToProtoList([]*CareTypeAttribute{})
	assert.Empty(t, protos)

	// 测试 nil 列表
	protos = EntityToProtoList(nil)
	assert.Empty(t, protos)
}
