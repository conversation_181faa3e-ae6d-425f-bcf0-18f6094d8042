package attribute

import (
	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	pb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func ProtoToEntity(p *pb.CareTypeAttribute) *CareTypeAttribute {
	if p == nil {
		return nil
	}

	return &CareTypeAttribute{
		ID:           p.GetId(),
		CareTypeID:   p.GetCareTypeId(),
		AttributeKey: p.GetAttributeKey(),
		Label:        p.Label,
		ValueType:    p.GetValueType(),
		Options:      p.Options,
		Description:  p.Description,
		IsRequired:   p.GetIsRequired(),
		DefaultValue: p.DefaultValue,
	}
}

func EntityToProto(e *CareTypeAttribute) *pb.CareTypeAttribute {
	if e == nil {
		return nil
	}
	pb := &pb.CareTypeAttribute{
		Id:           e.ID,
		CareTypeId:   e.CareTypeID,
		AttributeKey: e.AttributeKey,
		FieldName:    e.FieldName,
		Label:        e.Label,
		ValueType:    e.ValueType,
		Options:      e.Options,
		Description:  e.Description,
		IsRequired:   e.IsRequired,
		DefaultValue: e.DefaultValue,
	}
	if e.CreateTime != nil {
		pb.CreateTime = timestamppb.New(*e.CreateTime)
	}
	if e.UpdateTime != nil {
		pb.UpdateTime = timestamppb.New(*e.UpdateTime)
	}
	if e.DeleteTime != nil {
		pb.DeleteTime = timestamppb.New(*e.DeleteTime)
	}

	return pb
}

func EntityToModel(e *CareTypeAttribute) (*model.CareTypeAttribute, error) {
	if e == nil {
		return nil, nil
	}
	m := &model.CareTypeAttribute{
		ID:           e.ID,
		CareTypeID:   e.CareTypeID,
		AttributeKey: e.AttributeKey,
		Label:        e.Label,
		Description:  e.Description,
		ValueType:    e.ValueType,
		IsRequired:   e.IsRequired,
	}

	if e.DefaultValue != nil {
		defaultValueJSON, err := e.DefaultValue.MarshalJSON()
		if err != nil {
			return nil, err
		}
		defaultValueStr := string(defaultValueJSON)
		m.DefaultValue = defaultValueStr
	}

	if e.Options != nil {
		optionsJSON, err := e.Options.MarshalJSON()
		if err != nil {
			return nil, err
		}
		optionsStr := string(optionsJSON)
		m.Options = &optionsStr
	}

	return m, nil
}

func EntityToModels(es []*CareTypeAttribute) ([]*model.CareTypeAttribute, error) {
	models := make([]*model.CareTypeAttribute, 0, len(es))
	for _, e := range es {
		m, err := EntityToModel(e)
		if err != nil {
			return nil, err
		}
		models = append(models, m)
	}

	return models, nil
}

func ModelToEntity(m *model.CareTypeAttribute) (*CareTypeAttribute, error) {
	if m == nil {
		return nil, nil
	}
	e := &CareTypeAttribute{
		ID:           m.ID,
		CareTypeID:   m.CareTypeID,
		AttributeKey: m.AttributeKey,
		FieldName:    m.FieldName,
		Label:        m.Label,
		Description:  m.Description,
		ValueType:    m.ValueType,
		IsRequired:   m.IsRequired,
		CreateTime:   m.CreateTime,
		UpdateTime:   m.UpdateTime,
		DeleteTime:   m.DeleteTime,
	}

	if m.DefaultValue != "" {
		val := &structpb.Value{}
		if err := val.UnmarshalJSON([]byte(m.DefaultValue)); err != nil {
			return nil, err
		}
		e.DefaultValue = val
	}

	if m.Options != nil {
		opts := &structpb.Struct{}
		if err := opts.UnmarshalJSON([]byte(*m.Options)); err != nil {
			return nil, err
		}
		e.Options = opts
	}

	return e, nil
}

func ModelToEntityList(ms []*model.CareTypeAttribute) ([]*CareTypeAttribute, error) {
	es := make([]*CareTypeAttribute, 0, len(ms))
	for _, m := range ms {
		e, err := ModelToEntity(m)
		if err != nil {
			return nil, err
		}
		es = append(es, e)
	}

	return es, nil
}

func EntityToProtoList(es []*CareTypeAttribute) []*pb.CareTypeAttribute {
	ps := make([]*pb.CareTypeAttribute, 0, len(es))
	for _, e := range es {
		ps = append(ps, EntityToProto(e))
	}

	return ps
}
