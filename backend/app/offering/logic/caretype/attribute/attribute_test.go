package attribute

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"google.golang.org/protobuf/types/known/structpb"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/caretypeattribute"
	mock "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/caretypeattribute/mocks"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	pb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func TestLogic_CreateCareTypeAttribute(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := &Logic{repo: mockRepo}

	label := "Test Label"
	description := "Test Description"
	e := &CareTypeAttribute{
		CareTypeID:   100,
		AttributeKey: pb.AttributeKey_DURATION,
		Label:        &label,
		ValueType:    pb.ValueType_NUMBER,
		Description:  &description,
		IsRequired:   true,
	}
	mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, m *model.CareTypeAttribute) error {
		m.ID = 1
		return nil
	})

	result, err := logic.CreateCareTypeAttribute(context.Background(), e)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result.ID)
	assert.Equal(t, "Test Label", *result.Label)
	assert.Equal(t, pb.AttributeKey_DURATION, result.AttributeKey)
	assert.Equal(t, pb.ValueType_NUMBER, result.ValueType)
	assert.Equal(t, "Test Description", *result.Description)
	assert.Equal(t, true, result.IsRequired)
}

func TestLogic_CreateCareTypeAttribute_Error(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := &Logic{repo: mockRepo}

	label := "Test Label"
	e := &CareTypeAttribute{Label: &label}
	mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(errors.New("create error"))

	result, err := logic.CreateCareTypeAttribute(context.Background(), e)

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "create error")
}

func TestLogic_GetCareTypeAttribute(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := &Logic{repo: mockRepo}

	label := "Test Label"
	description := "Test Description"
	mockRepo.EXPECT().
		Get(gomock.Any(), int64(1)).
		Return(&model.CareTypeAttribute{
			ID:           1,
			CareTypeID:   100,
			AttributeKey: pb.AttributeKey_DURATION,
			Label:        &label,
			ValueType:    pb.ValueType_NUMBER,
			Description:  &description,
			IsRequired:   true,
		}, nil)

	result, err := logic.GetCareTypeAttribute(context.Background(), 1)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result.ID)
	assert.Equal(t, int64(100), result.CareTypeID)
	assert.Equal(t, "Test Label", *result.Label)
	assert.Equal(t, pb.AttributeKey_DURATION, result.AttributeKey)
	assert.Equal(t, pb.ValueType_NUMBER, result.ValueType)
	assert.Equal(t, "Test Description", *result.Description)
	assert.Equal(t, true, result.IsRequired)
}

func TestLogic_GetCareTypeAttribute_Error(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := &Logic{repo: mockRepo}

	mockRepo.EXPECT().
		Get(gomock.Any(), int64(1)).
		Return(nil, errors.New("get error"))

	result, err := logic.GetCareTypeAttribute(context.Background(), 1)

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "get error")
}

func TestLogic_UpdateCareTypeAttribute(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := &Logic{repo: mockRepo}

	label := "Updated Label"
	description := "Updated Description"
	e := &CareTypeAttribute{
		ID:           1,
		CareTypeID:   100,
		AttributeKey: pb.AttributeKey_DURATION,
		Label:        &label,
		ValueType:    pb.ValueType_NUMBER,
		Description:  &description,
		IsRequired:   true,
	}

	updatedLabel := "Updated Label"
	updatedDescription := "Updated Description"
	updatedModel := &model.CareTypeAttribute{
		ID:           1,
		CareTypeID:   100,
		AttributeKey: pb.AttributeKey_DURATION,
		Label:        &updatedLabel,
		ValueType:    pb.ValueType_NUMBER,
		Description:  &updatedDescription,
		IsRequired:   true,
	}

	mockRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
	mockRepo.EXPECT().Get(gomock.Any(), int64(1)).Return(updatedModel, nil)

	result, err := logic.UpdateCareTypeAttribute(context.Background(), e)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result.ID)
	assert.Equal(t, "Updated Label", *result.Label)
	assert.Equal(t, "Updated Description", *result.Description)
}

func TestLogic_UpdateCareTypeAttribute_UpdateError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := &Logic{repo: mockRepo}

	label := "Updated Label"
	e := &CareTypeAttribute{ID: 1, Label: &label}
	mockRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(errors.New("update error"))

	result, err := logic.UpdateCareTypeAttribute(context.Background(), e)

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "update error")
}

func TestLogic_UpdateCareTypeAttribute_GetError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := &Logic{repo: mockRepo}

	label := "Updated Label"
	e := &CareTypeAttribute{ID: 1, Label: &label}
	mockRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
	mockRepo.EXPECT().Get(gomock.Any(), int64(1)).Return(nil, errors.New("get error"))

	result, err := logic.UpdateCareTypeAttribute(context.Background(), e)

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "get error")
}

func TestLogic_ListCareTypeAttributes(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := &Logic{repo: mockRepo}

	label1 := "Duration"
	label2 := "Lodging Required"
	description1 := "Service duration in minutes"
	description2 := "Whether lodging is required"

	mockRepo.EXPECT().
		List(gomock.Any(), &caretypeattribute.ListCareTypeAttributeFilter{
			CareTypeID: 123,
		}).
		Return([]*model.CareTypeAttribute{
			{
				ID:           1,
				CareTypeID:   123,
				AttributeKey: pb.AttributeKey_DURATION,
				Label:        &label1,
				ValueType:    pb.ValueType_NUMBER,
				Description:  &description1,
				IsRequired:   true,
			},
			{
				ID:           2,
				CareTypeID:   123,
				AttributeKey: pb.AttributeKey_STAFF_AUTO_ASSIGN,
				Label:        &label2,
				ValueType:    pb.ValueType_BOOLEAN,
				Description:  &description2,
				IsRequired:   false,
			},
		}, nil)

	result, err := logic.ListCareTypeAttributes(context.Background(), 123)

	assert.NoError(t, err)
	assert.Len(t, result, 2)
	assert.Equal(t, int64(1), result[0].ID)
	assert.Equal(t, int64(123), result[0].CareTypeID)
	assert.Equal(t, pb.AttributeKey_DURATION, result[0].AttributeKey)
	assert.Equal(t, "Duration", *result[0].Label)
	assert.Equal(t, pb.ValueType_NUMBER, result[0].ValueType)
	assert.Equal(t, "Service duration in minutes", *result[0].Description)
	assert.True(t, result[0].IsRequired)

	assert.Equal(t, int64(2), result[1].ID)
	assert.Equal(t, int64(123), result[1].CareTypeID)
	assert.Equal(t, pb.AttributeKey_STAFF_AUTO_ASSIGN, result[1].AttributeKey)
	assert.Equal(t, "Lodging Required", *result[1].Label)
	assert.Equal(t, pb.ValueType_BOOLEAN, result[1].ValueType)
	assert.Equal(t, "Whether lodging is required", *result[1].Description)
	assert.False(t, result[1].IsRequired)
}

func TestLogic_ListCareTypeAttributes_Error(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := &Logic{repo: mockRepo}

	mockRepo.EXPECT().
		List(gomock.Any(), &caretypeattribute.ListCareTypeAttributeFilter{
			CareTypeID: 100,
		}).
		Return(nil, errors.New("list error"))

	results, err := logic.ListCareTypeAttributes(context.Background(), 100)

	assert.Error(t, err)
	assert.Nil(t, results)
	assert.Contains(t, err.Error(), "list error")
}

func TestLogic_DeleteCareTypeAttribute(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := &Logic{repo: mockRepo}

	mockRepo.EXPECT().Delete(gomock.Any(), int64(1)).Return(nil)
	mockRepo.EXPECT().Delete(gomock.Any(), int64(2)).Return(errors.New("not found"))

	err := logic.DeleteCareTypeAttribute(context.Background(), 1)
	assert.NoError(t, err)

	err = logic.DeleteCareTypeAttribute(context.Background(), 2)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not found")
}

func TestLogic_CreateCareTypeAttributeWithOptions(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := &Logic{repo: mockRepo}

	// 创建带选项的属性
	options, _ := structpb.NewStruct(map[string]interface{}{
		"option1": "value1",
		"option2": "value2",
	})
	defaultValue, _ := structpb.NewValue(42.0)

	label := "Duration"
	e := &CareTypeAttribute{
		CareTypeID:   100,
		AttributeKey: pb.AttributeKey_DURATION,
		Label:        &label,
		ValueType:    pb.ValueType_NUMBER,
		Options:      options,
		DefaultValue: defaultValue,
		IsRequired:   true,
	}

	mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, m *model.CareTypeAttribute) error {
		m.ID = 1
		return nil
	})

	result, err := logic.CreateCareTypeAttribute(context.Background(), e)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result.ID)
	assert.Equal(t, "Duration", *result.Label)
	assert.Equal(t, options, result.Options)
	assert.Equal(t, defaultValue, result.DefaultValue)
}

func TestLogic_BatchCreateCareTypeAttributes(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := &Logic{repo: mockRepo}

	// 创建多个属性
	label1 := "Duration"
	label2 := "Lodging Required"
	attributes := []*CareTypeAttribute{
		{
			CareTypeID:   100,
			AttributeKey: pb.AttributeKey_DURATION,
			Label:        &label1,
			ValueType:    pb.ValueType_NUMBER,
			IsRequired:   true,
		},
		{
			CareTypeID:   100,
			AttributeKey: pb.AttributeKey_STAFF_AUTO_ASSIGN,
			Label:        &label2,
			ValueType:    pb.ValueType_BOOLEAN,
			IsRequired:   false,
		},
	}

	// 模拟批量创建成功
	mockRepo.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, models []*model.CareTypeAttribute) error {
		// 模拟设置ID
		for i, m := range models {
			m.ID = int64(i + 1)
		}
		return nil
	})

	results, err := logic.BatchCreateCareTypeAttributes(context.Background(), attributes)

	assert.NoError(t, err)
	assert.Nil(t, results) // 根据实现，BatchCreateCareTypeAttributes返回nil, nil
}

func TestLogic_BatchCreateCareTypeAttributes_PartialError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := &Logic{repo: mockRepo}

	// 创建多个属性
	label1 := "Duration"
	label2 := "Lodging Required"
	attributes := []*CareTypeAttribute{
		{
			CareTypeID:   100,
			AttributeKey: pb.AttributeKey_DURATION,
			Label:        &label1,
			ValueType:    pb.ValueType_NUMBER,
			IsRequired:   true,
		},
		{
			CareTypeID:   100,
			AttributeKey: pb.AttributeKey_STAFF_AUTO_ASSIGN,
			Label:        &label2,
			ValueType:    pb.ValueType_BOOLEAN,
			IsRequired:   false,
		},
	}

	// 模拟批量创建失败
	mockRepo.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(errors.New("create error"))

	results, err := logic.BatchCreateCareTypeAttributes(context.Background(), attributes)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "create error")
	assert.Nil(t, results)
}
