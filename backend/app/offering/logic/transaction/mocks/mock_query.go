package mocks

import (
	"database/sql"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
)

// MockQuery 是一个通用的 mock 实现
type MockQuery struct {
	transactionFunc func(fc func(tx *query.Query) error, opts ...*sql.TxOptions) error
}

func (mq *MockQuery) Transaction(fc func(tx *query.Query) error, opts ...*sql.TxOptions) error {
	return mq.transactionFunc(fc, opts...)
}

// 提供便捷的创建方法
func NewMockQuery() *MockQuery {
	return &MockQuery{}
}

// 设置事务成功的期望
func (mq *MockQuery) ExpectTransactionSuccess() {
	mq.transactionFunc = func(fc func(tx *query.Query) error, _ ...*sql.TxOptions) error {
		return fc(nil)
	}
}

// 设置事务失败的期望
func (mq *MockQuery) ExpectTransactionError(err error) {
	mq.transactionFunc = func(_ func(tx *query.Query) error, _ ...*sql.TxOptions) error {
		return err
	}
}
