package service

import (
	"testing"
	"time"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

func TestProtoToEntity(t *testing.T) {
	// 测试正常转换
	now := time.Now()
	pbTimestamp := timestamppb.New(now)

	description := "Test Description"
	pbService := &offeringpb.Service{
		Id:               1,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		CareTypeId:       456,
		CategoryId:       lo.ToPtr(int64(789)),
		Name:             "Test Service",
		Description:      &description,
		ColorCode:        "#FF0000",
		Sort:             lo.ToPtr(int64(10)),
		Images:           []string{"image1.jpg", "image2.jpg"},
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.Service_ACTIVE,
		CreateTime:       pbTimestamp,
		UpdateTime:       pbTimestamp,
	}

	entity := ProtoToEntity(pbService)

	assert.NotNil(t, entity)
	assert.Equal(t, int64(1), entity.ID)
	assert.Equal(t, organizationpb.OrganizationType_COMPANY, entity.OrganizationType)
	assert.Equal(t, int64(123), entity.OrganizationID)
	assert.Equal(t, int64(456), entity.CareTypeID)
	assert.Equal(t, int64(789), *entity.CategoryID)
	assert.Equal(t, "Test Service", entity.Name)
	assert.Equal(t, "Test Description", *entity.Description)
	assert.Equal(t, "#FF0000", entity.ColorCode)
	assert.Equal(t, int64(10), *entity.Sort)
	assert.Equal(t, []string{"image1.jpg", "image2.jpg"}, entity.Images)
	assert.Equal(t, offeringpb.OfferingSource_MOEGO, entity.Source)
	assert.True(t, entity.Status == offeringpb.Service_ACTIVE)

	// 测试 nil 输入
	entity = ProtoToEntity(nil)
	assert.Nil(t, entity)
}

func TestEntityToProto(t *testing.T) {
	// 测试正常转换
	now := time.Now()
	deleteTime := now.Add(time.Hour)

	description := "Test Description"
	entity := &Service{
		ID:               1,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationID:   123,
		CareTypeID:       456,
		CategoryID:       lo.ToPtr(int64(789)),
		Name:             "Test Service",
		Description:      &description,
		ColorCode:        "#FF0000",
		Sort:             lo.ToPtr(int64(10)),
		Images:           []string{"image1.jpg", "image2.jpg"},
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.Service_ACTIVE,
		CreateTime:       &now,
		UpdateTime:       &now,
		DeleteTime:       &deleteTime,
	}

	proto := EntityToProto(entity)

	assert.NotNil(t, proto)
	assert.Equal(t, int64(1), proto.Id)
	assert.Equal(t, organizationpb.OrganizationType_COMPANY, proto.OrganizationType)
	assert.Equal(t, int64(123), proto.OrganizationId)
	assert.Equal(t, int64(456), proto.CareTypeId)
	assert.Equal(t, int64(789), *proto.CategoryId)
	assert.Equal(t, "Test Service", proto.Name)
	assert.Equal(t, "Test Description", *proto.Description)
	assert.Equal(t, "#FF0000", proto.ColorCode)
	assert.Equal(t, int64(10), *proto.Sort)
	assert.Equal(t, []string{"image1.jpg", "image2.jpg"}, proto.Images)
	assert.Equal(t, offeringpb.OfferingSource_MOEGO, proto.Source)
	assert.True(t, proto.Status == offeringpb.Service_ACTIVE)
	assert.NotNil(t, proto.CreateTime)
	assert.NotNil(t, proto.UpdateTime)
	assert.NotNil(t, proto.DeleteTime)
	assert.Equal(t, timestamppb.New(now).Seconds, proto.CreateTime.Seconds)
	assert.Equal(t, timestamppb.New(now).Seconds, proto.UpdateTime.Seconds)
	assert.Equal(t, timestamppb.New(deleteTime).Seconds, proto.DeleteTime.Seconds)

	// 测试无 DeleteTime
	entity.DeleteTime = nil
	proto = EntityToProto(entity)
	assert.NotNil(t, proto)
	assert.Nil(t, proto.DeleteTime)

	// 测试 nil 输入
	proto = EntityToProto(nil)
	assert.Nil(t, proto)
}

func TestEntityToModel(t *testing.T) {
	// 测试正常转换
	now := time.Now()
	deleteTime := now.Add(time.Hour)
	description := "Test Description"

	entity := &Service{
		ID:               1,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationID:   123,
		CareTypeID:       456,
		CategoryID:       lo.ToPtr(int64(789)),
		Name:             "Test Service",
		Description:      &description,
		ColorCode:        "#FF0000",
		Sort:             lo.ToPtr(int64(10)),
		Images:           []string{"image1.jpg", "image2.jpg"},
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.Service_ACTIVE,
		CreateTime:       &now,
		UpdateTime:       &now,
		DeleteTime:       &deleteTime,
	}

	model := EntityToModel(entity)

	assert.NotNil(t, model)
	assert.Equal(t, int64(1), model.ID)
	assert.Equal(t, organizationpb.OrganizationType_COMPANY, model.OrganizationType)
	assert.Equal(t, int64(123), model.OrganizationID)
	assert.Equal(t, int64(456), model.CareTypeID)
	assert.Equal(t, int64(789), model.CategoryID)
	assert.Equal(t, "Test Service", model.Name)
	assert.Equal(t, "Test Description", *model.Description)
	assert.Equal(t, "#FF0000", model.ColorCode)
	assert.Equal(t, int64(10), model.Sort)
	assert.Equal(t, []string{"image1.jpg", "image2.jpg"}, model.Images)
	assert.Equal(t, offeringpb.OfferingSource_MOEGO, model.Source)
	assert.True(t, model.Status == offeringpb.Service_ACTIVE)
	// EntityToModel 不设置 CreateTime 和 UpdateTime，所以不进行断言

	// 测试空字段
	entity.Description = nil
	entity.Images = nil
	entity.DeleteTime = nil

	model = EntityToModel(entity)
	assert.NotNil(t, model)
	assert.Nil(t, model.Description)
	assert.Nil(t, model.Images)
	assert.Nil(t, model.DeleteTime)

	// 测试 nil 输入
	model = EntityToModel(nil)
	assert.Nil(t, model)
}

func TestModelToEntity(t *testing.T) {
	// 准备测试数据
	now := time.Now()
	deleteTime := now.Add(time.Hour)
	description := "Test Description"

	// 测试正常转换
	m := &model.Service{
		ID:               1,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationID:   123,
		CareTypeID:       456,
		CategoryID:       789,
		Name:             "Test Service",
		Description:      &description,
		ColorCode:        "#FF0000",
		Sort:             10,
		Images:           []string{"image1.jpg", "image2.jpg"},
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.Service_ACTIVE,
		CreateTime:       &now,
		UpdateTime:       &now,
		DeleteTime:       &deleteTime,
	}

	entity := ModelToEntity(m)

	assert.NotNil(t, entity)
	assert.Equal(t, int64(1), entity.ID)
	assert.Equal(t, organizationpb.OrganizationType_COMPANY, entity.OrganizationType)
	assert.Equal(t, int64(123), entity.OrganizationID)
	assert.Equal(t, int64(456), entity.CareTypeID)
	assert.Equal(t, int64(789), *entity.CategoryID)
	assert.Equal(t, "Test Service", entity.Name)
	assert.Equal(t, "Test Description", *entity.Description)
	assert.Equal(t, "#FF0000", entity.ColorCode)
	assert.Equal(t, int64(10), *entity.Sort)
	assert.Equal(t, []string{"image1.jpg", "image2.jpg"}, entity.Images)
	assert.Equal(t, offeringpb.OfferingSource_MOEGO, entity.Source)
	assert.True(t, entity.Status == offeringpb.Service_ACTIVE)
	assert.Equal(t, &now, entity.CreateTime)
	assert.Equal(t, &now, entity.UpdateTime)
	assert.Equal(t, &deleteTime, entity.DeleteTime)

	// 测试空字段
	m = &model.Service{
		ID:         1,
		Status:     offeringpb.Service_INACTIVE,
		Images:     []string{},
		CreateTime: &now,
		UpdateTime: &now,
	}
	entity = ModelToEntity(m)
	assert.NotNil(t, entity)
	assert.Nil(t, entity.Description)
	assert.Empty(t, entity.Images)
	assert.Nil(t, entity.DeleteTime)

	// 测试 nil 输入
	entity = ModelToEntity(nil)
	assert.Nil(t, entity)
}
