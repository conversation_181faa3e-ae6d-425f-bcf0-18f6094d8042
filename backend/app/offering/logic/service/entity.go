package service

import (
	"fmt"
	"time"

	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

// Service represents a service entity
type Service struct {
	ID               int64
	OrganizationType organizationpb.OrganizationType
	OrganizationID   int64
	CareTypeID       int64
	CategoryID       *int64
	Name             string
	Description      *string
	ColorCode        string
	Sort             *int64
	Images           []string
	Source           offeringpb.OfferingSource
	Status           offeringpb.Service_Status
	Type             offeringpb.Service_Type
	CreateTime       *time.Time
	UpdateTime       *time.Time
	DeleteTime       *time.Time
}

// Aggregate includes Service and its related entities (ServiceAttribute, ServiceBinding, etc.)
type Aggregate struct {
	Service           *Service
	Attributes        *offeringpb.ServiceAttributes
	AvailableBusiness *offeringpb.AvailableBusiness
	AdditionalService *offeringpb.AdditionalService
}

// NewAggregate creates a new Aggregate
func NewAggregate() *Aggregate {
	return &Aggregate{
		Service:    &Service{},
		Attributes: &offeringpb.ServiceAttributes{},
	}
}

// IsValid checks if the aggregate is valid
func (a *Aggregate) IsValid() error {
	if a.Service == nil {
		return fmt.Errorf("service cannot be nil")
	}

	if a.Service.Name == "" {
		return fmt.Errorf("service name cannot be empty")
	}

	if a.Service.OrganizationType == organizationpb.OrganizationType_ORGANIZATION_TYPE_UNSPECIFIED {
		return fmt.Errorf("organization type cannot be unspecified")
	}

	if a.Service.OrganizationID == 0 {
		return fmt.Errorf("organization id cannot be 0")
	}

	return nil
}
