package category

import (
	"context"
	"fmt"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	servicecategory2 "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/servicecategory"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func New() *Logic {
	return &Logic{
		repo: servicecategory2.NewRepository(),
	}
}

// NewWithRepository creates a new Logic with a custom repository (useful for testing)
func NewWithRepository(repo servicecategory2.Repository) *Logic {
	return &Logic{
		repo: repo,
	}
}

type Logic struct {
	repo servicecategory2.Repository
}

// ListCategories lists service categories based on the request
func (l *Logic) ListCategories(ctx context.Context,
	req *offeringpb.ListCategoriesRequest) (*offeringpb.ListCategoriesResponse, error) {
	// Convert request to repository filter
	filter := &servicecategory2.ListServiceCategoryFilter{
		OrganizationID:   req.GetOrganizationId(),
		OrganizationType: req.GetOrganizationType(),
		Types:            []offeringpb.Service_Type{offeringpb.Service_SERVICE},
	}
	if req.GetFilter() != nil {
		filter.CareTypeIDs = req.GetFilter().GetCareTypeIds()
		filter.IDs = req.GetFilter().GetCategoryIds()
	}

	// Get categories from repository
	categories, _, err := l.repo.List(ctx, filter, nil)
	if err != nil {
		return nil, err
	}

	// Convert models to entities
	entities := ModelToEntityList(categories)

	// Convert entities to protobuf messages
	protoCategories := EntityToProtoList(entities)

	return &offeringpb.ListCategoriesResponse{
		Categories: protoCategories,
	}, nil
}

// SaveCategories saves categories based on the request
func (l *Logic) SaveCategories(ctx context.Context,
	req *offeringpb.SaveCategoriesRequest) (*offeringpb.SaveCategoriesResponse, error) {
	// 使用事务执行所有数据库操作
	q := query.Use(db.GetDB())
	err := q.Transaction(func(tx *query.Query) error {
		// 创建带事务的 repository
		txRepo := l.repo.WithQuery(tx)

		// 1. 删除指定的分类
		err := l.deleteCategoriesWithRepo(ctx, req, txRepo)
		if err != nil {
			return fmt.Errorf("failed to delete categories: %w", err)
		}

		// 2. 更新现有分类
		err = l.updateCategoriesWithRepo(ctx, req, txRepo)
		if err != nil {
			return fmt.Errorf("failed to update categories: %w", err)
		}

		// 3. 创建新分类
		err = l.createCategoriesWithRepo(ctx, req, txRepo)
		if err != nil {
			return fmt.Errorf("failed to create categories: %w", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &offeringpb.SaveCategoriesResponse{}, nil
}

// buildNewCategoryModel 构建新分类模型
func (l *Logic) buildNewCategoryModel(
	req *offeringpb.SaveCategoriesRequest,
	category *offeringpb.SaveCategoriesRequest_CategoryCreateDef,
	sort int32) *model.ServiceCategory {
	return &model.ServiceCategory{
		OrganizationType: req.OrganizationType,
		OrganizationID:   req.OrganizationId,
		CareTypeID:       category.CareTypeId,
		Name:             category.Name,
		Sort:             sort,
		Type:             offeringpb.Service_SERVICE,
	}
}

// deleteCategoriesWithRepo 使用指定的 repository 删除指定的分类
func (l *Logic) deleteCategoriesWithRepo(
	ctx context.Context,
	req *offeringpb.SaveCategoriesRequest,
	repo servicecategory2.Repository) error {
	for _, id := range req.DeleteIds {
		orgType := req.OrganizationType
		where := &servicecategory2.WhereCondition{
			ID:               id,
			OrganizationType: &orgType,
			OrganizationID:   &req.OrganizationId,
			IncludeDeleted:   false, // 删除时不需要包含已删除的记录
		}
		err := repo.Delete(ctx, where)
		if err != nil {
			return fmt.Errorf("failed to delete category %d: %w", id, err)
		}
	}

	return nil
}

// createCategoriesWithRepo 使用指定的 repository 创建新分类
func (l *Logic) createCategoriesWithRepo(
	ctx context.Context,
	req *offeringpb.SaveCategoriesRequest,
	repo servicecategory2.Repository) error {
	for _, category := range req.CreateCategories {
		newCategory := l.buildNewCategoryModel(req, category, int32(category.Sort))
		err := repo.Create(ctx, newCategory)
		if err != nil {
			return fmt.Errorf("failed to create category: %w", err)
		}
	}

	return nil
}

// updateCategoriesWithRepo 使用指定的 repository 更新现有分类
func (l *Logic) updateCategoriesWithRepo(
	ctx context.Context,
	req *offeringpb.SaveCategoriesRequest,
	repo servicecategory2.Repository) error {
	for _, category := range req.UpdateCategories {

		// 获取现有分类
		existingCategory, err := repo.Get(ctx, category.Id)
		if err != nil {
			return fmt.Errorf("failed to get category %d: %w", category.Id, err)
		}

		// 更新字段
		existingCategory.Name = category.Name
		existingCategory.CareTypeID = category.CareTypeId
		existingCategory.Sort = int32(category.Sort)

		orgType := req.OrganizationType
		where := &servicecategory2.WhereCondition{
			ID:               category.Id,
			OrganizationType: &orgType,
			OrganizationID:   &req.OrganizationId,
			IncludeDeleted:   false, // 更新时不需要包含已删除的记录
		}
		err = repo.Update(ctx, existingCategory, where)
		if err != nil {
			return fmt.Errorf("failed to update category %d: %w", category.Id, err)
		}
	}

	return nil
}
