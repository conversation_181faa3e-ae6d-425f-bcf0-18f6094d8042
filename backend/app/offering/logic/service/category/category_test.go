package category

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	mock "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/servicecategory/mocks"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

func TestLogic_SaveCategories_Success(t *testing.T) {
	// 跳过这个测试，因为需要真实的数据库连接
	t.Skip("Skipping TestLogic_SaveCategories_Success due to database dependency")
}

func TestLogic_BuildNewCategoryModel(t *testing.T) {
	logic := &Logic{}

	req := &offeringpb.SaveCategoriesRequest{
		OrganizationType: 2,
		OrganizationId:   123,
	}

	category := &offeringpb.SaveCategoriesRequest_CategoryCreateDef{
		CareTypeId: 1,
		Name:       "Test Category",
		Sort:       5,
	}

	model := logic.buildNewCategoryModel(req, category, int32(category.Sort))

	assert.Equal(t, organizationpb.OrganizationType_COMPANY, model.OrganizationType)
	assert.Equal(t, int64(123), model.OrganizationID)
	assert.Equal(t, int64(1), model.CareTypeID)
	assert.Equal(t, "Test Category", model.Name)
	assert.Equal(t, int32(5), model.Sort)
}

func TestLogic_ListCategories(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo)

	req := &offeringpb.ListCategoriesRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Filter: &offeringpb.ListCategoriesRequest_Filter{
			CareTypeIds: []int64{1, 2},
		},
	}

	// Mock repository response
	now := time.Now()
	mockCategories := []*model.ServiceCategory{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			CareTypeID:       1,
			Name:             "Category 1",
			Sort:             1,
			CreateTime:       &now,
			UpdateTime:       &now,
		},
		{
			ID:               2,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			CareTypeID:       2,
			Name:             "Category 2",
			Sort:             2,
			CreateTime:       &now,
			UpdateTime:       &now,
		},
	}

	mockRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockCategories, int64(2), nil)

	resp, err := logic.ListCategories(context.Background(), req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Len(t, resp.Categories, 2)
	assert.Equal(t, int64(1), resp.Categories[0].Id)
	assert.Equal(t, "Category 1", resp.Categories[0].Name)
	assert.Equal(t, int64(2), resp.Categories[1].Id)
	assert.Equal(t, "Category 2", resp.Categories[1].Name)
}
