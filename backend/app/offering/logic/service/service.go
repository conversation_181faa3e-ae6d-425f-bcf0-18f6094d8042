package service

import (
	"context"
	"errors"
	"fmt"
	"sort"

	"github.com/samber/lo"

	"github.com/MoeGolibrary/moego/backend/app/offering/logic/association"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/attribute"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/availability"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/obsetting"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/override"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	service2 "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func NewLogic() *Logic {
	return &Logic{
		query:               query.Use(db.GetDB()),
		repo:                service2.NewRepository(),
		obSettingLogic:      obsetting.New(),
		manager:             attribute.NewManager(),
		availabilityManager: availability.NewManager(),
		businessScopeLogic:  NewBusinessScope(),
		associationLogic:    association.NewLogic(),
		overrideLogic:       override.NewLogic(),
	}
}

type Logic struct {
	query               *query.Query
	repo                service2.Repository
	manager             *attribute.Manager
	availabilityManager *availability.Manager
	obSettingLogic      *obsetting.Logic
	businessScopeLogic  *BusinessScope
	associationLogic    *association.Logic
	overrideLogic       *override.Logic
}

func (l *Logic) checkCreateDef(ctx context.Context, createDef *offeringpb.ServiceCreateDef) error {
	if createDef == nil {
		return errors.New("createDef is nil")
	}

	_, total, err := l.repo.List(ctx, &service2.ListServiceFilter{
		OrganizationType: createDef.GetOrganizationType(),
		OrganizationID:   createDef.GetOrganizationId(),
		Keyword:          lo.ToPtr(createDef.GetName()),
	}, nil)
	if err != nil {
		return err
	}
	if total > 0 {
		return errors.New("service name already exists")
	}

	return nil
}

// CreateService creates a new service aggregate.
func (l *Logic) CreateService(ctx context.Context, createDef *offeringpb.ServiceCreateDef) (int64, error) {
	if err := l.checkCreateDef(ctx, createDef); err != nil {
		return 0, err
	}

	// TODO:
	// l.query.Transaction(func(tx *query.Query) error {})
	// 1. 保存 Service
	m := CreateDefToModel(createDef)
	if err := l.repo.Create(ctx, m); err != nil {
		return 0, err
	}

	// 2. 更新 sort 为 ID
	serviceID := m.ID
	m.Sort = serviceID
	if err := l.repo.Update(ctx, m); err != nil {
		return 0, err
	}

	// 3. 保存 Attributes
	if err := l.manager.Save(ctx, serviceID, createDef.GetAttributes()); err != nil {
		return 0, err
	}

	// 4. 保存 AvailableBusiness
	if err := l.businessScopeLogic.UpdateBusinessScope(ctx, serviceID, createDef.GetAvailableBusiness()); err != nil {
		return 0, err
	}

	// 5. 保存 AdditionalService
	if err := l.saveAdditionalService(ctx, serviceID, createDef.GetAdditionalService()); err != nil {
		return 0, err
	}

	// 6. 保存 Pet availability
	petAvailability := &availability.PetAvailability{
		ServiceID:    serviceID,
		PetTypeBreed: createDef.GetAvailableTypeBreed(),
		PetSize:      createDef.GetAvailablePetSize(),
		CoatType:     createDef.GetAvailableCoatType(),
		PetCode:      createDef.GetAvailablePetCode(),
		PetWeight:    createDef.GetAvailablePetWeight(),
	}
	if err := l.availabilityManager.Save(ctx, l.query, petAvailability); err != nil {
		return 0, err
	}

	// 7. 保存 Business and staff override
	if err := l.overrideLogic.Save(ctx, l.query, serviceID, createDef.GetBusinessStaffOverrides()); err != nil {
		return 0, err
	}

	return m.ID, nil
}

// UpdateService updates a service aggregate.
func (l *Logic) UpdateService(ctx context.Context, updateDef *offeringpb.ServiceUpdateDef) error {
	// TODO:
	// l.query.Transaction(func(tx *query.Query) error {})
	// 1. 保存 Service
	m := UpdateDefToModel(updateDef)
	if err := l.repo.Update(ctx, m); err != nil {
		return err
	}
	serviceID := m.ID

	// 2. 保存新的 Attributes
	if err := l.manager.Save(ctx, serviceID, updateDef.GetAttributes()); err != nil {
		return err
	}

	// 3. 保存 AvailableBusiness
	if err := l.businessScopeLogic.UpdateBusinessScope(ctx, serviceID, updateDef.GetAvailableBusiness()); err != nil {
		return err
	}

	// 4. 保存 AdditionalService
	if err := l.saveAdditionalService(ctx, serviceID, updateDef.GetAdditionalService()); err != nil {
		return err
	}

	// 5. 保存 Pet availability
	petAvailability := &availability.PetAvailability{
		ServiceID:    serviceID,
		PetTypeBreed: updateDef.GetAvailableTypeBreed(),
		PetSize:      updateDef.GetAvailablePetSize(),
		CoatType:     updateDef.GetAvailableCoatType(),
		PetCode:      updateDef.GetAvailablePetCode(),
		PetWeight:    updateDef.GetAvailablePetWeight(),
	}
	if err := l.availabilityManager.Save(ctx, l.query, petAvailability); err != nil {
		return err
	}

	// 6. 保存 Business and staff override
	if err := l.overrideLogic.Save(ctx, l.query, serviceID, updateDef.GetBusinessStaffOverrides()); err != nil {
		return err
	}

	return nil
}

// GetService gets a service by ID.
func (l *Logic) GetService(ctx context.Context, id int64) (*offeringpb.Service, error) {
	// 1. 获取 Service
	m, err := l.repo.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 2. 获取 Attribute values
	attributes, err := l.manager.ListByServiceID(ctx, id)
	if err != nil {
		return nil, err
	}

	// 3. 获取 Business scope
	businessScope, err := l.businessScopeLogic.ListBusinessScopes(ctx, []int64{id})
	if err != nil {
		return nil, err
	}

	// 4. 获取 AdditionalService
	additionalService, err := l.GetAdditionalService(ctx, id)
	if err != nil {
		return nil, err
	}

	// 5. 获取 Pet availability
	petAvailability, err := l.availabilityManager.GetByServiceID(ctx, id)
	if err != nil {
		return nil, err
	}

	// 6. 获取 Business and staff override
	overrides, err := l.overrideLogic.GetByServiceID(ctx, id)
	if err != nil {
		return nil, err
	}

	pb := ModelToProto(m)
	pb.Attributes = attributes
	pb.AvailableBusiness = businessScope[id]
	pb.AdditionalService = additionalService
	pb.AvailableTypeBreed = petAvailability.PetTypeBreed
	pb.AvailablePetSize = petAvailability.PetSize
	pb.AvailableCoatType = petAvailability.CoatType
	pb.AvailablePetCode = petAvailability.PetCode
	pb.BusinessStaffOverrides = overrides

	return pb, nil
}

// DeleteService deletes a service.
func (l *Logic) DeleteService(ctx context.Context, id int64) error {
	// TODO:
	// l.query.Transaction(func(tx *query.Query) error {})
	// 1. 删除 Attributes
	if err := l.manager.DeleteByServiceID(ctx, id); err != nil {
		return err
	}

	// 2. 删除 Business scope
	if err := l.businessScopeLogic.DeleteBusinessScope(ctx, id); err != nil {
		return err
	}

	// 3. 删除 AdditionalService
	if err := l.DeleteAdditionalService(ctx, id); err != nil {
		return err
	}

	// 4. 删除 Pet availability
	if err := l.availabilityManager.DeleteByServiceID(ctx, l.query, id); err != nil {
		return err
	}

	// 5. 删除 Business and staff override
	if err := l.overrideLogic.DeleteByServiceID(ctx, l.query, id); err != nil {
		return err
	}

	// 6. 删除 Service
	return l.repo.Delete(ctx, id)
}

// ListServices lists services based on the given request parameters.
func (l *Logic) ListServices(ctx context.Context,
	req *offeringpb.ListServicesRequest) (*offeringpb.ListServicesResponse, error) {
	// 构造过滤条件
	filter := l.buildListServicesFilter(req)

	// 查询服务列表
	services, total, err := l.repo.List(ctx, filter, req.Pagination)
	if err != nil {
		return nil, err
	}

	// 批量加载并丰富关联数据
	serviceProtos, err := l.enrichServicesWithRelatedData(ctx, services)
	if err != nil {
		return nil, err
	}

	// 构造响应
	resp := &offeringpb.ListServicesResponse{
		Services: []*offeringpb.ServiceWithExtraInfo{},
		Total:    int32(total),
	}

	// 添加分页信息
	if req.Pagination != nil {
		resp.Pagination = req.Pagination
	}

	// 如果需要包含在线预约设置，批量查询所有服务的在线预约设置
	var obSettingsMap map[int64]*offeringpb.ServiceOBSetting
	if req.ExtraInfoOptions != nil &&
		req.ExtraInfoOptions.IncludeObSetting != nil &&
		*req.ExtraInfoOptions.IncludeObSetting {
		serviceIDs := lo.Map(serviceProtos, func(service *offeringpb.Service, _ int) int64 {
			return service.Id
		})

		obSettings, err := l.loadOnlineBookingSettings(ctx, serviceIDs)
		if err != nil {
			return nil, err
		}

		// 转换为 map 以便快速查找
		obSettingsMap = make(map[int64]*offeringpb.ServiceOBSetting)
		for _, setting := range obSettings {
			obSettingsMap[setting.ServiceId] = setting
		}
	}

	// 构建 ServiceWithExtraInfo 列表
	for _, service := range serviceProtos {
		serviceWithExtraInfo := &offeringpb.ServiceWithExtraInfo{
			Service: service,
		}

		// 如果包含在线预约设置，添加到响应中
		if obSettingsMap != nil {
			if obSetting, exists := obSettingsMap[service.Id]; exists {
				serviceWithExtraInfo.ObSetting = obSetting
			}
		}

		resp.Services = append(resp.Services, serviceWithExtraInfo)
	}

	return resp, nil
}

// buildListServicesFilter 构造服务列表查询的过滤条件
func (l *Logic) buildListServicesFilter(req *offeringpb.ListServicesRequest) *service2.ListServiceFilter {
	var filter = &service2.ListServiceFilter{
		OrganizationType: req.OrganizationType,
		OrganizationID:   req.OrganizationId,
		Types:            []offeringpb.Service_Type{offeringpb.Service_SERVICE},
	}
	if req.Filter != nil {
		filter.CareTypeIDs = req.Filter.CareTypeIds
		filter.CategoriesIDs = req.Filter.CategoryIds
		filter.Statuses = req.Filter.Statuses
	}

	return filter
}

// enrichServicesWithRelatedData 将模型转换为proto并批量加载丰富关联数据
func (l *Logic) enrichServicesWithRelatedData(
	ctx context.Context, services []*model.Service) ([]*offeringpb.Service, error) {
	if len(services) == 0 {
		return []*offeringpb.Service{}, nil
	}

	// 转换为 proto 对象
	serviceProtos := lo.Map(services, func(service *model.Service, _ int) *offeringpb.Service {
		return ModelToProto(service)
	})

	// 提取服务ID列表
	serviceIDs := lo.Map(serviceProtos, func(service *offeringpb.Service, _ int) int64 {
		return service.Id
	})

	// 1. 批量获取 Attributes
	attributesMap, err := l.manager.ListByServiceIDs(ctx, serviceIDs)
	if err != nil {
		return nil, fmt.Errorf("failed to load attributes: %w", err)
	}

	// 2. 批量获取 Business scopes
	businessScopes, err := l.businessScopeLogic.ListBusinessScopes(ctx, serviceIDs)
	if err != nil {
		return nil, fmt.Errorf("failed to load business scopes: %w", err)
	}

	// 3. 批量获取 AdditionalServices
	additionalServices := make(map[int64]*offeringpb.AdditionalService)
	for _, serviceID := range serviceIDs {
		additionalService, err := l.GetAdditionalService(ctx, serviceID)
		if err != nil {
			return nil, fmt.Errorf("failed to load additional service for service %d: %w", serviceID, err)
		}
		additionalServices[serviceID] = additionalService
	}

	// 4. 批量获取 Pet availabilities
	petAvailabilities := make(map[int64]*availability.PetAvailability)
	for _, serviceID := range serviceIDs {
		petAvailability, err := l.availabilityManager.GetByServiceID(ctx, serviceID)
		if err != nil {
			return nil, fmt.Errorf("failed to load pet availability for service %d: %w", serviceID, err)
		}
		petAvailabilities[serviceID] = petAvailability
	}

	// 5. 批量获取 Business and staff overrides
	businessStaffOverrides := make(map[int64][]*offeringpb.BusinessStaffOverride)
	for _, serviceID := range serviceIDs {
		overrides, err := l.overrideLogic.GetByServiceID(ctx, serviceID)
		if err != nil {
			return nil, fmt.Errorf("failed to load overrides for service %d: %w", serviceID, err)
		}
		businessStaffOverrides[serviceID] = overrides
	}

	// 6. 直接丰富服务对象
	for _, service := range serviceProtos {
		// 设置 Attributes
		if attributes, exists := attributesMap[service.Id]; exists {
			service.Attributes = attributes
		} else {
			service.Attributes = &offeringpb.ServiceAttributes{}
		}

		// 设置 AvailableBusiness
		if businessScope, exists := businessScopes[service.Id]; exists {
			service.AvailableBusiness = businessScope
		}

		// 设置 AdditionalService
		if additionalService, exists := additionalServices[service.Id]; exists {
			service.AdditionalService = additionalService
		}

		// 设置 Pet availability
		if petAvailability, exists := petAvailabilities[service.Id]; exists {
			service.AvailableTypeBreed = petAvailability.PetTypeBreed
			service.AvailablePetSize = petAvailability.PetSize
			service.AvailableCoatType = petAvailability.CoatType
			service.AvailablePetCode = petAvailability.PetCode
			service.AvailablePetWeight = petAvailability.PetWeight
		}

		// 设置 Business and staff overrides
		if overrides, exists := businessStaffOverrides[service.Id]; exists {
			service.BusinessStaffOverrides = overrides
		}
	}

	return serviceProtos, nil
}

// loadOnlineBookingSettings 批量加载在线预约设置
func (l *Logic) loadOnlineBookingSettings(ctx context.Context,
	serviceIDs []int64) ([]*offeringpb.ServiceOBSetting, error) {
	if len(serviceIDs) == 0 {
		return []*offeringpb.ServiceOBSetting{}, nil
	}

	// 从 logic 层获取在线预约设置
	obSettings, err := l.obSettingLogic.ListByServiceIDs(ctx, serviceIDs)
	if err != nil {
		return nil, err
	}

	// 转换为 proto 对象
	protoSettings := make([]*offeringpb.ServiceOBSetting, 0, len(obSettings))
	for _, setting := range obSettings {
		protoSetting := obsetting.EntityToProto(setting)
		protoSettings = append(protoSettings, protoSetting)
	}

	return protoSettings, nil
}

// ListAvailableServices 查询指定 business 的可用服务列表
func (l *Logic) ListAvailableServices(ctx context.Context,
	req *offeringpb.ListAvailableServicesRequest) (*offeringpb.ListAvailableServicesResponse, error) {
	// 1. 查询全部服务
	services, _, err := l.repo.List(ctx, RequestToFilter(req), nil)
	if err != nil {
		return nil, err
	}

	// 2. 应用 business scope 过滤
	if req.EnableBusinessScopeFilter {
		services, err = l.applyBusinessScopeFilter(ctx, services, req.BusinessId)
		if err != nil {
			return nil, err
		}
	}

	// TODO: 应用 pet 过滤

	// 3. 内存排序分页
	services = l.memorySortAndPaginate(services, req.Pagination)

	// 批量加载并丰富关联数据
	serviceProtos, err := l.enrichServicesWithRelatedData(ctx, services)
	if err != nil {
		return nil, err
	}

	// 构造响应
	resp := &offeringpb.ListAvailableServicesResponse{
		Services: serviceProtos,
		Total:    int32(len(serviceProtos)),
	}

	// 添加分页信息
	if req.Pagination != nil {
		resp.Pagination = req.Pagination
	}

	return resp, nil
}

// applyBusinessScopeFilter 应用 business scope 过滤
func (l *Logic) applyBusinessScopeFilter(
	ctx context.Context, services []*model.Service, businessID int64) ([]*model.Service, error) {
	if len(services) == 0 {
		return services, nil
	}

	// 1. 获取所有服务的 business scope
	serviceIDs := lo.Map(services, func(service *model.Service, _ int) int64 {
		return service.ID
	})
	scopeByServiceID, err := l.businessScopeLogic.ListBusinessScopes(ctx, serviceIDs)
	if err != nil {
		return nil, err
	}

	// 2. 过滤服务
	var filteredServices []*model.Service
	for _, service := range services {
		scope := scopeByServiceID[service.ID]
		if scope == nil || scope.IsAll {
			// 适用于所有 business
			filteredServices = append(filteredServices, service)
		} else {
			// 检查是否包含指定的 business_id
			for _, id := range scope.BusinessIds {
				if id == businessID {
					filteredServices = append(filteredServices, service)

					break
				}
			}
		}
	}

	return filteredServices, nil
}

// memorySortAndPaginate 内存排序分页
func (l *Logic) memorySortAndPaginate(
	services []*model.Service, pagination *offeringpb.PaginationRef) []*model.Service {
	if pagination == nil {
		return services
	}

	// 按照 sort 排序
	sort.Slice(services, func(i, j int) bool {
		return services[i].Sort < services[j].Sort
	})

	// 分页
	start := int(pagination.Offset)
	end := int(pagination.Offset + pagination.Limit)

	// 边界检查
	if start >= len(services) {
		return []*model.Service{}
	}
	if end > len(services) {
		end = len(services)
	}

	return services[start:end]
}

// BatchUpdateServices 批量更新服务信息
func (l *Logic) BatchUpdateServices(ctx context.Context,
	req *offeringpb.BatchUpdateServicesRequest) (*offeringpb.BatchUpdateServicesResponse, error) {
	// 1. 获取需要更新的服务列表
	serviceIDs := lo.Map(req.UpdateServices, func(s *offeringpb.ServiceUpdateDef, _ int) int64 { return s.Id })
	filter := &service2.ListServiceFilter{
		OrganizationType: req.OrganizationType,
		OrganizationID:   req.OrganizationId,
		IDs:              serviceIDs,
	}
	services, _, err := l.repo.List(ctx, filter, nil)
	if err != nil {
		return nil, err
	}

	serviceMap := lo.KeyBy(services, func(s *model.Service) int64 { return s.ID })

	// 2. 批量更新服务
	for _, updateDef := range req.UpdateServices {
		// 3. 检查服务是否存在
		_, exists := serviceMap[updateDef.Id]
		if !exists {
			return nil, errors.New("service not found")
		}

		// 更新服务
		update := l.buildUpdateService(updateDef)
		if err := l.repo.Update(ctx, update); err != nil {
			return nil, err
		}

		// 更新关联数据
		if err := l.updateServiceRelatedData(ctx, updateDef); err != nil {
			return nil, err
		}
	}

	// 4. 构造响应
	return &offeringpb.BatchUpdateServicesResponse{}, nil
}

// buildUpdateService 构造更新服务
func (l *Logic) buildUpdateService(def *offeringpb.ServiceUpdateDef) *model.Service {
	update := &model.Service{
		ID: def.Id,
	}
	if def.Name != nil {
		update.Name = *def.Name
	}
	if def.CategoryId != nil {
		update.CategoryID = *def.CategoryId
	}
	if def.Description != nil {
		update.Description = def.Description
	}
	if def.ColorCode != nil {
		update.ColorCode = *def.ColorCode
	}
	if def.Sort != nil {
		update.Sort = *def.Sort
	}
	if len(def.Images) > 0 {
		update.Images = def.Images
	}
	if def.Status != nil {
		update.Status = *def.Status
	}

	return update
}

// updateServiceRelatedData 更新服务关联数据
func (l *Logic) updateServiceRelatedData(
	ctx context.Context, updateDef *offeringpb.ServiceUpdateDef) error {
	// 更新 AvailableBusiness
	if updateDef.AvailableBusiness != nil {
		if err := l.businessScopeLogic.UpdateBusinessScope(
			ctx, updateDef.GetId(), updateDef.GetAvailableBusiness()); err != nil {
			return fmt.Errorf("failed to update business scope: %w", err)
		}
	}

	// 更新 ServiceAttributes
	if updateDef.Attributes != nil {
		if err := l.manager.Save(ctx, updateDef.GetId(), updateDef.GetAttributes()); err != nil {
			return fmt.Errorf("failed to update service attributes: %w", err)
		}
	}

	// 更新 Business and staff override
	if updateDef.BusinessStaffOverrides != nil {
		if err := l.overrideLogic.Save(
			ctx, l.query, updateDef.GetId(), updateDef.GetBusinessStaffOverrides()); err != nil {
			return fmt.Errorf("failed to save business staff override: %w", err)
		}
	}

	return nil
}

// UpdateOBService updates the online booking setting for a service
func (l *Logic) UpdateOBService(ctx context.Context, req *offeringpb.UpdateOBServiceRequest) error {
	return l.obSettingLogic.UpdateOBService(ctx, req)
}

// saveAdditionalService 保存服务的 additional service 配置
func (l *Logic) saveAdditionalService(
	ctx context.Context, serviceID int64, additionalService *offeringpb.AdditionalService) error {
	if additionalService == nil {
		return nil
	}

	// 将 AdditionalService 转换为 ServiceAssociation 数组
	associations := ToAssociationAggregate(serviceID, additionalService)

	// 保存到 service association
	return l.associationLogic.UpsertAssociations(ctx, associations)
}

// GetAdditionalService 获取服务的 additional service 配置
func (l *Logic) GetAdditionalService(ctx context.Context, serviceID int64) (*offeringpb.AdditionalService, error) {
	// 从 service association 获取数据
	associations, err := l.associationLogic.GetAggregateByServiceID(ctx, serviceID)
	if err != nil {
		return nil, err
	}

	// 将 ServiceAssociation 数组转换为 AdditionalService
	return ToAdditionalService(associations), nil
}

// DeleteAdditionalService 删除服务的 additional service 配置
func (l *Logic) DeleteAdditionalService(ctx context.Context, serviceID int64) error {
	return l.associationLogic.DeleteByServiceID(ctx, serviceID)
}

// BatchGetServices 批量获取服务
func (l *Logic) BatchGetServices(
	ctx context.Context, req *offeringpb.BatchGetServicesRequest) (*offeringpb.BatchGetServicesResponse, error) {
	// 1. 获取服务列表
	filter := &service2.ListServiceFilter{
		OrganizationType: req.GetOrganizationType(),
		OrganizationID:   req.GetOrganizationId(),
		IDs:              req.GetIds(),
	}
	services, _, err := l.repo.List(ctx, filter, nil)
	if err != nil {
		return nil, err
	}

	// 2. 批量加载并丰富关联数据
	serviceProtos, err := l.enrichServicesWithRelatedData(ctx, services)
	if err != nil {
		return nil, err
	}

	return &offeringpb.BatchGetServicesResponse{
		Services: serviceProtos,
	}, nil
}
