package service

import (
	"context"
	"errors"
	"fmt"
	"sort"

	"github.com/samber/lo"

	"github.com/MoeGolibrary/moego/backend/app/offering/logic/association"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/attribute"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/obsetting"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	service2 "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func NewLogic() *Logic {
	return &Logic{
		query:              query.Use(db.GetDB()),
		repo:               service2.NewRepository(),
		obSettingLogic:     obsetting.New(),
		manager:            attribute.NewManager(),
		businessScopeLogic: NewBusinessScope(),
		associationLogic:   association.NewLogic(),
	}
}

type Logic struct {
	query              *query.Query
	repo               service2.Repository
	manager            *attribute.Manager
	obSettingLogic     *obsetting.Logic
	businessScopeLogic *BusinessScope
	associationLogic   *association.Logic
}

// CreateService creates a new service aggregate.
func (l *Logic) CreateService(ctx context.Context, agg *Aggregate) (int64, error) {
	// 校验入参
	if err := agg.IsValid(); err != nil {
		return 0, err
	}

	// 1. 保存 Service
	m := EntityToModel(agg.Service)
	err := l.repo.Create(ctx, m)
	if err != nil {
		return 0, err
	}

	// 2. 更新 sort 为 ID
	m.Sort = m.ID
	err = l.repo.Update(ctx, m)
	if err != nil {
		return 0, err
	}

	// 3. 保存 Attributes
	err = l.manager.Save(ctx, m.ID, agg.Attributes)
	if err != nil {
		return 0, err
	}

	// 4. 保存 AvailableBusiness
	err = l.businessScopeLogic.UpdateBusinessScope(ctx, m.ID, agg.AvailableBusiness)
	if err != nil {
		return 0, err
	}

	// 5. 保存 AdditionalService
	err = l.saveAdditionalService(ctx, m.ID, agg.AdditionalService)
	if err != nil {
		return 0, err
	}

	return m.ID, nil
}

// UpdateService updates a service aggregate.
func (l *Logic) UpdateService(ctx context.Context, agg *Aggregate) error {
	if err := agg.IsValid(); err != nil {
		return err
	}

	// 1. 保存 Service
	m := EntityToModel(agg.Service)
	err := l.repo.Update(ctx, m)
	if err != nil {
		return err
	}

	// 2. 保存新的 Attributes
	err = l.manager.Save(ctx, agg.Service.ID, agg.Attributes)
	if err != nil {
		return err
	}

	// 3. 保存 AvailableBusiness
	err = l.businessScopeLogic.UpdateBusinessScope(ctx, agg.Service.ID, agg.AvailableBusiness)
	if err != nil {
		return err
	}

	// 4. 保存 AdditionalService
	err = l.saveAdditionalService(ctx, agg.Service.ID, agg.AdditionalService)
	if err != nil {
		return err
	}

	return nil
}

// GetService gets a service aggregate by ID.
func (l *Logic) GetService(ctx context.Context, id int64) (*Aggregate, error) {
	// 1. 获取 Service
	m, err := l.repo.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	entity := ModelToEntity(m)

	// 2. 获取 Attribute values
	attributes, err := l.manager.ListByServiceID(ctx, id)
	if err != nil {
		return nil, err
	}

	// 3. 获取 Business scope
	businessScope, err := l.businessScopeLogic.ListBusinessScopes(ctx, []int64{id})
	if err != nil {
		return nil, err
	}

	// 4. 获取 AdditionalService
	additionalService, err := l.GetAdditionalService(ctx, id)
	if err != nil {
		return nil, err
	}

	return &Aggregate{
		Service:           entity,
		Attributes:        attributes,
		AvailableBusiness: businessScope[id],
		AdditionalService: additionalService,
	}, nil
}

// DeleteService deletes a service.
func (l *Logic) DeleteService(ctx context.Context, id int64) error {
	// 1. 删除 Attributes
	err := l.manager.DeleteByServiceID(ctx, id)
	if err != nil {
		return err
	}

	// 2. 删除 Business scope
	err = l.businessScopeLogic.DeleteBusinessScope(ctx, id)
	if err != nil {
		return err
	}

	// 3. 删除 AdditionalService
	err = l.DeleteAdditionalService(ctx, id)
	if err != nil {
		return err
	}

	// 4. 删除 Service
	return l.repo.Delete(ctx, id)
}

// ListServices lists services based on the given request parameters.
func (l *Logic) ListServices(ctx context.Context,
	req *offeringpb.ListServicesRequest) (*offeringpb.ListServicesResponse, error) {
	// 构造过滤条件
	filter := l.buildListServicesFilter(req)

	// 获取分页参数
	pagination := l.getPaginationParams(req)

	// 查询服务列表
	services, total, err := l.repo.List(ctx, filter, pagination)
	if err != nil {
		return nil, err
	}

	// 批量获取所有服务的属性
	attributesMap, err := l.loadServicesWithAttributes(ctx, services)
	if err != nil {
		return nil, err
	}

	// 构造响应
	return l.buildListServicesResponse(ctx, services, attributesMap, total, req)
}

// buildListServicesFilter 构造服务列表查询的过滤条件
func (l *Logic) buildListServicesFilter(req *offeringpb.ListServicesRequest) *service2.ListServiceFilter {
	var filter = &service2.ListServiceFilter{
		OrganizationType: req.OrganizationType,
		OrganizationID:   req.OrganizationId,
		Types:            []offeringpb.Service_Type{offeringpb.Service_SERVICE},
	}
	if req.Filter != nil {
		filter.CareTypeIDs = req.Filter.CareTypeIds
		filter.CategoriesIDs = req.Filter.CategoryIds
		filter.Statuses = req.Filter.Statuses
	}

	return filter
}

// getPaginationParams 获取分页参数
func (l *Logic) getPaginationParams(req *offeringpb.ListServicesRequest) *offeringpb.PaginationRef {
	if req.Pagination != nil {
		return req.Pagination
	}

	return &offeringpb.PaginationRef{
		Offset: 0,
		Limit:  200,
	}
}

// loadServicesWithAttributes 加载服务列表及其属性
func (l *Logic) loadServicesWithAttributes(
	ctx context.Context, services []*model.Service) (map[int64]*offeringpb.ServiceAttributes, error) {
	serviceIDs := lo.Map(services, func(service *model.Service, _ int) int64 {
		return service.ID
	})

	// 批量获取所有服务的属性
	attributesMap, err := l.manager.ListByServiceIDs(ctx, serviceIDs)
	if err != nil {
		return nil, err
	}

	return attributesMap, nil
}

// buildListServicesResponse 构造服务列表响应
func (l *Logic) buildListServicesResponse(
	ctx context.Context, services []*model.Service,
	attributesMap map[int64]*offeringpb.ServiceAttributes,
	total int64, req *offeringpb.ListServicesRequest) (*offeringpb.ListServicesResponse, error) {
	// 构造响应
	resp := &offeringpb.ListServicesResponse{
		Services: []*offeringpb.ServiceWithExtraInfo{},
		Total:    int32(total),
	}

	// 添加分页信息
	if req.Pagination != nil {
		resp.Pagination = req.Pagination
	}

	// 使用新的方法构建 ServiceWithExtraInfo 列表
	servicesWithExtraInfo, err := l.buildServiceWithExtraInfoList(
		ctx, services, attributesMap, req.ExtraInfoOptions)
	if err != nil {
		return nil, err
	}

	resp.Services = servicesWithExtraInfo

	return resp, nil
}

// buildServiceWithExtraInfoList 根据 ExtraInfoOptions 加载关联数据并构建 ServiceWithExtraInfo 列表
func (l *Logic) buildServiceWithExtraInfoList(
	ctx context.Context, services []*model.Service,
	attributesMap map[int64]*offeringpb.ServiceAttributes,
	extraInfoOptions *offeringpb.ExtraInfoOptions) ([]*offeringpb.ServiceWithExtraInfo, error) {

	serviceWithExtraInfoList := make([]*offeringpb.ServiceWithExtraInfo, 0, len(services))

	// 如果需要包含在线预约设置，批量查询所有服务的在线预约设置
	var obSettingsMap map[int64]*offeringpb.ServiceOBSetting
	if extraInfoOptions != nil &&
		extraInfoOptions.IncludeObSetting != nil &&
		*extraInfoOptions.IncludeObSetting {
		serviceIDs := lo.Map(services, func(service *model.Service, _ int) int64 {
			return service.ID
		})

		obSettings, err := l.loadOnlineBookingSettings(ctx, serviceIDs)
		if err != nil {
			return nil, err
		}

		// 转换为 map 以便快速查找
		obSettingsMap = make(map[int64]*offeringpb.ServiceOBSetting)
		for _, setting := range obSettings {
			obSettingsMap[setting.ServiceId] = setting
		}
	}

	// 构建 ServiceWithExtraInfo 列表
	for _, service := range services {
		serviceProto := ModelToProto(service)

		// 设置属性
		if attributes, exists := attributesMap[service.ID]; exists {
			serviceProto.Attributes = attributes
		} else {
			// 如果没有找到属性，设置为空的 ServiceAttributes
			serviceProto.Attributes = &offeringpb.ServiceAttributes{}
		}

		serviceWithExtraInfo := &offeringpb.ServiceWithExtraInfo{
			Service: serviceProto,
		}

		// 如果包含在线预约设置，添加到响应中
		if obSettingsMap != nil {
			if obSetting, exists := obSettingsMap[service.ID]; exists {
				serviceWithExtraInfo.ObSetting = obSetting
			}
		}

		serviceWithExtraInfoList = append(serviceWithExtraInfoList, serviceWithExtraInfo)
	}

	return serviceWithExtraInfoList, nil
}

// loadOnlineBookingSettings 批量加载在线预约设置
func (l *Logic) loadOnlineBookingSettings(ctx context.Context,
	serviceIDs []int64) ([]*offeringpb.ServiceOBSetting, error) {
	if len(serviceIDs) == 0 {
		return []*offeringpb.ServiceOBSetting{}, nil
	}

	// 从 logic 层获取在线预约设置
	obSettings, err := l.obSettingLogic.ListByServiceIDs(ctx, serviceIDs)
	if err != nil {
		return nil, err
	}

	// 转换为 proto 对象
	protoSettings := make([]*offeringpb.ServiceOBSetting, 0, len(obSettings))
	for _, setting := range obSettings {
		protoSetting := obsetting.EntityToProto(setting)
		protoSettings = append(protoSettings, protoSetting)
	}

	return protoSettings, nil
}

// ListAvailableServices 查询指定 business 的可用服务列表
func (l *Logic) ListAvailableServices(ctx context.Context,
	req *offeringpb.ListAvailableServicesRequest) (*offeringpb.ListAvailableServicesResponse, error) {
	// 1. 查询全部服务
	services, _, err := l.repo.List(ctx, RequestToFilter(req), nil)
	if err != nil {
		return nil, err
	}

	// 2. 应用 business scope 过滤
	if req.EnableBusinessScopeFilter {
		services, err = l.applyBusinessScopeFilter(ctx, services, req.BusinessId)
		if err != nil {
			return nil, err
		}
	}

	// TODO: 应用 pet 过滤

	// 3. 内存排序分页
	services = l.memorySortAndPaginate(services, req.Pagination)

	// 批量获取所有服务的属性
	attributesMap, err := l.loadServicesWithAttributes(ctx, services)
	if err != nil {
		return nil, err
	}

	// 构造响应
	return l.buildAvailableServicesResponse(services, attributesMap, req)
}

// applyBusinessScopeFilter 应用 business scope 过滤
func (l *Logic) applyBusinessScopeFilter(
	ctx context.Context, services []*model.Service, businessID int64) ([]*model.Service, error) {
	if len(services) == 0 {
		return services, nil
	}

	// 1. 获取所有服务的 business scope
	serviceIDs := lo.Map(services, func(service *model.Service, _ int) int64 {
		return service.ID
	})
	scopeByServiceID, err := l.businessScopeLogic.ListBusinessScopes(ctx, serviceIDs)
	if err != nil {
		return nil, err
	}

	// 2. 过滤服务
	var filteredServices []*model.Service
	for _, service := range services {
		scope := scopeByServiceID[service.ID]
		if scope == nil || scope.IsAll {
			// 适用于所有 business
			filteredServices = append(filteredServices, service)
		} else {
			// 检查是否包含指定的 business_id
			for _, id := range scope.BusinessIds {
				if id == businessID {
					filteredServices = append(filteredServices, service)

					break
				}
			}
		}
	}

	return filteredServices, nil
}

// memorySortAndPaginate 内存排序分页
func (l *Logic) memorySortAndPaginate(
	services []*model.Service, pagination *offeringpb.PaginationRef) []*model.Service {
	if pagination == nil {
		return services
	}

	// 按照 sort 排序
	sort.Slice(services, func(i, j int) bool {
		return services[i].Sort < services[j].Sort
	})

	// 分页
	start := int(pagination.Offset)
	end := int(pagination.Offset + pagination.Limit)

	// 边界检查
	if start >= len(services) {
		return []*model.Service{}
	}
	if end > len(services) {
		end = len(services)
	}

	return services[start:end]
}

// buildAvailableServicesResponse 构造可用服务查询响应
func (l *Logic) buildAvailableServicesResponse(
	services []*model.Service,
	attributesMap map[int64]*offeringpb.ServiceAttributes,
	req *offeringpb.ListAvailableServicesRequest) (*offeringpb.ListAvailableServicesResponse, error) {

	// 构造响应
	resp := &offeringpb.ListAvailableServicesResponse{
		Services: []*offeringpb.Service{},
		Total:    int32(len(services)),
	}

	// 添加分页信息
	if req.Pagination != nil {
		resp.Pagination = req.Pagination
	}

	// 转换为 proto 对象
	serviceProtos := lo.Map(services, func(service *model.Service, _ int) *offeringpb.Service {
		pb := ModelToProto(service)

		if attributes, exists := attributesMap[service.ID]; exists {
			pb.Attributes = attributes
		} else {
			pb.Attributes = &offeringpb.ServiceAttributes{}
		}

		return pb
	})

	resp.Services = serviceProtos

	return resp, nil
}

// BatchUpdateServices 批量更新服务信息
func (l *Logic) BatchUpdateServices(ctx context.Context,
	req *offeringpb.BatchUpdateServicesRequest) (*offeringpb.BatchUpdateServicesResponse, error) {
	// 1. 获取需要更新的服务列表
	serviceIDs := lo.Map(req.UpdateServices, func(s *offeringpb.ServiceUpdateDef, _ int) int64 { return s.Id })
	filter := &service2.ListServiceFilter{
		OrganizationType: req.OrganizationType,
		OrganizationID:   req.OrganizationId,
		IDs:              serviceIDs,
	}
	services, _, err := l.repo.List(ctx, filter, nil)
	if err != nil {
		return nil, err
	}

	serviceMap := lo.KeyBy(services, func(s *model.Service) int64 { return s.ID })

	// 2. 批量更新服务
	for _, updateDef := range req.UpdateServices {
		// 3. 检查服务是否存在
		_, exists := serviceMap[updateDef.Id]
		if !exists {
			return nil, errors.New("service not found")
		}

		// 更新服务
		update := l.buildUpdateService(updateDef)
		if err := l.repo.Update(ctx, update); err != nil {
			return nil, err
		}

		// 更新关联数据
		if err := l.updateServiceRelatedData(ctx, updateDef); err != nil {
			return nil, err
		}
	}

	// 4. 构造响应
	return &offeringpb.BatchUpdateServicesResponse{}, nil
}

// buildUpdateService 构造更新服务
func (l *Logic) buildUpdateService(def *offeringpb.ServiceUpdateDef) *model.Service {
	update := &model.Service{
		ID: def.Id,
	}
	if def.Name != nil {
		update.Name = *def.Name
	}
	if def.CategoryId != nil {
		update.CategoryID = *def.CategoryId
	}
	if def.Description != nil {
		update.Description = def.Description
	}
	if def.ColorCode != nil {
		update.ColorCode = *def.ColorCode
	}
	if def.Sort != nil {
		update.Sort = *def.Sort
	}
	if len(def.Images) > 0 {
		update.Images = def.Images
	}
	if def.Status != nil {
		update.Status = *def.Status
	}

	return update
}

// updateServiceRelatedData 更新服务关联数据
func (l *Logic) updateServiceRelatedData(
	ctx context.Context, updateDef *offeringpb.ServiceUpdateDef) error {
	// 更新 AvailableBusiness
	if updateDef.AvailableBusiness != nil {
		err := l.businessScopeLogic.UpdateBusinessScope(ctx, updateDef.Id, updateDef.AvailableBusiness)
		if err != nil {
			return fmt.Errorf("failed to update business scope: %w", err)
		}
	}

	// 更新 ServiceAttributes
	if updateDef.Attributes != nil {
		err := l.manager.Save(ctx, updateDef.Id, updateDef.Attributes)
		if err != nil {
			return fmt.Errorf("failed to update service attributes: %w", err)
		}
	}

	return nil
}

// saveAdditionalService 保存服务的 additional service 配置
func (l *Logic) saveAdditionalService(
	ctx context.Context, serviceID int64, additionalService *offeringpb.AdditionalService) error {
	if additionalService == nil {
		return nil
	}

	// 将 AdditionalService 转换为 ServiceAssociation 数组
	associations := ToAssociationAggregate(serviceID, additionalService)

	// 保存到 service association
	return l.associationLogic.UpsertAssociations(ctx, associations)
}

// GetAdditionalService 获取服务的 additional service 配置
func (l *Logic) GetAdditionalService(ctx context.Context, serviceID int64) (*offeringpb.AdditionalService, error) {
	// 从 service association 获取数据
	associations, err := l.associationLogic.GetAggregateByServiceID(ctx, serviceID)
	if err != nil {
		return nil, err
	}

	// 将 ServiceAssociation 数组转换为 AdditionalService
	return ToAdditionalService(associations), nil
}

// DeleteAdditionalService 删除服务的 additional service 配置
func (l *Logic) DeleteAdditionalService(ctx context.Context, serviceID int64) error {
	return l.associationLogic.DeleteByServiceID(ctx, serviceID)
}
