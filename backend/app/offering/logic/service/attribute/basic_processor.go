package attribute

import (
	"context"
	"log"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/serviceattribute"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// BasicProcessor 存储到 service_attribute 表
type BasicProcessor struct {
	BaseProcessor
	repo serviceattribute.Repository
}

// NewBasicProcessor 创建基础属性处理器
func NewBasicProcessor() *BasicProcessor {
	p := &BasicProcessor{repo: serviceattribute.NewRepository()}
	p.Processor = p

	return p
}

// NewBasicProcessorWithRepository 创建带有自定义repository的基础属性处理器（用于测试）
func NewBasicProcessorWithRepository(repo serviceattribute.Repository) *BasicProcessor {
	p := &BasicProcessor{repo: repo}
	p.Processor = p

	return p
}

func (p *BasicProcessor) Save(ctx context.Context, serviceID int64, attributes *offeringpb.ServiceAttributes) error {
	models := p.ToModel(serviceID, attributes)
	if len(models) == 0 {
		// 没有属性需要保存，直接返回
		return nil
	}

	// 实现 upsert 逻辑：先查询现有记录，如果存在则更新，不存在则创建
	return p.upsertBasicAttributes(ctx, serviceID, models)
}

// upsertBasicAttributes 实现 upsert 逻辑：先尝试更新，如果不存在则创建
func (p *BasicProcessor) upsertBasicAttributes(
	ctx context.Context, serviceID int64, newModels []*model.ServiceAttribute) error {
	// 先查询现有记录
	existingAttrs, err := p.repo.ListByServiceID(ctx, serviceID)
	if err != nil {
		return err
	}

	// 按 AttributeKey 分组现有记录
	existingByKey := make(map[offeringpb.AttributeKey]*model.ServiceAttribute)
	for _, attr := range existingAttrs {
		if p.IsSupportedKey(attr.AttributeKey) {
			existingByKey[attr.AttributeKey] = attr
		}
	}

	// 处理每个新模型
	for _, newModel := range newModels {
		if existing, exists := existingByKey[newModel.AttributeKey]; exists {
			// 记录存在，更新现有记录
			newModel.ID = existing.ID
			if err := p.repo.Update(ctx, newModel); err != nil {
				return err
			}
		} else {
			// 记录不存在，创建新记录
			if err := p.repo.Create(ctx, newModel); err != nil {
				return err
			}
		}
	}

	return nil
}

// ListByServiceIDs 批量获取基础属性
func (p *BasicProcessor) ListByServiceIDs(
	ctx context.Context, serviceIDs []int64) (map[int64]*offeringpb.ServiceAttributes, error) {
	// 全量获取属性
	attributes, err := p.repo.ListByServiceIDs(ctx, serviceIDs)
	if err != nil {
		return nil, err
	}

	// 按 serviceID 分组 attribute
	attrsByServiceID := make(map[int64][]*model.ServiceAttribute)
	for _, attr := range attributes {
		if !p.IsSupportedKey(attr.AttributeKey) {
			log.Printf("unsupported attribute key for basic processor: %v", attr.AttributeKey)

			continue
		}
		attrsByServiceID[attr.ServiceID] = append(attrsByServiceID[attr.ServiceID], attr)
	}

	result := make(map[int64]*offeringpb.ServiceAttributes)
	for _, serviceID := range serviceIDs {
		attrs := attrsByServiceID[serviceID]
		if len(attrs) == 0 {
			// 没有配置，返回空的 ServiceAttributes
			result[serviceID] = &offeringpb.ServiceAttributes{}

			continue
		}

		result[serviceID] = p.FromModel(attrs)
	}

	return result, nil
}

func (p *BasicProcessor) Remove(
	ctx context.Context, serviceID int64, attributeKey offeringpb.AttributeKey) error {
	if !p.IsSupportedKey(attributeKey) {
		return status.Error(codes.InvalidArgument, "unsupported attribute key for basic processor")
	}

	return p.RemoveByServiceID(ctx, serviceID)
}

func (p *BasicProcessor) RemoveByServiceID(ctx context.Context, serviceID int64) error {
	_, err := p.repo.DeleteByServiceID(ctx, serviceID, p.SupportedKeys())

	return err
}

// SupportedKeys 获取基础处理器支持的属性类型
func (p *BasicProcessor) SupportedKeys() []offeringpb.AttributeKey {
	return []offeringpb.AttributeKey{
		offeringpb.AttributeKey_DURATION,
		offeringpb.AttributeKey_MAX_DURATION,
		offeringpb.AttributeKey_IS_REQUIRED_STAFF,
	}
}

// IsSupportedKey 检查属性键是否被支持
func (p *BasicProcessor) IsSupportedKey(key offeringpb.AttributeKey) bool {
	for _, supportedKey := range p.SupportedKeys() {
		if supportedKey == key {
			return true
		}
	}

	return false
}

// Register 向管理器注册自己
func (p *BasicProcessor) Register(manager *Manager) {
	manager.RegisterProcessor(p)
}
