package attribute

import (
	"context"

	"github.com/samber/lo"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/serviceattribute"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// StaffProcessor 处理 staff 相关的属性配置
type StaffProcessor struct {
	BaseProcessor
	repo serviceattribute.StaffScopeRepository
}

// NewStaffProcessor 创建 staff 处理器
func NewStaffProcessor() *StaffProcessor {
	p := &StaffProcessor{repo: serviceattribute.NewStaffScopeRepository()}
	p.Processor = p

	return p
}

// NewStaffProcessorWithRepositories 创建带有自定义repository的staff处理器（用于测试）
func NewStaffProcessorWithRepositories(repo serviceattribute.StaffScopeRepository) *StaffProcessor {
	p := &StaffProcessor{repo: repo}
	p.Processor = p

	return p
}

func (p *StaffProcessor) Save(ctx context.Context, serviceID int64, attributes *offeringpb.ServiceAttributes) error {
	if attributes == nil || attributes.AvailableStaff == nil {
		return nil
	}

	scope := p.ToModel(serviceID, attributes)

	return p.upsertStaffScope(ctx, scope)
}

// upsertStaffScope 实现 upsert 逻辑：先尝试更新，如果不存在则创建
func (p *StaffProcessor) upsertStaffScope(ctx context.Context, scope *model.ServiceStaffScope) error {
	// 先尝试查询现有记录
	existing, err := p.repo.GetByServiceID(ctx, scope.ServiceID)
	if err != nil {
		return err
	}
	// 记录不存在，创建新记录
	if existing == nil {
		return p.repo.Create(ctx, scope)
	}

	// 记录存在，更新现有记录
	scope.ID = existing.ID

	return p.repo.Update(ctx, scope)
}

// ListByServiceIDs 批量获取多个服务的 staff 属性配置
func (p *StaffProcessor) ListByServiceIDs(
	ctx context.Context, serviceIDs []int64) (map[int64]*offeringpb.ServiceAttributes, error) {
	scopes, err := p.repo.ListByServiceIDs(ctx, serviceIDs)
	if err != nil {
		return nil, err
	}

	scopesByServiceID := lo.KeyBy(scopes, func(scope *model.ServiceStaffScope) int64 {
		return scope.ServiceID
	})

	result := make(map[int64]*offeringpb.ServiceAttributes)
	for _, serviceID := range serviceIDs {
		scope := scopesByServiceID[serviceID]
		result[serviceID] = p.FromModel(scope)
	}

	return result, nil
}

// ListByServiceID 根据 service ID 获取所有支持的属性
func (p *StaffProcessor) ListByServiceID(ctx context.Context, serviceID int64) (*offeringpb.ServiceAttributes, error) {
	attrsByServiceID, err := p.ListByServiceIDs(ctx, []int64{serviceID})
	if err != nil {
		return nil, err
	}

	return attrsByServiceID[serviceID], nil
}

// Register 向管理器注册自己
func (p *StaffProcessor) Register(manager *Manager) {
	manager.RegisterProcessor(p)
}

func (p *StaffProcessor) Remove(ctx context.Context, serviceID int64, attributeKey offeringpb.AttributeKey) error {
	if !p.IsSupportedKey(attributeKey) {
		return status.Error(codes.InvalidArgument, "unsupported attribute key for staff processor")
	}

	return p.RemoveByServiceID(ctx, serviceID)
}

func (p *StaffProcessor) RemoveByServiceID(ctx context.Context, serviceID int64) error {
	_, err := p.repo.DeleteByServiceID(ctx, serviceID)

	return err
}

func (p *StaffProcessor) SupportedKeys() []offeringpb.AttributeKey {
	return []offeringpb.AttributeKey{offeringpb.AttributeKey_AVAILABLE_STAFF}
}

// IsSupportedKey 检查属性键是否被支持
func (p *StaffProcessor) IsSupportedKey(key offeringpb.AttributeKey) bool {
	for _, supportedKey := range p.SupportedKeys() {
		if supportedKey == key {
			return true
		}
	}

	return false
}
