package attribute

import (
	"strconv"

	model2 "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	utils "github.com/MoeGolibrary/moego/backend/app/offering/utils"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// EntityToModel converts a domain entity ServiceAttribute to a database model.
func EntityToModel(e *ServiceAttribute) *model2.ServiceAttribute {
	if e == nil {
		return nil
	}

	m := &model2.ServiceAttribute{
		ID:             e.ID,
		ServiceID:      e.ServiceID,
		FieldName:      e.Field<PERSON>ame,
		AttributeKey:   e.AttributeKey,
		AttributeValue: utils.PBValueToString(e.AttributeValue),
	}

	return m
}

// ToModel converter proto to model
func (p *BasicProcessor) ToModel(
	serviceID int64, attributes *offeringpb.ServiceAttributes) []*model2.ServiceAttribute {
	if attributes == nil {
		return nil
	}

	var attrs []*model2.ServiceAttribute

	if attributes.Duration != nil {
		attr := &model2.ServiceAttribute{
			ServiceID:      serviceID,
			FieldName:      attributeKeyToFieldName[offeringpb.AttributeKey_DURATION],
			AttributeKey:   offeringpb.AttributeKey_DURATION,
			AttributeValue: strconv.Itoa(int(*attributes.Duration)),
		}
		attrs = append(attrs, attr)
	}

	if attributes.MaxDuration != nil {
		attr := &model2.ServiceAttribute{
			ServiceID:      serviceID,
			FieldName:      attributeKeyToFieldName[offeringpb.AttributeKey_MAX_DURATION],
			AttributeKey:   offeringpb.AttributeKey_MAX_DURATION,
			AttributeValue: strconv.Itoa(int(*attributes.MaxDuration)),
		}
		attrs = append(attrs, attr)
	}

	if attributes.IsRequiredStaff != nil {
		attr := &model2.ServiceAttribute{
			ServiceID:      serviceID,
			FieldName:      attributeKeyToFieldName[offeringpb.AttributeKey_IS_REQUIRED_STAFF],
			AttributeKey:   offeringpb.AttributeKey_IS_REQUIRED_STAFF,
			AttributeValue: strconv.FormatBool(*attributes.IsRequiredStaff),
		}
		attrs = append(attrs, attr)
	}

	return attrs
}

func (p *BasicProcessor) FromModel(
	attributes []*model2.ServiceAttribute) *offeringpb.ServiceAttributes {
	if len(attributes) == 0 {
		return &offeringpb.ServiceAttributes{}
	}

	result := &offeringpb.ServiceAttributes{}

	for _, attr := range attributes {
		switch attr.AttributeKey {
		case offeringpb.AttributeKey_DURATION:
			if attr.AttributeValue != "" {
				if duration, err := strconv.Atoi(attr.AttributeValue); err == nil {
					duration32 := int32(duration)
					result.Duration = &duration32
				}
			}
		case offeringpb.AttributeKey_MAX_DURATION:
			if attr.AttributeValue != "" {
				if maxDuration, err := strconv.Atoi(attr.AttributeValue); err == nil {
					maxDuration32 := int32(maxDuration)
					result.MaxDuration = &maxDuration32
				}
			}
		case offeringpb.AttributeKey_IS_REQUIRED_STAFF:
			if attr.AttributeValue != "" {
				if isRequiredStaff, err := strconv.ParseBool(attr.AttributeValue); err == nil {
					result.IsRequiredStaff = &isRequiredStaff
				}
			}
		}
	}

	return result
}

// StaffProcessor converter methods
func (p *StaffProcessor) ToModel(
	serviceID int64, attributes *offeringpb.ServiceAttributes) *model2.ServiceStaffScope {
	if attributes == nil || attributes.AvailableStaff == nil {
		return nil
	}

	return &model2.ServiceStaffScope{
		ServiceID:  serviceID,
		IsAllStaff: attributes.AvailableStaff.IsAll,
		AvailableStaffIds: func() []int64 {
			if attributes.AvailableStaff.IsAll {
				return []int64{}
			}

			return attributes.AvailableStaff.StaffIds
		}(),
	}
}

func (p *StaffProcessor) FromModel(
	scope *model2.ServiceStaffScope) *offeringpb.ServiceAttributes {
	if scope == nil {
		return &offeringpb.ServiceAttributes{
			AvailableStaff: &offeringpb.AvailableStaff{
				IsAll:    true,
				StaffIds: []int64{},
			},
		}
	}

	return &offeringpb.ServiceAttributes{
		AvailableStaff: &offeringpb.AvailableStaff{
			IsAll:    scope.IsAllStaff,
			StaffIds: scope.AvailableStaffIds,
		},
	}
}

// AutoRolloverProcessor converter methods
func (p *AutoRolloverProcessor) ToModel(
	serviceID int64, attributes *offeringpb.ServiceAttributes) *model2.ServiceAutoRollover {
	if attributes == nil || attributes.AutoRollover == nil {
		return nil
	}

	return &model2.ServiceAutoRollover{
		ServiceID:       serviceID,
		Enabled:         attributes.AutoRollover.Enabled,
		TargetServiceID: attributes.AutoRollover.TargetServiceId,
		AfterMinute:     attributes.AutoRollover.AfterMinute,
	}
}

func (p *AutoRolloverProcessor) FromModel(
	rollover *model2.ServiceAutoRollover) *offeringpb.ServiceAttributes {
	if rollover == nil {
		return &offeringpb.ServiceAttributes{}
	}

	return &offeringpb.ServiceAttributes{
		AutoRollover: &offeringpb.AutoRollover{
			Enabled:         rollover.Enabled,
			TargetServiceId: rollover.TargetServiceID,
			AfterMinute:     rollover.AfterMinute,
		},
	}
}

// LodgingProcessor converter methods
func (p *LodgingProcessor) ToModel(
	serviceID int64, attributes *offeringpb.ServiceAttributes) *model2.ServiceLodgingScope {
	if attributes == nil || attributes.AvailableLodgingType == nil {
		return nil
	}

	return &model2.ServiceLodgingScope{
		ServiceID:    serviceID,
		IsAllLodging: attributes.AvailableLodgingType.IsAll,
		AvailableLodgingTypeIds: func() []int64 {
			if attributes.AvailableLodgingType.IsAll {
				return []int64{}
			}

			return attributes.AvailableLodgingType.LodgingTypeIds
		}(),
	}
}

func (p *LodgingProcessor) FromModel(
	scope *model2.ServiceLodgingScope) *offeringpb.ServiceAttributes {
	if scope == nil {
		return &offeringpb.ServiceAttributes{
			AvailableLodgingType: &offeringpb.AvailableLodgingType{
				IsAll:          true,
				LodgingTypeIds: []int64{},
			},
		}
	}

	return &offeringpb.ServiceAttributes{
		AvailableLodgingType: &offeringpb.AvailableLodgingType{
			IsAll:          scope.IsAllLodging,
			LodgingTypeIds: scope.AvailableLodgingTypeIds,
		},
	}
}
