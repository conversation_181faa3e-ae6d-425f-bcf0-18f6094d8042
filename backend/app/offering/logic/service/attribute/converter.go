package attribute

import (
	"encoding/json"
	"fmt"
	"strconv"

	model2 "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	utils "github.com/MoeGolibrary/moego/backend/app/offering/utils"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// EntityToModel converts a domain entity ServiceAttribute to a database model.
func EntityToModel(e *ServiceAttribute) *model2.ServiceAttribute {
	if e == nil {
		return nil
	}

	m := &model2.ServiceAttribute{
		ID:             e.ID,
		ServiceID:      e.ServiceID,
		FieldName:      e.FieldName,
		AttributeKey:   e.Attribute<PERSON>ey,
		AttributeValue: utils.PBValueToString(e.AttributeValue),
	}

	return m
}

// ToModel converter proto to model
func (p *BasicProcessor) ToModel(
	serviceID int64, attributes *offeringpb.ServiceAttributes) ([]*model2.ServiceAttribute, error) {
	if attributes == nil {
		return nil, nil
	}

	var attrs []*model2.ServiceAttribute

	// 处理基础属性
	if err := p.addBasicAttributes(serviceID, attributes, &attrs); err != nil {
		return nil, err
	}

	// 处理 JSON 属性
	if err := p.addJSONAttributes(serviceID, attributes, &attrs); err != nil {
		return nil, err
	}

	return attrs, nil
}

// addBasicAttributes 添加基础类型属性（字符串、数字、布尔值）
func (p *BasicProcessor) addBasicAttributes(
	serviceID int64, attributes *offeringpb.ServiceAttributes, attrs *[]*model2.ServiceAttribute) error {

	if attributes.Duration != nil {
		*attrs = append(*attrs, p.createBasicAttribute(
			serviceID, offeringpb.AttributeKey_DURATION,
			strconv.FormatInt(int64(attributes.GetDuration()), 10)))
	}

	if attributes.MaxDuration != nil {
		*attrs = append(*attrs, p.createBasicAttribute(
			serviceID, offeringpb.AttributeKey_MAX_DURATION,
			strconv.FormatInt(int64(attributes.GetMaxDuration()), 10)))
	}

	if attributes.IsRequiredStaff != nil {
		*attrs = append(*attrs, p.createBasicAttribute(
			serviceID, offeringpb.AttributeKey_IS_REQUIRED_STAFF,
			strconv.FormatBool(attributes.GetIsRequiredStaff())))
	}

	if attributes.AutoAssignStaff != nil {
		*attrs = append(*attrs, p.createBasicAttribute(
			serviceID, offeringpb.AttributeKey_STAFF_AUTO_ASSIGN,
			strconv.FormatBool(attributes.GetAutoAssignStaff())))
	}

	if attributes.ObAlias != nil {
		*attrs = append(*attrs, p.createBasicAttribute(
			serviceID, offeringpb.AttributeKey_ONLINE_BOOKING_ALIAS,
			attributes.GetObAlias()))
	}

	if attributes.PriceUnit != nil {
		*attrs = append(*attrs, p.createBasicAttribute(
			serviceID, offeringpb.AttributeKey_PRICE_UNIT,
			attributes.GetPriceUnit().String()))
	}

	return nil
}

// addJSONAttributes 添加需要 JSON 序列化的属性
func (p *BasicProcessor) addJSONAttributes(
	serviceID int64, attributes *offeringpb.ServiceAttributes, attrs *[]*model2.ServiceAttribute) error {

	if attributes.PrerequisiteRule != nil {
		jsonValue, err := json.Marshal(attributes.GetPrerequisiteRule())
		if err != nil {
			return fmt.Errorf("failed to marshal prerequisite rule: %w", err)
		}
		*attrs = append(*attrs, p.createBasicAttribute(
			serviceID, offeringpb.AttributeKey_PREREQUISITE_SERVICE, string(jsonValue)))
	}

	if attributes.ResultValidityPeriod != nil {
		jsonValue, err := json.Marshal(attributes.GetResultValidityPeriod())
		if err != nil {
			return fmt.Errorf("failed to marshal result validity period: %w", err)
		}
		*attrs = append(*attrs, p.createBasicAttribute(
			serviceID, offeringpb.AttributeKey_RESULT_RESETTABLE, string(jsonValue)))
	}

	if attributes.DefaultService != nil {
		jsonValue, err := json.Marshal(attributes.GetDefaultService())
		if err != nil {
			return fmt.Errorf("failed to marshal default service: %w", err)
		}
		*attrs = append(*attrs, p.createBasicAttribute(
			serviceID, offeringpb.AttributeKey_DEFAULT_SERVICE, string(jsonValue)))
	}

	if attributes.ConditionalDefaultService != nil {
		jsonValue, err := json.Marshal(attributes.GetConditionalDefaultService())
		if err != nil {
			return fmt.Errorf("failed to marshal conditional default service: %w", err)
		}
		*attrs = append(*attrs, p.createBasicAttribute(
			serviceID, offeringpb.AttributeKey_CONDITIONAL_DEFAULT_SERVICE, string(jsonValue)))
	}

	return nil
}

// createBasicAttribute 创建基础属性模型
func (p *BasicProcessor) createBasicAttribute(
	serviceID int64, key offeringpb.AttributeKey, value string) *model2.ServiceAttribute {
	return &model2.ServiceAttribute{
		ServiceID:      serviceID,
		FieldName:      attributeKeyToFieldName[key],
		AttributeKey:   key,
		AttributeValue: value,
	}
}

func (p *BasicProcessor) FromModel(
	attributes []*model2.ServiceAttribute) (*offeringpb.ServiceAttributes, error) {
	if len(attributes) == 0 {
		return &offeringpb.ServiceAttributes{}, nil
	}

	result := &offeringpb.ServiceAttributes{}

	for _, attr := range attributes {
		if attr.AttributeValue == "" {
			continue
		}

		switch attr.AttributeKey {
		case offeringpb.AttributeKey_DURATION:
			duration, err := strconv.ParseInt(attr.AttributeValue, 10, 32)
			if err != nil {
				return nil, fmt.Errorf("failed to parse duration: %w", err)
			}
			duration32 := int32(duration)
			result.Duration = &duration32
		case offeringpb.AttributeKey_MAX_DURATION:
			maxDuration, err := strconv.ParseInt(attr.AttributeValue, 10, 32)
			if err != nil {
				return nil, fmt.Errorf("failed to parse max duration: %s", attr.AttributeValue)
			}
			maxDuration32 := int32(maxDuration)
			result.MaxDuration = &maxDuration32
		case offeringpb.AttributeKey_PREREQUISITE_SERVICE:
			var prerequisiteRule offeringpb.PrerequisiteRule
			if err := json.Unmarshal([]byte(attr.AttributeValue), &prerequisiteRule); err != nil {
				return nil, fmt.Errorf("failed to unmarshal prerequisite rule: %w", err)
			}
			result.PrerequisiteRule = &prerequisiteRule
		case offeringpb.AttributeKey_IS_REQUIRED_STAFF:
			isRequiredStaff, err := strconv.ParseBool(attr.AttributeValue)
			if err != nil {
				return nil, fmt.Errorf("failed to parse is required staff: %w", err)
			}
			result.IsRequiredStaff = &isRequiredStaff
		case offeringpb.AttributeKey_RESULT_RESETTABLE:
			var resultValidityPeriod offeringpb.ResultValidityPeriod
			if err := json.Unmarshal([]byte(attr.AttributeValue), &resultValidityPeriod); err != nil {
				return nil, fmt.Errorf("failed to unmarshal result validity period: %w", err)
			}
			result.ResultValidityPeriod = &resultValidityPeriod
		case offeringpb.AttributeKey_STAFF_AUTO_ASSIGN:
			autoAssignStaff, err := strconv.ParseBool(attr.AttributeValue)
			if err != nil {
				return nil, fmt.Errorf("failed to parse auto assign staff: %w", err)
			}
			result.AutoAssignStaff = &autoAssignStaff
		case offeringpb.AttributeKey_ONLINE_BOOKING_ALIAS:
			result.ObAlias = &attr.AttributeValue
		case offeringpb.AttributeKey_PRICE_UNIT:
			priceUnit, ok := offeringpb.PriceUnit_value[attr.AttributeValue]
			if !ok {
				return nil, fmt.Errorf("failed to parse price unit: %s", attr.AttributeValue)
			}
			priceUnitEnum := offeringpb.PriceUnit(priceUnit)
			result.PriceUnit = &priceUnitEnum
		case offeringpb.AttributeKey_DEFAULT_SERVICE:
			var defaultService offeringpb.DefaultService
			if err := json.Unmarshal([]byte(attr.AttributeValue), &defaultService); err != nil {
				return nil, fmt.Errorf("failed to unmarshal default service: %w", err)
			}
			result.DefaultService = &defaultService
		case offeringpb.AttributeKey_CONDITIONAL_DEFAULT_SERVICE:
			var conditionalDefaultService offeringpb.ConditionalDefaultService
			if err := json.Unmarshal([]byte(attr.AttributeValue), &conditionalDefaultService); err != nil {
				return nil, fmt.Errorf("failed to unmarshal conditional default service: %w", err)
			}
			result.ConditionalDefaultService = &conditionalDefaultService
		}
	}

	return result, nil
}

// StaffProcessor converter methods
func (p *StaffProcessor) ToModel(
	serviceID int64, attributes *offeringpb.ServiceAttributes) *model2.ServiceStaffScope {
	if attributes == nil || attributes.AvailableStaff == nil {
		return nil
	}

	return &model2.ServiceStaffScope{
		ServiceID:  serviceID,
		IsAllStaff: attributes.AvailableStaff.IsAll,
		AvailableStaffIds: func() []int64 {
			if attributes.AvailableStaff.IsAll {
				return []int64{}
			}

			return attributes.AvailableStaff.StaffIds
		}(),
	}
}

func (p *StaffProcessor) FromModel(
	scope *model2.ServiceStaffScope) *offeringpb.ServiceAttributes {
	if scope == nil {
		return &offeringpb.ServiceAttributes{
			AvailableStaff: &offeringpb.AvailableStaff{
				IsAll:    true,
				StaffIds: []int64{},
			},
		}
	}

	return &offeringpb.ServiceAttributes{
		AvailableStaff: &offeringpb.AvailableStaff{
			IsAll:    scope.IsAllStaff,
			StaffIds: scope.AvailableStaffIds,
		},
	}
}

// AutoRolloverProcessor converter methods
func (p *AutoRolloverProcessor) ToModel(
	serviceID int64, attributes *offeringpb.ServiceAttributes) *model2.ServiceAutoRollover {
	if attributes == nil || attributes.AutoRollover == nil {
		return nil
	}

	return &model2.ServiceAutoRollover{
		ServiceID:       serviceID,
		Enabled:         attributes.AutoRollover.Enabled,
		TargetServiceID: attributes.AutoRollover.TargetServiceId,
		AfterMinute:     attributes.AutoRollover.AfterMinute,
	}
}

func (p *AutoRolloverProcessor) FromModel(
	rollover *model2.ServiceAutoRollover) *offeringpb.ServiceAttributes {
	if rollover == nil {
		return &offeringpb.ServiceAttributes{}
	}

	return &offeringpb.ServiceAttributes{
		AutoRollover: &offeringpb.AutoRollover{
			Enabled:         rollover.Enabled,
			TargetServiceId: rollover.TargetServiceID,
			AfterMinute:     rollover.AfterMinute,
		},
	}
}

// LodgingProcessor converter methods
func (p *LodgingProcessor) ToModel(
	serviceID int64, attributes *offeringpb.ServiceAttributes) *model2.ServiceLodgingScope {
	if attributes == nil || attributes.AvailableLodgingType == nil {
		return nil
	}

	return &model2.ServiceLodgingScope{
		ServiceID:    serviceID,
		IsAllLodging: attributes.AvailableLodgingType.IsAll,
		AvailableLodgingTypeIds: func() []int64 {
			if attributes.AvailableLodgingType.IsAll {
				return []int64{}
			}

			return attributes.AvailableLodgingType.LodgingTypeIds
		}(),
	}
}

func (p *LodgingProcessor) FromModel(
	scope *model2.ServiceLodgingScope) *offeringpb.ServiceAttributes {
	if scope == nil {
		return &offeringpb.ServiceAttributes{
			AvailableLodgingType: &offeringpb.AvailableLodgingType{
				IsAll:          true,
				LodgingTypeIds: []int64{},
			},
		}
	}

	return &offeringpb.ServiceAttributes{
		AvailableLodgingType: &offeringpb.AvailableLodgingType{
			IsAll:          scope.IsAllLodging,
			LodgingTypeIds: scope.AvailableLodgingTypeIds,
		},
	}
}
