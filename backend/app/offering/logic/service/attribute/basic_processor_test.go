package attribute

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	attrmock "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/serviceattribute/mocks"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

var (
	duration32    = int32(60)
	maxDuration32 = int32(120)
)

func TestBasicProcessor_Save(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)

	// 测试保存 Duration 属性
	attr := &offeringpb.ServiceAttributes{
		Duration: &duration32,
	}

	// 模拟查询现有记录，返回空列表（表示没有现有记录）
	mockRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model.ServiceAttribute{}, nil)
	// 模拟创建新记录
	mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	err := processor.Save(context.Background(), 1, attr)
	assert.NoError(t, err)
}

func TestBasicProcessor_Save_MaxDuration(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)

	// 测试保存 MaxDuration 属性
	attr := &offeringpb.ServiceAttributes{
		MaxDuration: &maxDuration32,
	}

	// 模拟查询现有记录，返回空列表（表示没有现有记录）
	mockRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model.ServiceAttribute{}, nil)
	// 模拟创建新记录
	mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	err := processor.Save(context.Background(), 1, attr)
	assert.NoError(t, err)
}

func TestBasicProcessor_Save_BothAttributes(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)

	// 测试同时保存 Duration 和 MaxDuration 属性
	attr := &offeringpb.ServiceAttributes{
		Duration:    &duration32,
		MaxDuration: &maxDuration32,
	}

	// 模拟查询现有记录，返回空列表（表示没有现有记录）
	mockRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model.ServiceAttribute{}, nil)
	// 模拟创建两个新记录（Duration 和 MaxDuration）
	mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).Times(2)

	err := processor.Save(context.Background(), 1, attr)
	assert.NoError(t, err)
}

func TestBasicProcessor_Save_EmptyAttributes(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)

	// 测试保存空属性 - 空的 ServiceAttributes 不应该调用 repository
	attr := &offeringpb.ServiceAttributes{}

	err := processor.Save(context.Background(), 1, attr)
	assert.NoError(t, err)
}

func TestBasicProcessor_Save_NilAttributes(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)

	// 测试保存 nil 属性
	err := processor.Save(context.Background(), 1, nil)
	assert.NoError(t, err)
}

func TestBasicProcessor_Save_RepositoryError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)

	attr := &offeringpb.ServiceAttributes{
		Duration: &duration32,
	}

	// 模拟查询现有记录，返回空列表（表示没有现有记录）
	mockRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model.ServiceAttribute{}, nil)
	// 模拟创建新记录时发生错误
	mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(errors.New("database error"))

	err := processor.Save(context.Background(), 1, attr)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "database error")
}

// TestBasicProcessor_Save_UpdateExisting 测试更新现有记录的情况
func TestBasicProcessor_Save_UpdateExisting(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)

	// 测试更新 Duration 属性
	attr := &offeringpb.ServiceAttributes{
		Duration: &duration32,
	}

	// 模拟查询现有记录，返回现有记录
	existingAttr := &model.ServiceAttribute{
		ID:             1,
		ServiceID:      1,
		AttributeKey:   offeringpb.AttributeKey_DURATION,
		AttributeValue: "30", // 旧值
	}
	mockRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model.ServiceAttribute{existingAttr}, nil)
	// 模拟更新现有记录
	mockRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	err := processor.Save(context.Background(), 1, attr)
	assert.NoError(t, err)
}

// TestBasicProcessor_Save_MixedCreateAndUpdate 测试混合创建和更新的情况
func TestBasicProcessor_Save_MixedCreateAndUpdate(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)

	// 测试同时保存 Duration 和 MaxDuration 属性
	attr := &offeringpb.ServiceAttributes{
		Duration:    &duration32,
		MaxDuration: &maxDuration32,
	}

	// 模拟查询现有记录，返回部分现有记录
	existingDurationAttr := &model.ServiceAttribute{
		ID:             1,
		ServiceID:      1,
		AttributeKey:   offeringpb.AttributeKey_DURATION,
		AttributeValue: "30", // 旧值
	}
	mockRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model.ServiceAttribute{existingDurationAttr}, nil)
	// 模拟更新现有的 Duration 记录
	mockRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
	// 模拟创建新的 MaxDuration 记录
	mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	err := processor.Save(context.Background(), 1, attr)
	assert.NoError(t, err)
}

// TestBasicProcessor_Save_ListByServiceIDError 测试查询现有记录时发生错误的情况
func TestBasicProcessor_Save_ListByServiceIDError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)

	attr := &offeringpb.ServiceAttributes{
		Duration: &duration32,
	}

	// 模拟查询现有记录时发生错误
	mockRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return(nil, errors.New("query error"))

	err := processor.Save(context.Background(), 1, attr)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "query error")
}

// TestBasicProcessor_Save_UpdateError 测试更新现有记录时发生错误的情况
func TestBasicProcessor_Save_UpdateError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)

	attr := &offeringpb.ServiceAttributes{
		Duration: &duration32,
	}

	// 模拟查询现有记录，返回现有记录
	existingAttr := &model.ServiceAttribute{
		ID:             1,
		ServiceID:      1,
		AttributeKey:   offeringpb.AttributeKey_DURATION,
		AttributeValue: "30", // 旧值
	}
	mockRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model.ServiceAttribute{existingAttr}, nil)
	// 模拟更新现有记录时发生错误
	mockRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(errors.New("update error"))

	err := processor.Save(context.Background(), 1, attr)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "update error")
}

func TestBasicProcessor_ListByServiceID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)

	// 模拟返回的属性列表
	mockAttributes := []*model.ServiceAttribute{
		{
			ID:             1,
			ServiceID:      1,
			FieldName:      "duration",
			AttributeKey:   offeringpb.AttributeKey_DURATION,
			AttributeValue: "60",
		},
	}

	mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return(mockAttributes, nil)

	result, err := processor.ListByServiceID(context.Background(), 1)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int32(60), *result.Duration)
}

func TestBasicProcessor_ListByServiceID_NotFound(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)

	// 模拟返回空列表
	mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return([]*model.ServiceAttribute{}, nil)

	result, err := processor.ListByServiceID(context.Background(), 1)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	// 应该返回空的 ServiceAttributes，而不是错误
	assert.Nil(t, result.Duration)
	assert.Nil(t, result.MaxDuration)
}

func TestBasicProcessor_ListByServiceID_RepositoryError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)

	mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return(nil, errors.New("database error"))

	result, err := processor.ListByServiceID(context.Background(), 1)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "database error")
}

func TestBasicProcessor_ListByServiceIDs(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)

	// 模拟返回的属性列表
	mockAttributes := []*model.ServiceAttribute{
		{
			ID:             1,
			ServiceID:      1,
			FieldName:      "duration",
			AttributeKey:   offeringpb.AttributeKey_DURATION,
			AttributeValue: "60",
		},
		{
			ID:             2,
			ServiceID:      2,
			FieldName:      "max_duration",
			AttributeKey:   offeringpb.AttributeKey_MAX_DURATION,
			AttributeValue: "120",
		},
	}

	mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1, 2}).Return(mockAttributes, nil)

	result, err := processor.ListByServiceIDs(context.Background(), []int64{1, 2})
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 验证服务1的属性
	assert.Equal(t, int32(60), *result[1].Duration)
	assert.Nil(t, result[1].MaxDuration)

	// 验证服务2的属性
	assert.Nil(t, result[2].Duration)
	assert.Equal(t, int32(120), *result[2].MaxDuration)
}

func TestBasicProcessor_ListByServiceIDs_SomeServicesNotFound(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)

	// 模拟只有服务1有属性，服务2没有属性
	mockAttributes := []*model.ServiceAttribute{
		{
			ID:             1,
			ServiceID:      1,
			FieldName:      "duration",
			AttributeKey:   offeringpb.AttributeKey_DURATION,
			AttributeValue: "60",
		},
	}

	mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1, 2}).Return(mockAttributes, nil)

	result, err := processor.ListByServiceIDs(context.Background(), []int64{1, 2})
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 验证服务1的属性
	assert.Equal(t, int32(60), *result[1].Duration)
	assert.Nil(t, result[1].MaxDuration)

	// 验证服务2返回空属性
	assert.NotNil(t, result[2])
	assert.Nil(t, result[2].Duration)
	assert.Nil(t, result[2].MaxDuration)
}

func TestBasicProcessor_ListByServiceIDs_UnsupportedKey(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)

	// 模拟返回包含不支持的属性键的属性列表
	mockAttributes := []*model.ServiceAttribute{
		{
			ID:             1,
			ServiceID:      1,
			FieldName:      "duration",
			AttributeKey:   offeringpb.AttributeKey_DURATION,
			AttributeValue: "60",
		},
		{
			ID:             2,
			ServiceID:      1,
			FieldName:      "unsupported_field",
			AttributeKey:   offeringpb.AttributeKey_PREREQUISITE_SERVICE, // 不支持的键
			AttributeValue: "some_value",
		},
	}

	mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return(mockAttributes, nil)

	result, err := processor.ListByServiceIDs(context.Background(), []int64{1})
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 只应该处理支持的属性键
	assert.Equal(t, int32(60), *result[1].Duration)
	assert.Nil(t, result[1].MaxDuration)
}

func TestBasicProcessor_ListByServiceIDs_InvalidDurationValue(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)

	// 模拟返回无效的数值
	mockAttributes := []*model.ServiceAttribute{
		{
			ID:             1,
			ServiceID:      1,
			FieldName:      "duration",
			AttributeKey:   offeringpb.AttributeKey_DURATION,
			AttributeValue: "invalid_number",
		},
	}

	mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return(mockAttributes, nil)

	result, err := processor.ListByServiceIDs(context.Background(), []int64{1})
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 无效值应该被忽略，返回 nil
	assert.Nil(t, result[1].Duration)
}

func TestBasicProcessor_ListByServiceIDs_EmptyDurationValue(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)

	// 模拟返回空值
	mockAttributes := []*model.ServiceAttribute{
		{
			ID:             1,
			ServiceID:      1,
			FieldName:      "duration",
			AttributeKey:   offeringpb.AttributeKey_DURATION,
			AttributeValue: "",
		},
	}

	mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return(mockAttributes, nil)

	result, err := processor.ListByServiceIDs(context.Background(), []int64{1})
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 空值应该被忽略，返回 nil
	assert.Nil(t, result[1].Duration)
}

func TestBasicProcessor_Remove(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)

	mockRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1), []offeringpb.AttributeKey{
		offeringpb.AttributeKey_DURATION,
		offeringpb.AttributeKey_MAX_DURATION,
	}).Return(int64(1), nil)

	err := processor.Remove(context.Background(), 1, offeringpb.AttributeKey_DURATION)
	assert.NoError(t, err)
}

func TestBasicProcessor_Remove_UnsupportedKey(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)

	// 测试删除不支持的属性键
	err := processor.Remove(context.Background(), 1, offeringpb.AttributeKey_PREREQUISITE_SERVICE)
	assert.Error(t, err)
	assert.Equal(t, codes.InvalidArgument, status.Code(err))
	assert.Contains(t, err.Error(), "unsupported attribute key for basic processor")
}

func TestBasicProcessor_Remove_RepositoryError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)

	mockRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1), []offeringpb.AttributeKey{
		offeringpb.AttributeKey_DURATION,
		offeringpb.AttributeKey_MAX_DURATION,
	}).Return(int64(0), errors.New("delete error"))

	err := processor.Remove(context.Background(), 1, offeringpb.AttributeKey_DURATION)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "delete error")
}

func TestBasicProcessor_RemoveByServiceID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)

	mockRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1), []offeringpb.AttributeKey{
		offeringpb.AttributeKey_DURATION,
		offeringpb.AttributeKey_MAX_DURATION,
	}).Return(int64(1), nil)

	err := processor.RemoveByServiceID(context.Background(), 1)
	assert.NoError(t, err)
}

func TestBasicProcessor_RemoveByServiceID_RepositoryError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)

	mockRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1), []offeringpb.AttributeKey{
		offeringpb.AttributeKey_DURATION,
		offeringpb.AttributeKey_MAX_DURATION,
	}).Return(int64(0), errors.New("delete error"))

	err := processor.RemoveByServiceID(context.Background(), 1)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "delete error")
}

func TestBasicProcessor_SupportedKeys(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)
	keys := processor.SupportedKeys()

	assert.Len(t, keys, 2)
	assert.Contains(t, keys, offeringpb.AttributeKey_DURATION)
	assert.Contains(t, keys, offeringpb.AttributeKey_MAX_DURATION)
}

func TestBasicProcessor_IsSupportedKey(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)

	// 测试支持的键
	assert.True(t, processor.IsSupportedKey(offeringpb.AttributeKey_DURATION))
	assert.True(t, processor.IsSupportedKey(offeringpb.AttributeKey_MAX_DURATION))

	// 测试不支持的键
	assert.False(t, processor.IsSupportedKey(offeringpb.AttributeKey_PREREQUISITE_SERVICE))
	assert.False(t, processor.IsSupportedKey(offeringpb.AttributeKey_STAFF_AUTO_ASSIGN))
}

func TestBasicProcessor_Register(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)

	// 创建管理器，使用 mock repositories 避免数据库初始化
	manager := NewManagerWithRepositories(
		mockRepo, // basicRepo
		nil,      // autoRolloverRepo
		nil,      // lodgingScopeRepo
		nil,      // staffScopeRepo
	)

	// 注册处理器
	processor.Register(manager)

	// 验证注册方法不会 panic
	assert.NotPanics(t, func() {
		processor.Register(manager)
	})
}

func TestBasicProcessor_NewBasicProcessor(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)

	assert.NotNil(t, processor)
	assert.Equal(t, mockRepo, processor.repo)
}

func TestBasicProcessor_NewBasicProcessorWithRepository(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := attrmock.NewMockRepository(ctrl)
	processor := NewBasicProcessorWithRepository(mockRepo)

	assert.NotNil(t, processor)
	assert.Equal(t, mockRepo, processor.repo)
}
