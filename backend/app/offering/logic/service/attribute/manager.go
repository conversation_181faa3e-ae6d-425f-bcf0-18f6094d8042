package attribute

import (
	"context"
	"fmt"
	"sync"

	"golang.org/x/sync/errgroup"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/serviceattribute"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// Manager 负责管理和协调不同类型的 attribute
// 只处理通用的协调逻辑，具体的 attribute 逻辑由各个 Processor 实现
type Manager struct {
	processors []Processor
}

// NewManager 创建 attribute 管理器
func NewManager() *Manager {
	manager := &Manager{
		processors: make([]Processor, 0),
	}

	NewBasicProcessor().Register(manager)
	NewAutoRolloverProcessor().Register(manager)
	NewStaffProcessor().Register(manager)
	NewLodgingProcessor().Register(manager)

	return manager
}

// NewManagerWithRepositories 创建带有自定义repository的attribute管理器（用于测试）
func NewManagerWithRepositories(
	basicRepo serviceattribute.Repository,
	autoRolloverRepo serviceattribute.AutoRollOverRepository,
	lodgingScopeRepo serviceattribute.LodgingScopeRepository,
	staffScopeRepo serviceattribute.StaffScopeRepository) *Manager {
	manager := &Manager{
		processors: make([]Processor, 0),
	}

	NewBasicProcessorWithRepository(basicRepo).Register(manager)
	NewAutoRolloverProcessorWithRepository(autoRolloverRepo).Register(manager)
	NewStaffProcessorWithRepositories(staffScopeRepo).Register(manager)
	NewLodgingProcessorWithRepositories(lodgingScopeRepo).Register(manager)

	return manager
}

// RegisterProcessor 动态注册新的处理器（用于扩展）
func (m *Manager) RegisterProcessor(processor Processor) {
	m.processors = append(m.processors, processor)
}

// Save 保存服务属性配置
// 参数：
//   - ctx: 上下文
//   - serviceID: 服务ID
//   - attributes: 服务属性配置
//
// 返回：处理成功返回nil，失败返回错误
func (m *Manager) Save(
	ctx context.Context, serviceID int64, attributes *offeringpb.ServiceAttributes) error {
	if attributes == nil {
		return nil
	}

	// 遍历所有处理器进行处理
	for _, processor := range m.processors {
		err := processor.Save(ctx, serviceID, attributes)
		if err != nil {
			return fmt.Errorf("failed to process attributes with processor %T: %w", processor, err)
		}
	}

	return nil
}

// ListByServiceID 获取 service 下的所有属性值
// 参数：
//   - ctx: 上下文
//   - serviceID: 服务ID
//
// 返回：服务属性配置，如果不存在则返回空配置
func (m *Manager) ListByServiceID(
	ctx context.Context, serviceID int64) (*offeringpb.ServiceAttributes, error) {
	attributes, err := m.ListByServiceIDs(ctx, []int64{serviceID})
	if err != nil {
		return nil, err
	}

	return attributes[serviceID], nil
}

// ListByServiceIDs 批量获取多个服务的属性配置
// 参数：
//   - ctx: 上下文
//   - serviceIDs: 服务ID列表
//
// 返回：服务ID到属性配置的映射，如果不存在则返回空配置
func (m *Manager) ListByServiceIDs(
	ctx context.Context, serviceIDs []int64) (map[int64]*offeringpb.ServiceAttributes, error) {
	result := make(map[int64]*offeringpb.ServiceAttributes)

	// 初始化结果映射，为每个服务ID创建空配置
	for _, serviceID := range serviceIDs {
		result[serviceID] = &offeringpb.ServiceAttributes{}
	}

	group, ctx := errgroup.WithContext(ctx)
	var mu sync.Mutex

	// 启动所有 processor
	for _, processor := range m.processors {
		p := processor // 避免闭包问题
		group.Go(func() error {
			attributes, err := p.ListByServiceIDs(ctx, serviceIDs)
			if err != nil {
				return fmt.Errorf("failed to list attributes with processor %T: %w", p, err)
			}

			// 合并配置
			mu.Lock()
			defer mu.Unlock()
			for serviceID, attrs := range attributes {
				if attrs != nil {
					if err := m.mergeServiceAttributes(result[serviceID], attrs); err != nil {
						return fmt.Errorf("failed to merge attributes for service %d: %w", serviceID, err)
					}
				}
			}

			return nil
		})
	}

	// 等待所有完成或遇到第一个错误
	if err := group.Wait(); err != nil {
		return nil, err
	}

	return result, nil
}

// mergeServiceAttributes 合并两个 ServiceAttributes 结构
func (m *Manager) mergeServiceAttributes(target, source *offeringpb.ServiceAttributes) error {
	// 合并基础属性
	if source.Duration != nil {
		target.Duration = source.Duration
	}
	if source.MaxDuration != nil {
		target.MaxDuration = source.MaxDuration
	}

	// 合并关联表配置
	if source.AutoRollover != nil {
		target.AutoRollover = source.AutoRollover
	}

	if source.AvailableStaff != nil {
		target.AvailableStaff = source.AvailableStaff
	}

	if source.AvailableLodgingType != nil {
		target.AvailableLodgingType = source.AvailableLodgingType
	}

	return nil
}

// DeleteByServiceID 删除 service 下的所有属性值
// 参数：
//   - ctx: 上下文
//   - serviceID: 服务ID
//
// 返回：删除成功返回nil，失败返回错误
func (m *Manager) DeleteByServiceID(ctx context.Context, serviceID int64) error {
	for _, processor := range m.processors {
		if err := processor.RemoveByServiceID(ctx, serviceID); err != nil {
			return err
		}
	}

	return nil
}
