load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "attribute",
    srcs = [
        "basic_processor.go",
        "converter.go",
        "entity.go",
        "lodging_processor.go",
        "manager.go",
        "processor.go",
        "rollover_processor.go",
        "staff_processor.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/logic/service/attribute",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/repo/db/model",
        "//backend/app/offering/repo/db/serviceattribute",
        "//backend/app/offering/utils",
        "//backend/proto/offering/v1:offering",
        "@cat_dario_mergo//:mergo",
        "@com_github_samber_lo//:lo",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/structpb",
        "@org_golang_x_sync//errgroup",
    ],
)

go_test(
    name = "attribute_test",
    srcs = [
        "basic_processor_test.go",
        "converter_test.go",
        "lodging_processor_test.go",
        "manager_test.go",
        "rollover_processor_test.go",
        "staff_processor_test.go",
    ],
    embed = [":attribute"],
    deps = [
        "//backend/app/offering/repo/db/model",
        "//backend/app/offering/repo/db/serviceattribute/mocks",
        "//backend/app/offering/utils",
        "//backend/proto/offering/v1:offering",
        "@com_github_stretchr_testify//assert",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_uber_go_mock//gomock",
    ],
)
