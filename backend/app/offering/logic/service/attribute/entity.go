package attribute

import (
	"time"

	"google.golang.org/protobuf/types/known/structpb"

	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// attributeKeyToFieldName 定义 AttributeKey 到 field_name 的映射
var attributeKeyToFieldName = map[offeringpb.AttributeKey]string{
	offeringpb.AttributeKey_DURATION:               "duration",
	offeringpb.AttributeKey_MAX_DURATION:           "max_duration",
	offeringpb.AttributeKey_PREREQUISITE_SERVICE:   "prerequisite_service",
	offeringpb.AttributeKey_STAFF_AUTO_ASSIGN:      "staff_auto_assign",
	offeringpb.AttributeKey_RESULT_RESETTABLE:      "result_resettle",
	offeringpb.AttributeKey_AVAILABLE_STAFF:        "available_staff",
	offeringpb.AttributeKey_AVAILABLE_LODGING_TYPE: "available_lodging_type",
	offeringpb.AttributeKey_AUTO_ROLLOVER:          "auto_rollover",
}

// ServiceAttribute represents a service attribute value entity
type ServiceAttribute struct {
	ID             int64
	ServiceID      int64
	FieldName      string
	AttributeKey   offeringpb.AttributeKey
	AttributeValue *structpb.Value
	CreateTime     time.Time
	UpdateTime     time.Time
	DeleteTime     *time.Time
}
