package attribute

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	attrmock "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/serviceattribute/mocks"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func TestLodgingProcessor_Save(t *testing.T) {
	t.Run("保存适用于所有 lodging 的配置", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockLodgingScopeRepository(ctrl)
		processor := NewLodgingProcessorWithRepositories(mockRepo)

		attr := &offeringpb.ServiceAttributes{
			AvailableLodgingType: &offeringpb.AvailableLodgingType{
				IsAll:          true,
				LodgingTypeIds: []int64{},
			},
		}

		mockRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return(nil, nil)
		mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, scope *model.ServiceLodgingScope) error {
				assert.Equal(t, int64(1), scope.ServiceID)
				assert.True(t, scope.IsAllLodging)
				return nil
			})

		err := processor.Save(context.Background(), 1, attr)
		assert.NoError(t, err)
	})

	t.Run("保存适用于特定 lodging 的配置", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockLodgingScopeRepository(ctrl)
		processor := NewLodgingProcessorWithRepositories(mockRepo)

		attr := &offeringpb.ServiceAttributes{
			AvailableLodgingType: &offeringpb.AvailableLodgingType{
				IsAll:          false,
				LodgingTypeIds: []int64{1, 2, 3},
			},
		}

		mockRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return(nil, nil)
		mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, scope *model.ServiceLodgingScope) error {
				assert.Equal(t, int64(1), scope.ServiceID)
				assert.False(t, scope.IsAllLodging)
				assert.Equal(t, []int64{1, 2, 3}, scope.AvailableLodgingTypeIds)
				return nil
			})

		err := processor.Save(context.Background(), 1, attr)
		assert.NoError(t, err)
	})

	t.Run("更新现有配置", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockLodgingScopeRepository(ctrl)
		processor := NewLodgingProcessorWithRepositories(mockRepo)

		attr := &offeringpb.ServiceAttributes{
			AvailableLodgingType: &offeringpb.AvailableLodgingType{
				IsAll:          false,
				LodgingTypeIds: []int64{4, 5},
			},
		}

		existingScope := &model.ServiceLodgingScope{
			ID:           1,
			ServiceID:    1,
			IsAllLodging: true,
		}

		mockRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return(existingScope, nil)
		mockRepo.EXPECT().Update(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, scope *model.ServiceLodgingScope) error {
				assert.Equal(t, int64(1), scope.ID)
				assert.False(t, scope.IsAllLodging)
				return nil
			})

		err := processor.Save(context.Background(), 1, attr)
		assert.NoError(t, err)
	})

	t.Run("没有 lodging 配置时直接返回", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockLodgingScopeRepository(ctrl)
		processor := NewLodgingProcessorWithRepositories(mockRepo)

		attr := &offeringpb.ServiceAttributes{}

		err := processor.Save(context.Background(), 1, attr)
		assert.NoError(t, err)
	})

	t.Run("nil 属性时直接返回", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockLodgingScopeRepository(ctrl)
		processor := NewLodgingProcessorWithRepositories(mockRepo)

		err := processor.Save(context.Background(), 1, nil)
		assert.NoError(t, err)
	})

	t.Run("创建记录时发生错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockLodgingScopeRepository(ctrl)
		processor := NewLodgingProcessorWithRepositories(mockRepo)

		attr := &offeringpb.ServiceAttributes{
			AvailableLodgingType: &offeringpb.AvailableLodgingType{
				IsAll:          true,
				LodgingTypeIds: []int64{},
			},
		}

		mockRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return(nil, nil)
		mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(errors.New("create error"))

		err := processor.Save(context.Background(), 1, attr)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "create error")
	})

	t.Run("查询现有记录时发生错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockLodgingScopeRepository(ctrl)
		processor := NewLodgingProcessorWithRepositories(mockRepo)

		attr := &offeringpb.ServiceAttributes{
			AvailableLodgingType: &offeringpb.AvailableLodgingType{
				IsAll:          true,
				LodgingTypeIds: []int64{},
			},
		}

		mockRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return(nil, errors.New("query error"))

		err := processor.Save(context.Background(), 1, attr)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "query error")
	})

	t.Run("更新记录时发生错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockLodgingScopeRepository(ctrl)
		processor := NewLodgingProcessorWithRepositories(mockRepo)

		attr := &offeringpb.ServiceAttributes{
			AvailableLodgingType: &offeringpb.AvailableLodgingType{
				IsAll:          false,
				LodgingTypeIds: []int64{1, 2},
			},
		}

		existingScope := &model.ServiceLodgingScope{
			ID:           1,
			ServiceID:    1,
			IsAllLodging: true,
		}

		mockRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return(existingScope, nil)
		mockRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(errors.New("update error"))

		err := processor.Save(context.Background(), 1, attr)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "update error")
	})
}

func TestLodgingProcessor_ListByServiceID(t *testing.T) {
	t.Run("获取单个服务的 lodging 配置", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockLodgingScopeRepository(ctrl)
		processor := NewLodgingProcessorWithRepositories(mockRepo)

		mockScope := &model.ServiceLodgingScope{
			ID:                      1,
			ServiceID:               1,
			IsAllLodging:            false,
			AvailableLodgingTypeIds: []int64{1, 2, 3},
		}

		mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return([]*model.ServiceLodgingScope{mockScope}, nil)

		result, err := processor.ListByServiceID(context.Background(), 1)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.False(t, result.AvailableLodgingType.IsAll)
		assert.Equal(t, []int64{1, 2, 3}, result.AvailableLodgingType.LodgingTypeIds)
	})

	t.Run("获取适用于所有 lodging 的配置", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockLodgingScopeRepository(ctrl)
		processor := NewLodgingProcessorWithRepositories(mockRepo)

		mockScope := &model.ServiceLodgingScope{
			ID:           1,
			ServiceID:    1,
			IsAllLodging: true,
		}

		mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return([]*model.ServiceLodgingScope{mockScope}, nil)

		result, err := processor.ListByServiceID(context.Background(), 1)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.True(t, result.AvailableLodgingType.IsAll)
		assert.Empty(t, result.AvailableLodgingType.LodgingTypeIds)
	})

	t.Run("服务没有 lodging 配置时返回空配置", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockLodgingScopeRepository(ctrl)
		processor := NewLodgingProcessorWithRepositories(mockRepo)

		mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return([]*model.ServiceLodgingScope{}, nil)

		result, err := processor.ListByServiceID(context.Background(), 1)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.True(t, result.AvailableLodgingType.IsAll)
		assert.Empty(t, result.AvailableLodgingType.LodgingTypeIds)
	})

	t.Run("查询失败时返回错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockLodgingScopeRepository(ctrl)
		processor := NewLodgingProcessorWithRepositories(mockRepo)

		mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1}).Return(nil, errors.New("query error"))

		result, err := processor.ListByServiceID(context.Background(), 1)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "query error")
	})
}

func TestLodgingProcessor_ListByServiceIDs(t *testing.T) {
	t.Run("批量获取多个服务的 lodging 配置", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockLodgingScopeRepository(ctrl)
		processor := NewLodgingProcessorWithRepositories(mockRepo)

		mockScopes := []*model.ServiceLodgingScope{
			{
				ID:                      1,
				ServiceID:               1,
				IsAllLodging:            false,
				AvailableLodgingTypeIds: []int64{1, 2},
			},
			{
				ID:           2,
				ServiceID:    2,
				IsAllLodging: true,
			},
		}

		mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1, 2}).Return(mockScopes, nil)

		result, err := processor.ListByServiceIDs(context.Background(), []int64{1, 2})
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result, 2)

		assert.False(t, result[1].AvailableLodgingType.IsAll)
		assert.Equal(t, []int64{1, 2}, result[1].AvailableLodgingType.LodgingTypeIds)

		assert.True(t, result[2].AvailableLodgingType.IsAll)
		assert.Empty(t, result[2].AvailableLodgingType.LodgingTypeIds)
	})

	t.Run("部分服务没有配置时返回默认值", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockLodgingScopeRepository(ctrl)
		processor := NewLodgingProcessorWithRepositories(mockRepo)

		mockScopes := []*model.ServiceLodgingScope{
			{
				ID:                      1,
				ServiceID:               1,
				IsAllLodging:            false,
				AvailableLodgingTypeIds: []int64{1, 2},
			},
		}

		mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1, 2}).Return(mockScopes, nil)

		result, err := processor.ListByServiceIDs(context.Background(), []int64{1, 2})
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Len(t, result, 2)

		assert.False(t, result[1].AvailableLodgingType.IsAll)
		assert.Equal(t, []int64{1, 2}, result[1].AvailableLodgingType.LodgingTypeIds)

		assert.True(t, result[2].AvailableLodgingType.IsAll)
		assert.Empty(t, result[2].AvailableLodgingType.LodgingTypeIds)
	})

	t.Run("查询失败时返回错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockLodgingScopeRepository(ctrl)
		processor := NewLodgingProcessorWithRepositories(mockRepo)

		mockRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{1, 2}).Return(nil, errors.New("query error"))

		result, err := processor.ListByServiceIDs(context.Background(), []int64{1, 2})
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "query error")
	})
}

func TestLodgingProcessor_Remove(t *testing.T) {
	t.Run("删除支持的属性键", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockLodgingScopeRepository(ctrl)
		processor := NewLodgingProcessorWithRepositories(mockRepo)

		mockRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(1), nil)

		err := processor.Remove(context.Background(), 1, offeringpb.AttributeKey_AVAILABLE_LODGING_TYPE)
		assert.NoError(t, err)
	})

	t.Run("删除不支持的属性键时返回错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockLodgingScopeRepository(ctrl)
		processor := NewLodgingProcessorWithRepositories(mockRepo)

		err := processor.Remove(context.Background(), 1, offeringpb.AttributeKey_DURATION)
		assert.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
		assert.Contains(t, err.Error(), "unsupported attribute key for lodging processor")
	})

	t.Run("删除失败时返回错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockLodgingScopeRepository(ctrl)
		processor := NewLodgingProcessorWithRepositories(mockRepo)

		mockRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(0), errors.New("delete error"))

		err := processor.Remove(context.Background(), 1, offeringpb.AttributeKey_AVAILABLE_LODGING_TYPE)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "delete error")
	})
}

func TestLodgingProcessor_RemoveByServiceID(t *testing.T) {
	t.Run("删除指定服务的所有 lodging 配置", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockLodgingScopeRepository(ctrl)
		processor := NewLodgingProcessorWithRepositories(mockRepo)

		mockRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(1), nil)

		err := processor.RemoveByServiceID(context.Background(), 1)
		assert.NoError(t, err)
	})

	t.Run("删除失败时返回错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockLodgingScopeRepository(ctrl)
		processor := NewLodgingProcessorWithRepositories(mockRepo)

		mockRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(int64(0), errors.New("delete error"))

		err := processor.RemoveByServiceID(context.Background(), 1)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "delete error")
	})
}

func TestLodgingProcessor_SupportedKeys(t *testing.T) {
	t.Run("返回支持的属性键列表", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockLodgingScopeRepository(ctrl)
		processor := NewLodgingProcessorWithRepositories(mockRepo)

		keys := processor.SupportedKeys()
		assert.Len(t, keys, 1)
		assert.Contains(t, keys, offeringpb.AttributeKey_AVAILABLE_LODGING_TYPE)
	})
}

func TestLodgingProcessor_IsSupportedKey(t *testing.T) {
	t.Run("检查支持的属性键", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockLodgingScopeRepository(ctrl)
		processor := NewLodgingProcessorWithRepositories(mockRepo)

		assert.True(t, processor.IsSupportedKey(offeringpb.AttributeKey_AVAILABLE_LODGING_TYPE))

		assert.False(t, processor.IsSupportedKey(offeringpb.AttributeKey_DURATION))
		assert.False(t, processor.IsSupportedKey(offeringpb.AttributeKey_MAX_DURATION))
		assert.False(t, processor.IsSupportedKey(offeringpb.AttributeKey_STAFF_AUTO_ASSIGN))
	})
}

func TestLodgingProcessor_Register(t *testing.T) {
	t.Run("向管理器注册处理器", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockLodgingScopeRepository(ctrl)
		processor := NewLodgingProcessorWithRepositories(mockRepo)

		manager := NewManagerWithRepositories(
			nil,      // basicRepo
			nil,      // autoRolloverRepo
			mockRepo, // lodgingScopeRepo
			nil,      // staffScopeRepo
		)

		processor.Register(manager)

		assert.NotPanics(t, func() {
			processor.Register(manager)
		})
	})
}

func TestLodgingProcessor_Constructor(t *testing.T) {
	t.Run("创建带有自定义 repository 的处理器", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockLodgingScopeRepository(ctrl)
		processor := NewLodgingProcessorWithRepositories(mockRepo)

		assert.NotNil(t, processor)
		assert.Equal(t, mockRepo, processor.repo)
	})
}

func TestLodgingProcessor_Converter(t *testing.T) {
	t.Run("ToModel 转换", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockLodgingScopeRepository(ctrl)
		processor := NewLodgingProcessorWithRepositories(mockRepo)

		// 测试 nil 属性
		result := processor.ToModel(1, nil)
		assert.Nil(t, result)

		// 测试没有 AvailableLodgingType 的属性
		attr := &offeringpb.ServiceAttributes{}
		result = processor.ToModel(1, attr)
		assert.Nil(t, result)

		// 测试适用于所有 lodging 的配置
		attr = &offeringpb.ServiceAttributes{
			AvailableLodgingType: &offeringpb.AvailableLodgingType{
				IsAll:          true,
				LodgingTypeIds: []int64{},
			},
		}
		result = processor.ToModel(1, attr)
		assert.NotNil(t, result)
		assert.Equal(t, int64(1), result.ServiceID)
		assert.True(t, result.IsAllLodging)
		assert.Empty(t, result.AvailableLodgingTypeIds)

		// 测试适用于特定 lodging 的配置
		attr = &offeringpb.ServiceAttributes{
			AvailableLodgingType: &offeringpb.AvailableLodgingType{
				IsAll:          false,
				LodgingTypeIds: []int64{1, 2, 3},
			},
		}
		result = processor.ToModel(1, attr)
		assert.NotNil(t, result)
		assert.Equal(t, int64(1), result.ServiceID)
		assert.False(t, result.IsAllLodging)
		assert.Equal(t, []int64{1, 2, 3}, result.AvailableLodgingTypeIds)
	})

	t.Run("FromModel 转换", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := attrmock.NewMockLodgingScopeRepository(ctrl)
		processor := NewLodgingProcessorWithRepositories(mockRepo)

		// 测试 nil scope
		result := processor.FromModel(nil)
		assert.NotNil(t, result)
		assert.True(t, result.AvailableLodgingType.IsAll)
		assert.Empty(t, result.AvailableLodgingType.LodgingTypeIds)

		// 测试适用于所有 lodging 的 scope
		scope := &model.ServiceLodgingScope{
			ID:           1,
			ServiceID:    1,
			IsAllLodging: true,
		}
		result = processor.FromModel(scope)
		assert.NotNil(t, result)
		assert.True(t, result.AvailableLodgingType.IsAll)
		assert.Empty(t, result.AvailableLodgingType.LodgingTypeIds)

		// 测试适用于特定 lodging 的 scope
		scope = &model.ServiceLodgingScope{
			ID:                      1,
			ServiceID:               1,
			IsAllLodging:            false,
			AvailableLodgingTypeIds: []int64{1, 2, 3},
		}
		result = processor.FromModel(scope)
		assert.NotNil(t, result)
		assert.False(t, result.AvailableLodgingType.IsAll)
		assert.Equal(t, []int64{1, 2, 3}, result.AvailableLodgingType.LodgingTypeIds)
	})
}
