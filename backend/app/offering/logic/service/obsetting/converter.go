package obsetting

import (
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// EntityToModel converts a domain entity to a model
func EntityToModel(e *ServiceOnlineBookingSetting) *model.ServiceObSetting {
	if e == nil {
		return nil
	}

	return &model.ServiceObSetting{
		ID:               e.ID,
		OrganizationType: e.OrganizationType,
		OrganizationID:   e.OrganizationID,
		ServiceID:        e.ServiceID,
		IsAvailable:      e.IsAvailable,
		ShowBasePrice:    int16(e.ShowBasePrice),
		IsAllStaff:       e.Is<PERSON>ll<PERSON>taff,
		Alias_:           e.<PERSON>,
		CreatedAt:        &e.CreateTime,
		UpdatedAt:        &e.UpdateTime,
	}
}

// ModelToEntity converts a model to a domain entity
func ModelToEntity(m *model.ServiceObSetting) *ServiceOnlineBookingSetting {
	if m == nil {
		return nil
	}

	return &ServiceOnlineBookingSetting{
		ID:               m.ID,
		OrganizationType: m.OrganizationType,
		OrganizationID:   m.OrganizationID,
		ServiceID:        m.ServiceID,
		IsAvailable:      m.IsAvailable,
		ShowBasePrice:    offeringpb.ServiceOBSetting_ShowBasePriceMode(m.ShowBasePrice),
		IsAllStaff:       m.IsAllStaff,
		Alias:            m.Alias_,
		CreateTime:       *m.CreatedAt,
		UpdateTime:       *m.UpdatedAt,
	}
}

// ModelToEntityList converts a list of models to domain entities
func ModelToEntityList(ms []*model.ServiceObSetting) []*ServiceOnlineBookingSetting {
	if ms == nil {
		return nil
	}

	entities := make([]*ServiceOnlineBookingSetting, 0, len(ms))
	for _, m := range ms {
		entities = append(entities, ModelToEntity(m))
	}

	return entities
}

// EntityToProto converts a domain entity to a protobuf message
func EntityToProto(e *ServiceOnlineBookingSetting) *offeringpb.ServiceOBSetting {
	if e == nil {
		return nil
	}

	return &offeringpb.ServiceOBSetting{
		ServiceId:     e.ServiceID,
		IsAvailable:   e.IsAvailable,
		ShowBasePrice: e.ShowBasePrice,
		IsAllStaff:    e.IsAllStaff,
		Alias:         e.Alias,
	}
}

// EntityToProtoList converts a list of domain entities to protobuf messages
func EntityToProtoList(es []*ServiceOnlineBookingSetting) []*offeringpb.ServiceOBSetting {
	if es == nil {
		return nil
	}

	protos := make([]*offeringpb.ServiceOBSetting, 0, len(es))
	for _, e := range es {
		protos = append(protos, EntityToProto(e))
	}

	return protos
}
