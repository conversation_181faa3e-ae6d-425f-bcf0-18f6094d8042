package availability

import (
	"context"
	"errors"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// Manager 负责管理和协调不同类型的 availability 逻辑
type Manager struct {
	typeBreedLogic *TypeBreedLogic
	petSizeLogic   *PetSizeLogic
	coatTypeLogic  *CoatTypeLogic
	petCodeLogic   *PetCodeLogic
	petWeightLogic *PetWeightLogic
	petScopeRepo   service.PetScopeRepository
	petWeightRepo  service.PetWeightRepository
}

// NewManager 创建 availability 管理器
func NewManager() *Manager {
	return &Manager{
		typeBreedLogic: NewTypeBreedLogic(),
		petSizeLogic:   NewPetSizeLogic(),
		coatTypeLogic:  NewCoatTypeLogic(),
		petCodeLogic:   NewPetCodeLogic(),
		petWeightLogic: NewPetWeightLogic(),
		petScopeRepo:   service.NewPetScopeRepository(),
		petWeightRepo:  service.NewPetWeightRepository(),
	}
}

// NewManagerWithRepository 创建 availability 管理器，允许传入自定义的 repository
func NewManagerWithRepository(
	petScopeRepo service.PetScopeRepository,
	petWeightRepo service.PetWeightRepository,
) *Manager {
	return &Manager{
		typeBreedLogic: NewTypeBreedLogic(),
		petSizeLogic:   NewPetSizeLogic(),
		coatTypeLogic:  NewCoatTypeLogic(),
		petCodeLogic:   NewPetCodeLogic(),
		petWeightLogic: NewPetWeightLogic(),
		petScopeRepo:   petScopeRepo,
		petWeightRepo:  petWeightRepo,
	}
}

// Save 保存所有类型的可用性配置
func (m *Manager) Save(ctx context.Context, query *query.Query, availability *PetAvailability) error {
	// 1. 转换 Pet availability 配置为通用的 scope 记录
	var allScopes []*model.ServicePetAvailabilityScope
	var serviceID = availability.ServiceID
	if availability.PetTypeBreed != nil {
		scopes := m.typeBreedLogic.ProtoToModel(serviceID, availability.PetTypeBreed)
		allScopes = append(allScopes, scopes...)
	}

	if availability.PetSize != nil {
		scopes := m.petSizeLogic.ProtoToModel(serviceID, availability.PetSize)
		allScopes = append(allScopes, scopes...)
	}

	if availability.CoatType != nil {
		scopes := m.coatTypeLogic.ProtoToModel(serviceID, availability.CoatType)
		allScopes = append(allScopes, scopes...)
	}

	if availability.PetCode != nil {
		scopes := m.petCodeLogic.ProtoToModel(serviceID, availability.PetCode)
		allScopes = append(allScopes, scopes...)
	}

	// 2. 批量保存所有 scope 记录
	if err := m.petScopeRepo.WithQuery(query).DeleteByServiceID(ctx, serviceID); err != nil {
		return errors.New("failed to delete pet availability scopes: %w")
	}
	if len(allScopes) > 0 {
		if err := m.petScopeRepo.WithQuery(query).BatchCreate(ctx, allScopes); err != nil {
			return errors.New("failed to batch create pet availability scopes: %w")
		}
	}

	// 3. 保存 Pet weight ranges
	if err := m.petWeightRepo.WithQuery(query).DeleteByServiceID(ctx, serviceID); err != nil {
		return errors.New("failed to delete pet weight ranges: %w")
	}
	if availability.PetWeight != nil {
		weightRanges, err := m.petWeightLogic.ProtoToModel(serviceID, availability.PetWeight)
		if err != nil {
			return err
		}
		if len(weightRanges) > 0 {
			if err := m.petWeightRepo.WithQuery(query).BatchCreate(ctx, weightRanges); err != nil {
				return errors.New("failed to batch create pet weight ranges: %w")
			}
		}
	}

	return nil
}

// DeleteByServiceID 删除所有类型的可用性配置
func (m *Manager) DeleteByServiceID(ctx context.Context, query *query.Query, serviceID int64) error {
	if err := m.petScopeRepo.WithQuery(query).DeleteByServiceID(ctx, serviceID); err != nil {
		return errors.New("failed to delete pet availability scopes: %w")
	}

	if err := m.petWeightRepo.WithQuery(query).DeleteByServiceID(ctx, serviceID); err != nil {
		return errors.New("failed to delete pet weight ranges: %w")
	}

	return nil
}

// GetByServiceID 根据服务ID获取所有类型的可用性配置
func (m *Manager) GetByServiceID(ctx context.Context, serviceID int64) (*PetAvailability, error) {
	allScopes, err := m.petScopeRepo.List(ctx, service.ListScopeFilter{
		ServiceIDs: []int64{serviceID},
	})
	if err != nil {
		return nil, errors.New("failed to get pet availability scopes: %w")
	}

	// 获取 pet weight ranges
	weightRanges, err := m.petWeightRepo.GetByServiceID(ctx, serviceID)
	if err != nil {
		return nil, errors.New("failed to get pet weight ranges: %w")
	}

	// TODO: 如果没有配置，返回适用于所有宠物类型的默认配置（不确定是否需要）
	if allScopes == nil && len(weightRanges) == 0 {
		return &PetAvailability{
			ServiceID:    serviceID,
			PetTypeBreed: &offeringpb.AvailablePetTypeBreed{IsAll: true},
			PetSize:      &offeringpb.AvailablePetSize{IsAll: true},
			CoatType:     &offeringpb.AvailableCoatType{IsAll: true},
			PetCode:      &offeringpb.AvailablePetCode{RuleType: offeringpb.AvailabilityRuleType_NO_RESTRICTION},
			PetWeight:    &offeringpb.AvailablePetWeight{IsAll: true},
		}, nil
	}

	// 将 scope 记录转换为对应的 pb 配置
	return &PetAvailability{
		ServiceID:    serviceID,
		PetTypeBreed: m.typeBreedLogic.ModelToProto(allScopes),
		PetSize:      m.petSizeLogic.ModelToProto(allScopes),
		CoatType:     m.coatTypeLogic.ModelToProto(allScopes),
		PetCode:      m.petCodeLogic.ModelToProto(allScopes),
		PetWeight:    m.petWeightLogic.ModelToProto(weightRanges),
	}, nil
}
