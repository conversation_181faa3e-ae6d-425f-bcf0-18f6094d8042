package availability

import (
	"errors"
	"fmt"
	"log"

	"github.com/samber/lo"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// PetWeightLogic 处理宠物重量的可用性逻辑
type PetWeightLogic struct {
}

// NewPetWeightLogic 创建新的 PetWeightLogic 实例
func NewPetWeightLogic() *PetWeightLogic {
	return &PetWeightLogic{}
}

// ProtoToModel 将 pb 配置转换为 model 结构，供 manager 批量保存
func (l *PetWeightLogic) ProtoToModel(
	serviceID int64, config *offeringpb.AvailablePetWeight) ([]*model.ServicePetWeightRange, error) {
	// Case 1: 如果 config 为 nil，表示全部可用
	if config == nil {
		return []*model.ServicePetWeightRange{
			{
				ServiceID:  serviceID,
				IsAllRange: true,
			},
		}, nil
	}

	// Case 2: 如果 IsAll 为 true，表示全部可用
	if config.IsAll {
		return []*model.ServicePetWeightRange{
			{
				ServiceID:  serviceID,
				IsAllRange: true,
			},
		}, nil
	}

	// Case 3: 部分可用，为每个重量范围创建记录
	// 验证重量范围不重叠
	if err := l.validateWeightRanges(config.GetPetWeightRanges()); err != nil {
		// 如果验证失败，返回错误
		return nil, err
	}

	ranges := []*model.ServicePetWeightRange{}
	for _, weightRange := range config.GetPetWeightRanges() {
		ranges = append(ranges, &model.ServicePetWeightRange{
			ServiceID:  serviceID,
			MinWeight:  weightRange.GetMinWeight(),
			MaxWeight:  weightRange.GetMaxWeight(),
			IsAllRange: false,
		})
	}

	return ranges, nil
}

// ModelToProto 将 model 结构转换为 pb 配置
func (l *PetWeightLogic) ModelToProto(
	ranges []*model.ServicePetWeightRange) *offeringpb.AvailablePetWeight {
	// Case 1: 如果没有 ranges，表示全部可用
	if len(ranges) == 0 {
		return &offeringpb.AvailablePetWeight{
			IsAll:           true,
			PetWeightRanges: []*offeringpb.PetWeightRange{},
		}
	}

	// Case 2: 检查是否有 IsAllRange 为 true 的记录
	for _, weightRange := range ranges {
		if weightRange.IsAllRange {
			return &offeringpb.AvailablePetWeight{
				IsAll:           true,
				PetWeightRanges: []*offeringpb.PetWeightRange{},
			}
		}
	}

	// Case 3: 部分可用，转换具体的重量范围
	weightRanges := lo.Map(ranges, func(weightRange *model.ServicePetWeightRange, _ int) *offeringpb.PetWeightRange {
		return &offeringpb.PetWeightRange{
			MinWeight: weightRange.MinWeight,
			MaxWeight: weightRange.MaxWeight,
		}
	})

	return &offeringpb.AvailablePetWeight{
		IsAll:           false,
		PetWeightRanges: weightRanges,
	}
}

// validateWeightRanges 验证重量范围不重叠
func (l *PetWeightLogic) validateWeightRanges(ranges []*offeringpb.PetWeightRange) error {
	if len(ranges) <= 1 {
		return nil // 单个范围或空范围不需要验证重叠
	}

	// 按 MinWeight 排序，便于检查重叠
	sortedRanges := make([]*offeringpb.PetWeightRange, len(ranges))
	copy(sortedRanges, ranges)

	// 简单的冒泡排序，按 MinWeight 升序排列
	for i := 0; i < len(sortedRanges)-1; i++ {
		for j := 0; j < len(sortedRanges)-1-i; j++ {
			if sortedRanges[j].MinWeight > sortedRanges[j+1].MinWeight {
				sortedRanges[j], sortedRanges[j+1] = sortedRanges[j+1], sortedRanges[j]
			}
		}
	}

	// 检查相邻范围是否重叠
	for i := 0; i < len(sortedRanges)-1; i++ {
		current := sortedRanges[i]
		next := sortedRanges[i+1]

		// 检查重叠：当前范围的 MaxWeight >= 下一个范围的 MinWeight
		if current.MaxWeight >= next.MinWeight {
			errMsg := fmt.Sprintf("weight ranges overlap: [%f-%f] and [%f-%f]",
				current.MinWeight, current.MaxWeight, next.MinWeight, next.MaxWeight)
			log.Printf("PetWeightLogic: %s", errMsg)

			return errors.New(errMsg)
		}
	}

	return nil
}
