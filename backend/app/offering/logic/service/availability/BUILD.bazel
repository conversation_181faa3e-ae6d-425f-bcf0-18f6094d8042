load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "availability",
    srcs = [
        "coat_type.go",
        "entity.go",
        "manager.go",
        "pet_code.go",
        "pet_size.go",
        "pet_weight.go",
        "type_breed.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/logic/service/availability",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/repo/db/model",
        "//backend/app/offering/repo/db/query",
        "//backend/app/offering/repo/db/service",
        "//backend/proto/offering/v1:offering",
        "@com_github_samber_lo//:lo",
    ],
)

go_test(
    name = "availability_test",
    srcs = [
        "coat_type_test.go",
        "entity_test.go",
        "manager_test.go",
        "pet_code_test.go",
        "pet_size_test.go",
        "type_breed_test.go",
    ],
    embed = [":availability"],
    deps = [
        "//backend/app/offering/repo/db/model",
        "//backend/app/offering/repo/db/query",
        "//backend/app/offering/repo/db/service/mocks",
        "//backend/proto/offering/v1:offering",
        "@com_github_stretchr_testify//assert",
        "@org_uber_go_mock//gomock",
    ],
)
