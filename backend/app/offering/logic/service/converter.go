package service

import (
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// ProtoToEntity converts protobuf Service to Service entity
func ProtoToEntity(pb *offeringpb.Service) *Service {
	if pb == nil {
		return nil
	}

	return &Service{
		ID:               pb.GetId(),
		OrganizationType: pb.GetOrganizationType(),
		OrganizationID:   pb.GetOrganizationId(),
		CareTypeID:       pb.GetCareTypeId(),
		CategoryID:       pb.CategoryId,
		Name:             pb.GetName(),
		Description:      pb.Description,
		ColorCode:        pb.GetColorCode(),
		Sort:             pb.Sort,
		Images:           pb.GetImages(),
		Source:           pb.GetSource(),
		Status:           pb.GetStatus(),
	}
}

// EntityToProto converts Service entity to protobuf Service
func EntityToProto(e *Service) *offeringpb.Service {
	if e == nil {
		return nil
	}

	pb := &offeringpb.Service{
		Id:               e.ID,
		OrganizationType: e.OrganizationType,
		OrganizationId:   e.OrganizationID,
		CareTypeId:       e.CareTypeID,
		CategoryId:       e.CategoryID,
		Name:             e.Name,
		Description:      e.Description,
		ColorCode:        e.ColorCode,
		Sort:             e.Sort,
		Images:           e.Images,
		Source:           e.Source,
		Status:           e.Status,
		IsDeleted:        e.DeleteTime != nil,
	}

	if e.CreateTime != nil {
		pb.CreateTime = timestamppb.New(*e.CreateTime)
	}
	if e.UpdateTime != nil {
		pb.UpdateTime = timestamppb.New(*e.UpdateTime)
	}
	if e.DeleteTime != nil {
		pb.DeleteTime = timestamppb.New(*e.DeleteTime)
	}

	return pb
}

// EntityToModel converts a domain entity Service to a database model.
func EntityToModel(e *Service) *model.Service {
	if e == nil {
		return nil
	}

	m := &model.Service{
		ID:               e.ID,
		OrganizationType: e.OrganizationType,
		OrganizationID:   e.OrganizationID,
		CareTypeID:       e.CareTypeID,
		Name:             e.Name,
		Description:      e.Description,
		ColorCode:        e.ColorCode,
		Source:           e.Source,
		Status:           e.Status,
		Images:           e.Images,
		Type:             e.Type,
	}

	if e.CategoryID != nil {
		m.CategoryID = *e.CategoryID
	}
	if e.Sort != nil {
		m.Sort = *e.Sort
	}

	return m
}

// ModelToEntity converts a database model Service to a domain entity.
func ModelToEntity(m *model.Service) *Service {
	if m == nil {
		return nil
	}

	e := &Service{
		ID:               m.ID,
		OrganizationType: m.OrganizationType,
		OrganizationID:   m.OrganizationID,
		CareTypeID:       m.CareTypeID,
		CategoryID:       &m.CategoryID,
		Name:             m.Name,
		Description:      m.Description,
		ColorCode:        m.ColorCode,
		Sort:             &m.Sort,
		Images:           m.Images,
		Source:           m.Source,
		Status:           m.Status,
		Type:             m.Type,
		CreateTime:       m.CreateTime,
		UpdateTime:       m.UpdateTime,
		DeleteTime:       m.DeleteTime,
	}

	return e
}

// ProtoToAggregate converts protobuf Service to Aggregate
func ProtoToAggregate(pb *offeringpb.Service) *Aggregate {
	if pb == nil {
		return nil
	}

	agg := NewAggregate()

	agg.Service = ProtoToEntity(pb)
	agg.Attributes = pb.Attributes
	agg.AvailableBusiness = pb.AvailableBusiness

	return agg
}

// AggregateToProto converts Aggregate to protobuf Service
func AggregateToProto(agg *Aggregate) *offeringpb.Service {
	if agg == nil || agg.Service == nil {
		return nil
	}

	pb := EntityToProto(agg.Service)
	pb.Attributes = agg.Attributes
	pb.AvailableBusiness = agg.AvailableBusiness

	return pb
}

// RequestToFilter 构造服务查询的过滤条件
func RequestToFilter(
	req *offeringpb.ListAvailableServicesRequest) *service.ListServiceFilter {
	filter := &service.ListServiceFilter{
		OrganizationType: req.OrganizationType,
		OrganizationID:   req.OrganizationId,
	}
	// 有过滤条件
	if req.Filter != nil {
		filter.CareTypeIDs = req.Filter.CareTypeIds
		filter.Statuses = req.Filter.Statuses
		filter.Keyword = req.Filter.Keyword
	}

	return filter
}

// ModelToProto 转换 model.Service 为 proto.Service
func ModelToProto(m *model.Service) *offeringpb.Service {
	if m == nil {
		return nil
	}

	pb := &offeringpb.Service{
		Id:               m.ID,
		OrganizationType: m.OrganizationType,
		OrganizationId:   m.OrganizationID,
		CareTypeId:       m.CareTypeID,
		Name:             m.Name,
		Description:      m.Description,
		ColorCode:        m.ColorCode,
		Sort:             &m.Sort,
		Images:           m.Images,
		Source:           m.Source,
		Status:           m.Status,
	}

	if m.CategoryID != 0 {
		pb.CategoryId = &m.CategoryID
	}

	if m.CreateTime != nil {
		pb.CreateTime = timestamppb.New(*m.CreateTime)
	}
	if m.UpdateTime != nil {
		pb.UpdateTime = timestamppb.New(*m.UpdateTime)
	}
	if m.DeleteTime != nil {
		pb.DeleteTime = timestamppb.New(*m.DeleteTime)
	}

	return pb
}
