package addon

import (
	"github.com/samber/lo"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/moego/backend/app/offering/logic/association"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// ServiceModelToProto converts Service model directly to AddOn proto.
func ServiceModelToProto(m *model.Service) *offeringpb.AddOn {
	if m == nil {
		return nil
	}

	return &offeringpb.AddOn{
		Id:               m.ID,
		OrganizationType: m.OrganizationType,
		OrganizationId:   m.OrganizationID,
		CategoryId:       &m.CategoryID,
		Name:             m.Name,
		Description:      m.Description,
		ColorCode:        m.ColorCode,
		Sort:             &m.Sort,
		Images:           m.Images,
		Source:           m.Source,
		Status:           offeringpb.AddOn_Status(m.Status),
		IsDeleted:        m.DeleteTime != nil,
		CreateTime:       timestamppb.New(lo.FromPtr(m.CreateTime)),
		UpdateTime:       timestamppb.New(lo.FromPtr(m.UpdateTime)),
		DeleteTime:       timestamppb.New(lo.FromPtr(m.DeleteTime)),
	}
}

// CreateDefToServiceModel converts AddOnCreateDef directly to Service model.
func CreateDefToServiceModel(pb *offeringpb.AddOnCreateDef) *model.Service {
	if pb == nil {
		return nil
	}

	return &model.Service{
		OrganizationType: pb.GetOrganizationType(),
		OrganizationID:   pb.GetOrganizationId(),
		CategoryID:       pb.GetCategoryId(),
		Name:             pb.GetName(),
		Description:      pb.Description,
		ColorCode:        pb.GetColorCode(),
		Images:           pb.GetImages(),
		Source:           pb.GetSource(),
		Status:           offeringpb.Service_Status(pb.GetStatus()),
		Type:             offeringpb.Service_ADD_ON, // 标记为 AddOn 类型
	}
}

// UpdateDefToServiceModel converts AddOnUpdateDef directly to Service model.
func UpdateDefToServiceModel(pb *offeringpb.AddOnUpdateDef) *model.Service {
	if pb == nil {
		return nil
	}

	m := &model.Service{
		ID: pb.Id,
	}

	if pb.CategoryId != nil {
		m.CategoryID = *pb.CategoryId
	}
	if pb.Name != nil {
		m.Name = *pb.Name
	}
	if pb.Description != nil {
		m.Description = pb.Description
	}
	if pb.ColorCode != nil {
		m.ColorCode = *pb.ColorCode
	}
	if pb.Sort != nil {
		m.Sort = *pb.Sort
	}
	if pb.Images != nil {
		m.Images = pb.Images
	}
	if pb.Status != nil {
		m.Status = offeringpb.Service_Status(*pb.Status)
	}

	return m
}

// ProtoToAggregate 从 AddOn 的 ApplicableService 转换为 ServiceAssociation 数组
func ProtoToAggregate(addonID int64, pb *offeringpb.ApplicableService) *association.ServiceAssociationAggregate {
	if pb == nil {
		return nil
	}

	agg := association.NewAggregate(addonID)

	// Case 1: 适用于所有服务，配置 AddOn 到 ServiceType.SERVICE 的关联关系
	if pb.IsAll {
		agg.AddServiceTypeTarget(offeringpb.Service_SERVICE)
	}

	for _, specificService := range pb.SpecificServices {
		// Case 2: 适用于特定护理类型，配置 AddOn 到 CareTypeID 的关联关系
		if specificService.IsAllServices {
			agg.AddCareTypeTarget(*specificService.CareTypeId)
		} else {
			// Case 3: 适用于特定服务，配置 AddOn 到 ServiceID 的关联关系
			for _, serviceID := range specificService.ServiceIds {
				agg.AddServiceIDTarget(serviceID)
			}
		}
	}

	return agg
}

// AggregateToProto 从 ServiceAssociation 数组转换为 AdditionalService
func AggregateToProto(
	agg *association.ServiceAssociationAggregate, careTypeIDByServiceID map[int64]int64) *offeringpb.ApplicableService {
	if agg == nil {
		return &offeringpb.ApplicableService{}
	}

	// Case 1: 处理所有服务关联
	pb := &offeringpb.ApplicableService{
		IsAll:            agg.IsAllServices(),
		SpecificServices: make([]*offeringpb.SpecificService, 0),
	}

	// Case 2: 处理护理类型关联
	for _, careTypeID := range agg.TargetCareTypeIDs {
		pb.SpecificServices = append(pb.SpecificServices, &offeringpb.SpecificService{
			CareTypeId:    &careTypeID,
			IsAllServices: true,
		})
	}

	// Case 3: 处理服务关联，按 careTypeID 分组
	careTypeToServiceIDs := make(map[int64][]int64)
	for _, serviceID := range agg.TargetServiceIDs {
		careTypeID := careTypeIDByServiceID[serviceID]
		careTypeToServiceIDs[careTypeID] = append(careTypeToServiceIDs[careTypeID], serviceID)
	}
	for careTypeID, serviceIDs := range careTypeToServiceIDs {
		pb.SpecificServices = append(pb.SpecificServices, &offeringpb.SpecificService{
			CareTypeId:    &careTypeID,
			IsAllServices: false,
			ServiceIds:    serviceIDs,
		})
	}

	return pb
}
