package addon

import (
	"context"
	"fmt"
	"testing"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/moego/backend/app/offering/logic/association"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/attribute"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/availability"
	mock5 "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/association/mocks"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	dbservice "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service"
	mock3 "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service/mocks"
	mock4 "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/serviceattribute/mocks"
	mock2 "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/servicecategory/mocks"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

// testHelper 测试辅助结构体
type testHelper struct {
	ctrl                  *gomock.Controller
	mockServiceRepo       *mock3.MockRepository
	mockCategoryRepo      *mock2.MockRepository
	mockBasicRepo         *mock4.MockRepository
	mockRolloverRepo      *mock4.MockAutoRollOverRepository
	mockLodgingRepo       *mock4.MockLodgingScopeRepository
	mockStaffRepo         *mock4.MockStaffScopeRepository
	mockBusinessScopeRepo *mock3.MockBusinessScopeRepository
	mockAssociationRepo   *mock5.MockRepository
	logic                 *Logic
	petScopeRepo          *mock3.MockPetScopeRepository
	petWeightRepo         *mock3.MockPetWeightRepository
}

// newTestHelper 创建测试辅助实例
func newTestHelper(t *testing.T) *testHelper {
	ctrl := gomock.NewController(t)

	mockServiceRepo := mock3.NewMockRepository(ctrl)
	mockCategoryRepo := mock2.NewMockRepository(ctrl)
	mockBasicRepo := mock4.NewMockRepository(ctrl)
	mockRolloverRepo := mock4.NewMockAutoRollOverRepository(ctrl)
	mockLodgingRepo := mock4.NewMockLodgingScopeRepository(ctrl)
	mockStaffRepo := mock4.NewMockStaffScopeRepository(ctrl)
	mockBusinessScopeRepo := mock3.NewMockBusinessScopeRepository(ctrl)
	mockAssociationRepo := mock5.NewMockRepository(ctrl)
	mockPetScopeRepo := mock3.NewMockPetScopeRepository(ctrl)
	mockPetWeightRepo := mock3.NewMockPetWeightRepository(ctrl)
	// 创建必要的依赖项
	attributeManager := attribute.NewManagerWithRepositories(
		mockBasicRepo, mockRolloverRepo, mockLodgingRepo, mockStaffRepo)
	businessScopeLogic := service.NewBusinessScopeWithRepository(mockBusinessScopeRepo)
	associationLogic := association.NewLogicWithRepository(mockAssociationRepo)
	availabilityManager := availability.NewManagerWithRepository(mockPetScopeRepo, mockPetWeightRepo)

	logic := &Logic{
		serviceRepo:         mockServiceRepo,
		categoryRepo:        mockCategoryRepo,
		attributeManager:    attributeManager,
		businessScopeLogic:  businessScopeLogic,
		associationLogic:    associationLogic,
		availabilityManager: availabilityManager,
	}

	return &testHelper{
		ctrl:                  ctrl,
		mockServiceRepo:       mockServiceRepo,
		mockCategoryRepo:      mockCategoryRepo,
		mockBasicRepo:         mockBasicRepo,
		mockRolloverRepo:      mockRolloverRepo,
		mockLodgingRepo:       mockLodgingRepo,
		mockStaffRepo:         mockStaffRepo,
		mockBusinessScopeRepo: mockBusinessScopeRepo,
		mockAssociationRepo:   mockAssociationRepo,
		petScopeRepo:          mockPetScopeRepo,
		petWeightRepo:         mockPetWeightRepo,
		logic:                 logic,
	}
}

// finish 清理测试资源
func (h *testHelper) finish() {
	h.ctrl.Finish()
}

func TestNewLogic(t *testing.T) {
	// 跳过这个测试，因为需要真实的数据库连接
	t.Skip("Skipping TestNewLogic due to database dependency")

	logic := NewLogic()
	assert.NotNil(t, logic)
	assert.NotNil(t, logic.serviceRepo)
	assert.NotNil(t, logic.categoryRepo)
}

func TestCreateAddOn_Success(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	createDef := &offeringpb.AddOnCreateDef{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Name:             "Test AddOn",
		Description:      lo.ToPtr("Test Description"),
		ColorCode:        "#FF0000",
		Images:           []string{"image1.jpg", "image2.jpg"},
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.AddOn_ACTIVE,
		IsRequiredStaff:  true,
		Duration:         60,
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll: true,
		},
		ApplicableService: &offeringpb.ApplicableService{
			IsAll: true,
		},
	}

	// Mock expectations
	helper.mockServiceRepo.EXPECT().Create(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, service *model.Service) error {
			service.ID = 1 // 设置 ID
			return nil
		})
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for business scope
	helper.mockBusinessScopeRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return(nil, nil)
	helper.mockBusinessScopeRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for attributes - 需要为所有 processor 设置期望
	helper.mockBasicRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model.ServiceAttribute{}, nil)
	helper.mockBasicRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).Times(2) // 可能被调用两次

	// Mock expectations for association
	helper.mockAssociationRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(nil)
	helper.mockAssociationRepo.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for pet availability
	helper.petScopeRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.petScopeRepo).Times(1)
	helper.petScopeRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(nil)
	helper.petWeightRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.petWeightRepo).Times(1)
	helper.petWeightRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(nil)

	// Execute
	id, err := helper.logic.CreateAddOn(context.Background(), createDef)

	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, int64(1), id)
}

func TestGetAddOn_Success(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	serviceID := int64(123)
	addon := &model.Service{
		ID:               serviceID,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationID:   123,
		Name:             "Test AddOn",
		Type:             offeringpb.Service_ADD_ON,
		Status:           offeringpb.Service_ACTIVE,
	}

	// Mock expectations
	helper.mockServiceRepo.EXPECT().Get(gomock.Any(), serviceID).Return(addon, nil)

	// Mock expectations for attributes - 需要为所有 processor 设置期望
	helper.mockBasicRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{serviceID}).
		Return([]*model.ServiceAttribute{}, nil)
	helper.mockRolloverRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{serviceID}).
		Return([]*model.ServiceAutoRollover{}, nil)
	helper.mockStaffRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{serviceID}).
		Return([]*model.ServiceStaffScope{}, nil)
	helper.mockLodgingRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{serviceID}).
		Return([]*model.ServiceLodgingScope{}, nil)

	// Mock expectations for business scope
	helper.mockBusinessScopeRepo.EXPECT().GetByServiceID(gomock.Any(), serviceID).Return(nil, nil)

	// Mock expectations for association
	helper.mockAssociationRepo.EXPECT().GetByServiceID(gomock.Any(), serviceID).
		Return([]*model.ServiceAssociation{}, nil)

	// Mock expectations for pet availability
	filter := dbservice.ListScopeFilter{ServiceIDs: []int64{serviceID}}
	helper.petScopeRepo.EXPECT().List(gomock.Any(), filter).Return(nil, nil)
	helper.petWeightRepo.EXPECT().GetByServiceID(gomock.Any(), serviceID).Return([]*model.ServicePetWeightRange{}, nil)

	// Execute
	result, err := helper.logic.GetAddOn(context.Background(), serviceID)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, serviceID, result.Id)
	assert.Equal(t, organizationpb.OrganizationType_COMPANY, result.OrganizationType)
	assert.Equal(t, int64(123), result.OrganizationId)
	assert.Equal(t, "Test AddOn", result.Name)
	assert.Equal(t, offeringpb.AddOn_ACTIVE, result.Status)
}

func TestGetAddOn_NotAddOnType(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	serviceID := int64(123)
	service := &model.Service{
		ID:   serviceID,
		Type: offeringpb.Service_SERVICE, // Not ADD_ON type
	}

	// Mock expectations
	helper.mockServiceRepo.EXPECT().Get(gomock.Any(), serviceID).Return(service, nil)

	// Execute
	result, err := helper.logic.GetAddOn(context.Background(), serviceID)

	// Assertions
	assert.Error(t, err)
	assert.Nil(t, result)

	grpcErr, ok := status.FromError(err)
	assert.True(t, ok)
	assert.Equal(t, codes.NotFound, grpcErr.Code())
	assert.Contains(t, grpcErr.Message(), "service is not an addon")
}

func TestDeleteAddOn_Success(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	serviceID := int64(123)

	// Mock expectations for business scope
	helper.mockBusinessScopeRepo.EXPECT().DeleteByServiceID(gomock.Any(), serviceID).Return(int64(1), nil)

	// Mock expectations for association
	helper.mockAssociationRepo.EXPECT().DeleteByServiceID(gomock.Any(), serviceID).Return(nil)

	// Mock expectations for attributes - 需要为所有 processor 设置期望
	helper.mockBasicRepo.EXPECT().DeleteByServiceID(gomock.Any(), serviceID, gomock.Any()).Return(int64(1), nil)
	helper.mockRolloverRepo.EXPECT().DeleteByServiceID(gomock.Any(), serviceID).Return(int64(1), nil)
	helper.mockStaffRepo.EXPECT().DeleteByServiceID(gomock.Any(), serviceID).Return(int64(1), nil)
	helper.mockLodgingRepo.EXPECT().DeleteByServiceID(gomock.Any(), serviceID).Return(int64(1), nil)

	// Mock expectations for pet availability
	helper.petScopeRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.petScopeRepo)
	helper.petScopeRepo.EXPECT().DeleteByServiceID(gomock.Any(), serviceID).Return(nil)
	helper.petWeightRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.petWeightRepo)
	helper.petWeightRepo.EXPECT().DeleteByServiceID(gomock.Any(), serviceID).Return(nil)

	// Mock expectations for service deletion (最后执行)
	helper.mockServiceRepo.EXPECT().Delete(gomock.Any(), serviceID).Return(nil)

	// Execute
	err := helper.logic.DeleteAddOn(context.Background(), serviceID)

	// Assertions
	assert.NoError(t, err)
}

func TestListAddOns_Success(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	req := &offeringpb.ListAddOnsRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	services := []*model.Service{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			Name:             "AddOn 1",
			Type:             offeringpb.Service_ADD_ON,
		},
		{
			ID:               2,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			Name:             "AddOn 2",
			Type:             offeringpb.Service_ADD_ON,
		},
	}

	// Mock expectations for service list
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(services, int64(2), nil)

	// Mock expectations for attributes - 为每个 service 设置期望
	serviceIDs := []int64{1, 2}

	// Mock 底层的 processor 方法，因为 attributeManager 会调用这些方法
	// Basic attributes
	helper.mockBasicRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).
		Return([]*model.ServiceAttribute{
			{
				ServiceID:      1,
				FieldName:      "is_required_staff",
				AttributeKey:   offeringpb.AttributeKey_IS_REQUIRED_STAFF,
				AttributeValue: "true",
			},
			{
				ServiceID:      1,
				FieldName:      "duration",
				AttributeKey:   offeringpb.AttributeKey_DURATION,
				AttributeValue: "60",
			},
			{
				ServiceID:      2,
				FieldName:      "is_required_staff",
				AttributeKey:   offeringpb.AttributeKey_IS_REQUIRED_STAFF,
				AttributeValue: "false",
			},
			{
				ServiceID:      2,
				FieldName:      "duration",
				AttributeKey:   offeringpb.AttributeKey_DURATION,
				AttributeValue: "30",
			},
		}, nil)

	// Auto rollover attributes
	helper.mockRolloverRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).
		Return([]*model.ServiceAutoRollover{}, nil)

	// Staff scope attributes
	helper.mockStaffRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).
		Return([]*model.ServiceStaffScope{}, nil)

	// Lodging scope attributes
	helper.mockLodgingRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).
		Return([]*model.ServiceLodgingScope{}, nil)

	// Mock expectations for business scope - 为每个 service 设置期望
	helper.mockBusinessScopeRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).
		Return([]*model.ServiceBusinessScope{
			{ServiceID: 1, IsAllBusiness: true, AvailableBusinessIds: []int64{}},
			{ServiceID: 2, IsAllBusiness: false, AvailableBusinessIds: []int64{1}},
		}, nil)

	// Mock expectations for association - 为每个 service 设置期望
	helper.mockAssociationRepo.EXPECT().ListByServiceIDs(gomock.Any(), serviceIDs).
		Return([]*model.ServiceAssociation{
			{SourceServiceID: 1, TargetServiceType: offeringpb.Service_SERVICE},
			{SourceServiceID: 2, TargetCareTypeID: 1, TargetServiceID: 10},
			{SourceServiceID: 2, TargetCareTypeID: 1, TargetServiceID: 11},
		}, nil)

	// Execute
	result, err := helper.logic.ListAddOns(context.Background(), req)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result.AddOns, 2)
	assert.Equal(t, int32(2), result.Total)

	// Verify first addon
	assert.Equal(t, int64(1), result.AddOns[0].Id)
	assert.Equal(t, "AddOn 1", result.AddOns[0].Name)
	assert.True(t, result.AddOns[0].IsRequiredStaff)
	assert.Equal(t, int32(60), result.AddOns[0].Duration)
	assert.True(t, result.AddOns[0].AvailableBusiness.IsAll)
	assert.True(t, result.AddOns[0].ApplicableService.IsAll)

	// Verify second addon
	assert.Equal(t, int64(2), result.AddOns[1].Id)
	assert.Equal(t, "AddOn 2", result.AddOns[1].Name)
	assert.False(t, result.AddOns[1].IsRequiredStaff)
	assert.Equal(t, int32(30), result.AddOns[1].Duration)
	assert.False(t, result.AddOns[1].AvailableBusiness.IsAll)
	assert.Len(t, result.AddOns[1].AvailableBusiness.BusinessIds, 1)
	assert.Equal(t, int64(1), result.AddOns[1].AvailableBusiness.BusinessIds[0])
	assert.False(t, result.AddOns[1].ApplicableService.IsAll)
}

func TestBuildListAddOnsFilter(t *testing.T) {
	logic := &Logic{}

	// Test case 1: Basic filter
	req := &offeringpb.ListAddOnsRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
	}

	filter := logic.buildListAddOnsFilter(req)
	assert.Equal(t, organizationpb.OrganizationType_COMPANY, filter.OrganizationType)
	assert.Equal(t, int64(123), filter.OrganizationID)
	assert.Len(t, filter.Types, 1)
	assert.Equal(t, offeringpb.Service_ADD_ON, filter.Types[0])

	// Test case 2: With filter
	reqWithFilter := &offeringpb.ListAddOnsRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Filter: &offeringpb.ListAddOnsRequest_Filter{
			Statuses: []offeringpb.AddOn_Status{offeringpb.AddOn_ACTIVE},
			Sources:  []offeringpb.OfferingSource{offeringpb.OfferingSource_MOEGO},
			Keyword:  lo.ToPtr("test"),
		},
	}

	filterWithFilter := logic.buildListAddOnsFilter(reqWithFilter)
	assert.Equal(t, organizationpb.OrganizationType_COMPANY, filterWithFilter.OrganizationType)
	assert.Equal(t, int64(123), filterWithFilter.OrganizationID)
	assert.Len(t, filterWithFilter.Types, 1)
	assert.Equal(t, offeringpb.Service_ADD_ON, filterWithFilter.Types[0])
	assert.Len(t, filterWithFilter.Statuses, 1)
	assert.Equal(t, offeringpb.Service_ACTIVE, filterWithFilter.Statuses[0])
	assert.Len(t, filterWithFilter.Sources, 1)
	assert.Equal(t, offeringpb.OfferingSource_MOEGO, filterWithFilter.Sources[0])
	assert.Equal(t, "test", *filterWithFilter.Keyword)
}

func TestGetPaginationParams(t *testing.T) {
	logic := &Logic{}

	// Test case 1: With pagination
	req := &offeringpb.ListAddOnsRequest{
		Pagination: &offeringpb.PaginationRef{
			Offset: 10,
			Limit:  20,
		},
	}

	pagination := logic.getPaginationParams(req)
	assert.Equal(t, int32(10), pagination.Offset)
	assert.Equal(t, int32(20), pagination.Limit)

	// Test case 2: Without pagination (should return default)
	reqNoPagination := &offeringpb.ListAddOnsRequest{}

	paginationDefault := logic.getPaginationParams(reqNoPagination)
	assert.Equal(t, int32(0), paginationDefault.Offset)
	assert.Equal(t, int32(200), paginationDefault.Limit)
}

func TestUpdateAddOn_Success(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data - 只包含基本字段，不包含复杂的属性、业务范围和关联服务
	updateDef := &offeringpb.AddOnUpdateDef{
		Id:   123,
		Name: lo.ToPtr("Updated AddOn"),
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll: true,
		},
	}

	// Mock expectations for service update only
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for business scope (因为 AvailableBusiness 不为 nil)
	helper.mockBusinessScopeRepo.EXPECT().GetByServiceID(gomock.Any(), int64(123)).Return(nil, nil)
	helper.mockBusinessScopeRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for pet availability (Update 方法总是会调用 availabilityManager.Update)
	helper.petScopeRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.petScopeRepo)
	helper.petScopeRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(123)).Return(nil)
	helper.petWeightRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.petWeightRepo)
	helper.petWeightRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(123)).Return(nil)

	// Execute
	err := helper.logic.UpdateAddOn(context.Background(), updateDef)

	// Assertions
	assert.NoError(t, err)
}

func TestBatchUpdateAddOns_Success(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	req := &offeringpb.BatchUpdateAddOnsRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		UpdateAddOns: []*offeringpb.AddOnUpdateDef{
			{
				Id:     1,
				Status: lo.ToPtr(offeringpb.AddOn_INACTIVE),
			},
			{
				Id:     2,
				Status: lo.ToPtr(offeringpb.AddOn_ACTIVE),
			},
		},
	}

	// Mock expectations for service list (获取需要更新的 addon 列表)
	services := []*model.Service{
		{ID: 1, Type: offeringpb.Service_ADD_ON},
		{ID: 2, Type: offeringpb.Service_ADD_ON},
	}
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(services, int64(2), nil)

	// Mock expectations for service updates
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil).Times(2)

	// Execute
	_, err := helper.logic.BatchUpdateAddOns(context.Background(), req)

	// Assertions
	assert.NoError(t, err)
}

func TestBatchUpdateAddOns_EmptyUpdates(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data - 空的更新列表
	req := &offeringpb.BatchUpdateAddOnsRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		UpdateAddOns:     []*offeringpb.AddOnUpdateDef{},
	}

	// Mock expectations for service list (空的更新列表不需要查询)
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).
		Return([]*model.Service{}, int64(0), nil)

	// Execute
	_, err := helper.logic.BatchUpdateAddOns(context.Background(), req)

	// Assertions
	assert.NoError(t, err)
}

func TestBatchUpdateAddOns_UpdateError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	req := &offeringpb.BatchUpdateAddOnsRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		UpdateAddOns: []*offeringpb.AddOnUpdateDef{
			{
				Id:     1,
				Status: lo.ToPtr(offeringpb.AddOn_INACTIVE),
			},
		},
	}

	// Mock expectations for service list (获取需要更新的 addon 列表)
	services := []*model.Service{
		{ID: 1, Type: offeringpb.Service_ADD_ON},
	}
	helper.mockServiceRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(services, int64(1), nil)

	// Mock expectations for service update - 模拟更新失败
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(fmt.Errorf("update failed"))

	// Execute
	_, err := helper.logic.BatchUpdateAddOns(context.Background(), req)

	// Assertions
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "update failed")
}

func TestCreateAddOn_ServiceCreateError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	createDef := &offeringpb.AddOnCreateDef{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Name:             "Test AddOn",
		ColorCode:        "#FF0000",
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.AddOn_ACTIVE,
	}

	// Mock expectations - 模拟服务创建失败
	helper.mockServiceRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(fmt.Errorf("service create failed"))

	// Execute
	id, err := helper.logic.CreateAddOn(context.Background(), createDef)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, int64(0), id)
	assert.Contains(t, err.Error(), "service create failed")
}

func TestCreateAddOn_AttributesError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data - 只包含基本字段，不包含复杂的属性、业务范围和关联服务
	createDef := &offeringpb.AddOnCreateDef{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Name:             "Test AddOn",
		ColorCode:        "#FF0000",
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.AddOn_ACTIVE,
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll: true,
		},
	}

	// Mock expectations for service create
	helper.mockServiceRepo.EXPECT().Create(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, service *model.Service) error {
			service.ID = 1 // 设置 ID
			return nil
		})

	// Mock expectations for service update (更新 sort 字段)
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for attributes (因为 saveAddOnAttributes 总是被调用)
	helper.mockBasicRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model.ServiceAttribute{}, nil)
	helper.mockBasicRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(fmt.Errorf("attribute create failed"))

	// Execute
	id, err := helper.logic.CreateAddOn(context.Background(), createDef)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, int64(0), id)
	assert.Contains(t, err.Error(), "attribute create failed")
}

func TestGetAddOn_ServiceNotFound(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	serviceID := int64(123)

	// Mock expectations - 模拟服务不存在
	helper.mockServiceRepo.EXPECT().Get(gomock.Any(), serviceID).Return(nil, fmt.Errorf("service not found"))

	// Execute
	result, err := helper.logic.GetAddOn(context.Background(), serviceID)

	// Assertions
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "service not found")
}

func TestBuildListAddOnsFilter_WithAllFilters(t *testing.T) {
	logic := &Logic{}

	// Test case: 包含所有过滤条件
	req := &offeringpb.ListAddOnsRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Filter: &offeringpb.ListAddOnsRequest_Filter{
			Statuses: []offeringpb.AddOn_Status{offeringpb.AddOn_ACTIVE, offeringpb.AddOn_INACTIVE},
			Sources: []offeringpb.OfferingSource{
				offeringpb.OfferingSource_MOEGO, offeringpb.OfferingSource_ENTERPRISE},
			Keyword:     lo.ToPtr("test keyword"),
			CategoryIds: []int64{1, 2, 3},
		},
	}

	filter := logic.buildListAddOnsFilter(req)
	assert.Equal(t, organizationpb.OrganizationType_COMPANY, filter.OrganizationType)
	assert.Equal(t, int64(123), filter.OrganizationID)
	assert.Len(t, filter.Types, 1)
	assert.Equal(t, offeringpb.Service_ADD_ON, filter.Types[0])
	assert.Len(t, filter.Statuses, 2)
	assert.Len(t, filter.Sources, 2)
	assert.Equal(t, "test keyword", *filter.Keyword)
	// CategoriesIDs 字段目前没有被实现，所以不进行断言
}

// TestCreateAddOn_WithPetAvailability 测试创建 AddOn 时包含 Pet availability 配置
func TestCreateAddOn_WithPetAvailability(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data with Pet availability
	createDef := &offeringpb.AddOnCreateDef{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Name:             "Test AddOn with Pet Availability",
		Description:      lo.ToPtr("Test Description"),
		ColorCode:        "#FF0000",
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.AddOn_ACTIVE,
		IsRequiredStaff:  true,
		Duration:         60,
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll: true,
		},
		ApplicableService: &offeringpb.ApplicableService{
			IsAll: true,
		},
		AvailableTypeBreed: &offeringpb.AvailablePetTypeBreed{
			IsAll: false,
			AvailablePetTypes: []*offeringpb.AvailablePetType{
				{
					PetTypeId: 1,
					IsAll:     true,
					BreedIds:  []int64{},
				},
			},
		},
		AvailablePetSize: &offeringpb.AvailablePetSize{
			IsAll:      false,
			PetSizeIds: []int64{1, 2},
		},
		AvailableCoatType: &offeringpb.AvailableCoatType{
			IsAll:       false,
			CoatTypeIds: []int64{1},
		},
	}

	// Mock expectations
	helper.mockServiceRepo.EXPECT().Create(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, service *model.Service) error {
			service.ID = 1 // 设置 ID
			return nil
		})
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for business scope
	helper.mockBusinessScopeRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return(nil, nil)
	helper.mockBusinessScopeRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for attributes
	helper.mockBasicRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model.ServiceAttribute{}, nil)
	helper.mockBasicRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).Times(2)

	// Mock expectations for association
	helper.mockAssociationRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(nil)
	helper.mockAssociationRepo.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for pet availability
	helper.petScopeRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.petScopeRepo).Times(2)
	helper.petScopeRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(nil)
	helper.petScopeRepo.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil)
	helper.petWeightRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.petWeightRepo).Times(1)
	helper.petWeightRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(nil)

	// Execute
	id, err := helper.logic.CreateAddOn(context.Background(), createDef)

	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, int64(1), id)
}

// TestCreateAddOn_WithApplicableService 测试创建 AddOn 时包含 ApplicableService 配置
func TestCreateAddOn_WithApplicableService(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data with ApplicableService
	createDef := &offeringpb.AddOnCreateDef{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Name:             "Test AddOn with ApplicableService",
		ColorCode:        "#FF0000",
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.AddOn_ACTIVE,
		IsRequiredStaff:  false,
		Duration:         30,
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll: true,
		},
		ApplicableService: &offeringpb.ApplicableService{
			IsAll: false,
			SpecificServices: []*offeringpb.SpecificService{
				{
					CareTypeId:    lo.ToPtr(int64(1)),
					IsAllServices: true,
				},
			},
		},
	}

	// Mock expectations
	helper.mockServiceRepo.EXPECT().Create(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, service *model.Service) error {
			service.ID = 1
			return nil
		})
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for business scope
	helper.mockBusinessScopeRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return(nil, nil)
	helper.mockBusinessScopeRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for attributes
	helper.mockBasicRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model.ServiceAttribute{}, nil)
	helper.mockBasicRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).Times(2)

	// Mock expectations for association
	helper.mockAssociationRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(nil)
	helper.mockAssociationRepo.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for pet availability
	helper.petScopeRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.petScopeRepo).Times(1)
	helper.petScopeRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(nil)
	helper.petWeightRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.petWeightRepo).Times(1)
	helper.petWeightRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(nil)

	// Execute
	id, err := helper.logic.CreateAddOn(context.Background(), createDef)

	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, int64(1), id)
}

// TestUpdateAddOn_WithPetAvailability 测试更新 AddOn 时包含 Pet availability 配置
func TestUpdateAddOn_WithPetAvailability(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data with Pet availability
	updateDef := &offeringpb.AddOnUpdateDef{
		Id:   123,
		Name: lo.ToPtr("Updated AddOn with Pet Availability"),
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll:       false,
			BusinessIds: []int64{1, 2},
		},
		AvailableTypeBreed: &offeringpb.AvailablePetTypeBreed{
			IsAll: true,
		},
		AvailablePetSize: &offeringpb.AvailablePetSize{
			IsAll:      false,
			PetSizeIds: []int64{1, 2, 3},
		},
		AvailableCoatType: &offeringpb.AvailableCoatType{
			IsAll:       false,
			CoatTypeIds: []int64{1, 2},
		},
	}

	// Mock expectations for service update
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for business scope
	helper.mockBusinessScopeRepo.EXPECT().GetByServiceID(gomock.Any(), int64(123)).Return(nil, nil)
	helper.mockBusinessScopeRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for pet availability (Update 方法总是会调用 availabilityManager.Update)
	// Update 方法会先删除现有记录，然后调用 Save 方法保存新记录
	// 所以 DeleteByServiceID 会被调用两次：一次在 Update 中，一次在 Save 中
	helper.petScopeRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.petScopeRepo).AnyTimes()
	helper.petScopeRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(123)).Return(nil).Times(1)
	helper.petScopeRepo.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil)
	helper.petWeightRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.petWeightRepo).AnyTimes()
	helper.petWeightRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(123)).Return(nil).Times(1)

	// Execute
	err := helper.logic.UpdateAddOn(context.Background(), updateDef)

	// Assertions
	assert.NoError(t, err)
}

// TestGetAddOn_WithPetAvailability 测试获取 AddOn 时包含 Pet availability 配置
func TestGetAddOn_WithPetAvailability(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	serviceID := int64(123)
	addon := &model.Service{
		ID:               serviceID,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationID:   123,
		Name:             "Test AddOn with Pet Availability",
		Type:             offeringpb.Service_ADD_ON,
		Status:           offeringpb.Service_ACTIVE,
	}

	// Mock expectations
	helper.mockServiceRepo.EXPECT().Get(gomock.Any(), serviceID).Return(addon, nil)

	// Mock expectations for attributes
	helper.mockBasicRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{serviceID}).
		Return([]*model.ServiceAttribute{
			{
				ServiceID:      serviceID,
				FieldName:      "is_required_staff",
				AttributeKey:   offeringpb.AttributeKey_IS_REQUIRED_STAFF,
				AttributeValue: "true",
			},
			{
				ServiceID:      serviceID,
				FieldName:      "duration",
				AttributeKey:   offeringpb.AttributeKey_DURATION,
				AttributeValue: "60",
			},
		}, nil)
	helper.mockRolloverRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{serviceID}).
		Return([]*model.ServiceAutoRollover{}, nil)
	helper.mockStaffRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{serviceID}).
		Return([]*model.ServiceStaffScope{}, nil)
	helper.mockLodgingRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{serviceID}).
		Return([]*model.ServiceLodgingScope{}, nil)

	// Mock expectations for business scope
	helper.mockBusinessScopeRepo.EXPECT().GetByServiceID(gomock.Any(), serviceID).Return(nil, nil)

	// Mock expectations for association
	helper.mockAssociationRepo.EXPECT().GetByServiceID(gomock.Any(), serviceID).
		Return([]*model.ServiceAssociation{}, nil)

	// Mock expectations for pet availability
	filter := dbservice.ListScopeFilter{ServiceIDs: []int64{serviceID}}
	helper.petScopeRepo.EXPECT().List(gomock.Any(), filter).Return([]*model.ServicePetAvailabilityScope{
		{
			ServiceID: serviceID,
			ScopeType: offeringpb.PetAvailabilityScopeType_SPECIFIC_TYPE,
			TargetID:  1,
		},
		{
			ServiceID: serviceID,
			ScopeType: offeringpb.PetAvailabilityScopeType_SPECIFIC_SIZE,
			TargetID:  1,
		},
	}, nil)
	helper.petWeightRepo.EXPECT().GetByServiceID(gomock.Any(), serviceID).Return([]*model.ServicePetWeightRange{
		{
			ServiceID:  serviceID,
			IsAllRange: true,
		},
	}, nil)

	// Execute
	result, err := helper.logic.GetAddOn(context.Background(), serviceID)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, serviceID, result.Id)
	assert.Equal(t, "Test AddOn with Pet Availability", result.Name)
	assert.True(t, result.IsRequiredStaff)
	assert.Equal(t, int32(60), result.Duration)
	assert.NotNil(t, result.AvailableTypeBreed)
	assert.NotNil(t, result.AvailablePetSize)
	assert.NotNil(t, result.AvailableCoatType)
}

// TestCreateAddOn_BusinessScopeError 测试创建 AddOn 时 business scope 错误
func TestCreateAddOn_BusinessScopeError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	createDef := &offeringpb.AddOnCreateDef{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Name:             "Test AddOn",
		ColorCode:        "#FF0000",
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.AddOn_ACTIVE,
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll: true,
		},
	}

	// Mock expectations for service create
	helper.mockServiceRepo.EXPECT().Create(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, service *model.Service) error {
			service.ID = 1
			return nil
		})

	// Mock expectations for service update
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for attributes
	helper.mockBasicRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model.ServiceAttribute{}, nil)
	helper.mockBasicRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).Times(2)

	// Mock expectations for business scope - 模拟错误
	helper.mockBusinessScopeRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return(nil, fmt.Errorf("business scope error"))

	// Execute
	id, err := helper.logic.CreateAddOn(context.Background(), createDef)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, int64(0), id)
	assert.Contains(t, err.Error(), "business scope error")
}

// TestCreateAddOn_ApplicableServiceError 测试创建 AddOn 时 applicable service 错误
func TestCreateAddOn_ApplicableServiceError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	createDef := &offeringpb.AddOnCreateDef{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Name:             "Test AddOn",
		ColorCode:        "#FF0000",
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.AddOn_ACTIVE,
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll: true,
		},
		ApplicableService: &offeringpb.ApplicableService{
			IsAll: false,
			SpecificServices: []*offeringpb.SpecificService{
				{
					CareTypeId:    lo.ToPtr(int64(1)),
					IsAllServices: true,
				},
			},
		},
	}

	// Mock expectations for service create
	helper.mockServiceRepo.EXPECT().Create(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, service *model.Service) error {
			service.ID = 1
			return nil
		})

	// Mock expectations for service update
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for attributes
	helper.mockBasicRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model.ServiceAttribute{}, nil)
	helper.mockBasicRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).Times(2)

	// Mock expectations for business scope
	helper.mockBusinessScopeRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return(nil, nil)
	helper.mockBusinessScopeRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for association - 模拟错误
	helper.mockAssociationRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(fmt.Errorf("association error"))

	// Execute
	id, err := helper.logic.CreateAddOn(context.Background(), createDef)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, int64(0), id)
	assert.Contains(t, err.Error(), "association error")
}

// TestCreateAddOn_PetAvailabilityError 测试创建 AddOn 时 pet availability 错误
func TestCreateAddOn_PetAvailabilityError(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	createDef := &offeringpb.AddOnCreateDef{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Name:             "Test AddOn",
		ColorCode:        "#FF0000",
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.AddOn_ACTIVE,
		AvailableBusiness: &offeringpb.AvailableBusiness{
			IsAll: true,
		},
		AvailableTypeBreed: &offeringpb.AvailablePetTypeBreed{
			IsAll: true,
		},
	}

	// Mock expectations for service create
	helper.mockServiceRepo.EXPECT().Create(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, service *model.Service) error {
			service.ID = 1
			return nil
		})

	// Mock expectations for service update
	helper.mockServiceRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for attributes
	helper.mockBasicRepo.EXPECT().ListByServiceID(gomock.Any(), int64(1)).Return([]*model.ServiceAttribute{}, nil)
	helper.mockBasicRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).Times(2)

	// Mock expectations for business scope
	helper.mockBusinessScopeRepo.EXPECT().GetByServiceID(gomock.Any(), int64(1)).Return(nil, nil)
	helper.mockBusinessScopeRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for pet availability - 模拟错误
	helper.petScopeRepo.EXPECT().WithQuery(gomock.Any()).Return(helper.petScopeRepo).Times(2)
	helper.petScopeRepo.EXPECT().DeleteByServiceID(gomock.Any(), int64(1)).Return(nil)
	helper.petScopeRepo.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(fmt.Errorf("pet availability error"))

	// Execute
	id, err := helper.logic.CreateAddOn(context.Background(), createDef)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, int64(0), id)
	assert.Contains(t, err.Error(), "failed to batch create pet availability scopes")
}

// TestGetAddOn_WithApplicableService 测试获取 AddOn 时包含 ApplicableService 配置
func TestGetAddOn_WithApplicableService(t *testing.T) {
	helper := newTestHelper(t)
	defer helper.finish()

	// Test data
	serviceID := int64(123)
	addon := &model.Service{
		ID:               serviceID,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationID:   123,
		Name:             "Test AddOn with ApplicableService",
		Type:             offeringpb.Service_ADD_ON,
		Status:           offeringpb.Service_ACTIVE,
	}

	// Mock expectations
	helper.mockServiceRepo.EXPECT().Get(gomock.Any(), serviceID).Return(addon, nil)

	// Mock expectations for attributes
	helper.mockBasicRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{serviceID}).
		Return([]*model.ServiceAttribute{}, nil)
	helper.mockRolloverRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{serviceID}).
		Return([]*model.ServiceAutoRollover{}, nil)
	helper.mockStaffRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{serviceID}).
		Return([]*model.ServiceStaffScope{}, nil)
	helper.mockLodgingRepo.EXPECT().ListByServiceIDs(gomock.Any(), []int64{serviceID}).
		Return([]*model.ServiceLodgingScope{}, nil)

	// Mock expectations for business scope
	helper.mockBusinessScopeRepo.EXPECT().GetByServiceID(gomock.Any(), serviceID).Return(nil, nil)

	// Mock expectations for association
	helper.mockAssociationRepo.EXPECT().GetByServiceID(gomock.Any(), serviceID).
		Return([]*model.ServiceAssociation{
			{
				SourceServiceID:  serviceID,
				TargetCareTypeID: 1,
				TargetServiceID:  10,
			},
			{
				SourceServiceID:  serviceID,
				TargetCareTypeID: 2,
				TargetServiceID:  20,
			},
		}, nil)

	// Mock expectations for pet availability
	filter := dbservice.ListScopeFilter{ServiceIDs: []int64{serviceID}}
	helper.petScopeRepo.EXPECT().List(gomock.Any(), filter).Return([]*model.ServicePetAvailabilityScope{}, nil)
	helper.petWeightRepo.EXPECT().GetByServiceID(gomock.Any(), serviceID).Return([]*model.ServicePetWeightRange{}, nil)

	// Execute
	result, err := helper.logic.GetAddOn(context.Background(), serviceID)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, serviceID, result.Id)
	assert.Equal(t, "Test AddOn with ApplicableService", result.Name)
	assert.NotNil(t, result.ApplicableService)
}

// TestGetPaginationParams_EdgeCases 测试分页参数的边界情况
func TestGetPaginationParams_EdgeCases(t *testing.T) {
	logic := &Logic{}

	// Test case 1: 零值分页
	req1 := &offeringpb.ListAddOnsRequest{
		Pagination: &offeringpb.PaginationRef{
			Offset: 0,
			Limit:  0,
		},
	}

	pagination1 := logic.getPaginationParams(req1)
	assert.Equal(t, int32(0), pagination1.Offset)
	assert.Equal(t, int32(0), pagination1.Limit)

	// Test case 2: 大值分页
	req2 := &offeringpb.ListAddOnsRequest{
		Pagination: &offeringpb.PaginationRef{
			Offset: 1000,
			Limit:  1000,
		},
	}

	pagination2 := logic.getPaginationParams(req2)
	assert.Equal(t, int32(1000), pagination2.Offset)
	assert.Equal(t, int32(1000), pagination2.Limit)

	// Test case 3: 负数分页（应该被处理为默认值）
	req3 := &offeringpb.ListAddOnsRequest{
		Pagination: &offeringpb.PaginationRef{
			Offset: -10,
			Limit:  -5,
		},
	}

	pagination3 := logic.getPaginationParams(req3)
	assert.Equal(t, int32(-10), pagination3.Offset) // 注意：这里没有验证逻辑，只是测试当前行为
	assert.Equal(t, int32(-5), pagination3.Limit)
}
