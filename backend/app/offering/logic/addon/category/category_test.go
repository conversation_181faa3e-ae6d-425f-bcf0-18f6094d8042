package category

import (
	"context"
	"database/sql"
	"fmt"
	"testing"
	"time"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	mock "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/servicecategory/mocks"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

// MockQuery 是一个简单的 mock 实现
type MockQuery struct {
	transactionFunc func(fc func(tx *query.Query) error, opts ...*sql.TxOptions) error
}

func (mq *MockQuery) Transaction(fc func(tx *query.Query) error, opts ...*sql.TxOptions) error {
	return mq.transactionFunc(fc, opts...)
}

func TestListAddOnCategories_Success(t *testing.T) {
	// Setup mocks
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)

	logic := &Logic{
		categoryRepo: mockRepo,
	}

	// Test data
	req := &offeringpb.ListAddOnCategoriesRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Filter: &offeringpb.ListAddOnCategoriesRequest_Filter{
			CategoryIds: []int64{1, 2, 3},
		},
	}

	categories := []*model.ServiceCategory{
		{
			ID:               1,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			Name:             "Category 1",
			Sort:             100,
			Type:             offeringpb.Service_ADD_ON,
			CreateTime:       lo.ToPtr(time.Now()),
			UpdateTime:       lo.ToPtr(time.Now()),
		},
		{
			ID:               2,
			OrganizationType: organizationpb.OrganizationType_COMPANY,
			OrganizationID:   123,
			Name:             "Category 2",
			Sort:             200,
			Type:             offeringpb.Service_ADD_ON,
			CreateTime:       lo.ToPtr(time.Now()),
			UpdateTime:       lo.ToPtr(time.Now()),
		},
	}

	// Mock expectations
	mockRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(categories, int64(2), nil)

	// Execute
	result, err := logic.ListAddOnCategories(context.Background(), req)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result.Categories, 2)
	assert.Equal(t, int64(1), result.Categories[0].Id)
	assert.Equal(t, "Category 1", result.Categories[0].Name)
	assert.Equal(t, int64(2), result.Categories[1].Id)
	assert.Equal(t, "Category 2", result.Categories[1].Name)
}

func TestListAddOnCategories_EmptyResult(t *testing.T) {
	// Setup mocks
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)

	logic := &Logic{
		categoryRepo: mockRepo,
	}

	// Test data
	req := &offeringpb.ListAddOnCategoriesRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
	}

	// Mock expectations
	mockRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*model.ServiceCategory{}, int64(0), nil)

	// Execute
	result, err := logic.ListAddOnCategories(context.Background(), req)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Empty(t, result.Categories)
}

func TestListAddOnCategories_FilterByIDs(t *testing.T) {
	// Setup mocks
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)

	logic := &Logic{
		categoryRepo: mockRepo,
	}

	// Test data
	req := &offeringpb.ListAddOnCategoriesRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Filter: &offeringpb.ListAddOnCategoriesRequest_Filter{
			CategoryIds: []int64{1, 2},
		},
	}

	// Mock expectations
	mockRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*model.ServiceCategory{}, int64(0), nil)

	// Execute
	result, err := logic.ListAddOnCategories(context.Background(), req)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// Verify that the filter was applied correctly
}

func TestBuildNewAddOnCategoryModel(t *testing.T) {

	// Test data
	req := &offeringpb.SaveAddOnCategoriesRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
	}

	category := &offeringpb.SaveAddOnCategoriesRequest_CategoryCreateDef{
		Name: "New Category",
		Sort: 100,
	}

	// Execute
	result := (&Logic{}).buildNewAddOnCategoryModel(req, category, 150)

	// Assertions
	assert.NotNil(t, result)
	assert.Equal(t, organizationpb.OrganizationType_COMPANY, result.OrganizationType)
	assert.Equal(t, int64(123), result.OrganizationID)
	assert.Equal(t, "New Category", result.Name)
	assert.Equal(t, int32(150), result.Sort)
	assert.Equal(t, offeringpb.Service_ADD_ON, result.Type)
}

func TestSaveAddOnCategories_Success(t *testing.T) {
	// Setup mocks
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)

	// 创建 mock query
	mockQuery := &MockQuery{
		transactionFunc: func(fc func(tx *query.Query) error, opts ...*sql.TxOptions) error {
			// 模拟事务执行，直接调用传入的函数
			return fc(nil)
		},
	}

	logic := &Logic{
		query:        mockQuery,
		categoryRepo: mockRepo,
	}

	// Test data
	req := &offeringpb.SaveAddOnCategoriesRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		CreateCategories: []*offeringpb.SaveAddOnCategoriesRequest_CategoryCreateDef{
			{
				Name: "New Category 1",
				Sort: 100,
			},
			{
				Name: "New Category 2",
				Sort: 200,
			},
		},
		UpdateCategories: []*offeringpb.SaveAddOnCategoriesRequest_CategoryUpdateDef{
			{
				Id:   1,
				Name: "Updated Category 1",
				Sort: 150,
			},
		},
		DeleteIds: []int64{3, 4},
	}

	// Mock expectations for WithQuery calls - called once at the start of transaction
	mockRepo.EXPECT().WithQuery(gomock.Any()).Return(mockRepo).Times(1)

	// Mock expectations for delete
	mockRepo.EXPECT().Delete(gomock.Any(), gomock.Any()).Return(nil).Times(2)

	// Mock expectations for update
	mockRepo.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

	// Mock expectations for create
	mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).Times(2)

	// Execute
	result, err := logic.SaveAddOnCategories(context.Background(), req)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
}

func TestSaveAddOnCategories_DeleteOnly(t *testing.T) {
	// Setup mocks
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)

	// 创建 mock query
	mockQuery := &MockQuery{
		transactionFunc: func(fc func(tx *query.Query) error, opts ...*sql.TxOptions) error {
			// 模拟事务执行，直接调用传入的函数
			return fc(nil)
		},
	}

	logic := &Logic{
		query:        mockQuery,
		categoryRepo: mockRepo,
	}

	// Test data
	req := &offeringpb.SaveAddOnCategoriesRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		DeleteIds:        []int64{1, 2},
	}

	// Mock expectations for WithQuery calls
	mockRepo.EXPECT().WithQuery(gomock.Any()).Return(mockRepo).Times(1)

	// Mock expectations for delete
	mockRepo.EXPECT().Delete(gomock.Any(), gomock.Any()).Return(nil).Times(2)

	// Execute
	result, err := logic.SaveAddOnCategories(context.Background(), req)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
}

func TestSaveAddOnCategories_CreateOnly(t *testing.T) {
	// Setup mocks
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)

	// 创建 mock query
	mockQuery := &MockQuery{
		transactionFunc: func(fc func(tx *query.Query) error, opts ...*sql.TxOptions) error {
			// 模拟事务执行，直接调用传入的函数
			return fc(nil)
		},
	}

	logic := &Logic{
		query:        mockQuery,
		categoryRepo: mockRepo,
	}

	// Test data
	req := &offeringpb.SaveAddOnCategoriesRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		CreateCategories: []*offeringpb.SaveAddOnCategoriesRequest_CategoryCreateDef{
			{
				Name: "New Category",
				Sort: 100,
			},
		},
	}

	// Mock expectations for WithQuery calls
	mockRepo.EXPECT().WithQuery(gomock.Any()).Return(mockRepo).Times(1)

	// Mock expectations for create
	mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)

	// Execute
	result, err := logic.SaveAddOnCategories(context.Background(), req)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
}

func TestSaveAddOnCategories_UpdateOnly(t *testing.T) {
	// Setup mocks
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)

	// 创建 mock query
	mockQuery := &MockQuery{
		transactionFunc: func(fc func(tx *query.Query) error, opts ...*sql.TxOptions) error {
			// 模拟事务执行，直接调用传入的函数
			return fc(nil)
		},
	}

	logic := &Logic{
		query:        mockQuery,
		categoryRepo: mockRepo,
	}

	// Test data
	req := &offeringpb.SaveAddOnCategoriesRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		UpdateCategories: []*offeringpb.SaveAddOnCategoriesRequest_CategoryUpdateDef{
			{
				Id:   1,
				Name: "Updated Category",
				Sort: 150,
			},
		},
	}

	// Mock expectations for WithQuery calls
	mockRepo.EXPECT().WithQuery(gomock.Any()).Return(mockRepo).Times(1)

	// Mock expectations for update
	mockRepo.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

	// Execute
	result, err := logic.SaveAddOnCategories(context.Background(), req)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
}

func TestSaveAddOnCategories_TransactionError(t *testing.T) {
	// Setup mocks
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)

	// 创建 mock query，模拟事务失败
	mockQuery := &MockQuery{
		transactionFunc: func(fc func(tx *query.Query) error, opts ...*sql.TxOptions) error {
			// 模拟事务失败
			return fmt.Errorf("transaction failed")
		},
	}

	logic := &Logic{
		query:        mockQuery,
		categoryRepo: mockRepo,
	}

	// Test data
	req := &offeringpb.SaveAddOnCategoriesRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		CreateCategories: []*offeringpb.SaveAddOnCategoriesRequest_CategoryCreateDef{
			{
				Name: "New Category",
				Sort: 100,
			},
		},
	}

	// Execute
	result, err := logic.SaveAddOnCategories(context.Background(), req)

	// Assertions
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "transaction failed")
}

func TestSaveAddOnCategories_DeleteError(t *testing.T) {
	// Setup mocks
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)

	// 创建 mock query
	mockQuery := &MockQuery{
		transactionFunc: func(fc func(tx *query.Query) error, opts ...*sql.TxOptions) error {
			// 模拟事务执行，直接调用传入的函数
			return fc(nil)
		},
	}

	logic := &Logic{
		query:        mockQuery,
		categoryRepo: mockRepo,
	}

	// Test data
	req := &offeringpb.SaveAddOnCategoriesRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		DeleteIds:        []int64{1},
	}

	// Mock expectations for WithQuery calls
	mockRepo.EXPECT().WithQuery(gomock.Any()).Return(mockRepo).Times(1)

	// Mock expectations for delete - 模拟删除失败
	mockRepo.EXPECT().Delete(gomock.Any(), gomock.Any()).Return(fmt.Errorf("delete failed"))

	// Execute
	result, err := logic.SaveAddOnCategories(context.Background(), req)

	// Assertions
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "failed to delete categories")
}

func TestSaveAddOnCategories_CreateError(t *testing.T) {
	// Setup mocks
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)

	// 创建 mock query
	mockQuery := &MockQuery{
		transactionFunc: func(fc func(tx *query.Query) error, opts ...*sql.TxOptions) error {
			// 模拟事务执行，直接调用传入的函数
			return fc(nil)
		},
	}

	logic := &Logic{
		query:        mockQuery,
		categoryRepo: mockRepo,
	}

	// Test data
	req := &offeringpb.SaveAddOnCategoriesRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		CreateCategories: []*offeringpb.SaveAddOnCategoriesRequest_CategoryCreateDef{
			{
				Name: "New Category",
				Sort: 100,
			},
		},
	}

	// Mock expectations for WithQuery calls
	mockRepo.EXPECT().WithQuery(gomock.Any()).Return(mockRepo).Times(1)

	// Mock expectations for create - 模拟创建失败
	mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(fmt.Errorf("create failed"))

	// Execute
	result, err := logic.SaveAddOnCategories(context.Background(), req)

	// Assertions
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "failed to create categories")
}

func TestSaveAddOnCategories_UpdateError(t *testing.T) {
	// Setup mocks
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)

	// 创建 mock query
	mockQuery := &MockQuery{
		transactionFunc: func(fc func(tx *query.Query) error, opts ...*sql.TxOptions) error {
			// 模拟事务执行，直接调用传入的函数
			return fc(nil)
		},
	}

	logic := &Logic{
		query:        mockQuery,
		categoryRepo: mockRepo,
	}

	// Test data
	req := &offeringpb.SaveAddOnCategoriesRequest{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		UpdateCategories: []*offeringpb.SaveAddOnCategoriesRequest_CategoryUpdateDef{
			{
				Id:   1,
				Name: "Updated Category",
				Sort: 150,
			},
		},
	}

	// Mock expectations for WithQuery calls
	mockRepo.EXPECT().WithQuery(gomock.Any()).Return(mockRepo).Times(1)

	// Mock expectations for update - 模拟更新失败
	mockRepo.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(fmt.Errorf("update failed"))

	// Execute
	result, err := logic.SaveAddOnCategories(context.Background(), req)

	// Assertions
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "failed to update categories")
}
