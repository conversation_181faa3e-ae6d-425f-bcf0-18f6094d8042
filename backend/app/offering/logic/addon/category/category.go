package category

import (
	"context"
	"fmt"

	"github.com/samber/lo"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/moego/backend/app/offering/logic/transaction"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	servicecategory2 "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/servicecategory"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

type Logic struct {
	query        transaction.Manager
	categoryRepo servicecategory2.Repository
}

func NewLogic() *Logic {
	return &Logic{
		query:        query.Use(db.GetDB()),
		categoryRepo: servicecategory2.NewRepository(),
	}
}

// NewLogicWithQuery creates a new Logic instance with a custom query interface (for testing)
func NewLogicWithQuery(q transaction.Manager, categoryRepo servicecategory2.Repository) *Logic {
	return &Logic{
		query:        q,
		categoryRepo: categoryRepo,
	}
}

// ListAddOnCategories lists addon categories based on the request
func (l *Logic) ListAddOnCategories(ctx context.Context,
	req *offeringpb.ListAddOnCategoriesRequest) (*offeringpb.ListAddOnCategoriesResponse, error) {
	// Convert request to repository filter
	filter := &servicecategory2.ListServiceCategoryFilter{
		OrganizationID:   req.OrganizationId,
		OrganizationType: req.OrganizationType,
		Types:            []offeringpb.Service_Type{offeringpb.Service_ADD_ON},
	}
	if req.GetFilter() != nil {
		filter.IDs = req.GetFilter().GetCategoryIds()
	}

	// Get categories from repository
	categories, _, err := l.categoryRepo.List(ctx, filter, nil)
	if err != nil {
		return nil, err
	}

	// Convert models to protobuf messages
	var protoCategories []*offeringpb.AddOnCategory
	for _, category := range categories {
		// 只返回 ADD_ON 类型的分类
		if category.Type == offeringpb.Service_ADD_ON {
			protoCategories = append(protoCategories, &offeringpb.AddOnCategory{
				Id:               category.ID,
				OrganizationType: category.OrganizationType,
				OrganizationId:   category.OrganizationID,
				Name:             category.Name,
				Sort:             category.Sort,
				CreateTime:       timestamppb.New(lo.FromPtr(category.CreateTime)),
				UpdateTime:       timestamppb.New(lo.FromPtr(category.UpdateTime)),
				DeleteTime:       timestamppb.New(lo.FromPtr(category.DeleteTime)),
				IsDeleted:        category.DeleteTime != nil,
			})
		}
	}

	return &offeringpb.ListAddOnCategoriesResponse{
		Categories: protoCategories,
	}, nil
}

// SaveAddOnCategories saves addon categories based on the request
func (l *Logic) SaveAddOnCategories(ctx context.Context,
	req *offeringpb.SaveAddOnCategoriesRequest) (*offeringpb.SaveAddOnCategoriesResponse, error) {
	// 使用事务执行所有数据库操作
	err := l.query.Transaction(func(tx *query.Query) error {
		// 创建带事务的 repository
		txRepo := l.categoryRepo.WithQuery(tx)

		// 1. 删除指定的分类
		err := l.deleteAddOnCategoriesWithRepo(ctx, req, txRepo)
		if err != nil {
			return fmt.Errorf("failed to delete categories: %w", err)
		}

		// 2. 更新现有分类
		err = l.updateAddOnCategoriesWithRepo(ctx, req, txRepo)
		if err != nil {
			return fmt.Errorf("failed to update categories: %w", err)
		}

		// 3. 创建新分类
		err = l.createAddOnCategoriesWithRepo(ctx, req, txRepo)
		if err != nil {
			return fmt.Errorf("failed to create categories: %w", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &offeringpb.SaveAddOnCategoriesResponse{}, nil
}

// buildNewAddOnCategoryModel 构建新的 addon 分类模型
func (l *Logic) buildNewAddOnCategoryModel(
	req *offeringpb.SaveAddOnCategoriesRequest,
	category *offeringpb.SaveAddOnCategoriesRequest_CategoryCreateDef,
	sort int32) *model.ServiceCategory {
	return &model.ServiceCategory{
		OrganizationType: req.OrganizationType,
		OrganizationID:   req.OrganizationId,
		Name:             category.Name,
		Sort:             sort,
		Type:             offeringpb.Service_ADD_ON,
	}
}

// deleteAddOnCategoriesWithRepo 使用指定的 repository 删除指定的分类
func (l *Logic) deleteAddOnCategoriesWithRepo(
	ctx context.Context,
	req *offeringpb.SaveAddOnCategoriesRequest,
	repo servicecategory2.Repository) error {
	for _, id := range req.DeleteIds {
		orgType := req.OrganizationType
		where := &servicecategory2.WhereCondition{
			ID:               id,
			OrganizationType: &orgType,
			OrganizationID:   &req.OrganizationId,
			IncludeDeleted:   false, // 删除时不需要包含已删除的记录
		}
		err := repo.Delete(ctx, where)
		if err != nil {
			return fmt.Errorf("failed to delete category %d: %w", id, err)
		}
	}

	return nil
}

// createAddOnCategoriesWithRepo 使用指定的 repository 创建新分类
func (l *Logic) createAddOnCategoriesWithRepo(
	ctx context.Context,
	req *offeringpb.SaveAddOnCategoriesRequest,
	repo servicecategory2.Repository) error {
	for _, category := range req.CreateCategories {
		newCategory := l.buildNewAddOnCategoryModel(req, category, int32(category.Sort))
		err := repo.Create(ctx, newCategory)
		if err != nil {
			return fmt.Errorf("failed to create category: %w", err)
		}
	}

	return nil
}

// updateAddOnCategoriesWithRepo 使用指定的 repository 更新现有分类
func (l *Logic) updateAddOnCategoriesWithRepo(
	ctx context.Context,
	req *offeringpb.SaveAddOnCategoriesRequest,
	repo servicecategory2.Repository) error {
	for _, category := range req.UpdateCategories {
		updateData := &model.ServiceCategory{
			Name: category.Name,
			Sort: int32(category.Sort),
		}

		orgType := req.OrganizationType
		where := &servicecategory2.WhereCondition{
			ID:               category.Id,
			OrganizationType: &orgType,
			OrganizationID:   &req.OrganizationId,
			IncludeDeleted:   false,
		}

		err := repo.Update(ctx, updateData, where)
		if err != nil {
			return fmt.Errorf("failed to update category %d: %w", category.Id, err)
		}
	}

	return nil
}
