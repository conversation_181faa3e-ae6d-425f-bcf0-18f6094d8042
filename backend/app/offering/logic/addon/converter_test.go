package addon

import (
	"testing"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"

	"github.com/MoeGolibrary/moego/backend/app/offering/logic/association"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

func TestServiceModelToProto(t *testing.T) {
	// Test case 1: Normal service model
	service := &model.Service{
		ID:               123,
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationID:   456,
		CategoryID:       789,
		Name:             "Test Service",
		Description:      lo.ToPtr("Test Description"),
		ColorCode:        "#FF0000",
		Sort:             100,
		Images:           []string{"image1.jpg", "image2.jpg"},
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.Service_ACTIVE,
		Type:             offeringpb.Service_ADD_ON,
	}

	result := ServiceModelToProto(service)

	assert.NotNil(t, result)
	assert.Equal(t, int64(123), result.Id)
	assert.Equal(t, organizationpb.OrganizationType_COMPANY, result.OrganizationType)
	assert.Equal(t, int64(456), result.OrganizationId)
	assert.Equal(t, int64(789), *result.CategoryId)
	assert.Equal(t, "Test Service", result.Name)
	assert.Equal(t, "Test Description", *result.Description)
	assert.Equal(t, "#FF0000", result.ColorCode)
	assert.Equal(t, int64(100), *result.Sort)
	assert.Equal(t, []string{"image1.jpg", "image2.jpg"}, result.Images)
	assert.Equal(t, offeringpb.AddOn_ACTIVE, result.Status)
	assert.Equal(t, offeringpb.OfferingSource_MOEGO, result.Source)
	assert.False(t, result.IsDeleted)

	// Test case 2: Nil service model
	result = ServiceModelToProto(nil)
	assert.Nil(t, result)
}

func TestCreateDefToServiceModel(t *testing.T) {
	// Test data
	createDef := &offeringpb.AddOnCreateDef{
		OrganizationType: organizationpb.OrganizationType_COMPANY,
		OrganizationId:   123,
		Name:             "Test AddOn",
		Description:      lo.ToPtr("Test Description"),
		ColorCode:        "#FF0000",
		Images:           []string{"image1.jpg", "image2.jpg"},
		Source:           offeringpb.OfferingSource_MOEGO,
		Status:           offeringpb.AddOn_ACTIVE,
		IsRequiredStaff:  true,
		Duration:         60,
	}

	// Execute
	result := CreateDefToServiceModel(createDef)

	// Assertions
	assert.NotNil(t, result)
	assert.Equal(t, organizationpb.OrganizationType_COMPANY, result.OrganizationType)
	assert.Equal(t, int64(123), result.OrganizationID)
	assert.Equal(t, "Test AddOn", result.Name)
	assert.Equal(t, "Test Description", *result.Description)
	assert.Equal(t, "#FF0000", result.ColorCode)
	assert.Equal(t, []string{"image1.jpg", "image2.jpg"}, result.Images)
	assert.Equal(t, offeringpb.OfferingSource_MOEGO, result.Source)
	assert.Equal(t, offeringpb.Service_ACTIVE, result.Status)
	assert.Equal(t, offeringpb.Service_ADD_ON, result.Type)
}

func TestUpdateDefToServiceModel(t *testing.T) {
	// Test data
	updateDef := &offeringpb.AddOnUpdateDef{
		Id:          123,
		Name:        lo.ToPtr("Updated AddOn"),
		Description: lo.ToPtr("Updated Description"),
		ColorCode:   lo.ToPtr("#00FF00"),
		Images:      []string{"updated1.jpg", "updated2.jpg"},
		Status:      lo.ToPtr(offeringpb.AddOn_INACTIVE),
	}

	// Execute
	result := UpdateDefToServiceModel(updateDef)

	// Assertions
	assert.NotNil(t, result)
	assert.Equal(t, int64(123), result.ID)
	assert.Equal(t, "Updated AddOn", result.Name)
	assert.Equal(t, "Updated Description", *result.Description)
	assert.Equal(t, "#00FF00", result.ColorCode)
	assert.Equal(t, []string{"updated1.jpg", "updated2.jpg"}, result.Images)
	assert.Equal(t, offeringpb.Service_INACTIVE, result.Status)
}

func TestProtoToAggregate(t *testing.T) {
	// Test case 1: Applicable to all services
	applicableService := &offeringpb.ApplicableService{
		IsAll: true,
	}

	result := ProtoToAggregate(123, applicableService)

	assert.NotNil(t, result)
	assert.Equal(t, int64(123), result.SourceServiceID)
	assert.True(t, result.IsAllServices())
	assert.Empty(t, result.TargetCareTypeIDs)
	assert.Empty(t, result.TargetServiceIDs)

	// Test case 2: Applicable to specific care type
	applicableService2 := &offeringpb.ApplicableService{
		IsAll: false,
		SpecificServices: []*offeringpb.SpecificService{
			{
				CareTypeId:    lo.ToPtr(int64(456)),
				IsAllServices: true,
			},
		},
	}

	result2 := ProtoToAggregate(123, applicableService2)

	assert.NotNil(t, result2)
	assert.Equal(t, int64(123), result2.SourceServiceID)
	assert.False(t, result2.IsAllServices())
	assert.Contains(t, result2.TargetCareTypeIDs, int64(456))
	assert.Empty(t, result2.TargetServiceIDs)

	// Test case 3: Applicable to specific services
	applicableService3 := &offeringpb.ApplicableService{
		IsAll: false,
		SpecificServices: []*offeringpb.SpecificService{
			{
				CareTypeId:    lo.ToPtr(int64(456)),
				IsAllServices: false,
				ServiceIds:    []int64{789, 101},
			},
		},
	}

	result3 := ProtoToAggregate(123, applicableService3)

	assert.NotNil(t, result3)
	assert.Equal(t, int64(123), result3.SourceServiceID)
	assert.False(t, result3.IsAllServices())
	assert.Empty(t, result3.TargetCareTypeIDs)
	assert.Contains(t, result3.TargetServiceIDs, int64(789))
	assert.Contains(t, result3.TargetServiceIDs, int64(101))

	// Test case 4: Nil applicable service
	result4 := ProtoToAggregate(123, nil)
	assert.Nil(t, result4)
}

func TestAggregateToProto(t *testing.T) {
	// Test case 1: All services aggregate
	agg := association.NewAggregate(123)
	agg.AddServiceTypeTarget(offeringpb.Service_SERVICE)

	result := AggregateToProto(agg, nil)

	assert.NotNil(t, result)
	assert.True(t, result.IsAll)
	assert.Empty(t, result.SpecificServices)

	// Test case 2: Care type aggregate
	agg2 := association.NewAggregate(123)
	agg2.AddCareTypeTarget(456)

	result2 := AggregateToProto(agg2, nil)

	assert.NotNil(t, result2)
	assert.False(t, result2.IsAll)
	assert.Len(t, result2.SpecificServices, 1)
	assert.Equal(t, int64(456), *result2.SpecificServices[0].CareTypeId)
	assert.True(t, result2.SpecificServices[0].IsAllServices)

	// Test case 3: Service aggregate
	agg3 := association.NewAggregate(123)
	agg3.AddServiceIDTarget(789)
	agg3.AddServiceIDTarget(101)

	careTypeMap := map[int64]int64{
		789: 456,
		101: 456,
	}

	result3 := AggregateToProto(agg3, careTypeMap)

	assert.NotNil(t, result3)
	assert.False(t, result3.IsAll)
	assert.Len(t, result3.SpecificServices, 1)
	assert.Equal(t, int64(456), *result3.SpecificServices[0].CareTypeId)
	assert.False(t, result3.SpecificServices[0].IsAllServices)
	assert.Contains(t, result3.SpecificServices[0].ServiceIds, int64(789))
	assert.Contains(t, result3.SpecificServices[0].ServiceIds, int64(101))

	// Test case 4: Nil aggregate
	result4 := AggregateToProto(nil, nil)
	assert.NotNil(t, result4)
	assert.False(t, result4.IsAll)
	assert.Empty(t, result4.SpecificServices)
}
