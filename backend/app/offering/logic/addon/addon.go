package addon

import (
	"context"
	"errors"
	"fmt"

	"github.com/samber/lo"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/moego/backend/app/offering/logic/association"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/attribute"
	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/availability"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	service2 "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service"
	servicecategory2 "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/servicecategory"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func NewLogic() *Logic {
	return &Logic{
		query:               query.Use(db.GetDB()),
		serviceRepo:         service2.NewRepository(),
		attributeManager:    attribute.NewManager(),
		availabilityManager: availability.NewManager(),
		businessScopeLogic:  service.NewBusinessScope(),
		associationLogic:    association.NewLogic(),
		categoryRepo:        servicecategory2.NewRepository(),
	}
}

type Logic struct {
	query               *query.Query
	serviceRepo         service2.Repository
	attributeManager    *attribute.Manager
	availabilityManager *availability.Manager
	businessScopeLogic  *service.BusinessScope
	associationLogic    *association.Logic
	categoryRepo        servicecategory2.Repository
}

// CreateAddOn creates a new addon.
func (l *Logic) CreateAddOn(ctx context.Context, createDef *offeringpb.AddOnCreateDef) (int64, error) {
	addon := CreateDefToServiceModel(createDef)

	// 1. 创建 addon
	err := l.serviceRepo.Create(ctx, addon)
	if err != nil {
		return 0, err
	}

	// 2. 更新 sort 为 ID
	addon.Sort = addon.ID
	err = l.serviceRepo.Update(ctx, addon)
	if err != nil {
		return 0, err
	}

	// 3. 保存 AddOn 相关属性
	if err := l.saveAddOnAttributes(ctx, addon.ID, createDef); err != nil {
		return 0, err
	}

	// 4. 保存 business scope
	if err := l.businessScopeLogic.UpdateBusinessScope(ctx, addon.ID, createDef.AvailableBusiness); err != nil {
		return 0, err
	}

	// 5. 保存 applicable service
	if err := l.saveApplicableService(ctx, addon.ID, createDef.ApplicableService); err != nil {
		return 0, err
	}

	// 6. 保存 Pet availability
	petAvailability := &availability.PetAvailability{
		ServiceID:    addon.ID,
		PetTypeBreed: createDef.AvailableTypeBreed,
		PetSize:      createDef.AvailablePetSize,
		CoatType:     createDef.AvailableCoatType,
	}
	if err := l.availabilityManager.Save(ctx, l.query, petAvailability); err != nil {
		return 0, err
	}

	return addon.ID, nil
}

// saveAddOnAttributes 保存 AddOn 特有的属性
func (l *Logic) saveAddOnAttributes(ctx context.Context, addonID int64, createDef *offeringpb.AddOnCreateDef) error {
	addonAttributes := &offeringpb.ServiceAttributes{
		IsRequiredStaff: &createDef.IsRequiredStaff,
		Duration:        &createDef.Duration,
	}

	return l.attributeManager.Save(ctx, addonID, addonAttributes)
}

// saveApplicableService 保存 applicable service 配置
func (l *Logic) saveApplicableService(
	ctx context.Context, addonID int64, applicableService *offeringpb.ApplicableService) error {
	if applicableService == nil {
		return nil
	}

	agg := ProtoToAggregate(addonID, applicableService)

	if agg == nil {
		return nil
	}

	return l.associationLogic.UpsertAssociations(ctx, agg)
}

// UpdateAddOn updates an addon.
func (l *Logic) UpdateAddOn(ctx context.Context, updateDef *offeringpb.AddOnUpdateDef) error {
	addon := UpdateDefToServiceModel(updateDef)

	// 1. 更新 service
	if err := l.serviceRepo.Update(ctx, addon); err != nil {
		return err
	}

	// 2. 更新 AddOn 相关属性
	if err := l.updateAddOnAttributes(ctx, addon.ID, updateDef); err != nil {
		return err
	}

	// 3. 更新 business scope
	if err := l.businessScopeLogic.UpdateBusinessScope(ctx, addon.ID, updateDef.AvailableBusiness); err != nil {
		return err
	}

	// 4. 更新 applicable service
	if err := l.saveApplicableService(ctx, addon.ID, updateDef.ApplicableService); err != nil {
		return err
	}

	// 5. 更新 Pet availability
	petAvailability := &availability.PetAvailability{
		ServiceID:    addon.ID,
		PetTypeBreed: updateDef.AvailableTypeBreed,
		PetSize:      updateDef.AvailablePetSize,
		CoatType:     updateDef.AvailableCoatType,
	}
	if err := l.availabilityManager.Save(ctx, l.query, petAvailability); err != nil {
		return err
	}

	return nil
}

// GetAddOn gets an addon by ID.
func (l *Logic) GetAddOn(ctx context.Context, id int64) (*offeringpb.AddOn, error) {
	// 1. 获取 service
	addon, err := l.serviceRepo.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	// 2. 检查是否为 ADD_ON 类型
	if addon.Type != offeringpb.Service_ADD_ON {
		return nil, status.Error(codes.NotFound, "service is not an addon")
	}

	// 3. Model 转换为 Proto
	addonProto := ServiceModelToProto(addon)

	// 4. 加载 AddOn 特有的属性
	attributes, err := l.attributeManager.ListByServiceID(ctx, id)
	if err != nil {
		return nil, err
	}
	if attributes != nil {
		addonProto.IsRequiredStaff = attributes.GetIsRequiredStaff()
		addonProto.Duration = attributes.GetDuration()
	}

	// 5. 加载 AvailableBusiness
	availableBusiness, err := l.businessScopeLogic.GetBusinessScope(ctx, id)
	if err != nil {
		return nil, err
	}
	addonProto.AvailableBusiness = availableBusiness

	// 6. 加载 ApplicableService
	applicableService, err := l.getApplicableService(ctx, id)
	if err != nil {
		return nil, err
	}
	addonProto.ApplicableService = applicableService

	// 7. 加载 Pet availability
	petAvailability, err := l.availabilityManager.GetByServiceID(ctx, id)
	if err != nil {
		return nil, err
	}
	addonProto.AvailableTypeBreed = petAvailability.PetTypeBreed
	addonProto.AvailablePetSize = petAvailability.PetSize
	addonProto.AvailableCoatType = petAvailability.CoatType

	return addonProto, nil
}

// DeleteAddOn deletes an addon.
func (l *Logic) DeleteAddOn(ctx context.Context, id int64) error {
	// 1. 删除 Business scope
	err := l.businessScopeLogic.DeleteBusinessScope(ctx, id)
	if err != nil {
		return err
	}

	// 2. 删除 ServiceAssociation 关联配置
	err = l.associationLogic.DeleteByServiceID(ctx, id)
	if err != nil {
		return err
	}

	// 3. 删除 AddOn 特有的属性
	err = l.deleteAddOnAttributes(ctx, id)
	if err != nil {
		return err
	}

	// 4. 删除 Pet availability
	err = l.availabilityManager.DeleteByServiceID(ctx, l.query, id)
	if err != nil {
		return err
	}

	// 5. 删除 AddOn (使用 Service model)
	return l.serviceRepo.Delete(ctx, id)
}

// ListAddOns lists addons based on the given request parameters.
func (l *Logic) ListAddOns(
	ctx context.Context, req *offeringpb.ListAddOnsRequest) (*offeringpb.ListAddOnsResponse, error) {
	// 1. 查询 addon 列表
	filter := l.buildListAddOnsFilter(req)
	pagination := l.getPaginationParams(req)
	services, total, err := l.serviceRepo.List(ctx, filter, pagination)
	if err != nil {
		return nil, err
	}

	serviceIDs := lo.Map(services, func(service *model.Service, _ int) int64 {
		return service.ID
	})
	// 2. 查询 addon 的 attributes
	attributeMap, err := l.attributeManager.ListByServiceIDs(ctx, serviceIDs)
	if err != nil {
		return nil, err
	}

	// 3. 查询 addon 的 business scope
	businessScopeMap, err := l.businessScopeLogic.ListBusinessScopes(ctx, serviceIDs)
	if err != nil {
		return nil, err
	}

	// 4. 查询 addon 的 applicable service
	associationMap, err := l.associationLogic.ListAggregatesByServiceIDs(ctx, serviceIDs)
	if err != nil {
		return nil, err
	}

	// 5. 构建响应
	addons := lo.Map(services, func(service *model.Service, _ int) *offeringpb.AddOn {
		addon := ServiceModelToProto(service)
		attributes := attributeMap[service.ID]
		if attributes != nil {
			addon.IsRequiredStaff = attributes.GetIsRequiredStaff()
			addon.Duration = attributes.GetDuration()
		}
		addon.AvailableBusiness = businessScopeMap[service.ID]
		addon.ApplicableService = AggregateToProto(associationMap[service.ID], nil)

		return addon
	})

	return &offeringpb.ListAddOnsResponse{
		AddOns:     addons,
		Total:      int32(total),
		Pagination: pagination,
	}, nil
}

// buildListAddOnsFilter 构造 addon 列表查询的过滤条件
func (l *Logic) buildListAddOnsFilter(req *offeringpb.ListAddOnsRequest) *service2.ListServiceFilter {
	var filter = &service2.ListServiceFilter{
		OrganizationType: req.OrganizationType,
		OrganizationID:   req.OrganizationId,
		Types:            []offeringpb.Service_Type{offeringpb.Service_ADD_ON},
	}
	if req.Filter != nil {
		filter.Statuses = lo.Map(req.Filter.Statuses, func(s offeringpb.AddOn_Status, _ int) offeringpb.Service_Status {
			return offeringpb.Service_Status(s)
		})
		filter.Sources = req.GetFilter().Sources
		filter.Keyword = req.GetFilter().Keyword

		return filter
	}

	return filter
}

// getPaginationParams 获取分页参数
func (l *Logic) getPaginationParams(req *offeringpb.ListAddOnsRequest) *offeringpb.PaginationRef {
	if req.Pagination != nil {
		return req.Pagination
	}

	return &offeringpb.PaginationRef{
		Offset: 0,
		Limit:  200,
	}
}

// BatchUpdateAddOns 批量更新 addon 信息
func (l *Logic) BatchUpdateAddOns(ctx context.Context,
	req *offeringpb.BatchUpdateAddOnsRequest) (*offeringpb.BatchUpdateAddOnsResponse, error) {
	// 1. 获取需要更新的 addon 列表
	addonIDs := lo.Map(req.UpdateAddOns, func(a *offeringpb.AddOnUpdateDef, _ int) int64 { return a.Id })
	filter := &service2.ListServiceFilter{
		OrganizationType: req.OrganizationType,
		OrganizationID:   req.OrganizationId,
		IDs:              addonIDs,
		Types:            []offeringpb.Service_Type{offeringpb.Service_ADD_ON},
	}
	services, _, err := l.serviceRepo.List(ctx, filter, nil)
	if err != nil {
		return nil, err
	}

	serviceMap := lo.KeyBy(services, func(s *model.Service) int64 { return s.ID })

	// 2. 批量更新 addon
	for _, updateDef := range req.UpdateAddOns {
		// 3. 检查 addon 是否存在
		_, exists := serviceMap[updateDef.Id]
		if !exists {
			return nil, errors.New("addon not found")
		}

		update := UpdateDefToServiceModel(updateDef)
		if err := l.serviceRepo.Update(ctx, update); err != nil {
			return nil, err
		}

		// 更新关联数据
		if err := l.updateAddOnRelatedData(ctx, updateDef); err != nil {
			return nil, err
		}
	}

	// 4. 构造响应
	return &offeringpb.BatchUpdateAddOnsResponse{}, nil
}

// updateAddOnRelatedData 更新 addon 关联数据
func (l *Logic) updateAddOnRelatedData(
	ctx context.Context, updateDef *offeringpb.AddOnUpdateDef) error {
	// 更新 AvailableBusiness
	if updateDef.AvailableBusiness != nil {
		err := l.businessScopeLogic.UpdateBusinessScope(ctx, updateDef.Id, updateDef.AvailableBusiness)
		if err != nil {
			return fmt.Errorf("failed to update business scope: %w", err)
		}
	}

	// 更新 is_required_staff 属性
	if updateDef.IsRequiredStaff != nil {
		err := l.updateAddOnAttributes(ctx, updateDef.Id, updateDef)
		if err != nil {
			return fmt.Errorf("failed to update addon attributes: %w", err)
		}
	}

	return nil
}

// updateAddOnAttributes 更新 AddOn 特有的属性
func (l *Logic) updateAddOnAttributes(ctx context.Context, addonID int64, updateDef *offeringpb.AddOnUpdateDef) error {
	addonAttributes := &offeringpb.ServiceAttributes{
		IsRequiredStaff: updateDef.IsRequiredStaff,
		Duration:        updateDef.Duration,
	}

	return l.attributeManager.Save(ctx, addonID, addonAttributes)
}

// deleteAddOnAttributes 删除 AddOn 特有的属性
func (l *Logic) deleteAddOnAttributes(ctx context.Context, addonID int64) error {
	return l.attributeManager.DeleteByServiceID(ctx, addonID)
}

// getApplicableService 获取 addon 的 applicable service 配置
func (l *Logic) getApplicableService(ctx context.Context, addonID int64) (*offeringpb.ApplicableService, error) {
	agg, err := l.associationLogic.GetAggregateByServiceID(ctx, addonID)
	if err != nil {
		return nil, err
	}

	var careTypeIDByServiceID = make(map[int64]int64)
	if len(agg.TargetServiceIDs) > 0 {
		services, err := l.serviceRepo.BatchGet(ctx, agg.TargetServiceIDs)
		if err != nil {
			return nil, err
		}

		for _, service := range services {
			careTypeIDByServiceID[service.ID] = service.CareTypeID
		}
	}

	return AggregateToProto(agg, careTypeIDByServiceID), nil
}
