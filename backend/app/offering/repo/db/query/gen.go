// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                    db,
		CareType:              newCareType(db, opts...),
		CareTypeAttribute:     newCareTypeAttribute(db, opts...),
		Service:               newService(db, opts...),
		ServiceAssociation:    newServiceAssociation(db, opts...),
		ServiceAttribute:      newServiceAttribute(db, opts...),
		ServiceAutoRollover:   newServiceAutoRollover(db, opts...),
		ServiceBusinessScope:  newServiceBusinessScope(db, opts...),
		ServiceCategory:       newServiceCategory(db, opts...),
		ServiceLodgingScope:   newServiceLodgingScope(db, opts...),
		ServiceObSetting:      newServiceObSetting(db, opts...),
		ServiceObStaffBinding: newServiceObStaffBinding(db, opts...),
		ServiceStaffScope:     newServiceStaffScope(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	CareType              careType
	CareTypeAttribute     careTypeAttribute
	Service               service
	ServiceAssociation    serviceAssociation
	ServiceAttribute      serviceAttribute
	ServiceAutoRollover   serviceAutoRollover
	ServiceBusinessScope  serviceBusinessScope
	ServiceCategory       serviceCategory
	ServiceLodgingScope   serviceLodgingScope
	ServiceObSetting      serviceObSetting
	ServiceObStaffBinding serviceObStaffBinding
	ServiceStaffScope     serviceStaffScope
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                    db,
		CareType:              q.CareType.clone(db),
		CareTypeAttribute:     q.CareTypeAttribute.clone(db),
		Service:               q.Service.clone(db),
		ServiceAssociation:    q.ServiceAssociation.clone(db),
		ServiceAttribute:      q.ServiceAttribute.clone(db),
		ServiceAutoRollover:   q.ServiceAutoRollover.clone(db),
		ServiceBusinessScope:  q.ServiceBusinessScope.clone(db),
		ServiceCategory:       q.ServiceCategory.clone(db),
		ServiceLodgingScope:   q.ServiceLodgingScope.clone(db),
		ServiceObSetting:      q.ServiceObSetting.clone(db),
		ServiceObStaffBinding: q.ServiceObStaffBinding.clone(db),
		ServiceStaffScope:     q.ServiceStaffScope.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                    db,
		CareType:              q.CareType.replaceDB(db),
		CareTypeAttribute:     q.CareTypeAttribute.replaceDB(db),
		Service:               q.Service.replaceDB(db),
		ServiceAssociation:    q.ServiceAssociation.replaceDB(db),
		ServiceAttribute:      q.ServiceAttribute.replaceDB(db),
		ServiceAutoRollover:   q.ServiceAutoRollover.replaceDB(db),
		ServiceBusinessScope:  q.ServiceBusinessScope.replaceDB(db),
		ServiceCategory:       q.ServiceCategory.replaceDB(db),
		ServiceLodgingScope:   q.ServiceLodgingScope.replaceDB(db),
		ServiceObSetting:      q.ServiceObSetting.replaceDB(db),
		ServiceObStaffBinding: q.ServiceObStaffBinding.replaceDB(db),
		ServiceStaffScope:     q.ServiceStaffScope.replaceDB(db),
	}
}

type queryCtx struct {
	CareType              *careTypeDo
	CareTypeAttribute     *careTypeAttributeDo
	Service               *serviceDo
	ServiceAssociation    *serviceAssociationDo
	ServiceAttribute      *serviceAttributeDo
	ServiceAutoRollover   *serviceAutoRolloverDo
	ServiceBusinessScope  *serviceBusinessScopeDo
	ServiceCategory       *serviceCategoryDo
	ServiceLodgingScope   *serviceLodgingScopeDo
	ServiceObSetting      *serviceObSettingDo
	ServiceObStaffBinding *serviceObStaffBindingDo
	ServiceStaffScope     *serviceStaffScopeDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		CareType:              q.CareType.WithContext(ctx),
		CareTypeAttribute:     q.CareTypeAttribute.WithContext(ctx),
		Service:               q.Service.WithContext(ctx),
		ServiceAssociation:    q.ServiceAssociation.WithContext(ctx),
		ServiceAttribute:      q.ServiceAttribute.WithContext(ctx),
		ServiceAutoRollover:   q.ServiceAutoRollover.WithContext(ctx),
		ServiceBusinessScope:  q.ServiceBusinessScope.WithContext(ctx),
		ServiceCategory:       q.ServiceCategory.WithContext(ctx),
		ServiceLodgingScope:   q.ServiceLodgingScope.WithContext(ctx),
		ServiceObSetting:      q.ServiceObSetting.WithContext(ctx),
		ServiceObStaffBinding: q.ServiceObStaffBinding.WithContext(ctx),
		ServiceStaffScope:     q.ServiceStaffScope.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
