// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
)

func newServiceObStaffBinding(db *gorm.DB, opts ...gen.DOOption) serviceObStaffBinding {
	_serviceObStaffBinding := serviceObStaffBinding{}

	_serviceObStaffBinding.serviceObStaffBindingDo.UseDB(db, opts...)
	_serviceObStaffBinding.serviceObStaffBindingDo.UseModel(&model.ServiceObStaffBinding{})

	tableName := _serviceObStaffBinding.serviceObStaffBindingDo.TableName()
	_serviceObStaffBinding.ALL = field.NewAsterisk(tableName)
	_serviceObStaffBinding.ID = field.NewInt64(tableName, "id")
	_serviceObStaffBinding.ServiceID = field.NewInt64(tableName, "service_id")
	_serviceObStaffBinding.StaffID = field.NewInt64(tableName, "staff_id")

	_serviceObStaffBinding.fillFieldMap()

	return _serviceObStaffBinding
}

type serviceObStaffBinding struct {
	serviceObStaffBindingDo serviceObStaffBindingDo

	ALL       field.Asterisk
	ID        field.Int64
	ServiceID field.Int64
	StaffID   field.Int64

	fieldMap map[string]field.Expr
}

func (s serviceObStaffBinding) Table(newTableName string) *serviceObStaffBinding {
	s.serviceObStaffBindingDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s serviceObStaffBinding) As(alias string) *serviceObStaffBinding {
	s.serviceObStaffBindingDo.DO = *(s.serviceObStaffBindingDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *serviceObStaffBinding) updateTableName(table string) *serviceObStaffBinding {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.ServiceID = field.NewInt64(table, "service_id")
	s.StaffID = field.NewInt64(table, "staff_id")

	s.fillFieldMap()

	return s
}

func (s *serviceObStaffBinding) WithContext(ctx context.Context) *serviceObStaffBindingDo {
	return s.serviceObStaffBindingDo.WithContext(ctx)
}

func (s serviceObStaffBinding) TableName() string { return s.serviceObStaffBindingDo.TableName() }

func (s serviceObStaffBinding) Alias() string { return s.serviceObStaffBindingDo.Alias() }

func (s serviceObStaffBinding) Columns(cols ...field.Expr) gen.Columns {
	return s.serviceObStaffBindingDo.Columns(cols...)
}

func (s *serviceObStaffBinding) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *serviceObStaffBinding) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 3)
	s.fieldMap["id"] = s.ID
	s.fieldMap["service_id"] = s.ServiceID
	s.fieldMap["staff_id"] = s.StaffID
}

func (s serviceObStaffBinding) clone(db *gorm.DB) serviceObStaffBinding {
	s.serviceObStaffBindingDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s serviceObStaffBinding) replaceDB(db *gorm.DB) serviceObStaffBinding {
	s.serviceObStaffBindingDo.ReplaceDB(db)
	return s
}

type serviceObStaffBindingDo struct{ gen.DO }

func (s serviceObStaffBindingDo) Debug() *serviceObStaffBindingDo {
	return s.withDO(s.DO.Debug())
}

func (s serviceObStaffBindingDo) WithContext(ctx context.Context) *serviceObStaffBindingDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s serviceObStaffBindingDo) ReadDB() *serviceObStaffBindingDo {
	return s.Clauses(dbresolver.Read)
}

func (s serviceObStaffBindingDo) WriteDB() *serviceObStaffBindingDo {
	return s.Clauses(dbresolver.Write)
}

func (s serviceObStaffBindingDo) Session(config *gorm.Session) *serviceObStaffBindingDo {
	return s.withDO(s.DO.Session(config))
}

func (s serviceObStaffBindingDo) Clauses(conds ...clause.Expression) *serviceObStaffBindingDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s serviceObStaffBindingDo) Returning(value interface{}, columns ...string) *serviceObStaffBindingDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s serviceObStaffBindingDo) Not(conds ...gen.Condition) *serviceObStaffBindingDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s serviceObStaffBindingDo) Or(conds ...gen.Condition) *serviceObStaffBindingDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s serviceObStaffBindingDo) Select(conds ...field.Expr) *serviceObStaffBindingDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s serviceObStaffBindingDo) Where(conds ...gen.Condition) *serviceObStaffBindingDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s serviceObStaffBindingDo) Order(conds ...field.Expr) *serviceObStaffBindingDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s serviceObStaffBindingDo) Distinct(cols ...field.Expr) *serviceObStaffBindingDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s serviceObStaffBindingDo) Omit(cols ...field.Expr) *serviceObStaffBindingDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s serviceObStaffBindingDo) Join(table schema.Tabler, on ...field.Expr) *serviceObStaffBindingDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s serviceObStaffBindingDo) LeftJoin(table schema.Tabler, on ...field.Expr) *serviceObStaffBindingDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s serviceObStaffBindingDo) RightJoin(table schema.Tabler, on ...field.Expr) *serviceObStaffBindingDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s serviceObStaffBindingDo) Group(cols ...field.Expr) *serviceObStaffBindingDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s serviceObStaffBindingDo) Having(conds ...gen.Condition) *serviceObStaffBindingDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s serviceObStaffBindingDo) Limit(limit int) *serviceObStaffBindingDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s serviceObStaffBindingDo) Offset(offset int) *serviceObStaffBindingDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s serviceObStaffBindingDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *serviceObStaffBindingDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s serviceObStaffBindingDo) Unscoped() *serviceObStaffBindingDo {
	return s.withDO(s.DO.Unscoped())
}

func (s serviceObStaffBindingDo) Create(values ...*model.ServiceObStaffBinding) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s serviceObStaffBindingDo) CreateInBatches(values []*model.ServiceObStaffBinding, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s serviceObStaffBindingDo) Save(values ...*model.ServiceObStaffBinding) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s serviceObStaffBindingDo) First() (*model.ServiceObStaffBinding, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceObStaffBinding), nil
	}
}

func (s serviceObStaffBindingDo) Take() (*model.ServiceObStaffBinding, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceObStaffBinding), nil
	}
}

func (s serviceObStaffBindingDo) Last() (*model.ServiceObStaffBinding, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceObStaffBinding), nil
	}
}

func (s serviceObStaffBindingDo) Find() ([]*model.ServiceObStaffBinding, error) {
	result, err := s.DO.Find()
	return result.([]*model.ServiceObStaffBinding), err
}

func (s serviceObStaffBindingDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ServiceObStaffBinding, err error) {
	buf := make([]*model.ServiceObStaffBinding, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s serviceObStaffBindingDo) FindInBatches(result *[]*model.ServiceObStaffBinding, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s serviceObStaffBindingDo) Attrs(attrs ...field.AssignExpr) *serviceObStaffBindingDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s serviceObStaffBindingDo) Assign(attrs ...field.AssignExpr) *serviceObStaffBindingDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s serviceObStaffBindingDo) Joins(fields ...field.RelationField) *serviceObStaffBindingDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s serviceObStaffBindingDo) Preload(fields ...field.RelationField) *serviceObStaffBindingDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s serviceObStaffBindingDo) FirstOrInit() (*model.ServiceObStaffBinding, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceObStaffBinding), nil
	}
}

func (s serviceObStaffBindingDo) FirstOrCreate() (*model.ServiceObStaffBinding, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceObStaffBinding), nil
	}
}

func (s serviceObStaffBindingDo) FindByPage(offset int, limit int) (result []*model.ServiceObStaffBinding, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s serviceObStaffBindingDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s serviceObStaffBindingDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s serviceObStaffBindingDo) Delete(models ...*model.ServiceObStaffBinding) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *serviceObStaffBindingDo) withDO(do gen.Dao) *serviceObStaffBindingDo {
	s.DO = *do.(*gen.DO)
	return s
}
