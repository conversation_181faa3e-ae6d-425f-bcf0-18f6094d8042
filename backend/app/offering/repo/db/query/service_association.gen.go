// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
)

func newServiceAssociation(db *gorm.DB, opts ...gen.DOOption) serviceAssociation {
	_serviceAssociation := serviceAssociation{}

	_serviceAssociation.serviceAssociationDo.UseDB(db, opts...)
	_serviceAssociation.serviceAssociationDo.UseModel(&model.ServiceAssociation{})

	tableName := _serviceAssociation.serviceAssociationDo.TableName()
	_serviceAssociation.ALL = field.NewAsterisk(tableName)
	_serviceAssociation.ID = field.NewInt64(tableName, "id")
	_serviceAssociation.SourceServiceID = field.NewInt64(tableName, "source_service_id")
	_serviceAssociation.TargetServiceID = field.NewInt64(tableName, "target_service_id")
	_serviceAssociation.TargetCareTypeID = field.NewInt64(tableName, "target_care_type_id")
	_serviceAssociation.TargetServiceType = field.NewField(tableName, "target_service_type")
	_serviceAssociation.CreateTime = field.NewTime(tableName, "create_time")
	_serviceAssociation.UpdateTime = field.NewTime(tableName, "update_time")
	_serviceAssociation.DeleteTime = field.NewTime(tableName, "delete_time")

	_serviceAssociation.fillFieldMap()

	return _serviceAssociation
}

type serviceAssociation struct {
	serviceAssociationDo serviceAssociationDo

	ALL               field.Asterisk
	ID                field.Int64
	SourceServiceID   field.Int64 // The ID of the service/addon that the rule is configured FOR.
	TargetServiceID   field.Int64 // Rule Type 1: The ID of a specific service/addon that is being linked TO.
	TargetCareTypeID  field.Int64 // Rule Type 2: The ID of a care type. Links to all services within this category.
	TargetServiceType field.Field // Rule Type 3: The type of service (0=Service, 1=Addon). Links to all offerings of this type.
	CreateTime        field.Time
	UpdateTime        field.Time
	DeleteTime        field.Time

	fieldMap map[string]field.Expr
}

func (s serviceAssociation) Table(newTableName string) *serviceAssociation {
	s.serviceAssociationDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s serviceAssociation) As(alias string) *serviceAssociation {
	s.serviceAssociationDo.DO = *(s.serviceAssociationDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *serviceAssociation) updateTableName(table string) *serviceAssociation {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.SourceServiceID = field.NewInt64(table, "source_service_id")
	s.TargetServiceID = field.NewInt64(table, "target_service_id")
	s.TargetCareTypeID = field.NewInt64(table, "target_care_type_id")
	s.TargetServiceType = field.NewField(table, "target_service_type")
	s.CreateTime = field.NewTime(table, "create_time")
	s.UpdateTime = field.NewTime(table, "update_time")
	s.DeleteTime = field.NewTime(table, "delete_time")

	s.fillFieldMap()

	return s
}

func (s *serviceAssociation) WithContext(ctx context.Context) *serviceAssociationDo {
	return s.serviceAssociationDo.WithContext(ctx)
}

func (s serviceAssociation) TableName() string { return s.serviceAssociationDo.TableName() }

func (s serviceAssociation) Alias() string { return s.serviceAssociationDo.Alias() }

func (s serviceAssociation) Columns(cols ...field.Expr) gen.Columns {
	return s.serviceAssociationDo.Columns(cols...)
}

func (s *serviceAssociation) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *serviceAssociation) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 8)
	s.fieldMap["id"] = s.ID
	s.fieldMap["source_service_id"] = s.SourceServiceID
	s.fieldMap["target_service_id"] = s.TargetServiceID
	s.fieldMap["target_care_type_id"] = s.TargetCareTypeID
	s.fieldMap["target_service_type"] = s.TargetServiceType
	s.fieldMap["create_time"] = s.CreateTime
	s.fieldMap["update_time"] = s.UpdateTime
	s.fieldMap["delete_time"] = s.DeleteTime
}

func (s serviceAssociation) clone(db *gorm.DB) serviceAssociation {
	s.serviceAssociationDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s serviceAssociation) replaceDB(db *gorm.DB) serviceAssociation {
	s.serviceAssociationDo.ReplaceDB(db)
	return s
}

type serviceAssociationDo struct{ gen.DO }

func (s serviceAssociationDo) Debug() *serviceAssociationDo {
	return s.withDO(s.DO.Debug())
}

func (s serviceAssociationDo) WithContext(ctx context.Context) *serviceAssociationDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s serviceAssociationDo) ReadDB() *serviceAssociationDo {
	return s.Clauses(dbresolver.Read)
}

func (s serviceAssociationDo) WriteDB() *serviceAssociationDo {
	return s.Clauses(dbresolver.Write)
}

func (s serviceAssociationDo) Session(config *gorm.Session) *serviceAssociationDo {
	return s.withDO(s.DO.Session(config))
}

func (s serviceAssociationDo) Clauses(conds ...clause.Expression) *serviceAssociationDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s serviceAssociationDo) Returning(value interface{}, columns ...string) *serviceAssociationDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s serviceAssociationDo) Not(conds ...gen.Condition) *serviceAssociationDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s serviceAssociationDo) Or(conds ...gen.Condition) *serviceAssociationDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s serviceAssociationDo) Select(conds ...field.Expr) *serviceAssociationDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s serviceAssociationDo) Where(conds ...gen.Condition) *serviceAssociationDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s serviceAssociationDo) Order(conds ...field.Expr) *serviceAssociationDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s serviceAssociationDo) Distinct(cols ...field.Expr) *serviceAssociationDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s serviceAssociationDo) Omit(cols ...field.Expr) *serviceAssociationDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s serviceAssociationDo) Join(table schema.Tabler, on ...field.Expr) *serviceAssociationDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s serviceAssociationDo) LeftJoin(table schema.Tabler, on ...field.Expr) *serviceAssociationDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s serviceAssociationDo) RightJoin(table schema.Tabler, on ...field.Expr) *serviceAssociationDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s serviceAssociationDo) Group(cols ...field.Expr) *serviceAssociationDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s serviceAssociationDo) Having(conds ...gen.Condition) *serviceAssociationDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s serviceAssociationDo) Limit(limit int) *serviceAssociationDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s serviceAssociationDo) Offset(offset int) *serviceAssociationDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s serviceAssociationDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *serviceAssociationDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s serviceAssociationDo) Unscoped() *serviceAssociationDo {
	return s.withDO(s.DO.Unscoped())
}

func (s serviceAssociationDo) Create(values ...*model.ServiceAssociation) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s serviceAssociationDo) CreateInBatches(values []*model.ServiceAssociation, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s serviceAssociationDo) Save(values ...*model.ServiceAssociation) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s serviceAssociationDo) First() (*model.ServiceAssociation, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceAssociation), nil
	}
}

func (s serviceAssociationDo) Take() (*model.ServiceAssociation, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceAssociation), nil
	}
}

func (s serviceAssociationDo) Last() (*model.ServiceAssociation, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceAssociation), nil
	}
}

func (s serviceAssociationDo) Find() ([]*model.ServiceAssociation, error) {
	result, err := s.DO.Find()
	return result.([]*model.ServiceAssociation), err
}

func (s serviceAssociationDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ServiceAssociation, err error) {
	buf := make([]*model.ServiceAssociation, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s serviceAssociationDo) FindInBatches(result *[]*model.ServiceAssociation, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s serviceAssociationDo) Attrs(attrs ...field.AssignExpr) *serviceAssociationDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s serviceAssociationDo) Assign(attrs ...field.AssignExpr) *serviceAssociationDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s serviceAssociationDo) Joins(fields ...field.RelationField) *serviceAssociationDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s serviceAssociationDo) Preload(fields ...field.RelationField) *serviceAssociationDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s serviceAssociationDo) FirstOrInit() (*model.ServiceAssociation, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceAssociation), nil
	}
}

func (s serviceAssociationDo) FirstOrCreate() (*model.ServiceAssociation, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceAssociation), nil
	}
}

func (s serviceAssociationDo) FindByPage(offset int, limit int) (result []*model.ServiceAssociation, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s serviceAssociationDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s serviceAssociationDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s serviceAssociationDo) Delete(models ...*model.ServiceAssociation) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *serviceAssociationDo) withDO(do gen.Dao) *serviceAssociationDo {
	s.DO = *do.(*gen.DO)
	return s
}
