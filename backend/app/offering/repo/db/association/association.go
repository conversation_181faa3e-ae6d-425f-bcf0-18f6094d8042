package association

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
)

const (
	batchSize = 100
)

//go:generate mockgen -package=mock -destination=mocks/mock_association_repo.go . Repository
type Repository interface {
	WithTX(q *query.Query) Repository

	BatchCreate(ctx context.Context, scopes []*model.ServiceAssociation) error
	GetByServiceID(ctx context.Context, serviceID int64) ([]*model.ServiceAssociation, error)
	ListByServiceIDs(ctx context.Context, serviceIDs []int64) ([]*model.ServiceAssociation, error)
	DeleteByServiceID(ctx context.Context, serviceID int64) error
}

// repository implements the data access logic for ServiceAssociation.
type repository struct {
	query *query.Query
}

// NewRepository creates a new repository.
func NewRepository() Repository {
	return &repository{query: query.Use(db.GetDB())}
}

// NewRepositoryWithQuery creates a new repository with a custom query.
func NewRepositoryWithQuery(query *query.Query) Repository {
	return &repository{
		query: query,
	}
}

// WithTX sets the query for the repository, useful for multi operation in one transaction.
func (r *repository) WithTX(query *query.Query) Repository {
	return NewRepositoryWithQuery(query)
}

// BatchCreate creates multiple service associations.
func (r *repository) BatchCreate(
	ctx context.Context, models []*model.ServiceAssociation) error {
	if len(models) == 0 {
		return nil
	}

	return r.query.ServiceAssociation.WithContext(ctx).CreateInBatches(models, batchSize)
}

// GetByServiceID gets all service associations by service ID.
func (r *repository) GetByServiceID(
	ctx context.Context, serviceID int64) ([]*model.ServiceAssociation, error) {
	return r.ListByServiceIDs(ctx, []int64{serviceID})
}

// ListByServiceIDs lists service associations by multiple service IDs.
func (r *repository) ListByServiceIDs(
	ctx context.Context, serviceIDs []int64) ([]*model.ServiceAssociation, error) {
	if len(serviceIDs) == 0 {
		return []*model.ServiceAssociation{}, nil
	}

	return r.query.ServiceAssociation.WithContext(ctx).
		Where(r.query.ServiceAssociation.SourceServiceID.In(serviceIDs...)).
		Where(r.query.ServiceAssociation.DeleteTime.IsNull()).
		Find()
}

// DeleteByServiceID deletes all service associations by service ID.
func (r *repository) DeleteByServiceID(
	ctx context.Context, serviceID int64) error {
	_, err := r.DeleteByServiceIDs(ctx, []int64{serviceID})

	return err
}

// DeleteByServiceIDs deletes service associations by multiple service IDs.
func (r *repository) DeleteByServiceIDs(
	ctx context.Context, serviceIDs []int64) (int64, error) {
	if len(serviceIDs) == 0 {
		return 0, nil
	}

	result, err := r.query.ServiceAssociation.WithContext(ctx).
		Where(r.query.ServiceAssociation.SourceServiceID.In(serviceIDs...)).
		Delete()
	if err != nil {
		return 0, err
	}

	return result.RowsAffected, nil
}
