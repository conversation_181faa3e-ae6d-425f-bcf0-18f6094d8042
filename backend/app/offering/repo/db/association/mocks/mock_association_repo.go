// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego/backend/app/offering/repo/db/association (interfaces: Repository)
//
// Generated by this command:
//
//	mockgen -package=mock -destination=mocks/mock_association_repo.go . Repository
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	association "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/association"
	model "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	query "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	gomock "go.uber.org/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
	isgomock struct{}
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// BatchCreate mocks base method.
func (m *MockRepository) BatchCreate(ctx context.Context, scopes []*model.ServiceAssociation) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreate", ctx, scopes)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchCreate indicates an expected call of BatchCreate.
func (mr *MockRepositoryMockRecorder) BatchCreate(ctx, scopes any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreate", reflect.TypeOf((*MockRepository)(nil).BatchCreate), ctx, scopes)
}

// DeleteByServiceID mocks base method.
func (m *MockRepository) DeleteByServiceID(ctx context.Context, serviceID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteByServiceID", ctx, serviceID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteByServiceID indicates an expected call of DeleteByServiceID.
func (mr *MockRepositoryMockRecorder) DeleteByServiceID(ctx, serviceID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByServiceID", reflect.TypeOf((*MockRepository)(nil).DeleteByServiceID), ctx, serviceID)
}

// GetByServiceID mocks base method.
func (m *MockRepository) GetByServiceID(ctx context.Context, serviceID int64) ([]*model.ServiceAssociation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByServiceID", ctx, serviceID)
	ret0, _ := ret[0].([]*model.ServiceAssociation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByServiceID indicates an expected call of GetByServiceID.
func (mr *MockRepositoryMockRecorder) GetByServiceID(ctx, serviceID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByServiceID", reflect.TypeOf((*MockRepository)(nil).GetByServiceID), ctx, serviceID)
}

// ListByServiceIDs mocks base method.
func (m *MockRepository) ListByServiceIDs(ctx context.Context, serviceIDs []int64) ([]*model.ServiceAssociation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByServiceIDs", ctx, serviceIDs)
	ret0, _ := ret[0].([]*model.ServiceAssociation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByServiceIDs indicates an expected call of ListByServiceIDs.
func (mr *MockRepositoryMockRecorder) ListByServiceIDs(ctx, serviceIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByServiceIDs", reflect.TypeOf((*MockRepository)(nil).ListByServiceIDs), ctx, serviceIDs)
}

// WithTX mocks base method.
func (m *MockRepository) WithTX(q *query.Query) association.Repository {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTX", q)
	ret0, _ := ret[0].(association.Repository)
	return ret0
}

// WithTX indicates an expected call of WithTX.
func (mr *MockRepositoryMockRecorder) WithTX(q any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTX", reflect.TypeOf((*MockRepository)(nil).WithTX), q)
}
