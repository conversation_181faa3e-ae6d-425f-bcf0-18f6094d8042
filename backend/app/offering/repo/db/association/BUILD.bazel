load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "association",
    srcs = ["association.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/association",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/repo/db",
        "//backend/app/offering/repo/db/model",
        "//backend/app/offering/repo/db/query",
    ],
)
