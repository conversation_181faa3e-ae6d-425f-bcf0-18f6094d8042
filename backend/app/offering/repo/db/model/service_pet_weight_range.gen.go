// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameServicePetWeightRange = "service_pet_weight_range"

// ServicePetWeightRange mapped from table <service_pet_weight_range>
type ServicePetWeightRange struct {
	ID         int64      `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	ServiceID  int64      `gorm:"column:service_id;type:bigint;not null;index:idx_service_pet_weight_range_service_id,priority:1;comment:The ID of the service being configured" json:"service_id"` // The ID of the service being configured
	MinWeight  float64    `gorm:"column:min_weight;type:numeric(10,2);not null;comment:Minimum weight in pounds (inclusive)" json:"min_weight"`                                                     // Minimum weight in pounds (inclusive)
	MaxWeight  float64    `gorm:"column:max_weight;type:numeric(10,2);not null;comment:Maximum weight in pounds (inclusive)" json:"max_weight"`                                                     // Maximum weight in pounds (inclusive)
	IsAllRange bool       `gorm:"column:is_all_range;type:boolean;not null;comment:Whether this range represents all weights (used when is_all is true)" json:"is_all_range"`                       // Whether this range represents all weights (used when is_all is true)
	CreateTime *time.Time `gorm:"column:create_time;type:timestamp with time zone;not null;default:now()" json:"create_time"`
	UpdateTime *time.Time `gorm:"column:update_time;type:timestamp with time zone;not null;default:now()" json:"update_time"`
}

// TableName ServicePetWeightRange's table name
func (*ServicePetWeightRange) TableName() string {
	return TableNameServicePetWeightRange
}
