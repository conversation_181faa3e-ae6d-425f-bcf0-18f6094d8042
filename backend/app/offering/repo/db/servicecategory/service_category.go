package servicecategory

import (
	"context"
	"database/sql/driver"
	"time"

	"github.com/samber/lo"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	offeringutils "github.com/MoeGolibrary/moego/backend/app/offering/utils"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

//go:generate mockgen -package=mock -destination=mocks/mock_servicecategory_repo.go . Repository
type Repository interface {
	WithQuery(q *query.Query) Repository

	Create(ctx context.Context, serviceCategory *model.ServiceCategory) error
	Get(ctx context.Context, id int64) (*model.ServiceCategory, error)
	Update(ctx context.Context, serviceCategory *model.ServiceCategory, where *WhereCondition) error
	Delete(ctx context.Context, where *WhereCondition) error
	List(ctx context.Context,
		filter *ListServiceCategoryFilter,
		pagination *Pagination,
	) ([]*model.ServiceCategory, int64, error)
}

// repository implements the data access logic for ServiceCategory.
type repository struct {
	query *query.Query
}

// NewRepository creates a new repository.
func NewRepository() Repository {
	return &repository{query: query.Use(db.GetDB())}
}

// NewRepositoryWithQuery creates a new repository with a custom query.
func NewRepositoryWithQuery(q *query.Query) Repository {
	return &repository{query: q}
}

// WithQuery sets the query for the repository, useful for multi operation in one transaction.
func (r *repository) WithQuery(q *query.Query) Repository {
	return NewRepositoryWithQuery(q)
}

// Create creates a new service category.
func (r *repository) Create(ctx context.Context, serviceCategory *model.ServiceCategory) error {
	return r.query.ServiceCategory.WithContext(ctx).Create(serviceCategory)
}

// Get gets a service category by ID.
func (r *repository) Get(ctx context.Context, id int64) (*model.ServiceCategory, error) {
	return r.query.ServiceCategory.WithContext(ctx).
		Where(r.query.ServiceCategory.ID.Eq(id)).
		First()
}

// Update updates a service category with flexible where conditions.
func (r *repository) Update(ctx context.Context, serviceCategory *model.ServiceCategory, where *WhereCondition) error {
	q := r.query.ServiceCategory.WithContext(ctx)

	// Build where conditions
	if where.ID != 0 {
		q = q.Where(r.query.ServiceCategory.ID.Eq(where.ID))
	}
	if where.OrganizationType != nil {
		q = q.Where(r.query.ServiceCategory.OrganizationType.Eq(
			offeringutils.ToValuer(where.OrganizationType.String())))
	}
	if where.OrganizationID != nil {
		q = q.Where(r.query.ServiceCategory.OrganizationID.Eq(*where.OrganizationID))
	}

	// Filter out deleted records unless explicitly included
	if !where.IncludeDeleted {
		q = q.Where(r.query.ServiceCategory.DeleteTime.IsNull())
	}

	_, err := q.Updates(serviceCategory)
	if err != nil {
		return err
	}

	return nil
}

// Delete performs a soft delete operation on service categories based on where conditions.
func (r *repository) Delete(ctx context.Context, where *WhereCondition) error {
	q := r.query.ServiceCategory.WithContext(ctx)

	// Build where conditions
	if where.ID != 0 {
		q = q.Where(r.query.ServiceCategory.ID.Eq(where.ID))
	}
	if where.OrganizationType != nil {
		q = q.Where(r.query.ServiceCategory.OrganizationType.Eq(
			offeringutils.ToValuer(where.OrganizationType.String())))
	}
	if where.OrganizationID != nil {
		q = q.Where(r.query.ServiceCategory.OrganizationID.Eq(*where.OrganizationID))
	}

	// Filter out deleted records unless explicitly included
	if !where.IncludeDeleted {
		q = q.Where(r.query.ServiceCategory.DeleteTime.IsNull())
	}

	// Perform soft delete operation
	now := time.Now()
	updateData := map[string]interface{}{
		"delete_time": &now,
	}

	_, err := q.Updates(updateData)
	if err != nil {
		return err
	}

	return nil
}

// Pagination represents pagination parameters
type Pagination struct {
	Offset int32
	Limit  int32
}

// List lists service categories with optional pagination.
func (r *repository) List(
	ctx context.Context,
	filter *ListServiceCategoryFilter,
	pagination *Pagination,
) ([]*model.ServiceCategory, int64, error) {
	if filter == nil {
		return []*model.ServiceCategory{}, 0, nil
	}

	q := r.query.ServiceCategory.WithContext(ctx).
		Where(r.query.ServiceCategory.OrganizationType.Eq(offeringutils.ToValuer(filter.OrganizationType.String()))).
		Where(r.query.ServiceCategory.OrganizationID.Eq(filter.OrganizationID)).
		Where(r.query.ServiceCategory.DeleteTime.IsNull())

	if len(filter.CareTypeIDs) > 0 {
		q = q.Where(r.query.ServiceCategory.CareTypeID.In(filter.CareTypeIDs...))
	}
	if len(filter.Types) > 0 {
		types := lo.Map(filter.Types, func(t offeringpb.Service_Type, _ int) driver.Valuer {
			return offeringutils.ToValuer(t.String())
		})
		q = q.Where(r.query.ServiceCategory.Type.In(types...))
	}
	if len(filter.IDs) > 0 {
		q = q.Where(r.query.ServiceCategory.ID.In(filter.IDs...))
	}

	// Get total count if pagination is requested
	var total int64
	if pagination != nil {
		var err error
		total, err = q.Count()
		if err != nil {
			return nil, 0, err
		}
	}

	// Apply pagination if specified
	if pagination != nil && pagination.Limit > 0 {
		q = q.Offset(int(pagination.Offset)).Limit(int(pagination.Limit))
	}

	// Get results
	serviceCategories, err := q.Order(r.query.ServiceCategory.Sort.Desc()).Find()
	if err != nil {
		return nil, 0, err
	}

	// Return total count if pagination was requested, otherwise return 0
	if pagination != nil {
		return serviceCategories, total, nil
	}

	return serviceCategories, 0, nil
}
