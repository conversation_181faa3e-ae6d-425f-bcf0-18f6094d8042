package servicecategory

import (
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

// ListServiceCategoryFilter filter for listing service categories
type ListServiceCategoryFilter struct {
	// Required
	OrganizationType organizationpb.OrganizationType
	OrganizationID   int64

	// Optional
	CareTypeIDs []int64
}

// WhereCondition defines the where conditions for database operations
type WhereCondition struct {
	ID               int64
	OrganizationType *organizationpb.OrganizationType
	OrganizationID   *int64
	IncludeDeleted   bool // true to include deleted records, false to exclude them
}
