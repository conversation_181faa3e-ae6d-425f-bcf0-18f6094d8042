load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "servicecategory",
    srcs = [
        "entity.go",
        "service_category.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/servicecategory",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/repo/db",
        "//backend/app/offering/repo/db/model",
        "//backend/app/offering/repo/db/query",
        "//backend/app/offering/utils",
        "//backend/proto/organization/v1:organization",
    ],
)
