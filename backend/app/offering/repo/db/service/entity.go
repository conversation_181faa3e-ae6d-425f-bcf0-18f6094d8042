package service

import (
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
)

// ListServiceFilter filter for listing services
type ListServiceFilter struct {
	// Required
	OrganizationType organizationpb.OrganizationType
	OrganizationID   int64

	// Optional
	CareTypeIDs   []int64
	CategoriesIDs []int64
	Statuses      []offeringpb.Service_Status
	Sources       []offeringpb.Service_Source
	IDs           []int64
	IsDeleted     *bool

	// 关键词搜索（服务名称）
	Keyword *string
}
