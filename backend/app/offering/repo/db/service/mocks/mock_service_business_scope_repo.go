// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service (interfaces: BusinessScopeRepository)
//
// Generated by this command:
//
//	mockgen -package=mock -destination=mocks/mock_service_business_scope_repo.go . BusinessScopeRepository
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	model "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	query "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	service "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/service"
	gomock "go.uber.org/mock/gomock"
)

// MockBusinessScopeRepository is a mock of BusinessScopeRepository interface.
type MockBusinessScopeRepository struct {
	ctrl     *gomock.Controller
	recorder *MockBusinessScopeRepositoryMockRecorder
	isgomock struct{}
}

// MockBusinessScopeRepositoryMockRecorder is the mock recorder for MockBusinessScopeRepository.
type MockBusinessScopeRepositoryMockRecorder struct {
	mock *MockBusinessScopeRepository
}

// NewMockBusinessScopeRepository creates a new mock instance.
func NewMockBusinessScopeRepository(ctrl *gomock.Controller) *MockBusinessScopeRepository {
	mock := &MockBusinessScopeRepository{ctrl: ctrl}
	mock.recorder = &MockBusinessScopeRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBusinessScopeRepository) EXPECT() *MockBusinessScopeRepositoryMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m_2 *MockBusinessScopeRepository) Create(ctx context.Context, m *model.ServiceBusinessScope) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "Create", ctx, m)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockBusinessScopeRepositoryMockRecorder) Create(ctx, m any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockBusinessScopeRepository)(nil).Create), ctx, m)
}

// DeleteByServiceID mocks base method.
func (m *MockBusinessScopeRepository) DeleteByServiceID(ctx context.Context, serviceID int64) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteByServiceID", ctx, serviceID)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteByServiceID indicates an expected call of DeleteByServiceID.
func (mr *MockBusinessScopeRepositoryMockRecorder) DeleteByServiceID(ctx, serviceID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByServiceID", reflect.TypeOf((*MockBusinessScopeRepository)(nil).DeleteByServiceID), ctx, serviceID)
}

// GetByServiceID mocks base method.
func (m *MockBusinessScopeRepository) GetByServiceID(ctx context.Context, serviceID int64) (*model.ServiceBusinessScope, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByServiceID", ctx, serviceID)
	ret0, _ := ret[0].(*model.ServiceBusinessScope)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByServiceID indicates an expected call of GetByServiceID.
func (mr *MockBusinessScopeRepositoryMockRecorder) GetByServiceID(ctx, serviceID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByServiceID", reflect.TypeOf((*MockBusinessScopeRepository)(nil).GetByServiceID), ctx, serviceID)
}

// ListByServiceIDs mocks base method.
func (m *MockBusinessScopeRepository) ListByServiceIDs(ctx context.Context, serviceIDs []int64) ([]*model.ServiceBusinessScope, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByServiceIDs", ctx, serviceIDs)
	ret0, _ := ret[0].([]*model.ServiceBusinessScope)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByServiceIDs indicates an expected call of ListByServiceIDs.
func (mr *MockBusinessScopeRepositoryMockRecorder) ListByServiceIDs(ctx, serviceIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByServiceIDs", reflect.TypeOf((*MockBusinessScopeRepository)(nil).ListByServiceIDs), ctx, serviceIDs)
}

// Update mocks base method.
func (m_2 *MockBusinessScopeRepository) Update(ctx context.Context, m *model.ServiceBusinessScope) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "Update", ctx, m)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockBusinessScopeRepositoryMockRecorder) Update(ctx, m any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockBusinessScopeRepository)(nil).Update), ctx, m)
}

// WithQuery mocks base method.
func (m *MockBusinessScopeRepository) WithQuery(q *query.Query) service.BusinessScopeRepository {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithQuery", q)
	ret0, _ := ret[0].(service.BusinessScopeRepository)
	return ret0
}

// WithQuery indicates an expected call of WithQuery.
func (mr *MockBusinessScopeRepositoryMockRecorder) WithQuery(q any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithQuery", reflect.TypeOf((*MockBusinessScopeRepository)(nil).WithQuery), q)
}
