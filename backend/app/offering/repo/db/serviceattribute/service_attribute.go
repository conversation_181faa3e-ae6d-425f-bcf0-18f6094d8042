package serviceattribute

import (
	"context"
	"database/sql/driver"
	"time"

	"github.com/samber/lo"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
	offeringutils "github.com/MoeGolibrary/moego/backend/app/offering/utils"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

//go:generate mockgen -package=mock -destination=mocks/mock_service_attribute_repo.go . Repository
type Repository interface {
	WithQuery(q *query.Query) Repository

	BatchCreate(ctx context.Context, attrs []*model.ServiceAttribute) error
	Create(ctx context.Context, attr *model.ServiceAttribute) error
	Update(ctx context.Context, attr *model.ServiceAttribute) error
	DeleteByServiceID(ctx context.Context, serviceID int64, keys []offeringpb.AttributeKey) (int64, error)
	ListByServiceID(ctx context.Context, serviceID int64) ([]*model.ServiceAttribute, error)
	ListByServiceIDs(ctx context.Context, serviceIDs []int64) ([]*model.ServiceAttribute, error)
}

const (
	batchSize = 100
)

// repository implements the data access logic for ServiceAttribute.
type repository struct {
	query *query.Query
}

// NewRepository creates a new repository.
func NewRepository() Repository {
	return &repository{query: query.Use(db.GetDB())}
}

// WithQuery sets the query for the repository, useful for multi operation in one transaction.
func (r *repository) WithQuery(q *query.Query) Repository {
	return &repository{query: q}
}

// BatchCreate creates multiple service attribute values.
func (r *repository) BatchCreate(ctx context.Context, attrs []*model.ServiceAttribute) error {
	if len(attrs) == 0 {
		return nil
	}

	return r.query.ServiceAttribute.WithContext(ctx).CreateInBatches(attrs, batchSize)
}

// Create creates a new service attribute value.
func (r *repository) Create(ctx context.Context, attr *model.ServiceAttribute) error {
	return r.query.ServiceAttribute.WithContext(ctx).Create(attr)
}

// Update updates a service attribute value.
func (r *repository) Update(ctx context.Context, attr *model.ServiceAttribute) error {
	// 更新 UpdateTime 字段
	now := time.Now()
	attr.UpdateTime = &now

	_, err := r.query.ServiceAttribute.WithContext(ctx).
		Select(r.query.ServiceAttribute.AttributeValue,
			r.query.ServiceAttribute.UpdateTime).
		Where(r.query.ServiceAttribute.ID.Eq(attr.ID)).Updates(attr)
	if err != nil {
		return err
	}

	return nil
}

// DeleteByServiceID deletes service attribute values by service ID and optionally by attribute keys.
// If keys is nil or empty, deletes all attributes for the service.
// If keys is provided, deletes only the specified attributes.
func (r *repository) DeleteByServiceID(
	ctx context.Context, serviceID int64, keys []offeringpb.AttributeKey) (int64, error) {
	keyStrings := lo.Map(keys, func(key offeringpb.AttributeKey, _ int) driver.Valuer {
		return offeringutils.ToValuer(key.String())
	})
	query := r.query.ServiceAttribute.WithContext(ctx).
		Where(r.query.ServiceAttribute.ServiceID.Eq(serviceID)).
		Where(r.query.ServiceAttribute.AttributeKey.In(keyStrings...))

	result, err := query.UpdateColumn(r.query.ServiceAttribute.DeleteTime, time.Now())
	if err != nil {
		return 0, err
	}

	return result.RowsAffected, nil
}

// ListByServiceID lists service attribute values.
func (r *repository) ListByServiceID(ctx context.Context, serviceID int64) ([]*model.ServiceAttribute, error) {
	attrs, err := r.ListByServiceIDs(ctx, []int64{serviceID})
	if err != nil {
		return nil, err
	}

	if len(attrs) == 0 {
		return []*model.ServiceAttribute{}, nil
	}

	return attrs, nil
}

// ListByServiceIDs lists service attribute values for multiple services in one query.
func (r *repository) ListByServiceIDs(ctx context.Context, serviceIDs []int64) ([]*model.ServiceAttribute, error) {
	if len(serviceIDs) == 0 {
		return []*model.ServiceAttribute{}, nil
	}

	return r.query.ServiceAttribute.WithContext(ctx).
		Where(r.query.ServiceAttribute.ServiceID.In(serviceIDs...)).
		Where(r.query.ServiceAttribute.DeleteTime.IsNull()).Find()
}
