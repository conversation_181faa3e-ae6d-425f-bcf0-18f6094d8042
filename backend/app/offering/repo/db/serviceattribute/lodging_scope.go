package serviceattribute

import (
	"context"
	"time"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
)

//go:generate mockgen -package=mock -destination=mocks/mock_service_lodging_scope_repo.go . LodgingScopeRepository
type LodgingScopeRepository interface {
	WithQuery(q *query.Query) LodgingScopeRepository

	Create(ctx context.Context, m *model.ServiceLodgingScope) error
	GetByServiceID(ctx context.Context, serviceID int64) (*model.ServiceLodgingScope, error)
	ListByServiceIDs(ctx context.Context, serviceIDs []int64) ([]*model.ServiceLodgingScope, error)
	Update(ctx context.Context, m *model.ServiceLodgingScope) error
	DeleteByServiceID(ctx context.Context, serviceID int64) (int64, error)
	DeleteByServiceIDs(ctx context.Context, serviceIDs []int64) (int64, error)
}

// lodgingScopeRepository implements the data access logic for ServiceLodgingScope.
type lodgingScopeRepository struct {
	query *query.Query
}

// NewLodgingScopeRepository creates a new repository.
func NewLodgingScopeRepository() LodgingScopeRepository {
	return &lodgingScopeRepository{query: query.Use(db.GetDB())}
}

// WithQuery sets the query for the repository, useful for multi operation in one transaction.
func (r *lodgingScopeRepository) WithQuery(q *query.Query) LodgingScopeRepository {
	return &lodgingScopeRepository{query: q}
}

// Create creates a new service lodging scope.
func (r *lodgingScopeRepository) Create(
	ctx context.Context, scope *model.ServiceLodgingScope) error {
	return r.query.ServiceLodgingScope.WithContext(ctx).Create(scope)
}

// GetByServiceID gets service lodging scope by service ID.
func (r *lodgingScopeRepository) GetByServiceID(
	ctx context.Context, serviceID int64) (*model.ServiceLodgingScope, error) {
	scopes, err := r.ListByServiceIDs(ctx, []int64{serviceID})
	if err != nil {
		return nil, err
	}

	if len(scopes) == 0 {
		return nil, nil
	}

	return scopes[0], nil
}

// ListByServiceIDs list service lodging scope by multiple service IDs.
func (r *lodgingScopeRepository) ListByServiceIDs(
	ctx context.Context, serviceIDs []int64) ([]*model.ServiceLodgingScope, error) {
	if len(serviceIDs) == 0 {
		return []*model.ServiceLodgingScope{}, nil
	}

	return r.query.ServiceLodgingScope.WithContext(ctx).
		Where(r.query.ServiceLodgingScope.ServiceID.In(serviceIDs...)).
		Where(r.query.ServiceLodgingScope.DeleteTime.IsNull()).Find()
}

// Update updates a service lodging scope.
func (r *lodgingScopeRepository) Update(
	ctx context.Context, scope *model.ServiceLodgingScope) error {
	// 更新 UpdateTime 字段
	now := time.Now()
	scope.UpdateTime = &now

	_, err := r.query.ServiceLodgingScope.WithContext(ctx).
		Select(r.query.ServiceLodgingScope.IsAllLodging,
			r.query.ServiceLodgingScope.AvailableLodgingTypeIds,
			r.query.ServiceLodgingScope.UpdateTime).
		Where(r.query.ServiceLodgingScope.ServiceID.Eq(scope.ServiceID)).
		Updates(scope)

	return err
}

// DeleteByServiceID deletes service lodging scope by service ID.
func (r *lodgingScopeRepository) DeleteByServiceID(
	ctx context.Context, serviceID int64) (int64, error) {
	return r.DeleteByServiceIDs(ctx, []int64{serviceID})
}

// DeleteByServiceIDs deletes service lodging scope by multiple service IDs.
func (r *lodgingScopeRepository) DeleteByServiceIDs(
	ctx context.Context, serviceIDs []int64) (int64, error) {
	if len(serviceIDs) == 0 {
		return 0, nil
	}

	result, err := r.query.ServiceLodgingScope.WithContext(ctx).
		Where(r.query.ServiceLodgingScope.ServiceID.In(serviceIDs...)).
		UpdateColumn(r.query.ServiceLodgingScope.DeleteTime, time.Now())
	if err != nil {
		return 0, err
	}

	return result.RowsAffected, nil
}
