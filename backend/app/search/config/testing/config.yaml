secrets:
  - name: 'moego/testing/opensearch'
    prefix: 'secret.opensearch.'
    option: false
server:
  filter:
    - recovery
    - debuglog
  service:
    - name: backend.proto.search.v1.SearchService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 60000
client:
  network: tcp
  protocol: grpc
  filter:
    - debuglog
  transport: grpc
  timeout: 60000
  service:
    - callee: greeter_cutomized_name
      target: ip://127.0.0.1:9090
      protocol: grpc
      network: tcp
      transport: grpc
plugins:
  database:
    gorm:
      max_idle: 20
      max_open: 100
      max_lifetime: 180000 # millisecond
      service:
        - name: postgres.t2.moego.dev
          max_idle: 10
          max_open: 50
          max_lifetime: 18000
opensearch:
  endpoint: ${secret.opensearch.endpoint}
  accessKey: ${secret.opensearch.operator.accessKey}
  secretKey: ${secret.opensearch.operator.secretKey}