package service

import (
	"context"

	"go.uber.org/zap"

	"github.com/MoeGolibrary/moego/backend/app/search/logic/document"
	"github.com/MoeGolibrary/moego/backend/app/search/logic/search"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	searchpb "github.com/MoeGolibrary/moego/backend/proto/search/v1"
)

type Search struct {
	searchpb.UnimplementedSearchServiceServer
	search   *search.Logic
	document *document.Logic
}

func NewSearch() *Search {
	return &Search{
		search:   search.New(),
		document: document.New(),
	}
}

func (s *Search) SearchDocument(ctx context.Context,
	req *searchpb.SearchDocumentRequest) (*searchpb.SearchDocumentResponse, error) {
	log.DebugContext(ctx, "search document", zap.Any("req", req))

	return s.search.Search(ctx, req.GetIndex(), req)
}

func (s *Search) BulkDocument(ctx context.Context,
	req *searchpb.BulkDocumentRequest) (*searchpb.BulkDocumentResponse, error) {
	log.DebugContext(ctx, "bulk document", zap.Any("req", req))

	return s.document.Bulk(ctx, req)
}
