package opensearch

import (
	"bytes"
	"context"
	"net/http"

	"github.com/bytedance/sonic"
	"github.com/opensearch-project/opensearch-go/opensearchapi"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type IndexDocumentRequest struct {
	Index        string         `json:"index"`
	DocumentID   string         `json:"document_id"`
	DocumentType string         `json:"document_type"`
	Document     map[string]any `json:"document"`
}

func (i *impl) IndexDocument(ctx context.Context, request *IndexDocumentRequest) error {
	doc, err := sonic.Marshal(request.Document)
	if err != nil {
		log.ErrorContext(ctx, "failed to marshal document", zap.Error(err), zap.Any("request", request))

		return status.Errorf(codes.Internal, "failed to marshal document")
	}

	req := opensearchapi.IndexRequest{
		Index:        request.Index,
		DocumentID:   request.DocumentID,
		DocumentType: request.DocumentType,
		Body:         bytes.NewReader(doc),
	}

	response, err := req.Do(ctx, i.client)
	if err != nil {
		log.ErrorContext(ctx, "failed to index document", zap.Error(err), zap.Any("response", response))

		return status.Errorf(codes.Internal, "failed to index document")
	}

	if response.IsError() {
		log.ErrorContext(ctx, "failed to index document", zap.Any("response", response))

		return status.Errorf(codes.Internal, "failed to index document")
	}

	return nil
}

type BulkResponse struct {
	Took   int                            `json:"took"`
	Errors bool                           `json:"errors"`
	Items  []map[string]*BulkResponseItem `json:"items"`
}

type BulkResponseItem struct {
	Index   string                  `json:"_index"`
	ID      string                  `json:"_id"`
	Version int                     `json:"_version"`
	Result  string                  `json:"result"`
	Status  int                     `json:"status"`
	Error   *BulkResponseItemError  `json:"error,omitempty"`
	Shards  *BulkResponseItemShards `json:"_shards,omitempty"`
}

type BulkResponseItemError struct {
	Type   string `json:"type"`
	Reason string `json:"reason"`
	Index  string `json:"index"`
	Shard  string `json:"shard"`
}

type BulkResponseItemShards struct {
	Total      int `json:"total"`
	Successful int `json:"successful"`
	Failed     int `json:"failed"`
}

func (i *impl) Bulk(ctx context.Context, bulkreq *opensearchapi.BulkRequest) (*BulkResponse, error) {
	resp, err := bulkreq.Do(ctx, i.client)
	if err != nil {
		log.ErrorContext(ctx, "failed to bulk request", zap.Error(err), zap.Any("response", resp))

		return nil, status.Errorf(codes.Internal, "failed to bulk request")
	}

	if resp.StatusCode >= http.StatusBadRequest {
		log.ErrorContext(ctx, "failed to bulk request", zap.Any("response", resp))

		return nil, status.Errorf(codes.Internal, "failed to bulk request, reason: %s", resp)
	}

	return bulkResponse(ctx, resp)
}

func bulkResponse(ctx context.Context, resp *opensearchapi.Response) (*BulkResponse, error) {
	var bulkResponse BulkResponse
	if err := sonic.ConfigDefault.NewDecoder(resp.Body).Decode(&bulkResponse); err != nil {
		return nil, status.Errorf(codes.Internal, "failed to parse response: %v", err)
	}
	log.DebugContext(ctx, "bulk response", zap.Any("response", bulkResponse))

	return &bulkResponse, nil
}
