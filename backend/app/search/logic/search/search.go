package search

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/search/repo/opensearch"
	searchpb "github.com/MoeGolibrary/moego/backend/proto/search/v1"
)

type Logic struct {
	opensearch opensearch.OpenSearch
	converter  *Converter
}

func New() *Logic {
	return &Logic{
		opensearch: opensearch.New(),
		converter:  &Converter{},
	}
}

func NewMock(opensearch opensearch.OpenSearch) *Logic {
	return &Logic{
		opensearch: opensearch,
		converter:  &Converter{},
	}
}

func (l *Logic) Search(ctx context.Context, index string,
	req *searchpb.SearchDocumentRequest) (*searchpb.SearchDocumentResponse, error) {
	request, err := l.converter.BuildRequest(ctx, req)
	if err != nil {
		return nil, err
	}

	resp, err := l.opensearch.Search(ctx, index, request)
	if err != nil {
		return nil, err
	}

	return l.converter.Convert(ctx, resp)
}
