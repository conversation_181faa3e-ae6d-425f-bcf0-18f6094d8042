package search_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/structpb"

	"github.com/MoeGolibrary/moego/backend/app/search/logic/search"
	"github.com/MoeGolibrary/moego/backend/app/search/repo/opensearch"
	"github.com/MoeGolibrary/moego/backend/app/search/repo/opensearch/mock"
	searchpb "github.com/MoeGolibrary/moego/backend/proto/search/v1"
)

const (
	IndexName = "search-index-test-jett"
)

func NewMockLogic(os opensearch.OpenSearch) *search.Logic {
	return search.NewMock(os)
}

func TestNew(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	os := mock.NewMockOpenSearch(ctrl)
	opensearch.SetGlobalOpenSearch(os)
	search := search.New()
	t.Log(search)
}

func TestError(t *testing.T) {
	t.Run("test search error", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		os := mock.NewMockOpenSearch(ctrl)
		os.EXPECT().
			Search(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, status.Errorf(codes.InvalidArgument, "search error"))
		search := NewMockLogic(os)
		resp, err := search.Search(ctx, IndexName, &searchpb.SearchDocumentRequest{
			Strategy: &searchpb.Strategy{
				Strategy: &searchpb.Strategy_Term{
					Term: &searchpb.TermStrategy{
						Field: "name",
						Value: &structpb.Value{
							Kind: &structpb.Value_StringValue{
								StringValue: "jett",
							},
						},
					},
				},
			},
		})
		require.Error(t, err)
		require.Nil(t, resp)
		require.Contains(t, err.Error(), "search error")
	})

	t.Run("test strategy error", func(t *testing.T) {
		ctx := context.Background()
		search := NewMockLogic(nil)
		resp, err := search.Search(ctx, IndexName, &searchpb.SearchDocumentRequest{
			Strategy: &searchpb.Strategy{
				Strategy: nil,
			},
		})
		require.Error(t, err)
		require.Nil(t, resp)
		require.Contains(t, err.Error(), "invalid strategy")
	})
}

func TestTermSearch(t *testing.T) {
	t.Run("Term search success", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		os := mock.NewMockOpenSearch(ctrl)
		os.EXPECT().
			Search(gomock.Any(), gomock.Any(), gomock.AssignableToTypeOf(&opensearch.SearchRequest{})).
			DoAndReturn(func(_ context.Context, _ string,
				request *opensearch.SearchRequest) (*opensearch.SearchResponse, error) {
				require.Equal(t, map[string]any{
					"term": map[string]any{
						"name": "jett",
					},
				}, request.Query)
				require.Equal(t, []map[string]any{
					{
						"age": "DESC",
					},
				}, request.Sort)
				require.Equal(t, int32(10), request.Size)
				require.Equal(t, []any{float64(5)}, request.SearchAfter)

				return &opensearch.SearchResponse{
					Took:     1,
					TimedOut: false,
					Hits: &opensearch.Hits{
						Total: &opensearch.SearchHitsTotal{
							Value:    1,
							Relation: "eq",
						},
						MaxScore: 1.0,
						Hits: []*opensearch.SearchHit{
							{
								Index: IndexName,
								ID:    "1",
								Score: 1.0,
								Source: map[string]any{
									"name": "jett",
									"age":  5,
									"sort": []int{4},
								},
							},
						},
					},
				}, nil
			})

		search := NewMockLogic(os)
		resp, err := search.Search(ctx, IndexName, &searchpb.SearchDocumentRequest{
			Strategy: &searchpb.Strategy{
				Strategy: &searchpb.Strategy_Term{
					Term: &searchpb.TermStrategy{
						Field: "name",
						Value: &structpb.Value{
							Kind: &structpb.Value_StringValue{
								StringValue: "jett",
							},
						},
					},
				},
			},
			Sort: []*searchpb.SearchDocumentRequest_Sort{
				{
					Field: "age",
					Order: searchpb.SearchDocumentRequest_Sort_DESC,
				},
			},
			SearchAfter: []*structpb.Value{
				{
					Kind: &structpb.Value_NumberValue{
						NumberValue: 5,
					},
				},
			},
			PageSize: 10,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Equal(t, int32(1), resp.Total.Value)
		require.Equal(t, "jett", resp.GetHits()[0].Source.AsMap()["name"])
		require.Equal(t, 1.0, resp.GetHits()[0].Score)
		require.Equal(t, IndexName, resp.GetHits()[0].Index)
		require.Equal(t, "1", resp.GetHits()[0].Id)
	})

	t.Run("Term search not found", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		os := mock.NewMockOpenSearch(ctrl)
		os.EXPECT().
			Search(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&opensearch.SearchResponse{
				Took:     1,
				TimedOut: false,
				Hits: &opensearch.Hits{
					Total: &opensearch.SearchHitsTotal{
						Value:    0,
						Relation: "eq",
					},
					MaxScore: 0,
					Hits:     []*opensearch.SearchHit{},
				},
			}, nil)
		search := NewMockLogic(os)
		resp, err := search.Search(ctx, IndexName, &searchpb.SearchDocumentRequest{
			Strategy: &searchpb.Strategy{
				Strategy: &searchpb.Strategy_Term{
					Term: &searchpb.TermStrategy{
						Field: "name",
						Value: &structpb.Value{
							Kind: &structpb.Value_StringValue{
								StringValue: "jett-4396",
							},
						},
					},
				},
			},
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Equal(t, int32(0), resp.Total.Value)
		require.Equal(t, "eq", resp.Total.Relation)
		require.Equal(t, 0.0, resp.MaxScore)
		require.Equal(t, 0, len(resp.Hits))
	})

	t.Run("Term search keyword field", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		os := mock.NewMockOpenSearch(ctrl)
		os.EXPECT().
			Search(gomock.Any(), gomock.Any(), gomock.AssignableToTypeOf(&opensearch.SearchRequest{})).
			DoAndReturn(func(_ context.Context, _ string,
				request *opensearch.SearchRequest) (*opensearch.SearchResponse, error) {
				require.Equal(t, map[string]any{
					"term": map[string]any{
						"name.keyword": "jett-4396",
					},
				}, request.Query)
				require.Equal(t, []map[string]any{
					{
						"age": "DESC",
					},
				}, request.Sort)
				require.Equal(t, int32(10), request.Size)
				require.Equal(t, []any{float64(5)}, request.SearchAfter)

				return &opensearch.SearchResponse{
					Took:     1,
					TimedOut: false,
					Hits: &opensearch.Hits{
						Total: &opensearch.SearchHitsTotal{
							Value:    1,
							Relation: "eq",
						},
						MaxScore: 1.0,
						Hits: []*opensearch.SearchHit{
							{
								Index: IndexName,
								ID:    "1",
								Score: 1.0,
								Source: map[string]any{
									"name": "jett-4396",
									"age":  5,
									"sort": []int{4},
								},
							},
						},
					},
				}, nil
			})
		search := NewMockLogic(os)

		resp, err := search.Search(ctx, IndexName, &searchpb.SearchDocumentRequest{
			Strategy: &searchpb.Strategy{
				Strategy: &searchpb.Strategy_Term{
					Term: &searchpb.TermStrategy{
						Field: "name.keyword",
						Value: &structpb.Value{
							Kind: &structpb.Value_StringValue{
								StringValue: "jett-4396",
							},
						},
					},
				},
			},
			Sort: []*searchpb.SearchDocumentRequest_Sort{
				{
					Field: "age",
					Order: searchpb.SearchDocumentRequest_Sort_DESC,
				},
			},
			SearchAfter: []*structpb.Value{
				{
					Kind: &structpb.Value_NumberValue{
						NumberValue: 5,
					},
				},
			},
			PageSize: 10,
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Equal(t, int32(1), resp.Total.Value)
		require.Equal(t, "jett-4396", resp.GetHits()[0].Source.AsMap()["name"])
		require.Equal(t, 1.0, resp.GetHits()[0].Score)
		require.Equal(t, IndexName, resp.GetHits()[0].Index)
		require.Equal(t, "1", resp.GetHits()[0].Id)
	})
}

func TestMatchSearch(t *testing.T) {
	t.Run("Match search success", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		os := mock.NewMockOpenSearch(ctrl)
		os.EXPECT().
			Search(gomock.Any(), gomock.Any(), gomock.AssignableToTypeOf(&opensearch.SearchRequest{})).
			DoAndReturn(func(_ context.Context, _ string,
				request *opensearch.SearchRequest) (*opensearch.SearchResponse, error) {
				require.Equal(t, map[string]any{
					"match": map[string]any{
						"name": map[string]any{
							"query":    "chi 5805",
							"operator": search.MatchOperatorOr,
							"boost":    float32(0),
						},
					},
				}, request.Query)

				return &opensearch.SearchResponse{
					Took:     1,
					TimedOut: false,
					Hits: &opensearch.Hits{
						Total: &opensearch.SearchHitsTotal{
							Value:    2,
							Relation: "eq",
						},
						MaxScore: 1.0,
						Hits: []*opensearch.SearchHit{
							{
								Index: IndexName,
								ID:    "ESW05JQBB-Y1tVESGOvt",
								Score: 5.753438,
								Source: map[string]any{
									"name": "chi-5805",
									"age":  10,
									"city": "nanjing",
									"tags": []string{
										"nanjing",
										"consultant",
										"tall",
									},
									"jobs":  "consultant",
									"wages": 103999,
									"meta": map[string]any{
										"created_at": "2025-02-08T16:36:35+08:00",
										"updated_at": "2025-02-08T16:36:35+08:00",
									},
								},
							},
							{
								Index: IndexName,
								ID:    "EyW05JQBB-Y1tVESGuvj",
								Score: 2.2270775,
								Source: map[string]any{
									"name": "chi-7316",
									"age":  14,
									"city": "shenzhen",
									"tags": []string{
										"shenzhen",
										"director",
										"dumb",
									},
									"jobs":  "director",
									"wages": 105156,
									"meta": map[string]any{
										"created_at": "2025-02-08T16:36:36+08:00",
										"updated_at": "2025-02-08T16:36:36+08:00",
									},
								},
							},
						},
					},
				}, nil
			})
		search := NewMockLogic(os)
		resp, err := search.Search(ctx, IndexName, &searchpb.SearchDocumentRequest{
			Strategy: &searchpb.Strategy{
				Strategy: &searchpb.Strategy_Match{
					Match: &searchpb.MatchStrategy{
						Field:    "name",
						Query:    "chi 5805",
						Operator: searchpb.MatchStrategy_OR,
					},
				},
			},
			Sort: []*searchpb.SearchDocumentRequest_Sort{
				{
					Field: "age",
					Order: searchpb.SearchDocumentRequest_Sort_DESC,
				},
			},
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Equal(t, int32(2), resp.Total.Value)
		require.Equal(t, "chi-5805", resp.GetHits()[0].Source.AsMap()["name"])
		require.Equal(t, 5.753438, resp.GetHits()[0].Score)
		require.Equal(t, IndexName, resp.GetHits()[0].Index)
		require.Equal(t, "ESW05JQBB-Y1tVESGOvt", resp.GetHits()[0].Id)
		require.Equal(t, "chi-7316", resp.GetHits()[1].Source.AsMap()["name"])
		require.Equal(t, 2.2270775, resp.GetHits()[1].Score)
		require.Equal(t, IndexName, resp.GetHits()[1].Index)
		require.Equal(t, "EyW05JQBB-Y1tVESGuvj", resp.GetHits()[1].Id)
	})
}

func TestRangeSearch(t *testing.T) {
	t.Run("Range search success", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		os := mock.NewMockOpenSearch(ctrl)
		os.EXPECT().
			Search(gomock.Any(), gomock.Any(), gomock.AssignableToTypeOf(&opensearch.SearchRequest{})).
			DoAndReturn(func(_ context.Context, _ string,
				request *opensearch.SearchRequest) (*opensearch.SearchResponse, error) {
				require.Equal(t, map[string]any{
					"range": map[string]any{
						"age": map[string]any{
							"gte": float64(10),
							"lte": float64(20),
						},
					},
				}, request.Query)

				return &opensearch.SearchResponse{
					Took:     1,
					TimedOut: false,
					Hits: &opensearch.Hits{
						Total: &opensearch.SearchHitsTotal{
							Value:    2,
							Relation: "eq",
						},
						MaxScore: 1.0,
						Hits: []*opensearch.SearchHit{
							{
								Index: IndexName,
								ID:    "HCW05JQBB-Y1tVESJOvV",
								Score: 1.0,
								Source: map[string]any{
									"name": "kuroko-3622",
									"age":  11,
									"city": "wuhan",
									"tags": []string{
										"wuhan",
										"designer",
										"handsome",
									},
									"jobs":  "designer",
									"wages": 115080,
									"meta": map[string]any{
										"created_at": "2025-02-08T16:36:38+08:00",
										"updated_at": "2025-02-08T16:36:38+08:00",
									},
								},
							},
							{
								Index: IndexName,
								ID:    "HiW05JQBB-Y1tVESJuvC",
								Score: 1.0,
								Source: map[string]any{
									"name": "perqin-8260",
									"age":  12,
									"city": "shanghai",
									"tags": []string{
										"shanghai",
										"designer",
										"rich",
									},
									"jobs":  "designer",
									"wages": 100606,
									"meta": map[string]any{
										"created_at": "2025-02-08T16:36:39+08:00",
										"updated_at": "2025-02-08T16:36:39+08:00",
									},
								},
							},
						},
					},
				}, nil
			})
		search := NewMockLogic(os)
		resp, err := search.Search(ctx, IndexName, &searchpb.SearchDocumentRequest{
			Strategy: &searchpb.Strategy{
				Strategy: &searchpb.Strategy_Range{
					Range: &searchpb.RangeStrategy{
						Field: "age",
						Gte: &structpb.Value{
							Kind: &structpb.Value_NumberValue{
								NumberValue: 10,
							},
						},
						Lte: &structpb.Value{
							Kind: &structpb.Value_NumberValue{
								NumberValue: 20,
							},
						},
					},
				},
			},
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Equal(t, int32(2), resp.Total.Value)
		require.Equal(t, 1.0, resp.GetHits()[0].Score)
		require.Equal(t, 1.0, resp.GetHits()[1].Score)
		require.Equal(t, IndexName, resp.GetHits()[0].Index)
		require.Equal(t, "HCW05JQBB-Y1tVESJOvV", resp.GetHits()[0].Id)
		require.Equal(t, IndexName, resp.GetHits()[1].Index)
		require.Equal(t, "HiW05JQBB-Y1tVESJuvC", resp.GetHits()[1].Id)
	})
}

func TestWildcardSearch(t *testing.T) {
	t.Run("Wildcard search success", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		os := mock.NewMockOpenSearch(ctrl)
		os.EXPECT().
			Search(gomock.Any(), gomock.Any(), gomock.AssignableToTypeOf(&opensearch.SearchRequest{})).
			DoAndReturn(func(_ context.Context, _ string,
				request *opensearch.SearchRequest) (*opensearch.SearchResponse, error) {
				require.Equal(t, map[string]any{
					"wildcard": map[string]any{
						"name": "chi*",
					},
				}, request.Query)

				return &opensearch.SearchResponse{
					Took:     1,
					TimedOut: false,
					Hits: &opensearch.Hits{
						Total: &opensearch.SearchHitsTotal{
							Value:    1,
							Relation: "eq",
						},
						MaxScore: 1.0,
						Hits: []*opensearch.SearchHit{
							{
								Index: IndexName,
								ID:    "HCW05JQBB-Y1tVESJOvV",
								Score: 1.0,
								Source: map[string]any{
									"name": "chi-5805",
									"age":  10,
									"city": "wuhan",
									"tags": []string{
										"wuhan",
										"designer",
										"handsome",
									},
									"jobs":  "designer",
									"wages": 115080,
									"meta": map[string]any{
										"created_at": "2025-02-08T16:36:38+08:00",
										"updated_at": "2025-02-08T16:36:38+08:00",
									},
								},
							},
						},
					},
				}, nil
			})
		search := NewMockLogic(os)
		resp, err := search.Search(ctx, IndexName, &searchpb.SearchDocumentRequest{
			Strategy: &searchpb.Strategy{
				Strategy: &searchpb.Strategy_Wildcard{
					Wildcard: &searchpb.WildcardStrategy{
						Field: "name",
						Value: "chi*",
					},
				},
			},
		})
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Equal(t, int32(1), resp.Total.Value)
		require.Equal(t, 1.0, resp.GetHits()[0].Score)
		require.Equal(t, IndexName, resp.GetHits()[0].Index)
		require.Equal(t, "HCW05JQBB-Y1tVESJOvV", resp.GetHits()[0].Id)
		require.Equal(t, "chi-5805", resp.GetHits()[0].Source.AsMap()["name"])
		require.Equal(t, float64(10), resp.GetHits()[0].Source.AsMap()["age"])
	})
}

func TestBoolSearch(t *testing.T) {
	t.Run("bool search", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		os := mock.NewMockOpenSearch(ctrl)
		os.EXPECT().
			Search(gomock.Any(), gomock.Any(), gomock.AssignableToTypeOf(&opensearch.SearchRequest{})).
			DoAndReturn(func(_ context.Context, _ string,
				request *opensearch.SearchRequest) (*opensearch.SearchResponse, error) {
				require.Equal(t, map[string]any{
					"bool": map[string]any{
						"must": []map[string]any{
							{
								"term": map[string]any{
									"city": "wuhan",
								},
							},
						},
						"should": []map[string]any{
							{
								"range": map[string]any{
									"age": map[string]any{
										"gte": float64(10),
										"lte": float64(20),
									},
								},
							},
						},
						"minimum_should_match": 1,
						"filter": []map[string]any{
							{
								"terms": map[string]any{
									"tags": []any{"designer"},
								},
							},
						},
					},
				}, request.Query)

				return &opensearch.SearchResponse{
					Took:     1,
					TimedOut: false,
					Hits: &opensearch.Hits{
						Total: &opensearch.SearchHitsTotal{
							Value:    1,
							Relation: "eq",
						},
						MaxScore: 1.0,
						Hits: []*opensearch.SearchHit{
							{
								Index: IndexName,
								ID:    "HCW05JQBB-Y1tVESJOvV",
								Score: 1.0,
								Source: map[string]any{
									"name": "chi-5805",
									"age":  10,
									"city": "wuhan",
									"tags": []string{
										"wuhan",
										"designer",
										"handsome",
									},
									"jobs":  "designer",
									"wages": 115080,
									"meta": map[string]any{
										"created_at": "2025-02-08T16:36:38+08:00",
										"updated_at": "2025-02-08T16:36:38+08:00",
									},
								},
							},
						},
					},
				}, nil
			})

		search := NewMockLogic(os)
		resp, err := search.Search(ctx, IndexName, &searchpb.SearchDocumentRequest{
			Strategy: &searchpb.Strategy{
				Strategy: &searchpb.Strategy_Bool{
					Bool: &searchpb.BoolStrategy{
						Must: []*searchpb.Strategy{
							{
								Strategy: &searchpb.Strategy_Term{
									Term: &searchpb.TermStrategy{
										Field: "city",
										Value: structpb.NewStringValue("wuhan"),
									},
								},
							},
						},
						Should: &searchpb.BoolStrategy_BoolShouldStrategy{
							Strategies: []*searchpb.Strategy{
								{
									Strategy: &searchpb.Strategy_Range{
										Range: &searchpb.RangeStrategy{
											Field: "age",
											Gte:   structpb.NewNumberValue(10),
											Lte:   structpb.NewNumberValue(20),
										},
									},
								},
							},
							MinimumMatch: 1,
						},
						Filter: []*searchpb.Strategy{
							{
								Strategy: &searchpb.Strategy_Terms{
									Terms: &searchpb.TermsStrategy{
										Field:  "tags",
										Values: []*structpb.Value{structpb.NewStringValue("designer")},
									},
								},
							},
						},
					},
				},
			},
		})

		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Equal(t, int32(1), resp.Total.Value)
		require.Equal(t, 1.0, resp.GetHits()[0].Score)
		require.Equal(t, IndexName, resp.GetHits()[0].Index)
		require.Equal(t, "HCW05JQBB-Y1tVESJOvV", resp.GetHits()[0].Id)
		require.Equal(t, "chi-5805", resp.GetHits()[0].Source.AsMap()["name"])
		require.Equal(t, float64(10), resp.GetHits()[0].Source.AsMap()["age"])
		require.Equal(t, "wuhan", resp.GetHits()[0].Source.AsMap()["city"])
		require.Equal(t, "designer", resp.GetHits()[0].Source.AsMap()["jobs"])
		require.Equal(t, float64(115080), resp.GetHits()[0].Source.AsMap()["wages"])
	})
}

func TestMultiMatchSearch(t *testing.T) {
	t.Run("multi match search", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		os := mock.NewMockOpenSearch(ctrl)
		os.EXPECT().
			Search(gomock.Any(), gomock.Any(), gomock.AssignableToTypeOf(&opensearch.SearchRequest{})).
			DoAndReturn(func(_ context.Context, _ string,
				request *opensearch.SearchRequest) (*opensearch.SearchResponse, error) {
				require.Equal(t, map[string]any{
					"multi_match": map[string]any{
						"query":  "test",
						"fields": []string{"name", "age"},
						"type":   "best_fields",
					},
				}, request.Query)

				return &opensearch.SearchResponse{
					Took:     1,
					TimedOut: false,
					Hits: &opensearch.Hits{
						Total: &opensearch.SearchHitsTotal{
							Value:    1,
							Relation: "eq",
						},
						MaxScore: 1.0,
						Hits: []*opensearch.SearchHit{
							{
								Index: IndexName,
								ID:    "test-id",
								Score: 1.0,
								Source: map[string]any{
									"name":  "test",
									"age":   10,
									"score": 95.5,
									"tags":  []string{"tag1", "tag2"},
									"meta": map[string]any{
										"created_at": "2024-03-20",
										"numbers":    []int{1, 2, 3},
									},
								},
							},
						},
					},
				}, nil
			})

		search := NewMockLogic(os)
		resp, err := search.Search(ctx, IndexName, &searchpb.SearchDocumentRequest{
			Strategy: &searchpb.Strategy{
				Strategy: &searchpb.Strategy_MultiMatch{
					MultiMatch: &searchpb.MultiMatchStrategy{
						Query:  "test",
						Fields: []string{"name", "age"},
						Type:   searchpb.MultiMatchStrategy_BEST_FIELDS,
					},
				},
			},
		})

		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Equal(t, int32(1), resp.Total.Value)
		require.Equal(t, 1.0, resp.GetHits()[0].Score)
		require.Equal(t, IndexName, resp.GetHits()[0].Index)
		require.Equal(t, "test-id", resp.GetHits()[0].Id)
		require.Equal(t, "test", resp.GetHits()[0].Source.AsMap()["name"])
		require.Equal(t, float64(10), resp.GetHits()[0].Source.AsMap()["age"])
	})
}

func TestMatchPhraseSearch(t *testing.T) {
	t.Run("match phrase search", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		os := mock.NewMockOpenSearch(ctrl)
		os.EXPECT().
			Search(gomock.Any(), gomock.Any(), gomock.AssignableToTypeOf(&opensearch.SearchRequest{})).
			DoAndReturn(func(_ context.Context, _ string,
				request *opensearch.SearchRequest) (*opensearch.SearchResponse, error) {
				require.Equal(t, map[string]any{
					"match_phrase": map[string]any{
						"content": map[string]any{
							"query": "test",
						},
					},
				}, request.Query)

				return &opensearch.SearchResponse{
					Took:     1,
					TimedOut: false,
					Hits: &opensearch.Hits{
						Total: &opensearch.SearchHitsTotal{
							Value:    1,
							Relation: "eq",
						},
						MaxScore: 1.0,
						Hits: []*opensearch.SearchHit{
							{
								Index: IndexName,
								ID:    "test-id",
								Score: 1.0,
								Source: map[string]any{
									"content": "test",
								},
							},
						},
					},
				}, nil
			})

		search := NewMockLogic(os)
		resp, err := search.Search(ctx, IndexName, &searchpb.SearchDocumentRequest{
			Strategy: &searchpb.Strategy{
				Strategy: &searchpb.Strategy_MatchPhrase{
					MatchPhrase: &searchpb.MatchPhraseStrategy{
						Field: "content",
						Query: "test",
					},
				},
			},
		})

		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Equal(t, int32(1), resp.Total.Value)
		require.Equal(t, 1.0, resp.GetHits()[0].Score)
	})
}

func TestQueryStringSearch(t *testing.T) {
	t.Run("QueryString search success", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		os := mock.NewMockOpenSearch(ctrl)
		os.EXPECT().
			Search(gomock.Any(), gomock.Any(), gomock.AssignableToTypeOf(&opensearch.SearchRequest{})).
			DoAndReturn(func(_ context.Context, _ string,
				request *opensearch.SearchRequest) (*opensearch.SearchResponse, error) {
				require.Equal(t, map[string]any{
					"query_string": map[string]any{
						"query":  "test AND example",
						"fields": []string{"title", "content"},
					},
				}, request.Query)

				return &opensearch.SearchResponse{
					Took:     1,
					TimedOut: false,
					Hits: &opensearch.Hits{
						Total: &opensearch.SearchHitsTotal{
							Value:    2,
							Relation: "eq",
						},
						MaxScore: 1.5,
						Hits: []*opensearch.SearchHit{
							{
								Index: IndexName,
								ID:    "test-id-1",
								Score: 1.5,
								Source: map[string]any{
									"title":   "test document",
									"content": "test and example content",
								},
							},
							{
								Index: IndexName,
								ID:    "test-id-2",
								Score: 1.2,
								Source: map[string]any{
									"title":   "example document",
									"content": "another test content",
								},
							},
						},
					},
				}, nil
			})

		search := NewMockLogic(os)
		resp, err := search.Search(ctx, IndexName, &searchpb.SearchDocumentRequest{
			Strategy: &searchpb.Strategy{
				Strategy: &searchpb.Strategy_QueryString{
					QueryString: &searchpb.QueryStringStrategy{
						Query:  "test AND example",
						Fields: []string{"title", "content"},
					},
				},
			},
		})

		require.NoError(t, err)
		require.NotNil(t, resp)
		require.Equal(t, int32(2), resp.Total.Value)
		require.Equal(t, 1.5, resp.GetHits()[0].Score)
		require.Equal(t, 1.2, resp.GetHits()[1].Score)
	})
}

func TestConverter(t *testing.T) {
	t.Run("Convert basic search response", func(t *testing.T) {
		ctx := context.Background()
		converter := &search.Converter{}

		resp := &opensearch.SearchResponse{
			Took:     100,
			TimedOut: false,
			Hits: &opensearch.Hits{
				Total: &opensearch.SearchHitsTotal{
					Value:    1,
					Relation: "eq",
				},
				MaxScore: 1.5,
				Hits: []*opensearch.SearchHit{
					{
						Index: IndexName,
						ID:    "test-id",
						Score: 1.5,
						Source: map[string]any{
							"name":  "测试",
							"age":   30,
							"score": 95.5,
							"tags":  []string{"tag1", "tag2"},
							"meta": map[string]any{
								"created_at": "2024-03-20",
								"numbers":    []int{1, 2, 3},
							},
						},
					},
				},
			},
		}

		result, err := converter.Convert(ctx, resp)
		require.NoError(t, err)
		require.NotNil(t, result)

		// 验证基本字段
		require.Equal(t, int32(100), result.Took)
		require.False(t, result.TimedOut)
		require.Equal(t, int32(1), result.Total.Value)
		require.Equal(t, "eq", result.Total.Relation)
		require.Equal(t, 1.5, result.MaxScore)

		// 验证命中结果
		require.Len(t, result.Hits, 1)
		hit := result.Hits[0]
		require.Equal(t, IndexName, hit.Index)
		require.Equal(t, "test-id", hit.Id)
		require.Equal(t, 1.5, hit.Score)

		// 验证source字段的转换
		source := hit.Source.AsMap()
		require.Equal(t, "测试", source["name"])
		require.Equal(t, float64(30), source["age"])
		require.Equal(t, float64(95.5), source["score"])
		require.Equal(t, []any{"tag1", "tag2"}, source["tags"])

		meta := source["meta"].(map[string]any)
		require.Equal(t, "2024-03-20", meta["created_at"])
		require.Equal(t, []any{float64(1), float64(2), float64(3)}, meta["numbers"])
	})

	t.Run("Convert nil response", func(t *testing.T) {
		ctx := context.Background()
		converter := &search.Converter{}

		result, err := converter.Convert(ctx, nil)
		require.Error(t, err)
		require.Nil(t, result)
	})

	t.Run("Convert empty hits", func(t *testing.T) {
		ctx := context.Background()
		converter := &search.Converter{}

		resp := &opensearch.SearchResponse{
			Took:     50,
			TimedOut: false,
			Hits: &opensearch.Hits{
				Total: &opensearch.SearchHitsTotal{
					Value:    0,
					Relation: "eq",
				},
				MaxScore: 0,
				Hits:     []*opensearch.SearchHit{},
			},
		}

		result, err := converter.Convert(ctx, resp)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Equal(t, int32(50), result.Took)
		require.Equal(t, int32(0), result.Total.Value)
		require.Empty(t, result.Hits)
	})

	t.Run("Convert source with invalid nested value", func(t *testing.T) {
		ctx := context.Background()
		converter := &search.Converter{}

		resp := &opensearch.SearchResponse{
			Hits: &opensearch.Hits{
				Total: &opensearch.SearchHitsTotal{
					Value:    1,
					Relation: "eq",
				},
				Hits: []*opensearch.SearchHit{
					{
						Source: map[string]any{
							"nested": map[string]any{
								"invalid": complex(1, 2), // 不支持的复数类型
							},
						},
					},
				},
			},
		}

		result, err := converter.Convert(ctx, resp)
		require.Error(t, err)
		require.Nil(t, result)
		require.Contains(t, err.Error(), "failed to convert source to proto compatible format")
	})

	t.Run("Convert nil values", func(t *testing.T) {
		ctx := context.Background()
		converter := &search.Converter{}

		resp := &opensearch.SearchResponse{
			Hits: &opensearch.Hits{
				Total: &opensearch.SearchHitsTotal{
					Value:    1,
					Relation: "eq",
				},
				Hits: []*opensearch.SearchHit{
					{
						Source: map[string]any{
							"null_value": nil,
							"nested": map[string]any{
								"test\xc0": []string{"a", "b"},
							},
						},
					},
				},
			},
		}

		result, err := converter.Convert(ctx, resp)
		require.Error(t, err)
		require.Nil(t, result)
		require.Contains(t, err.Error(), "failed to create proto struct from converted source")
	})
	t.Run("Converted source is not a valid map type", func(t *testing.T) {
		ctx := context.Background()
		converter := &search.Converter{}

		resp := &opensearch.SearchResponse{
			Hits: &opensearch.Hits{
				Total: &opensearch.SearchHitsTotal{
					Value:    1,
					Relation: "eq",
				},
				MaxScore: 1.0,
				Hits: []*opensearch.SearchHit{
					{
						Index:  IndexName,
						ID:     "test-id",
						Score:  1.0,
						Source: nil,
					},
				},
			},
		}

		result, err := converter.Convert(ctx, resp)
		require.Error(t, err)
		require.Nil(t, result)
		require.Contains(t, err.Error(), "converted source is not a map[string]any")
	})

	t.Run("Convert complex nested slice", func(t *testing.T) {
		ctx := context.Background()
		converter := &search.Converter{}

		resp := &opensearch.SearchResponse{
			Took:     100,
			TimedOut: false,
			Hits: &opensearch.Hits{
				Total: &opensearch.SearchHitsTotal{
					Value:    1,
					Relation: "eq",
				},
				MaxScore: 1.0,
				Hits: []*opensearch.SearchHit{
					{
						Index: "test-index",
						ID:    "test-id",
						Score: 1.0,
						Source: map[string]any{
							"complex_array": []any{
								map[string]any{
									"name": "item1",
									"tags": []any{"tag1", "tag2"},
									"scores": []any{
										float64(95.5),
										float64(88.0),
									},
								},
								map[string]any{
									"name": "item2",
									"metadata": map[string]any{
										"created": "2024-03-20",
										"values":  []any{1, 2, 3},
									},
								},
								[]any{
									"nested1",
									map[string]any{
										"key": "value",
									},
									[]any{4, 5, 6},
								},
							},
						},
					},
				},
			},
		}

		result, err := converter.Convert(ctx, resp)
		require.NoError(t, err)
		require.NotNil(t, result)

		// 验证复杂数组转换
		source := result.Hits[0].Source.AsMap()
		complexArray := source["complex_array"].([]any)
		require.Len(t, complexArray, 3)

		// 验证第一个元素（包含嵌套map和数组）
		item1 := complexArray[0].(map[string]any)
		require.Equal(t, "item1", item1["name"])
		require.Equal(t, []any{"tag1", "tag2"}, item1["tags"])
		require.Equal(t, []any{float64(95.5), float64(88.0)}, item1["scores"])

		// 验证第二个元素（包含深层嵌套）
		item2 := complexArray[1].(map[string]any)
		require.Equal(t, "item2", item2["name"])
		metadata := item2["metadata"].(map[string]any)
		require.Equal(t, "2024-03-20", metadata["created"])
		require.Equal(t, []any{float64(1), float64(2), float64(3)}, metadata["values"])

		// 验证第三个元素（混合类型数组）
		item3 := complexArray[2].([]any)
		require.Equal(t, "nested1", item3[0])
		require.Equal(t, map[string]any{"key": "value"}, item3[1])
		require.Equal(t, []any{float64(4), float64(5), float64(6)}, item3[2])
	})

	t.Run("Convert slice with invalid element", func(t *testing.T) {
		ctx := context.Background()
		converter := &search.Converter{}

		resp := &opensearch.SearchResponse{
			Hits: &opensearch.Hits{
				Total: &opensearch.SearchHitsTotal{
					Value:    1,
					Relation: "eq",
				},
				Hits: []*opensearch.SearchHit{
					{
						Source: map[string]any{
							"invalid_array": []any{
								make(chan int), // 不支持的类型
							},
						},
					},
				},
			},
		}

		result, err := converter.Convert(ctx, resp)
		require.Error(t, err)
		require.Nil(t, result)
	})
	t.Run("test validate error", func(t *testing.T) {
		ctx := context.Background()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		os := mock.NewMockOpenSearch(ctrl)
		search := NewMockLogic(os)
		resp, err := search.Search(ctx, IndexName, &searchpb.SearchDocumentRequest{
			Strategy: &searchpb.Strategy{
				Strategy: &searchpb.Strategy_Match{
					Match: &searchpb.MatchStrategy{
						Field:    "",
						Query:    "",
						Operator: searchpb.MatchStrategy_OR,
					},
				},
			},
		})
		require.Error(t, err)
		require.Nil(t, resp)
		require.Contains(t, err.Error(), "requires field and query")
	})
}
