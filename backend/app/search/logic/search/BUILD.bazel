load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "search",
    srcs = [
        "converter.go",
        "entity.go",
        "mach.go",
        "search.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/search/logic/search",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/search/repo/opensearch",
        "//backend/app/search/utils",
        "//backend/common/rpc/framework/log",
        "//backend/proto/search/v1:search",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/structpb",
        "@org_uber_go_zap//:zap",
    ],
)

go_test(
    name = "search_test",
    srcs = [
        "entity_test.go",
        "search_test.go",
    ],
    deps = [
        ":search",
        "//backend/app/search/repo/opensearch",
        "//backend/app/search/repo/opensearch/mock",
        "//backend/proto/search/v1:search",
        "@com_github_stretchr_testify//require",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/structpb",
        "@org_uber_go_mock//gomock",
    ],
)
