package document

import (
	"bytes"
	"context"
	"strings"

	"github.com/bytedance/sonic"
	"github.com/opensearch-project/opensearch-go/opensearchapi"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/moego/backend/app/search/repo/opensearch"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	searchpb "github.com/MoeGolibrary/moego/backend/proto/search/v1"
)

type Converter struct{}

func (c *Converter) BuildBulkResponse(_ context.Context,
	resp *opensearch.BulkResponse) (*searchpb.BulkDocumentResponse, error) {
	response := &searchpb.BulkDocumentResponse{
		Results:   make([]*searchpb.BulkDocumentResponse_OperationResult, 0, len(resp.Items)),
		HasErrors: resp.Errors,
	}

	for _, item := range resp.Items {
		for key, value := range item {
			result := &searchpb.BulkDocumentResponse_OperationResult{
				Index: value.Index,
				OperationType: searchpb.OperationType(
					searchpb.OperationType_value[strings.ToUpper(key)],
				),
				DocumentId: value.ID,
				Version:    int32(value.Version),
				Result:     value.Result,
				Status:     int32(value.Status),
			}
			if value.Error != nil {
				result.Error = &searchpb.BulkDocumentResponse_Error{
					ErrorType:   value.Error.Type,
					ErrorReason: value.Error.Reason,
				}
			}

			if value.Shards != nil {
				result.Shards = &searchpb.BulkDocumentResponse_Shards{
					Total:      int32(value.Shards.Total),
					Successful: int32(value.Shards.Successful),
					Failed:     int32(value.Shards.Failed),
				}
			}

			response.Results = append(response.Results, result)
		}
	}

	return response, nil
}

func (c *Converter) ConvertBulkRequest(ctx context.Context,
	request *searchpb.BulkDocumentRequest) (*opensearchapi.BulkRequest, error) {
	var buf bytes.Buffer
	for _, operation := range request.Operations {
		if err := c.check(operation); err != nil {
			log.ErrorContext(ctx, "invalid operation", zap.Error(err), zap.Any("operation", operation))

			return nil, err
		}
		// 组装元数据
		operationType := strings.ToLower(operation.OperationType.String())
		meta := map[string]map[string]string{
			operationType: {
				"_index": operation.Target.Index,
			},
		}

		if operation.Target.Id != nil {
			meta[operationType]["_id"] = *operation.Target.Id
		}
		// 编码meta
		_ = sonic.ConfigDefault.NewEncoder(&buf).Encode(meta)
		// 编码body
		c.encodeOperation(ctx, &buf, operation)
		buf.WriteString("\n")
	}

	bulkreq := &opensearchapi.BulkRequest{
		Body:    &buf,
		Refresh: "true",
	}

	return bulkreq, nil
}

// EncodeOperation 将单个操作编码到buffer中
func (c *Converter) encodeOperation(_ context.Context,
	buf *bytes.Buffer, operation *searchpb.BulkDocumentRequest_BulkOperation) {
	if operation.OperationType == searchpb.OperationType_DELETE {
		return // delete操作不需要文档体
	}

	doc, _ := sonic.MarshalString(operation.GetDocument().GetFields())
	switch operation.OperationType {
	case searchpb.OperationType_UPDATE:
		_ = sonic.ConfigDefault.NewEncoder(buf).Encode(map[string]string{"doc": doc})
	default: // index/create操作
		buf.WriteString(doc)
	}
}

// 所有操作都必填Index
func (c *Converter) check(operation *searchpb.BulkDocumentRequest_BulkOperation) error {
	if operation == nil || operation.Target == nil || operation.Target.Index == "" {
		return status.Errorf(codes.InvalidArgument, "operation is nil or target is nil or index is empty")
	}

	return c.isValidByType(operation)
}

// delete操作：必须有ID，不能有document
// update操作：必须有ID，必须有document
// index/create操作：ID可选，必须有document
func (c *Converter) isValidByType(operation *searchpb.BulkDocumentRequest_BulkOperation) error {
	if operation.OperationType == searchpb.OperationType_UPDATE ||
		operation.OperationType == searchpb.OperationType_DELETE {
		if operation.Target.Id == nil {
			return status.Errorf(codes.InvalidArgument,
				"operation is %s but id is nil", operation.OperationType)
		}
	}

	switch operation.OperationType {
	case searchpb.OperationType_DELETE:
		if operation.Document != nil {
			return status.Errorf(codes.InvalidArgument, "operation is delete but document is not nil")
		}
	default: // update, index, create
		if operation.Document == nil {
			return status.Errorf(codes.InvalidArgument,
				"operation is %s but document is nil", operation.OperationType)
		}
	}

	return nil
}
