package openai

import (
	"log"
	"net/http"

	"github.com/gorilla/websocket"

	"github.com/MoeGolibrary/moego/backend/app/voice_agent/config"
)

const (
	url = "wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview-2024-12-17"
)

func NewConn() *websocket.Conn {
	header := http.Header{}
	header.Add("Authorization", "Bearer "+config.GetCfg().ChatGPT.APIKey)
	header.Add("OpenAI-Beta", "realtime=v1")

	ws, _, err := websocket.DefaultDialer.Dial(url, header)
	if err != nil {
		log.Println("dial websocket failed", err)
		return nil
	}

	return ws
}
