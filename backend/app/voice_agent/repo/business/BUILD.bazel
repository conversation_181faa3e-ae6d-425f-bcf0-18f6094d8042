load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "business",
    srcs = ["business.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/voice_agent/repo/business",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/organization/v1:organization",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/service/organization/v1:organization",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//credentials/insecure",
    ],
)
