package service

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	reportLogic "github.com/MoeGolibrary/moego/backend/app/fulfillment/logic/report"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/logic/reportrefactor"
	fulfillmentpb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
)

type FulfillmentReportService struct {
	fulfillmentReportLogic                                    *reportLogic.Logic
	fulfillmentReportRefactorLogic                            *reportrefactor.Logic
	fulfillmentpb.UnimplementedFulfillmentReportServiceServer // TODO: 实现所有接口后删除
}

func NewFulfillmentReportService() *FulfillmentReportService {
	return &FulfillmentReportService{
		fulfillmentReportLogic:         reportLogic.New(),
		fulfillmentReportRefactorLogic: reportrefactor.New(),
	}
}

func (s *FulfillmentReportService) checkReportAvailable(ctx context.Context, companyID int64) error {
	available, err := s.fulfillmentReportLogic.CheckReportAvailable(ctx, companyID)
	if err != nil {
		return err
	}
	if !available {
		return status.Errorf(codes.PermissionDenied, "appointment report not available")
	}

	return nil
}

func (s *FulfillmentReportService) GetFulfillmentReportTemplate(ctx context.Context,
	req *fulfillmentpb.GetFulfillmentReportTemplateRequest,
) (*fulfillmentpb.GetFulfillmentReportTemplateResponse, error) {
	if err := s.checkReportAvailable(ctx, req.CompanyId); err != nil {
		return nil, err
	}

	template, err := s.fulfillmentReportLogic.GetTemplate(
		ctx, req.GetCompanyId(), req.GetBusinessId(), req.GetCareType())
	if err != nil {
		return nil, err
	}

	return &fulfillmentpb.GetFulfillmentReportTemplateResponse{
		Template: reportLogic.ConvertTemplateLogicToPB(template),
	}, nil
}

func (s *FulfillmentReportService) UpdateFulfillmentReportTemplate(ctx context.Context,
	req *fulfillmentpb.UpdateFulfillmentReportTemplateRequest,
) (*fulfillmentpb.UpdateFulfillmentReportTemplateResponse, error) {
	if err := s.checkReportAvailable(ctx, req.CompanyId); err != nil {
		return nil, err
	}

	template := reportLogic.ConverterUpdateTemplateRequestToEntity(req)
	template.UpdateBy = req.StaffId

	err := s.fulfillmentReportLogic.UpdateTemplate(
		ctx, template, req.DeleteQuestionIds)
	if err != nil {
		return nil, err
	}

	return &fulfillmentpb.UpdateFulfillmentReportTemplateResponse{}, nil
}

func (s *FulfillmentReportService) GetFulfillmentReport(ctx context.Context,
	req *fulfillmentpb.GetFulfillmentReportRequest,
) (*fulfillmentpb.GetFulfillmentReportResponse, error) {
	if err := s.checkReportAvailable(ctx, req.CompanyId); err != nil {
		return nil, err
	}
	logicReport, err := s.fulfillmentReportLogic.GetFulfillmentReport(
		ctx, reportLogic.ConvertGetFulfillmentReportRequestToEntity(req))
	if err != nil {
		return nil, err
	}

	isNeedRefresh, err := s.fulfillmentReportLogic.IsNeedRefresh(ctx, logicReport)
	if err != nil {
		return nil, err
	}

	return &fulfillmentpb.GetFulfillmentReportResponse{
		FulfillmentReport: reportLogic.ConvertReportLogicToPB(logicReport),
		IsNeedRefresh:     isNeedRefresh,
	}, nil
}

func (s *FulfillmentReportService) UpdateFulfillmentReport(ctx context.Context,
	req *fulfillmentpb.UpdateFulfillmentReportRequest) (*fulfillmentpb.UpdateFulfillmentReportResponse, error) {
	if err := s.checkReportAvailable(ctx, req.CompanyId); err != nil {
		return nil, err
	}
	report := reportLogic.ConverterUpdateReportRequestToEntity(req)

	report, err := s.fulfillmentReportLogic.UpdateFulfillmentReport(ctx, report)
	if err != nil {
		return nil, err
	}

	return &fulfillmentpb.UpdateFulfillmentReportResponse{
		FulfillmentReport: reportLogic.ConvertReportLogicToPB(report),
		IsNeedRefresh:     false,
	}, nil
}

func (s *FulfillmentReportService) GetFulfillmentTemplateReport(ctx context.Context,
	req *fulfillmentpb.GetFulfillmentTemplateReportRequest) (
	*fulfillmentpb.GetFulfillmentTemplateReportResponse, error) {
	if err := s.checkReportAvailable(ctx, req.CompanyId); err != nil {
		return nil, err
	}
	logicReport, err := s.fulfillmentReportLogic.GetTemplateReport(ctx, req)
	if err != nil {
		return nil, err
	}
	summaryInfo, err := s.fulfillmentReportLogic.GetReportSummaryInfo(ctx, logicReport)
	if err != nil {
		return nil, err
	}

	return &fulfillmentpb.GetFulfillmentTemplateReportResponse{
		SummaryInfo: summaryInfo,
		SampleValue: &fulfillmentpb.FulfillmentReportSampleValue{
			Comment:      reportLogic.SampleComment,
			PetAvatarUrl: reportLogic.SamplePetAvatar,
			PhotoUrls:    []string{reportLogic.SamplePhotoBefore, reportLogic.SamplePhotoAfter},
			Urls: &fulfillmentpb.BodyViewUrl{
				Left:  reportLogic.SampleBodyViewLeft,
				Right: reportLogic.SampleBodyViewLeft,
			},
		},
	}, nil
}

func (s *FulfillmentReportService) GetFulfillmentReportSummaryInfo(ctx context.Context,
	req *fulfillmentpb.GetFulfillmentReportSummaryInfoRequest) (
	*fulfillmentpb.GetFulfillmentReportSummaryInfoResponse, error) {

	report, err := s.fulfillmentReportLogic.GetFulfillmentReport(ctx, &reportLogic.GetFulfillmentReport{
		ID:         req.GetFulfillmentReportId(),
		UUID:       req.GetUuid(),
		CompanyID:  req.GetCompanyId(),
		BusinessID: req.GetBusinessId(),
	})
	if err != nil {
		return nil, err
	}

	summaryInfo, err := s.fulfillmentReportLogic.GetReportSummaryInfo(ctx, report)
	if err != nil {
		return nil, err
	}

	return &fulfillmentpb.GetFulfillmentReportSummaryInfoResponse{
		SummaryInfo: summaryInfo,
	}, nil
}

// GenerateMessageContent 获取 SMS/Email 发送内容
func (s *FulfillmentReportService) GenerateMessageContent(ctx context.Context,
	req *fulfillmentpb.GenerateMessageContentRequest) (*fulfillmentpb.GenerateMessageContentResponse, error) {

	// 查询 report
	logicReport, err := s.fulfillmentReportLogic.GetFulfillmentReport(
		ctx, reportLogic.ConvertGenerateMessageContentRequestToEntity(req))
	if err != nil {
		return nil, err
	}
	reportSummaryInfo, err := s.fulfillmentReportLogic.GetReportSummaryInfo(ctx, logicReport)
	if err != nil {
		return nil, err
	}
	// 根据发送方式生成消息内容
	var messageContent string
	switch req.GetSendMethod() {
	case fulfillmentpb.SendMethod_EMAIL:
		messageContent, err = s.fulfillmentReportLogic.BuildEmailSubject("", reportSummaryInfo)
	case fulfillmentpb.SendMethod_SMS:
		messageContent, err = s.fulfillmentReportLogic.BuildSmsSendContent(reportSummaryInfo)
	default:
		return nil, status.Errorf(codes.InvalidArgument, "unsupported send method")
	}
	if err != nil {
		return nil, err
	}

	return &fulfillmentpb.GenerateMessageContentResponse{
		MessageContent: messageContent,
	}, nil
}

func (s *FulfillmentReportService) SendFulfillmentReport(
	ctx context.Context, req *fulfillmentpb.SendFulfillmentReportRequest) (
	*fulfillmentpb.SendFulfillmentReportResponse, error) {
	// 查询 report
	logicReport, err := s.fulfillmentReportLogic.GetFulfillmentReport(
		ctx, &reportLogic.GetFulfillmentReport{
			ID: req.GetFulfillmentReportId(),
		})
	if err != nil {
		return nil, err
	}

	reportSummaryInfo, err := s.fulfillmentReportLogic.GetReportSummaryInfo(ctx, logicReport)
	if err != nil {
		return nil, err
	}

	// 根据 send method 发送 report
	var sendResult *fulfillmentpb.SendFulfillmentReportResponse
	switch req.GetSendMethod() {
	case fulfillmentpb.SendMethod_EMAIL:
<<<<<<< HEAD
=======
		// TODO: 待实现
>>>>>>> 31603ae254fb1a33e35a1de6b64e820e2bf3ab96
		sendResult, err = s.fulfillmentReportLogic.SendEmailMessage(ctx, reportSummaryInfo, req)
	case fulfillmentpb.SendMethod_SMS:
		sendResult, err = s.fulfillmentReportLogic.SendSmsMessage(ctx, reportSummaryInfo, req)
	default:
		return nil, status.Errorf(codes.InvalidArgument, "unsupported send method")
	}
	if err != nil {
		return nil, err
	}

	return sendResult, nil
}

func (s *FulfillmentReportService) ListSendReportRecords(
	ctx context.Context,
	req *fulfillmentpb.ListSendReportRecordsRequest) (*fulfillmentpb.ListSendReportRecordsResponse, error) {

	resp, err := s.fulfillmentReportLogic.ListSendReportRecords(
		ctx,
		req,
	)
	if err != nil {
		return nil, err
	}

	return &fulfillmentpb.ListSendReportRecordsResponse{
		SendRecords: resp.GetSendRecords(),
		Pagination:  resp.GetPagination(),
		Total:       resp.GetTotal(),
	}, nil
}

func (s *FulfillmentReportService) CountFulfillmentReport(ctx context.Context,
	req *fulfillmentpb.CountFulfillmentReportRequest) (*fulfillmentpb.CountFulfillmentReportResponse, error) {
	resp, err := s.fulfillmentReportLogic.CountFulfillmentReport(ctx, req)
	if err != nil {
		return nil, err
	}

	return &fulfillmentpb.CountFulfillmentReportResponse{
		Total:      resp.GetTotal(),
		DraftCount: resp.GetDraftCount(),
		SentCount:  resp.GetSentCount(),
	}, nil
}

func (s *FulfillmentReportService) ListFulfillmentReport(
	ctx context.Context,
	req *fulfillmentpb.ListFulfillmentReportRequest) (*fulfillmentpb.ListFulfillmentReportResponse, error) {
	resp, err := s.fulfillmentReportLogic.ListFulfillmentReport(ctx, req)
	if err != nil {
		return nil, err
	}

	return &fulfillmentpb.ListFulfillmentReportResponse{
		FulfillmentReportCards: resp.GetFulfillmentReportCards(),
		Pagination:             resp.GetPagination(),
		Total:                  resp.GetTotal(),
	}, nil
}

func (s *FulfillmentReportService) IncreaseFulfillmentOpenedCount(ctx context.Context,
	req *fulfillmentpb.IncreaseFulfillmentOpenedCountRequest) (
	*fulfillmentpb.IncreaseFulfillmentOpenedCountResponse, error) {
	return s.fulfillmentReportLogic.IncreaseFulfillmentOpenedCount(ctx, req)
}

func (s *FulfillmentReportService) BatchSendFulfillmentReport(ctx context.Context,
	req *fulfillmentpb.BatchSendFulfillmentReportRequest) (*fulfillmentpb.BatchSendFulfillmentReportResponse, error) {
	return s.fulfillmentReportLogic.BatchSendFulfillmentReport(ctx, req)
}

func (s *FulfillmentReportService) BatchDeleteFulfillmentReport(ctx context.Context,
	req *fulfillmentpb.BatchDeleteFulfillmentReportRequest) (
	*fulfillmentpb.BatchDeleteFulfillmentReportResponse, error) {
	return s.fulfillmentReportLogic.BatchDeleteFulfillmentReport(ctx, req)
}

// ------------------------------------------- report card 重构双写------------------------------------------------------

// SyncFulfillmentReportTemplate 同步履约报告模板（双写）
func (s *FulfillmentReportService) SyncFulfillmentReportTemplate(ctx context.Context,
	req *fulfillmentpb.SyncFulfillmentReportTemplateRequest,
) (*fulfillmentpb.SyncFulfillmentReportTemplateResponse, error) {
	return s.fulfillmentReportRefactorLogic.SyncFulfillmentReportTemplate(ctx, req)
}

// BatchSyncFulfillmentReportQuestions 批量同步履约报告模板问题（双写）
func (s *FulfillmentReportService) BatchSyncFulfillmentReportQuestions(ctx context.Context,
	req *fulfillmentpb.BatchSyncFulfillmentReportQuestionsRequest,
) (*fulfillmentpb.BatchSyncFulfillmentReportQuestionsResponse, error) {
	return s.fulfillmentReportRefactorLogic.BatchSyncFulfillmentReportQuestions(ctx, req)
}

// SyncFulfillmentReport 同步履约报告（双写）
func (s *FulfillmentReportService) SyncFulfillmentReport(ctx context.Context,
	req *fulfillmentpb.SyncFulfillmentReportRequest) (*fulfillmentpb.SyncFulfillmentReportResponse, error) {
	return s.fulfillmentReportRefactorLogic.SyncFulfillmentReport(ctx, req)
}

// SyncFulfillmentReportSendRecord 同步履约报告发送记录（双写）
func (s *FulfillmentReportService) SyncFulfillmentReportSendRecord(ctx context.Context,
	req *fulfillmentpb.SyncFulfillmentReportSendRecordRequest,
) (*fulfillmentpb.SyncFulfillmentReportSendRecordResponse, error) {
	return s.fulfillmentReportRefactorLogic.SyncFulfillmentReportSendRecord(ctx, req)
}

// BatchMigrateTemplates 批量迁移模板（数据迁移专用）
func (s *FulfillmentReportService) BatchMigrateTemplates(ctx context.Context,
	req *fulfillmentpb.BatchMigrateTemplatesRequest) (*fulfillmentpb.BatchMigrateTemplatesResponse, error) {
	return s.fulfillmentReportRefactorLogic.BatchMigrateTemplates(ctx, req)
}

// BatchMigrateQuestions 批量迁移问题（数据迁移专用）
func (s *FulfillmentReportService) BatchMigrateQuestions(ctx context.Context,
	req *fulfillmentpb.BatchMigrateQuestionsRequest) (*fulfillmentpb.BatchMigrateQuestionsResponse, error) {
	return s.fulfillmentReportRefactorLogic.BatchMigrateQuestions(ctx, req)
}

// BatchMigrateReports 批量迁移报告（数据迁移专用）
func (s *FulfillmentReportService) BatchMigrateReports(ctx context.Context,
	req *fulfillmentpb.BatchMigrateReportsRequest) (*fulfillmentpb.BatchMigrateReportsResponse, error) {
	return s.fulfillmentReportRefactorLogic.BatchMigrateReports(ctx, req)
}

// BatchMigrateRecords 批量迁移发送记录（数据迁移专用）
func (s *FulfillmentReportService) BatchMigrateRecords(ctx context.Context,
	req *fulfillmentpb.BatchMigrateRecordsRequest) (*fulfillmentpb.BatchMigrateRecordsResponse, error) {
	return s.fulfillmentReportRefactorLogic.BatchMigrateRecords(ctx, req)
}

// GetTemplatesByUniqueKeys 通过唯一键批量查询模板
func (s *FulfillmentReportService) GetTemplatesByUniqueKeys(ctx context.Context,
	req *fulfillmentpb.GetTemplatesByUniqueKeysRequest) (*fulfillmentpb.GetTemplatesByUniqueKeysResponse, error) {
	return s.fulfillmentReportRefactorLogic.GetTemplatesByUniqueKeys(ctx, req)
}

// GetQuestionsByTemplateKeys 通过模板唯一键批量查询问题
func (s *FulfillmentReportService) GetQuestionsByTemplateKeys(ctx context.Context,
	req *fulfillmentpb.GetQuestionsByTemplateKeysRequest) (*fulfillmentpb.GetQuestionsByTemplateKeysResponse, error) {
	return s.fulfillmentReportRefactorLogic.GetQuestionsByTemplateKeys(ctx, req)
}

// GetGroomingQuestionsByQuestionKeys 通过问题唯一键批量查询问题（grooming迁移专用）
func (s *FulfillmentReportService) GetGroomingQuestionsByQuestionKeys(ctx context.Context,
	req *fulfillmentpb.GetGroomingQuestionsByQuestionKeysRequest,
) (*fulfillmentpb.GetGroomingQuestionsByQuestionKeysResponse, error) {
	return s.fulfillmentReportRefactorLogic.GetGroomingQuestionsByQuestionKeys(ctx, req)
}

// GetReportsByUniqueKeys 通过唯一键批量查询报告
func (s *FulfillmentReportService) GetReportsByUniqueKeys(ctx context.Context,
	req *fulfillmentpb.GetReportsByUniqueKeysRequest) (*fulfillmentpb.GetReportsByUniqueKeysResponse, error) {
	return s.fulfillmentReportRefactorLogic.GetReportsByUniqueKeys(ctx, req)
}

// GetRecordsByReportKeys 通过报告唯一键批量查询发送记录
func (s *FulfillmentReportService) GetRecordsByReportKeys(ctx context.Context,
	req *fulfillmentpb.GetRecordsByReportKeysRequest) (*fulfillmentpb.GetRecordsByReportKeysResponse, error) {
	return s.fulfillmentReportRefactorLogic.GetRecordsByReportKeys(ctx, req)
}
