load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "service",
    srcs = [
        "appointment.go",
        "fulfillment.go",
        "fulfullment_inner.go",
        "fulfullment_report.go",
        "service_instance.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/fulfillment/service",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/fulfillment/logic/appointment",
        "//backend/app/fulfillment/logic/fulfillment",
        "//backend/app/fulfillment/logic/inner",
        "//backend/app/fulfillment/logic/report",
        "//backend/app/fulfillment/logic/reportrefactor:report_refactor",
        "//backend/app/fulfillment/logic/service",
        "//backend/common/rpc/framework/log",
        "//backend/proto/fulfillment/v1:fulfillment",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
    ],
)
