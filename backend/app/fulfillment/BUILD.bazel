load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "fulfillment_lib",
    srcs = ["main.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/fulfillment",
    visibility = ["//visibility:private"],
    deps = [
        "//backend/app/fulfillment/config",
        "//backend/app/fulfillment/service",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/filters/opentelemetry",
        "//backend/common/rpc/filters/recovery",
        "//backend/common/rpc/framework",
        "//backend/common/rpc/framework/log",
        "//backend/proto/fulfillment/v1:fulfillment",
    ],
)

go_binary(
    name = "fulfillment",
    embed = [":fulfillment_lib"],
    visibility = ["//visibility:public"],
)
