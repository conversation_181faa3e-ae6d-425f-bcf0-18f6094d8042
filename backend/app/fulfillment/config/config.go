package config

import (
	"fmt"
	"os"
	"sync"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/config"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

var cfg *FulfillmentReportConfig

var initCfg sync.Once

type FulfillmentReportConfig struct {
	ReportClient *ReportClient `yaml:"report_client"`
}

type ReportClient struct {
	GroomingReportURL string `yaml:"grooming_report_url"`
	DailyReportURL    string `yaml:"daily_report_url"`
}

func Init(dir string) {
	initCfg.Do(func() {
		env := os.Getenv("MOEGO_ENVIRONMENT")
		if len(env) == 0 {
			env = "local"
			log.Warnf("MOEGO_ENVIRONMENT is not set, using default value: %s", env)
		}

		c, err := config.DefaultConfigLoader.Load(fmt.Sprintf("%s/%s/fulfillmentReport.yaml", dir, env))
		if err != nil {
			panic(err)
		}

		cfg = &FulfillmentReportConfig{}
		err = c.Unmarshal(cfg)
		if err != nil {
			panic(err)
		}
	})
}

func GetCfg() *FulfillmentReportConfig {
	return cfg
}
