load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "config",
    srcs = ["config.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/fulfillment/config",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/config",
        "//backend/common/rpc/framework/log",
    ],
)

filegroup(
    name = "testing_config",
    srcs = [
        "testing/config.yaml",
        "testing/fulfillmentReport.yaml",
    ],
    visibility = ["//visibility:public"],
)
