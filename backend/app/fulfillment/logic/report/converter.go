// nolint: lll
package report

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"
	"google.golang.org/protobuf/types/known/timestamppb"

	appointmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	offeringoldpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/appointment"
	questionrepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/question"
	reportrepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/report"
	templaterepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/template"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/message"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	fulfillmentpb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	petpb "github.com/MoeGolibrary/moego/backend/proto/pet/v1"
)

// ==================== Template 转换函数 ====================

// ConvertTemplateRepoToLogic 将 Repo 层的 Template 转换为 Logic 层的 Template
func ConvertTemplateRepoToLogic(repoTemplate *templaterepo.Template) *Template {
	if repoTemplate == nil {
		return nil
	}

	return &Template{
		ID:              repoTemplate.ID,
		CompanyID:       repoTemplate.CompanyID,
		BusinessID:      repoTemplate.BusinessID,
		CareType:        offeringpb.CareCategory(repoTemplate.CareType),
		Title:           repoTemplate.Title,
		ThemeColor:      repoTemplate.ThemeColor,
		LightThemeColor: repoTemplate.LightThemeColor,
		ThemeCode:       repoTemplate.ThemeCode,
		ThankYouMessage: repoTemplate.ThankYouMessage,
		// nolint: lll
		NextAppointmentDateFormatType: fulfillmentpb.NextAppointmentDateFormatType(repoTemplate.NextAppointmentDateFormatType),
		ShowShowcase:                  lo.FromPtrOr(repoTemplate.ShowShowcase, false),
		ShowOverallFeedback:           lo.FromPtrOr(repoTemplate.ShowOverallFeedback, false),
		ShowCustomizedFeedback:        lo.FromPtrOr(repoTemplate.ShowCustomizedFeedback, false),
		ShowPetCondition:              lo.FromPtrOr(repoTemplate.ShowPetCondition, false),
		ShowServiceStaffName:          lo.FromPtrOr(repoTemplate.ShowServiceStaffName, false),
		ShowNextAppointment:           lo.FromPtrOr(repoTemplate.ShowNextAppointment, false),
		ShowReviewBooster:             lo.FromPtrOr(repoTemplate.ShowReviewBooster, false),
		ShowYelpReview:                lo.FromPtrOr(repoTemplate.ShowYelpReview, false),
		ShowGoogleReview:              lo.FromPtrOr(repoTemplate.ShowGoogleReview, false),
		ShowFacebookReview:            lo.FromPtrOr(repoTemplate.ShowFacebookReview, false),
		LastPublishTime:               repoTemplate.LastPublishTime.Unix(),
		UpdateBy:                      repoTemplate.UpdateBy,
		CreateTime:                    repoTemplate.CreateTime,
		UpdateTime:                    repoTemplate.UpdateTime,
		Questions:                     &TemplateQuestion{}, // 需要单独转换
	}
}

// ConvertTemplateLogicToRepo 将 Logic 层的 Template 转换为 Repo 层的 Template
func ConvertTemplateLogicToRepo(logicTemplate *Template) *templaterepo.Template {
	if logicTemplate == nil {
		return nil
	}

	return &templaterepo.Template{
		ID:                            logicTemplate.ID,
		CompanyID:                     logicTemplate.CompanyID,
		BusinessID:                    logicTemplate.BusinessID,
		CareType:                      int32(logicTemplate.CareType.Number()),
		Title:                         logicTemplate.Title,
		ThemeColor:                    logicTemplate.ThemeColor,
		LightThemeColor:               logicTemplate.LightThemeColor,
		ThemeCode:                     logicTemplate.ThemeCode,
		ThankYouMessage:               logicTemplate.ThankYouMessage,
		NextAppointmentDateFormatType: int32(logicTemplate.NextAppointmentDateFormatType.Number()),
		ShowShowcase:                  lo.ToPtr(logicTemplate.ShowShowcase),
		ShowOverallFeedback:           lo.ToPtr(logicTemplate.ShowOverallFeedback),
		ShowCustomizedFeedback:        lo.ToPtr(logicTemplate.ShowCustomizedFeedback),
		ShowPetCondition:              lo.ToPtr(logicTemplate.ShowPetCondition),
		ShowServiceStaffName:          lo.ToPtr(logicTemplate.ShowServiceStaffName),
		ShowNextAppointment:           lo.ToPtr(logicTemplate.ShowNextAppointment),
		ShowReviewBooster:             lo.ToPtr(logicTemplate.ShowReviewBooster),
		ShowYelpReview:                lo.ToPtr(logicTemplate.ShowYelpReview),
		ShowGoogleReview:              lo.ToPtr(logicTemplate.ShowGoogleReview),
		ShowFacebookReview:            lo.ToPtr(logicTemplate.ShowFacebookReview),
		LastPublishTime:               time.Unix(logicTemplate.LastPublishTime, 0),
		UpdateBy:                      logicTemplate.UpdateBy,
		CreateTime:                    logicTemplate.CreateTime,
		UpdateTime:                    logicTemplate.UpdateTime,
	}
}

// ConvertTemplateLogicToPB 将 Logic 层的 Template 转换为 Protobuf
func ConvertTemplateLogicToPB(logicTemplate *Template) *fulfillmentpb.FulfillmentReportTemplate {
	if logicTemplate == nil {
		return nil
	}

	questions := ConvertTemplateQuestionLogicToPB(logicTemplate.Questions)

	return &fulfillmentpb.FulfillmentReportTemplate{
		Id:                            logicTemplate.ID,
		CompanyId:                     logicTemplate.CompanyID,
		BusinessId:                    logicTemplate.BusinessID,
		CareType:                      logicTemplate.CareType,
		Title:                         logicTemplate.Title,
		ThemeColor:                    logicTemplate.ThemeColor,
		LightThemeColor:               logicTemplate.LightThemeColor,
		ThemeCode:                     logicTemplate.ThemeCode,
		ThankYouMessage:               logicTemplate.ThankYouMessage,
		NextAppointmentDateFormatType: logicTemplate.NextAppointmentDateFormatType,
		ShowShowcase:                  logicTemplate.ShowShowcase,
		ShowOverallFeedback:           logicTemplate.ShowOverallFeedback,
		ShowCustomizedFeedback:        logicTemplate.ShowCustomizedFeedback,
		ShowPetCondition:              logicTemplate.ShowPetCondition,
		ShowStaff:                     logicTemplate.ShowServiceStaffName,
		ShowNextAppointment:           logicTemplate.ShowNextAppointment,
		ShowReviewBooster:             logicTemplate.ShowReviewBooster,
		ShowYelpReview:                logicTemplate.ShowYelpReview,
		ShowGoogleReview:              logicTemplate.ShowGoogleReview,
		ShowFacebookReview:            logicTemplate.ShowFacebookReview,
		GoogleReviewLink:              logicTemplate.GoogleReviewLink,
		YelpReviewLink:                logicTemplate.YelpReviewLink,
		FacebookReviewLink:            logicTemplate.FacebookReviewLink,
		LastPublishTime:               timestamppb.New(time.Unix(logicTemplate.LastPublishTime, 0)),
		CreateTime:                    timestamppb.New(logicTemplate.CreateTime),
		UpdateTime:                    timestamppb.New(logicTemplate.UpdateTime),
		UpdateBy:                      logicTemplate.UpdateBy,
		Questions:                     questions,
	}
}

func ConvertTemplateQuestionToQuestions(questions *TemplateQuestion) []*Question {
	if questions == nil {
		return nil
	}

	var allQuestions []*Question
	allQuestions = append(allQuestions, questions.Feedback...)
	allQuestions = append(allQuestions, questions.PetConditions...)
	allQuestions = append(allQuestions, questions.CustomizeFeedback...)

	return allQuestions
}

func ConvertTemplateQuestionLogicToPB(questions *TemplateQuestion) []*fulfillmentpb.FulfillmentReportTemplateQuestion {

	if questions == nil {
		return nil
	}

	feedbackQuestions := make([]*fulfillmentpb.FulfillmentReportTemplateQuestion, 0, len(questions.Feedback))
	for _, question := range questions.Feedback {
		feedbackQuestions = append(feedbackQuestions, ConvertQuestionLogicToPB(question))
	}

	petConditionsQuestions := make([]*fulfillmentpb.FulfillmentReportTemplateQuestion, 0, len(questions.PetConditions))
	for _, question := range questions.PetConditions {
		petConditionsQuestions = append(petConditionsQuestions, ConvertQuestionLogicToPB(question))
	}

	customizeFeedbackQuestions := make([]*fulfillmentpb.FulfillmentReportTemplateQuestion, 0, len(questions.CustomizeFeedback))
	for _, question := range questions.CustomizeFeedback {
		customizeFeedbackQuestions = append(customizeFeedbackQuestions, ConvertQuestionLogicToPB(question))
	}

	allQuestions := make([]*fulfillmentpb.FulfillmentReportTemplateQuestion, 0,
		len(questions.Feedback)+len(questions.PetConditions))
	allQuestions = append(allQuestions, feedbackQuestions...)
	allQuestions = append(allQuestions, petConditionsQuestions...)
	allQuestions = append(allQuestions, customizeFeedbackQuestions...)

	return allQuestions
}

// ConvertTemplatePBToLogic 将 Protobuf 转换为 Logic 层的 Template
func ConvertTemplatePBToLogic(pbTemplate *fulfillmentpb.FulfillmentReportTemplate) *Template {
	if pbTemplate == nil {
		return nil
	}

	return &Template{
		ID:                            pbTemplate.GetId(),
		CompanyID:                     pbTemplate.GetCompanyId(),
		BusinessID:                    pbTemplate.GetBusinessId(),
		CareType:                      pbTemplate.GetCareType(),
		Title:                         pbTemplate.GetTitle(),
		ThemeColor:                    pbTemplate.GetThemeColor(),
		LightThemeColor:               pbTemplate.GetLightThemeColor(),
		ThemeCode:                     pbTemplate.GetThemeCode(),
		ThankYouMessage:               pbTemplate.GetThankYouMessage(),
		NextAppointmentDateFormatType: pbTemplate.GetNextAppointmentDateFormatType(),
		ShowShowcase:                  pbTemplate.GetShowShowcase(),
		ShowOverallFeedback:           pbTemplate.GetShowOverallFeedback(),
		ShowCustomizedFeedback:        pbTemplate.GetShowCustomizedFeedback(),
		ShowPetCondition:              pbTemplate.GetShowPetCondition(),
		ShowServiceStaffName:          pbTemplate.GetShowStaff(),
		ShowNextAppointment:           pbTemplate.GetShowNextAppointment(),
		ShowReviewBooster:             pbTemplate.GetShowReviewBooster(),
		ShowYelpReview:                pbTemplate.GetShowYelpReview(),
		ShowGoogleReview:              pbTemplate.GetShowGoogleReview(),
		ShowFacebookReview:            pbTemplate.GetShowFacebookReview(),
		GoogleReviewLink:              pbTemplate.GetGoogleReviewLink(),
		YelpReviewLink:                pbTemplate.GetYelpReviewLink(),
		FacebookReviewLink:            pbTemplate.GetFacebookReviewLink(),
		LastPublishTime:               pbTemplate.GetLastPublishTime().AsTime().Unix(),
		CreateTime:                    pbTemplate.GetCreateTime().AsTime(),
		UpdateTime:                    pbTemplate.GetUpdateTime().AsTime(),
		UpdateBy:                      pbTemplate.GetUpdateBy(),
		Questions:                     ConvertTemplateQuestionPBToLogic(pbTemplate.GetQuestions()),
	}
}

func ConverterUpdateTemplateRequestToEntity(
	req *fulfillmentpb.UpdateFulfillmentReportTemplateRequest) *Template {

	feedbackQuestions := make([]*Question, 0)
	petConditionsQuestions := make([]*Question, 0)
	customizeFeedbackQuestions := make([]*Question, 0)
	for _, question := range req.GetQuestions() {
		logicQuestion := &Question{
			ID:                question.GetId(),
			CompanyID:         req.GetCompanyId(),
			BusinessID:        req.GetBusinessId(),
			CareType:          req.GetCareType(),
			Category:          question.GetCategory(),
			Type:              question.GetType().String(),
			Key:               question.GetKey(),
			Title:             question.GetTitle(),
			IsDefault:         question.GetIsDefault(),
			IsRequired:        question.GetIsRequired(),
			IsTypeEditable:    question.GetIsTypeEditable(),
			IsTitleEditable:   question.GetIsTitleEditable(),
			IsOptionsEditable: question.GetIsOptionsEditable(),
			Sort:              question.GetSort(),
			Extra:             ConvertExtraInfoPBToLogic(question.GetExtra()),
		}
		switch question.GetCategory() {
		case fulfillmentpb.QuestionCategory_FEEDBACK:
			feedbackQuestions = append(feedbackQuestions, logicQuestion)
		case fulfillmentpb.QuestionCategory_PET_CONDITION:
			petConditionsQuestions = append(petConditionsQuestions, logicQuestion)
		case fulfillmentpb.QuestionCategory_CUSTOMIZE_FEEDBACK:
			customizeFeedbackQuestions = append(customizeFeedbackQuestions, logicQuestion)
		}
	}

	return &Template{
		ID:                            req.GetId(),
		CompanyID:                     req.GetCompanyId(),
		BusinessID:                    req.GetBusinessId(),
		CareType:                      req.GetCareType(),
		Title:                         req.GetTitle(),
		ThemeColor:                    req.GetThemeColor(),
		LightThemeColor:               req.GetLightThemeColor(),
		ThemeCode:                     req.GetThemeCode(),
		ThankYouMessage:               req.GetThankYouMessage(),
		ShowShowcase:                  req.GetShowShowcase(),
		ShowOverallFeedback:           req.GetShowOverallFeedback(),
		ShowPetCondition:              req.GetShowPetCondition(),
		ShowServiceStaffName:          req.GetShowStaff(),
		ShowCustomizedFeedback:        req.GetShowCustomizedFeedback(),
		ShowNextAppointment:           req.GetShowNextAppointment(),
		NextAppointmentDateFormatType: req.GetNextAppointmentDateFormatType(),
		ShowReviewBooster:             req.GetShowReviewBooster(),
		ShowYelpReview:                req.GetShowYelpReview(),
		YelpReviewLink:                req.GetYelpReviewLink(),
		ShowGoogleReview:              req.GetShowGoogleReview(),
		GoogleReviewLink:              req.GetGoogleReviewLink(),
		ShowFacebookReview:            req.GetShowFacebookReview(),
		FacebookReviewLink:            req.GetFacebookReviewLink(),
		UpdateBy:                      req.GetStaffId(),
		Questions: &TemplateQuestion{
			Feedback:          feedbackQuestions,
			PetConditions:     petConditionsQuestions,
			CustomizeFeedback: customizeFeedbackQuestions,
		},
	}
}

func buildTemplate(template *Template, questions *TemplateQuestion, reviewBooster *message.ReviewBooster) *Template {
	if template == nil {
		return nil
	}

	template.Questions = questions

	if reviewBooster != nil {
		template.FacebookReviewLink = reviewBooster.PositiveFacebook
		template.GoogleReviewLink = reviewBooster.PositiveGoogle
		template.YelpReviewLink = reviewBooster.PositiveYelp
	}

	return template
}

// getDefaultSampleAppointment creates a sample appointment with pet details.
func getDefaultSampleAppointment(frequencyDay int32) *fulfillmentpb.FulfillmentReportCardSummaryInfo_AppointmentInfo {

	appointment := &fulfillmentpb.FulfillmentReportCardSummaryInfo_AppointmentInfo{
		AppointmentId:        0,
		AppointmentStartTime: sampleAppointmentStartTime,
		AppointmentEndTime:   sampleAppointmentEndTime,
	}

	// Determine the appointment date based on frequencyDay
	date := time.Now()
	if frequencyDay != 0 {
		date = date.AddDate(0, 0, int(frequencyDay))
	}
	appointment.AppointmentDate = date.Format(sampleDateLayout)

	appointment.PetService = []*fulfillmentpb.FulfillmentReportCardSummaryInfo_PetService{
		{
			PetInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetInfo{},
			PetDetails: []*fulfillmentpb.FulfillmentReportCardSummaryInfo_PetDetailInfo{
				{
					ServiceId:       2,
					ServiceName:     sampleAddonName,
					ServiceDuration: sampleAddonDuration,
				},
				{
					ServiceId:       1,
					ServiceName:     sampleAddonName,
					ServiceDuration: sampleAddonDuration,
				},
			},
		},
	}

	return appointment
}

// ==================== Question 转换函数 ====================

func ConvertTemplateQuestionPBToLogic(
	pbQuestions []*fulfillmentpb.FulfillmentReportTemplateQuestion) *TemplateQuestion {

	if pbQuestions == nil {
		return nil
	}

	feedbackQuestions := make([]*Question, 0)
	petConditionsQuestions := make([]*Question, 0)

	for _, question := range pbQuestions {
		switch question.Category {
		case fulfillmentpb.QuestionCategory_PET_CONDITION:
			petConditionsQuestions = append(petConditionsQuestions, ConvertQuestionPBToLogic(question))
		case fulfillmentpb.QuestionCategory_FEEDBACK, fulfillmentpb.QuestionCategory_CUSTOMIZE_FEEDBACK:
			feedbackQuestions = append(feedbackQuestions, ConvertQuestionPBToLogic(question))
		}
	}

	return &TemplateQuestion{Feedback: feedbackQuestions, PetConditions: petConditionsQuestions}
}

func ConvertQuestionRepoToLogics(ctx context.Context,
	repoQuestion []*questionrepo.Question) (*TemplateQuestion, error) {
	if len(repoQuestion) == 0 {
		return nil, nil
	}

	feedbackQuestions := make([]*Question, 0, len(repoQuestion))
	petConditionsQuestions := make([]*Question, 0, len(repoQuestion))
	customizeFeedbackQuestions := make([]*Question, 0, len(repoQuestion))

	for _, question := range repoQuestion {
		question, err := ConvertQuestionRepoToLogic(ctx, question)
		if err != nil {
			return nil, err
		}
		switch question.Category {
		case fulfillmentpb.QuestionCategory_FEEDBACK:
			feedbackQuestions = append(feedbackQuestions, question)
		case fulfillmentpb.QuestionCategory_PET_CONDITION:
			petConditionsQuestions = append(petConditionsQuestions, question)
		case fulfillmentpb.QuestionCategory_CUSTOMIZE_FEEDBACK:
			customizeFeedbackQuestions = append(customizeFeedbackQuestions, question)
		}
	}

	return &TemplateQuestion{Feedback: feedbackQuestions, PetConditions: petConditionsQuestions, CustomizeFeedback: customizeFeedbackQuestions}, nil
}

// ConvertQuestionRepoToLogic 将 Repo 层的 Question 转换为 Logic 层的 Question
func ConvertQuestionRepoToLogic(ctx context.Context, repoQuestion *questionrepo.Question) (*Question, error) {
	if repoQuestion == nil {
		return nil, nil
	}

	var extra *ExtraInfo
	if repoQuestion.ExtraJSON != "" {
		extra = &ExtraInfo{}
		if err := json.Unmarshal([]byte(repoQuestion.ExtraJSON), extra); err != nil {
			log.ErrorContextf(ctx, "ConvertQuestionRepoToLogic unmarshal extra err: %v", err)

			return nil, err
		}
	}

	return &Question{
		ID:                repoQuestion.ID,
		CompanyID:         repoQuestion.CompanyID,
		BusinessID:        repoQuestion.BusinessID,
		CareType:          offeringpb.CareCategory(repoQuestion.CareType),
		Category:          fulfillmentpb.QuestionCategory(repoQuestion.Category),
		Type:              repoQuestion.Type,
		Key:               repoQuestion.Key,
		Title:             repoQuestion.Title,
		Sort:              repoQuestion.Sort,
		IsDefault:         lo.FromPtrOr(repoQuestion.IsDefault, false),
		IsRequired:        lo.FromPtrOr(repoQuestion.IsRequired, false),
		IsTypeEditable:    lo.FromPtrOr(repoQuestion.IsTypeEditable, false),
		IsTitleEditable:   lo.FromPtrOr(repoQuestion.IsTitleEditable, false),
		IsOptionsEditable: lo.FromPtrOr(repoQuestion.IsOptionsEditable, false),
		Extra:             extra,
		CreateTime:        repoQuestion.CreateTime,
		UpdateTime:        repoQuestion.UpdateTime,
	}, nil
}

// ConvertQuestionLogicToRepo 将 Logic 层的 Question 转换为 Repo 层的 Question
func ConvertQuestionLogicToRepo(logicQuestion *Question) *questionrepo.Question {
	if logicQuestion == nil {
		return nil
	}

	extraJSON := "{}"
	if logicQuestion.Extra != nil {
		if data, err := json.Marshal(logicQuestion.Extra); err == nil {
			extraJSON = string(data)
		}
	}

	return &questionrepo.Question{
		ID:                logicQuestion.ID,
		CompanyID:         logicQuestion.CompanyID,
		BusinessID:        logicQuestion.BusinessID,
		CareType:          int32(logicQuestion.CareType.Number()),
		Category:          int32(logicQuestion.Category.Number()),
		Type:              logicQuestion.Type,
		Key:               logicQuestion.Key,
		Title:             logicQuestion.Title,
		Sort:              logicQuestion.Sort,
		IsDefault:         lo.ToPtr(logicQuestion.IsDefault),
		IsRequired:        lo.ToPtr(logicQuestion.IsRequired),
		IsTypeEditable:    lo.ToPtr(logicQuestion.IsTypeEditable),
		IsTitleEditable:   lo.ToPtr(logicQuestion.IsTitleEditable),
		IsOptionsEditable: lo.ToPtr(logicQuestion.IsOptionsEditable),
		ExtraJSON:         extraJSON,
		CreateTime:        logicQuestion.CreateTime,
		UpdateTime:        logicQuestion.UpdateTime,
	}
}

// ConvertQuestionLogicToPB 将 Logic 层的 Question 转换为 Protobuf
func ConvertQuestionLogicToPB(logicQuestion *Question) *fulfillmentpb.FulfillmentReportTemplateQuestion {
	if logicQuestion == nil {
		return nil
	}

	return &fulfillmentpb.FulfillmentReportTemplateQuestion{
		Id:                logicQuestion.ID,
		CareType:          logicQuestion.CareType,
		Category:          logicQuestion.Category,
		Type:              fulfillmentpb.QuestionType(fulfillmentpb.QuestionType_value[strings.ToUpper(logicQuestion.Type)]),
		Key:               logicQuestion.Key,
		Title:             logicQuestion.Title,
		IsDefault:         logicQuestion.IsDefault,
		IsRequired:        logicQuestion.IsRequired,
		IsTypeEditable:    logicQuestion.IsTypeEditable,
		IsTitleEditable:   logicQuestion.IsTitleEditable,
		IsOptionsEditable: logicQuestion.IsOptionsEditable,
		Sort:              logicQuestion.Sort,
		CreateTime:        timestamppb.New(logicQuestion.CreateTime),
		UpdateTime:        timestamppb.New(logicQuestion.UpdateTime),
		Extra:             ConvertExtraInfoLogicToPB(logicQuestion.Extra),
	}
}

// ConvertQuestionPBToLogic 将 Protobuf 转换为 Logic 层的 Question
func ConvertQuestionPBToLogic(pbQuestion *fulfillmentpb.FulfillmentReportTemplateQuestion) *Question {
	if pbQuestion == nil {
		return nil
	}

	return &Question{
		ID:                pbQuestion.GetId(),
		CareType:          pbQuestion.GetCareType(),
		Category:          pbQuestion.GetCategory(),
		Type:              pbQuestion.GetType().String(),
		Key:               pbQuestion.GetKey(),
		Title:             pbQuestion.GetTitle(),
		IsDefault:         pbQuestion.GetIsDefault(),
		IsRequired:        pbQuestion.GetIsRequired(),
		IsTypeEditable:    pbQuestion.GetIsTypeEditable(),
		IsTitleEditable:   pbQuestion.GetIsTitleEditable(),
		IsOptionsEditable: pbQuestion.GetIsOptionsEditable(),
		Sort:              pbQuestion.GetSort(),
		CreateTime:        pbQuestion.GetCreateTime().AsTime(),
		UpdateTime:        pbQuestion.GetUpdateTime().AsTime(),
		Extra:             ConvertExtraInfoPBToLogic(pbQuestion.GetExtra()),
	}
}

func ConvertExtraInfoLogicToPB(extra *ExtraInfo) *fulfillmentpb.FulfillmentReportTemplateQuestion_ExtraInfo {

	if extra == nil {
		return nil
	}

	return &fulfillmentpb.FulfillmentReportTemplateQuestion_ExtraInfo{
		BuildInOptions: extra.BuildInOptions,
		Options:        extra.Options,
	}
}

func ConvertExtraInfoPBToLogic(pbExtra *fulfillmentpb.FulfillmentReportTemplateQuestion_ExtraInfo) *ExtraInfo {

	if pbExtra == nil {
		return nil
	}

	return &ExtraInfo{
		BuildInOptions: pbExtra.BuildInOptions,
		Options:        pbExtra.Options,
	}
}

// ==================== Report 转换函数 ====================

func ConvertGetFulfillmentReportRequestToEntity(req *fulfillmentpb.GetFulfillmentReportRequest) *GetFulfillmentReport {
	return &GetFulfillmentReport{
		ID:            req.GetReportId(),
		CompanyID:     req.GetCompanyId(),
		BusinessID:    req.GetBusinessId(),
		AppointmentID: req.GetAppointmentId(),
		PetID:         req.GetPetId(),
		CareType:      req.GetCareType(),
		ServiceDate:   req.GetServiceDate(),
	}
}

func ConverterUpdateReportRequestToEntity(req *fulfillmentpb.UpdateFulfillmentReportRequest) *Report {

	content := ConvertContentPBToLogic(req.GetContent())

	return &Report{
		ID:            req.GetId(),
		CompanyID:     req.GetCompanyId(),
		BusinessID:    req.GetBusinessId(),
		AppointmentID: req.GetAppointmentId(),
		PetID:         req.GetPetId(),
		CareType:      req.GetCareType(),
		ServiceDate:   req.GetServiceDate(),
		ThemeCode:     req.GetThemeCode(),
		UpdateBy:      req.GetStaffId(),
		Content:       content,
	}
}

func ConvertGenerateMessageContentRequestToEntity(
	req *fulfillmentpb.GenerateMessageContentRequest) *GetFulfillmentReport {
	return &GetFulfillmentReport{
		CompanyID:     req.GetCompanyId(),
		BusinessID:    req.GetBusinessId(),
		AppointmentID: req.GetAppointmentId(),
		PetID:         req.GetPetId(),
		CareType:      req.GetCareType(),
		ServiceDate:   req.GetServiceDate(),
	}
}

// ConvertReportRepoToLogic 将 Repo 层的 Report 转换为 Logic 层的 Report
func ConvertReportRepoToLogic(ctx context.Context, repoReport *reportrepo.Report) (*Report, error) {
	if repoReport == nil {
		return nil, nil
	}

	template := &Template{}
	content := &Content{}

	// 解析 TemplateJSON
	if repoReport.TemplateJSON != "" {
		if err := json.Unmarshal([]byte(repoReport.TemplateJSON), template); err != nil {
			log.ErrorContextf(ctx, "ConvertReportRepoToLogic unmarshal template err: %v", err)

			return nil, err
		}
	}

	// 解析 ContentJSON
	if repoReport.ContentJSON != "" {
		if err := json.Unmarshal([]byte(repoReport.ContentJSON), content); err != nil {
			log.ErrorContextf(ctx, "ConvertReportRepoToLogic unmarshal content err: %v", err)

			return nil, err
		}
	}

	return &Report{
		ID:              repoReport.ID,
		CompanyID:       repoReport.CompanyID,
		BusinessID:      repoReport.BusinessID,
		CustomerID:      repoReport.CustomerID,
		AppointmentID:   repoReport.AppointmentID,
		PetID:           repoReport.PetID,
		PetTypeID:       repoReport.PetTypeID,
		CareType:        offeringpb.CareCategory(repoReport.CareType),
		ServiceDate:     repoReport.ServiceDate,
		Status:          fulfillmentpb.ReportStatus(fulfillmentpb.ReportStatus_value[strings.ToUpper(repoReport.Status)]),
		UUID:            repoReport.UUID,
		LinkOpenedCount: repoReport.LinkOpenedCount,
		ThemeCode:       repoReport.ThemeCode,
		TemplateVersion: repoReport.TemplateVersion,
		UpdateBy:        repoReport.UpdateBy,
		CreateTime:      repoReport.CreateTime,
		UpdateTime:      repoReport.UpdateTime,
		Template:        template,
		Content:         content,
	}, nil
}

// ConvertReportLogicToPB 将 Logic 层的 Report 转换为 Protobuf
func ConvertReportLogicToPB(logicReport *Report) *fulfillmentpb.FulfillmentReport {
	if logicReport == nil {
		return nil
	}

	var template *fulfillmentpb.FulfillmentReportTemplate
	var content *fulfillmentpb.FulfillmentReportContent

	if logicReport.Template != nil {
		template = ConvertTemplateLogicToPB(logicReport.Template)
	}

	if logicReport.Content != nil {
		content = ConvertContentLogicToPB(logicReport.Content)
	}

	return &fulfillmentpb.FulfillmentReport{
		Id:              lo.ToPtr(logicReport.ID),
		CompanyId:       logicReport.CompanyID,
		BusinessId:      logicReport.BusinessID,
		CustomerId:      logicReport.CustomerID,
		AppointmentId:   logicReport.AppointmentID,
		PetId:           logicReport.PetID,
		PetTypeId:       logicReport.PetTypeID,
		CareType:        logicReport.CareType,
		ServiceDate:     logicReport.ServiceDate,
		Status:          logicReport.Status,
		Uuid:            logicReport.UUID,
		LinkOpenedCount: logicReport.LinkOpenedCount,
		ThemeCode:       logicReport.ThemeCode,
		TemplateVersion: timestamppb.New(logicReport.TemplateVersion),
		CreateTime:      timestamppb.New(logicReport.CreateTime),
		UpdateTime:      timestamppb.New(logicReport.UpdateTime),
		Template:        template,
		Content:         content,
	}
}

func ConvertReportLogicToRepo(ctx context.Context, logicReport *Report) (*reportrepo.Report, error) {

	if logicReport == nil {
		return nil, nil
	}

	contentJSON, err := json.Marshal(logicReport.Content)
	if err != nil {
		log.ErrorContextf(ctx, "ConvertReportLogicToRepo marshal content err: %v", err)

		return nil, err
	}

	templateJSON, err := json.Marshal(logicReport.Template)
	if err != nil {
		log.ErrorContextf(ctx, "ConvertReportLogicToRepo marshal template err: %v", err)

		return nil, err
	}

	return &reportrepo.Report{
		ID:              logicReport.ID,
		CompanyID:       logicReport.CompanyID,
		BusinessID:      logicReport.BusinessID,
		CustomerID:      logicReport.CustomerID,
		AppointmentID:   logicReport.AppointmentID,
		PetID:           logicReport.PetID,
		PetTypeID:       logicReport.PetTypeID,
		CareType:        int32(logicReport.CareType.Number()),
		ServiceDate:     logicReport.ServiceDate,
		Status:          logicReport.Status.Enum().String(),
		UUID:            logicReport.UUID,
		LinkOpenedCount: logicReport.LinkOpenedCount,
		ThemeCode:       logicReport.ThemeCode,
		TemplateVersion: logicReport.TemplateVersion,
		UpdateBy:        logicReport.UpdateBy,
		CreateTime:      logicReport.CreateTime,
		UpdateTime:      logicReport.UpdateTime,
		TemplateJSON:    string(templateJSON),
		ContentJSON:     string(contentJSON),
	}, nil
}

func ConvertReportLogicToGetFulfillmentReport(logicReport *Report) *GetFulfillmentReport {
	return &GetFulfillmentReport{
		ID:            logicReport.ID,
		CompanyID:     logicReport.CompanyID,
		BusinessID:    logicReport.BusinessID,
		AppointmentID: logicReport.AppointmentID,
		PetID:         logicReport.PetID,
		CareType:      logicReport.CareType,
		ServiceDate:   logicReport.ServiceDate,
	}
}

func ConvertFulfillmentReportToReportLogic(report *fulfillmentpb.FulfillmentReport) *Report {
	return &Report{
		ID:              report.GetId(),
		CompanyID:       report.GetCompanyId(),
		BusinessID:      report.GetBusinessId(),
		AppointmentID:   report.GetAppointmentId(),
		PetID:           report.GetPetId(),
		CareType:        report.GetCareType(),
		ServiceDate:     report.GetServiceDate(),
		Status:          report.GetStatus(),
		UUID:            report.GetUuid(),
		LinkOpenedCount: report.GetLinkOpenedCount(),
		ThemeCode:       report.GetThemeCode(),
		TemplateVersion: report.GetTemplateVersion().AsTime(),
		CreateTime:      report.GetCreateTime().AsTime(),
		UpdateTime:      report.GetUpdateTime().AsTime(),
		Template:        ConvertTemplatePBToLogic(report.GetTemplate()),
		Content:         ConvertContentPBToLogic(report.GetContent()),
	}
}

// ==================== Content 转换函数 ====================

// ConvertContentLogicToPB 将 Logic 层的 Content 转换为 Protobuf
func ConvertContentLogicToPB(logicContent *Content) *fulfillmentpb.FulfillmentReportContent {
	if logicContent == nil {
		return nil
	}

	feedbacks := make([]*fulfillmentpb.FulfillmentReportQuestion, 0, len(logicContent.Feedbacks))
	for _, feedback := range logicContent.Feedbacks {
		feedbacks = append(feedbacks, ConvertReportQuestionLogicToPB(feedback))
	}

	petConditions := make([]*fulfillmentpb.FulfillmentReportQuestion, 0, len(logicContent.PetConditions))
	for _, condition := range logicContent.PetConditions {
		petConditions = append(petConditions, ConvertReportQuestionLogicToPB(condition))
	}

	var recommendation *fulfillmentpb.FulfillmentReportRecommendation
	if logicContent.Recommendation != nil {
		recommendation = ConvertReportRecommendationLogicToPB(logicContent.Recommendation)
	}

	return &fulfillmentpb.FulfillmentReportContent{
		Photos:          logicContent.Photos,
		Videos:          logicContent.Videos,
		Feedbacks:       feedbacks,
		PetConditions:   petConditions,
		Recommendation:  recommendation,
		ThemeColor:      lo.ToPtr(logicContent.ThemeColor),
		LightThemeColor: logicContent.LightThemeColor,
	}
}

// ConvertContentPBToLogic 将 Protobuf 转换为 Logic 层的 Content
func ConvertContentPBToLogic(pbContent *fulfillmentpb.FulfillmentReportContent) *Content {
	if pbContent == nil {
		return &Content{}
	}

	feedbacks := make([]*ReportQuestion, 0, len(pbContent.GetFeedbacks()))
	for _, feedback := range pbContent.GetFeedbacks() {
		feedbacks = append(feedbacks, ConvertReportQuestionPBToLogic(feedback))
	}

	petConditions := make([]*ReportQuestion, 0, len(pbContent.GetPetConditions()))
	for _, condition := range pbContent.GetPetConditions() {
		petConditions = append(petConditions, ConvertReportQuestionPBToLogic(condition))
	}

	var recommendation *ReportRecommendation
	if pbContent.GetRecommendation() != nil {
		recommendation = ConvertReportRecommendationPBToLogic(pbContent.GetRecommendation())
	}

	return &Content{
		Photos:          pbContent.GetPhotos(),
		Videos:          pbContent.GetVideos(),
		Feedbacks:       feedbacks,
		PetConditions:   petConditions,
		Recommendation:  recommendation,
		ThemeColor:      pbContent.GetThemeColor(),
		LightThemeColor: pbContent.LightThemeColor,
	}
}

// ==================== ReportQuestion 转换函数 ====================

// ConvertReportQuestionLogicToPB 将 Logic 层的 ReportQuestion 转换为 Protobuf
func ConvertReportQuestionLogicToPB(logicQuestion *ReportQuestion) *fulfillmentpb.FulfillmentReportQuestion {
	if logicQuestion == nil {
		return nil
	}

	var urls *fulfillmentpb.BodyViewUrl
	if logicQuestion.URLs != nil {
		urls = &fulfillmentpb.BodyViewUrl{
			Left:  logicQuestion.URLs.Left,
			Right: logicQuestion.URLs.Right,
		}
	}

	return &fulfillmentpb.FulfillmentReportQuestion{
		Id:            logicQuestion.ID,
		Category:      logicQuestion.Category,
		Type:          fulfillmentpb.QuestionType(fulfillmentpb.QuestionType_value[strings.ToUpper(logicQuestion.Type)]),
		Key:           logicQuestion.Key,
		Title:         logicQuestion.Title,
		Required:      logicQuestion.Required,
		IsShow:        logicQuestion.IsShow,
		Options:       logicQuestion.Options,
		Choices:       logicQuestion.Choices,
		CustomOptions: logicQuestion.CustomOptions,
		InputText:     logicQuestion.InputText,
		Placeholder:   logicQuestion.Placeholder,
		Urls:          urls,
	}
}

// ConvertReportQuestionPBToLogic 将 Protobuf 转换为 Logic 层的 ReportQuestion
func ConvertReportQuestionPBToLogic(pbQuestion *fulfillmentpb.FulfillmentReportQuestion) *ReportQuestion {
	if pbQuestion == nil {
		return nil
	}

	var urls *BodyViewURL
	if pbQuestion.GetUrls() != nil {
		urls = &BodyViewURL{
			Left:  pbQuestion.GetUrls().GetLeft(),
			Right: pbQuestion.GetUrls().GetRight(),
		}
	}

	return &ReportQuestion{
		ID:            pbQuestion.GetId(),
		Category:      pbQuestion.GetCategory(),
		Type:          pbQuestion.GetType().String(),
		Key:           pbQuestion.GetKey(),
		Title:         pbQuestion.GetTitle(),
		Required:      pbQuestion.GetRequired(),
		IsShow:        pbQuestion.GetIsShow(),
		Options:       pbQuestion.GetOptions(),
		Choices:       pbQuestion.GetChoices(),
		CustomOptions: pbQuestion.GetCustomOptions(),
		InputText:     pbQuestion.GetInputText(),
		Placeholder:   pbQuestion.GetPlaceholder(),
		URLs:          urls,
	}
}

// ==================== ReportRecommendation 转换函数 ====================

// ConvertReportRecommendationLogicToPB 将 Logic 层的 ReportRecommendation 转换为 Protobuf
func ConvertReportRecommendationLogicToPB(
	logicRecommendation *ReportRecommendation) *fulfillmentpb.FulfillmentReportRecommendation {
	if logicRecommendation == nil {
		return nil
	}

	return &fulfillmentpb.FulfillmentReportRecommendation{
		FrequencyDay:            logicRecommendation.FrequencyDay,
		FrequencyType:           logicRecommendation.FrequencyType,
		FrequencyText:           lo.ToPtr(logicRecommendation.FrequencyText),
		NextAppointmentDate:     lo.ToPtr(logicRecommendation.NextAppointmentDate),
		NextAppointmentDateText: lo.ToPtr(logicRecommendation.NextAppointmentDateText),
	}
}

// ConvertReportRecommendationPBToLogic 将 Protobuf 转换为 Logic 层的 ReportRecommendation
func ConvertReportRecommendationPBToLogic(
	pbRecommendation *fulfillmentpb.FulfillmentReportRecommendation) *ReportRecommendation {
	if pbRecommendation == nil {
		return nil
	}

	return &ReportRecommendation{
		FrequencyDay:            pbRecommendation.GetFrequencyDay(),
		FrequencyType:           pbRecommendation.GetFrequencyType(),
		FrequencyText:           pbRecommendation.GetFrequencyText(),
		NextAppointmentDate:     pbRecommendation.GetNextAppointmentDate(),
		NextAppointmentDateText: pbRecommendation.GetNextAppointmentDateText(),
	}
}

// summary info
func BuildSummaryAppointmentInfo(report *Report,
	appointment *appointmentpb.AppointmentModel,
	companyPreferenceSetting *organizationpb.CompanyPreferenceSettingModel,
	arrivalWindowSetting *message.ArrivalWindowSetting,
	petDetailList []*appointmentpb.PetDetailModel,
	petInfoList []*businesscustomerpb.BusinessCustomerPetInfoModel,
	staffInfoList []*organizationpb.StaffModel,
	serviceInfoList []*offeringoldpb.ServiceBriefView,
) *fulfillmentpb.FulfillmentReportCardSummaryInfo_AppointmentInfo {
	petMap := lo.SliceToMap(petInfoList, func(item *businesscustomerpb.BusinessCustomerPetInfoModel) (
		int64, *businesscustomerpb.BusinessCustomerPetInfoModel) {
		return item.GetId(), item
	})
	staffMap := lo.SliceToMap(staffInfoList, func(item *organizationpb.StaffModel) (
		int64, *organizationpb.StaffModel) {
		return item.GetId(), item
	})
	serviceMap := lo.SliceToMap(serviceInfoList, func(item *offeringoldpb.ServiceBriefView) (
		int64, *offeringoldpb.ServiceBriefView) {
		return item.GetId(), item
	})

	// 按 PetId 分组 PetDetail
	petDetailMap := make(map[int64][]*appointmentpb.PetDetailModel)
	for _, petDetail := range petDetailList {
		petID := petDetail.GetPetId()
		petDetailMap[petID] = append(petDetailMap[petID], petDetail)
	}

	petServices := make([]*fulfillmentpb.FulfillmentReportCardSummaryInfo_PetService, 0, len(petDetailMap))
	for petID, petDetails := range petDetailMap {
		pet := petMap[petID]
		if pet == nil {
			break
		}
		// 创建 PetInfo
		petInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetInfo{
			PetId:      petID,
			PetName:    pet.GetPetName(),
			AvatarPath: pet.GetAvatarPath(),
			PetBreed:   pet.GetBreed(),
			Gender:     petpb.Pet_PetGender(pet.GetGender()),
			PetType:    petpb.Pet_PetType(pet.GetPetType()),
			Weight:     pet.GetWeight(),
			WeightWithUnit: fmt.Sprintf("%s %s", pet.GetWeight(),
				companyPreferenceSetting.UnitOfWeightType.String()),
		}

		// 为这个 Pet 创建所有的 PetDetailInfo
		petDetailInfos := make([]*fulfillmentpb.FulfillmentReportCardSummaryInfo_PetDetailInfo, 0, len(petDetails))
		for _, petDetail := range petDetails {
			var staffInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo_StaffInfo
			if petDetail.GetStaffId() != 0 {
				staffInfo = &fulfillmentpb.FulfillmentReportCardSummaryInfo_StaffInfo{
					StaffId:         petDetail.GetStaffId(),
					StaffFirstName:  staffMap[petDetail.GetStaffId()].GetFirstName(),
					StaffLastName:   staffMap[petDetail.GetStaffId()].GetLastName(),
					StaffAvatarPath: staffMap[petDetail.GetStaffId()].GetAvatarPath(),
				}
			}

			petDetailInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetDetailInfo{
				PetId:           petDetail.GetPetId(),
				ServiceId:       petDetail.GetServiceId(),
				ServiceName:     serviceMap[petDetail.GetServiceId()].GetName(),
				ServiceType:     int32(petDetail.GetServiceType()),
				StartTime:       petDetail.GetStartTime(),
				ServiceDuration: petDetail.GetServiceTime(),
				StaffInfo:       staffInfo,
			}
			petDetailInfos = append(petDetailInfos, petDetailInfo)
		}

		petService := &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetService{
			PetInfo:    petInfo,
			PetDetails: petDetailInfos,
		}

		petServices = append(petServices, petService)
	}

	appointmentInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo_AppointmentInfo{
		AppointmentId:           appointment.GetId(),
		State:                   fulfillmentpb.AppointmentState(appointment.GetStatus()),
		AppointmentDate:         appointment.GetAppointmentDate(),
		AppointmentStartTime:    appointment.GetAppointmentStartTime(),
		AppointmentEndTime:      appointment.GetAppointmentEndTime(),
		AppointmentDateTimeText: appointment.GetAppointmentDate(),
	}

	if arrivalWindowSetting.ArrivalWindowStatus {
		appointmentInfo.ArrivalWindowBefore = appointment.GetAppointmentStartTime() - int32(arrivalWindowSetting.ArrivalBefore)
		appointmentInfo.ArrivalWindowAfter = appointment.GetAppointmentEndTime() - int32(arrivalWindowSetting.ArrivalAfter)
	}

	appointmentInfo.AppointmentDateTimeText = buildAppointmentDateTimeText(appointmentInfo,
		companyPreferenceSetting.DateFormatType,
		companyPreferenceSetting.TimeFormatType,
		report.Template.NextAppointmentDateFormatType == fulfillmentpb.NextAppointmentDateFormatType_ONLY_DATE)
	appointmentInfo.PetService = petServices

	return appointmentInfo
}

func buildAppointmentDateTimeText(appointmentInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo_AppointmentInfo,
	dateFormat organizationpb.DateFormat, timeFormat organizationpb.TimeFormat, showOnlyDate bool) string {

	// 解析日期
	appointmentDate, err := time.Parse("2006-01-02", appointmentInfo.AppointmentDate)
	if err != nil {
		return ""
	}

	// 格式化日期
	appointmentDateText := formatDateByBusiness(appointmentDate, dateFormat)

	if showOnlyDate {
		// 只展示 date
		return appointmentDateText
	}

	// 检查是否有 arrival window
	if appointmentInfo.ArrivalWindowBefore != 0 && appointmentInfo.ArrivalWindowAfter != 0 &&
		appointmentInfo.ArrivalWindowBefore != appointmentInfo.ArrivalWindowAfter {
		// 有 arrival window，显示 arrival window 时间
		arrivalWindowText := formatArrivalWindowTime(
			appointmentInfo.ArrivalWindowBefore,
			appointmentInfo.ArrivalWindowAfter,
			timeFormat)

		return appointmentDateText + ", arrive between: " + arrivalWindowText
	}
	// 没有 arrival window，显示日期和时间
	return getAppointmentDateAndTimeStr(
		appointmentInfo.AppointmentDate,
		appointmentInfo.AppointmentStartTime,
		dateFormat,
		timeFormat)
}

// formatDateByBusiness 根据业务格式格式化日期
func formatDateByBusiness(date time.Time, dateFormat organizationpb.DateFormat) string {
	switch dateFormat {
	case organizationpb.DateFormat_MM_DD_YYYY_LINE:
		return date.Format("01/02/2006")
	case organizationpb.DateFormat_DD_MM_YYYY_LINE:
		return date.Format("02/01/2006")
	case organizationpb.DateFormat_DD_MM_YYYY_DOT:
		return date.Format("02.01.2006")
	case organizationpb.DateFormat_YYYY_MM_DD_DOT:
		return date.Format("2006.01.02")
	case organizationpb.DateFormat_YYYY_MM_DD_LINE:
		return date.Format("2006/01/02")
	case organizationpb.DateFormat_MMM_DD_YYYY_LINE:
		return date.Format("Jan 02/2006")
	case organizationpb.DateFormat_MM_DD_YY_LINE:
		return date.Format("01/02/06")
	default:
		return date.Format("2006-01-02")
	}
}

// formatArrivalWindowTime 格式化 arrival window 时间
func formatArrivalWindowTime(arrivalWindowStartTime, arrivalWindowEndTime int32, timeFormat organizationpb.TimeFormat) string {
	// 将分钟转换为 LocalTime
	startTime := time.Date(2000, 1, 1, 0, 0, 0, 0, time.UTC).Add(time.Duration(arrivalWindowStartTime) * time.Minute)
	endTime := time.Date(2000, 1, 1, 0, 0, 0, 0, time.UTC).Add(time.Duration(arrivalWindowEndTime) * time.Minute)

	var startTimeStr, endTimeStr string
	if timeFormat == organizationpb.TimeFormat_HOUR_24 {
		startTimeStr = startTime.Format("15:04")
		endTimeStr = endTime.Format("15:04")
	} else {
		startTimeStr = startTime.Format("03:04 PM")
		endTimeStr = endTime.Format("03:04 PM")
	}

	// 使用 en dash (–) 而不是减号
	return startTimeStr + " – " + endTimeStr
}

// getAppointmentDateAndTimeStr 获取预约日期和时间字符串
func getAppointmentDateAndTimeStr(appointmentDateStr string, startTime int32,
	dateFormat organizationpb.DateFormat, timeFormat organizationpb.TimeFormat) string {
	appointmentDate, err := time.Parse("2006-01-02", appointmentDateStr)
	if err != nil {
		return ""
	}

	// 计算实际时间
	actualTime := appointmentDate.Add(time.Duration(startTime) * time.Minute)

	// 格式化日期
	dateText := formatDateByBusiness(actualTime, dateFormat)

	// 格式化时间
	var timeText string
	if timeFormat == organizationpb.TimeFormat_HOUR_24 {
		timeText = actualTime.Format("15:04")
	} else {
		timeText = actualTime.Format("03:04 PM")
	}

	return dateText + " " + timeText
}

func buildReviewBooster(reviewBooster *message.ReviewBooster) *fulfillmentpb.FulfillmentReportCardSummaryInfo_ReviewBoosterConfig {
	if reviewBooster == nil {
		return nil
	}

	return &fulfillmentpb.FulfillmentReportCardSummaryInfo_ReviewBoosterConfig{
		PositiveScore:    int32(reviewBooster.PositiveScore),
		PositiveYelp:     reviewBooster.PositiveYelp,
		PositiveFacebook: reviewBooster.PositiveFacebook,
		PositiveGoogle:   reviewBooster.PositiveGoogle,
	}
}

func buildReviewBoosterRecord(report *Report, appointmentInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo_AppointmentInfo, reviewBoosterRecords []*message.ReviewBoosterRecord) *fulfillmentpb.FulfillmentReportCardSummaryInfo_ReviewBoosterRecord {
	if report == nil || appointmentInfo == nil || len(reviewBoosterRecords) == 0 {
		return nil
	}

	petID := report.PetID
	staffIDs := make([]int64, 0)
	for _, petService := range appointmentInfo.PetService {
		for _, petDetail := range petService.PetDetails {
			if petDetail.StaffInfo != nil {
				staffIDs = append(staffIDs, petDetail.StaffInfo.StaffId)
			}
		}
	}

	reviewBoosterRecords = lo.FilterMap(reviewBoosterRecords, func(item *message.ReviewBoosterRecord, _ int) (*message.ReviewBoosterRecord, bool) {
		if lo.Contains(item.PetIDs, petID) && lo.ContainsBy(item.StaffIDs, func(staffID int64) bool {
			return lo.Contains(staffIDs, staffID)
		}) {
			return item, true
		}

		return nil, false
	})

	if len(reviewBoosterRecords) == 0 || reviewBoosterRecords[0] == nil {
		return nil
	}

	return &fulfillmentpb.FulfillmentReportCardSummaryInfo_ReviewBoosterRecord{
		PositiveScore: int32(reviewBoosterRecords[0].PositiveScore),
		ReviewContent: reviewBoosterRecords[0].ReviewContent,
		ReviewTime:    int32(reviewBoosterRecords[0].ReviewTime),
	}
}

func buildSummaryBusinessInfo(businessInfo *organizationpb.LocationModel,
	companyPreferenceSetting *organizationpb.CompanyPreferenceSettingModel,
	bookOnlineSetting *appointment.BookOnlineSetting) *fulfillmentpb.FulfillmentReportCardSummaryInfo_BusinessInfo {
	if businessInfo == nil {
		return nil
	}
	summaryBusinessInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo_BusinessInfo{
		BusinessId:     businessInfo.GetId(),
		BusinessName:   businessInfo.GetName(),
		AvatarPath:     businessInfo.GetAvatarPath(),
		PhoneNumber:    businessInfo.GetContactPhoneNumber(),
		BusinessMode:   int32(businessInfo.BusinessMode.Number()),
		Address1:       businessInfo.GetAddress().GetAddress1(),
		Address2:       businessInfo.GetAddress().GetAddress2(),
		AddressCity:    businessInfo.GetAddress().GetCity(),
		AddressState:   businessInfo.GetAddress().GetState(),
		AddressZipcode: businessInfo.GetAddress().GetZipcode(),
		AddressCountry: businessInfo.GetAddress().GetCountry(),
		Coordinate:     businessInfo.GetAddress().GetCoordinate(),
	}

	if companyPreferenceSetting != nil {
		summaryBusinessInfo.DateFormat = companyPreferenceSetting.GetDateFormatType().String()
		summaryBusinessInfo.TimeFormatType = int32(companyPreferenceSetting.TimeFormatType.Number())
	}

	if bookOnlineSetting != nil {
		summaryBusinessInfo.BookOnlineName = bookOnlineSetting.BookOnlineName
		summaryBusinessInfo.BookOnlineEnable = bookOnlineSetting.IsEnable != 0
	}

	return summaryBusinessInfo
}

func buildSummaryPetInfo(petInfo *businesscustomerpb.BusinessCustomerPetInfoModel,
	companyPreferenceSetting *organizationpb.CompanyPreferenceSettingModel,
) *fulfillmentpb.FulfillmentReportCardSummaryInfo_PetInfo {
	if petInfo == nil {
		return nil
	}

	summaryPetInfo := &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetInfo{
		PetId:      petInfo.GetId(),
		PetName:    petInfo.GetPetName(),
		AvatarPath: petInfo.GetAvatarPath(),
		PetBreed:   petInfo.GetBreed(),
		Gender:     petpb.Pet_PetGender(petpb.Pet_PetGender_value[petInfo.GetGender().String()]),
		PetType:    petpb.Pet_PetType(petpb.Pet_PetType_value[petInfo.PetType.String()]),
		Weight:     petInfo.GetWeight(),
	}

	if companyPreferenceSetting != nil {
		summaryPetInfo.WeightWithUnit = fmt.Sprintf("%s %s", petInfo.GetWeight(),
			companyPreferenceSetting.UnitOfWeightType.String())
	}

	return summaryPetInfo
}
