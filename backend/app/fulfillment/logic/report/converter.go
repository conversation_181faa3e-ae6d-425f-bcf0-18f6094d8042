// nolint: lll
package report

import (
	"context"
	"encoding/json"
	"strings"
	"time"

	"github.com/samber/lo"
	"google.golang.org/protobuf/types/known/timestamppb"

	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	questionrepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/question"
	reportrepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/report"
	sendrecordrepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/sendrecord"
	templaterepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/template"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/message"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	fulfillmentpb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	petpb "github.com/MoeGolibrary/moego/backend/proto/pet/v1"
)

// ==================== Template 转换函数 ====================

// ConvertTemplateRepoToLogic 将 Repo 层的 Template 转换为 Logic 层的 Template
func ConvertTemplateRepoToLogic(repoTemplate *templaterepo.Template) *Template {
	if repoTemplate == nil {
		return nil
	}

	return &Template{
		ID:              repoTemplate.ID,
		CompanyID:       repoTemplate.CompanyID,
		BusinessID:      repoTemplate.BusinessID,
		CareType:        offeringpb.CareCategory(repoTemplate.CareType),
		Title:           repoTemplate.Title,
		ThemeColor:      repoTemplate.ThemeColor,
		LightThemeColor: repoTemplate.LightThemeColor,
		ThemeCode:       repoTemplate.ThemeCode,
		ThankYouMessage: repoTemplate.ThankYouMessage,
		// nolint: lll
		NextAppointmentDateFormatType: fulfillmentpb.NextAppointmentDateFormatType(repoTemplate.NextAppointmentDateFormatType),
		ShowShowcase:                  lo.FromPtrOr(repoTemplate.ShowShowcase, false),
		ShowOverallFeedback:           lo.FromPtrOr(repoTemplate.ShowOverallFeedback, false),
		ShowCustomizedFeedback:        lo.FromPtrOr(repoTemplate.ShowCustomizedFeedback, false),
		ShowPetCondition:              lo.FromPtrOr(repoTemplate.ShowPetCondition, false),
		ShowServiceStaffName:          lo.FromPtrOr(repoTemplate.ShowServiceStaffName, false),
		ShowNextAppointment:           lo.FromPtrOr(repoTemplate.ShowNextAppointment, false),
		ShowReviewBooster:             lo.FromPtrOr(repoTemplate.ShowReviewBooster, false),
		ShowYelpReview:                lo.FromPtrOr(repoTemplate.ShowYelpReview, false),
		ShowGoogleReview:              lo.FromPtrOr(repoTemplate.ShowGoogleReview, false),
		ShowFacebookReview:            lo.FromPtrOr(repoTemplate.ShowFacebookReview, false),
		LastPublishTime:               repoTemplate.LastPublishTime.Unix(),
		UpdateBy:                      repoTemplate.UpdateBy,
		CreateTime:                    &repoTemplate.CreateTime,
		UpdateTime:                    &repoTemplate.UpdateTime,
	}
}

// ConvertTemplateLogicToRepo 将 Logic 层的 Template 转换为 Repo 层的 Template
func ConvertTemplateLogicToRepo(logicTemplate *Template) *templaterepo.Template {
	if logicTemplate == nil {
		return nil
	}

	template := &templaterepo.Template{
		ID:                            logicTemplate.ID,
		CompanyID:                     logicTemplate.CompanyID,
		BusinessID:                    logicTemplate.BusinessID,
		CareType:                      int32(logicTemplate.CareType.Number()),
		Title:                         logicTemplate.Title,
		ThemeColor:                    logicTemplate.ThemeColor,
		LightThemeColor:               logicTemplate.LightThemeColor,
		ThemeCode:                     logicTemplate.ThemeCode,
		ThankYouMessage:               logicTemplate.ThankYouMessage,
		NextAppointmentDateFormatType: int32(logicTemplate.NextAppointmentDateFormatType.Number()),
		ShowShowcase:                  lo.ToPtr(logicTemplate.ShowShowcase),
		ShowOverallFeedback:           lo.ToPtr(logicTemplate.ShowOverallFeedback),
		ShowCustomizedFeedback:        lo.ToPtr(logicTemplate.ShowCustomizedFeedback),
		ShowPetCondition:              lo.ToPtr(logicTemplate.ShowPetCondition),
		ShowServiceStaffName:          lo.ToPtr(logicTemplate.ShowServiceStaffName),
		ShowNextAppointment:           lo.ToPtr(logicTemplate.ShowNextAppointment),
		ShowReviewBooster:             lo.ToPtr(logicTemplate.ShowReviewBooster),
		ShowYelpReview:                lo.ToPtr(logicTemplate.ShowYelpReview),
		ShowGoogleReview:              lo.ToPtr(logicTemplate.ShowGoogleReview),
		ShowFacebookReview:            lo.ToPtr(logicTemplate.ShowFacebookReview),
		LastPublishTime:               time.Unix(logicTemplate.LastPublishTime, 0),
		UpdateBy:                      logicTemplate.UpdateBy,
	}

	if logicTemplate.CreateTime != nil {
		template.CreateTime = *logicTemplate.CreateTime
	}

	if logicTemplate.UpdateTime != nil {
		template.UpdateTime = *logicTemplate.UpdateTime
	}

	return template
}

// ConvertTemplateLogicToPB 将 Logic 层的 Template 转换为 Protobuf
func ConvertTemplateLogicToPB(logicTemplate *Template) *fulfillmentpb.FulfillmentReportTemplate {
	if logicTemplate == nil {
		return nil
	}

	questions := ConvertTemplateQuestionLogicToPB(logicTemplate.Questions)

	template := &fulfillmentpb.FulfillmentReportTemplate{
		Id:                            logicTemplate.ID,
		CompanyId:                     logicTemplate.CompanyID,
		BusinessId:                    logicTemplate.BusinessID,
		CareType:                      logicTemplate.CareType,
		Title:                         logicTemplate.Title,
		ThemeColor:                    logicTemplate.ThemeColor,
		LightThemeColor:               logicTemplate.LightThemeColor,
		ThemeCode:                     logicTemplate.ThemeCode,
		ThankYouMessage:               logicTemplate.ThankYouMessage,
		NextAppointmentDateFormatType: logicTemplate.NextAppointmentDateFormatType,
		ShowShowcase:                  logicTemplate.ShowShowcase,
		ShowOverallFeedback:           logicTemplate.ShowOverallFeedback,
		ShowCustomizedFeedback:        logicTemplate.ShowCustomizedFeedback,
		ShowPetCondition:              logicTemplate.ShowPetCondition,
		ShowStaff:                     logicTemplate.ShowServiceStaffName,
		ShowNextAppointment:           logicTemplate.ShowNextAppointment,
		ShowReviewBooster:             logicTemplate.ShowReviewBooster,
		ShowYelpReview:                logicTemplate.ShowYelpReview,
		ShowGoogleReview:              logicTemplate.ShowGoogleReview,
		ShowFacebookReview:            logicTemplate.ShowFacebookReview,
		GoogleReviewLink:              logicTemplate.GoogleReviewLink,
		YelpReviewLink:                logicTemplate.YelpReviewLink,
		FacebookReviewLink:            logicTemplate.FacebookReviewLink,
		LastPublishTime:               timestamppb.New(time.Unix(logicTemplate.LastPublishTime, 0)),
		UpdateBy:                      logicTemplate.UpdateBy,
		Questions:                     questions,
	}

	if logicTemplate.CreateTime != nil {
		template.CreateTime = timestamppb.New(*logicTemplate.CreateTime)
	}

	if logicTemplate.UpdateTime != nil {
		template.UpdateTime = timestamppb.New(*logicTemplate.UpdateTime)
	}

	return template
}

func ConvertTemplateQuestionToQuestions(questions *TemplateQuestion) []*Question {
	if questions == nil {
		return nil
	}

	var allQuestions []*Question
	allQuestions = append(allQuestions, questions.Feedback...)
	allQuestions = append(allQuestions, questions.PetConditions...)
	allQuestions = append(allQuestions, questions.CustomizeFeedback...)

	return allQuestions
}

func ConvertTemplateQuestionLogicToPB(questions *TemplateQuestion) []*fulfillmentpb.FulfillmentReportTemplateQuestion {

	if questions == nil {
		return nil
	}

	feedbackQuestions := make([]*fulfillmentpb.FulfillmentReportTemplateQuestion, 0, len(questions.Feedback))
	for _, question := range questions.Feedback {
		feedbackQuestions = append(feedbackQuestions, ConvertQuestionLogicToPB(question))
	}

	petConditionsQuestions := make([]*fulfillmentpb.FulfillmentReportTemplateQuestion, 0, len(questions.PetConditions))
	for _, question := range questions.PetConditions {
		petConditionsQuestions = append(petConditionsQuestions, ConvertQuestionLogicToPB(question))
	}

	customizeFeedbackQuestions := make([]*fulfillmentpb.FulfillmentReportTemplateQuestion, 0, len(questions.CustomizeFeedback))
	for _, question := range questions.CustomizeFeedback {
		customizeFeedbackQuestions = append(customizeFeedbackQuestions, ConvertQuestionLogicToPB(question))
	}

	allQuestions := make([]*fulfillmentpb.FulfillmentReportTemplateQuestion, 0,
		len(questions.Feedback)+len(questions.PetConditions))
	allQuestions = append(allQuestions, feedbackQuestions...)
	allQuestions = append(allQuestions, petConditionsQuestions...)
	allQuestions = append(allQuestions, customizeFeedbackQuestions...)

	return allQuestions
}

// ConvertTemplatePBToLogic 将 Protobuf 转换为 Logic 层的 Template
func ConvertTemplatePBToLogic(pbTemplate *fulfillmentpb.FulfillmentReportTemplate) *Template {
	if pbTemplate == nil {
		return nil
	}

	template := &Template{
		ID:                            pbTemplate.GetId(),
		CompanyID:                     pbTemplate.GetCompanyId(),
		BusinessID:                    pbTemplate.GetBusinessId(),
		CareType:                      pbTemplate.GetCareType(),
		Title:                         pbTemplate.GetTitle(),
		ThemeColor:                    pbTemplate.GetThemeColor(),
		LightThemeColor:               pbTemplate.GetLightThemeColor(),
		ThemeCode:                     pbTemplate.GetThemeCode(),
		ThankYouMessage:               pbTemplate.GetThankYouMessage(),
		NextAppointmentDateFormatType: pbTemplate.GetNextAppointmentDateFormatType(),
		ShowShowcase:                  pbTemplate.GetShowShowcase(),
		ShowOverallFeedback:           pbTemplate.GetShowOverallFeedback(),
		ShowCustomizedFeedback:        pbTemplate.GetShowCustomizedFeedback(),
		ShowPetCondition:              pbTemplate.GetShowPetCondition(),
		ShowServiceStaffName:          pbTemplate.GetShowStaff(),
		ShowNextAppointment:           pbTemplate.GetShowNextAppointment(),
		ShowReviewBooster:             pbTemplate.GetShowReviewBooster(),
		ShowYelpReview:                pbTemplate.GetShowYelpReview(),
		ShowGoogleReview:              pbTemplate.GetShowGoogleReview(),
		ShowFacebookReview:            pbTemplate.GetShowFacebookReview(),
		GoogleReviewLink:              pbTemplate.GetGoogleReviewLink(),
		YelpReviewLink:                pbTemplate.GetYelpReviewLink(),
		FacebookReviewLink:            pbTemplate.GetFacebookReviewLink(),
		LastPublishTime:               pbTemplate.GetLastPublishTime().AsTime().Unix(),
		UpdateBy:                      pbTemplate.GetUpdateBy(),
		Questions:                     ConvertTemplateQuestionPBToLogic(pbTemplate.GetQuestions()),
	}

	if pbTemplate.CreateTime != nil {
		template.CreateTime = lo.ToPtr(pbTemplate.GetCreateTime().AsTime())
	}

	if pbTemplate.UpdateTime != nil {
		template.UpdateTime = lo.ToPtr(pbTemplate.GetUpdateTime().AsTime())
	}

	return template
}

func ConverterUpdateTemplateRequestToEntity(
	req *fulfillmentpb.UpdateFulfillmentReportTemplateRequest) *Template {

	feedbackQuestions := make([]*Question, 0)
	petConditionsQuestions := make([]*Question, 0)
	customizeFeedbackQuestions := make([]*Question, 0)
	for _, question := range req.GetQuestions() {
		logicQuestion := &Question{
			ID:                question.GetId(),
			CompanyID:         req.GetCompanyId(),
			BusinessID:        req.GetBusinessId(),
			CareType:          req.GetCareType(),
			Category:          question.GetCategory(),
			Type:              question.GetType().String(),
			Key:               question.GetKey(),
			Title:             question.GetTitle(),
			IsDefault:         question.GetIsDefault(),
			IsRequired:        question.GetIsRequired(),
			IsTypeEditable:    question.GetIsTypeEditable(),
			IsTitleEditable:   question.GetIsTitleEditable(),
			IsOptionsEditable: question.GetIsOptionsEditable(),
			Sort:              question.GetSort(),
			Extra:             ConvertExtraInfoPBToLogic(question.GetExtra()),
		}
		switch question.GetCategory() {
		case fulfillmentpb.QuestionCategory_FEEDBACK:
			feedbackQuestions = append(feedbackQuestions, logicQuestion)
		case fulfillmentpb.QuestionCategory_PET_CONDITION:
			petConditionsQuestions = append(petConditionsQuestions, logicQuestion)
		case fulfillmentpb.QuestionCategory_CUSTOMIZE_FEEDBACK:
			customizeFeedbackQuestions = append(customizeFeedbackQuestions, logicQuestion)
		}
	}

	return &Template{
		ID:                            req.GetId(),
		CompanyID:                     req.GetCompanyId(),
		BusinessID:                    req.GetBusinessId(),
		CareType:                      req.GetCareType(),
		Title:                         req.GetTitle(),
		ThemeColor:                    req.GetThemeColor(),
		LightThemeColor:               req.GetLightThemeColor(),
		ThemeCode:                     req.GetThemeCode(),
		ThankYouMessage:               req.GetThankYouMessage(),
		ShowShowcase:                  req.GetShowShowcase(),
		ShowOverallFeedback:           req.GetShowOverallFeedback(),
		ShowPetCondition:              req.GetShowPetCondition(),
		ShowServiceStaffName:          req.GetShowStaff(),
		ShowCustomizedFeedback:        req.GetShowCustomizedFeedback(),
		ShowNextAppointment:           req.GetShowNextAppointment(),
		NextAppointmentDateFormatType: req.GetNextAppointmentDateFormatType(),
		ShowReviewBooster:             req.GetShowReviewBooster(),
		ShowYelpReview:                req.GetShowYelpReview(),
		YelpReviewLink:                req.GetYelpReviewLink(),
		ShowGoogleReview:              req.GetShowGoogleReview(),
		GoogleReviewLink:              req.GetGoogleReviewLink(),
		ShowFacebookReview:            req.GetShowFacebookReview(),
		FacebookReviewLink:            req.GetFacebookReviewLink(),
		UpdateBy:                      req.GetStaffId(),
		Questions: &TemplateQuestion{
			Feedback:          feedbackQuestions,
			PetConditions:     petConditionsQuestions,
			CustomizeFeedback: customizeFeedbackQuestions,
		},
	}
}

func buildTemplate(template *Template, questions *TemplateQuestion, reviewBooster *message.ReviewBooster) *Template {
	if template == nil {
		return nil
	}

	template.Questions = questions

	if reviewBooster != nil {
		template.FacebookReviewLink = lo.FromPtr(reviewBooster.PositiveFacebook)
		template.GoogleReviewLink = lo.FromPtr(reviewBooster.PositiveGoogle)
		template.YelpReviewLink = lo.FromPtr(reviewBooster.PositiveYelp)
	}

	return template
}

// ==================== Question 转换函数 ====================

func ConvertTemplateQuestionPBToLogic(
	pbQuestions []*fulfillmentpb.FulfillmentReportTemplateQuestion) *TemplateQuestion {

	if pbQuestions == nil {
		return nil
	}

	feedbackQuestions := make([]*Question, 0)
	petConditionsQuestions := make([]*Question, 0)

	for _, question := range pbQuestions {
		switch question.Category {
		case fulfillmentpb.QuestionCategory_PET_CONDITION:
			petConditionsQuestions = append(petConditionsQuestions, ConvertQuestionPBToLogic(question))
		case fulfillmentpb.QuestionCategory_FEEDBACK, fulfillmentpb.QuestionCategory_CUSTOMIZE_FEEDBACK:
			feedbackQuestions = append(feedbackQuestions, ConvertQuestionPBToLogic(question))
		}
	}

	return &TemplateQuestion{Feedback: feedbackQuestions, PetConditions: petConditionsQuestions}
}

func ConvertQuestionRepoToLogics(ctx context.Context,
	repoQuestion []*questionrepo.Question) (*TemplateQuestion, error) {
	if len(repoQuestion) == 0 {
		return nil, nil
	}

	feedbackQuestions := make([]*Question, 0, len(repoQuestion))
	petConditionsQuestions := make([]*Question, 0, len(repoQuestion))
	customizeFeedbackQuestions := make([]*Question, 0, len(repoQuestion))

	for _, question := range repoQuestion {
		question, err := ConvertQuestionRepoToLogic(ctx, question)
		if err != nil {
			return nil, err
		}
		switch question.Category {
		case fulfillmentpb.QuestionCategory_FEEDBACK:
			feedbackQuestions = append(feedbackQuestions, question)
		case fulfillmentpb.QuestionCategory_PET_CONDITION:
			petConditionsQuestions = append(petConditionsQuestions, question)
		case fulfillmentpb.QuestionCategory_CUSTOMIZE_FEEDBACK:
			customizeFeedbackQuestions = append(customizeFeedbackQuestions, question)
		}
	}

	return &TemplateQuestion{Feedback: feedbackQuestions, PetConditions: petConditionsQuestions, CustomizeFeedback: customizeFeedbackQuestions}, nil
}

// ConvertQuestionRepoToLogic 将 Repo 层的 Question 转换为 Logic 层的 Question
func ConvertQuestionRepoToLogic(ctx context.Context, repoQuestion *questionrepo.Question) (*Question, error) {
	if repoQuestion == nil {
		return nil, nil
	}

	var extra *ExtraInfo
	if repoQuestion.ExtraJSON != "" {
		extra = &ExtraInfo{}
		if err := json.Unmarshal([]byte(repoQuestion.ExtraJSON), extra); err != nil {
			log.ErrorContextf(ctx, "ConvertQuestionRepoToLogic unmarshal extra err: %v", err)

			return nil, err
		}
	}

	return &Question{
		ID:                repoQuestion.ID,
		CompanyID:         repoQuestion.CompanyID,
		BusinessID:        repoQuestion.BusinessID,
		CareType:          offeringpb.CareCategory(repoQuestion.CareType),
		Category:          fulfillmentpb.QuestionCategory(repoQuestion.Category),
		Type:              repoQuestion.Type,
		Key:               repoQuestion.Key,
		Title:             repoQuestion.Title,
		Sort:              lo.FromPtr(repoQuestion.Sort),
		IsDefault:         lo.FromPtrOr(repoQuestion.IsDefault, false),
		IsRequired:        lo.FromPtrOr(repoQuestion.IsRequired, false),
		IsTypeEditable:    lo.FromPtrOr(repoQuestion.IsTypeEditable, false),
		IsTitleEditable:   lo.FromPtrOr(repoQuestion.IsTitleEditable, false),
		IsOptionsEditable: lo.FromPtrOr(repoQuestion.IsOptionsEditable, false),
		Extra:             extra,
		CreateTime:        &repoQuestion.CreateTime,
		UpdateTime:        &repoQuestion.UpdateTime,
	}, nil
}

// ConvertQuestionLogicToRepo 将 Logic 层的 Question 转换为 Repo 层的 Question
func ConvertQuestionLogicToRepo(logicQuestion *Question) *questionrepo.Question {
	if logicQuestion == nil {
		return nil
	}

	extraJSON := "{}"
	if logicQuestion.Extra != nil {
		if data, err := json.Marshal(logicQuestion.Extra); err == nil {
			extraJSON = string(data)
		}
	}

	question := &questionrepo.Question{
		ID:                logicQuestion.ID,
		CompanyID:         logicQuestion.CompanyID,
		BusinessID:        logicQuestion.BusinessID,
		CareType:          int32(logicQuestion.CareType.Number()),
		Category:          int32(logicQuestion.Category.Number()),
		Type:              logicQuestion.Type,
		Key:               logicQuestion.Key,
		Title:             logicQuestion.Title,
		Sort:              lo.ToPtr(logicQuestion.Sort),
		IsDefault:         lo.ToPtr(logicQuestion.IsDefault),
		IsRequired:        lo.ToPtr(logicQuestion.IsRequired),
		IsTypeEditable:    lo.ToPtr(logicQuestion.IsTypeEditable),
		IsTitleEditable:   lo.ToPtr(logicQuestion.IsTitleEditable),
		IsOptionsEditable: lo.ToPtr(logicQuestion.IsOptionsEditable),
		ExtraJSON:         extraJSON,
	}

	if logicQuestion.CreateTime != nil {
		question.CreateTime = *logicQuestion.CreateTime
	}

	if logicQuestion.UpdateTime != nil {
		question.UpdateTime = *logicQuestion.UpdateTime
	}

	return question
}

// ConvertQuestionLogicToPB 将 Logic 层的 Question 转换为 Protobuf
func ConvertQuestionLogicToPB(logicQuestion *Question) *fulfillmentpb.FulfillmentReportTemplateQuestion {
	if logicQuestion == nil {
		return nil
	}

	question := &fulfillmentpb.FulfillmentReportTemplateQuestion{
		Id:                logicQuestion.ID,
		CareType:          logicQuestion.CareType,
		Category:          logicQuestion.Category,
		Type:              fulfillmentpb.QuestionType(fulfillmentpb.QuestionType_value[strings.ToUpper(logicQuestion.Type)]),
		Key:               logicQuestion.Key,
		Title:             logicQuestion.Title,
		IsDefault:         logicQuestion.IsDefault,
		IsRequired:        logicQuestion.IsRequired,
		IsTypeEditable:    logicQuestion.IsTypeEditable,
		IsTitleEditable:   logicQuestion.IsTitleEditable,
		IsOptionsEditable: logicQuestion.IsOptionsEditable,
		Sort:              logicQuestion.Sort,
		Extra:             ConvertExtraInfoLogicToPB(logicQuestion.Extra),
	}

	if logicQuestion.CreateTime != nil {
		question.CreateTime = timestamppb.New(*logicQuestion.CreateTime)
	}

	if logicQuestion.UpdateTime != nil {
		question.UpdateTime = timestamppb.New(*logicQuestion.UpdateTime)
	}

	return question
}

// ConvertQuestionPBToLogic 将 Protobuf 转换为 Logic 层的 Question
func ConvertQuestionPBToLogic(pbQuestion *fulfillmentpb.FulfillmentReportTemplateQuestion) *Question {
	if pbQuestion == nil {
		return nil
	}

	return &Question{
		ID:                pbQuestion.GetId(),
		CareType:          pbQuestion.GetCareType(),
		Category:          pbQuestion.GetCategory(),
		Type:              pbQuestion.GetType().String(),
		Key:               pbQuestion.GetKey(),
		Title:             pbQuestion.GetTitle(),
		IsDefault:         pbQuestion.GetIsDefault(),
		IsRequired:        pbQuestion.GetIsRequired(),
		IsTypeEditable:    pbQuestion.GetIsTypeEditable(),
		IsTitleEditable:   pbQuestion.GetIsTitleEditable(),
		IsOptionsEditable: pbQuestion.GetIsOptionsEditable(),
		Sort:              pbQuestion.GetSort(),
		CreateTime:        lo.ToPtr(pbQuestion.GetCreateTime().AsTime()),
		UpdateTime:        lo.ToPtr(pbQuestion.GetUpdateTime().AsTime()),
		Extra:             ConvertExtraInfoPBToLogic(pbQuestion.GetExtra()),
	}
}

func ConvertExtraInfoLogicToPB(extra *ExtraInfo) *fulfillmentpb.FulfillmentReportTemplateQuestion_ExtraInfo {

	if extra == nil {
		return nil
	}

	return &fulfillmentpb.FulfillmentReportTemplateQuestion_ExtraInfo{
		BuildInOptions: extra.BuildInOptions,
		Options:        extra.Options,
	}
}

func ConvertExtraInfoPBToLogic(pbExtra *fulfillmentpb.FulfillmentReportTemplateQuestion_ExtraInfo) *ExtraInfo {

	if pbExtra == nil {
		return nil
	}

	return &ExtraInfo{
		BuildInOptions: pbExtra.BuildInOptions,
		Options:        pbExtra.Options,
	}
}

// ==================== Report 转换函数 ====================

func ConvertGetFulfillmentReportRequestToEntity(req *fulfillmentpb.GetFulfillmentReportRequest) *GetFulfillmentReport {
	return &GetFulfillmentReport{
		ID:            req.GetReportId(),
		CompanyID:     req.GetCompanyId(),
		BusinessID:    req.GetBusinessId(),
		AppointmentID: req.GetAppointmentId(),
		PetID:         req.GetPetId(),
		CareType:      req.GetCareType(),
		ServiceDate:   req.GetServiceDate(),
	}
}

func ConverterUpdateReportRequestToEntity(req *fulfillmentpb.UpdateFulfillmentReportRequest) *Report {

	content := ConvertContentPBToLogic(req.GetContent())

	return &Report{
		ID:            req.GetId(),
		CompanyID:     req.GetCompanyId(),
		BusinessID:    req.GetBusinessId(),
		AppointmentID: req.GetAppointmentId(),
		PetID:         req.GetPetId(),
		CareType:      req.GetCareType(),
		ServiceDate:   req.GetServiceDate(),
		ThemeCode:     req.GetThemeCode(),
		UpdateBy:      req.GetStaffId(),
		Content:       content,
	}
}

func ConvertGenerateMessageContentRequestToEntity(
	req *fulfillmentpb.GenerateMessageContentRequest) *GetFulfillmentReport {
	return &GetFulfillmentReport{
		CompanyID:     req.GetCompanyId(),
		BusinessID:    req.GetBusinessId(),
		AppointmentID: req.GetAppointmentId(),
		PetID:         req.GetPetId(),
		CareType:      req.GetCareType(),
		ServiceDate:   req.GetServiceDate(),
	}
}

// ConvertReportReposToLogic 将 Repo 层的 Report 转换为 Logic 层的 Report
func ConvertReportReposToLogic(ctx context.Context, repoReport []*reportrepo.Report) ([]*Report, error) {
	logicReports := make([]*Report, 0, len(repoReport))
	for _, report := range repoReport {
		logicReport, err := ConvertReportRepoToLogic(ctx, report)
		if err != nil {
			return nil, err
		}
		logicReports = append(logicReports, logicReport)
	}

	return logicReports, nil
}

// ConvertReportRepoToLogic 将 Repo 层的 Report 转换为 Logic 层的 Report
func ConvertReportRepoToLogic(ctx context.Context, repoReport *reportrepo.Report) (*Report, error) {
	if repoReport == nil {
		return nil, nil
	}

	template := &Template{}
	content := &Content{}

	// 解析 TemplateJSON
	if repoReport.TemplateJSON != "" {
		if err := json.Unmarshal([]byte(repoReport.TemplateJSON), template); err != nil {
			log.ErrorContextf(ctx, "ConvertReportRepoToLogic unmarshal template err: %v", err)

			return nil, err
		}
	}

	// 解析 ContentJSON
	if repoReport.ContentJSON != "" {
		if err := json.Unmarshal([]byte(repoReport.ContentJSON), content); err != nil {
			log.ErrorContextf(ctx, "ConvertReportRepoToLogic unmarshal content err: %v", err)

			return nil, err
		}
	}

	return &Report{
		ID:              repoReport.ID,
		CompanyID:       repoReport.CompanyID,
		BusinessID:      repoReport.BusinessID,
		CustomerID:      repoReport.CustomerID,
		AppointmentID:   repoReport.AppointmentID,
		PetID:           repoReport.PetID,
		PetTypeID:       repoReport.PetTypeID,
		CareType:        offeringpb.CareCategory(repoReport.CareType),
		ServiceDate:     repoReport.ServiceDate,
		Status:          fulfillmentpb.ReportStatus(fulfillmentpb.ReportStatus_value[strings.ToUpper(repoReport.Status)]),
		UUID:            repoReport.UUID,
		LinkOpenedCount: repoReport.LinkOpenedCount,
		ThemeCode:       repoReport.ThemeCode,
		TemplateVersion: repoReport.TemplateVersion,
		UpdateBy:        repoReport.UpdateBy,
		CreateTime:      &repoReport.CreateTime,
		UpdateTime:      &repoReport.UpdateTime,
		Template:        template,
		Content:         content,
	}, nil
}

// ConvertReportLogicToPB 将 Logic 层的 Report 转换为 Protobuf
func ConvertReportLogicToPB(logicReport *Report) *fulfillmentpb.FulfillmentReport {
	if logicReport == nil {
		return nil
	}

	var template *fulfillmentpb.FulfillmentReportTemplate
	var content *fulfillmentpb.FulfillmentReportContent

	if logicReport.Template != nil {
		template = ConvertTemplateLogicToPB(logicReport.Template)
	}

	if logicReport.Content != nil {
		content = ConvertContentLogicToPB(logicReport.Content)
	}

	report := &fulfillmentpb.FulfillmentReport{
		Id:              lo.ToPtr(logicReport.ID),
		CompanyId:       logicReport.CompanyID,
		BusinessId:      logicReport.BusinessID,
		CustomerId:      logicReport.CustomerID,
		AppointmentId:   logicReport.AppointmentID,
		PetId:           logicReport.PetID,
		PetTypeId:       logicReport.PetTypeID,
		CareType:        logicReport.CareType,
		ServiceDate:     logicReport.ServiceDate,
		Status:          logicReport.Status,
		Uuid:            logicReport.UUID,
		LinkOpenedCount: logicReport.LinkOpenedCount,
		ThemeCode:       logicReport.ThemeCode,
		TemplateVersion: timestamppb.New(logicReport.TemplateVersion),
		Template:        template,
		Content:         content,
	}
	if logicReport.CreateTime != nil {
		report.CreateTime = timestamppb.New(*logicReport.CreateTime)
	}
	if logicReport.UpdateTime != nil {
		report.UpdateTime = timestamppb.New(*logicReport.UpdateTime)
	}

	return report
}

func ConvertReportLogicToRepo(ctx context.Context, logicReport *Report) (*reportrepo.Report, error) {

	if logicReport == nil {
		return nil, nil
	}

	contentJSON, err := json.Marshal(logicReport.Content)
	if err != nil {
		log.ErrorContextf(ctx, "ConvertReportLogicToRepo marshal content err: %v", err)

		return nil, err
	}

	templateJSON, err := json.Marshal(logicReport.Template)
	if err != nil {
		log.ErrorContextf(ctx, "ConvertReportLogicToRepo marshal template err: %v", err)

		return nil, err
	}

	report := &reportrepo.Report{
		ID:              logicReport.ID,
		CompanyID:       logicReport.CompanyID,
		BusinessID:      logicReport.BusinessID,
		CustomerID:      logicReport.CustomerID,
		AppointmentID:   logicReport.AppointmentID,
		PetID:           logicReport.PetID,
		PetTypeID:       logicReport.PetTypeID,
		CareType:        int32(logicReport.CareType.Number()),
		ServiceDate:     logicReport.ServiceDate,
		Status:          logicReport.Status.Enum().String(),
		UUID:            logicReport.UUID,
		LinkOpenedCount: logicReport.LinkOpenedCount,
		ThemeCode:       logicReport.ThemeCode,
		TemplateVersion: logicReport.TemplateVersion,
		UpdateBy:        logicReport.UpdateBy,
		TemplateJSON:    string(templateJSON),
		ContentJSON:     string(contentJSON),
	}

	if logicReport.CreateTime != nil {
		report.CreateTime = *logicReport.CreateTime
	}
	if logicReport.UpdateTime != nil {
		report.UpdateTime = *logicReport.UpdateTime
	}

	return report, nil
}

func ConvertReportLogicToGetFulfillmentReport(logicReport *Report) *GetFulfillmentReport {
	return &GetFulfillmentReport{
		ID:            logicReport.ID,
		CompanyID:     logicReport.CompanyID,
		BusinessID:    logicReport.BusinessID,
		AppointmentID: logicReport.AppointmentID,
		PetID:         logicReport.PetID,
		CareType:      logicReport.CareType,
		ServiceDate:   logicReport.ServiceDate,
	}
}

func ConvertFulfillmentReportToReportLogic(reportPB *fulfillmentpb.FulfillmentReport) *Report {
	report := &Report{
		ID:              reportPB.GetId(),
		CompanyID:       reportPB.GetCompanyId(),
		BusinessID:      reportPB.GetBusinessId(),
		AppointmentID:   reportPB.GetAppointmentId(),
		PetID:           reportPB.GetPetId(),
		CareType:        reportPB.GetCareType(),
		ServiceDate:     reportPB.GetServiceDate(),
		Status:          reportPB.GetStatus(),
		UUID:            reportPB.GetUuid(),
		LinkOpenedCount: reportPB.GetLinkOpenedCount(),
		ThemeCode:       reportPB.GetThemeCode(),
		TemplateVersion: reportPB.GetTemplateVersion().AsTime(),
		Template:        ConvertTemplatePBToLogic(reportPB.GetTemplate()),
		Content:         ConvertContentPBToLogic(reportPB.GetContent()),
	}

	if reportPB.CreateTime != nil {
		report.CreateTime = lo.ToPtr(reportPB.CreateTime.AsTime())
	}

	if reportPB.UpdateTime != nil {
		report.UpdateTime = lo.ToPtr(reportPB.UpdateTime.AsTime())
	}

	return report
}

// ==================== Content 转换函数 ====================

// ConvertContentLogicToPB 将 Logic 层的 Content 转换为 Protobuf
func ConvertContentLogicToPB(logicContent *Content) *fulfillmentpb.FulfillmentReportContent {
	if logicContent == nil {
		return nil
	}

	feedbacks := make([]*fulfillmentpb.FulfillmentReportQuestion, 0, len(logicContent.Feedbacks))
	for _, feedback := range logicContent.Feedbacks {
		feedbacks = append(feedbacks, ConvertReportQuestionLogicToPB(feedback))
	}

	petConditions := make([]*fulfillmentpb.FulfillmentReportQuestion, 0, len(logicContent.PetConditions))
	for _, condition := range logicContent.PetConditions {
		petConditions = append(petConditions, ConvertReportQuestionLogicToPB(condition))
	}

	var recommendation *fulfillmentpb.FulfillmentReportRecommendation
	if logicContent.Recommendation != nil {
		recommendation = ConvertReportRecommendationLogicToPB(logicContent.Recommendation)
	}

	return &fulfillmentpb.FulfillmentReportContent{
		Photos:          logicContent.Photos,
		Videos:          logicContent.Videos,
		Feedbacks:       feedbacks,
		PetConditions:   petConditions,
		Recommendation:  recommendation,
		ThemeColor:      lo.ToPtr(logicContent.ThemeColor),
		LightThemeColor: logicContent.LightThemeColor,
	}
}

// ConvertContentPBToLogic 将 Protobuf 转换为 Logic 层的 Content
func ConvertContentPBToLogic(pbContent *fulfillmentpb.FulfillmentReportContent) *Content {
	if pbContent == nil {
		return &Content{}
	}

	feedbacks := make([]*ReportQuestion, 0, len(pbContent.GetFeedbacks()))
	for _, feedback := range pbContent.GetFeedbacks() {
		feedbacks = append(feedbacks, ConvertReportQuestionPBToLogic(feedback))
	}

	petConditions := make([]*ReportQuestion, 0, len(pbContent.GetPetConditions()))
	for _, condition := range pbContent.GetPetConditions() {
		petConditions = append(petConditions, ConvertReportQuestionPBToLogic(condition))
	}

	var recommendation *Recommendation
	if pbContent.GetRecommendation() != nil {
		recommendation = ConvertReportRecommendationPBToLogic(pbContent.GetRecommendation())
	}

	return &Content{
		Photos:          pbContent.GetPhotos(),
		Videos:          pbContent.GetVideos(),
		Feedbacks:       feedbacks,
		PetConditions:   petConditions,
		Recommendation:  recommendation,
		ThemeColor:      pbContent.GetThemeColor(),
		LightThemeColor: pbContent.LightThemeColor,
	}
}

// ==================== ReportQuestion 转换函数 ====================

// ConvertReportQuestionLogicToPB 将 Logic 层的 ReportQuestion 转换为 Protobuf
func ConvertReportQuestionLogicToPB(logicQuestion *ReportQuestion) *fulfillmentpb.FulfillmentReportQuestion {
	if logicQuestion == nil {
		return nil
	}

	var urls *fulfillmentpb.BodyViewUrl
	if logicQuestion.URLs != nil {
		urls = &fulfillmentpb.BodyViewUrl{
			Left:  logicQuestion.URLs.Left,
			Right: logicQuestion.URLs.Right,
		}
	}

	return &fulfillmentpb.FulfillmentReportQuestion{
		Id:            logicQuestion.ID,
		Category:      logicQuestion.Category,
		Type:          fulfillmentpb.QuestionType(fulfillmentpb.QuestionType_value[strings.ToUpper(logicQuestion.Type)]),
		Key:           logicQuestion.Key,
		Title:         logicQuestion.Title,
		Required:      logicQuestion.Required,
		IsShow:        logicQuestion.IsShow,
		Options:       logicQuestion.Options,
		Choices:       logicQuestion.Choices,
		CustomOptions: logicQuestion.CustomOptions,
		InputText:     logicQuestion.InputText,
		Placeholder:   logicQuestion.Placeholder,
		Urls:          urls,
	}
}

// ConvertReportQuestionPBToLogic 将 Protobuf 转换为 Logic 层的 ReportQuestion
func ConvertReportQuestionPBToLogic(pbQuestion *fulfillmentpb.FulfillmentReportQuestion) *ReportQuestion {
	if pbQuestion == nil {
		return nil
	}

	var urls *BodyViewURL
	if pbQuestion.GetUrls() != nil {
		urls = &BodyViewURL{
			Left:  pbQuestion.GetUrls().GetLeft(),
			Right: pbQuestion.GetUrls().GetRight(),
		}
	}

	return &ReportQuestion{
		ID:            pbQuestion.GetId(),
		Category:      pbQuestion.GetCategory(),
		Type:          pbQuestion.GetType().String(),
		Key:           pbQuestion.GetKey(),
		Title:         pbQuestion.GetTitle(),
		Required:      pbQuestion.GetRequired(),
		IsShow:        pbQuestion.GetIsShow(),
		Options:       pbQuestion.GetOptions(),
		Choices:       pbQuestion.GetChoices(),
		CustomOptions: pbQuestion.GetCustomOptions(),
		InputText:     pbQuestion.GetInputText(),
		Placeholder:   pbQuestion.GetPlaceholder(),
		URLs:          urls,
	}
}

// ==================== ReportRecommendation 转换函数 ====================

// ConvertReportRecommendationLogicToPB 将 Logic 层的 ReportRecommendation 转换为 Protobuf
func ConvertReportRecommendationLogicToPB(
	logicRecommendation *Recommendation) *fulfillmentpb.FulfillmentReportRecommendation {
	if logicRecommendation == nil {
		return nil
	}

	return &fulfillmentpb.FulfillmentReportRecommendation{
		FrequencyDay:  logicRecommendation.FrequencyDay,
		FrequencyType: logicRecommendation.FrequencyType,
		FrequencyText: lo.ToPtr(logicRecommendation.FrequencyText),
	}
}

// ConvertReportRecommendationPBToLogic 将 Protobuf 转换为 Logic 层的 ReportRecommendation
func ConvertReportRecommendationPBToLogic(
	pbRecommendation *fulfillmentpb.FulfillmentReportRecommendation) *Recommendation {
	if pbRecommendation == nil {
		return nil
	}

	return &Recommendation{
		FrequencyDay:  pbRecommendation.GetFrequencyDay(),
		FrequencyType: pbRecommendation.GetFrequencyType(),
		FrequencyText: pbRecommendation.GetFrequencyText(),
	}
}

// ==================== SendRecord 转换函数 ====================

// ConvertSendRecordLogicsToRepo 将多个 Logic 层的 SendRecord 转换为多个 Repo 层的 SendRecord
func ConvertSendRecordLogicsToRepo(ctx context.Context, logicSendRecords []*SendRecord) ([]*sendrecordrepo.SendRecord, error) {
	repoSendRecords := make([]*sendrecordrepo.SendRecord, 0, len(logicSendRecords))
	for _, logicSendRecord := range logicSendRecords {
		repoSendRecord, err := ConvertSendRecordLogicToRepo(ctx, logicSendRecord)
		if err != nil {
			return nil, err
		}
		repoSendRecords = append(repoSendRecords, repoSendRecord)
	}

	return repoSendRecords, nil
}

// ConvertSendRecordLogicToRepo 将 Logic 层的 SendRecord 转换为 Repo 层的 SendRecord
func ConvertSendRecordLogicToRepo(ctx context.Context, logicSendRecord *SendRecord) (*sendrecordrepo.SendRecord, error) {
	if logicSendRecord == nil {
		return nil, nil
	}

	contentJSON, err := json.Marshal(logicSendRecord.Content)
	if err != nil {
		log.ErrorContextf(ctx, "ConvertSendRecordLogicToRepo marshal content err: %v", err)

		return nil, err
	}

	return &sendrecordrepo.SendRecord{
		ID:            logicSendRecord.ID,
		ReportID:      logicSendRecord.ReportID,
		CompanyID:     logicSendRecord.CompanyID,
		BusinessID:    logicSendRecord.BusinessID,
		AppointmentID: logicSendRecord.AppointmentID,
		ContentJSON:   string(contentJSON),
		PetID:         logicSendRecord.PetID,
		SendMethod:    int32(logicSendRecord.SendMethod.Number()),
		SentTime:      logicSendRecord.SentTime,
		SentBy:        logicSendRecord.SentBy,
		ErrorMessage:  logicSendRecord.ErrorMessage,
		IsSentSuccess: logicSendRecord.IsSentSuccess,
		CreateTime:    logicSendRecord.CreateTime,
		UpdateTime:    logicSendRecord.UpdateTime,
	}, nil
}

// ConvertSendRecordRepoToLogics 将多个 Repo 层的 SendRecord 转换为多个 Logic 层的 SendRecord
func ConvertSendRecordRepoToLogics(ctx context.Context, repoSendRecords []*sendrecordrepo.SendRecord) ([]*SendRecord, error) {
	logicSendRecords := make([]*SendRecord, 0, len(repoSendRecords))
	for _, repoSendRecord := range repoSendRecords {
		logicSendRecord, err := ConvertSendRecordRepoToLogic(ctx, repoSendRecord)
		if err != nil {
			return nil, err
		}
		logicSendRecords = append(logicSendRecords, logicSendRecord)
	}

	return logicSendRecords, nil
}

// ConvertSendRecordRepoToLogic 将 Repo 层的 SendRecord 转换为 Logic 层的 SendRecord
func ConvertSendRecordRepoToLogic(ctx context.Context, repoSendRecord *sendrecordrepo.SendRecord) (*SendRecord, error) {
	if repoSendRecord == nil {
		return nil, nil
	}

	content := &Content{}
	err := json.Unmarshal([]byte(repoSendRecord.ContentJSON), content)

	if err != nil {
		log.ErrorContextf(ctx, "ConvertSendRecordRepoToLogic unmarshal content err: %v", err)

		return nil, err
	}

	return &SendRecord{
		ID:            repoSendRecord.ID,
		ReportID:      repoSendRecord.ReportID,
		CompanyID:     repoSendRecord.CompanyID,
		BusinessID:    repoSendRecord.BusinessID,
		AppointmentID: repoSendRecord.AppointmentID,
		Content:       content,
		PetID:         repoSendRecord.PetID,
		SendMethod:    fulfillmentpb.SendMethod(repoSendRecord.SendMethod),
		SentTime:      repoSendRecord.SentTime,
		SentBy:        repoSendRecord.SentBy,
		ErrorMessage:  repoSendRecord.ErrorMessage,
		IsSentSuccess: repoSendRecord.IsSentSuccess,
		CreateTime:    repoSendRecord.CreateTime,
		UpdateTime:    repoSendRecord.UpdateTime,
	}, nil
}

func ConvertListFulfillmentReportPB(reports []*Report, records []*SendRecord,
	pets []*businesscustomerpb.BusinessCustomerPetInfoModel,
) []*fulfillmentpb.ListFulfillmentReportResponse_FulfillmentReportCard {
	if len(reports) == 0 {
		return []*fulfillmentpb.ListFulfillmentReportResponse_FulfillmentReportCard{}
	}

	sendRecordsMap := make(map[int64][]*SendRecord)
	for _, record := range records {
		sendRecordsMap[record.ReportID] = append(sendRecordsMap[record.ReportID], record)
	}

	petMap := make(map[int64]*businesscustomerpb.BusinessCustomerPetInfoModel)
	for _, pet := range pets {
		petMap[pet.Id] = pet
	}

	reportCards := make([]*fulfillmentpb.ListFulfillmentReportResponse_FulfillmentReportCard, 0, len(reports))
	for _, report := range reports {
		sendRecords := make([]*fulfillmentpb.FulfillmentReportSendRecord, 0)
		for _, record := range sendRecordsMap[report.ID] {
			sendRecords = append(sendRecords, ConvertFulfillmentReportSendRecord(report, record))
		}
		mediaCount := 0
		if report.Content != nil {
			mediaCount = len(report.Content.Photos) + len(report.Content.Videos)
		}
		title := report.Template.Title
		if title == "" {
			title = getDefaultTemplateTitle(report.CareType)
		}
		reportCard := &fulfillmentpb.ListFulfillmentReportResponse_FulfillmentReportCard{
			ReportId:        report.ID,
			CustomerId:      report.CustomerID,
			UpdateTime:      timestamppb.New(*report.UpdateTime),
			CareType:        report.CareType,
			MediaCount:      int32(mediaCount),
			ServiceDate:     report.ServiceDate,
			Uuid:            report.UUID,
			AppointmentId:   report.AppointmentID,
			Title:           title,
			SendRecord:      sendRecords,
			Pet:             buildFulfillmentReportCardPet(petMap[report.PetID]),
			ReportStatus:    report.Status,
			LinkOpenedCount: report.LinkOpenedCount,
		}
		if report.UpdateTime != nil {
			reportCard.UpdateTime = timestamppb.New(*report.UpdateTime)
		}

		reportCards = append(reportCards, reportCard)
	}

	return reportCards
}

func buildFulfillmentReportCardPet(pet *businesscustomerpb.BusinessCustomerPetInfoModel,
) *fulfillmentpb.ListFulfillmentReportResponse_FulfillmentReportCard_Pet {
	if pet == nil {
		return &fulfillmentpb.ListFulfillmentReportResponse_FulfillmentReportCard_Pet{
			PetName:    SamplePetName,
			AvatarPath: SamplePetAvatar,
			PetType:    samplePetTypeID,
		}
	}

	return &fulfillmentpb.ListFulfillmentReportResponse_FulfillmentReportCard_Pet{
		PetId:      pet.Id,
		PetName:    pet.PetName,
		AvatarPath: pet.AvatarPath,
		PetType:    petpb.Pet_PetType(pet.PetType.Number()),
	}
}

func ConvertFulfillmentReportSendRecords(records []*SendRecord, reports []*Report) []*fulfillmentpb.FulfillmentReportSendRecord {
	if len(records) == 0 {
		return []*fulfillmentpb.FulfillmentReportSendRecord{}
	}

	reportMap := make(map[int64]*Report)
	for _, report := range reports {
		reportMap[report.ID] = report
	}

	sendRecords := make([]*fulfillmentpb.FulfillmentReportSendRecord, 0, len(records))
	for _, record := range records {
		report := reportMap[record.ReportID]
		if report == nil {
			continue
		}
		sendRecords = append(sendRecords, ConvertFulfillmentReportSendRecord(reportMap[record.ReportID], record))
	}

	return sendRecords
}

func ConvertFulfillmentReportSendRecord(report *Report, record *SendRecord) *fulfillmentpb.FulfillmentReportSendRecord {
	if report == nil || record == nil {
		return nil
	}

	return &fulfillmentpb.FulfillmentReportSendRecord{
		RecordId:      record.ID,
		ReportId:      record.ReportID,
		AppointmentId: record.AppointmentID,
		PetId:         record.PetID,
		SendMethod:    record.SendMethod,
		SendContent:   ConvertContentLogicToPB(record.Content),
		SendTime:      timestamppb.New(record.SentTime),
		IsSentSuccess: lo.FromPtr(record.IsSentSuccess),
		ErrorMessage:  lo.FromPtr(record.ErrorMessage),
		ServiceDate:   report.ServiceDate,
		CareType:      report.CareType,
		ReportStatus:  report.Status,
		Uuid:          report.UUID,
	}
}

func getDefaultTemplateTitle(careType offeringpb.CareCategory) string {
	switch careType {
	case offeringpb.CareCategory_DAYCARE:
		return defaultDaycareTitle
	case offeringpb.CareCategory_BOARDING:
		return defaultBoardingTitle
	default:
		return defaultGroomingTitle
	}
}
