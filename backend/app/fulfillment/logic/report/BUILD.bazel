load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "report",
    srcs = [
        "const.go",
        "converter.go",
        "entity.go",
        "record.go",
        "report.go",
        "summary_info.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/fulfillment/logic/report",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/fulfillment/config",
        "//backend/app/fulfillment/repo/appointment",
        "//backend/app/fulfillment/repo/customer",
        "//backend/app/fulfillment/repo/db",
        "//backend/app/fulfillment/repo/db/report/question",
        "//backend/app/fulfillment/repo/db/report/report",
        "//backend/app/fulfillment/repo/db/report/sendrecord",
        "//backend/app/fulfillment/repo/db/report/template",
        "//backend/app/fulfillment/repo/message",
        "//backend/app/fulfillment/repo/offering",
        "//backend/app/fulfillment/repo/organization",
        "//backend/app/fulfillment/repo/payment",
        "//backend/app/fulfillment/repo/pet",
        "//backend/app/fulfillment/repo/redis",
        "//backend/app/fulfillment/utils",
        "//backend/common/rpc/framework/log",
        "//backend/common/utils/random",
        "//backend/proto/fulfillment/v1:fulfillment",
        "//backend/proto/offering/v1:offering",
        "//backend/proto/pet/v1:pet",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/appointment/v1:appointment",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/business_customer/v1:business_customer",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/customer/v1:customer",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/offering/v1:offering",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/organization/v1:organization",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/utils/v1:utils",
        "@com_github_samber_lo//:lo",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_genproto//googleapis/type/calendarperiod",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "report_test",
    srcs = ["report_test.go"],
    data = [
        "//backend/app/fulfillment/config:testing_config",
    ],
    embed = [":report"],
    deps = [
        "//backend/app/fulfillment/config",
        "//backend/app/fulfillment/repo/appointment",
        "//backend/app/fulfillment/repo/appointment/mock",
        "//backend/app/fulfillment/repo/customer/mock",
        "//backend/app/fulfillment/repo/db/mock",
        "//backend/app/fulfillment/repo/db/report/question",
        "//backend/app/fulfillment/repo/db/report/question/mock",
        "//backend/app/fulfillment/repo/db/report/report",
        "//backend/app/fulfillment/repo/db/report/report/mock",
        "//backend/app/fulfillment/repo/db/report/sendrecord",
        "//backend/app/fulfillment/repo/db/report/sendrecord/mock",
        "//backend/app/fulfillment/repo/db/report/template",
        "//backend/app/fulfillment/repo/db/report/template/mock",
        "//backend/app/fulfillment/repo/message",
        "//backend/app/fulfillment/repo/message/mock",
        "//backend/app/fulfillment/repo/offering/mock",
        "//backend/app/fulfillment/repo/organization/mock",
        "//backend/app/fulfillment/repo/payment",
        "//backend/app/fulfillment/repo/payment/mock",
        "//backend/app/fulfillment/repo/pet/mock",
        "//backend/app/fulfillment/repo/redis/mock",
        "//backend/common/utils/env",
        "//backend/proto/fulfillment/v1:fulfillment",
        "//backend/proto/offering/v1:offering",
        "//backend/proto/pet/v1:pet",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/appointment/v1:appointment",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/business_customer/v1:business_customer",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/customer/v1:customer",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/offering/v1:offering",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/organization/v1:organization",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/utils/v1:utils",
        "@com_github_samber_lo//:lo",
        "@com_github_stretchr_testify//assert",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_genproto//googleapis/type/calendarperiod",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_mock//gomock",
    ],
)
