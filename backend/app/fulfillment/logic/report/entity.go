package report

import (
	"time"

	fulfillmentpb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

type Template struct {
	ID         int64                   `json:"-"`
	CompanyID  int64                   `json:"-"`
	BusinessID int64                   `json:"-"`
	CareType   offeringpb.CareCategory `json:"careType"`

	Title                         string                                      `json:"title"`
	ThemeColor                    string                                      `json:"themeColor"`
	LightThemeColor               string                                      `json:"lightThemeColor"`
	ThemeCode                     string                                      `json:"themeCode"`
	ThankYouMessage               string                                      `json:"thankYouMessage"`
	NextAppointmentDateFormatType fulfillmentpb.NextAppointmentDateFormatType `json:"next_appointment_date_format_type"`

	ShowShowcase           bool `json:"showShowcase"`
	ShowOverallFeedback    bool `json:"showOverallFeedback"`
	ShowCustomizedFeedback bool `json:"showCustomizedFeedback"`
	ShowPetCondition       bool `json:"showPetCondition"`
	ShowServiceStaffName   bool `json:"showServiceStaffName"`
	ShowNextAppointment    bool `json:"showNextAppointment"`
	ShowReviewBooster      bool `json:"showReviewBooster"`
	ShowYelpReview         bool `json:"showYelpReview"`
	ShowGoogleReview       bool `json:"showGoogleReview"`
	ShowFacebookReview     bool `json:"showFacebookReview"`

	YelpReviewLink     string `json:"yelpReviewLink"`
	GoogleReviewLink   string `json:"googleReviewLink"`
	FacebookReviewLink string `json:"facebookReviewLink"`

	LastPublishTime int64     `json:"lastPublishTime"`
	UpdateBy        int64     `json:"updateBy"`
	CreateTime      time.Time `json:"-"`
	UpdateTime      time.Time `json:"-"`

	Questions *TemplateQuestion `json:"questions"`
}

type TemplateQuestion struct {
	Feedback          []*Question `json:"feedback"`
	PetConditions     []*Question `json:"petConditions"`
	CustomizeFeedback []*Question `json:"customizeFeedback"`
}

type Question struct {
	ID         int64                   `json:"id"`
	CompanyID  int64                   `json:"companyId"`
	BusinessID int64                   `json:"businessId"`
	CareType   offeringpb.CareCategory `json:"careType"`

	Category fulfillmentpb.QuestionCategory `json:"category"`
	Type     string                         `json:"type"`
	Key      string                         `json:"key"`
	Title    string                         `json:"title"`
	Sort     int32                          `json:"sort"`

	IsDefault         bool `json:"isDefault"`
	IsRequired        bool `json:"isRequired"`
	IsTypeEditable    bool `json:"isTypeEditable"`
	IsTitleEditable   bool `json:"isTitleEditable"`
	IsOptionsEditable bool `json:"isOptionsEditable"`

	Extra *ExtraInfo `json:"extra"`

	CreateTime time.Time `json:"-"`
	UpdateTime time.Time `json:"-"`
}

// extra info
type ExtraInfo struct {
	BuildInOptions []string `json:"buildInOptions,omitempty"`
	Options        []string `json:"options,omitempty"`
}

type Report struct {
	ID              int64                      `json:"id"`
	CompanyID       int64                      `json:"company_id"`
	BusinessID      int64                      `json:"businessId"`
	CustomerID      int64                      `json:"customerId"`
	AppointmentID   int64                      `json:"appointmentId"`
	PetID           int64                      `json:"petId"`
	PetTypeID       int64                      `json:"petTypeId"`
	CareType        offeringpb.CareCategory    `json:"careType"`
	ServiceDate     string                     `json:"serviceDate"`
	Status          fulfillmentpb.ReportStatus `json:"status"`
	UUID            string                     `json:"uuid"`
	LinkOpenedCount int32                      `json:"linkOpenedCount"`
	ThemeCode       string                     `json:"themeCode"`
	TemplateVersion time.Time                  `json:"templateVersion"`
	UpdateBy        int64                      `json:"updateBy"`
	CreateTime      time.Time                  `json:"createTime"`
	UpdateTime      time.Time                  `json:"updateTime"`

	// 结构化的模板和内容
	Template *Template `json:"template"`
	Content  *Content  `json:"content"`
}

type Content struct {
	Photos          []string              `json:"photos"`
	Videos          []string              `json:"videos"`
	Feedbacks       []*ReportQuestion     `json:"feedbacks"`
	PetConditions   []*ReportQuestion     `json:"petConditions"`
	Recommendation  *ReportRecommendation `json:"recommendation"`
	ThemeColor      string                `json:"themeColor"`
	LightThemeColor *string               `json:"lightThemeColor,omitempty"`
}

// nolint: revive
type ReportQuestion struct {
	ID                  int64                          `json:"-"`
	FulfillmentReportID int64                          `json:"fulfillment_report_id"`
	Category            fulfillmentpb.QuestionCategory `json:"category"`
	Type                string                         `json:"type"`
	Key                 string                         `json:"key"`
	Title               string                         `json:"title"`
	Required            bool                           `json:"required"`
	IsShow              bool                           `json:"show"`
	Options             []string                       `json:"options"`
	Choices             []string                       `json:"choices"`
	CustomOptions       []string                       `json:"custom_options"`
	InputText           string                         `json:"input_text"`
	Placeholder         string                         `json:"placeholder"`
	URLs                *BodyViewURL                   `json:"urls"`
}

// nolint: revive, lll
type ReportRecommendation struct {
	FrequencyDay            int32                                                       `json:"frequency_day"`
	FrequencyType           fulfillmentpb.FulfillmentReportRecommendation_FrequencyType `json:"frequency_type"`
	FrequencyText           string                                                      `json:"frequency_text"`
	NextAppointmentDate     string                                                      `json:"next_appointment_date"`
	NextAppointmentDateText string                                                      `json:"next_appointment_date_text"`
}

type BodyViewURL struct {
	Left  string `json:"left"`
	Right string `json:"right"`
}

type GetFulfillmentReport struct {
	CompanyID     int64
	BusinessID    int64
	ID            int64
	UUID          string
	AppointmentID int64
	PetID         int64
	CareType      offeringpb.CareCategory
	ServiceDate   string
}
