package report

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"
	"google.golang.org/genproto/googleapis/type/calendarperiod"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"

	appointmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	utilsV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/config"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/appointment"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/customer"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db"
	questionrepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/question"
	reportrepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/report"
	sendrecordrepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/sendrecord"
	templaterepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/template"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/message"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/offering"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/organization"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/payment"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/pet"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/redis"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/utils/random"
	fulfillmentpb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func New() *Logic {
	return &Logic{
		templateRepo:     templaterepo.NewFulfillmentReportTemplateRepo(),
		questionRepo:     questionrepo.NewFulfillmentReportQuestionRepo(),
		reportRepo:       reportrepo.NewFulfillmentReportRepo(),
		sendRecordRepo:   sendrecordrepo.NewFulfillmentReportSendRecordRepo(),
		redisRepo:        redis.New(),
		petRepo:          pet.New(),
		customerRepo:     customer.New(),
		messageRepo:      message.New(),
		organizationRepo: organization.New(),
		appointmentRepo:  appointment.New(),
		paymentRepo:      payment.New(),
		serviceOldRepo:   offering.NewServiceOldReadWriter(),
		tx:               db.NewTxManager(),
	}
}

type Logic struct {
	templateRepo     templaterepo.ReadWriter
	questionRepo     questionrepo.ReadWriter
	reportRepo       reportrepo.ReadWriter
	sendRecordRepo   sendrecordrepo.ReadWriter
	redisRepo        redis.API
	petRepo          pet.ReadWriter
	customerRepo     customer.ReadWriter
	messageRepo      message.ReadWriter
	appointmentRepo  appointment.ReadWriter
	organizationRepo organization.ReadWriter
	paymentRepo      payment.ReadWriter
	serviceOldRepo   offering.ServiceOldReadWriter
	tx               db.TransactionManager
}

func (l *Logic) initTemplate(
	ctx context.Context, companyID, businessID int64, careType offeringpb.CareCategory,
) (*Template, *TemplateQuestion, error) {

	lockKey := fmt.Sprintf(redis.InitFulfillmentReportTemplateLockKey, companyID, businessID, careType.String())
	if err := l.redisRepo.LockWithRetry(
		ctx, lockKey, redis.InitFulfillmentReportTemplateLockTTL, redis.DefaultDelay, redis.DefaultRetry); err != nil {
		return nil, nil, err
	}
	defer func() {
		if err := l.redisRepo.Unlock(ctx, lockKey); err != nil {
			log.ErrorContextf(ctx, "InitFulfillmentReportTemplate Failed to release lock, lock:%s", lockKey)
		}
	}()

	// check init before
	repoTemplate, err := l.templateRepo.FindByUniqueKey(ctx, companyID, businessID, int32(careType.Number()))
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil, err
	}

	var repoQuestions []*questionrepo.Question

	// 使用事务处理
	err = l.tx.ExecuteInTransaction(ctx, []func(opCtx context.Context, tx *gorm.DB) error{
		func(opCtx context.Context, _ *gorm.DB) error {
			if repoTemplate == nil {
				repoTemplate = &templaterepo.Template{
					CompanyID:       companyID,
					BusinessID:      businessID,
					CareType:        int32(careType.Number()),
					Title:           getDefaultTemplateTitle(careType),
					LastPublishTime: time.Now().UTC(),
				}
				if err = l.templateRepo.Create(opCtx, repoTemplate); err != nil {
					return err
				}
			}

			// 如果 question 已经初始化过则直接返回
			existQuestion, err := l.questionRepo.FindByFilter(opCtx, questionrepo.Filter{
				CompanyID:  &companyID,
				BusinessID: &businessID,
				CareType:   lo.ToPtr(int32(careType.Number())),
			})
			if err != nil {
				return err
			}
			if len(existQuestion) > 0 {
				repoQuestions = existQuestion

				return nil
			}

			// 通过 0 值获取默认问题
			defaultQuestions, err := l.questionRepo.FindByFilter(opCtx, questionrepo.Filter{
				CompanyID:  lo.ToPtr(int64(0)),
				BusinessID: lo.ToPtr(int64(0)),
				CareType:   lo.ToPtr(int32(careType.Number())),
			})
			if err != nil {
				return err
			}

			lo.ForEach(defaultQuestions, func(question *questionrepo.Question, _ int) {
				question.ID = 0
				question.CareType = int32(careType.Number())
				question.CompanyID = companyID
				question.BusinessID = businessID
				repoQuestions = append(repoQuestions, question)
			})

			if err := l.questionRepo.BatchCreate(opCtx, repoQuestions); err != nil {
				return err
			}

			return nil
		},
	})
	if err != nil {
		return nil, nil, err
	}

	templateQuestion, err := ConvertQuestionRepoToLogics(ctx, repoQuestions)
	if err != nil {
		return nil, nil, status.Errorf(codes.Internal, "convert question repo to logics err: %v", err)
	}

	return ConvertTemplateRepoToLogic(repoTemplate), templateQuestion, nil
}

func (l *Logic) CheckReportAvailable(ctx context.Context, companyID int64) (bool, error) {
	if companyID == 0 {
		return false, status.Errorf(codes.Internal, "company id invaild")
	}
	// 检查是否有权限使用 report
	featureQuota, err := l.paymentRepo.
		QueryCompanyPlanFeatureByCidCode(ctx, int32(companyID), payment.FcGroomingReportLv1)
	if err != nil {
		return false, status.Errorf(codes.Internal, "check report available err: %v", err)
	}

	return featureQuota.Enable, err
}

// GetTemplate 获取 template 及 question 相关数据并初始化
func (l *Logic) GetTemplate(ctx context.Context,
	companyID, businessID int64, careType offeringpb.CareCategory) (*Template, error) {
	// daily report 的 templateModel 为是 company level
	if lo.Contains(DailyReportCareTypeList, careType) {
		businessID = 0
	}

	var template *Template
	var questions *TemplateQuestion

	// 获取模板 & 问题
	repoTemplate, err := l.templateRepo.FindByUniqueKey(ctx, companyID, businessID, int32(careType.Number()))
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, status.Errorf(codes.Internal, "find template err: %v", err)
	}
	template = ConvertTemplateRepoToLogic(repoTemplate)

	repoQuestions, err := l.questionRepo.FindByFilter(ctx, questionrepo.Filter{
		CompanyID:  &companyID,
		BusinessID: &businessID,
		CareType:   lo.ToPtr(int32(careType.Number())),
	})
	if err != nil {
		return nil, status.Errorf(codes.Internal, "find question err: %v", err)
	}
	questions, err = ConvertQuestionRepoToLogics(ctx, repoQuestions)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "convert question repo to logics err: %v", err)
	}

	if template == nil || questions == nil {
		template, questions, err = l.initTemplate(ctx, companyID, businessID, careType)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "init template err: %v", err)
		}
	}

	// 填充 review booster，只有 grooming template 有 review booster
	var boosterConfig *message.ReviewBooster
	if careType == offeringpb.CareCategory_GROOMING {
		boosterConfig, err = l.messageRepo.GetReviewBoosterConfig(ctx, businessID)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "get review booster err: %v", err)
		}
	}

	template = buildTemplate(template, questions, boosterConfig)

	if template.ShowReviewBooster {
		// 检查是否有权限使用 review booster
		featureQuota, err := l.paymentRepo.
			QueryCompanyPlanFeatureByCidCode(ctx, int32(template.CompanyID), payment.FcReviewBooster)
		if err != nil || !featureQuota.Enable {
			template.ShowReviewBooster = false
		}
	}

	return template, nil
}

func (l *Logic) UpdateTemplate(ctx context.Context, template *Template, deleteQuestionIDs []int64) error {
	// 获取模板
	repoTemplate, err := l.templateRepo.FindByUniqueKey(
		ctx, template.CompanyID, template.BusinessID, int32(template.CareType.Number()))
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return status.Errorf(codes.Internal, "find template err: %v", err)
	}

	if repoTemplate == nil {
		return status.Errorf(codes.NotFound, "template not found")
	}

	repoTemplate = ConvertTemplateLogicToRepo(template)
	now := time.Now().UTC()
	repoTemplate.UpdateTime = now
	repoTemplate.LastPublishTime = now

	// TODO 更新 review booster link

	// 分离创建和更新的问题
	var createQuestions []*questionrepo.Question
	var updateQuestions []*questionrepo.Question
	for _, question := range ConvertTemplateQuestionToQuestions(template.Questions) {
		repoQuestion := ConvertQuestionLogicToRepo(question)
		if question.ID == 0 {
			repoQuestion.CompanyID = template.CompanyID
			createQuestions = append(createQuestions, repoQuestion)
		} else {
			updateQuestions = append(updateQuestions, repoQuestion)
		}
	}

	// 使用事务处理
	err = l.tx.ExecuteInTransaction(ctx, []func(opCtx context.Context, tx *gorm.DB) error{
		func(opCtx context.Context, _ *gorm.DB) error {
			// 更新模板
			if err = l.templateRepo.Update(opCtx, repoTemplate); err != nil {
				return status.Errorf(codes.Internal, "update template err: %v", err)
			}
			// 更新现有问题
			if len(updateQuestions) > 0 {
				if err = l.questionRepo.BatchUpdate(opCtx, updateQuestions); err != nil {
					return status.Errorf(codes.Internal, "update questions err: %v", err)
				}
			}
			// 创建新问题
			if len(createQuestions) > 0 {
				if err = l.questionRepo.BatchCreate(opCtx, createQuestions); err != nil {
					return status.Errorf(codes.Internal, "create questions err: %v", err)
				}
			}
			// 删除指定问题
			if len(deleteQuestionIDs) > 0 {
				err = l.questionRepo.BatchDelete(opCtx, questionrepo.Filter{
					CompanyID: &template.CompanyID,
					IDList:    deleteQuestionIDs,
				})
				if err != nil {
					return status.Errorf(codes.Internal, "delete questions err: %v", err)
				}
			}

			return nil
		},
	})
	if err != nil {
		return err
	}

	// 更新 review booster
	err = l.updateReviewBoosterConfig(ctx, template)
	if err != nil {
		return err
	}

	return nil
}

func (l *Logic) updateReviewBoosterConfig(ctx context.Context, template *Template) error {
	if template == nil {
		return nil
	}
	reviewBooster := &message.ReviewBooster{
		CompanyID:        &template.CompanyID,
		BusinessID:       &template.BusinessID,
		PositiveYelp:     &template.YelpReviewLink,
		PositiveGoogle:   &template.GoogleReviewLink,
		PositiveFacebook: &template.FacebookReviewLink,
	}

	_, err := l.messageRepo.UpdateReviewBoosterConfig(ctx, reviewBooster)
	if err != nil {
		return status.Errorf(codes.Internal, "update review booster config err: %v", err)
	}

	return nil
}

// getFulfillmentReport if not found, will return nil
func (l *Logic) getFulfillmentReport(ctx context.Context,
	req *GetFulfillmentReport) (*Report, error) {
	var err error
	if err = l.verifyGetFulfillmentReportRequest(req); err != nil {
		return nil, err
	}

	var existReport *reportrepo.Report

	if req.ID != 0 {
		existReport, err = l.reportRepo.FindByID(ctx, req.ID)
		if err != nil {
			return nil, err
		}

		if existReport == nil {
			return nil, status.Errorf(codes.NotFound, "report not found")
		}

	} else if req.UUID != "" {
		existReport, err = l.reportRepo.FindByUUID(ctx, req.UUID)
		if err != nil {
			return nil, err
		}

		if existReport == nil {
			return nil, status.Errorf(codes.NotFound, "report not found")
		}
	} else {
		existReport, err = l.reportRepo.FindByUniqueKey(
			ctx,
			req.BusinessID,
			req.AppointmentID,
			req.PetID,
			int32(req.CareType.Number()),
			req.ServiceDate)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "find report err:%v", err)
		}
	}

	report, err := ConvertReportRepoToLogic(ctx, existReport)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "convert report err:%v", err)
	}

	if report == nil {
		return nil, nil
	}

	// 兼容没有 template 的 daily report 的旧数据
	if l.isTemplateEmpty(report.Template) {
		if err = l.buildDailyReportTemplate(ctx, report); err != nil {
			return nil, err
		}
	}

	// fill report detail

	return report, nil
}

// 判断 template 是否为空，
func (l *Logic) isTemplateEmpty(template *Template) bool {
	if template == nil {
		return true
	}

	// 由于 template 可能是由 json 解析出来的，json 为空时 template 仍有值，没办法通过是否为 nil 进行判断
	// 因此全部选项均为默认值也判断为空
	defaultTemplate := Template{}

	return *template == defaultTemplate
}

// 以前的 daily report 没有 template，因此 report.Template = nil，需要主动 set 一遍
func (l *Logic) buildDailyReportTemplate(ctx context.Context, report *Report) error {
	template, err := l.GetTemplate(ctx, report.CompanyID, report.BusinessID, report.CareType)
	if err != nil {
		return status.Errorf(codes.Internal, "build daily report template err:%v", err)
	}

	report.Template = template
	report.TemplateVersion = time.Unix(template.LastPublishTime, 0)

	return nil
}

// GetFulfillmentReport 获取 report 并初始化，不落库
func (l *Logic) GetFulfillmentReport(ctx context.Context,
	req *GetFulfillmentReport) (*Report, error) {

	report, err := l.getFulfillmentReport(ctx, req)
	if err != nil {
		return nil, err
	}

	if report == nil {
		report, err = l.initFulfillmentReport(ctx, req)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "init report error: %v", err)
		}
	}

	return report, nil
}

func (l *Logic) IsNeedRefresh(ctx context.Context, report *Report) (bool, error) {
	if report == nil {
		return false, nil
	}

	businessID := report.BusinessID
	// daily report 的 templateModel 为是 company level
	if lo.Contains(DailyReportCareTypeList, report.CareType) {
		businessID = 0
	}

	repoTemplate, err := l.templateRepo.FindByUniqueKey(
		ctx, report.CompanyID, businessID, int32(report.CareType.Number()))
	if err != nil {
		return false, err
	}

	if repoTemplate == nil {
		return false, nil
	}

	if repoTemplate.LastPublishTime.Unix() > report.TemplateVersion.Unix() {
		return true, nil
	}

	return false, nil
}

// initFulfillmentReport 初始化报告 不落库
func (l *Logic) initFulfillmentReport(ctx context.Context, req *GetFulfillmentReport) (*Report, error) {

	companyID, businessID, petID, careType := req.CompanyID, req.BusinessID, req.PetID, req.CareType

	petInfo, err := l.petRepo.GetPetInfo(ctx, companyID, petID)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "get pet info err:%v", err)
	}
	if petInfo == nil {
		return nil, status.Errorf(codes.NotFound, "pet not found")
	}

	customerInfo, err := l.customerRepo.GetCustomerInfo(ctx, companyID, petInfo.CustomerId)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "get customer info err:%v", err)
	}
	if customerInfo == nil {
		return nil, status.Errorf(codes.NotFound, "customer not found")
	}

	// 获取模板信息
	logicTemplate, err := l.GetTemplate(ctx, companyID, businessID, careType)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "init report get template err:%v", err)
	}

	return l.buildInitReport(req, logicTemplate, petInfo, customerInfo), nil
}

// initTemplateReport 初始化报告 不落库
func (l *Logic) initTemplateReport(ctx context.Context,
	req *fulfillmentpb.GetFulfillmentTemplateReportRequest, template *Template) (*Report, error) {

	param := &GetFulfillmentReport{
		CompanyID:     req.GetCompanyId(),
		BusinessID:    req.GetBusinessId(),
		AppointmentID: 0,
		PetID:         0,
		CareType:      template.CareType,
		ServiceDate:   "",
	}

	templateReport := l.buildInitReport(param, template, SamplePet, SampleCustomer)

	// 设置为 draft、uuid
	templateReport.Status = fulfillmentpb.ReportStatus_DRAFT
	uuid, err := l.genReportUUID(ctx)
	if err != nil {
		return nil, err
	}
	templateReport.UUID = uuid

	return templateReport, nil
}

func (l *Logic) buildInitReport(req *GetFulfillmentReport,
	logicTemplate *Template, petInfo *businesscustomerpb.BusinessCustomerPetInfoModel,
	customerInfo *businesscustomerpb.BusinessCustomerModel) *Report {

	report := &Report{
		CompanyID:       req.CompanyID,
		BusinessID:      req.BusinessID,
		CustomerID:      petInfo.GetCustomerId(),
		AppointmentID:   req.AppointmentID,
		PetID:           req.PetID,
		PetTypeID:       int64(petInfo.PetType.Number()),
		CareType:        req.CareType,
		ServiceDate:     req.ServiceDate,
		Status:          fulfillmentpb.ReportStatus_CREATED,
		UUID:            "",
		LinkOpenedCount: 0,
		ThemeCode:       logicTemplate.ThemeCode,
		Template:        logicTemplate,
		Content:         l.buildReportContent(logicTemplate, customerInfo),
	}

	if logicTemplate.LastPublishTime == 0 {
		report.TemplateVersion = time.Now().UTC()
	} else {
		report.TemplateVersion = time.Unix(logicTemplate.LastPublishTime, 0)
	}

	return report
}

// buildReportContent 根据 template 构造 report content 相关数据
func (l *Logic) buildReportContent(logicTemplate *Template,
	customerInfo *businesscustomerpb.BusinessCustomerModel) *Content {
	// report feedback pet_conditions 数据构造及初始化
	feedbackQuestions, petConditions := l.buildReportQuestions(logicTemplate)

	content := &Content{
		Photos:         []string{},
		Videos:         []string{},
		Feedbacks:      feedbackQuestions,
		PetConditions:  petConditions,
		Recommendation: l.buildRecommendation(customerInfo.GetPreferredGroomingFrequency()),

		ThemeColor:      logicTemplate.ThemeColor,
		LightThemeColor: lo.ToPtr(logicTemplate.LightThemeColor),
	}

	return content
}

func (l *Logic) getFrequencyDay(frequencyTimePeriod *utilsV1.TimePeriod,
) (int32, fulfillmentpb.FulfillmentReportRecommendation_FrequencyType) {
	var frequency = frequencyTimePeriod.GetValue()
	var frequencyType = frequencyTimePeriod.GetPeriod()
	switch frequencyType {
	case calendarperiod.CalendarPeriod_DAY:
		return frequency, fulfillmentpb.FulfillmentReportRecommendation_DAY
	case calendarperiod.CalendarPeriod_WEEK:
		return frequency * 7, fulfillmentpb.FulfillmentReportRecommendation_WEEK
	case calendarperiod.CalendarPeriod_MONTH:
		return frequency * 30, fulfillmentpb.FulfillmentReportRecommendation_MONTH
	default:
		// 默认返回 week
		return frequency, fulfillmentpb.FulfillmentReportRecommendation_WEEK
	}

}

func (l *Logic) buildRecommendation(frequencyTimePeriod *utilsV1.TimePeriod) (recommendation *Recommendation) {
	frequencyDay, frequencyType := l.getFrequencyDay(frequencyTimePeriod)
	recommendation = &Recommendation{
		FrequencyDay:  frequencyDay,
		FrequencyType: frequencyType,
	}

	if frequencyDay == 0 {
		recommendation.FrequencyText = defaultFrequencyDayText

		return
	}

	var frequencyText string

	frequency := frequencyTimePeriod.GetValue()
	switch frequencyType {
	case fulfillmentpb.FulfillmentReportRecommendation_DAY:
		frequencyText = fmt.Sprintf("Every %d day", frequencyDay)
	case fulfillmentpb.FulfillmentReportRecommendation_MONTH:
		frequencyText = fmt.Sprintf("Every %d month", frequency)
	default:
		frequencyText = fmt.Sprintf("Every %d week", frequency)
	}

	if frequency > 0 {
		frequencyText = frequencyText + "s"
	}

	recommendation.FrequencyText = frequencyText

	return
}

// buildReportQuestions 将 template 中的 question 转换为 report content 的 feedback 以及 pet_condition，并初始化基本数据
func (l *Logic) buildReportQuestions(template *Template) (
	feedbackQuestions []*ReportQuestion,
	petConditions []*ReportQuestion,
) {
	var petConditionIndex int32

	lo.ForEach(ConvertTemplateQuestionToQuestions(template.Questions), func(question *Question, _ int) {
		reportQuestion := &ReportQuestion{
			ID:       question.ID,
			Category: question.Category,
			Title:    question.Title,
			Key:      question.Key,
			Type:     question.Type,
			Required: question.IsRequired,
			Options:  question.Extra.Options,
		}

		switch question.Category {
		case fulfillmentpb.QuestionCategory_FEEDBACK:
			l.initReportQuestionChoice(reportQuestion)
			reportQuestion.IsShow = template.ShowOverallFeedback
			feedbackQuestions = append(feedbackQuestions, reportQuestion)
		case fulfillmentpb.QuestionCategory_CUSTOMIZE_FEEDBACK:
			// customized feedback 默认不选中
			reportQuestion.IsShow = template.ShowCustomizedFeedback
			feedbackQuestions = append(feedbackQuestions, reportQuestion)
		case fulfillmentpb.QuestionCategory_PET_CONDITION:
			l.initReportQuestionChoice(reportQuestion)
			// 前四个 pet condition 设置为 true
			if template.ShowPetCondition && petConditionIndex < defaultPetConditionShowLen {
				reportQuestion.IsShow = true
				petConditionIndex++
			} else {
				reportQuestion.IsShow = false
			}

			petConditions = append(petConditions, reportQuestion)
		}
	})

	return feedbackQuestions, petConditions
}

func (l *Logic) initReportQuestionChoice(question *ReportQuestion) {
	// 非选择题以及 customize feedback 不需要选择
	if !l.isChoiceQuestion(question.Type) || l.isCustomizeFeedback(question) {
		return
	}

	// 单选题未选，如果 choices, customOptions 为空且 options 不为空，则选中第一个，多选题可以为空
	if len(question.Choices) == 0 || len(question.CustomOptions) == 0 && len(question.Options) > 0 {
		question.Choices = append(question.Choices, question.Options[0])
	}
}

func (l *Logic) isCustomizeFeedback(reportQuestion *ReportQuestion) bool {
	return reportQuestion.Category == fulfillmentpb.QuestionCategory_CUSTOMIZE_FEEDBACK
}

func (l *Logic) verifyGetFulfillmentReportRequest(req *GetFulfillmentReport) error {
	if req.UUID != "" || req.ID != 0 {
		return nil
	}
	if req.CompanyID == 0 || req.BusinessID == 0 {
		return status.Errorf(codes.InvalidArgument, "company_id and business_id is required")
	}

	// 如果是 daycare 或者 boarding，则必须有 service date
	if lo.Contains(DailyReportCareTypeList, req.CareType) && req.ServiceDate == "" {
		return status.Errorf(codes.InvalidArgument, "service date is required")
	}

	if req.ID == 0 &&
		(req.AppointmentID == 0 ||
			req.CareType == offeringpb.CareCategory_CARE_CATEGORY_UNSPECIFIED ||
			req.PetID == 0) {
		return status.Errorf(codes.InvalidArgument, "invalid request")
	}

	return nil
}

func (l *Logic) UpdateFulfillmentReport(ctx context.Context,
	updateReportParams *Report, isRefresh bool) (*Report, error) {

	existReport, err := l.getFulfillmentReport(ctx, ConvertReportLogicToGetFulfillmentReport(updateReportParams))
	if err != nil {
		return nil, err
	}

	var report *Report
	if existReport == nil {
		report, err = l.createReport(ctx, updateReportParams)
		if err != nil {
			return nil, err
		}
	} else {
		if existReport.Status == fulfillmentpb.ReportStatus_CREATED &&
			updateReportParams.Status == fulfillmentpb.ReportStatus_REPORT_STATUS_UNSPECIFIED {
			// 兼容 status 为 created 的老数据，更新 report 时将 created 状态修改为 draft
			updateReportParams.Status = fulfillmentpb.ReportStatus_DRAFT
		}
		// 处理现有报告的更新
		report, err = l.handleExistingReportUpdate(ctx, existReport, updateReportParams, isRefresh)
		if err != nil {
			return nil, err
		}
	}

	return report, nil
}

// handleBodyView 针对 pet condition 中的 body view 特殊处理
func (l *Logic) handleBodyView(ctx context.Context,
	careType offeringpb.CareCategory, petType int64, updateReportReq *Report) (*Report, error) {
	// daily report 没有 pet condition
	if lo.Contains(DailyReportCareTypeList, careType) {
		return updateReportReq, nil
	}
	// 根据 question type 筛选出 body view question
	bodyViewQuestion, ok := lo.Find(updateReportReq.Content.PetConditions, func(question *ReportQuestion) bool {
		return l.isBodyViewQuestion(question.Type)
	})

	if !ok || bodyViewQuestion == nil {
		return updateReportReq, nil
	}

	// 处理 body view 选中逻辑，上传 S3，并返回 body view url
	bodyViewURL, err := l.appointmentRepo.HandleReportBodyView(ctx, &appointment.HandleReportBodyViewParams{
		PetTypeID: petType,
		BodyViewQuestionParams: &appointment.GroomingReportQuestionParams{
			Type:    strings.ToLower(bodyViewQuestion.Type),
			Choices: bodyViewQuestion.Choices,
		},
	})
	if err != nil {
		return nil, status.Errorf(codes.Internal, "handle body view error: %v", err)
	}
	if bodyViewURL != nil {
		bodyViewQuestion.URLs = &BodyViewURL{
			Left:  bodyViewURL.Left,
			Right: bodyViewURL.Right,
		}
	}

	return updateReportReq, nil
}

// handleExistingReportUpdate 处理现有报告的更新，包括模板合并逻辑
func (l *Logic) handleExistingReportUpdate(ctx context.Context,
	existReport, updateReportParams *Report, isMerge bool) (*Report, error) {
	// body view 特殊处理
	updateReportParams, err := l.handleBodyView(ctx, existReport.CareType, existReport.PetTypeID, updateReportParams)
	if err != nil {
		return nil, err
	}

	// 检查是否需要合并模板
	needMerge, err := l.IsNeedRefresh(ctx, existReport)
	if err != nil {
		return nil, err
	}

	if !needMerge || !isMerge {
		// 不需要更新就直接返回
		return l.updateReport(ctx, existReport, updateReportParams)
	}

	// 获取最新的模板
	latestTemplate, err := l.GetTemplate(ctx, existReport.CompanyID, existReport.BusinessID, existReport.CareType)
	if err != nil {
		return nil, err
	}

	// 获取客户信息以获取偏好设置
	customerInfo, err := l.customerRepo.GetCustomerInfo(ctx, existReport.CompanyID, existReport.CustomerID)
	if err != nil {
		return nil, err
	}

	// 合并报告到新模板
	l.adaptFulfillmentReportToTemplate(
		existReport,
		updateReportParams,
		latestTemplate,
		customerInfo.GetPreferredGroomingFrequency().GetValue(),
		fulfillmentpb.FulfillmentReportRecommendation_FrequencyType(
			fulfillmentpb.
				FulfillmentReportRecommendation_FrequencyType_value[customerInfo.
				GetPreferredGroomingFrequency().Period.String()]),
	)

	// 更新报告
	return l.updateReport(ctx, existReport, updateReportParams)
}

// adaptFulfillmentReportToTemplate 跟进 latestTemplate，调整 existReport
func (l *Logic) adaptFulfillmentReportToTemplate(
	existReport *Report,
	updateReport *Report,
	latestTemplate *Template,
	customerPreferredFrequencyDay int32,
	customerPreferredFrequencyType fulfillmentpb.FulfillmentReportRecommendation_FrequencyType,
) {
	// 设置模板信息
	existReport.Template = latestTemplate
	existReport.TemplateVersion = time.Unix(latestTemplate.LastPublishTime, 0).UTC()

	// 构建内容
	content := &Content{}

	// 从 updateReport.Content.Feedbacks 根据 category 拆分为两个 param
	// 入参 fulfillmentpb.QuestionCategory_FEEDBACK / fulfillmentpb.QuestionCategory_CUSTOMIZE_FEEDBACK
	overrallFeedbacksParam := make([]*ReportQuestion, 0, len(updateReport.Content.Feedbacks))
	customizedFeedbackParam := make([]*ReportQuestion, 0, len(updateReport.Content.Feedbacks))
	// 结果
	overrallFeedbacksResult := make([]*ReportQuestion, 0, len(latestTemplate.Questions.Feedback))
	customizedFeedbackResult := make([]*ReportQuestion, 0, len(latestTemplate.Questions.CustomizeFeedback))

	if updateReport.Content != nil {
		for _, q := range updateReport.Content.Feedbacks {
			switch q.Category {
			case fulfillmentpb.QuestionCategory_FEEDBACK:
				overrallFeedbacksParam = append(overrallFeedbacksParam, q)
			case fulfillmentpb.QuestionCategory_CUSTOMIZE_FEEDBACK:
				customizedFeedbackParam = append(customizedFeedbackParam, q)
			}
		}
	}

	// 处理反馈问题
	if latestTemplate.ShowOverallFeedback {
		overrallFeedbacksResult = l.adaptQuestionList(
			overrallFeedbacksParam,
			latestTemplate.Questions.Feedback,
			&latestTemplate.ShowOverallFeedback,
		)
	}
	if latestTemplate.ShowCustomizedFeedback {
		customizedFeedbackResult = l.adaptQuestionList(
			customizedFeedbackParam,
			latestTemplate.Questions.CustomizeFeedback,
			&latestTemplate.ShowCustomizedFeedback,
		)
	}

	// 合并结果（overall + customized）
	feedbacks := make([]*ReportQuestion, 0, len(overrallFeedbacksResult)+len(customizedFeedbackResult))
	feedbacks = append(feedbacks, overrallFeedbacksResult...)
	feedbacks = append(feedbacks, customizedFeedbackResult...)
	content.Feedbacks = feedbacks

	// 处理宠物状况问题
	if latestTemplate.ShowPetCondition {
		content.PetConditions = l.adaptQuestionList(
			updateReport.Content.PetConditions,
			latestTemplate.Questions.PetConditions,
			nil,
		)
		// 原本没有 pet condition 数据，需要默认 show 前四个
		if len(updateReport.Content.PetConditions) == 0 && len(content.PetConditions) > 0 {
			for i := 0; i < defaultPetConditionShowLen && i < len(content.PetConditions); i++ {
				content.PetConditions[i].IsShow = true
			}
		}
	}

	// 处理展示内容
	if latestTemplate.ShowShowcase {
		content.Photos = updateReport.Content.Photos
		content.Videos = updateReport.Content.Videos
	}

	// 处理下次预约推荐
	if latestTemplate.ShowNextAppointment {
		recommendation := updateReport.Content.Recommendation
		if recommendation == nil {
			recommendation = &Recommendation{}
		}

		// 如果之前没有设置频率，则使用客户的偏好设置
		if recommendation.FrequencyDay == 0 && customerPreferredFrequencyDay != 0 {
			recommendation.FrequencyDay = customerPreferredFrequencyDay
			recommendation.FrequencyType = customerPreferredFrequencyType
		}

		content.Recommendation = recommendation
	}

	// 设置主题颜色
	content.ThemeColor = latestTemplate.ThemeColor
	content.LightThemeColor = lo.ToPtr(latestTemplate.LightThemeColor)

	updateReport.Content = content
}

// adaptQuestionList 合并问题列表，保留已输入的值
func (l *Logic) adaptQuestionList(
	questionList []*ReportQuestion,
	templateQuestionList []*Question,
	isShow *bool,
) []*ReportQuestion {
	// 创建问题映射，用于快速查找
	questionMap := make(map[string]*ReportQuestion)
	for _, question := range questionList {
		if question.Key != "" {
			questionMap[question.Key] = question
		} else {
			questionMap[question.Title] = question
		}
	}

	result := make([]*ReportQuestion, 0, len(templateQuestionList))

	for _, templateQuestion := range templateQuestionList {
		question := &ReportQuestion{
			ID:       templateQuestion.ID,
			Category: templateQuestion.Category,
			Type:     templateQuestion.Type,
			Key:      templateQuestion.Key,
			Title:    templateQuestion.Title,
			Required: templateQuestion.IsRequired,
			Options:  templateQuestion.Extra.Options,
		}

		// 检查是否有对应的已输入参数
		var params *ReportQuestion
		var exists bool
		if templateQuestion.Key != "" {
			params, exists = questionMap[templateQuestion.Key]
		} else {
			params, exists = questionMap[templateQuestion.Title]
		}
		if exists {
			if l.isChoiceQuestion(params.Type) && len(params.Choices) > 0 {
				// 选择题：合并所有选项
				allOptions := make([]string, 0, len(question.Options))
				allOptions = append(allOptions, question.Options...)

				if len(params.CustomOptions) > 0 {
					allOptions = append(allOptions, params.CustomOptions...)
					question.CustomOptions = params.CustomOptions
				}

				// 移除不在options和customOptions中的choice
				filteredChoices := make([]string, 0)
				for _, choice := range params.Choices {
					if lo.Contains(allOptions, choice) {
						filteredChoices = append(filteredChoices, choice)
					}
				}
				question.Choices = filteredChoices
			} else if l.isBodyViewQuestion(params.Type) {
				// body view question：保留选择的内容
				if params.Choices != nil {
					question.Choices = params.Choices
				}
			}

			// 设置显示状态：如果required为true，则忽略params.show，直接设置为true
			question.IsShow = question.Required || params.IsShow

			// 设置其他字段
			question.InputText = params.InputText
			question.Placeholder = params.Placeholder
			question.URLs = params.URLs
		} else {
			question.IsShow = question.Required
		}

		if isShow != nil && *isShow {
			question.IsShow = *isShow
		}
		// init
		l.initReportQuestionChoice(question)

		result = append(result, question)
	}

	return result
}

// isChoiceQuestion 判断是否为选择题
func (l *Logic) isChoiceQuestion(questionType string) bool {
	// 根据Go版本的问题类型判断
	upperType := strings.ToUpper(questionType)

	return upperType == fulfillmentpb.QuestionType_SINGLE_CHOICE.String() ||
		upperType == fulfillmentpb.QuestionType_MULTI_CHOICE.String() ||
		upperType == fulfillmentpb.QuestionType_TAG_CHOICE.String()
}

// isBodyViewQuestion 判断是否为身体视图问题
func (l *Logic) isBodyViewQuestion(questionType string) bool {
	// 根据Go版本的问题类型判断
	upperType := strings.ToUpper(questionType)

	return upperType == fulfillmentpb.QuestionType_BODY_VIEW.String()
}

func (l *Logic) createReport(ctx context.Context, report *Report) (*Report, error) {
	initReport, err := l.initFulfillmentReport(ctx, ConvertReportLogicToGetFulfillmentReport(report))
	if err != nil {
		return nil, status.Errorf(codes.Internal, "init report error: %v", err)
	}

	initReport.UUID, err = l.genReportUUID(ctx)
	if err != nil {
		return nil, err
	}
	initReport.UpdateBy = report.UpdateBy
	initReport.Status = fulfillmentpb.ReportStatus_DRAFT
	initReport.Content = report.Content
	initReport.ThemeCode = report.ThemeCode

	initReport, err = l.handleBodyView(ctx, report.CareType, initReport.PetTypeID, initReport)
	if err != nil {
		return nil, err
	}

	repoReport, err := ConvertReportLogicToRepo(ctx, initReport)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "convert report logic to repo err: %v", err)
	}

	if err = l.reportRepo.Create(ctx, repoReport); err != nil {
		return nil, status.Errorf(codes.Internal, "create report err:%v", err)
	}

	return ConvertReportRepoToLogic(ctx, repoReport)
}

func (l *Logic) updateReport(ctx context.Context, report *Report, updateReportParams *Report) (*Report, error) {
	if report == nil {
		return nil, status.Errorf(codes.NotFound, "report not found")
	}

	report.UpdateBy = updateReportParams.UpdateBy
	report.UpdateTime = lo.ToPtr(time.Now().UTC())
	report.ThemeCode = updateReportParams.ThemeCode
	if updateReportParams.Content != nil {
		report.Content = updateReportParams.Content
	}
	if updateReportParams.Status != fulfillmentpb.ReportStatus_REPORT_STATUS_UNSPECIFIED {
		report.Status = updateReportParams.Status
	}

	repoReport, err := ConvertReportLogicToRepo(ctx, report)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "convert report logic to repo err: %v", err)
	}

	if err = l.reportRepo.Update(ctx, repoReport); err != nil {
		return nil, status.Errorf(codes.Internal, "update report err:%v", err)
	}

	return ConvertReportRepoToLogic(ctx, repoReport)
}

// genReportUUID 获取 DB 内唯一的 uuid
func (l *Logic) genReportUUID(ctx context.Context) (uuid string, err error) {
	for {
		uuid = random.String(12)
		report, err := l.reportRepo.FindByUUID(ctx, uuid)
		if err != nil {
			return "", status.Errorf(codes.Internal, "get report by uuid err:%v", err)
		}

		if report == nil {
			break
		}
	}

	return uuid, err
}

// GetTemplateReport 获取 template report,appointmentId = 0 petId = 0 serviceDate = "" 的 Report 为 template report
func (l *Logic) GetTemplateReport(ctx context.Context,
	req *fulfillmentpb.GetFulfillmentTemplateReportRequest) (*Report, error) {
	if req.GetCompanyId() == 0 || req.GetBusinessId() == 0 || req.GetCareType() == 0 {
		return nil, status.Errorf(codes.InvalidArgument, "get default template report param invalid")
	}
	// todo 检查套餐是否可用

	templateReportRepo, err := l.reportRepo.FindByUniqueKey(ctx,
		req.GetBusinessId(), 0, 0, int32(req.GetCareType().Number()), "")
	if err != nil {
		return nil, status.Errorf(codes.Internal, "get default template report err:%v", err)
	}

	existTemplate, err := l.GetTemplate(ctx, req.GetCompanyId(), req.GetBusinessId(), req.GetCareType())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "get template err:%v", err)
	}

	var templateReport *Report
	if templateReportRepo == nil {
		templateReport, err = l.initTemplateReport(ctx, req, existTemplate)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "init template report err:%v", err)
		}

	} else {
		templateReport, err = ConvertReportRepoToLogic(ctx, templateReportRepo)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "convert template report err:%v", err)
		}
	}

	// 优先使用 req 中传递的 template
	if req.PreviewTemplate == nil {
		templateReport.Template = existTemplate
	} else {
		templateReport.Template = ConvertTemplatePBToLogic(req.GetPreviewTemplate())
	}

	if req.GetThemeCode() != "" {
		templateReport.ThemeCode = req.GetThemeCode()
	}

	templateReport = l.fillTemplateReportContent(templateReport)

	repoReport, err := ConvertReportLogicToRepo(ctx, templateReport)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "convert template report err:%v", err)
	}

	if templateReport.ID == 0 {
		if err = l.reportRepo.Create(ctx, repoReport); err != nil {
			return nil, status.Errorf(codes.Internal, "create template report err:%v", err)
		}
	} else {
		repoReport.UpdateTime = time.Now().UTC()
		if err = l.reportRepo.Update(ctx, repoReport); err != nil {
			return nil, status.Errorf(codes.Internal, "update template report err:%v", err)
		}
	}

	return ConvertReportRepoToLogic(ctx, repoReport)
}

// 填充 template report 相关内容，等同于 preview 的概念
func (l *Logic) fillTemplateReportContent(report *Report) *Report {
	content := l.buildReportContent(report.Template, SampleCustomer)
	// 填充默认值
	for _, question := range content.Feedbacks {
		if question.Key == QuestionKeyAdditionalNote {
			question.InputText = SampleComment
		}
		// preview 时默认全都展示
		question.IsShow = true
		// preview 时, customize feedback 也默认选择第一个
		if l.isChoiceQuestion(question.Type) && len(question.Options) > 0 &&
			question.Category == fulfillmentpb.QuestionCategory_CUSTOMIZE_FEEDBACK {
			question.Choices = append(question.Choices, question.Options[0])
		}
	}

	for _, question := range content.PetConditions {
		// preview 时默认全都展示
		question.IsShow = true
		// body view 默认填充数据
		if strings.ToUpper(question.Type) == fulfillmentpb.QuestionType_BODY_VIEW.String() {
			question.URLs = &BodyViewURL{
				Left:  SampleBodyViewLeft,
				Right: SampleBodyViewRight,
			}
		}
	}

	if lo.Contains(DailyReportCareTypeList, report.CareType) {
		content.Photos = append(content.Photos, SampleDailyPhoto1, SampleDailyPhoto2, SampleDailyPhoto3)
	} else {
		content.Photos = append(content.Photos, SampleGroomingPhotoBefore, SampleGroomingPhotoAfter)
	}
	report.Content = content

	return report
}

func (l *Logic) BuildEmailSubject(subject string,
	summaryInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo) (string, error) {
	// 检查是否为重发
	isResend := summaryInfo.GetFulfillmentReport().GetStatus() == fulfillmentpb.ReportStatus_SENT

	// 判断 subject 是否为空
	if subject == "" {
		// 设置默认主题模板
		if isResend {
			subject = "[Updated] {PetName}'s {Title} at {BusinessName}"
		} else {
			subject = "{PetName}'s {CareType} Report at {BusinessName}"
		}
	}

	// 获取业务信息
	businessName := summaryInfo.GetBusinessInfo().GetBusinessName()
	petName := summaryInfo.GetPetInfo().GetPetName()

	// 获取模板标题
	title := summaryInfo.GetFulfillmentReport().GetTemplate().GetTitle()
	if title == "" {
		title = getDefaultTemplateTitle(summaryInfo.GetFulfillmentReport().GetCareType())
	}

	// 获取主要员工信息
	staffNames := l.getMainStaffNames(summaryInfo)
	mainStaff := strings.Join(staffNames, ", ")

	// 替换占位符
	careTypeStr := "daily"
	if summaryInfo.GetFulfillmentReport().GetCareType() == offeringpb.CareCategory_GROOMING {
		careTypeStr = "grooming"
	}
	subject = strings.ReplaceAll(subject, "{BusinessName}", businessName)
	subject = strings.ReplaceAll(subject, "{PetName}", petName)
	subject = strings.ReplaceAll(subject, "{Title}", title)
	subject = strings.ReplaceAll(subject, "{MainStaff}", mainStaff)
	subject = strings.ReplaceAll(subject, "{CareType}", careTypeStr)

	return subject, nil
}

func (l *Logic) BuildSmsSendContent(summaryInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo) (string, error) {
	// 获取业务信息
	businessInfo := summaryInfo.GetBusinessInfo()
	petInfo := summaryInfo.GetPetInfo()
	careType := summaryInfo.GetFulfillmentReport().GetCareType()

	// 生成直接访问 url
	reportClientURL, err := l.getReportClientURL(careType)
	if err != nil {
		return "", err
	}
	directLink := fmt.Sprintf(reportClientURL, summaryInfo.GetFulfillmentReport().GetUuid())

	// 检查是否为重发
	isResend := summaryInfo.GetFulfillmentReport().GetStatus() == fulfillmentpb.ReportStatus_SENT

	// 获取模板标题
	title := summaryInfo.GetFulfillmentReport().GetTemplate().GetTitle()
	if title == "" {
		title = getDefaultTemplateTitle(summaryInfo.GetFulfillmentReport().GetCareType())
	}

	// 设置短信模板
	var sendContent string
	if isResend {
		sendContent = "[Updated] {PetName}’s {Title} at {BusinessName} {DirectAccessLink}"
	} else {
		sendContent = "{PetName}’s {Title} at {BusinessName} {DirectAccessLink}"
	}

	// 获取主要员工信息
	staffNames := l.getMainStaffNames(summaryInfo)
	mainStaff := strings.Join(staffNames, ", ")

	// 替换占位符
	sendContent = strings.ReplaceAll(sendContent, "{BusinessName}", businessInfo.GetBusinessName())
	sendContent = strings.ReplaceAll(sendContent, "{PetName}", petInfo.GetPetName())
	sendContent = strings.ReplaceAll(sendContent, "{Title}", title)
	sendContent = strings.ReplaceAll(sendContent, "{MainStaff}", mainStaff)
	sendContent = strings.ReplaceAll(sendContent, "{DirectAccessLink}", directLink)

	return sendContent, nil
}

func (l *Logic) getReportClientURL(careType offeringpb.CareCategory) (string, error) {
	cfg := config.GetCfg()

	if careType == offeringpb.CareCategory_GROOMING {
		if cfg.ReportClient != nil && cfg.ReportClient.GroomingReportURL != "" {
			return cfg.ReportClient.GroomingReportURL, nil
		}

		return "", status.Errorf(codes.Internal, "grooming report url is not configured")
	}

	if cfg.ReportClient != nil && cfg.ReportClient.DailyReportURL != "" {
		return cfg.ReportClient.DailyReportURL, nil
	}

	return "", status.Errorf(codes.Internal, "daily report url is not configured")
}

// getMainStaffNames 获取主要员工姓名列表
func (l *Logic) getMainStaffNames(summaryInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo) []string {
	var staffNames []string
	if appointmentInfo := summaryInfo.GetAppointmentInfo(); appointmentInfo != nil {
		for _, petService := range appointmentInfo.GetPetService() {
			for _, petDetail := range petService.GetPetDetails() {
				if staffInfo := petDetail.GetStaffInfo(); staffInfo != nil {
					firstName := staffInfo.GetStaffFirstName()
					if firstName != "" {
						staffNames = append(staffNames, firstName)
					}
				}
			}
		}
	}

	return staffNames
}

// CountFulfillmentReport 统计履约报告数量
func (l *Logic) CountFulfillmentReport(ctx context.Context, req *fulfillmentpb.CountFulfillmentReportRequest) (
	*fulfillmentpb.CountFulfillmentReportResponse, error) {
	var err error

	// 校验请求
	if err := l.verifyCountFulfillmentReportRequest(req); err != nil {
		return nil, err
	}

	// 构建查询参数
	baseParam := &reportrepo.BaseParam{
		CompanyID:  req.GetCompanyId(),
		BusinessID: req.GetBusinessId(),
	}

	// 构建过滤条件
	filter := l.buildReportCountFilter(req.GetFilter())

	var filterNoServiceDate *reportrepo.Filter
	// 这里由于 grooming service 在 report 这里没有 service date 概念，无法通过 service date 筛选
	// 因此需要先在 appointment 中查询，再通过 appointment ids 进行筛选
	if lo.Contains(filter.CareTypes, int32(offeringpb.CareCategory_GROOMING.Number())) {
		filterNoServiceDate, err = l.getListReportFilterNoServiceDate(ctx, baseParam, req.GetFilter())
		if err != nil {
			return nil, status.Errorf(codes.Internal, "get list report filter no service date: %v", err)
		}
	}

	// 按状态统计数量
	statusCount, err := l.reportRepo.CountByStatus(ctx, baseParam, filter, filterNoServiceDate)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to count reports by status: %v", err)
	}

	return &fulfillmentpb.CountFulfillmentReportResponse{
		Total:      int32(statusCount.Total),
		DraftCount: int32(statusCount.DraftCount),
		SentCount:  int32(statusCount.SentCount),
	}, nil
}

// verifyCountFulfillmentReportRequest 验证统计履约报告请求
func (l *Logic) verifyCountFulfillmentReportRequest(req *fulfillmentpb.CountFulfillmentReportRequest) error {
	if req == nil {
		return status.Errorf(codes.InvalidArgument, "request cannot be nil")
	}

	if req.GetCompanyId() <= 0 {
		return status.Errorf(codes.InvalidArgument, "company_id must be greater than 0")
	}

	if req.GetBusinessId() <= 0 {
		return status.Errorf(codes.InvalidArgument, "business_id must be greater than 0")
	}

	return nil
}

// buildReportCountFilter 构建报告计数过滤条件
func (l *Logic) buildReportCountFilter(filter *fulfillmentpb.ListFulfillmentReportConfigFilter) *reportrepo.Filter {
	repoFilter := &reportrepo.Filter{}

	if filter != nil {
		// 处理状态过滤
		if filter.GetStatus() != fulfillmentpb.ReportStatus_REPORT_STATUS_UNSPECIFIED {
			statusStr := strings.ToLower(filter.GetStatus().String())
			repoFilter.Status = &statusStr
		}

		// 处理护理类型过滤
		if len(filter.GetCareTypes()) > 0 {
			careTypes := make([]int32, 0, len(filter.GetCareTypes()))
			for _, careType := range filter.GetCareTypes() {
				careTypes = append(careTypes, int32(careType.Number()))
			}
			repoFilter.CareTypes = careTypes
		}

		// 处理日期范围过滤
		if filter.GetStartDate() != "" {
			repoFilter.StartDate = lo.ToPtr(filter.GetStartDate())
		}
		if filter.GetEndDate() != "" {
			repoFilter.EndDate = lo.ToPtr(filter.GetEndDate())
		}

		// 处理宠物ID过滤
		if filter.GetPetId() > 0 {
			repoFilter.PetID = filter.GetPetId()
		}
	}

	return repoFilter
}

// ListFulfillmentReport 获取履约报告列表
func (l *Logic) listFulfillmentReport(ctx context.Context, req *fulfillmentpb.ListFulfillmentReportRequest) (
	[]*Report, int64, error) {
	var err error
	// 校验请求
	if err = l.verifyListFulfillmentReportRequest(req); err != nil {
		return nil, 0, err
	}

	reqFilter := req.GetFilter()
	// 构建查询参数
	baseParam := &reportrepo.BaseParam{
		CompanyID:  req.GetCompanyId(),
		BusinessID: req.GetBusinessId(),
	}

	// 设置分页信息
	baseParam.PaginationInfo = l.buildReportPaginationInfo(req)

	// 构建过滤条件
	filter := l.buildReportListFilter(reqFilter)
	var filterNoServiceDate *reportrepo.Filter
	// 这里由于 grooming service 在 report 这里没有 service date 概念，无法通过 service date 筛选
	// 因此需要先在 appointment 中查询，再通过 appointment ids 进行筛选
	if len(filter.CareTypes) == 0 || lo.Contains(filter.CareTypes, int32(offeringpb.CareCategory_GROOMING.Number())) {
		filterNoServiceDate, err = l.getListReportFilterNoServiceDate(ctx, baseParam, reqFilter)
		if err != nil {
			return nil, 0, status.Errorf(codes.Internal, "get list report filter no service date: %v", err)
		}
	}

	// 查询总数
	total, err := l.reportRepo.Count(ctx, baseParam, filter, filterNoServiceDate)
	if err != nil {
		return nil, 0, status.Errorf(codes.Internal, "failed to count reports: %v", err)
	}

	// 查询列表
	reports, err := l.reportRepo.List(ctx, baseParam, filter, filterNoServiceDate)
	if err != nil {
		return nil, 0, status.Errorf(codes.Internal, "failed to list reports: %v", err)
	}

	logicReports, err := ConvertReportReposToLogic(ctx, reports)
	if err != nil {
		return nil, 0, status.Errorf(codes.Internal, "failed to convert reports to logic: %v", err)
	}

	// grooming report 没有记录 service date，需要从 appointment 内获取
	logicReports, err = l.fillGroomingReportServiceDate(ctx, logicReports)
	if err != nil {
		return nil, 0, status.Errorf(codes.Internal, "failed to fill grooming report service date: %v", err)
	}

	return logicReports, total, nil
}

func (l *Logic) listFulfillmentReportConfig(ctx context.Context,
	req *fulfillmentpb.ListFulfillmentReportConfigRequest) (
	[]*Report, error) {
	reqFilter := req.GetFilter()
	// 构建查询参数
	baseParam := &reportrepo.BaseParam{
		CompanyID:  req.GetCompanyId(),
		BusinessID: req.GetBusinessId(),
	}
	filter := l.buildReportListFilter(reqFilter)

	// 查询列表
	reports, err := l.reportRepo.ListReportConfig(ctx, baseParam, filter)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to list report config: %v", err)
	}

	logicReports, err := ConvertReportReposToLogic(ctx, reports)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to convert reports to logic: %v", err)
	}

	return logicReports, nil
}

func (l *Logic) ListFulfillmentReport(ctx context.Context, req *fulfillmentpb.ListFulfillmentReportRequest) (
	*fulfillmentpb.ListFulfillmentReportResponse, error) {
	reports, total, err := l.listFulfillmentReport(ctx, req)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to list reports: %v", err)
	}

	if len(reports) == 0 {
		response := &fulfillmentpb.ListFulfillmentReportResponse{
			FulfillmentReportCards: []*fulfillmentpb.ListFulfillmentReportResponse_FulfillmentReportCard{},
			Pagination:             req.GetPagination(),
			Total:                  0,
		}

		return response, nil
	}

	reportIDs := lo.Uniq(lo.Map(reports, func(report *Report, _ int) int64 {
		return report.ID
	}))

	petIDs := lo.Uniq(lo.Map(reports, func(report *Report, _ int) int64 {
		return report.PetID
	}))

	pets, err := l.petRepo.BatchGetPetInfo(ctx, req.GetCompanyId(), petIDs)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get pet infos: %v", err)
	}

	records, err := l.listSendReportRecord(ctx,
		req.GetCompanyId(), req.GetBusinessId(), &sendrecordrepo.Filter{ReportIDs: reportIDs})
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to list send report: %v", err)
	}

	reportPBList := ConvertListFulfillmentReportPB(reports, records, pets)

	// 构建响应
	response := &fulfillmentpb.ListFulfillmentReportResponse{
		FulfillmentReportCards: reportPBList,
		Pagination:             req.GetPagination(),
		Total:                  int32(total),
	}

	return response, nil
}

func (l *Logic) ListFulfillmentReportConfig(ctx context.Context,
	req *fulfillmentpb.ListFulfillmentReportConfigRequest) (*fulfillmentpb.ListFulfillmentReportConfigResponse, error) {
	reports, err := l.listFulfillmentReportConfig(ctx, req)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to list report config: %v", err)
	}

	return &fulfillmentpb.ListFulfillmentReportConfigResponse{
		ReportConfigs: reports,
	}, nil
}

func (l *Logic) fillGroomingReportServiceDate(ctx context.Context, reports []*Report) ([]*Report, error) {
	if len(reports) == 0 {
		return reports, nil
	}

	// 获取 grooming report 的 appointment ids
	appointmentIDs := lo.Uniq(lo.Map(lo.Filter(reports, func(report *Report, _ int) bool {
		return report.CareType == offeringpb.CareCategory_GROOMING && report.AppointmentID != 0
	}), func(report *Report, _ int) int64 {
		return report.AppointmentID
	}))

	if len(appointmentIDs) == 0 {
		return reports, nil
	}

	petDetails, err := l.appointmentRepo.GetPetDetailListByAppointmentIDs(ctx, reports[0].CompanyID, appointmentIDs)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get pet details: %v", err)
	}

	if len(petDetails) == 0 {
		return reports, nil
	}

	petDetails = lo.Filter(petDetails, func(petDetail *appointmentpb.PetDetailModel, _ int) bool {
		return petDetail.GetServiceItemType().Number() == offeringpb.CareCategory_GROOMING.Number()
	})

	// grooming report 会挂载在同 pet 下的最后一个 grooming service 上，按顺序遍历覆盖 service date 即可
	groomingServiceDateMap := make(map[int64]string)
	for _, petDetail := range petDetails {
		groomingServiceDateMap[petDetail.GetGroomingId()] = petDetail.GetStartDate()
	}

	for _, report := range reports {
		if report.CareType == offeringpb.CareCategory_GROOMING {
			report.ServiceDate = groomingServiceDateMap[report.AppointmentID]
		}
	}

	return reports, nil
}

// getListReportGroomingFilter 获取
func (l *Logic) getListReportFilterNoServiceDate(ctx context.Context, baseParam *reportrepo.BaseParam,
	filterParam *fulfillmentpb.ListFulfillmentReportConfigFilter) (*reportrepo.Filter, error) {
	filter := l.buildReportListFilter(filterParam)

	// 筛选 no service date 的 care type
	filter.CareTypes = lo.Filter(filter.CareTypes, func(careType int32, _ int) bool {
		return !lo.Contains(DailyReportCareTypeList, offeringpb.CareCategory(careType))
	})

	companySetting, err := l.organizationRepo.GetCompanyPreferenceSetting(ctx, baseParam.CompanyID)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to list appointment: %v", err)
	}

	// 当未有 appointmentIDs 时，才通过 appointment 服务查询
	if len(filter.AppointmentIDs) == 0 {
		// 查询 appointment
		appointments, err := l.appointmentRepo.GetPetAppointments(ctx, baseParam.CompanyID, baseParam.BusinessID,
			filterParam.GetPetId(), filter.CareTypes,
			filterParam.GetStartDate(), filterParam.GetEndDate(), companySetting.GetTimeZone().GetName())
		if err != nil {
			return nil, status.Errorf(codes.Internal, "failed to list appointment: %v", err)
		}

		if len(appointments) == 0 {
			return nil, nil
		}

		// 获取 appointment id
		filter.AppointmentIDs = lo.Uniq(lo.Map(appointments,
			func(appointment *appointmentpb.AppointmentModel, _ int) int64 {
				return appointment.GetId()
			}))
	}

	filter.StartDate = nil
	filter.EndDate = nil

	return filter, nil
}

// verifyListFulfillmentReportRequest 验证列表履约报告请求
func (l *Logic) verifyListFulfillmentReportRequest(req *fulfillmentpb.ListFulfillmentReportRequest) error {
	if req == nil {
		return status.Errorf(codes.InvalidArgument, "request cannot be nil")
	}

	// 当没有指定以下过滤条件时，必须提供 company_id 和 business_id
	if req.GetFilter() == nil || req.GetFilter().GetPetId() == 0 {
		if req.GetCompanyId() <= 0 {
			return status.Errorf(codes.InvalidArgument, "company_id must be greater than 0")
		}
		if req.GetBusinessId() <= 0 {
			return status.Errorf(codes.InvalidArgument, "business_id must be greater than 0")
		}
	}

	// 验证分页参数
	if req.GetPagination() != nil {
		if req.GetPagination().GetOffset() < 0 {
			return status.Errorf(codes.InvalidArgument, "pagination offset cannot be negative")
		}
		if req.GetPagination().GetLimit() < 0 {
			return status.Errorf(codes.InvalidArgument, "pagination limit cannot be negative")
		}
	}

	return nil
}

// buildReportPaginationInfo 构建报告分页信息
func (l *Logic) buildReportPaginationInfo(req *fulfillmentpb.ListFulfillmentReportRequest,
) *reportrepo.PaginationInfo {
	if req.GetPagination() == nil {
		return &reportrepo.PaginationInfo{
			Offset: defaultOffset,
			Limit:  defaultLimit,
		}
	}

	return &reportrepo.PaginationInfo{
		Offset: req.GetPagination().GetOffset(),
		Limit:  req.GetPagination().GetLimit(),
	}
}

// buildReportListFilter 构建报告列表过滤条件
func (l *Logic) buildReportListFilter(filter *fulfillmentpb.ListFulfillmentReportConfigFilter) *reportrepo.Filter {
	repoFilter := &reportrepo.Filter{}

	if filter != nil {
		// 处理状态过滤
		if filter.GetStatus() != fulfillmentpb.ReportStatus_REPORT_STATUS_UNSPECIFIED {
			statusStr := strings.ToLower(filter.GetStatus().String())
			repoFilter.Status = &statusStr
		}

		// 处理护理类型过滤
		if len(filter.GetCareTypes()) > 0 {
			careTypes := make([]int32, 0, len(filter.GetCareTypes()))
			for _, careType := range filter.GetCareTypes() {
				careTypes = append(careTypes, int32(careType.Number()))
			}
			repoFilter.CareTypes = careTypes
		}

		// 处理日期范围过滤
		if filter.GetStartDate() != "" {
			startDate := filter.GetStartDate()
			repoFilter.StartDate = &startDate
		}
		if filter.GetEndDate() != "" {
			endDate := filter.GetEndDate()
			repoFilter.EndDate = &endDate
		}

		// 处理宠物ID过滤
		if filter.GetPetId() > 0 {
			repoFilter.PetID = filter.GetPetId()
		}

		if len(filter.GetReportIds()) > 0 {
			repoFilter.IDs = filter.GetReportIds()
		}

		if len(filter.GetAppointmentIds()) > 0 {
			repoFilter.AppointmentIDs = filter.GetAppointmentIds()
		}
	}

	return repoFilter
}

// IncreaseFulfillmentOpenedCount 增加履约报告打开次数
func (l *Logic) IncreaseFulfillmentOpenedCount(ctx context.Context,
	req *fulfillmentpb.IncreaseFulfillmentOpenedCountRequest) (
	*fulfillmentpb.IncreaseFulfillmentOpenedCountResponse, error) {

	// 参数验证
	if req == nil || req.GetUuid() == "" {
		return &fulfillmentpb.IncreaseFulfillmentOpenedCountResponse{}, nil
	}

	uuid := req.GetUuid()

	// share 模式暂时不统计 opened count
	if strings.HasPrefix(uuid, ShareUUIDPrefix) {
		return &fulfillmentpb.IncreaseFulfillmentOpenedCountResponse{}, nil
	}

	// 增加打开次数
	err := l.reportRepo.IncreaseOpenedCount(ctx, uuid)
	if err != nil {
		log.ErrorContextf(ctx, "failed to increase opened count for uuid %s: %v", uuid, err)
		// 不返回错误，避免影响用户体验
		return &fulfillmentpb.IncreaseFulfillmentOpenedCountResponse{}, nil
	}

	return &fulfillmentpb.IncreaseFulfillmentOpenedCountResponse{}, nil
}

// BatchSendFulfillmentReport 批量发送履约报告
func (l *Logic) BatchSendFulfillmentReport(ctx context.Context, req *fulfillmentpb.BatchSendFulfillmentReportRequest) (
	*fulfillmentpb.BatchSendFulfillmentReportResponse, error) {
	log.InfoContextf(ctx, "BatchSendFulfillmentReport req:%+v", req)

	// 校验请求
	if err := l.verifyBatchSendFulfillmentReportRequest(req); err != nil {
		return nil, err
	}

	// 批量获取报告信息
	reports, errorMessages, err := l.batchGetReports(ctx, req.GetReportIds())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to batch get reports: %v", err)
	}

	// 并发发送报告
	sendResults := l.batchSendReports(ctx, reports, req)

	// 为找不到的报告添加失败结果
	for _, reportID := range req.GetReportIds() {
		if errorMsg, exists := errorMessages[reportID]; exists {
			failedResult := &fulfillmentpb.FulfillmentReportSendResult{
				FulfillmentReportId: reportID,
				SendMethod:          req.GetSendMethod(),
				IsSentSuccess:       false,
				ErrorMessage:        errorMsg,
			}
			sendResults = append(sendResults, failedResult)
		}
	}

	// 构建 batch send status
	batchSendState := l.buildBatchSendState(sendResults)

	return &fulfillmentpb.BatchSendFulfillmentReportResponse{
		SendResults:    sendResults,
		BatchSendState: batchSendState,
	}, nil
}

// BatchDeleteFulfillmentReport 批量删除履约报告
func (l *Logic) BatchDeleteFulfillmentReport(
	ctx context.Context, req *fulfillmentpb.BatchDeleteFulfillmentReportRequest) (
	*fulfillmentpb.BatchDeleteFulfillmentReportResponse, error) {
	log.InfoContextf(ctx, "BatchDeleteFulfillmentReport req:%+v", req)

	// 校验请求
	if err := l.verifyBatchDeleteFulfillmentReportRequest(req); err != nil {
		return nil, err
	}

	// 批量删除报告
	err := l.reportRepo.BatchDelete(ctx, req.GetCompanyId(), req.GetBusinessId(), req.GetReportCardIds())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to batch delete reports: %v", err)
	}

	return &fulfillmentpb.BatchDeleteFulfillmentReportResponse{}, nil
}

// verifyBatchSendFulfillmentReportRequest 验证批量发送履约报告请求
func (l *Logic) verifyBatchSendFulfillmentReportRequest(req *fulfillmentpb.BatchSendFulfillmentReportRequest) error {
	if req == nil {
		return status.Errorf(codes.InvalidArgument, "request cannot be nil")
	}

	if req.GetCompanyId() <= 0 {
		return status.Errorf(codes.InvalidArgument, "company_id must be greater than 0")
	}

	if req.GetBusinessId() <= 0 {
		return status.Errorf(codes.InvalidArgument, "business_id must be greater than 0")
	}

	if len(req.GetReportIds()) == 0 {
		return status.Errorf(codes.InvalidArgument, "report_ids cannot be empty")
	}

	// 限制批量发送的数量，避免过载
	const maxBatchSize = 100
	if len(req.GetReportIds()) > maxBatchSize {
		return status.Errorf(codes.InvalidArgument, "report_ids count cannot exceed %d", maxBatchSize)
	}

	if req.GetSendMethod() == fulfillmentpb.SendMethod_SEND_METHOD_UNSPECIFIED {
		return status.Errorf(codes.InvalidArgument, "send_method is required")
	}

	if req.GetStaffId() <= 0 {
		return status.Errorf(codes.InvalidArgument, "staff_id must be greater than 0")
	}

	return nil
}

// verifyBatchDeleteFulfillmentReportRequest 验证批量删除履约报告请求
func (l *Logic) verifyBatchDeleteFulfillmentReportRequest(
	req *fulfillmentpb.BatchDeleteFulfillmentReportRequest) error {
	if req == nil {
		return status.Errorf(codes.InvalidArgument, "request cannot be nil")
	}

	if req.GetCompanyId() <= 0 {
		return status.Errorf(codes.InvalidArgument, "company_id must be greater than 0")
	}

	if req.GetBusinessId() <= 0 {
		return status.Errorf(codes.InvalidArgument, "business_id must be greater than 0")
	}

	if len(req.GetReportCardIds()) == 0 {
		return status.Errorf(codes.InvalidArgument, "report_card_ids cannot be empty")
	}

	// 限制批量删除的数量，避免过载
	const maxBatchSize = 100
	if len(req.GetReportCardIds()) > maxBatchSize {
		return status.Errorf(codes.InvalidArgument, "report_card_ids count cannot exceed %d", maxBatchSize)
	}

	return nil
}

// batchGetReports 批量获取报告信息
func (l *Logic) batchGetReports(ctx context.Context, reportIDs []int64) (
	[]*reportrepo.Report, map[int64]string, error) {
	reports := make([]*reportrepo.Report, 0, len(reportIDs))
	errorMessages := make(map[int64]string) // 记录每个报告ID对应的错误信息

	for _, reportID := range reportIDs {
		report, err := l.reportRepo.FindByID(ctx, reportID)
		if err != nil {
			log.ErrorContextf(ctx, "failed to find report %d: %v", reportID, err)
			errorMessages[reportID] = fmt.Sprintf("failed to find report: %v", err)

			continue
		}
		if report == nil {
			errorMessages[reportID] = "report not found"

			continue
		}
		reports = append(reports, report)
	}

	return reports, errorMessages, nil
}

// batchSendReports 批量发送报告
func (l *Logic) batchSendReports(ctx context.Context,
	reports []*reportrepo.Report,
	req *fulfillmentpb.BatchSendFulfillmentReportRequest) []*fulfillmentpb.FulfillmentReportSendResult {
	sendResults := make([]*fulfillmentpb.FulfillmentReportSendResult, 0, len(reports))

	// 使用 goroutine 并发发送，提高效率
	type sendResult struct {
		reportID int64
		result   *fulfillmentpb.FulfillmentReportSendResult
	}

	resultChan := make(chan sendResult, len(reports))

	// 限制并发数量，避免过载
	semaphore := make(chan struct{}, maxSendReportConcurrency)

	for _, report := range reports {
		go func(report *reportrepo.Report) {
			semaphore <- struct{}{}        // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			result := l.sendSingleReport(ctx, report, req)
			resultChan <- sendResult{
				reportID: report.ID,
				result:   result,
			}
		}(report)
	}

	// 收集结果
	resultMap := make(map[int64]*fulfillmentpb.FulfillmentReportSendResult)
	for i := 0; i < len(reports); i++ {
		result := <-resultChan
		resultMap[result.reportID] = result.result
	}

	// 按原始顺序返回结果
	for _, report := range reports {
		if result, exists := resultMap[report.ID]; exists {
			sendResults = append(sendResults, result)
		}
	}

	return sendResults
}

// sendSingleReport 发送单个报告
func (l *Logic) sendSingleReport(ctx context.Context, report *reportrepo.Report,
	req *fulfillmentpb.BatchSendFulfillmentReportRequest) *fulfillmentpb.FulfillmentReportSendResult {
	// 构建发送结果，默认为失败
	sendResult := &fulfillmentpb.FulfillmentReportSendResult{
		FulfillmentReportId: report.ID,
		SendMethod:          req.GetSendMethod(),
		IsSentSuccess:       false,
		ErrorMessage:        "",
	}

	// 获取完整的报告信息
	logicReport, err := l.GetFulfillmentReport(ctx, &GetFulfillmentReport{
		ID:            report.ID,
		CompanyID:     report.CompanyID,
		BusinessID:    report.BusinessID,
		AppointmentID: report.AppointmentID,
		PetID:         report.PetID,
		CareType:      offeringpb.CareCategory(report.CareType),
		ServiceDate:   report.ServiceDate,
		UUID:          report.UUID,
	})
	if err != nil {
		sendResult.ErrorMessage = fmt.Sprintf("failed to get report: %v", err)

		return sendResult
	}

	// 获取报告摘要信息
	reportSummaryInfo, err := l.GetReportSummaryInfo(ctx, logicReport)
	if err != nil {
		sendResult.ErrorMessage = fmt.Sprintf("failed to get report summary info: %v", err)

		return sendResult
	}

	// 构建单个发送请求
	sendReq := &fulfillmentpb.SendFulfillmentReportRequest{
		FulfillmentReportId: report.ID,
		SendMethod:          req.GetSendMethod(),
		StaffId:             req.GetStaffId(),
	}

	// 根据发送方法发送报告
	var sendResponse *fulfillmentpb.SendFulfillmentReportResponse
	switch req.GetSendMethod() {
	case fulfillmentpb.SendMethod_EMAIL:
		sendResponse, err = l.SendEmailMessage(ctx, reportSummaryInfo, sendReq)
	case fulfillmentpb.SendMethod_SMS:
		sendResponse, err = l.SendSmsMessage(ctx, reportSummaryInfo, sendReq)
	default:
		sendResult.ErrorMessage = "unsupported send method"

		return sendResult
	}

	if err != nil {
		sendResult.ErrorMessage = err.Error()

		return sendResult
	}

	// 更新发送结果
	if sendResponse != nil && sendResponse.SendResult != nil {
		sendResult.IsSentSuccess = sendResponse.SendResult.GetIsSentSuccess()
		sendResult.ErrorMessage = sendResponse.SendResult.GetErrorMessage()
	} else {
		sendResult.IsSentSuccess = true // 如果没有错误，认为发送成功
	}

	return sendResult
}

// buildBatchSendState 根据发送结果构建批量发送状态
func (l *Logic) buildBatchSendState(
	sendResults []*fulfillmentpb.FulfillmentReportSendResult) fulfillmentpb.BatchSendState {
	if len(sendResults) == 0 {
		return fulfillmentpb.BatchSendState_BATCH_SEND_STATE_UNSPECIFIED
	}

	successCount := 0
	totalCount := len(sendResults)

	// 统计成功的发送数量
	for _, result := range sendResults {
		if result.GetIsSentSuccess() {
			successCount++
		}
	}

	// 根据成功率判断批量发送状态
	switch successCount {
	case totalCount:
		// 全部成功
		return fulfillmentpb.BatchSendState_ALL_SUCCESS
	case 0:
		// 全部失败
		return fulfillmentpb.BatchSendState_ALL_FAILED
	default:
		// 部分失败
		return fulfillmentpb.BatchSendState_PARTIAL_FAILED
	}
}
