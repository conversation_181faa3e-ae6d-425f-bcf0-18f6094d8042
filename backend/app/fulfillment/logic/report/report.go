package report

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	customerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/config"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/appointment"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/customer"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db"
	questionrepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/question"
	reportrepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/report"
	sendrecordrepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/sendrecord"
	templaterepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/template"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/message"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/organization"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/payment"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/pet"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/redis"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/utils/random"
	fulfillmentpb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	petpb "github.com/MoeGolibrary/moego/backend/proto/pet/v1"
)

const (
	defaultOffset = 0
	defaultLimit  = 50
)

func New() *Logic {
	return &Logic{
		templateRepo:     templaterepo.NewFulfillmentReportTemplateRepo(),
		questionRepo:     questionrepo.NewFulfillmentReportQuestionRepo(),
		reportRepo:       reportrepo.NewFulfillmentReportRepo(),
		sendRecordRepo:   sendrecordrepo.NewFulfillmentReportSendRecordRepo(),
		redisRepo:        redis.New(),
		petRepo:          pet.New(),
		customerRepo:     customer.New(),
		messageRepo:      message.New(),
		organizationRepo: organization.New(),
		appointmentRepo:  appointment.New(),
		paymentRepo:      payment.New(),
		tx:               db.NewTxManager(),
	}
}

type Logic struct {
	templateRepo     templaterepo.ReadWriter
	questionRepo     questionrepo.ReadWriter
	reportRepo       reportrepo.ReadWriter
	sendRecordRepo   sendrecordrepo.ReadWriter
	redisRepo        redis.API
	petRepo          pet.ReadWriter
	customerRepo     customer.ReadWriter
	messageRepo      message.ReadWriter
	appointmentRepo  appointment.ReadWriter
	organizationRepo organization.ReadWriter
	paymentRepo      payment.ReadWriter
	tx               db.TransactionManager
}

func (l *Logic) initTemplate(
	ctx context.Context, companyID, businessID int64, careType offeringpb.CareCategory,
) (*Template, *TemplateQuestion, error) {

	lockKey := fmt.Sprintf(redis.InitFulfillmentReportTemplateLockKey, companyID, businessID, careType.String())
	if err := l.redisRepo.Lock(ctx, lockKey, redis.InitFulfillmentReportTemplateLockTTL); err != nil {
		return nil, nil, err
	}
	defer func() {
		if err := l.redisRepo.Unlock(ctx, lockKey); err != nil {
			log.ErrorContextf(ctx, "InitFulfillmentReportTemplate Failed to release lock, lock:%s", lockKey)
		}
	}()

	// check init before
	repoTemplate, err := l.templateRepo.FindByUniqueKey(ctx, companyID, businessID, int32(careType.Number()))
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil, err
	}

	var repoQuestions []*questionrepo.Question

	// 使用事务处理
	err = l.tx.ExecuteInTransaction(ctx, []func(opCtx context.Context, tx *gorm.DB) error{
		func(opCtx context.Context, _ *gorm.DB) error {
			if repoTemplate == nil {
				//todo 通过 plan feature 获取订阅 reviewBooster 的结果，作为 template showReviewBooster 的初始化结果
				repoTemplate = &templaterepo.Template{
					CompanyID:  companyID,
					BusinessID: businessID,
					CareType:   int32(careType.Number()),
				}
				if err = l.templateRepo.Create(opCtx, repoTemplate); err != nil {
					return err
				}
			}

			// 如果 question 已经初始化过则直接返回
			existQuestion, err := l.questionRepo.FindByFilter(opCtx, questionrepo.Filter{
				CompanyID:  &companyID,
				BusinessID: &businessID,
				CareType:   lo.ToPtr(int32(careType.Number())),
			})
			if err != nil {
				return err
			}
			if len(existQuestion) > 0 {
				repoQuestions = existQuestion

				return nil
			}

			// 通过 0 值获取默认问题
			defaultQuestions, err := l.questionRepo.FindByFilter(opCtx, questionrepo.Filter{
				CompanyID:  lo.ToPtr(int64(0)),
				BusinessID: lo.ToPtr(int64(0)),
				CareType:   lo.ToPtr(int32(careType.Number())),
			})
			if err != nil {
				return err
			}

			lo.ForEach(defaultQuestions, func(question *questionrepo.Question, _ int) {
				question.ID = 0
				question.CareType = int32(careType.Number())
				question.CompanyID = companyID
				question.BusinessID = businessID
				repoQuestions = append(repoQuestions, question)
			})

			if err := l.questionRepo.BatchCreate(opCtx, repoQuestions); err != nil {
				return err
			}

			return nil
		},
	})
	if err != nil {
		return nil, nil, err
	}

	templateQuestion, err := ConvertQuestionRepoToLogics(ctx, repoQuestions)
	if err != nil {
		return nil, nil, status.Errorf(codes.Internal, "convert question repo to logics err: %v", err)
	}

	return ConvertTemplateRepoToLogic(repoTemplate), templateQuestion, nil
}

// GetTemplate 获取 template 及 question 相关数据并初始化
func (l *Logic) GetTemplate(ctx context.Context,
	companyID, businessID int64, careType offeringpb.CareCategory) (*Template, error) {
	// daily report 的 templateModel 为是 company level
	if lo.Contains(DailyReportCareTypeList, careType) {
		businessID = 0
	}

	var template *Template
	var questions *TemplateQuestion

	// 获取模板 & 问题
	repoTemplate, err := l.templateRepo.FindByUniqueKey(ctx, companyID, businessID, int32(careType.Number()))
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, status.Errorf(codes.Internal, "find template err: %v", err)
	}
	template = ConvertTemplateRepoToLogic(repoTemplate)

	repoQuestions, err := l.questionRepo.FindByFilter(ctx, questionrepo.Filter{
		CompanyID:  &companyID,
		BusinessID: &businessID,
		CareType:   lo.ToPtr(int32(careType.Number())),
	})
	if err != nil {
		return nil, status.Errorf(codes.Internal, "find question err: %v", err)
	}
	questions, err = ConvertQuestionRepoToLogics(ctx, repoQuestions)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "convert question repo to logics err: %v", err)
	}

	if template == nil || questions == nil {
		template, questions, err = l.initTemplate(ctx, companyID, businessID, careType)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "init template err: %v", err)
		}
	}

	// 填充 review booster，只有 grooming template 有 review booster
	var boosterConfig *message.ReviewBooster
	if careType == offeringpb.CareCategory_GROOMING {
		boosterConfig, err = l.messageRepo.GetReviewBoosterConfig(ctx, businessID)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "get review booster err: %v", err)
		}
	}

	template = buildTemplate(template, questions, boosterConfig)

	if template.ShowReviewBooster {
		// 检查是否有权限使用 review booster
		featureQuota, err := l.paymentRepo.
			QueryCompanyPlanFeatureByCidCode(ctx, int32(template.CompanyID), payment.FcReviewBooster)
		if err != nil || !featureQuota.Enable {
			template.ShowReviewBooster = false
		}
	}

	return template, nil
}

func (l *Logic) UpdateTemplate(ctx context.Context, template *Template, deleteQuestionIDs []int64) error {
	// 获取模板
	repoTemplate, err := l.templateRepo.FindByUniqueKey(
		ctx, template.CompanyID, template.BusinessID, int32(template.CareType.Number()))
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return status.Errorf(codes.Internal, "find template err: %v", err)
	}

	if repoTemplate == nil {
		return status.Errorf(codes.NotFound, "template not found")
	}

	repoTemplate = ConvertTemplateLogicToRepo(template)
	now := time.Now()
	repoTemplate.UpdateTime = now
	repoTemplate.LastPublishTime = now

	// TODO 更新 review booster link

	// 分离创建和更新的问题
	var createQuestions []*questionrepo.Question
	var updateQuestions []*questionrepo.Question
	for _, question := range ConvertTemplateQuestionToQuestions(template.Questions) {
		repoQuestion := ConvertQuestionLogicToRepo(question)
		if question.ID == 0 {
			repoQuestion.CompanyID = template.CompanyID
			createQuestions = append(createQuestions, repoQuestion)
		} else {
			updateQuestions = append(updateQuestions, repoQuestion)
		}
	}

	// 使用事务处理
	err = l.tx.ExecuteInTransaction(ctx, []func(opCtx context.Context, tx *gorm.DB) error{
		func(opCtx context.Context, _ *gorm.DB) error {
			// 更新模板
			if err = l.templateRepo.Update(opCtx, repoTemplate); err != nil {
				return status.Errorf(codes.Internal, "update template err: %v", err)
			}
			// 更新现有问题
			if len(updateQuestions) > 0 {
				if err = l.questionRepo.BatchUpdate(opCtx, updateQuestions); err != nil {
					return status.Errorf(codes.Internal, "update questions err: %v", err)
				}
			}
			// 创建新问题
			if len(createQuestions) > 0 {
				if err = l.questionRepo.BatchCreate(opCtx, createQuestions); err != nil {
					return status.Errorf(codes.Internal, "create questions err: %v", err)
				}
			}
			// 删除指定问题
			if len(deleteQuestionIDs) > 0 {
				err = l.questionRepo.BatchDelete(opCtx, questionrepo.Filter{
					CompanyID: &template.CompanyID,
					IDList:    deleteQuestionIDs,
				})
				if err != nil {
					return status.Errorf(codes.Internal, "delete questions err: %v", err)
				}
			}

			return nil
		},
	})
	if err != nil {
		return err
	}

	// 更新 review booster
	err = l.updateReviewBoosterConfig(ctx, template)
	if err != nil {
		return err
	}

	return nil
}

func (l *Logic) updateReviewBoosterConfig(ctx context.Context, template *Template) error {
	reviewBooster := &message.ReviewBooster{
		CompanyID:        template.CompanyID,
		BusinessID:       template.BusinessID,
		PositiveYelp:     template.YelpReviewLink,
		PositiveGoogle:   template.GoogleReviewLink,
		PositiveFacebook: template.FacebookReviewLink,
	}

	_, err := l.messageRepo.UpdateReviewBoosterConfig(ctx, reviewBooster)
	if err != nil {
		return status.Errorf(codes.Internal, "update review booster config err: %v", err)
	}

	return nil
}

func (l *Logic) getFulfillmentReport(ctx context.Context,
	req *GetFulfillmentReport) (*Report, error) {
	var err error
	if err = l.verifyGetFulfillmentReportRequest(req); err != nil {
		return nil, err
	}

	var existReport *reportrepo.Report

	if req.ID != 0 {
		existReport, err = l.reportRepo.FindByID(ctx, req.ID)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}

		if existReport == nil {
			return nil, status.Errorf(codes.NotFound, "report not found")
		}

	} else if req.UUID != "" {
		existReport, err = l.reportRepo.FindByUUID(ctx, req.UUID)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}

		if existReport == nil {
			return nil, status.Errorf(codes.NotFound, "report not found")
		}
	} else {
		existReport, err = l.reportRepo.FindByUniqueKey(
			ctx,
			req.BusinessID,
			req.AppointmentID,
			req.PetID,
			int32(req.CareType.Number()),
			req.ServiceDate)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, status.Errorf(codes.Internal, "find report err:%v", err)
		}
	}

	report, err := ConvertReportRepoToLogic(ctx, existReport)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "convert report err:%v", err)
	}

	return report, nil
}

// GetFulfillmentReport 获取 report 并初始化，不落库
func (l *Logic) GetFulfillmentReport(ctx context.Context,
	req *GetFulfillmentReport) (*Report, error) {

	report, err := l.getFulfillmentReport(ctx, req)
	if err != nil {
		return nil, err
	}

	if report == nil {
		report, err = l.initFulfillmentReport(ctx, req)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "init report error: %v", err)
		}
	}

	return report, nil
}

func (l *Logic) IsNeedRefresh(ctx context.Context, report *Report) (bool, error) {
	if report == nil {
		return false, nil
	}

	repoTemplate, err := l.templateRepo.FindByUniqueKey(
		ctx, report.CompanyID, report.BusinessID, int32(report.CareType.Number()))
	if err != nil {
		return false, err
	}

	if repoTemplate == nil {
		return false, nil
	}

	if repoTemplate.LastPublishTime.After(report.TemplateVersion) {
		return true, nil
	}

	return false, nil
}

// initFulfillmentReport 初始化报告 不落库
func (l *Logic) initFulfillmentReport(ctx context.Context, req *GetFulfillmentReport) (*Report, error) {

	companyID, businessID, petID, careType := req.CompanyID, req.BusinessID, req.PetID, req.CareType

	petInfo, err := l.petRepo.GetPetInfo(ctx, companyID, petID)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "get pet info err:%v", err)
	}
	if petInfo == nil {
		return nil, status.Errorf(codes.NotFound, "pet not found")
	}

	customerInfo, err := l.customerRepo.GetCustomerInfo(ctx, companyID, petInfo.CustomerId)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "get customer info err:%v", err)
	}
	if customerInfo == nil {
		return nil, status.Errorf(codes.NotFound, "customer not found")
	}

	// 获取模板信息
	logicTemplate, err := l.GetTemplate(ctx, companyID, businessID, careType)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "init report get template err:%v", err)
	}

	return l.buildInitReport(req, logicTemplate, petInfo, customerInfo), nil
}

func (l *Logic) buildInitReport(req *GetFulfillmentReport,
	logicTemplate *Template, petInfo *businesscustomerpb.BusinessCustomerPetInfoModel,
	customerInfo *businesscustomerpb.BusinessCustomerModel) *Report {

	report := &Report{
		CompanyID:       req.CompanyID,
		BusinessID:      req.BusinessID,
		CustomerID:      petInfo.GetCustomerId(),
		AppointmentID:   req.AppointmentID,
		PetID:           req.PetID,
		PetTypeID:       int64(petInfo.PetType.Number()),
		CareType:        req.CareType,
		ServiceDate:     req.ServiceDate,
		Status:          fulfillmentpb.ReportStatus_CREATED,
		UUID:            "",
		LinkOpenedCount: 0,
		ThemeCode:       logicTemplate.ThemeCode,
		Template:        logicTemplate,
		Content:         l.buildReportContent(logicTemplate, customerInfo),
	}

	if logicTemplate.LastPublishTime == 0 {
		report.TemplateVersion = time.Now()
	} else {
		report.TemplateVersion = time.Unix(logicTemplate.LastPublishTime, 0)
	}

	return report
}

func (l *Logic) buildReportContent(logicTemplate *Template,
	customerInfo *businesscustomerpb.BusinessCustomerModel) *Content {
	feedbackQuestions, petConditions := l.buildReportQuestions(logicTemplate)

	content := &Content{
		Photos:        []string{},
		Videos:        []string{},
		Feedbacks:     feedbackQuestions,
		PetConditions: petConditions,
		Recommendation: &ReportRecommendation{
			FrequencyDay: customerInfo.GetPreferredGroomingFrequency().GetValue(),
			// nolint: lll
			FrequencyType: fulfillmentpb.FulfillmentReportRecommendation_FrequencyType(
				fulfillmentpb.FulfillmentReportRecommendation_FrequencyType_value[customerInfo.GetPreferredGroomingFrequency().Period.String()]),
		},

		ThemeColor:      logicTemplate.ThemeColor,
		LightThemeColor: lo.ToPtr(logicTemplate.LightThemeColor),
	}

	return content
}

func (l *Logic) buildReportQuestions(template *Template) (
	feedbackQuestions []*ReportQuestion,
	petConditions []*ReportQuestion,
) {
	lo.ForEach(ConvertTemplateQuestionToQuestions(template.Questions), func(question *Question, _ int) {

		reportQuestion := &ReportQuestion{
			ID:       question.ID,
			Category: question.Category,
			Title:    question.Title,
			Key:      question.Key,
			Type:     question.Type,
			Required: question.IsRequired,
			Options:  question.Extra.Options,
		}

		switch question.Category {
		case fulfillmentpb.QuestionCategory_FEEDBACK:
			reportQuestion.IsShow = template.ShowOverallFeedback
			feedbackQuestions = append(feedbackQuestions, reportQuestion)
		case fulfillmentpb.QuestionCategory_CUSTOMIZE_FEEDBACK:
			reportQuestion.IsShow = template.ShowCustomizedFeedback
			feedbackQuestions = append(feedbackQuestions, reportQuestion)
		case fulfillmentpb.QuestionCategory_PET_CONDITION:
			reportQuestion.IsShow = template.ShowPetCondition
			petConditions = append(petConditions, reportQuestion)
		}
	})

	return feedbackQuestions, petConditions
}

func (l *Logic) verifyGetFulfillmentReportRequest(req *GetFulfillmentReport) error {
	if req.UUID != "" || req.ID != 0 {
		return nil
	}
	if req.CompanyID == 0 || req.BusinessID == 0 {
		return status.Errorf(codes.InvalidArgument, "company_id and business_id is required")
	}

	// 如果是 daycare 或者 boarding，则必须有 service date
	if lo.Contains(DailyReportCareTypeList, req.CareType) && req.ServiceDate == "" {
		return status.Errorf(codes.InvalidArgument, "service date is required")
	}

	if req.ID == 0 &&
		(req.AppointmentID == 0 ||
			req.CareType == offeringpb.CareCategory_CARE_CATEGORY_UNSPECIFIED ||
			req.PetID == 0) {
		return status.Errorf(codes.InvalidArgument, "invalid request")
	}

	return nil
}

func (l *Logic) UpdateFulfillmentReport(ctx context.Context, updateReport *Report) (*Report, error) {

	existReport, err := l.getFulfillmentReport(ctx, ConvertReportLogicToGetFulfillmentReport(updateReport))
	if err != nil {
		return nil, err
	}

	var report *Report
	if existReport == nil {
		report, err = l.createReport(ctx, updateReport)
		if err != nil {
			return nil, err
		}
	} else {
		// TODO merge report if template changed
		report, err = l.updateReport(ctx, existReport, updateReport)
		if err != nil {
			return nil, err
		}
	}

	// todo 调用 grooming server 上传 S3

	return report, nil
}

func (l *Logic) createReport(ctx context.Context, report *Report) (*Report, error) {
	initReport, err := l.initFulfillmentReport(ctx, ConvertReportLogicToGetFulfillmentReport(report))
	if err != nil {
		return nil, status.Errorf(codes.Internal, "init report error: %v", err)
	}

	initReport.UUID = l.genReportUUID()
	initReport.UpdateBy = report.UpdateBy
	initReport.Status = fulfillmentpb.ReportStatus_DRAFT
	initReport.Content = report.Content
	initReport.ThemeCode = report.ThemeCode

	repoReport, err := ConvertReportLogicToRepo(ctx, initReport)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "convert report logic to repo err: %v", err)
	}

	if err = l.reportRepo.Create(ctx, repoReport); err != nil {
		return nil, status.Errorf(codes.Internal, "create report err:%v", err)
	}

	return ConvertReportRepoToLogic(ctx, repoReport)
}

func (l *Logic) updateReport(ctx context.Context, existReport *Report, updateReport *Report) (*Report, error) {
	if existReport == nil {
		return nil, status.Errorf(codes.NotFound, "report not found")
	}

	existReport.UpdateBy = updateReport.UpdateBy
	existReport.UpdateTime = time.Now()
	existReport.ThemeCode = updateReport.ThemeCode
	existReport.Content = updateReport.Content
	existReport.TemplateVersion = time.Now()

	repoReport, err := ConvertReportLogicToRepo(ctx, existReport)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "convert report logic to repo err: %v", err)
	}

	if err = l.reportRepo.Update(ctx, repoReport); err != nil {
		return nil, status.Errorf(codes.Internal, "update report err:%v", err)
	}

	return ConvertReportRepoToLogic(ctx, repoReport)
}

func (l *Logic) genReportUUID() string {
	return random.String(12)
}

// GetTemplateReport 获取 template report,appointmentId = 0 petId = 0 serviceDate = "" 的 Report 为 template report
func (l *Logic) GetTemplateReport(ctx context.Context,
	req *fulfillmentpb.GetFulfillmentTemplateReportRequest) (*Report, error) {
	if req.GetCompanyId() == 0 || req.GetBusinessId() == 0 || req.GetCareType() == 0 {
		return nil, status.Errorf(codes.InvalidArgument, "get default template report param invalid")
	}
	// todo 检查套餐是否可用

	templateReportRepo, err := l.reportRepo.FindByUniqueKey(ctx,
		req.GetBusinessId(), 0, 0, int32(req.GetCareType().Number()), "")
	if err != nil {
		return nil, status.Errorf(codes.Internal, "get default template report err:%v", err)
	}

	existTemplate, err := l.GetTemplate(ctx, req.GetCompanyId(), req.GetBusinessId(), req.GetCareType())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "get template err:%v", err)
	}

	var templateReport *Report
	if templateReportRepo == nil {
		templateReport = l.buildInitReport(&GetFulfillmentReport{
			CompanyID:     req.GetCompanyId(),
			BusinessID:    req.GetBusinessId(),
			AppointmentID: 0,
			PetID:         0,
			CareType:      req.GetCareType(),
			ServiceDate:   "",
		}, existTemplate, SamplePet, SampleCustomer)

		templateReport.Status = fulfillmentpb.ReportStatus_DRAFT
		templateReport.UUID = l.genReportUUID()
		templateReport.PetTypeID = int64(petpb.Pet_DOG.Number())
	} else {
		templateReport, err = ConvertReportRepoToLogic(ctx, templateReportRepo)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "convert default template report err:%v", err)
		}
	}

	// 优先使用 req 中传递的 template
	if req.PreviewTemplate == nil {
		templateReport.Template = existTemplate
	} else {
		templateReport.Template = ConvertTemplatePBToLogic(req.GetPreviewTemplate())
	}

	if req.GetThemeCode() != "" {
		templateReport.ThemeCode = req.GetThemeCode()
	}

	templateReport = l.fillTemplateReportContent(templateReport)

	repoReport, err := ConvertReportLogicToRepo(ctx, templateReport)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "convert template report err:%v", err)
	}

	if templateReport.ID == 0 {
		if err = l.reportRepo.Create(ctx, repoReport); err != nil {
			return nil, status.Errorf(codes.Internal, "create template report err:%v", err)
		}
	} else {
		repoReport.UpdateTime = time.Now()
		if err = l.reportRepo.Update(ctx, repoReport); err != nil {
			return nil, status.Errorf(codes.Internal, "update template report err:%v", err)
		}
	}

	return templateReport, nil
}

func (l *Logic) fillTemplateReportContent(report *Report) *Report {
	content := l.buildReportContent(report.Template, SampleCustomer)
	// 填充默认值
	for _, question := range content.Feedbacks {
		if question.Key == QuestionKeyAdditionalNote {
			question.InputText = SampleComment
		}
		// preview 时默认全都展示
		question.IsShow = true
		// preview 时 mood 默认选择第一个
		if question.Key == QuestionKeyMood && len(question.Options) > 0 {
			question.Choices = append(question.Choices, question.Options[0])
		}
	}

	for _, question := range content.PetConditions {
		// preview 时默认全都展示
		question.IsShow = true
		// body view 默认填充数据
		if question.Type == strings.ToLower(fulfillmentpb.QuestionType_BODY_VIEW.String()) {
			question.URLs = &BodyViewURL{
				Left:  SampleBodyViewLeft,
				Right: SampleBodyViewRight,
			}
		}
	}
	content.Photos = append(content.Photos, SamplePhotoBefore, SamplePhotoAfter)
	report.Content = content

	return report
}

func (l *Logic) GetReportSummaryInfo(ctx context.Context,
	report *Report) (*fulfillmentpb.FulfillmentReportCardSummaryInfo, error) {
	companyID, businessID, petID := report.CompanyID, report.BusinessID, report.PetID
	// 获取 business 信息
	companyPreferenceSetting, err := l.organizationRepo.GetCompanyPreferenceSetting(ctx, companyID)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "get company preference setting err:%v", err)
	}

	businessInfo, err := l.organizationRepo.GetBusinessDetail(ctx, companyID, businessID)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "get business detail err:%v", err)
	}

	// 获取 pet 信息
	var petInfo *businesscustomerpb.BusinessCustomerPetInfoModel
	if petID == 0 {
		petInfo = SamplePet
	} else {
		petInfo, err = l.petRepo.GetPetInfo(ctx, companyID, petID)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "get pet info err:%v", err)
		}
	}

	// appointment info
	var appointmentInfo = &fulfillmentpb.FulfillmentReportCardSummaryInfo_AppointmentInfo{}
	if report.AppointmentID == 0 {
		appointmentInfo = getDefaultSampleAppointment(report.Content.Recommendation.FrequencyDay)
	}
	//todo get appointment info

	//todo next appointment

	// review booster
	reviewBooster := &message.ReviewBooster{}
	if report.CareType != offeringpb.CareCategory_GROOMING {
		reviewBooster, err = l.messageRepo.GetReviewBoosterConfig(ctx, businessID)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "get review booster config err: %v", err)
		}
	}

	// theme config

	return &fulfillmentpb.FulfillmentReportCardSummaryInfo{
		BusinessInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_BusinessInfo{
			BusinessId:       businessID,
			BusinessName:     businessInfo.GetName(),
			AvatarPath:       businessInfo.GetAvatarPath(),
			PhoneNumber:      businessInfo.GetContactPhoneNumber(),
			BusinessMode:     int32(businessInfo.BusinessMode.Number()),
			Address1:         businessInfo.GetAddress().GetAddress1(),
			Address2:         businessInfo.GetAddress().GetAddress2(),
			AddressCity:      businessInfo.GetAddress().GetCity(),
			AddressState:     businessInfo.GetAddress().GetState(),
			AddressZipcode:   businessInfo.GetAddress().GetZipcode(),
			AddressCountry:   businessInfo.GetAddress().GetCountry(),
			Coordinate:       businessInfo.GetAddress().GetCoordinate(),
			DateFormat:       companyPreferenceSetting.GetDateFormatType().String(),
			TimeFormatType:   int32(companyPreferenceSetting.TimeFormatType.Number()),
			BookOnlineName:   "",
			BookOnlineEnable: false,
		},
		AppointmentInfo: appointmentInfo,
		PetInfo: &fulfillmentpb.FulfillmentReportCardSummaryInfo_PetInfo{
			PetId:      petID,
			PetName:    petInfo.GetPetName(),
			AvatarPath: petInfo.GetAvatarPath(),
			PetBreed:   petInfo.GetBreed(),
			Gender:     petpb.Pet_PetGender(petpb.Pet_PetGender_value[petInfo.GetGender().String()]),
			PetType:    petpb.Pet_PetType(petpb.Pet_PetType_value[petInfo.PetType.String()]),
			Weight:     petInfo.GetWeight(),
		},
		FulfillmentReport: ConvertReportLogicToPB(report),
		ReviewBoosterConfig: &fulfillmentpb.FulfillmentReportCardSummaryInfo_ReviewBoosterConfig{
			PositiveScore:    int32(reviewBooster.PositiveScore),
			PositiveYelp:     reviewBooster.PositiveYelp,
			PositiveFacebook: reviewBooster.PositiveFacebook,
			PositiveGoogle:   reviewBooster.PositiveGoogle,
		},
	}, nil
}

func (l *Logic) BuildEmailSubject(subject string,
	summaryInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo) (string, error) {
	// 检查是否为重发
	isResend := summaryInfo.GetFulfillmentReport().GetStatus() == fulfillmentpb.ReportStatus_SENT

	// 判断 subject 是否为空
	if subject == "" {
		// 设置默认主题模板
		if isResend {
			subject = "[Updated] {PetName}'s {Title} at {BusinessName}"
		} else {
			subject = "{PetName}'s {CareType} Report at {BusinessName}"
		}
	}

	// 获取业务信息
	businessName := summaryInfo.GetBusinessInfo().GetBusinessName()
	petName := summaryInfo.GetPetInfo().GetPetName()

	// 获取模板标题
	title := summaryInfo.GetFulfillmentReport().GetTemplate().GetTitle()
	if title == "" {
		if summaryInfo.GetFulfillmentReport().GetCareType() == offeringpb.CareCategory_GROOMING {
			title = groomingReportTitle
		} else {
			title = dailyReportTitle
		}
	}

	// 获取主要员工信息
	staffNames := l.getMainStaffNames(summaryInfo)
	mainStaff := strings.Join(staffNames, ", ")

	// 替换占位符
	careTypeStr := "daily"
	if summaryInfo.GetFulfillmentReport().GetCareType() == offeringpb.CareCategory_GROOMING {
		careTypeStr = "grooming"
	}
	subject = strings.ReplaceAll(subject, "{BusinessName}", businessName)
	subject = strings.ReplaceAll(subject, "{PetName}", petName)
	subject = strings.ReplaceAll(subject, "{Title}", title)
	subject = strings.ReplaceAll(subject, "{MainStaff}", mainStaff)
	subject = strings.ReplaceAll(subject, "{CareType}", careTypeStr)

	return subject, nil
}

const (
	groomingReportTitle = "Grooming Report"
	dailyReportTitle    = "Daily Report"
)

func (l *Logic) BuildSmsSendContent(summaryInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo) (string, error) {
	// 获取业务信息
	businessInfo := summaryInfo.GetBusinessInfo()
	petInfo := summaryInfo.GetPetInfo()
	careType := summaryInfo.GetFulfillmentReport().GetCareType()

	// 生成直接访问 url
	reportClientURL, err := l.getReportClientURL(careType)
	if err != nil {
		return "", err
	}
	directLink := fmt.Sprintf(reportClientURL, summaryInfo.GetFulfillmentReport().GetUuid())

	// 检查是否为重发
	isResend := summaryInfo.GetFulfillmentReport().GetStatus() == fulfillmentpb.ReportStatus_SENT

	// 获取模板标题
	title := summaryInfo.GetFulfillmentReport().GetTemplate().GetTitle()
	if title == "" {
		if careType == offeringpb.CareCategory_GROOMING {
			title = groomingReportTitle
		} else {
			title = dailyReportTitle
		}
	}

	// 设置短信模板
	var sendContent string
	if isResend {
		sendContent = "[Updated] {PetName}’s {Title} at {BusinessName} {DirectAccessLink}"
	} else {
		sendContent = "{PetName}’s {Title} at {BusinessName} {DirectAccessLink}"
	}

	// 获取主要员工信息
	staffNames := l.getMainStaffNames(summaryInfo)
	mainStaff := strings.Join(staffNames, ", ")

	// 替换占位符
	sendContent = strings.ReplaceAll(sendContent, "{BusinessName}", businessInfo.GetBusinessName())
	sendContent = strings.ReplaceAll(sendContent, "{PetName}", petInfo.GetPetName())
	sendContent = strings.ReplaceAll(sendContent, "{Title}", title)
	sendContent = strings.ReplaceAll(sendContent, "{MainStaff}", mainStaff)
	sendContent = strings.ReplaceAll(sendContent, "{DirectAccessLink}", directLink)

	return sendContent, nil
}

func (l *Logic) getReportClientURL(careType offeringpb.CareCategory) (string, error) {
	cfg := config.GetCfg()

	if careType == offeringpb.CareCategory_GROOMING {
		if cfg.ReportClient != nil && cfg.ReportClient.GroomingReportURL != "" {
			return cfg.ReportClient.GroomingReportURL, nil
		}

		return "", status.Errorf(codes.Internal, "grooming report url is not configured")
	}

	if cfg.ReportClient != nil && cfg.ReportClient.DailyReportURL != "" {
		return cfg.ReportClient.DailyReportURL, nil
	}

	return "", status.Errorf(codes.Internal, "daily report url is not configured")
}

// getMainStaffNames 获取主要员工姓名列表
func (l *Logic) getMainStaffNames(summaryInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo) []string {
	var staffNames []string
	if appointmentInfo := summaryInfo.GetAppointmentInfo(); appointmentInfo != nil {
		for _, petService := range appointmentInfo.GetPetService() {
			for _, petDetail := range petService.GetPetDetails() {
				if staffInfo := petDetail.GetStaffInfo(); staffInfo != nil {
					firstName := staffInfo.GetStaffFirstName()
					if firstName != "" {
						staffNames = append(staffNames, firstName)
					}
				}
			}
		}
	}

	return staffNames
}

func (l *Logic) SendSmsMessage(ctx context.Context, summaryInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo,
	req *fulfillmentpb.SendFulfillmentReportRequest) (*fulfillmentpb.SendFulfillmentReportResponse, error) {
	// 查询 report
	report, err := l.GetFulfillmentReport(ctx, &GetFulfillmentReport{
		ID: req.GetFulfillmentReportId(),
	})
	if err != nil {
		return nil, err
	}
	if report == nil {
		return nil, status.Errorf(codes.NotFound, "report not found")
	}

	// 查询历史发送记录
	sendRecordHistory, err := l.sendRecordRepo.FindByReportIDAndSendMethod(
		ctx, report.ID, int32(fulfillmentpb.SendMethod_SMS))
	if err != nil {
		return nil, err
	}
	// 初始化或获取发送记录
	var sendRecord *sendrecordrepo.SendRecord
	if sendRecordHistory == nil {
		// 创建新的发送记录
		sendRecord = &sendrecordrepo.SendRecord{
			ReportID:      report.ID,
			CompanyID:     report.CompanyID,
			BusinessID:    report.BusinessID,
			AppointmentID: report.AppointmentID,
			PetID:         report.PetID,
			SendMethod:    int32(fulfillmentpb.SendMethod_SMS),
			SentBy:        req.GetStaffId(),
		}
	} else {
		// 使用现有记录
		sendRecord = &sendrecordrepo.SendRecord{
			ID:            sendRecordHistory.ID,
			ReportID:      report.ID,
			CompanyID:     report.CompanyID,
			BusinessID:    report.BusinessID,
			AppointmentID: report.AppointmentID,
			PetID:         report.PetID,
			SendMethod:    int32(fulfillmentpb.SendMethod_SMS),
			SentBy:        req.GetStaffId(),
		}
	}

	// 设置发送时间和内容
	sendRecord.SentTime = time.Now()
	contentJSON, _ := json.Marshal(report.Content)
	sendRecord.ContentJSON = string(contentJSON)

	// 构建短信内容
	messageBody, err := l.BuildSmsSendContent(summaryInfo)
	if err != nil {
		return nil, err
	}

	// 发送短信
	sendResult := false
	var errorMessage string
	var targetType int32
	if report.CareType == offeringpb.CareCategory_GROOMING {
		targetType = message.MessageTargetTypeGroomingReport
	} else {
		targetType = message.MessageTargetTypeDailyReport
	}

	// 调用实际的短信发送服务
	sendMessages := &message.SendMessages{
		BusinessID: int32(report.BusinessID),
		StaffID:    int32(req.GetStaffId()),
		Customer: &message.SendMessageCustomerParams{
			CustomerID: int32(report.CustomerID),
		},
		Method:      message.MessageMethodMsg,
		TargetType:  targetType,
		TargetID:    int32(report.AppointmentID),
		MessageBody: messageBody,
	}
	result, err := l.messageRepo.SendServicesMessageToCustomer(ctx, sendMessages)

	if err != nil {
		errorMessage = err.Error()
		sendResult = false
	} else {
		sendResult = true
		errorMessage = result
	}

	// 更新发送记录
	sendRecord.IsSentSuccess = &sendResult
	sendRecord.ErrorMessage = errorMessage

	log.InfoContextf(ctx, "SendSmsMessage sendRecord: %+v", sendRecord)

	// 保存或更新发送记录
	if sendRecordHistory == nil {
		err = l.sendRecordRepo.Create(ctx, sendRecord)
	} else {
		err = l.sendRecordRepo.Update(ctx, sendRecord)
	}
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to save send record: %v", err)
	}

	// 更新报告状态为已发送
	if sendResult {
		report.Status = fulfillmentpb.ReportStatus_SENT
		_, err = l.UpdateFulfillmentReport(ctx, report)
		if err != nil {
			log.ErrorContextf(ctx, "failed to update report status: %v", err)
		}
	}

	return &fulfillmentpb.SendFulfillmentReportResponse{
		SendResult: &fulfillmentpb.FulfillmentReportSendResult{
			FulfillmentReportId: report.ID,
			SendMethod:          fulfillmentpb.SendMethod_SMS,
			IsSentSuccess:       sendResult,
			ErrorMessage:        errorMessage,
		},
	}, nil
}

func (l *Logic) ListSendReportRecords(ctx context.Context, req *fulfillmentpb.ListSendReportRecordsRequest) (
	*fulfillmentpb.ListSendReportRecordsResponse, error) {
	log.InfoContextf(ctx, "ListSendReportRecords req:%+v", req)

	// 校验请求
	if err := l.verifyListSendReportRecordsRequest(req); err != nil {
		return nil, err
	}

	// 构建查询参数
	baseParam := &sendrecordrepo.BaseParam{
		CompanyID:  req.GetCompanyId(),
		BusinessID: req.GetBusinessId(),
	}

	// 设置分页信息
	baseParam.PaginationInfo = l.buildSendRecordPaginationInfo(req)

	// 构建过滤条件
	filter := l.buildSendRecordFilter(req.GetFilter())

	// 查询总数
	total, err := l.sendRecordRepo.Count(ctx, baseParam, filter)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to count send records: %v", err)
	}

	// 查询列表
	sendRecords, err := l.sendRecordRepo.List(ctx, baseParam, filter)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to list send records: %v", err)
	}

	// 如果列表为空，则返回空列表
	if len(sendRecords) == 0 {
		return &fulfillmentpb.ListSendReportRecordsResponse{
			SendRecords: []*fulfillmentpb.FulfillmentReportSendRecord{},
			Pagination:  req.GetPagination(),
			Total:       0,
		}, nil
	}

	// 转换为 proto 格式
	pbSendRecords := make([]*fulfillmentpb.FulfillmentReportSendRecord, 0, len(sendRecords))
	for _, record := range sendRecords {
		pbRecord, err := l.convertSendRecordRepoToPB(ctx, record)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "failed to convert send record: %v", err)
		}
		pbSendRecords = append(pbSendRecords, pbRecord)
	}

	response := &fulfillmentpb.ListSendReportRecordsResponse{
		SendRecords: pbSendRecords,
		Pagination:  req.GetPagination(),
		Total:       int32(total),
	}

	return response, nil
}

// verifyListSendReportRecordsRequest 验证列表发送记录请求
func (l *Logic) verifyListSendReportRecordsRequest(req *fulfillmentpb.ListSendReportRecordsRequest) error {
	if req == nil {
		return status.Errorf(codes.InvalidArgument, "request cannot be nil")
	}

	// 当没有指定以下过滤条件时，必须提供 company_id 和 business_id
	if req.GetFilter() == nil ||
		(len(req.GetFilter().GetReportIds()) == 0 &&
			len(req.GetFilter().GetAppointmentIds()) == 0 &&
			len(req.GetFilter().GetPetIds()) == 0) {
		if req.GetCompanyId() <= 0 {
			return status.Errorf(codes.InvalidArgument, "company_id must be greater than 0")
		}
		if req.GetBusinessId() <= 0 {
			return status.Errorf(codes.InvalidArgument, "business_id must be greater than 0")
		}
	}

	// 验证分页参数
	if req.GetPagination() != nil {
		if req.GetPagination().GetOffset() < 0 {
			return status.Errorf(codes.InvalidArgument, "pagination offset cannot be negative")
		}
		if req.GetPagination().GetLimit() < 0 {
			return status.Errorf(codes.InvalidArgument, "pagination limit cannot be negative")
		}
	}

	return nil
}

// buildSendRecordPaginationInfo 构建发送记录分页信息
func (l *Logic) buildSendRecordPaginationInfo(
	req *fulfillmentpb.ListSendReportRecordsRequest) *sendrecordrepo.PaginationInfo {
	if req.GetPagination() == nil {
		return &sendrecordrepo.PaginationInfo{
			Offset: defaultOffset,
			Limit:  defaultLimit,
		}
	}

	return &sendrecordrepo.PaginationInfo{
		Offset: req.GetPagination().GetOffset(),
		Limit:  req.GetPagination().GetLimit(),
	}
}

// buildSendRecordFilter 构建发送记录过滤条件
func (l *Logic) buildSendRecordFilter(filter *fulfillmentpb.ListSendReportRecordsFilter) *sendrecordrepo.Filter {
	repoFilter := &sendrecordrepo.Filter{}

	if filter != nil {
		// 处理预约ID过滤
		if len(filter.GetAppointmentIds()) > 0 {
			repoFilter.AppointmentIDs = filter.GetAppointmentIds()
		}
		// 处理宠物ID过滤
		if len(filter.GetPetIds()) > 0 {
			repoFilter.PetIDs = filter.GetPetIds()
		}
		// 处理护理类型过滤
		if len(filter.GetCareTypes()) > 0 {
			careTypes := make([]int32, 0, len(filter.GetCareTypes()))
			for _, careType := range filter.GetCareTypes() {
				careTypes = append(careTypes, int32(careType.Number()))
			}
			repoFilter.CareTypes = careTypes
		}
		// 处理发送方式过滤
		if filter.GetSendMethod() != fulfillmentpb.SendMethod_SEND_METHOD_UNSPECIFIED {
			repoFilter.SendMethods = []int32{int32(filter.GetSendMethod().Number())}
		}
	}

	return repoFilter
}

// convertSendRecordRepoToPB 将发送记录仓库实体转换为 Protobuf
func (l *Logic) convertSendRecordRepoToPB(ctx context.Context, record *sendrecordrepo.SendRecord) (
	*fulfillmentpb.FulfillmentReportSendRecord, error) {
	if record == nil {
		return nil, nil
	}
	// 查询report
	log.InfoContextf(ctx, "convertSendRecordRepoToPB report_id:%d", record.ReportID)
	report, err := l.reportRepo.FindByID(ctx, record.ReportID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}

		return nil, err
	}

	if report == nil {
		return nil, status.Errorf(codes.NotFound, "report not found, report_id:%d", record.ReportID)
	}

	// 解析内容JSON
	var content *fulfillmentpb.FulfillmentReportContent
	if record.ContentJSON != "" {
		if err := json.Unmarshal([]byte(record.ContentJSON), &content); err != nil {
			log.ErrorContextf(ctx, "failed to unmarshal content JSON: %v", err)
			// 如果解析失败，使用空内容
			content = &fulfillmentpb.FulfillmentReportContent{}
		}
	} else {
		content = &fulfillmentpb.FulfillmentReportContent{}
	}

	// 构建发送记录
	pbRecord := &fulfillmentpb.FulfillmentReportSendRecord{
		ReportId:      record.ReportID,
		AppointmentId: record.AppointmentID,
		PetId:         record.PetID,
		SendMethod:    fulfillmentpb.SendMethod(record.SendMethod),
		SendContent:   content,
		SendTime:      timestamppb.New(record.SentTime),
		IsSentSuccess: lo.FromPtr(record.IsSentSuccess),
		ErrorMessage:  record.ErrorMessage,
		ServiceDate:   report.ServiceDate,
		CareType:      offeringpb.CareCategory(report.CareType),
		ReportStatus:  fulfillmentpb.ReportStatus_REPORT_STATUS_UNSPECIFIED,
		Uuid:          report.UUID,
	}

	return pbRecord, nil
}

func (l *Logic) SendEmailMessage(
	ctx context.Context, summaryInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo,
	req *fulfillmentpb.SendFulfillmentReportRequest) (*fulfillmentpb.SendFulfillmentReportResponse, error) {
	// 根据 care type 调用不同的email 发送方式：grooming/daily
	careType := summaryInfo.FulfillmentReport.GetCareType()
	// TODO
	if careType == offeringpb.CareCategory_GROOMING {
		return nil, nil
	}
	response, err := l.sendDailyEmailMessage(ctx, summaryInfo, req)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func (l *Logic) sendDailyEmailMessage(ctx context.Context, summaryInfo *fulfillmentpb.FulfillmentReportCardSummaryInfo,
	req *fulfillmentpb.SendFulfillmentReportRequest) (*fulfillmentpb.SendFulfillmentReportResponse, error) {
	// 获取 report
	report := summaryInfo.GetFulfillmentReport()

	// 查询历史发送记录
	sendRecords, err := l.ListSendReportRecords(ctx, &fulfillmentpb.ListSendReportRecordsRequest{
		CompanyId:  lo.ToPtr(report.GetCompanyId()),
		BusinessId: lo.ToPtr(report.GetBusinessId()),
		Filter: &fulfillmentpb.ListSendReportRecordsFilter{
			ReportIds:  []int64{report.GetId()},
			SendMethod: lo.ToPtr(fulfillmentpb.SendMethod_EMAIL),
		},
	})
	if err != nil {
		return nil, err
	}

	// 初始化或获取发送记录
	var sendRecord *sendrecordrepo.SendRecord
	if len(sendRecords.GetSendRecords()) == 0 {
		// 创建新的发送记录
		sendRecord = &sendrecordrepo.SendRecord{
			ReportID:      report.GetId(),
			CompanyID:     report.GetCompanyId(),
			BusinessID:    report.GetBusinessId(),
			AppointmentID: report.GetAppointmentId(),
			PetID:         report.GetPetId(),
			SendMethod:    int32(fulfillmentpb.SendMethod_EMAIL),
			SentBy:        req.GetStaffId(),
		}
	} else {
		// 使用现有记录
		existingRecord := sendRecords.GetSendRecords()[0]
		sendRecord = &sendrecordrepo.SendRecord{
			ID:            existingRecord.ReportId,
			ReportID:      report.GetId(),
			CompanyID:     report.GetCompanyId(),
			BusinessID:    report.GetBusinessId(),
			AppointmentID: report.GetAppointmentId(),
			PetID:         report.GetPetId(),
			SendMethod:    int32(fulfillmentpb.SendMethod_EMAIL),
			SentBy:        req.GetStaffId(),
		}
	}

	// 设置发送时间和内容
	sendRecord.SentTime = time.Now()
	contentJSON, _ := json.Marshal(report.Content)
	sendRecord.ContentJSON = string(contentJSON)

	// 发送邮件
	sendResult := false
	var errorMessage string

	// 构建sendMessages
	sendMessages := &message.SendMessageByEmailParams{
		BusinessID:         report.GetBusinessId(),
		CompanyID:          report.GetCompanyId(),
		ID:                 report.GetId(),
		StaffID:            req.StaffId,
		RecipientEmailList: req.RecipientEmails,
	}

	// 调用发送服务
	result, err := l.messageRepo.SendDailyReportMessageByEmail(ctx, sendMessages)

	if err != nil {
		errorMessage = err.Error()
		sendResult = false
	} else {
		sendResult = result.SendSucceed
		errorMessage = result.ErrorMessage
	}

	// 更新发送记录
	sendRecord.IsSentSuccess = &sendResult
	sendRecord.ErrorMessage = errorMessage

	// 保存或更新发送记录
	if len(sendRecords.GetSendRecords()) == 0 {
		err = l.sendRecordRepo.Create(ctx, sendRecord)
	} else {
		err = l.sendRecordRepo.Update(ctx, sendRecord)
	}
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to save send record: %v", err)
	}

	// 更新报告状态为已发送
	if sendResult {
		report.Status = fulfillmentpb.ReportStatus_SENT
		_, err = l.UpdateFulfillmentReport(ctx, ConvertFulfillmentReportToReportLogic(report))
		if err != nil {
			log.ErrorContextf(ctx, "failed to update report status: %v", err)
		}
	}

	return &fulfillmentpb.SendFulfillmentReportResponse{
		SendResult: &fulfillmentpb.FulfillmentReportSendResult{
			FulfillmentReportId: report.GetId(),
			SendMethod:          fulfillmentpb.SendMethod_EMAIL,
			IsSentSuccess:       sendResult,
			ErrorMessage:        errorMessage,
		},
	}, nil
}

// CountFulfillmentReport 统计履约报告数量
func (l *Logic) CountFulfillmentReport(ctx context.Context, req *fulfillmentpb.CountFulfillmentReportRequest) (
	*fulfillmentpb.CountFulfillmentReportResponse, error) {
	log.InfoContextf(ctx, "CountFulfillmentReport req:%+v", req)

	// 校验请求
	if err := l.verifyCountFulfillmentReportRequest(req); err != nil {
		return nil, err
	}

	// 构建查询参数
	baseParam := &reportrepo.BaseParam{
		CompanyID:  req.GetCompanyId(),
		BusinessID: req.GetBusinessId(),
	}

	// 构建过滤条件
	filter := l.buildReportCountFilter(req.GetFilter())

	// 按状态统计数量
	statusCount, err := l.reportRepo.CountByStatus(ctx, baseParam, filter)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to count reports by status: %v", err)
	}

	return &fulfillmentpb.CountFulfillmentReportResponse{
		Total:      int32(statusCount.Total),
		DraftCount: int32(statusCount.DraftCount),
		SentCount:  int32(statusCount.SentCount),
	}, nil
}

// verifyCountFulfillmentReportRequest 验证统计履约报告请求
func (l *Logic) verifyCountFulfillmentReportRequest(req *fulfillmentpb.CountFulfillmentReportRequest) error {
	if req == nil {
		return status.Errorf(codes.InvalidArgument, "request cannot be nil")
	}

	if req.GetCompanyId() <= 0 {
		return status.Errorf(codes.InvalidArgument, "company_id must be greater than 0")
	}

	if req.GetBusinessId() <= 0 {
		return status.Errorf(codes.InvalidArgument, "business_id must be greater than 0")
	}

	return nil
}

// buildReportCountFilter 构建报告计数过滤条件
func (l *Logic) buildReportCountFilter(filter *fulfillmentpb.ListFulfillmentReportConfigFilter) *reportrepo.Filter {
	repoFilter := &reportrepo.Filter{}

	if filter != nil {
		// 处理状态过滤
		if filter.GetStatus() != fulfillmentpb.ReportStatus_REPORT_STATUS_UNSPECIFIED {
			statusStr := strings.ToLower(filter.GetStatus().String())
			repoFilter.Status = &statusStr
		}

		// 处理护理类型过滤
		if len(filter.GetCareTypes()) > 0 {
			careTypes := make([]int32, 0, len(filter.GetCareTypes()))
			for _, careType := range filter.GetCareTypes() {
				careTypes = append(careTypes, int32(careType.Number()))
			}
			repoFilter.CareTypes = careTypes
		}

		// 处理日期范围过滤
		if filter.GetStartDate() != "" {
			repoFilter.StartDate = lo.ToPtr(filter.GetStartDate())
		}
		if filter.GetEndDate() != "" {
			repoFilter.EndDate = lo.ToPtr(filter.GetEndDate())
		}

		// 处理宠物ID过滤
		if filter.GetPetId() > 0 {
			repoFilter.PetID = filter.GetPetId()
		}
	}

	return repoFilter
}

// ListFulfillmentReport 获取履约报告列表
func (l *Logic) ListFulfillmentReport(ctx context.Context, req *fulfillmentpb.ListFulfillmentReportRequest) (
	*fulfillmentpb.ListFulfillmentReportResponse, error) {
	log.InfoContextf(ctx, "ListFulfillmentReport req:%+v", req)

	// 校验请求
	if err := l.verifyListFulfillmentReportRequest(req); err != nil {
		return nil, err
	}

	// 构建查询参数
	baseParam := &reportrepo.BaseParam{
		CompanyID:  req.GetCompanyId(),
		BusinessID: req.GetBusinessId(),
	}

	// 设置分页信息
	baseParam.PaginationInfo = l.buildReportPaginationInfo(req)

	// 构建过滤条件
	filter := l.buildReportListFilter(req.GetFilter())

	// 查询总数
	total, err := l.reportRepo.Count(ctx, baseParam, filter)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to count reports: %v", err)
	}

	// 查询列表
	reports, err := l.reportRepo.List(ctx, baseParam, filter)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to list reports: %v", err)
	}

	// 如果列表为空，则返回空列表
	if len(reports) == 0 {
		return &fulfillmentpb.ListFulfillmentReportResponse{
			FulfillmentReportCards: []*fulfillmentpb.ListFulfillmentReportResponse_FulfillmentReportCard{},
			Pagination:             req.GetPagination(),
			Total:                  0,
		}, nil
	}

	// 转换为 proto 格式
	pbReportCards := make([]*fulfillmentpb.ListFulfillmentReportResponse_FulfillmentReportCard, 0, len(reports))
	for _, report := range reports {
		pbReportCard, err := l.convertReportToReportCard(ctx, report)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "failed to convert report to card: %v", err)
		}
		if pbReportCard != nil {
			pbReportCards = append(pbReportCards, pbReportCard)
		}
	}

	// 构建响应
	response := &fulfillmentpb.ListFulfillmentReportResponse{
		FulfillmentReportCards: pbReportCards,
		Pagination:             req.GetPagination(),
		Total:                  int32(total),
	}

	return response, nil
}

// verifyListFulfillmentReportRequest 验证列表履约报告请求
func (l *Logic) verifyListFulfillmentReportRequest(req *fulfillmentpb.ListFulfillmentReportRequest) error {
	if req == nil {
		return status.Errorf(codes.InvalidArgument, "request cannot be nil")
	}

	// 当没有指定以下过滤条件时，必须提供 company_id 和 business_id
	if req.GetFilter() == nil || req.GetFilter().GetPetId() == 0 {
		if req.GetCompanyId() <= 0 {
			return status.Errorf(codes.InvalidArgument, "company_id must be greater than 0")
		}
		if req.GetBusinessId() <= 0 {
			return status.Errorf(codes.InvalidArgument, "business_id must be greater than 0")
		}
	}

	// 验证分页参数
	if req.GetPagination() != nil {
		if req.GetPagination().GetOffset() < 0 {
			return status.Errorf(codes.InvalidArgument, "pagination offset cannot be negative")
		}
		if req.GetPagination().GetLimit() < 0 {
			return status.Errorf(codes.InvalidArgument, "pagination limit cannot be negative")
		}
	}

	return nil
}

// buildReportPaginationInfo 构建报告分页信息
func (l *Logic) buildReportPaginationInfo(req *fulfillmentpb.ListFulfillmentReportRequest) *reportrepo.PaginationInfo {
	if req.GetPagination() == nil {
		return &reportrepo.PaginationInfo{
			Offset: defaultOffset,
			Limit:  defaultLimit,
		}
	}

	return &reportrepo.PaginationInfo{
		Offset: req.GetPagination().GetOffset(),
		Limit:  req.GetPagination().GetLimit(),
	}
}

// buildReportListFilter 构建报告列表过滤条件
func (l *Logic) buildReportListFilter(filter *fulfillmentpb.ListFulfillmentReportConfigFilter) *reportrepo.Filter {
	repoFilter := &reportrepo.Filter{}

	if filter != nil {
		// 处理状态过滤
		if filter.GetStatus() != fulfillmentpb.ReportStatus_REPORT_STATUS_UNSPECIFIED {
			statusStr := strings.ToLower(filter.GetStatus().String())
			repoFilter.Status = &statusStr
		}

		// 处理护理类型过滤
		if len(filter.GetCareTypes()) > 0 {
			careTypes := make([]int32, 0, len(filter.GetCareTypes()))
			for _, careType := range filter.GetCareTypes() {
				careTypes = append(careTypes, int32(careType.Number()))
			}
			repoFilter.CareTypes = careTypes
		}

		// 处理日期范围过滤
		if filter.GetStartDate() != "" {
			startDate := filter.GetStartDate()
			repoFilter.StartDate = &startDate
		}
		if filter.GetEndDate() != "" {
			endDate := filter.GetEndDate()
			repoFilter.EndDate = &endDate
		}

		// 处理宠物ID过滤
		if filter.GetPetId() > 0 {
			repoFilter.PetID = filter.GetPetId()
		}
	}

	return repoFilter
}

// convertReportToReportCard 将报告仓库实体转换为报告卡片
func (l *Logic) convertReportToReportCard(ctx context.Context, report *reportrepo.Report) (
	*fulfillmentpb.ListFulfillmentReportResponse_FulfillmentReportCard, error) {
	if report == nil {
		return nil, nil
	}

	// 获取模板信息
	template, err := l.templateRepo.FindByUniqueKey(ctx, report.CompanyID, report.BusinessID, report.CareType)
	if err != nil {
		log.ErrorContextf(ctx, "failed to find template for report %d: %v", report.ID, err)
		// 如果找不到模板，使用默认标题
		template = &templaterepo.Template{Title: "Daily Report"}
	}

	// 获取发送记录
	sendRecords, err := l.getSendRecordsForReport(ctx, report.CompanyID, report.BusinessID, report.ID)
	if err != nil {
		log.ErrorContextf(ctx, "failed to get send records for report %d: %v", report.ID, err)
		// 如果获取失败，使用空列表
		sendRecords = []*fulfillmentpb.FulfillmentReportSendRecord{}
	}

	// 获取 pet 信息
	var petName string
	var avatarPath string
	var petType petpb.Pet_PetType
	if report.PetID == 0 {
		// 使用示例数据
		petName = SamplePet.GetPetName()
		avatarPath = SamplePet.GetAvatarPath()
		// 将 customerpb.PetType 转换为 petpb.Pet_PetType
		switch SamplePet.PetType {
		case customerpb.PetType_PET_TYPE_DOG:
			petType = petpb.Pet_DOG
		case customerpb.PetType_PET_TYPE_CAT:
			petType = petpb.Pet_CAT
		default:
			petType = petpb.Pet_PET_TYPE_UNSPECIFIED
		}
	} else {
		petInfo, err := l.petRepo.GetPetInfo(ctx, report.CompanyID, report.PetID)
		if err != nil {
			log.ErrorContextf(ctx, "failed to get pet info for report %d, pet_id %d: %v", report.ID, report.PetID, err)
			// 如果获取失败，使用默认值
			petName = "Unknown Pet"
			avatarPath = ""
			petType = petpb.Pet_PET_TYPE_UNSPECIFIED
		} else {
			petName = petInfo.GetPetName()
			avatarPath = petInfo.GetAvatarPath()
			// 将 customerpb.PetType 转换为 petpb.Pet_PetType
			switch petInfo.PetType {
			case customerpb.PetType_PET_TYPE_DOG:
				petType = petpb.Pet_DOG
			case customerpb.PetType_PET_TYPE_CAT:
				petType = petpb.Pet_CAT
			default:
				petType = petpb.Pet_PET_TYPE_UNSPECIFIED
			}
		}
	}

	// 计算媒体数量（照片+视频）
	mediaCount := int32(0)
	if report.ContentJSON != "" {
		var content fulfillmentpb.FulfillmentReportContent
		if err := json.Unmarshal([]byte(report.ContentJSON), &content); err == nil {
			mediaCount = int32(len(content.Photos) + len(content.Videos))
		}
	}

	// 构建报告卡片
	reportCard := &fulfillmentpb.ListFulfillmentReportResponse_FulfillmentReportCard{
		ReportId:      report.ID,
		CustomerId:    report.CustomerID,
		UpdateTime:    timestamppb.New(report.UpdateTime),
		CareType:      offeringpb.CareCategory(report.CareType),
		MediaCount:    mediaCount,
		ServiceDate:   report.ServiceDate,
		Uuid:          report.UUID,
		AppointmentId: report.AppointmentID,
		Title:         template.Title,
		SendRecord:    sendRecords,
		Pet: &fulfillmentpb.ListFulfillmentReportResponse_FulfillmentReportCard_Pet{
			PetId:      report.PetID,
			PetName:    petName,
			AvatarPath: avatarPath,
			PetType:    petType,
		},
	}

	return reportCard, nil
}

// getSendRecordsForReport 获取指定报告的发送记录
func (l *Logic) getSendRecordsForReport(ctx context.Context, companyID, businessID, reportID int64) (
	[]*fulfillmentpb.FulfillmentReportSendRecord, error) {

	// 构建查询参数
	baseParam := &sendrecordrepo.BaseParam{
		CompanyID:  companyID,
		BusinessID: businessID,
	}

	// 构建过滤条件，只查询指定报告的发送记录
	filter := &sendrecordrepo.Filter{
		ReportID: reportID,
	}

	// 查询发送记录列表
	sendRecords, err := l.sendRecordRepo.List(ctx, baseParam, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to list send records for report %d: %w", reportID, err)
	}

	// 如果没有发送记录，返回空列表
	if len(sendRecords) == 0 {
		return []*fulfillmentpb.FulfillmentReportSendRecord{}, nil
	}

	// 转换为 proto 格式
	pbSendRecords := make([]*fulfillmentpb.FulfillmentReportSendRecord, 0, len(sendRecords))
	for _, record := range sendRecords {
		pbRecord, err := l.convertSendRecordRepoToPB(ctx, record)
		if err != nil {
			log.ErrorContextf(ctx, "failed to convert send record %d: %v", record.ID, err)

			continue // 跳过转换失败的记录，继续处理其他记录
		}
		if pbRecord != nil {
			pbSendRecords = append(pbSendRecords, pbRecord)
		}
	}

	return pbSendRecords, nil
}

// IncreaseFulfillmentOpenedCount 增加履约报告打开次数
func (l *Logic) IncreaseFulfillmentOpenedCount(ctx context.Context,
	req *fulfillmentpb.IncreaseFulfillmentOpenedCountRequest) (
	*fulfillmentpb.IncreaseFulfillmentOpenedCountResponse, error) {

	// 参数验证
	if req == nil || req.GetUuid() == "" {
		return &fulfillmentpb.IncreaseFulfillmentOpenedCountResponse{}, nil
	}

	uuid := req.GetUuid()

	// share 模式暂时不统计 opened count
	const shareUUIDPrefix = "s_"
	if strings.HasPrefix(uuid, shareUUIDPrefix) {
		return &fulfillmentpb.IncreaseFulfillmentOpenedCountResponse{}, nil
	}

	// 增加打开次数
	err := l.reportRepo.IncreaseOpenedCount(ctx, uuid)
	if err != nil {
		log.ErrorContextf(ctx, "failed to increase opened count for uuid %s: %v", uuid, err)
		// 不返回错误，避免影响用户体验
		return &fulfillmentpb.IncreaseFulfillmentOpenedCountResponse{}, nil
	}

	return &fulfillmentpb.IncreaseFulfillmentOpenedCountResponse{}, nil
}

// BatchSendFulfillmentReport 批量发送履约报告
func (l *Logic) BatchSendFulfillmentReport(ctx context.Context, req *fulfillmentpb.BatchSendFulfillmentReportRequest) (
	*fulfillmentpb.BatchSendFulfillmentReportResponse, error) {
	log.InfoContextf(ctx, "BatchSendFulfillmentReport req:%+v", req)

	// 校验请求
	if err := l.verifyBatchSendFulfillmentReportRequest(req); err != nil {
		return nil, err
	}

	// 批量获取报告信息
	reports, errorMessages, err := l.batchGetReports(ctx, req.GetReportIds())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to batch get reports: %v", err)
	}

	// 并发发送报告
	sendResults := l.batchSendReports(ctx, reports, req)

	// 为找不到的报告添加失败结果
	for _, reportID := range req.GetReportIds() {
		if errorMsg, exists := errorMessages[reportID]; exists {
			failedResult := &fulfillmentpb.FulfillmentReportSendResult{
				FulfillmentReportId: reportID,
				SendMethod:          req.GetSendMethod(),
				IsSentSuccess:       false,
				ErrorMessage:        errorMsg,
			}
			sendResults = append(sendResults, failedResult)
		}
	}

	return &fulfillmentpb.BatchSendFulfillmentReportResponse{
		SendResults: sendResults,
	}, nil
}

// BatchDeleteFulfillmentReport 批量删除履约报告
func (l *Logic) BatchDeleteFulfillmentReport(ctx context.Context, req *fulfillmentpb.BatchDeleteFulfillmentReportRequest) (
	*fulfillmentpb.BatchDeleteFulfillmentReportResponse, error) {
	log.InfoContextf(ctx, "BatchDeleteFulfillmentReport req:%+v", req)

	// 校验请求
	if err := l.verifyBatchDeleteFulfillmentReportRequest(req); err != nil {
		return nil, err
	}

	// 批量删除报告
	err := l.reportRepo.BatchDelete(ctx, req.GetCompanyId(), req.GetBusinessId(), req.GetReportCardIds())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to batch delete reports: %v", err)
	}

	return &fulfillmentpb.BatchDeleteFulfillmentReportResponse{}, nil
}

// verifyBatchSendFulfillmentReportRequest 验证批量发送履约报告请求
func (l *Logic) verifyBatchSendFulfillmentReportRequest(req *fulfillmentpb.BatchSendFulfillmentReportRequest) error {
	if req == nil {
		return status.Errorf(codes.InvalidArgument, "request cannot be nil")
	}

	if req.GetCompanyId() <= 0 {
		return status.Errorf(codes.InvalidArgument, "company_id must be greater than 0")
	}

	if req.GetBusinessId() <= 0 {
		return status.Errorf(codes.InvalidArgument, "business_id must be greater than 0")
	}

	if len(req.GetReportIds()) == 0 {
		return status.Errorf(codes.InvalidArgument, "report_ids cannot be empty")
	}

	// 限制批量发送的数量，避免过载
	const maxBatchSize = 100
	if len(req.GetReportIds()) > maxBatchSize {
		return status.Errorf(codes.InvalidArgument, "report_ids count cannot exceed %d", maxBatchSize)
	}

	if req.GetSendMethod() == fulfillmentpb.SendMethod_SEND_METHOD_UNSPECIFIED {
		return status.Errorf(codes.InvalidArgument, "send_method is required")
	}

	if req.GetStaffId() <= 0 {
		return status.Errorf(codes.InvalidArgument, "staff_id must be greater than 0")
	}

	return nil
}

// batchGetReports 批量获取报告信息
func (l *Logic) batchGetReports(ctx context.Context, reportIDs []int64) (
	[]*reportrepo.Report, map[int64]string, error) {
	reports := make([]*reportrepo.Report, 0, len(reportIDs))
	errorMessages := make(map[int64]string) // 记录每个报告ID对应的错误信息

	for _, reportID := range reportIDs {
		report, err := l.reportRepo.FindByID(ctx, reportID)
		if err != nil {
			log.ErrorContextf(ctx, "failed to find report %d: %v", reportID, err)
			errorMessages[reportID] = fmt.Sprintf("failed to find report: %v", err)

			continue
		}
		if report == nil {
			errorMessages[reportID] = "report not found"

			continue
		}
		reports = append(reports, report)
	}

	return reports, errorMessages, nil
}

// batchSendReports 批量发送报告
func (l *Logic) batchSendReports(ctx context.Context,
	reports []*reportrepo.Report,
	req *fulfillmentpb.BatchSendFulfillmentReportRequest) []*fulfillmentpb.FulfillmentReportSendResult {
	sendResults := make([]*fulfillmentpb.FulfillmentReportSendResult, 0, len(reports))

	// 使用 goroutine 并发发送，提高效率
	type sendResult struct {
		reportID int64
		result   *fulfillmentpb.FulfillmentReportSendResult
	}

	resultChan := make(chan sendResult, len(reports))

	// 限制并发数量，避免过载
	const maxConcurrency = 10
	semaphore := make(chan struct{}, maxConcurrency)

	for _, report := range reports {
		go func(report *reportrepo.Report) {
			semaphore <- struct{}{}        // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			result := l.sendSingleReport(ctx, report, req)
			resultChan <- sendResult{
				reportID: report.ID,
				result:   result,
			}
		}(report)
	}

	// 收集结果
	resultMap := make(map[int64]*fulfillmentpb.FulfillmentReportSendResult)
	for i := 0; i < len(reports); i++ {
		result := <-resultChan
		resultMap[result.reportID] = result.result
	}

	// 按原始顺序返回结果
	for _, report := range reports {
		if result, exists := resultMap[report.ID]; exists {
			sendResults = append(sendResults, result)
		}
	}

	return sendResults
}

// sendSingleReport 发送单个报告
func (l *Logic) sendSingleReport(ctx context.Context, report *reportrepo.Report,
	req *fulfillmentpb.BatchSendFulfillmentReportRequest) *fulfillmentpb.FulfillmentReportSendResult {
	// 构建发送结果，默认为失败
	sendResult := &fulfillmentpb.FulfillmentReportSendResult{
		FulfillmentReportId: report.ID,
		SendMethod:          req.GetSendMethod(),
		IsSentSuccess:       false,
		ErrorMessage:        "",
	}

	// 获取完整的报告信息
	logicReport, err := l.GetFulfillmentReport(ctx, &GetFulfillmentReport{
		ID:            report.ID,
		CompanyID:     report.CompanyID,
		BusinessID:    report.BusinessID,
		AppointmentID: report.AppointmentID,
		PetID:         report.PetID,
		CareType:      offeringpb.CareCategory(report.CareType),
		ServiceDate:   report.ServiceDate,
		UUID:          report.UUID,
	})
	if err != nil {
		sendResult.ErrorMessage = fmt.Sprintf("failed to get report: %v", err)

		return sendResult
	}

	// 获取报告摘要信息
	reportSummaryInfo, err := l.GetReportSummaryInfo(ctx, logicReport)
	if err != nil {
		sendResult.ErrorMessage = fmt.Sprintf("failed to get report summary info: %v", err)

		return sendResult
	}

	// 构建单个发送请求
	sendReq := &fulfillmentpb.SendFulfillmentReportRequest{
		FulfillmentReportId: report.ID,
		SendMethod:          req.GetSendMethod(),
		StaffId:             req.GetStaffId(),
	}

	// 根据发送方法发送报告
	var sendResponse *fulfillmentpb.SendFulfillmentReportResponse
	switch req.GetSendMethod() {
	case fulfillmentpb.SendMethod_EMAIL:
		sendResponse, err = l.SendEmailMessage(ctx, reportSummaryInfo, sendReq)
	case fulfillmentpb.SendMethod_SMS:
		sendResponse, err = l.SendSmsMessage(ctx, reportSummaryInfo, sendReq)
	default:
		sendResult.ErrorMessage = "unsupported send method"

		return sendResult
	}

	if err != nil {
		sendResult.ErrorMessage = err.Error()

		return sendResult
	}

	// 更新发送结果
	if sendResponse != nil && sendResponse.SendResult != nil {
		sendResult.IsSentSuccess = sendResponse.SendResult.IsSentSuccess
		sendResult.ErrorMessage = sendResponse.SendResult.ErrorMessage
	} else {
		sendResult.IsSentSuccess = true // 如果没有错误，认为发送成功
	}

	return sendResult
}
