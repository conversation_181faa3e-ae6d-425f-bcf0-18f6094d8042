package report

import (
	"google.golang.org/genproto/googleapis/type/calendarperiod"

	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	customerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	utils "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// nolint: lll
const (
	QuestionKeyAdditionalNote = "additional_note"
	QuestionKeyMood           = "mood"

	SampleCustomerFrequency = 28
	SampleComment           = "You can tell from the wagging tail!!!"

	SamplePetName       = "Demo"
	SamplePetBreed      = "Demo breed"
	SamplePetWeight     = "15"
	SamplePetAvatar     = "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/16863058128431eee721074570b411ca6ef22bcc45.png?name=pet-avatar.png"
	SampleBodyViewLeft  = "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/16842917382cc63b95f7784b4fb4d031c2698f4981.png?name=grooming-report-dog-leg.png"
	SampleBodyViewRight = "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/1684827394ca8c70eed6e748eb83f8e3829df937e8.png?name=grooming-report-dog.png"

	SamplePhotoBefore = "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/169044845699449bae8ffb43a2a0989095ca3ba68f.jpg?name=grooming-report-default-img-before.jpg"
	SamplePhotoAfter  = "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/16904484632e511c9269244c9b8dc62c4fad3dc735.jpg?name=grooming-report-default-img-after.jpg"
)

var DailyReportCareTypeList = []offeringpb.CareCategory{
	offeringpb.CareCategory_DAYCARE,
	offeringpb.CareCategory_BOARDING,
}

var SampleCustomer = &businesscustomerpb.BusinessCustomerModel{
	Id: 0,
	PreferredGroomingFrequency: &utils.TimePeriod{
		Value:  SampleCustomerFrequency,
		Period: calendarperiod.CalendarPeriod_WEEK,
	},
}

var SamplePet = &businesscustomerpb.BusinessCustomerPetInfoModel{
	Id:         0,
	PetType:    customerpb.PetType_PET_TYPE_DOG,
	PetName:    SamplePetName,
	AvatarPath: SamplePetAvatar,
	Breed:      SamplePetBreed,
	Gender:     customerpb.PetGender_PET_GENDER_MALE,
	Weight:     SamplePetWeight,
}
