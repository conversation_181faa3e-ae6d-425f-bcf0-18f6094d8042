package fulfillment

import (
	"context"
	"errors"
	"testing"
	"time"

	"go.uber.org/mock/gomock"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gotest.tools/v3/assert"

	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/fulfillment"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/fulfillment/mock"
	pb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func TestListFulfillment_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockReadWriter(ctrl)
	logic := &Logic{
		fulfillmentCli: mockRepo,
	}

	req := &pb.ListFulfillmentRequest{
		CompanyId:  123,
		BusinessId: 456,
		StartTime:  timestamppb.New(time.Now().Add(-24 * time.Hour)),
		EndTime:    timestamppb.New(time.Now()),
		Filter: &pb.FulfillmentFilter{
			PetIds:    []int64{789},
			States:    []int32{int32(pb.State_STATE_NOT_START)},
			CareTypes: []offeringpb.CareCategory{offeringpb.CareCategory_GROOMING},
		},
		Pagination: &pb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	fulfillments := []*fulfillment.Fulfillment{
		{
			ID:                1,
			BusinessID:        456,
			CustomerID:        100,
			PetID:             789,
			ServiceInstanceID: 11,
			ServiceFactoryID:  22,
			CareType:          int32(offeringpb.CareCategory_GROOMING),
			LodgingID:         33,
			PlaygroupID:       44,
			State:             int32(pb.State_STATE_NOT_START),
			StaffID:           555,
			StartTime:         time.Now().Add(-12 * time.Hour),
			EndTime:           time.Now().Add(-10 * time.Hour),
		},
	}

	mockRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(fulfillments, nil)
	mockRepo.EXPECT().Count(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(1), nil)

	resp, err := logic.ListFulfillment(context.Background(), req)
	assert.NilError(t, err)
	assert.Assert(t, resp != nil)
	assert.Equal(t, len(resp.Fulfillments), 1)
	f := resp.Fulfillments[0]
	assert.Equal(t, f.Id, int64(1))
	assert.Equal(t, f.BusinessId, int64(456))
	assert.Equal(t, f.CustomerId, int64(100))
	assert.Equal(t, f.PetId, int64(789))
	assert.Equal(t, f.ServiceInstanceId, int64(11))
	assert.Equal(t, f.ServiceFactoryId, int64(22))
	assert.Equal(t, f.CareType, offeringpb.CareCategory_GROOMING)
	assert.Equal(t, f.LodgingId, int64(33))
	assert.Equal(t, f.PlaygroupId, int64(44))
	assert.Equal(t, f.State, pb.State_STATE_NOT_START)
	assert.DeepEqual(t, f.StaffIds, []int64{555})
	assert.Equal(t, resp.Total, int32(1))
	assert.Equal(t, resp.IsEnd, true)
}

func TestListFulfillment_EmptyResult(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockReadWriter(ctrl)
	logic := &Logic{
		fulfillmentCli: mockRepo,
	}

	req := &pb.ListFulfillmentRequest{
		CompanyId:  123,
		BusinessId: 456,
		StartTime:  timestamppb.New(time.Now().Add(-24 * time.Hour)),
		EndTime:    timestamppb.New(time.Now()),
		Pagination: &pb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	mockRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*fulfillment.Fulfillment{}, nil)
	mockRepo.EXPECT().Count(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(0), nil)

	resp, err := logic.ListFulfillment(context.Background(), req)
	assert.NilError(t, err)
	assert.Assert(t, resp != nil)
	assert.Equal(t, len(resp.Fulfillments), 0)
	assert.Equal(t, resp.Total, int32(0))
	assert.Equal(t, resp.IsEnd, true)
}

func TestListFulfillment_MultipleResults(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockReadWriter(ctrl)
	logic := &Logic{
		fulfillmentCli: mockRepo,
	}

	req := &pb.ListFulfillmentRequest{
		CompanyId:  123,
		BusinessId: 456,
		StartTime:  timestamppb.New(time.Now().Add(-24 * time.Hour)),
		EndTime:    timestamppb.New(time.Now()),
		Pagination: &pb.PaginationRef{
			Offset: 0,
			Limit:  5,
		},
	}

	fulfillments := []*fulfillment.Fulfillment{
		{
			ID:                1,
			BusinessID:        456,
			CustomerID:        100,
			PetID:             789,
			ServiceInstanceID: 11,
			ServiceFactoryID:  22,
			CareType:          int32(offeringpb.CareCategory_GROOMING),
			State:             int32(pb.State_STATE_NOT_START),
			StaffID:           555,
			StartTime:         time.Now().Add(-12 * time.Hour),
			EndTime:           time.Now().Add(-10 * time.Hour),
		},
		{
			ID:                2,
			BusinessID:        456,
			CustomerID:        101,
			PetID:             790,
			ServiceInstanceID: 12,
			ServiceFactoryID:  23,
			CareType:          int32(offeringpb.CareCategory_BOARDING),
			State:             int32(pb.State_STATE_NOT_START),
			StaffID:           556,
			StartTime:         time.Now().Add(-6 * time.Hour),
			EndTime:           time.Now().Add(-4 * time.Hour),
		},
	}

	mockRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(fulfillments, nil)
	mockRepo.EXPECT().Count(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(2), nil)

	resp, err := logic.ListFulfillment(context.Background(), req)
	assert.NilError(t, err)
	assert.Assert(t, resp != nil)
	assert.Equal(t, len(resp.Fulfillments), 2)
	assert.Equal(t, resp.Total, int32(2))
	assert.Equal(t, resp.IsEnd, true) // 因为返回数量等于limit，所以是最后一页
}

func TestListFulfillment_NotEndPage(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockReadWriter(ctrl)
	logic := &Logic{
		fulfillmentCli: mockRepo,
	}

	req := &pb.ListFulfillmentRequest{
		CompanyId:  123,
		BusinessId: 456,
		StartTime:  timestamppb.New(time.Now().Add(-24 * time.Hour)),
		EndTime:    timestamppb.New(time.Now()),
		Pagination: &pb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	fulfillments := []*fulfillment.Fulfillment{
		{
			ID:                1,
			BusinessID:        456,
			CustomerID:        100,
			PetID:             789,
			ServiceInstanceID: 11,
			ServiceFactoryID:  22,
			CareType:          int32(offeringpb.CareCategory_GROOMING),
			State:             int32(pb.State_STATE_NOT_START),
			StaffID:           555,
			StartTime:         time.Now().Add(-12 * time.Hour),
			EndTime:           time.Now().Add(-10 * time.Hour),
		},
	}

	mockRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(fulfillments, nil)
	mockRepo.EXPECT().Count(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(15), nil) // 总数大于返回数量

	resp, err := logic.ListFulfillment(context.Background(), req)
	assert.NilError(t, err)
	assert.Assert(t, resp != nil)
	assert.Equal(t, len(resp.Fulfillments), 1)
	assert.Equal(t, resp.Total, int32(15))
	assert.Equal(t, resp.IsEnd, true) // 因为返回数量小于limit，所以是最后一页
}

func TestListFulfillment_InvalidTimeRange(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockReadWriter(ctrl)
	logic := &Logic{
		fulfillmentCli: mockRepo,
	}

	req := &pb.ListFulfillmentRequest{
		StartTime: timestamppb.New(time.Now()),
		EndTime:   timestamppb.New(time.Now().Add(-24 * time.Hour)), // 开始时间晚于结束时间
	}

	resp, err := logic.ListFulfillment(context.Background(), req)
	assert.Assert(t, err != nil)
	assert.Assert(t, resp == nil)
}

func TestListFulfillment_DefaultLimit(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockReadWriter(ctrl)
	logic := &Logic{
		fulfillmentCli: mockRepo,
	}

	req := &pb.ListFulfillmentRequest{
		CompanyId:  123,
		BusinessId: 456,
		StartTime:  timestamppb.New(time.Now().Add(-24 * time.Hour)),
		EndTime:    timestamppb.New(time.Now()),
		Pagination: &pb.PaginationRef{
			Offset: 0,
			Limit:  0, // 不设置 limit，应该使用默认值
		},
	}

	mockRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*fulfillment.Fulfillment{}, nil)
	mockRepo.EXPECT().Count(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(0), nil)

	resp, err := logic.ListFulfillment(context.Background(), req)
	assert.NilError(t, err)
	assert.Assert(t, resp != nil)
	assert.Equal(t, resp.Total, int32(0))
	assert.Equal(t, resp.IsEnd, true)
}

func TestListFulfillment_NoPagination(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockReadWriter(ctrl)
	logic := &Logic{
		fulfillmentCli: mockRepo,
	}

	req := &pb.ListFulfillmentRequest{
		CompanyId:  123,
		BusinessId: 456,
		StartTime:  timestamppb.New(time.Now().Add(-24 * time.Hour)),
		EndTime:    timestamppb.New(time.Now()),
		// 不设置 Pagination
	}

	mockRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*fulfillment.Fulfillment{}, nil)
	mockRepo.EXPECT().Count(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(0), nil)

	resp, err := logic.ListFulfillment(context.Background(), req)
	assert.NilError(t, err)
	assert.Assert(t, resp != nil)
	assert.Equal(t, resp.Total, int32(0))
	assert.Equal(t, resp.IsEnd, true)
}

func TestListFulfillment_RepoListError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockReadWriter(ctrl)
	logic := &Logic{
		fulfillmentCli: mockRepo,
	}

	req := &pb.ListFulfillmentRequest{
		CompanyId:  123,
		BusinessId: 456,
		StartTime:  timestamppb.New(time.Now().Add(-24 * time.Hour)),
		EndTime:    timestamppb.New(time.Now()),
		Pagination: &pb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	mockRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("mock error"))

	resp, err := logic.ListFulfillment(context.Background(), req)
	assert.Assert(t, err != nil)
	assert.Assert(t, resp == nil)
}

func TestListFulfillment_RepoCountError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockReadWriter(ctrl)
	logic := &Logic{
		fulfillmentCli: mockRepo,
	}

	req := &pb.ListFulfillmentRequest{
		CompanyId:  123,
		BusinessId: 456,
		StartTime:  timestamppb.New(time.Now().Add(-24 * time.Hour)),
		EndTime:    timestamppb.New(time.Now()),
		Pagination: &pb.PaginationRef{
			Offset: 0,
			Limit:  10,
		},
	}

	mockRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*fulfillment.Fulfillment{}, nil)
	mockRepo.EXPECT().Count(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(0), errors.New("mock error"))

	resp, err := logic.ListFulfillment(context.Background(), req)
	assert.Assert(t, err != nil)
	assert.Assert(t, resp == nil)
}

func TestBuildFilter_Complete(t *testing.T) {
	req := &pb.ListFulfillmentRequest{
		Filter: &pb.FulfillmentFilter{
			PetIds:      []int64{123, 456},
			StaffIds:    []int64{789, 101},
			States:      []int32{1, 2, 3},
			CustomerIds: []int64{999, 888},
			CareTypes:   []offeringpb.CareCategory{offeringpb.CareCategory_BOARDING, offeringpb.CareCategory_GROOMING},
		},
	}

	filter := buildFilter(req)
	assert.DeepEqual(t, filter.PetIDs, []int64{123, 456})
	assert.Equal(t, filter.StaffID, int64(789)) // 只取第一个staffId
	assert.DeepEqual(t, filter.States, []int32{1, 2, 3})
	assert.DeepEqual(t, filter.CustomerIDs, []int64{999, 888})
	assert.DeepEqual(t, filter.CareTypes, []int32{2, 1}) // CARE_TYPE_BOARDING=2, CARE_TYPE_GROOMING=1
}

func TestBuildFilter_Empty(t *testing.T) {
	req := &pb.ListFulfillmentRequest{
		Filter: &pb.FulfillmentFilter{},
	}

	filter := buildFilter(req)
	assert.DeepEqual(t, filter.PetIDs, []int64{})
	assert.Equal(t, filter.StaffID, int64(0))
	assert.DeepEqual(t, filter.States, []int32{})
	assert.DeepEqual(t, filter.CustomerIDs, []int64{})
	assert.DeepEqual(t, filter.CareTypes, []int32{})
}

func TestBuildFilter_NilFilter(t *testing.T) {
	req := &pb.ListFulfillmentRequest{
		// 不设置 Filter
	}

	filter := buildFilter(req)
	assert.DeepEqual(t, filter.PetIDs, []int64{})
	assert.Equal(t, filter.StaffID, int64(0))
	assert.DeepEqual(t, filter.States, []int32{})
	assert.DeepEqual(t, filter.CustomerIDs, []int64{})
	assert.DeepEqual(t, filter.CareTypes, []int32{})
}

func TestChangeCareType(t *testing.T) {
	careTypes := []offeringpb.CareCategory{
		offeringpb.CareCategory_CARE_CATEGORY_UNSPECIFIED,
		offeringpb.CareCategory_GROOMING,
		offeringpb.CareCategory_BOARDING,
		offeringpb.CareCategory_DAYCARE,
		offeringpb.CareCategory_EVALUATION,
		offeringpb.CareCategory_DOG_WALKING,
		offeringpb.CareCategory_GROUP_CLASS,
	}

	result := changeCareType(careTypes)
	expected := []int32{0, 1, 2, 3, 4, 5, 6}
	assert.DeepEqual(t, result, expected)
}

func TestChangeCareType_Empty(t *testing.T) {
	careTypes := []offeringpb.CareCategory{}
	result := changeCareType(careTypes)
	assert.DeepEqual(t, result, []int32{})
}

func TestVerifyGetFulfillmentRequest_Valid(t *testing.T) {
	validReq := &pb.ListFulfillmentRequest{
		StartTime: timestamppb.New(time.Now().Add(-24 * time.Hour)),
		EndTime:   timestamppb.New(time.Now()),
	}
	err := verifyGetFulfillmentRequest(validReq)
	assert.NilError(t, err)
}

func TestVerifyGetFulfillmentRequest_InvalidTimeRange(t *testing.T) {
	invalidReq := &pb.ListFulfillmentRequest{
		StartTime: timestamppb.New(time.Now()),
		EndTime:   timestamppb.New(time.Now().Add(-24 * time.Hour)), // 开始时间晚于结束时间
	}
	err := verifyGetFulfillmentRequest(invalidReq)
	assert.Assert(t, err != nil)
}

func TestVerifyGetFulfillmentRequest_SameTime(t *testing.T) {
	sameTime := time.Now()
	req := &pb.ListFulfillmentRequest{
		StartTime: timestamppb.New(sameTime),
		EndTime:   timestamppb.New(sameTime), // 开始时间等于结束时间
	}
	err := verifyGetFulfillmentRequest(req)
	assert.NilError(t, err) // 相同时间应该是有效的
}

func TestNew(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockReadWriter(ctrl)
	logic := NewByParams(mockRepo)
	assert.Assert(t, logic != nil)
	assert.Assert(t, logic.fulfillmentCli != nil)
}
