package appointment

import (
	"context"
	"time"

	"github.com/gogo/protobuf/proto"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/customer"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/appointment"
	feedingRepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/feeding"
	fulfillmentRepo "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/fulfillment"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/medication"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/serviceinstance"
	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/offering"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	pb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

const (
	updateMessage = "更新成功"
	defaultOffset = 0
	defaultLimit  = 200
)

func New() *Logic {
	tx := db.NewTxManager()

	return &Logic{
		appointmentCli:      appointment.New(),
		fulfillmentCli:      fulfillmentRepo.New(),
		serviceInstanceCli:  serviceinstance.New(),
		offeringServiceCli:  offering.New(),
		offeringCareTypeCli: offering.NewCareTypeReader(),
		customerCli:         customer.New(),
		feedingCli:          feedingRepo.New(),
		medicationCli:       medication.New(),
		tx:                  tx,
	}
}

func NewByParams(
	appointmentCli appointment.ReadWriter,
	fulfillmentCli fulfillmentRepo.ReadWriter,
	serviceInstanceCli serviceinstance.ReadWriter,
	offeringServiceCli offering.ReadWriter,
	offeringCareTypeCli offering.CareTypeReader,
	customerCli customer.ReadWriter,
	feedingCli feedingRepo.ReadWriter,
	medicationCli medication.ReadWriter,
	tx db.TransactionManager,
) *Logic {
	return &Logic{
		appointmentCli:      appointmentCli,
		fulfillmentCli:      fulfillmentCli,
		serviceInstanceCli:  serviceInstanceCli,
		offeringServiceCli:  offeringServiceCli,
		offeringCareTypeCli: offeringCareTypeCli,
		customerCli:         customerCli,
		feedingCli:          feedingCli,
		medicationCli:       medicationCli,
		tx:                  tx,
	}
}

type Logic struct {
	appointmentCli      appointment.ReadWriter
	fulfillmentCli      fulfillmentRepo.ReadWriter
	serviceInstanceCli  serviceinstance.ReadWriter
	offeringServiceCli  offering.ReadWriter
	offeringCareTypeCli offering.CareTypeReader
	customerCli         customer.ReadWriter
	feedingCli          feedingRepo.ReadWriter
	medicationCli       medication.ReadWriter
	tx                  db.TransactionManager
}

func (l *Logic) CreateAppointment(ctx context.Context, req *pb.CreateAppointmentRequest) (
	*pb.CreateAppointmentResponse, error) {
	// 校验请求
	if err := verifyCreateAppointmentRequest(req); err != nil {
		return nil, err
	}
	// 获取所有serviceTemplateID并调用第三方服务获取careType信息
	serviceTemplateIDs := l.getAllServiceIDs(req.GetPets())
	careTypeMap, err := l.getCareTypesByServiceIDs(ctx, serviceTemplateIDs)
	if err != nil {
		return nil, err
	}
	// 计算ServiceItemType（所有careType去重后相加的值）
	serviceItemType := l.calculateServiceItemType(careTypeMap)
	// 组包
	appointmentEntity := &appointment.Appointment{
		BusinessID:      int(req.GetBusinessId()),
		CustomerID:      int(req.GetCustomerId()),
		CompanyID:       int(req.GetCompanyId()),
		Status:          int(pb.AppointmentState_APPOINTMENT_STATE_UNCONFIRMED),
		ServiceItemType: int(serviceItemType),
		StartTime:       req.GetStartTime().AsTime(),
		EndTime:         req.GetEndTime().AsTime(),
		ColorCode:       req.GetColorCode(),
	}
	appointmentID, serviceInstances, err := l.doCreateAppointment(ctx, req,
		appointmentEntity, careTypeMap)
	if err != nil {
		return nil, err
	}

	// 同步宠物档案的用药和喂养计划
	if err := l.syncPetDetailDef(ctx, int(req.GetCompanyId()), req.GetPets()); err != nil {
		log.Errorf("Failed to sync pet detail def: %v", err)
		// 注意：这里不返回错误，因为同步失败不应该影响预约创建.
	}

	// 在事务外保存宠物的喂养和用药计划
	if err := l.savePetScheduleData(ctx, serviceInstances); err != nil {
		log.Errorf("Failed to save pet schedule data: %v", err)
		// 注意：这里不返回错误，因为保存失败不应该影响预约创建.
	}

	return &pb.CreateAppointmentResponse{
		Id: int64(appointmentID),
	}, nil
}

func (l *Logic) doCreateAppointment(ctx context.Context, req *pb.CreateAppointmentRequest,
	appointmentEntity *appointment.Appointment,
	careTypeMap map[int64]offeringpb.CareCategory) (int, []*ServiceInstanceData, error) {
	var appointmentID int
	var serviceInstances []*ServiceInstanceData

	// 使用事务处理
	err := l.tx.ExecuteInTransaction(ctx, []func(opCtx context.Context, tx *gorm.DB) error{
		func(opCtx context.Context, _ *gorm.DB) error {
			instances, err := l.createAppointmentInTransaction(opCtx, req,
				appointmentEntity, careTypeMap, &appointmentID)
			if err != nil {
				return err
			}
			serviceInstances = instances

			return nil
		},
	})
	if err != nil {
		return 0, nil, err
	}

	return appointmentID, serviceInstances, nil
}

type ServiceInstanceData struct {
	ServiceInstanceID        int64
	CreateServiceInstanceDef *pb.CreateServiceInstanceDef
	ServiceInstance          *serviceinstance.ServiceInstance
}

// createAppointmentInTransaction 在事务中创建预约
func (l *Logic) createAppointmentInTransaction(ctx context.Context,
	req *pb.CreateAppointmentRequest,
	appointmentEntity *appointment.Appointment,
	careTypeMap map[int64]offeringpb.CareCategory,
	appointmentID *int) ([]*ServiceInstanceData, error) {
	// 创建 appointment
	if err := l.appointmentCli.Create(ctx, appointmentEntity); err != nil {
		return nil, err
	}
	*appointmentID = appointmentEntity.ID

	serviceInstances, err := l.createServiceInstances(ctx, req, *appointmentID, careTypeMap)
	if err != nil {
		return nil, err
	}

	if err := l.createFulfillments(ctx, serviceInstances); err != nil {
		return nil, err
	}

	return serviceInstances, nil
}

// PetScheduleData 收集的宠物计划数据
type PetScheduleData struct {
	FeedingRecords    []*feedingRepo.AppointmentPetFeeding
	MedicationRecords []*medication.AppointmentPetMedication
}

// createServiceInstances 创建服务实例并收集喂养和用药数据
func (l *Logic) createServiceInstances(ctx context.Context, req *pb.CreateAppointmentRequest,
	appointmentID int, careTypeMap map[int64]offeringpb.CareCategory) ([]*ServiceInstanceData, error) {
	var allServiceInstances []*ServiceInstanceData

	for _, petDetail := range req.GetPets() {
		for _, serviceInstance := range petDetail.GetServices() {
			// 递归创建服务实例，传入0作为parentID（顶层服务实例）
			instances, err := l.createServiceInstanceRecursively(ctx, req, appointmentID,
				int(petDetail.GetPetId()), serviceInstance, careTypeMap, 0, 0)
			if err != nil {
				return nil, err
			}
			allServiceInstances = append(allServiceInstances, instances...)
		}
	}

	return allServiceInstances, nil
}

// createServiceInstanceRecursively 递归创建服务实例和子服务实例，正确设置父子关系
func (l *Logic) createServiceInstanceRecursively(ctx context.Context, req *pb.CreateAppointmentRequest,
	appointmentID int, petID int, serviceInstance *pb.CreateServiceInstanceDef,
	careTypeMap map[int64]offeringpb.CareCategory, parentID int,
	rootParentID int) ([]*ServiceInstanceData, error) {

	var allInstances = make([]*ServiceInstanceData, 0)

	// 创建当前服务实例
	careType := careTypeMap[serviceInstance.GetId()]
	si := &serviceinstance.ServiceInstance{
		BusinessID:       int(req.GetBusinessId()),
		CustomerID:       int(req.GetCustomerId()),
		CompanyID:        int(req.GetCompanyId()),
		AppointmentID:    appointmentID,
		PetID:            petID,
		CareType:         int(careType),
		DateType:         0, // TODO: Get date_type from date_schedule_config if needed
		ServiceFactoryID: int(serviceInstance.GetId()),
		StartDate:        time.Now(), // TODO: Get start_time from time_config if available
		EndDate:          time.Now(), // TODO: Get end_time from time_config if available
		ParentID:         parentID,
		RootParentID:     rootParentID,
	}

	// 创建当前服务实例到数据库，获取生成的ID
	currentServiceInstanceID, err := l.serviceInstanceCli.Create(ctx, si)
	if err != nil {
		return nil, err
	}

	// 更新服务实例的ID
	si.ID = currentServiceInstanceID
	allInstances = append(allInstances, &ServiceInstanceData{
		ServiceInstanceID:        int64(currentServiceInstanceID),
		CreateServiceInstanceDef: serviceInstance,
		ServiceInstance:          si,
	})

	// 确定根父实例ID
	currentRootID := rootParentID
	if currentRootID == 0 {
		// 如果这是顶层服务实例，它本身就是根父实例
		currentRootID = currentServiceInstanceID
	}

	// 递归处理子服务实例
	for _, subServiceInstance := range serviceInstance.GetSubServiceInstances() {
		subInstances, err := l.createServiceInstanceRecursively(ctx, req, appointmentID,
			petID, subServiceInstance, careTypeMap, currentServiceInstanceID, currentRootID)
		if err != nil {
			return nil, err
		}
		allInstances = append(allInstances, subInstances...)
	}

	return allInstances, nil
}

// createFulfillments 创建履约记录
func (l *Logic) createFulfillments(ctx context.Context, serviceInstances []*ServiceInstanceData) error {
	var allFulfillments []*fulfillmentRepo.Fulfillment

	for _, si := range serviceInstances {
		item := si.ServiceInstance
		careType := offeringpb.CareCategory(item.CareType)
		fulfillments := l.generateFulfillmentRecords(item, careType)
		allFulfillments = append(allFulfillments, fulfillments...)
	}

	if len(allFulfillments) > 0 {
		if err := l.fulfillmentCli.BatchCreate(ctx, allFulfillments); err != nil {
			return err
		}
	}

	return nil
}

// getAllServiceIDs 获取pets下所有serviceTemplateID（递归处理SubServiceInstance）
func (l *Logic) getAllServiceIDs(pets []*pb.CreatePetDetailDef) []int64 {
	var serviceTemplateIDs []int64
	result := make(map[int64]bool) // 用于去重

	for _, petDetail := range pets {
		for _, serviceInstance := range petDetail.GetServices() {
			l.collectServiceTemplateIDsRecursively(serviceInstance, result)
		}
	}

	// 将map中的key转换为slice
	for templateID := range result {
		serviceTemplateIDs = append(serviceTemplateIDs, templateID)
	}

	// 确保返回空slice而不是nil
	if len(serviceTemplateIDs) == 0 {
		return []int64{}
	}

	return serviceTemplateIDs
}

// collectServiceTemplateIDsRecursively 递归收集所有serviceTemplateID
func (l *Logic) collectServiceTemplateIDsRecursively(serviceInstance *pb.CreateServiceInstanceDef,
	result map[int64]bool) {
	// 添加当前服务实例的templateID
	templateID := serviceInstance.GetId()
	if templateID > 0 {
		result[templateID] = true
	}

	// 递归处理子服务实例
	for _, subServiceInstance := range serviceInstance.GetSubServiceInstances() {
		l.collectServiceTemplateIDsRecursively(subServiceInstance, result)
	}
}

func (l *Logic) getCareTypesByServiceIDs(ctx context.Context,
	serviceTemplateIDs []int64) (map[int64]offeringpb.CareCategory, error) {
	result := make(map[int64]offeringpb.CareCategory)
	for _, templateID := range serviceTemplateIDs {
		// 调用offering服务获取service信息
		service, err := l.offeringServiceCli.GetService(ctx, templateID)
		if err != nil {
			log.Errorf("Failed to get service for templateID %d: %v", templateID, err)
			// 如果获取失败，使用默认值
			result[templateID] = offeringpb.CareCategory_BOARDING

			continue
		}
		// 从service中获取careTypeID
		careTypeID := service.GetCareTypeId()
		if careTypeID == 0 {
			// 如果没有careTypeID，使用默认值
			result[templateID] = offeringpb.CareCategory_CARE_CATEGORY_UNSPECIFIED

			continue
		}
		// 调用offering服务获取careType信息
		careType, err := l.offeringCareTypeCli.GetCareType(ctx, careTypeID)
		if err != nil {
			log.Errorf("Failed to get careType for careTypeID %d: %v", careTypeID, err)
			// 如果获取失败，使用默认值
			result[templateID] = offeringpb.CareCategory_BOARDING

			continue
		}
		// 直接使用offering的CareCategory
		result[templateID] = careType.GetCareCategory()
	}

	return result, nil
}

func (l *Logic) calculateServiceItemType(careTypeMap map[int64]offeringpb.CareCategory) int32 {
	// 使用map来去重careType
	uniqueCareTypes := make(map[offeringpb.CareCategory]bool)
	// 收集所有唯一的careType
	for _, careType := range careTypeMap {
		uniqueCareTypes[careType] = true
	}
	var serviceItemType int32
	for careType := range uniqueCareTypes {
		serviceItemType += int32(careType)
	}

	return serviceItemType
}

// generateFulfillmentRecords 根据 CareCategory 生成履约记录（不直接创建，只返回记录列表）
func (l *Logic) generateFulfillmentRecords(si *serviceinstance.ServiceInstance,
	careType offeringpb.CareCategory) []*fulfillmentRepo.Fulfillment {
	switch careType {
	case offeringpb.CareCategory_BOARDING:
		// Boarding 类型按天维度进行履约
		return l.generateDailyFulfillmentRecords(si)
	case offeringpb.CareCategory_GROOMING, offeringpb.CareCategory_DAYCARE,
		offeringpb.CareCategory_EVALUATION, offeringpb.CareCategory_DOG_WALKING,
		offeringpb.CareCategory_GROUP_CLASS:
		// 其他类型按服务维度创建一条记录
		return l.generateSingleFulfillmentRecord(si)
	default:
		// 默认按服务维度创建
		return l.generateSingleFulfillmentRecord(si)
	}
}

// generateDailyFulfillmentRecords 按天生成履约记录（用于 Boarding 等跨天服务）
func (l *Logic) generateDailyFulfillmentRecords(si *serviceinstance.ServiceInstance) []*fulfillmentRepo.Fulfillment {
	var fulfillments []*fulfillmentRepo.Fulfillment
	startDate := si.StartDate
	endDate := si.EndDate
	// 按天循环生成履约记录
	for currentDate := startDate; !currentDate.After(endDate); currentDate = currentDate.AddDate(0, 0, 1) {
		dayStart := time.Date(currentDate.Year(), currentDate.Month(), currentDate.Day(),
			0, 0, 0, 0, currentDate.Location())
		dayEnd := time.Date(currentDate.Year(), currentDate.Month(), currentDate.Day(),
			23, 59, 59, 0, currentDate.Location())
		// 如果是第一天，使用实际开始时间
		if currentDate.Equal(startDate) {
			dayStart = startDate
		}
		// 如果是最后一天，使用实际结束时间
		if currentDate.Year() == endDate.Year() &&
			currentDate.Month() == endDate.Month() &&
			currentDate.Day() == endDate.Day() {
			dayEnd = endDate
		}
		fulfillment := &fulfillmentRepo.Fulfillment{
			BusinessID:        int64(si.BusinessID),
			CompanyID:         int64(si.CompanyID),
			AppointmentID:     int64(si.AppointmentID),
			CustomerID:        int64(si.CustomerID),
			PetID:             int64(si.PetID),
			ServiceInstanceID: int64(si.ID),
			ServiceFactoryID:  int64(si.ServiceFactoryID),
			CareType:          int32(si.CareType),
			StartTime:         dayStart,
			EndTime:           dayEnd,
			State:             int32(pb.State_STATE_UNSPECIFIED),
		}
		fulfillments = append(fulfillments, fulfillment)
	}

	return fulfillments
}

// generateSingleFulfillmentRecord 生成单条履约记录（用于 Grooming 等不跨天服务）
func (l *Logic) generateSingleFulfillmentRecord(si *serviceinstance.ServiceInstance) []*fulfillmentRepo.Fulfillment {
	fulfillment := &fulfillmentRepo.Fulfillment{
		BusinessID:        int64(si.BusinessID),
		CompanyID:         int64(si.CompanyID),
		CustomerID:        int64(si.CustomerID),
		PetID:             int64(si.PetID),
		ServiceInstanceID: int64(si.ID),
		ServiceFactoryID:  int64(si.ServiceFactoryID),
		CareType:          int32(si.CareType),
		StartTime:         si.StartDate,
		EndTime:           si.EndDate,
		State:             int32(pb.State_STATE_UNSPECIFIED),
	}

	return []*fulfillmentRepo.Fulfillment{fulfillment}
}

func verifyCreateAppointmentRequest(req *pb.CreateAppointmentRequest) error {
	if req.GetBusinessId() <= 0 {
		return status.Error(codes.InvalidArgument, "business_id must be greater than 0")
	}
	if req.GetCompanyId() <= 0 {
		return status.Error(codes.InvalidArgument, "company_id must be greater than 0")
	}
	if req.GetCustomerId() <= 0 {
		return status.Error(codes.InvalidArgument, "customer_id must be greater than 0")
	}
	startTime := req.GetStartTime().AsTime()
	endTime := req.GetEndTime().AsTime()
	if startTime.After(endTime) {
		return status.Error(codes.InvalidArgument, "start_time must be before end_time")
	}
	if len(req.GetPets()) == 0 {
		return status.Error(codes.InvalidArgument, "pets cannot be empty")
	}

	return nil
}

func (l *Logic) UpdateAppointment(ctx context.Context, req *pb.UpdateAppointmentRequest) (
	*pb.UpdateAppointmentResponse, error) {
	// 校验请求
	if err := verifyUpdateAppointmentRequest(req); err != nil {
		return nil, err
	}
	// 验证预约是否存在
	appointmentEntity, err := l.appointmentCli.GetByID(ctx, int(req.GetAppointmentId()))
	if err != nil {
		return nil, status.Errorf(codes.NotFound, "appointment not found: %v", err)
	}
	// 执行更新操作
	success, message, err := l.doUpdateAppointment(ctx, req, appointmentEntity)
	if err != nil {
		return nil, err
	}

	return &pb.UpdateAppointmentResponse{
		Success: success,
		Message: message,
	}, nil
}

func (l *Logic) doUpdateAppointment(ctx context.Context, req *pb.UpdateAppointmentRequest,
	appointmentEntity *appointment.Appointment) (bool, string, error) {
	var success bool
	var message string

	// 正常环境，使用事务处理

	err := l.tx.ExecuteInTransaction(ctx, []func(opCtx context.Context, tx *gorm.DB) error{
		func(opCtx context.Context, _ *gorm.DB) error {
			// 更新预约基本信息
			if err := l.updateAppointmentBasicInfo(opCtx, req, appointmentEntity); err != nil {
				return err
			}
			// 处理服务操作
			if err := l.processServiceOperations(opCtx, req.GetServiceOperations(),
				int(req.GetAppointmentId()), req, appointmentEntity); err != nil {
				return err
			}
			// 重新计算ServiceItemType并更新
			if err := l.recalculateAndUpdateServiceItemType(opCtx, int(req.GetAppointmentId())); err != nil {
				return err
			}
			success = true
			message = updateMessage

			return nil
		},
	})
	if err != nil {
		return false, err.Error(), err
	}

	return success, message, nil
}

func (l *Logic) updateAppointmentBasicInfo(ctx context.Context, req *pb.UpdateAppointmentRequest,
	appointmentEntity *appointment.Appointment) error {
	operations := req.GetAppointmentOperation()
	if operations == nil {
		return nil
	}
	// 更新开始时间
	if operations.StartTime != nil {
		appointmentEntity.StartTime = operations.StartTime.AsTime()
	}
	// 更新结束时间
	if operations.EndTime != nil {
		appointmentEntity.EndTime = operations.EndTime.AsTime()
	}
	// 更新颜色代码
	if operations.ColorCode != nil {
		appointmentEntity.ColorCode = operations.GetColorCode()
	}
	// 更新状态
	if operations.NewStatus != nil {
		appointmentEntity.Status = int(operations.GetNewStatus())
	}

	return l.appointmentCli.Update(ctx, appointmentEntity)
}

func (l *Logic) processServiceOperations(ctx context.Context,
	serviceOperations []*pb.UpdateAppointmentRequest_ServiceOperation,
	appointmentID int, req *pb.UpdateAppointmentRequest, appointmentEntity *appointment.Appointment) error {
	for _, operation := range serviceOperations {
		switch operation.GetOperationMode() {
		case pb.OperationMode_OPERATION_MODE_CREATE:
			if err := l.createServiceInstance(ctx, operation, appointmentID, req, appointmentEntity); err != nil {
				return err
			}
		case pb.OperationMode_OPERATION_MODE_UPDATE:
			if err := l.updateServiceInstance(ctx, operation); err != nil {
				return err
			}
		case pb.OperationMode_OPERATION_MODE_DELETE:
			if err := l.deleteServiceInstance(ctx, operation); err != nil {
				return err
			}
		}
	}

	return nil
}

/*
		createServiceInstance
		1. 新建一个serviceInstance，在serviceOperation填充新建信息即可，不用填充serviceOperation里面的parent_service_instance_id
	    2. 新建一个subServiceInstance（additionalService）
	       a. 如果其父serviceInstance存在，则需要新建一个serviceOperation，在里面填充parent_service_instance_id和subServiceInstance内容
	       b. 如果其父serviceInstance不存在，则需要先新建一个serviceOperation，先填充父serviceInstance信息，
	          然后在serviceOperation的subServiceInstance字段
*/
func (l *Logic) createServiceInstance(ctx context.Context, operation *pb.UpdateAppointmentRequest_ServiceOperation,
	appointmentID int, req *pb.UpdateAppointmentRequest, appointmentEntity *appointment.Appointment) error {
	// 获取careType
	careType, err := l.getCareTypeByServiceTemplateID(ctx, operation.GetId())
	if err != nil {
		return err
	}
	// 创建主服务实例
	serviceInstance, err := l.buildServiceInstance(operation, appointmentID, req, appointmentEntity, careType)
	if err != nil {
		return err
	}
	// 设置父子关系
	if err := l.setParentChildRelationship(ctx, serviceInstance, operation); err != nil {
		return err
	}
	// 创建服务实例
	serviceInstanceID, err := l.serviceInstanceCli.Create(ctx, serviceInstance)
	if err != nil {
		return err
	}
	// 生成履约记录
	if err := l.createFulfillmentsForServiceInstance(ctx, serviceInstance, careType); err != nil {
		return err
	}
	// 处理子服务实例
	if err := l.processSubServiceInstances(ctx, operation, serviceInstanceID,
		appointmentID, req, appointmentEntity); err != nil {
		return err
	}

	return nil
}

// buildServiceInstance 构建服务实例
func (l *Logic) buildServiceInstance(operation *pb.UpdateAppointmentRequest_ServiceOperation,
	appointmentID int, _ *pb.UpdateAppointmentRequest, appointmentEntity *appointment.Appointment,
	careType offeringpb.CareCategory) (*serviceinstance.ServiceInstance, error) {
	serviceInstance := &serviceinstance.ServiceInstance{
		CustomerID:       appointmentEntity.CustomerID,
		AppointmentID:    appointmentID,
		PetID:            0, // TODO: 需要从operation中获取pet_id，或者从现有数据中推断
		CareType:         int(careType),
		DateType:         int(operation.GetDateScheduleConfig().GetDateType()),
		ServiceFactoryID: int(operation.GetId()),
		StartDate:        operation.GetTimeConfig().GetStartTime().AsTime(),
		EndDate:          operation.GetTimeConfig().GetEndTime().AsTime(),
	}

	return serviceInstance, nil
}

// setParentChildRelationship 设置父子关系
func (l *Logic) setParentChildRelationship(ctx context.Context, serviceInstance *serviceinstance.ServiceInstance,
	operation *pb.UpdateAppointmentRequest_ServiceOperation) error {
	if operation.GetParentServiceInstanceId() <= 0 {
		return nil
	}

	serviceInstance.ParentID = int(operation.GetParentServiceInstanceId())

	// 获取父服务的RootID，如果没有则使用父服务ID作为RootID
	parentService, err := l.serviceInstanceCli.GetByID(ctx, int(operation.GetParentServiceInstanceId()))
	if err != nil {
		return err
	}

	if parentService.RootParentID > 0 {
		serviceInstance.RootParentID = parentService.RootParentID
	} else {
		serviceInstance.RootParentID = parentService.ID
	}

	return nil
}

// createFulfillmentsForServiceInstance 为服务实例创建履约记录
func (l *Logic) createFulfillmentsForServiceInstance(ctx context.Context,
	serviceInstance *serviceinstance.ServiceInstance,
	careType offeringpb.CareCategory) error {
	fulfillments := l.generateFulfillmentRecords(serviceInstance, careType)
	if len(fulfillments) > 0 {
		if err := l.fulfillmentCli.BatchCreate(ctx, fulfillments); err != nil {
			return err
		}
	}

	return nil
}

// processSubServiceInstances 处理子服务实例
func (l *Logic) processSubServiceInstances(ctx context.Context, operation *pb.UpdateAppointmentRequest_ServiceOperation,
	serviceInstanceID int, appointmentID int, req *pb.UpdateAppointmentRequest,
	appointmentEntity *appointment.Appointment) error {
	for _, subOperation := range operation.GetSubServiceInstances() {
		subOperation.ParentServiceInstanceId = proto.Int64(int64(serviceInstanceID))
		if err := l.createServiceInstance(ctx, subOperation, appointmentID, req, appointmentEntity); err != nil {
			return err
		}
	}

	return nil
}

func (l *Logic) updateServiceInstance(ctx context.Context,
	operation *pb.UpdateAppointmentRequest_ServiceOperation) error {
	if operation.GetServiceInstanceId() <= 0 {
		return status.Error(codes.InvalidArgument, "service_instance_id is required for update operation")
	}
	// 获取现有服务实例
	serviceInstance, err := l.serviceInstanceCli.GetByID(ctx, int(operation.GetServiceInstanceId()))
	if err != nil {
		return err
	}
	// 更新字段
	if operation.GetTimeConfig() != nil {
		if operation.GetTimeConfig().GetStartTime() != nil {
			serviceInstance.StartDate = operation.GetTimeConfig().GetStartTime().AsTime()
		}
		if operation.GetTimeConfig().GetEndTime() != nil {
			serviceInstance.EndDate = operation.GetTimeConfig().GetEndTime().AsTime()
		}
	}
	if operation.GetDateScheduleConfig() != nil {
		if operation.GetDateScheduleConfig().GetDateType() != pb.DateType_DATE_TYPE_UNSPECIFIED {
			serviceInstance.DateType = int(operation.GetDateScheduleConfig().GetDateType())
		}
	}

	return l.serviceInstanceCli.Update(ctx, serviceInstance)
}

func (l *Logic) deleteServiceInstance(ctx context.Context,
	operation *pb.UpdateAppointmentRequest_ServiceOperation) error {
	if operation.GetServiceInstanceId() <= 0 {
		return status.Error(codes.InvalidArgument, "service_instance_id is required for delete operation")
	}

	// 删除相关的履约记录
	if err := l.fulfillmentCli.DeleteByServiceInstanceID(ctx, int64(operation.GetServiceInstanceId())); err != nil {
		return err
	}

	// 删除服务实例
	return l.serviceInstanceCli.Delete(ctx, int(operation.GetServiceInstanceId()))
}

func (l *Logic) getCareTypeByServiceTemplateID(_ context.Context, _ int64) (offeringpb.CareCategory, error) {
	// 调用offering服务获取careType
	// 暂时返回默认值
	return offeringpb.CareCategory_BOARDING, nil
}

func (l *Logic) recalculateAndUpdateServiceItemType(ctx context.Context, appointmentID int) error {
	// 获取预约下的所有服务实例
	serviceInstances, err := l.serviceInstanceCli.GetByAppointmentID(ctx, appointmentID)
	if err != nil {
		return err
	}

	// 收集所有唯一的careType
	uniqueCareTypes := make(map[offeringpb.CareCategory]bool)
	for _, si := range serviceInstances {
		careType := offeringpb.CareCategory(si.CareType)
		uniqueCareTypes[careType] = true
	}

	// 计算ServiceItemType
	var serviceItemType int32
	for careType := range uniqueCareTypes {
		serviceItemType += int32(careType)
	}

	// 更新预约的ServiceItemType
	appointmentEntity, err := l.appointmentCli.GetByID(ctx, appointmentID)
	if err != nil {
		return err
	}
	appointmentEntity.ServiceItemType = int(serviceItemType)

	return l.appointmentCli.Update(ctx, appointmentEntity)
}

func verifyUpdateAppointmentRequest(req *pb.UpdateAppointmentRequest) error {
	if req.GetAppointmentId() <= 0 {
		return status.Error(codes.InvalidArgument, "appointment_id must be greater than 0")
	}
	// 验证预约操作
	if operations := req.GetAppointmentOperation(); operations != nil {
		if operations.StartTime != nil && operations.EndTime != nil {
			startTime := operations.StartTime.AsTime()
			endTime := operations.EndTime.AsTime()
			if startTime.After(endTime) {
				return status.Error(codes.InvalidArgument, "start_time must be before end_time")
			}
		}
	}
	// 验证服务操作
	for _, serviceOp := range req.GetServiceOperations() {
		if err := verifyServiceOperation(serviceOp); err != nil {
			return err
		}
	}

	return nil
}

func verifyServiceOperation(operation *pb.UpdateAppointmentRequest_ServiceOperation) error {
	switch operation.GetOperationMode() {
	case pb.OperationMode_OPERATION_MODE_CREATE:
		if operation.GetId() <= 0 {
			return status.Error(codes.InvalidArgument, "service_id is required for create operation")
		}
		if operation.GetTimeConfig().GetStartTime() == nil || operation.GetTimeConfig().GetEndTime() == nil {
			return status.Error(codes.InvalidArgument, "start_time and end_time are required for create operation")
		}
	case pb.OperationMode_OPERATION_MODE_UPDATE, pb.OperationMode_OPERATION_MODE_DELETE:
		if operation.GetServiceInstanceId() <= 0 {
			return status.Error(codes.InvalidArgument, "service_instance_id is required for update/delete operation")
		}
	}

	// 验证时间
	if operation.GetTimeConfig().GetStartTime() != nil &&
		operation.GetTimeConfig().GetEndTime() != nil {
		startTime := operation.GetTimeConfig().GetStartTime().AsTime()
		endTime := operation.GetTimeConfig().GetEndTime().AsTime()
		if startTime.After(endTime) {
			return status.Error(codes.InvalidArgument, "start_time must be before end_time")
		}
	}

	return nil
}

// GetAppointmentByIDs 根据ID列表获取预约信息
func (l *Logic) GetAppointmentByIDs(ctx context.Context, req *pb.GetAppointmentByIDsRequest) (
	*pb.GetAppointmentByIDsResponse, error) {
	log.InfoContextf(ctx, "GetAppointmentByIDs req:%+v", req)
	// 校验请求
	if err := verifyGetAppointmentByIDsRequest(req); err != nil {
		return nil, err
	}
	// 如果 appointment_ids 为空，直接返回空结果
	if len(req.GetAppointmentIds()) == 0 {
		return &pb.GetAppointmentByIDsResponse{
			Appointments: []*pb.Appointment{},
		}, nil
	}
	// 查询数据库
	appointments, err := l.appointmentCli.GetByIDs(ctx, req.GetAppointmentIds())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get appointments: %v", err)
	}
	// 转换为proto对象
	var pbAppointments []*pb.Appointment
	for _, appointment := range appointments {
		pbAppointment, err := l.convertAppointmentToPB(ctx, appointment)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "failed to convert appointment: %v", err)
		}
		pbAppointments = append(pbAppointments, pbAppointment)
	}
	response := &pb.GetAppointmentByIDsResponse{
		Appointments: pbAppointments,
	}

	return response, nil
}

func (l *Logic) ListAppointment(ctx context.Context, req *pb.ListAppointmentRequest) (
	*pb.ListAppointmentResponse, error) {
	log.InfoContextf(ctx, "ListAppointment req:%+v", req)
	// 校验请求
	if err := verifyListAppointmentRequest(req); err != nil {
		return nil, err
	}
	// 构建查询参数
	param := &appointment.BaseParam{
		BusinessID: int32(req.GetBusinessId()),
		CompanyID:  int32(req.GetCompanyId()),
	}
	// 设置时间范围
	if req.GetStartTime() != nil {
		param.StartTime = req.GetStartTime().AsTime()
	}
	if req.GetEndTime() != nil {
		param.EndTime = req.GetEndTime().AsTime()
	}
	// 设置分页信息
	param.PaginationInfo = buildPaginationInfo(req)
	// 构建过滤条件
	filter, err := l.buildFilter(req)
	if err != nil {
		return nil, err
	}
	// 查询总数
	total, err := l.appointmentCli.Count(ctx, param, filter)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to count appointments: %v", err)
	}
	// 查询列表
	appointments, err := l.appointmentCli.List(ctx, param, filter)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to list appointments: %v", err)
	}
	// 转换为 proto 对象
	var pbAppointments []*pb.Appointment
	for _, appointment := range appointments {
		pbAppointment, err := l.convertAppointmentToPB(ctx, appointment)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "failed to convert appointment: %v", err)
		}
		pbAppointments = append(pbAppointments, pbAppointment)
	}
	// 计算是否最后一页
	isEnd := false
	if req.GetPagination() != nil {
		offset := req.GetPagination().GetOffset()
		limit := req.GetPagination().GetLimit()
		isEnd = int64(offset+limit) >= total
	} else {
		isEnd = int64(len(pbAppointments)) >= total
	}
	response := &pb.ListAppointmentResponse{
		Appointments: pbAppointments,
		Pagination:   req.GetPagination(),
		IsEnd:        isEnd,
		Total:        int32(total),
	}

	return response, nil
}

func (l *Logic) buildFilter(req *pb.ListAppointmentRequest) (*appointment.Filter, error) {
	filter := &appointment.Filter{}
	if req.GetFilter() != nil {
		// 处理状态过滤
		if len(req.GetFilter().GetStatuses()) > 0 {
			statuses := make([]int32, 0, len(req.GetFilter().GetStatuses()))
			for _, status := range req.GetFilter().GetStatuses() {
				statuses = append(statuses, int32(status))
			}
			filter.Statuses = statuses
		}
	}

	return filter, nil
}

func buildPaginationInfo(req *pb.ListAppointmentRequest) *appointment.PaginationInfo {
	if req.GetPagination() == nil {
		return &appointment.PaginationInfo{
			Offset: defaultOffset,
			Limit:  defaultLimit,
		}
	}

	return &appointment.PaginationInfo{
		Offset: req.GetPagination().GetOffset(),
		Limit:  req.GetPagination().GetLimit(),
	}
}

func verifyListAppointmentRequest(req *pb.ListAppointmentRequest) error {
	if req == nil {
		return status.Errorf(codes.InvalidArgument, "request cannot be nil")
	}
	if req.GetCompanyId() <= 0 {
		return status.Errorf(codes.InvalidArgument, "company_id must be greater than 0")
	}
	if req.GetBusinessId() <= 0 {
		return status.Errorf(codes.InvalidArgument, "business_id must be greater than 0")
	}
	// 验证时间范围
	if req.GetStartTime() != nil && req.GetEndTime() != nil {
		if req.GetStartTime().AsTime().After(req.GetEndTime().AsTime()) {
			return status.Errorf(codes.InvalidArgument, "start_time cannot be after end_time")
		}
	}
	// 验证分页参数
	if req.GetPagination() != nil {
		if req.GetPagination().GetOffset() < 0 {
			return status.Errorf(codes.InvalidArgument, "pagination offset cannot be negative")
		}
	}

	return nil
}

func verifyGetAppointmentByIDsRequest(req *pb.GetAppointmentByIDsRequest) error {
	if req == nil {
		return status.Errorf(codes.InvalidArgument, "request cannot be nil")
	}
	for _, id := range req.GetAppointmentIds() {
		if id <= 0 {
			return status.Errorf(codes.InvalidArgument, "appointment_id must be greater than 0")
		}
	}

	return nil
}

// convertAppointmentToPB 将数据库实体转换为protobuf格式
func (l *Logic) convertAppointmentToPB(ctx context.Context, entity *appointment.Appointment) (*pb.Appointment, error) {
	// 获取该appointment下的所有service instances
	serviceInstances, err := l.serviceInstanceCli.GetByAppointmentID(ctx, entity.ID)
	if err != nil {
		return nil, err
	}
	// 按pet_id分组service instances
	petServiceMap := make(map[int64][]*serviceinstance.ServiceInstance)
	for _, si := range serviceInstances {
		petID := int64(si.PetID)
		petServiceMap[petID] = append(petServiceMap[petID], si)
	}
	// 构建PetDetail列表
	var petDetails []*pb.PetDetail
	for petID, services := range petServiceMap {
		// 构建树形结构的服务实例
		rootServices := l.buildServiceInstanceTree(services)
		petDetail := &pb.PetDetail{
			PetId:    petID,
			Services: rootServices,
		}
		petDetails = append(petDetails, petDetail)
	}
	// 构建并返回Appointment
	pbAppointment := &pb.Appointment{
		Id:              int64(entity.ID),
		BusinessId:      int64(entity.BusinessID),
		CompanyId:       int64(entity.CompanyID),
		CustomerId:      int64(entity.CustomerID),
		Status:          pb.AppointmentState(entity.Status),
		ServiceItemType: int32(entity.ServiceItemType),
		ColorCode:       entity.ColorCode,
		StartTime:       timestamppb.New(entity.StartTime),
		EndTime:         timestamppb.New(entity.EndTime),
		Pets:            petDetails,
	}

	return pbAppointment, nil
}

// buildServiceInstanceTree 构建服务实例的树形结构
func (l *Logic) buildServiceInstanceTree(services []*serviceinstance.ServiceInstance) []*pb.ServiceInstanceImpl {
	// 创建ID到ServiceInstance的映射
	serviceMap := make(map[int]*serviceinstance.ServiceInstance)
	for _, si := range services {
		serviceMap[si.ID] = si
	}

	// 创建ID到pb.ServiceInstanceImpl的映射
	pbServiceMap := make(map[int]*pb.ServiceInstanceImpl)
	for _, si := range services {
		pbServiceMap[si.ID] = &pb.ServiceInstanceImpl{
			ServiceInstanceId:   int64(si.ID),
			Id:                  int64(si.ServiceFactoryID),
			StartTime:           timestamppb.New(si.StartDate),
			EndTime:             timestamppb.New(si.EndDate),
			SubServiceInstances: []*pb.ServiceInstanceImpl{},
			// TODO: 如果需要其他字段（如Options、Charges等），在这里添加
		}
	}
	// 构建父子关系
	var rootServices []*pb.ServiceInstanceImpl
	for _, si := range services {
		pbService := pbServiceMap[si.ID]
		if si.ParentID == 0 {
			// 这是根节点
			rootServices = append(rootServices, pbService)
		} else {
			// 这是子节点，需要添加到父节点的SubServiceInstance中
			if parentPbService, exists := pbServiceMap[si.ParentID]; exists {
				parentPbService.SubServiceInstances = append(parentPbService.SubServiceInstances, pbService)
			}
		}
	}

	return rootServices
}
