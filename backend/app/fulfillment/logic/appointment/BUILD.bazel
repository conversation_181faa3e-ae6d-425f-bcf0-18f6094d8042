load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "appointment",
    srcs = [
        "appointment.go",
        "pet_profile.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/fulfillment/logic/appointment",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/fulfillment/repo/customer",
        "//backend/app/fulfillment/repo/db",
        "//backend/app/fulfillment/repo/db/appointment",
        "//backend/app/fulfillment/repo/db/feeding",
        "//backend/app/fulfillment/repo/db/fulfillment",
        "//backend/app/fulfillment/repo/db/medication",
        "//backend/app/fulfillment/repo/db/serviceinstance",
        "//backend/app/fulfillment/repo/offering",
        "//backend/common/rpc/framework/log",
        "//backend/proto/fulfillment/v1:fulfillment",
        "//backend/proto/offering/v1:offering",
        "@com_github_gogo_protobuf//proto",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/business_customer/v1:business_customer",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "appointment_test",
    srcs = [
        "appointment_test.go",
        "pet_profile_test.go",
    ],
    embed = [":appointment"],
    deps = [
        "//backend/app/fulfillment/repo/customer/mock",
        "//backend/app/fulfillment/repo/db/appointment",
        "//backend/app/fulfillment/repo/db/appointment/mock",
        "//backend/app/fulfillment/repo/db/fulfillment/mock",
        "//backend/app/fulfillment/repo/db/mock",
        "//backend/app/fulfillment/repo/db/serviceinstance",
        "//backend/app/fulfillment/repo/db/serviceinstance/mock",
        "//backend/app/fulfillment/repo/offering/mock",
        "//backend/proto/fulfillment/v1:fulfillment",
        "//backend/proto/offering/v1:offering",
        "@com_github_gogo_protobuf//proto",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/service/business_customer/v1:business_customer",
        "@com_github_stretchr_testify//assert",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_mock//gomock",
    ],
)
