load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "utils",
    srcs = [
        "Des3Utils.go",
        "commonUtils.go",
        "utils.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/fulfillment/utils",
    visibility = ["//visibility:public"],
)

go_test(
    name = "utils_test",
    srcs = [
        "Des3Utils_test.go",
        "commonUtils_test.go",
    ],
    embed = [":utils"],
    deps = ["@com_github_stretchr_testify//assert"],
)
