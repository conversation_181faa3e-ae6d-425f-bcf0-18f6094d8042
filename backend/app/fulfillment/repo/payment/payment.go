package payment

import (
	"context"
	"fmt"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/http"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type impl struct {
	client http.Client
}

type ReadWriter interface {
	QueryCompanyPlanFeatureByCidCode(ctx context.Context, companyID int32, code FeatureCode) (*FeatureQuotaDto, error)
}

const (
	queryCompanyPlanFeatureByCidCodePath = "/service/payment/plan/queryCompanyPlanFeatureByCidCode"
)

func New() ReadWriter {
	return &impl{
		client: http.NewClientProxy("moego-server-payment"),
	}
}

func (i *impl) QueryCompanyPlanFeatureByCidCode(
	ctx context.Context, companyID int32, code FeatureCode) (*FeatureQuotaDto, error) {
	var result FeatureQuotaDto
	path := fmt.Sprintf("%s?companyId=%d&code=%s", queryCompanyPlanFeatureByCidCodePath, companyID, code)

	err := i.client.Get(ctx, path, &result)
	if err != nil {
		log.ErrorContextf(ctx, "query company plan feature by company id and code err: %v", err)

		return nil, err
	}

	return &result, nil
}
