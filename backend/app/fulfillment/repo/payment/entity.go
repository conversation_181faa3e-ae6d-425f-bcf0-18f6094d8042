package payment

// FeatureCode 功能代码枚举
type FeatureCode string

const (
	FcReviewBooster     FeatureCode = "reviewBooster"
	FcGroomingReportLv1 FeatureCode = "groomingReportLv1"
	FcGroomingReportLv2 FeatureCode = "groomingReportLv2"
)

// String 实现Stringer接口
func (fc FeatureCode) String() string {
	return string(fc)
}

// FeatureQuotaDto 功能配额数据传输对象
type FeatureQuotaDto struct {
	Code      FeatureCode `json:"code"`
	Enable    bool        `json:"enable"`
	Quota     int32       `json:"quota"`
	AllowType int8        `json:"allowType"`
}
