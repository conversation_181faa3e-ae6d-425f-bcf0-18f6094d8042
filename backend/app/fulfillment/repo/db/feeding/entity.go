package feeding

import (
	"time"
)

// 列名常量定义，避免手搓列名
const (
	ColumnID                 = "id"
	ColumnCompanyID          = "company_id"
	ColumnAppointmentID      = "appointment_id"
	ColumnPetDetailID        = "pet_detail_id"
	ColumnPetID              = "pet_id"
	ColumnFeedingAmount      = "feeding_amount"
	ColumnFeedingUnit        = "feeding_unit"
	ColumnFeedingType        = "feeding_type"
	ColumnFeedingSource      = "feeding_source"
	ColumnFeedingInstruction = "feeding_instruction"
	ColumnFeedingNote        = "feeding_note"
	ColumnCreatedAt          = "created_at"
	ColumnUpdatedAt          = "updated_at"
	ColumnDeletedAt          = "deleted_at"
)

type BaseParam struct {
	CompanyID      int64
	AppointmentID  int64
	PetDetailID    int64
	PetID          int64
	PaginationInfo *PaginationInfo
}

type Filter struct {
	CompanyIDs     []int64
	AppointmentIDs []int64
	PetDetailIDs   []int64
	PetIDs         []int64
}

type PaginationInfo struct {
	Offset int32
	Limit  int32
}

// AppointmentPetFeeding 预约宠物喂养记录
type AppointmentPetFeeding struct {
	ID                 int64      `json:"id" gorm:"column:id;primaryKey;autoIncrement"`
	CompanyID          int64      `json:"company_id" gorm:"column:company_id"`
	AppointmentID      int64      `json:"appointment_id" gorm:"column:appointment_id"`
	PetDetailID        int64      `json:"pet_detail_id" gorm:"column:pet_detail_id"`
	PetID              int64      `json:"pet_id" gorm:"column:pet_id"`
	FeedingAmount      string     `json:"feeding_amount" gorm:"column:feeding_amount"`
	FeedingUnit        string     `json:"feeding_unit" gorm:"column:feeding_unit"`
	FeedingType        string     `json:"feeding_type" gorm:"column:feeding_type"`
	FeedingSource      string     `json:"feeding_source" gorm:"column:feeding_source"`
	FeedingInstruction string     `json:"feeding_instruction" gorm:"column:feeding_instruction"`
	FeedingNote        string     `json:"feeding_note" gorm:"column:feeding_note"`
	CreatedAt          time.Time  `json:"created_at" gorm:"column:created_at"`
	UpdatedAt          time.Time  `json:"updated_at" gorm:"column:updated_at"`
	DeletedAt          *time.Time `json:"deleted_at" gorm:"column:deleted_at"`
}

// TableName 指定表名
func (AppointmentPetFeeding) TableName() string {
	return "appointment_pet_feeding"
}
