load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "feeding",
    srcs = [
        "create.go",
        "entity.go",
        "pet_feeding.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/feeding",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/fulfillment/repo/db",
        "//backend/common/rpc/framework/log",
        "@io_gorm_gorm//:gorm",
    ],
)
