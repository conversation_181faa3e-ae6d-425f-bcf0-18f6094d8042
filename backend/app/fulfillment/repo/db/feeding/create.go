package feeding

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

func (i *impl) BatchCreate(ctx context.Context, feedings []*AppointmentPetFeeding) error {
	if len(feedings) == 0 {
		return nil
	}
	// 使用事务进行批量插入
	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.CreateInBatches(feedings, len(feedings)).Error; err != nil {
			log.ErrorContextf(ctx, "BatchCreateAppointmentPetFeeding err, err:%+v", err)

			return err
		}

		return nil
	})
}

func (i *impl) Create(ctx context.Context, feeding *AppointmentPetFeeding) error {
	return i.db.WithContext(ctx).Create(feeding).Error
}
