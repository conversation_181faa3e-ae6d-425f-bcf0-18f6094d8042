package medication

import (
	"time"
)

// 列名常量定义，避免手搓列名
const (
	ColumnID               = "id"
	ColumnCompanyID        = "company_id"
	ColumnAppointmentID    = "appointment_id"
	ColumnPetDetailID      = "pet_detail_id"
	ColumnPetID            = "pet_id"
	ColumnMedicationAmount = "medication_amount"
	ColumnMedicationUnit   = "medication_unit"
	ColumnMedicationName   = "medication_name"
	ColumnMedicationNote   = "medication_note"
	ColumnDateType         = "date_type"
	ColumnSpecificDates    = "specific_dates"
	ColumnCreatedAt        = "created_at"
	ColumnUpdatedAt        = "updated_at"
	ColumnDeletedAt        = "deleted_at"
)

// 日期类型枚举
const (
	DateTypeEverydayExceptCheckout  = 1 // EVERYDAY_EXCEPT_CHECKOUT_DATE
	DateTypeEverydayIncludeCheckout = 2 // EVERYDAY_INCLUDE_CHECKOUT_DATE
	DateTypeSpecificDate            = 3 // SPECIFIC_DATE
)

type BaseParam struct {
	CompanyID      int64
	AppointmentID  int64
	PetDetailID    int64
	PetID          int64
	DateType       int32
	PaginationInfo *PaginationInfo
}

type Filter struct {
	CompanyIDs     []int64
	AppointmentIDs []int64
	PetDetailIDs   []int64
	PetIDs         []int64
	DateTypes      []int32
}

type PaginationInfo struct {
	Offset int32
	Limit  int32
}

// AppointmentPetMedication 预约宠物用药记录
type AppointmentPetMedication struct {
	ID               int64      `json:"id" gorm:"column:id;primaryKey;autoIncrement"`
	CompanyID        int64      `json:"company_id" gorm:"column:company_id"`
	AppointmentID    int64      `json:"appointment_id" gorm:"column:appointment_id"`
	PetDetailID      int64      `json:"pet_detail_id" gorm:"column:pet_detail_id"`
	PetID            int64      `json:"pet_id" gorm:"column:pet_id"`
	MedicationAmount string     `json:"medication_amount" gorm:"column:medication_amount"`
	MedicationUnit   string     `json:"medication_unit" gorm:"column:medication_unit"`
	MedicationName   string     `json:"medication_name" gorm:"column:medication_name"`
	MedicationNote   string     `json:"medication_note" gorm:"column:medication_note"`
	DateType         int32      `json:"date_type" gorm:"column:date_type"`
	SpecificDates    string     `json:"specific_dates" gorm:"column:specific_dates"`
	CreatedAt        time.Time  `json:"created_at" gorm:"column:created_at"`
	UpdatedAt        time.Time  `json:"updated_at" gorm:"column:updated_at"`
	DeletedAt        *time.Time `json:"deleted_at" gorm:"column:deleted_at"`
}

// TableName 指定表名
func (AppointmentPetMedication) TableName() string {
	return "appointment_pet_medication"
}
