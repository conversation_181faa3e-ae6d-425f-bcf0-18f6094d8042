load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "medication",
    srcs = [
        "create.go",
        "entity.go",
        "medication.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/medication",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/fulfillment/repo/db",
        "//backend/common/rpc/framework/log",
        "@io_gorm_gorm//:gorm",
    ],
)
