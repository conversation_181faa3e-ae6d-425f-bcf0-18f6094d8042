package medication

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db"
)

type ReadWriter interface {
	Create(ctx context.Context, medication *AppointmentPetMedication) error
	BatchCreate(ctx context.Context, medications []*AppointmentPetMedication) error
}

type impl struct {
	db                 *gorm.DB
	transactionManager db.TransactionManager
}

func New() ReadWriter {
	database := db.GetDB()

	return &impl{
		db:                 database,
		transactionManager: db.NewTxManager(),
	}
}
