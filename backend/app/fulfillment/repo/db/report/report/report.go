package report

import (
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db"
	fulfillmentpb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
)

type ReadWriter interface {
	Create(ctx context.Context, report *Report) error
	Update(ctx context.Context, report *Report) error
	Delete(ctx context.Context, id int64) error
	BatchDelete(ctx context.Context, companyID, businessID int64, reportIDs []int64) error
	FindByID(ctx context.Context, id int64) (*Report, error)
	FindByUUID(ctx context.Context, uuid string) (*Report, error)
	FindByUniqueKey(ctx context.Context,
		businessID, appointmentID, petID int64, careType int32, serviceDate string) (*Report, error)
	Upsert(ctx context.Context, report *Report) error
	FindByUniqueKeys(ctx context.Context, uniqueKeys []Filter) ([]*Report, error)
	BatchCreate(ctx context.Context, reports []*Report) error
	List(ctx context.Context, param *BaseParam, filter *Filter) ([]*Report, error)
	Count(ctx context.Context, param *BaseParam, filter *Filter) (int64, error)
	CountByStatus(ctx context.Context, param *BaseParam, filter *Filter) (*StatusCount, error)
	IncreaseOpenedCount(ctx context.Context, uuid string) error
}

type reportImpl struct {
	db                 *gorm.DB
	transactionManager db.TransactionManager
}

func NewFulfillmentReportRepo() ReadWriter {
	database := db.GetDB()

	return &reportImpl{
		db:                 database,
		transactionManager: db.NewTxManager(),
	}
}

func (i *reportImpl) Create(ctx context.Context, report *Report) error {
	return i.db.WithContext(ctx).Create(report).Error
}

func (i *reportImpl) Update(ctx context.Context, report *Report) error {
	result := i.db.WithContext(ctx).Updates(report)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}

	return nil
}

func (i *reportImpl) Delete(ctx context.Context, id int64) error {
	result := i.db.WithContext(ctx).Delete(&Report{}, id)
	if result.Error != nil {
		return result.Error
	}

	return nil
}

func (i *reportImpl) BatchDelete(ctx context.Context, companyID, businessID int64, reportIDs []int64) error {
	if len(reportIDs) == 0 {
		return nil
	}

	clauses := []clause.Expression{
		clause.Eq{Column: ColumnCompanyID, Value: companyID},
		clause.Eq{Column: ColumnBusinessID, Value: businessID},
		clause.IN{Column: ColumnID, Values: convertInt64SliceToInterface(reportIDs)},
	}

	result := i.db.WithContext(ctx).Clauses(clauses...).Delete(&Report{})
	if result.Error != nil {
		return fmt.Errorf("failed to batch delete reports: %w", result.Error)
	}

	return nil
}

func (i *reportImpl) FindByID(ctx context.Context, id int64) (*Report, error) {
	var report Report
	err := i.db.WithContext(ctx).First(&report, id).Error
	if err != nil {
		return nil, err
	}

	return &report, nil
}

func (i *reportImpl) FindByUUID(ctx context.Context, uuid string) (*Report, error) {
	var report Report
	err := i.db.WithContext(ctx).Where("uuid = ?", uuid).First(&report).Error
	if err != nil {
		return nil, err
	}

	return &report, nil
}

func (i *reportImpl) FindByUniqueKey(ctx context.Context, businessID, appointmentID, petID int64,
	careType int32, serviceDate string) (*Report, error) {
	var report Report
	clauses := []clause.Expression{
		clause.Eq{Column: ColumnBusinessID, Value: businessID},
		clause.Eq{Column: ColumnAppointmentID, Value: appointmentID},
		clause.Eq{Column: ColumnPetID, Value: petID},
		clause.Eq{Column: ColumnCareType, Value: careType},
		clause.Eq{Column: ColumnServiceDate, Value: serviceDate},
	}
	err := i.db.WithContext(ctx).Clauses(clauses...).First(&report).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}

		return nil, err
	}

	return &report, nil
}

func (i *reportImpl) Upsert(ctx context.Context, report *Report) error {
	// 尝试根据唯一键查找现有记录
	existing, err := i.FindByUniqueKey(ctx, report.BusinessID,
		report.AppointmentID, report.PetID, report.CareType, report.ServiceDate)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 记录不存在，创建新记录
			return i.Create(ctx, report)
		}

		return err
	}

	// 记录存在，更新现有记录
	report.ID = existing.ID
	report.CreateTime = existing.CreateTime // 保持原始创建时间

	return i.Update(ctx, report)
}

func (i *reportImpl) FindByUniqueKeys(ctx context.Context, uniqueKeys []Filter) ([]*Report, error) {
	if len(uniqueKeys) == 0 {
		return []*Report{}, nil
	}

	var reports []*Report
	query := i.db.WithContext(ctx)

	// 构建 OR 条件查询
	var orClauses []clause.Expression

	for _, uniqueKey := range uniqueKeys {
		andClauses := []clause.Expression{
			clause.Eq{Column: ColumnBusinessID, Value: uniqueKey.BusinessID},
			clause.Eq{Column: ColumnAppointmentID, Value: uniqueKey.AppointmentID},
			clause.Eq{Column: ColumnPetID, Value: uniqueKey.PetID},
			clause.Eq{Column: ColumnCareType, Value: uniqueKey.CareType},
			clause.Eq{Column: ColumnServiceDate, Value: uniqueKey.ServiceDate},
		}
		orClauses = append(orClauses, clause.And(andClauses...))
	}

	if len(orClauses) > 0 {
		query = query.Clauses(clause.Or(orClauses...))
	}

	// 执行查询
	if err := query.Find(&reports).Error; err != nil {
		return nil, fmt.Errorf("failed to query reports by unique keys: %w", err)
	}

	return reports, nil
}

func (i *reportImpl) BatchCreate(ctx context.Context, reports []*Report) error {
	if len(reports) == 0 {
		return nil
	}

	return i.db.WithContext(ctx).CreateInBatches(reports, MaxBatchCreateSize).Error
}

// Count 统计报告总数
func (i *reportImpl) Count(ctx context.Context, param *BaseParam, filter *Filter) (int64, error) {
	if param == nil {
		return 0, nil
	}

	query := i.buildCountQuery(ctx, param, filter)
	var count int64
	if err := query.Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count reports: %w", err)
	}

	return count, nil
}

// CountByStatus 按状态统计报告数量
func (i *reportImpl) CountByStatus(ctx context.Context, param *BaseParam, filter *Filter) (*StatusCount, error) {
	if param == nil {
		return &StatusCount{}, nil
	}

	baseQuery := i.buildCountQuery(ctx, param, filter)

	// 统计总数
	var total int64
	if err := baseQuery.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count total reports: %w", err)
	}

	finalFilter := filter
	if finalFilter != nil {
		finalFilter.Status = nil
	}

	// 统计草稿状态数量
	var draftCount int64
	draftQuery := i.buildCountQuery(ctx, param, finalFilter).
		Where("LOWER(status) = LOWER(?)", fulfillmentpb.ReportStatus_DRAFT.String())
	if err := draftQuery.Count(&draftCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count draft reports: %w", err)
	}

	// 统计已发送状态数量
	var sentCount int64
	sentQuery := i.buildCountQuery(ctx, param, finalFilter).
		Where("LOWER(status) = LOWER(?)", fulfillmentpb.ReportStatus_SENT.String())
	if err := sentQuery.Count(&sentCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count sent reports: %w", err)
	}

	return &StatusCount{
		Total:      total,
		DraftCount: draftCount,
		SentCount:  sentCount,
	}, nil
}

// buildCountQuery 构建计数查询
func (i *reportImpl) buildCountQuery(ctx context.Context, param *BaseParam, filter *Filter) *gorm.DB {
	query := i.db.WithContext(ctx).Model(&Report{})

	// 基础参数过滤
	if param.CompanyID > 0 {
		query = query.Where("company_id = ?", param.CompanyID)
	}
	if param.BusinessID > 0 {
		query = query.Where("business_id = ?", param.BusinessID)
	}

	// 过滤条件
	if filter != nil {
		// 状态过滤
		if filter.Status != nil {
			query = query.Where("LOWER(status) = LOWER(?)", *filter.Status)
		}

		// 护理类型过滤
		if len(filter.CareTypes) > 0 {
			query = query.Where("care_type IN ?", filter.CareTypes)
		}

		// 日期范围过滤
		if filter.StartDate != nil {
			query = query.Where("service_date >= ?", *filter.StartDate)
		}
		if filter.EndDate != nil {
			query = query.Where("service_date <= ?", *filter.EndDate)
		}

		// 宠物ID过滤
		if filter.PetID > 0 {
			query = query.Where("pet_id = ?", filter.PetID)
		}
	}

	return query
}

// List 查询报告列表
func (i *reportImpl) List(ctx context.Context, param *BaseParam, filter *Filter) ([]*Report, error) {
	if param == nil {
		return []*Report{}, nil
	}

	query := i.db.WithContext(ctx).Model(&Report{})

	// 基础参数过滤
	if param.CompanyID > 0 {
		query = query.Where("company_id = ?", param.CompanyID)
	}
	if param.BusinessID > 0 {
		query = query.Where("business_id = ?", param.BusinessID)
	}

	// 过滤条件
	if filter != nil {
		// 状态过滤
		if filter.Status != nil {
			query = query.Where("LOWER(status) = LOWER(?)", *filter.Status)
		}

		// 护理类型过滤
		if len(filter.CareTypes) > 0 {
			query = query.Where("care_type IN ?", filter.CareTypes)
		}

		// 日期范围过滤
		if filter.StartDate != nil {
			query = query.Where("service_date >= ?", *filter.StartDate)
		}
		if filter.EndDate != nil {
			query = query.Where("service_date <= ?", *filter.EndDate)
		}

		// 宠物ID过滤
		if filter.PetID > 0 {
			query = query.Where("pet_id = ?", filter.PetID)
		}
	}

	var reports []*Report

	// 设置分页
	if param.PaginationInfo != nil {
		query = query.Offset(int(param.PaginationInfo.Offset)).Limit(int(param.PaginationInfo.Limit))
	}

	// 按更新时间倒序排列
	query = query.Order("update_time DESC")

	if err := query.Find(&reports).Error; err != nil {
		return nil, fmt.Errorf("failed to list reports: %w", err)
	}

	return reports, nil
}

// IncreaseOpenedCount 增加报告打开次数
func (i *reportImpl) IncreaseOpenedCount(ctx context.Context, uuid string) error {
	if uuid == "" {
		return fmt.Errorf("uuid cannot be empty")
	}

	result := i.db.WithContext(ctx).Model(&Report{}).
		Where("uuid = ?", uuid).
		Update("link_opened_count", gorm.Expr("link_opened_count + 1"))

	if result.Error != nil {
		return fmt.Errorf("failed to increase opened count for uuid %s: %w", uuid, result.Error)
	}

	// 如果没有找到记录，不认为是错误，因为可能是无效的 UUID
	if result.RowsAffected == 0 {
		return fmt.Errorf("no report found with uuid: %s", uuid)
	}

	return nil
}

// convertInt64SliceToInterface converts []int64 to []interface{} for GORM IN clause
func convertInt64SliceToInterface(ids []int64) []interface{} {
	result := make([]interface{}, len(ids))
	for i, id := range ids {
		result[i] = id
	}
	return result
}
