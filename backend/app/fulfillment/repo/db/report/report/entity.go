package report

import "time"

const (
	MaxBatchCreateSize = 1000
)

const (
	ColumnID              = "id"
	ColumnBusinessID      = "business_id"
	ColumnCompanyID       = "company_id"
	ColumnCustomerID      = "customer_id"
	ColumnAppointmentID   = "appointment_id"
	ColumnPetID           = "pet_id"
	ColumnCareType        = "care_type"
	ColumnServiceDate     = "service_date"
	ColumnUUID            = "uuid"
	ColumnTemplateJSON    = "template_json"
	ColumnContentJSON     = "content_json"
	ColumnStatus          = "status"
	ColumnLinkOpenedCount = "link_opened_count"
	ColumnTemplateVersion = "template_version"
	ColumnThemeCode       = "theme_code"
	ColumnUpdateBy        = "update_by"
	ColumnCreateTime      = "create_time"
	ColumnUpdateTime      = "update_time"
)

type Report struct {
	ID              int64     `gorm:"column:id;primaryKey;autoIncrement"`
	CompanyID       int64     `gorm:"column:company_id;not null;default:0"`
	BusinessID      int64     `gorm:"column:business_id;not null"`
	CustomerID      int64     `gorm:"column:customer_id;not null"`
	AppointmentID   int64     `gorm:"column:appointment_id;not null"`
	CareType        int32     `gorm:"column:care_type;not null;default:1"`
	PetID           int64     `gorm:"column:pet_id;not null"`
	PetTypeID       int64     `gorm:"column:pet_type_id;not null"`
	UUID            string    `gorm:"column:uuid;unique"`
	TemplateVersion time.Time `gorm:"column:template_version"`
	TemplateJSON    string    `gorm:"column:template_json;type:jsonb;default:'{}'"`
	ContentJSON     string    `gorm:"column:content_json;type:jsonb;default:'{}'"`
	Status          string    `gorm:"column:status;not null;default:'draft'"`
	LinkOpenedCount int32     `gorm:"column:link_opened_count;not null;default:0"`
	ServiceDate     string    `gorm:"column:service_date;not null;default:''"`
	ThemeCode       string    `gorm:"column:theme_code"`
	UpdateBy        int64     `gorm:"column:update_by;not null;default:0"`
	CreateTime      time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP"`
	UpdateTime      time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP"`
}

func (r *Report) TableName() string {
	return "fulfillment_report"
}

// Filter
type Filter struct {
	ID             int64
	CompanyID      int64
	BusinessID     int64
	AppointmentID  int64
	PetID          int64
	CareType       int32
	ServiceDate    string
	Status         *string
	CareTypes      []int32
	StartDate      *string
	EndDate        *string
	AppointmentIDs []int64
}

// BaseParam 基础查询参数
type BaseParam struct {
	CompanyID      int64
	BusinessID     int64
	PaginationInfo *PaginationInfo
}

// PaginationInfo 分页信息
type PaginationInfo struct {
	Offset int32
	Limit  int32
}

// StatusCount 状态计数结果
type StatusCount struct {
	Total      int64
	DraftCount int64
	SentCount  int64
}
