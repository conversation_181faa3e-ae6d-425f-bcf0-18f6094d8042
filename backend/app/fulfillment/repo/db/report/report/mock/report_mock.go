// Code generated by MockGen. DO NOT EDIT.
// Source: ./report.go
//
// Generated by this command:
//
//	mockgen -source=./report.go -destination=./mock/report_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	report "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/report"
	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// BatchCreate mocks base method.
func (m *MockReadWriter) BatchCreate(ctx context.Context, reports []*report.Report) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreate", ctx, reports)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchCreate indicates an expected call of BatchCreate.
func (mr *MockReadWriterMockRecorder) BatchCreate(ctx, reports any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreate", reflect.TypeOf((*MockReadWriter)(nil).BatchCreate), ctx, reports)
}

// BatchDelete mocks base method.
func (m *MockReadWriter) BatchDelete(ctx context.Context, companyID, businessID int64, reportIDs []int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDelete", ctx, companyID, businessID, reportIDs)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDelete indicates an expected call of BatchDelete.
func (mr *MockReadWriterMockRecorder) BatchDelete(ctx, companyID, businessID, reportIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDelete", reflect.TypeOf((*MockReadWriter)(nil).BatchDelete), ctx, companyID, businessID, reportIDs)
}

// Count mocks base method.
func (m *MockReadWriter) Count(ctx context.Context, param *report.BaseParam, filter *report.Filter) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Count", ctx, param, filter)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Count indicates an expected call of Count.
func (mr *MockReadWriterMockRecorder) Count(ctx, param, filter any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Count", reflect.TypeOf((*MockReadWriter)(nil).Count), ctx, param, filter)
}

// CountByStatus mocks base method.
func (m *MockReadWriter) CountByStatus(ctx context.Context, param *report.BaseParam, filter *report.Filter) (*report.StatusCount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountByStatus", ctx, param, filter)
	ret0, _ := ret[0].(*report.StatusCount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountByStatus indicates an expected call of CountByStatus.
func (mr *MockReadWriterMockRecorder) CountByStatus(ctx, param, filter any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountByStatus", reflect.TypeOf((*MockReadWriter)(nil).CountByStatus), ctx, param, filter)
}

// Create mocks base method.
func (m *MockReadWriter) Create(ctx context.Context, arg1 *report.Report) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockReadWriterMockRecorder) Create(ctx, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockReadWriter)(nil).Create), ctx, arg1)
}

// Delete mocks base method.
func (m *MockReadWriter) Delete(ctx context.Context, id int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockReadWriterMockRecorder) Delete(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockReadWriter)(nil).Delete), ctx, id)
}

// FindByID mocks base method.
func (m *MockReadWriter) FindByID(ctx context.Context, id int64) (*report.Report, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByID", ctx, id)
	ret0, _ := ret[0].(*report.Report)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByID indicates an expected call of FindByID.
func (mr *MockReadWriterMockRecorder) FindByID(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByID", reflect.TypeOf((*MockReadWriter)(nil).FindByID), ctx, id)
}

// FindByUUID mocks base method.
func (m *MockReadWriter) FindByUUID(ctx context.Context, uuid string) (*report.Report, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByUUID", ctx, uuid)
	ret0, _ := ret[0].(*report.Report)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByUUID indicates an expected call of FindByUUID.
func (mr *MockReadWriterMockRecorder) FindByUUID(ctx, uuid any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByUUID", reflect.TypeOf((*MockReadWriter)(nil).FindByUUID), ctx, uuid)
}

// FindByUniqueKey mocks base method.
func (m *MockReadWriter) FindByUniqueKey(ctx context.Context, businessID, appointmentID, petID int64, careType int32, serviceDate string) (*report.Report, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByUniqueKey", ctx, businessID, appointmentID, petID, careType, serviceDate)
	ret0, _ := ret[0].(*report.Report)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByUniqueKey indicates an expected call of FindByUniqueKey.
func (mr *MockReadWriterMockRecorder) FindByUniqueKey(ctx, businessID, appointmentID, petID, careType, serviceDate any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByUniqueKey", reflect.TypeOf((*MockReadWriter)(nil).FindByUniqueKey), ctx, businessID, appointmentID, petID, careType, serviceDate)
}

// FindByUniqueKeys mocks base method.
func (m *MockReadWriter) FindByUniqueKeys(ctx context.Context, uniqueKeys []report.Filter) ([]*report.Report, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByUniqueKeys", ctx, uniqueKeys)
	ret0, _ := ret[0].([]*report.Report)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByUniqueKeys indicates an expected call of FindByUniqueKeys.
func (mr *MockReadWriterMockRecorder) FindByUniqueKeys(ctx, uniqueKeys any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByUniqueKeys", reflect.TypeOf((*MockReadWriter)(nil).FindByUniqueKeys), ctx, uniqueKeys)
}

// IncreaseOpenedCount mocks base method.
func (m *MockReadWriter) IncreaseOpenedCount(ctx context.Context, uuid string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncreaseOpenedCount", ctx, uuid)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncreaseOpenedCount indicates an expected call of IncreaseOpenedCount.
func (mr *MockReadWriterMockRecorder) IncreaseOpenedCount(ctx, uuid any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncreaseOpenedCount", reflect.TypeOf((*MockReadWriter)(nil).IncreaseOpenedCount), ctx, uuid)
}

// List mocks base method.
func (m *MockReadWriter) List(ctx context.Context, param *report.BaseParam, filter *report.Filter) ([]*report.Report, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, param, filter)
	ret0, _ := ret[0].([]*report.Report)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockReadWriterMockRecorder) List(ctx, param, filter any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockReadWriter)(nil).List), ctx, param, filter)
}

// Update mocks base method.
func (m *MockReadWriter) Update(ctx context.Context, arg1 *report.Report) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockReadWriterMockRecorder) Update(ctx, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockReadWriter)(nil).Update), ctx, arg1)
}

// Upsert mocks base method.
func (m *MockReadWriter) Upsert(ctx context.Context, arg1 *report.Report) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Upsert", ctx, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Upsert indicates an expected call of Upsert.
func (mr *MockReadWriterMockRecorder) Upsert(ctx, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upsert", reflect.TypeOf((*MockReadWriter)(nil).Upsert), ctx, arg1)
}
