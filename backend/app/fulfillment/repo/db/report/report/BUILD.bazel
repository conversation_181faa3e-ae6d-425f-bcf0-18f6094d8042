load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "report",
    srcs = [
        "entity.go",
        "report.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/report",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/fulfillment/repo/db",
        "//backend/proto/fulfillment/v1:fulfillment",
        "@io_gorm_gorm//:gorm",
        "@io_gorm_gorm//clause",
    ],
)
