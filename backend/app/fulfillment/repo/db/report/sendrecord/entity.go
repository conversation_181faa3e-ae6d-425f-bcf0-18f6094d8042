package sendrecord

import (
	"time"
)

const (
	MaxBatchCreateSize = 1000
)

const (
	ColumnSendRecordID            = "id"
	ColumnSendRecordReportID      = "report_id"
	ColumnSendRecordCompanyID     = "company_id"
	ColumnSendRecordBusinessID    = "business_id"
	ColumnSendRecordAppointmentID = "appointment_id"
	ColumnSendRecordContentJSON   = "content_json"
	ColumnSendRecordPetID         = "pet_id"
	ColumnSendRecordSendMethod    = "send_method"
	ColumnSendRecordSentBy        = "sent_by"
	ColumnSendRecordErrorMessage  = "error_message"
	ColumnSendRecordIsSentSuccess = "is_sent_success"
	ColumnSendRecordSentTime      = "sent_time"
	ColumnSendRecordCreateTime    = "create_time"
	ColumnSendRecordUpdateTime    = "update_time"
)

type SendRecord struct {
	ID            int64     `gorm:"column:id;primaryKey;autoIncrement"`
	ReportID      int64     `gorm:"column:report_id;not null"`
	CompanyID     int64     `gorm:"column:company_id;not null;default:0"`
	BusinessID    int64     `gorm:"column:business_id;not null"`
	AppointmentID int64     `gorm:"column:appointment_id;not null"`
	ContentJSON   string    `gorm:"column:content_json;type:jsonb;default:'{}'"`
	PetID         int64     `gorm:"column:pet_id;not null"`
	SendMethod    int32     `gorm:"column:send_method;not null;default:1"`
	SentTime      time.Time `gorm:"column:sent_time"`
	SentBy        int64     `gorm:"column:sent_by;not null;default:0"`
	ErrorMessage  string    `gorm:"column:error_message;not null;default:''"`
	IsSentSuccess *bool     `gorm:"column:is_sent_success;not null;default:false"`
	CreateTime    time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP"`
	UpdateTime    time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP"`
}

func (r *SendRecord) TableName() string {
	return "fulfillment_report_send_record"
}

// PaginationInfo 分页信息
type PaginationInfo struct {
	Offset int32
	Limit  int32
}

// Filter 过滤条件
type Filter struct {
	ReportID       int64
	SendMethod     int32
	AppointmentIDs []int64
	PetIDs         []int64
	CareTypes      []int32
	SendMethods    []int32
}

type BaseParam struct {
	BusinessID     int64
	CompanyID      int64
	PaginationInfo *PaginationInfo
}
