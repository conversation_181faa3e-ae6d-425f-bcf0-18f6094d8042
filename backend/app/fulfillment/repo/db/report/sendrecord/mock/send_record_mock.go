// Code generated by MockGen. DO NOT EDIT.
// Source: ../send_record.go
//
// Generated by this command:
//
//	mockgen -source=../send_record.go -destination=./send_record_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	sendrecord "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/sendrecord"
	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// BatchCreate mocks base method.
func (m *MockReadWriter) BatchCreate(ctx context.Context, records []*sendrecord.SendRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreate", ctx, records)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchCreate indicates an expected call of BatchCreate.
func (mr *MockReadWriterMockRecorder) BatchCreate(ctx, records any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreate", reflect.TypeOf((*MockReadWriter)(nil).BatchCreate), ctx, records)
}

// Count mocks base method.
func (m *MockReadWriter) Count(ctx context.Context, baseParam *sendrecord.BaseParam, filter *sendrecord.Filter) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Count", ctx, baseParam, filter)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Count indicates an expected call of Count.
func (mr *MockReadWriterMockRecorder) Count(ctx, baseParam, filter any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Count", reflect.TypeOf((*MockReadWriter)(nil).Count), ctx, baseParam, filter)
}

// Create mocks base method.
func (m *MockReadWriter) Create(ctx context.Context, record *sendrecord.SendRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, record)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockReadWriterMockRecorder) Create(ctx, record any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockReadWriter)(nil).Create), ctx, record)
}

// Delete mocks base method.
func (m *MockReadWriter) Delete(ctx context.Context, id int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockReadWriterMockRecorder) Delete(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockReadWriter)(nil).Delete), ctx, id)
}

// FindByID mocks base method.
func (m *MockReadWriter) FindByID(ctx context.Context, id int64) (*sendrecord.SendRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByID", ctx, id)
	ret0, _ := ret[0].(*sendrecord.SendRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByID indicates an expected call of FindByID.
func (mr *MockReadWriterMockRecorder) FindByID(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByID", reflect.TypeOf((*MockReadWriter)(nil).FindByID), ctx, id)
}

// FindByReportIDAndSendMethod mocks base method.
func (m *MockReadWriter) FindByReportIDAndSendMethod(ctx context.Context, reportID int64, sendMethod int32) (*sendrecord.SendRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByReportIDAndSendMethod", ctx, reportID, sendMethod)
	ret0, _ := ret[0].(*sendrecord.SendRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByReportIDAndSendMethod indicates an expected call of FindByReportIDAndSendMethod.
func (mr *MockReadWriterMockRecorder) FindByReportIDAndSendMethod(ctx, reportID, sendMethod any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByReportIDAndSendMethod", reflect.TypeOf((*MockReadWriter)(nil).FindByReportIDAndSendMethod), ctx, reportID, sendMethod)
}

// FindByUniqueKeys mocks base method.
func (m *MockReadWriter) FindByUniqueKeys(ctx context.Context, uniqueKeys []sendrecord.Filter) ([]*sendrecord.SendRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByUniqueKeys", ctx, uniqueKeys)
	ret0, _ := ret[0].([]*sendrecord.SendRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByUniqueKeys indicates an expected call of FindByUniqueKeys.
func (mr *MockReadWriterMockRecorder) FindByUniqueKeys(ctx, uniqueKeys any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByUniqueKeys", reflect.TypeOf((*MockReadWriter)(nil).FindByUniqueKeys), ctx, uniqueKeys)
}

// List mocks base method.
func (m *MockReadWriter) List(ctx context.Context, baseParam *sendrecord.BaseParam, filter *sendrecord.Filter) ([]*sendrecord.SendRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, baseParam, filter)
	ret0, _ := ret[0].([]*sendrecord.SendRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockReadWriterMockRecorder) List(ctx, baseParam, filter any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockReadWriter)(nil).List), ctx, baseParam, filter)
}

// Update mocks base method.
func (m *MockReadWriter) Update(ctx context.Context, record *sendrecord.SendRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, record)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockReadWriterMockRecorder) Update(ctx, record any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockReadWriter)(nil).Update), ctx, record)
}

// Upsert mocks base method.
func (m *MockReadWriter) Upsert(ctx context.Context, record *sendrecord.SendRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Upsert", ctx, record)
	ret0, _ := ret[0].(error)
	return ret0
}

// Upsert indicates an expected call of Upsert.
func (mr *MockReadWriterMockRecorder) Upsert(ctx, record any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upsert", reflect.TypeOf((*MockReadWriter)(nil).Upsert), ctx, record)
}
