package question

import "time"

const (
	MaxBatchCreateSize = 1000
)

const (
	ColumnQuestionID                = "id"
	ColumnQuestionCompanyID         = "company_id"
	ColumnQuestionBusinessID        = "business_id"
	ColumnQuestionCareType          = "care_type"
	ColumnQuestionCategory          = "category"
	ColumnQuestionType              = "type"
	ColumnQuestionKey               = "key"
	ColumnQuestionTitle             = "title"
	ColumnQuestionExtraJSON         = "extra_json"
	ColumnQuestionIsDefault         = "is_default"
	ColumnQuestionIsRequired        = "is_required"
	ColumnQuestionIsTypeEditable    = "is_type_editable"
	ColumnQuestionIsTitleEditable   = "is_title_editable"
	ColumnQuestionIsOptionsEditable = "is_options_editable"
	ColumnQuestionSort              = "sort"
	ColumnQuestionCreateTime        = "create_time"
	ColumnQuestionUpdateTime        = "update_time"
)

type Question struct {
	ID                int64     `gorm:"column:id;primaryKey;autoIncrement"`
	CompanyID         int64     `gorm:"column:company_id;not null;default:0"`
	BusinessID        int64     `gorm:"column:business_id;not null"`
	CareType          int32     `gorm:"column:care_type;not null;default:0"`
	Category          int32     `gorm:"column:category;not null;default:1"`
	Type              string    `gorm:"column:type;not null"`
	Key               string    `gorm:"column:key"`
	Title             string    `gorm:"column:title;not null"`
	ExtraJSON         string    `gorm:"column:extra_json;not null;type:jsonb;default:'{}'"`
	IsDefault         *bool     `gorm:"column:is_default;not null;default:false"`
	IsRequired        *bool     `gorm:"column:is_required;not null;default:true"`
	IsTypeEditable    *bool     `gorm:"column:is_type_editable;not null;default:true"`
	IsTitleEditable   *bool     `gorm:"column:is_title_editable;not null;default:true"`
	IsOptionsEditable *bool     `gorm:"column:is_options_editable;not null;default:true"`
	Sort              *int32    `gorm:"column:sort;not null;default:0;index:idx_question_template"`
	CreateTime        time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP"`
	UpdateTime        time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP"`
}

func (q *Question) TableName() string {
	return "fulfillment_report_question"
}

type Filter struct {
	CompanyID  *int64
	BusinessID *int64
	CareType   *int32
	IDList     []int64
}
