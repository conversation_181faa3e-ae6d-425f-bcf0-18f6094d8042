// Code generated by MockGen. DO NOT EDIT.
// Source: ../question.go
//
// Generated by this command:
//
//	mockgen -source=../question.go -destination=./question_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	question "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/question"
	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// BatchCreate mocks base method.
func (m *MockReadWriter) BatchCreate(ctx context.Context, questions []*question.Question) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreate", ctx, questions)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchCreate indicates an expected call of BatchCreate.
func (mr *MockReadWriterMockRecorder) BatchCreate(ctx, questions any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreate", reflect.TypeOf((*MockReadWriter)(nil).BatchCreate), ctx, questions)
}

// BatchDelete mocks base method.
func (m *MockReadWriter) BatchDelete(ctx context.Context, filter question.Filter) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDelete", ctx, filter)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDelete indicates an expected call of BatchDelete.
func (mr *MockReadWriterMockRecorder) BatchDelete(ctx, filter any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDelete", reflect.TypeOf((*MockReadWriter)(nil).BatchDelete), ctx, filter)
}

// BatchUpdate mocks base method.
func (m *MockReadWriter) BatchUpdate(ctx context.Context, questions []*question.Question) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdate", ctx, questions)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpdate indicates an expected call of BatchUpdate.
func (mr *MockReadWriterMockRecorder) BatchUpdate(ctx, questions any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdate", reflect.TypeOf((*MockReadWriter)(nil).BatchUpdate), ctx, questions)
}

// BatchUpsert mocks base method.
func (m *MockReadWriter) BatchUpsert(ctx context.Context, questions []*question.Question) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpsert", ctx, questions)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpsert indicates an expected call of BatchUpsert.
func (mr *MockReadWriterMockRecorder) BatchUpsert(ctx, questions any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpsert", reflect.TypeOf((*MockReadWriter)(nil).BatchUpsert), ctx, questions)
}

// Create mocks base method.
func (m *MockReadWriter) Create(ctx context.Context, arg1 *question.Question) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockReadWriterMockRecorder) Create(ctx, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockReadWriter)(nil).Create), ctx, arg1)
}

// Delete mocks base method.
func (m *MockReadWriter) Delete(ctx context.Context, id int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockReadWriterMockRecorder) Delete(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockReadWriter)(nil).Delete), ctx, id)
}

// FindByFilter mocks base method.
func (m *MockReadWriter) FindByFilter(ctx context.Context, filter question.Filter) ([]*question.Question, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByFilter", ctx, filter)
	ret0, _ := ret[0].([]*question.Question)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByFilter indicates an expected call of FindByFilter.
func (mr *MockReadWriterMockRecorder) FindByFilter(ctx, filter any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByFilter", reflect.TypeOf((*MockReadWriter)(nil).FindByFilter), ctx, filter)
}

// FindByFilterAndTitles mocks base method.
func (m *MockReadWriter) FindByFilterAndTitles(ctx context.Context, filter question.Filter, titles []string) ([]*question.Question, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByFilterAndTitles", ctx, filter, titles)
	ret0, _ := ret[0].([]*question.Question)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByFilterAndTitles indicates an expected call of FindByFilterAndTitles.
func (mr *MockReadWriterMockRecorder) FindByFilterAndTitles(ctx, filter, titles any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByFilterAndTitles", reflect.TypeOf((*MockReadWriter)(nil).FindByFilterAndTitles), ctx, filter, titles)
}

// FindByFilters mocks base method.
func (m *MockReadWriter) FindByFilters(ctx context.Context, filters []question.Filter) ([]*question.Question, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByFilters", ctx, filters)
	ret0, _ := ret[0].([]*question.Question)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByFilters indicates an expected call of FindByFilters.
func (mr *MockReadWriterMockRecorder) FindByFilters(ctx, filters any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByFilters", reflect.TypeOf((*MockReadWriter)(nil).FindByFilters), ctx, filters)
}

// FindByID mocks base method.
func (m *MockReadWriter) FindByID(ctx context.Context, id int64) (*question.Question, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByID", ctx, id)
	ret0, _ := ret[0].(*question.Question)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByID indicates an expected call of FindByID.
func (mr *MockReadWriterMockRecorder) FindByID(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByID", reflect.TypeOf((*MockReadWriter)(nil).FindByID), ctx, id)
}

// Update mocks base method.
func (m *MockReadWriter) Update(ctx context.Context, arg1 *question.Question) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockReadWriterMockRecorder) Update(ctx, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockReadWriter)(nil).Update), ctx, arg1)
}
