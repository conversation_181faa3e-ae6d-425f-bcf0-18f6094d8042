load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "template",
    srcs = [
        "entity.go",
        "template.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/template",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/fulfillment/repo/db",
        "@io_gorm_gorm//:gorm",
        "@io_gorm_gorm//clause",
    ],
)
