package template

import "time"

const (
	MaxBatchCreateSize = 1000
)

const (
	ColumnTemplateID                            = "id"
	ColumnTemplateCompanyID                     = "company_id"
	ColumnTemplateBusinessID                    = "business_id"
	ColumnTemplateCareType                      = "care_type"
	ColumnTemplateThankYouMessage               = "thank_you_message"
	ColumnTemplateThemeColor                    = "theme_color"
	ColumnTemplateLightThemeColor               = "light_theme_color"
	ColumnTemplateShowShowcase                  = "show_showcase"
	ColumnTemplateShowOverallFeedback           = "show_overall_feedback"
	ColumnTemplateShowCustomizedFeedback        = "show_customized_feedback"
	ColumnTemplateShowPetCondition              = "show_pet_condition"
	ColumnTemplateShowServiceStaffName          = "show_service_staff_name"
	ColumnTemplateShowNextAppointment           = "show_next_appointment"
	ColumnTemplateNextAppointmentDateFormatType = "next_appointment_date_format_type"
	ColumnTemplateShowReviewBooster             = "show_review_booster"
	ColumnTemplateShowYelpReview                = "show_yelp_review"
	ColumnTemplateShowGoogleReview              = "show_google_review"
	ColumnTemplateShowFacebookReview            = "show_facebook_review"
	ColumnTemplateLastPublishTime               = "last_publish_time"
	ColumnTemplateTitle                         = "title"
	ColumnTemplateThemeCode                     = "theme_code"
	ColumnTemplateUpdateBy                      = "update_by"
	ColumnTemplateCreateTime                    = "create_time"
	ColumnTemplateUpdateTime                    = "update_time"
)

//nolint:lll
type Template struct {
	ID                            int64     `gorm:"column:id;primaryKey;autoIncrement"`
	CompanyID                     int64     `gorm:"column:company_id;not null;default:0"`
	BusinessID                    int64     `gorm:"column:business_id;not null;uniqueIndex:udx_biz_care"`
	CareType                      int32     `gorm:"column:care_type;not null;default:1;uniqueIndex:udx_biz_care"`
	ThankYouMessage               string    `gorm:"column:thank_you_message;not null;default:'Thanks for your trust and support'"`
	ThemeColor                    string    `gorm:"column:theme_color;not null;default:'#F96B18'"`
	LightThemeColor               string    `gorm:"column:light_theme_color;not null;default:'#FEEFE6'"`
	ShowShowcase                  *bool     `gorm:"column:show_showcase;not null;default:true"`
	ShowOverallFeedback           *bool     `gorm:"column:show_overall_feedback;not null;default:true"`
	ShowCustomizedFeedback        *bool     `gorm:"column:show_customized_feedback;not null;default:true"`
	ShowPetCondition              *bool     `gorm:"column:show_pet_condition;not null;default:true"`
	ShowServiceStaffName          *bool     `gorm:"column:show_service_staff_name;not null;default:false"`
	ShowNextAppointment           *bool     `gorm:"column:show_next_appointment;not null;default:true"`
	NextAppointmentDateFormatType int32     `gorm:"column:next_appointment_date_format_type;not null;default:2"`
	ShowReviewBooster             *bool     `gorm:"column:show_review_booster;not null;default:true"`
	ShowYelpReview                *bool     `gorm:"column:show_yelp_review;not null;default:false"`
	ShowGoogleReview              *bool     `gorm:"column:show_google_review;not null;default:false"`
	ShowFacebookReview            *bool     `gorm:"column:show_facebook_review;not null;default:false"`
	LastPublishTime               time.Time `gorm:"column:last_publish_time"`
	Title                         string    `gorm:"column:title;not null;default:'Service Report'"`
	ThemeCode                     string    `gorm:"column:theme_code;not null;default:'Default'"`
	UpdateBy                      int64     `gorm:"column:update_by;not null;default:0"`
	CreateTime                    time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP"`
	UpdateTime                    time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP"`
}

func (t *Template) TableName() string {
	return "fulfillment_report_template"
}

type Filter struct {
	CompanyID  int64
	BusinessID *int64
	CareType   int32
}
