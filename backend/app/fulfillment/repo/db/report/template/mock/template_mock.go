// Code generated by MockGen. DO NOT EDIT.
// Source: ../template.go
//
// Generated by this command:
//
//	mockgen -source=../template.go -destination=./template_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	template "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db/report/template"
	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// BatchCreate mocks base method.
func (m *MockReadWriter) BatchCreate(ctx context.Context, templates []*template.Template) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreate", ctx, templates)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchCreate indicates an expected call of BatchCreate.
func (mr *MockReadWriterMockRecorder) BatchCreate(ctx, templates any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreate", reflect.TypeOf((*MockReadWriter)(nil).BatchCreate), ctx, templates)
}

// Create mocks base method.
func (m *MockReadWriter) Create(ctx context.Context, arg1 *template.Template) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockReadWriterMockRecorder) Create(ctx, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockReadWriter)(nil).Create), ctx, arg1)
}

// Delete mocks base method.
func (m *MockReadWriter) Delete(ctx context.Context, id int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockReadWriterMockRecorder) Delete(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockReadWriter)(nil).Delete), ctx, id)
}

// FindByID mocks base method.
func (m *MockReadWriter) FindByID(ctx context.Context, id int64) (*template.Template, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByID", ctx, id)
	ret0, _ := ret[0].(*template.Template)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByID indicates an expected call of FindByID.
func (mr *MockReadWriterMockRecorder) FindByID(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByID", reflect.TypeOf((*MockReadWriter)(nil).FindByID), ctx, id)
}

// FindByUniqueKey mocks base method.
func (m *MockReadWriter) FindByUniqueKey(ctx context.Context, companyID, businessID int64, careType int32) (*template.Template, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByUniqueKey", ctx, companyID, businessID, careType)
	ret0, _ := ret[0].(*template.Template)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByUniqueKey indicates an expected call of FindByUniqueKey.
func (mr *MockReadWriterMockRecorder) FindByUniqueKey(ctx, companyID, businessID, careType any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByUniqueKey", reflect.TypeOf((*MockReadWriter)(nil).FindByUniqueKey), ctx, companyID, businessID, careType)
}

// FindByUniqueKeys mocks base method.
func (m *MockReadWriter) FindByUniqueKeys(ctx context.Context, uniqueKeys []template.Filter) ([]*template.Template, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByUniqueKeys", ctx, uniqueKeys)
	ret0, _ := ret[0].([]*template.Template)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByUniqueKeys indicates an expected call of FindByUniqueKeys.
func (mr *MockReadWriterMockRecorder) FindByUniqueKeys(ctx, uniqueKeys any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByUniqueKeys", reflect.TypeOf((*MockReadWriter)(nil).FindByUniqueKeys), ctx, uniqueKeys)
}

// Update mocks base method.
func (m *MockReadWriter) Update(ctx context.Context, arg1 *template.Template) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockReadWriterMockRecorder) Update(ctx, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockReadWriter)(nil).Update), ctx, arg1)
}

// Upsert mocks base method.
func (m *MockReadWriter) Upsert(ctx context.Context, arg1 *template.Template) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Upsert", ctx, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Upsert indicates an expected call of Upsert.
func (mr *MockReadWriterMockRecorder) Upsert(ctx, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upsert", reflect.TypeOf((*MockReadWriter)(nil).Upsert), ctx, arg1)
}
