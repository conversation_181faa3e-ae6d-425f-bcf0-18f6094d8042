// Code generated by MockGen. DO NOT EDIT.
// Source: ./customer/customer.go
//
// Generated by this command:
//
//	mockgen -source=./customer/customer.go -destination=./customer/mock/customer_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// GetCustomerInfo mocks base method.
func (m *MockReadWriter) GetCustomerInfo(ctx context.Context, companyID, customerID int64) (*businesscustomerpb.BusinessCustomerModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCustomerInfo", ctx, companyID, customerID)
	ret0, _ := ret[0].(*businesscustomerpb.BusinessCustomerModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCustomerInfo indicates an expected call of GetCustomerInfo.
func (mr *MockReadWriterMockRecorder) GetCustomerInfo(ctx, companyID, customerID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCustomerInfo", reflect.TypeOf((*MockReadWriter)(nil).GetCustomerInfo), ctx, companyID, customerID)
}
