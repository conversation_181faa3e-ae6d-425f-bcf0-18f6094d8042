// Code generated by MockGen. DO NOT EDIT.
// Source: ./customer/customer.go
//
// Generated by this command:
//
//	mockgen -source=./customer/customer.go -destination=./customer/mock/customer_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	businesscustomersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1"
	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// GetCustomerInfo mocks base method.
func (m *MockReadWriter) GetCustomerInfo(ctx context.Context, companyID, customerID int64) (*businesscustomerpb.BusinessCustomerModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCustomerInfo", ctx, companyID, customerID)
	ret0, _ := ret[0].(*businesscustomerpb.BusinessCustomerModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCustomerInfo indicates an expected call of GetCustomerInfo.
func (mr *MockReadWriterMockRecorder) GetCustomerInfo(ctx, companyID, customerID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCustomerInfo", reflect.TypeOf((*MockReadWriter)(nil).GetCustomerInfo), ctx, companyID, customerID)
}

// BatchCreateFeedingSchedule mocks base method.
func (m *MockReadWriter) BatchCreateFeedingSchedule(ctx context.Context, companyID int64, feedingSchedules []*businesscustomerpb.BusinessPetFeedingScheduleDef) (*businesscustomersvcpb.BatchCreateFeedingScheduleResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreateFeedingSchedule", ctx, companyID, feedingSchedules)
	ret0, _ := ret[0].(*businesscustomersvcpb.BatchCreateFeedingScheduleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCreateFeedingSchedule indicates an expected call of BatchCreateFeedingSchedule.
func (mr *MockReadWriterMockRecorder) BatchCreateFeedingSchedule(ctx, companyID, feedingSchedules any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreateFeedingSchedule", reflect.TypeOf((*MockReadWriter)(nil).BatchCreateFeedingSchedule), ctx, companyID, feedingSchedules)
}

// BatchCreateMedicationSchedule mocks base method.
func (m *MockReadWriter) BatchCreateMedicationSchedule(ctx context.Context, companyID int64, medicationSchedules []*businesscustomerpb.BusinessPetMedicationScheduleDef) (*businesscustomersvcpb.BatchCreateMedicationScheduleResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreateMedicationSchedule", ctx, companyID, medicationSchedules)
	ret0, _ := ret[0].(*businesscustomersvcpb.BatchCreateMedicationScheduleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCreateMedicationSchedule indicates an expected call of BatchCreateMedicationSchedule.
func (mr *MockReadWriterMockRecorder) BatchCreateMedicationSchedule(ctx, companyID, medicationSchedules any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreateMedicationSchedule", reflect.TypeOf((*MockReadWriter)(nil).BatchCreateMedicationSchedule), ctx, companyID, medicationSchedules)
}

// ListPetFeedingSchedule mocks base method.
func (m *MockReadWriter) ListPetFeedingSchedule(ctx context.Context, companyID int64, petIDs []int64) (*businesscustomersvcpb.ListPetFeedingScheduleResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListPetFeedingSchedule", ctx, companyID, petIDs)
	ret0, _ := ret[0].(*businesscustomersvcpb.ListPetFeedingScheduleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListPetFeedingSchedule indicates an expected call of ListPetFeedingSchedule.
func (mr *MockReadWriterMockRecorder) ListPetFeedingSchedule(ctx, companyID, petIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPetFeedingSchedule", reflect.TypeOf((*MockReadWriter)(nil).ListPetFeedingSchedule), ctx, companyID, petIDs)
}

// ListPetMedicationSchedule mocks base method.
func (m *MockReadWriter) ListPetMedicationSchedule(ctx context.Context, companyID int64, petIDs []int64) (*businesscustomersvcpb.ListPetMedicationScheduleResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListPetMedicationSchedule", ctx, companyID, petIDs)
	ret0, _ := ret[0].(*businesscustomersvcpb.ListPetMedicationScheduleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListPetMedicationSchedule indicates an expected call of ListPetMedicationSchedule.
func (mr *MockReadWriterMockRecorder) ListPetMedicationSchedule(ctx, companyID, petIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPetMedicationSchedule", reflect.TypeOf((*MockReadWriter)(nil).ListPetMedicationSchedule), ctx, companyID, petIDs)
}
