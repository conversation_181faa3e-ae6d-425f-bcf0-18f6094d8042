package customer

import (
	"context"

	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	businesscustomersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ReadWriter interface {
	GetCustomerInfo(ctx context.Context, companyID, customerID int64) (*businesscustomerpb.BusinessCustomerModel, error)
	BatchCreateFeedingSchedule(ctx context.Context, companyID int64,
		feedingSchedules []*businesscustomerpb.BusinessPetFeedingScheduleDef) (
		*businesscustomersvcpb.BatchCreateFeedingScheduleResponse, error)
	BatchCreateMedicationSchedule(ctx context.Context, companyID int64,
		medicationSchedules []*businesscustomerpb.BusinessPetMedicationScheduleDef) (
		*businesscustomersvcpb.BatchCreateMedicationScheduleResponse, error)
	ListPetFeedingSchedule(ctx context.Context, companyID int64,
		petIDs []int64) (*businesscustomersvcpb.ListPetFeedingScheduleResponse, error)
	ListPetMedicationSchedule(ctx context.Context, companyID int64,
		petIDs []int64) (*businesscustomersvcpb.ListPetMedicationScheduleResponse, error)
}

type impl struct {
	customer   businesscustomersvcpb.BusinessCustomerServiceClient
	feeding    businesscustomersvcpb.BusinessPetFeedingScheduleServiceClient
	medication businesscustomersvcpb.BusinessPetMedicationScheduleServiceClient
}

func New() ReadWriter {
	return &impl{
		customer: grpc.NewClient("moego-svc-business-customer",
			businesscustomersvcpb.NewBusinessCustomerServiceClient),
		feeding: grpc.NewClient("moego-svc-business-customer",
			businesscustomersvcpb.NewBusinessPetFeedingScheduleServiceClient),
		medication: grpc.NewClient("moego-svc-business-customer",
			businesscustomersvcpb.NewBusinessPetMedicationScheduleServiceClient),
	}
}

func (i *impl) GetCustomerInfo(ctx context.Context, companyID, customerID int64) (
	*businesscustomerpb.BusinessCustomerModel, error) {
	customerInfoResp, err := i.customer.GetCustomer(ctx, &businesscustomersvcpb.GetCustomerRequest{
		Tenant:     &organizationpb.Tenant{CompanyId: companyID},
		Identifier: &businesscustomersvcpb.GetCustomerRequest_Id{Id: customerID},
	})
	if err != nil {
		log.ErrorContextf(ctx, "GetCustomerInfo err:%+v, customerID:%d", err, customerID)

		return nil, err
	}

	return customerInfoResp.GetCustomer(), nil
}

func (i *impl) BatchCreateFeedingSchedule(ctx context.Context, companyID int64,
	feedingSchedules []*businesscustomerpb.BusinessPetFeedingScheduleDef) (
	*businesscustomersvcpb.BatchCreateFeedingScheduleResponse, error) {
	feedingMedicationResp, err := i.feeding.BatchCreateFeedingSchedule(ctx,
		&businesscustomersvcpb.BatchCreateFeedingScheduleRequest{
			CompanyId:        companyID,
			FeedingSchedules: feedingSchedules,
		})
	if err != nil {
		log.ErrorContextf(ctx, "GetFeedingMedication err, err:%+v", err)

		return nil, err
	}

	return feedingMedicationResp, nil
}

func (i *impl) BatchCreateMedicationSchedule(ctx context.Context, companyID int64,
	medicationSchedules []*businesscustomerpb.BusinessPetMedicationScheduleDef) (
	*businesscustomersvcpb.BatchCreateMedicationScheduleResponse, error) {
	medicationScheduleResp, err := i.medication.BatchCreateMedicationSchedule(ctx,
		&businesscustomersvcpb.BatchCreateMedicationScheduleRequest{
			CompanyId:           companyID,
			MedicationSchedules: medicationSchedules,
		})
	if err != nil {
		log.ErrorContextf(ctx, "BatchCreateMedicationSchedule err, err:%+v", err)

		return nil, err
	}

	return medicationScheduleResp, nil
}

func (i *impl) ListPetFeedingSchedule(ctx context.Context, companyID int64,
	petIDs []int64) (*businesscustomersvcpb.ListPetFeedingScheduleResponse, error) {
	feedingScheduleResp, err := i.feeding.ListPetFeedingSchedule(ctx,
		&businesscustomersvcpb.ListPetFeedingScheduleRequest{
			CompanyId: companyID,
			PetIds:    petIDs,
		})
	if err != nil {
		log.ErrorContextf(ctx, "ListPetFeedingSchedule err, err:%+v", err)

		return nil, err
	}

	return feedingScheduleResp, nil
}

func (i *impl) ListPetMedicationSchedule(ctx context.Context, companyID int64,
	petIDs []int64) (*businesscustomersvcpb.ListPetMedicationScheduleResponse, error) {
	medicationScheduleResp, err := i.medication.ListPetMedicationSchedule(ctx,
		&businesscustomersvcpb.ListPetMedicationScheduleRequest{
			CompanyId: companyID,
			PetIds:    petIDs,
		})
	if err != nil {
		log.ErrorContextf(ctx, "ListPetMedicationSchedule err, err:%+v", err)

		return nil, err
	}

	return medicationScheduleResp, nil
}
