package customer

import (
	"context"

	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	businesscustomersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ReadWriter interface {
	GetCustomerInfo(ctx context.Context, companyID, customerID int64) (*businesscustomerpb.BusinessCustomerModel, error)
}

type impl struct {
	customer businesscustomersvcpb.BusinessCustomerServiceClient
}

func New() ReadWriter {
	return &impl{
		customer: grpc.NewClient("moego-svc-business-customer", businesscustomersvcpb.NewBusinessCustomerServiceClient),
	}
}

func (i *impl) GetCustomerInfo(ctx context.Context, companyID, customerID int64) (
	*businesscustomerpb.BusinessCustomerModel, error) {
	customerInfoResp, err := i.customer.GetCustomer(ctx, &businesscustomersvcpb.GetCustomerRequest{
		Tenant:     &organizationpb.Tenant{CompanyId: companyID},
		Identifier: &businesscustomersvcpb.GetCustomerRequest_Id{Id: customerID},
	})
	if err != nil {
		log.ErrorContextf(ctx, "GetCustomerInfo err:%+v, customerID:%d", err, customerID)

		return nil, err
	}

	return customerInfoResp.GetCustomer(), nil
}
