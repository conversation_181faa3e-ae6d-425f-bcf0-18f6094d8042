package offering

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

type CareTypeReader interface {
	GetCareType(ctx context.Context, careTypeID int64) (*offeringpb.CareType, error)
}

type careTypeReader struct {
	careTypeCli offeringpb.CareTypeServiceClient
}

func NewCareTypeReader() CareTypeReader {
	return &careTypeReader{
		careTypeCli: grpc.NewClient("moego-offering", offeringpb.NewCareTypeServiceClient),
	}
}

func (r *careTypeReader) GetCareType(ctx context.Context, careTypeID int64) (*offeringpb.CareType, error) {
	careTypeResp, err := r.careTypeCli.GetCareType(ctx, &offeringpb.GetCareTypeRequest{
		Id: careTypeID,
	})
	if err != nil {
		return nil, err
	}

	return careTypeResp.GetCareType(), nil
}
