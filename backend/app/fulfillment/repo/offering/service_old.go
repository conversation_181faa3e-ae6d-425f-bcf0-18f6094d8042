package offering

import (
	"context"

	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
)

type ServiceOldReadWriter interface {
	BatchGetServiceInfo(ctx context.Context, companyID int64, serviceIDs []int64) (
		[]*offeringpb.ServiceBriefView, error)
}

type serviceOldReadWriterImpl struct {
	service offeringvcpb.ServiceManagementServiceClient
}

func NewServiceOldReadWriter() ServiceOldReadWriter {
	return &serviceOldReadWriterImpl{
		service: grpc.NewClient("moego-svc-offering", offeringvcpb.NewServiceManagementServiceClient),
	}
}

func (i *serviceOldReadWriterImpl) BatchGetServiceInfo(ctx context.Context, companyID int64, serviceIDs []int64) (
	[]*offeringpb.ServiceBriefView, error) {
	resp, err := i.service.GetServiceListByIds(ctx, &offeringvcpb.GetServiceListByIdsRequest{
		CompanyId:  &companyID,
		ServiceIds: serviceIDs,
	})
	if err != nil {
		return nil, err
	}

	return resp.GetServices(), nil
}
