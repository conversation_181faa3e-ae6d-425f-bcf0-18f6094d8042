// Code generated by MockGen. DO NOT EDIT.
// Source: ./care_type.go
//
// Generated by this command:
//
//	mockgen -source=./care_type.go -destination=./mock/care_type_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	gomock "go.uber.org/mock/gomock"
)

// MockCareTypeReader is a mock of CareTypeReader interface.
type MockCareTypeReader struct {
	ctrl     *gomock.Controller
	recorder *MockCareTypeReaderMockRecorder
	isgomock struct{}
}

// MockCareTypeReaderMockRecorder is the mock recorder for MockCareTypeReader.
type MockCareTypeReaderMockRecorder struct {
	mock *MockCareTypeReader
}

// NewMockCareTypeReader creates a new mock instance.
func NewMockCareTypeReader(ctrl *gomock.Controller) *MockCareTypeReader {
	mock := &MockCareTypeReader{ctrl: ctrl}
	mock.recorder = &MockCareTypeReaderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCareTypeReader) EXPECT() *MockCareTypeReaderMockRecorder {
	return m.recorder
}

// GetCareType mocks base method.
func (m *MockCareTypeReader) GetCareType(ctx context.Context, careTypeID int64) (*offeringpb.CareType, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCareType", ctx, careTypeID)
	ret0, _ := ret[0].(*offeringpb.CareType)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCareType indicates an expected call of GetCareType.
func (mr *MockCareTypeReaderMockRecorder) GetCareType(ctx, careTypeID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCareType", reflect.TypeOf((*MockCareTypeReader)(nil).GetCareType), ctx, careTypeID)
}
