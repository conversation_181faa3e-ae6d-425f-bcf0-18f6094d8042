// Code generated by MockGen. DO NOT EDIT.
// Source: ./offering/service_old.go
//
// Generated by this command:
//
//	mockgen -source=./offering/service_old.go -destination=./offering/mock/service_old_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	gomock "go.uber.org/mock/gomock"
)

// MockServiceOldReadWriter is a mock of ServiceOldReadWriter interface.
type MockServiceOldReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockServiceOldReadWriterMockRecorder
	isgomock struct{}
}

// MockServiceOldReadWriterMockRecorder is the mock recorder for MockServiceOldReadWriter.
type MockServiceOldReadWriterMockRecorder struct {
	mock *MockServiceOldReadWriter
}

// NewMockServiceOldReadWriter creates a new mock instance.
func NewMockServiceOldReadWriter(ctrl *gomock.Controller) *MockServiceOldReadWriter {
	mock := &MockServiceOldReadWriter{ctrl: ctrl}
	mock.recorder = &MockServiceOldReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceOldReadWriter) EXPECT() *MockServiceOldReadWriterMockRecorder {
	return m.recorder
}

// BatchGetServiceInfo mocks base method.
func (m *MockServiceOldReadWriter) BatchGetServiceInfo(ctx context.Context, companyID int64, serviceIDs []int64) ([]*offeringpb.ServiceBriefView, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetServiceInfo", ctx, companyID, serviceIDs)
	ret0, _ := ret[0].([]*offeringpb.ServiceBriefView)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetServiceInfo indicates an expected call of BatchGetServiceInfo.
func (mr *MockServiceOldReadWriterMockRecorder) BatchGetServiceInfo(ctx, companyID, serviceIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetServiceInfo", reflect.TypeOf((*MockServiceOldReadWriter)(nil).BatchGetServiceInfo), ctx, companyID, serviceIDs)
}
