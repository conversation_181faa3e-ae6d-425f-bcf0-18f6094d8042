package pet

import (
	"context"

	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	businesscustomersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ReadWriter interface {
	GetPetInfo(ctx context.Context, companyID, petID int64) (*businesscustomerpb.BusinessCustomerPetInfoModel, error)
	BatchGetPetInfo(ctx context.Context, companyID int64, petIDs []int64) (
		[]*businesscustomerpb.BusinessCustomerPetInfoModel, error)
}

type impl struct {
	pet businesscustomersvcpb.BusinessCustomerPetServiceClient
}

func New() ReadWriter {
	return &impl{
		pet: grpc.NewClient("moego-svc-business-customer", businesscustomersvcpb.NewBusinessCustomerPetServiceClient),
	}
}

func (i *impl) GetPetInfo(ctx context.Context, companyID, petID int64) (
	*businesscustomerpb.BusinessCustomerPetInfoModel, error) {
	petInfoResp, err := i.pet.GetPetInfo(ctx, &businesscustomersvcpb.GetPetInfoRequest{
		Tenant: &organizationpb.Tenant{
			CompanyId: companyID,
		},
		Id: petID,
	})
	if err != nil {
		log.ErrorContextf(ctx, "GetPetInfo err:%+v, petID:%d", err, petID)

		return nil, err
	}

	return petInfoResp.GetPet(), nil
}

func (i *impl) BatchGetPetInfo(ctx context.Context, companyID int64, petIDs []int64) (
	[]*businesscustomerpb.BusinessCustomerPetInfoModel, error) {
	petInfoResp, err := i.pet.BatchGetPetInfo(ctx, &businesscustomersvcpb.BatchGetPetInfoRequest{
		Tenant: &organizationpb.Tenant{
			CompanyId: companyID,
		},
		Ids: petIDs,
	})
	if err != nil {
		log.ErrorContextf(ctx, "BatchGetPetInfo err:%+v, petIDs:%v", err, petIDs)

		return nil, err
	}

	return petInfoResp.GetPets(), nil
}
