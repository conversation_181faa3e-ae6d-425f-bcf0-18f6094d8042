// Code generated by MockGen. DO NOT EDIT.
// Source: ./pet/pet.go
//
// Generated by this command:
//
//	mockgen -source=./pet/pet.go -destination=./pet/mock/pet_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// BatchGetPetInfo mocks base method.
func (m *MockReadWriter) BatchGetPetInfo(ctx context.Context, companyID int64, petIDs []int64) ([]*businesscustomerpb.BusinessCustomerPetInfoModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetPetInfo", ctx, companyID, petIDs)
	ret0, _ := ret[0].([]*businesscustomerpb.BusinessCustomerPetInfoModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetPetInfo indicates an expected call of BatchGetPetInfo.
func (mr *MockReadWriterMockRecorder) BatchGetPetInfo(ctx, companyID, petIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPetInfo", reflect.TypeOf((*MockReadWriter)(nil).BatchGetPetInfo), ctx, companyID, petIDs)
}

// GetPetInfo mocks base method.
func (m *MockReadWriter) GetPetInfo(ctx context.Context, companyID, petID int64) (*businesscustomerpb.BusinessCustomerPetInfoModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPetInfo", ctx, companyID, petID)
	ret0, _ := ret[0].(*businesscustomerpb.BusinessCustomerPetInfoModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPetInfo indicates an expected call of GetPetInfo.
func (mr *MockReadWriterMockRecorder) GetPetInfo(ctx, companyID, petID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPetInfo", reflect.TypeOf((*MockReadWriter)(nil).GetPetInfo), ctx, companyID, petID)
}
