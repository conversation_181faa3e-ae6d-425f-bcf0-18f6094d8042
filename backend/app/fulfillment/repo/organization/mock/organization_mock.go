// Code generated by MockGen. DO NOT EDIT.
// Source: ./organization/organization.go
//
// Generated by this command:
//
//	mockgen -source=./organization/organization.go -destination=./organization/mock/organization_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// GetBusinessDetail mocks base method.
func (m *MockReadWriter) GetBusinessDetail(ctx context.Context, companyID, businessID int64) (*organizationpb.LocationModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBusinessDetail", ctx, companyID, businessID)
	ret0, _ := ret[0].(*organizationpb.LocationModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBusinessDetail indicates an expected call of GetBusinessDetail.
func (mr *MockReadWriterMockRecorder) GetBusinessDetail(ctx, companyID, businessID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBusinessDetail", reflect.TypeOf((*MockReadWriter)(nil).GetBusinessDetail), ctx, companyID, businessID)
}

// GetCompanyPreferenceSetting mocks base method.
func (m *MockReadWriter) GetCompanyPreferenceSetting(ctx context.Context, companyID int64) (*organizationpb.CompanyPreferenceSettingModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCompanyPreferenceSetting", ctx, companyID)
	ret0, _ := ret[0].(*organizationpb.CompanyPreferenceSettingModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCompanyPreferenceSetting indicates an expected call of GetCompanyPreferenceSetting.
func (mr *MockReadWriterMockRecorder) GetCompanyPreferenceSetting(ctx, companyID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCompanyPreferenceSetting", reflect.TypeOf((*MockReadWriter)(nil).GetCompanyPreferenceSetting), ctx, companyID)
}
