package organization

import (
	"context"

	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	organizationsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ReadWriter interface {
	GetCompanyPreferenceSetting(ctx context.Context,
		companyID int64) (*organizationpb.CompanyPreferenceSettingModel, error)
	GetBusinessDetail(ctx context.Context, companyID, businessID int64) (*organizationpb.LocationModel, error)
}

type impl struct {
	companyClient  organizationsvcpb.CompanyServiceClient
	businessClient organizationsvcpb.BusinessServiceClient
}

func New() ReadWriter {
	return &impl{
		companyClient:  grpc.NewClient("moego-svc-organization", organizationsvcpb.NewCompanyServiceClient),
		businessClient: grpc.NewClient("moego-svc-organization", organizationsvcpb.NewBusinessServiceClient),
	}
}

func (i *impl) GetCompanyPreferenceSetting(ctx context.Context,
	companyID int64) (*organizationpb.CompanyPreferenceSettingModel, error) {
	companyPreferenceSetting, err := i.companyClient.GetCompanyPreferenceSetting(
		ctx, &organizationsvcpb.GetCompanyPreferenceSettingRequest{CompanyId: companyID})
	if err != nil {
		log.ErrorContextf(ctx, "GetCompanyPreferenceSetting err:%+v, companyID:%d", err, companyID)

		return nil, err
	}

	return companyPreferenceSetting.GetPreferenceSetting(), nil
}

func (i *impl) GetBusinessDetail(ctx context.Context,
	companyID, businessID int64) (*organizationpb.LocationModel, error) {

	businessDetail, err := i.businessClient.GetLocationDetail(
		ctx, &organizationsvcpb.GetLocationDetailRequest{TokenCompanyId: &companyID, Id: businessID})
	if err != nil {
		log.ErrorContextf(ctx, "GetBusinessDetail err:%+v, companyID:%d, businessID:%d", err, companyID, businessID)

		return nil, err
	}

	return businessDetail.GetLocation(), nil
}
