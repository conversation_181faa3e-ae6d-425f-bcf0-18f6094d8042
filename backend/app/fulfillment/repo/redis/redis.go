package redis

import (
	"context"
	"errors"
	"time"

	redis "github.com/redis/go-redis/v9"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	local_redis "github.com/MoeGolibrary/moego/backend/common/rpc/config/redis"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

const (
	DefaultRetry = 3
	DefaultDelay = 500 * time.Millisecond
)

var (
	// 初始化模板锁(companyId, businessId, careType)
	InitFulfillmentReportTemplateLockKey = "init_fulfillment_report_template_lock_%d_%d_%s"
	InitFulfillmentReportTemplateLockTTL = 30 * time.Second
)

type API interface {
	Get(ctx context.Context, key string) (string, error)
	Set(ctx context.Context, key string, value string) error
	Lock(ctx context.Context, key string, ttl time.Duration) error
	Unlock(ctx context.Context, key string) error
	LockWithRetry(ctx context.Context, key string, ttl, delay time.Duration, retry int) error
}

const prefix = "moego:"

type impl struct {
	cli *redis.Client
}

func New() API {
	return &impl{
		cli: local_redis.RedisClient.Client,
	}
}

func (i *impl) Get(ctx context.Context, key string) (string, error) {
	result, err := i.cli.Get(ctx, prefix+key).Result()
	if errors.Is(err, redis.Nil) {
		return "", nil
	}
	if err != nil {
		log.ErrorContextf(ctx, "Redis Get key err, err:%v, key:%s", err, prefix+key)

		return "", err
	}
	log.ErrorContextf(ctx, "Redis Get key success, key:%s, value:%s", prefix+key, result)

	return result, nil

}

func (i *impl) Set(ctx context.Context, key string, value string) error {
	if err := i.cli.Set(ctx, prefix+key, value, 0).Err(); err != nil {
		log.ErrorContextf(ctx, "Redis Set key err, err:%v, key:%s, value:%s", err, prefix+key, value)

		return err
	}

	return nil
}

func (i *impl) Lock(ctx context.Context, key string, ttl time.Duration) error {
	ok, err := i.cli.SetNX(ctx, prefix+key, 1, ttl).Result()
	if err != nil {
		log.ErrorContextf(ctx, "Redis SetNX err, err:%v, key:%s", err, prefix+key)

		return err
	}
	if !ok {
		return status.Error(codes.ResourceExhausted, "Failed to acquire lock")
	}

	return nil
}

func (i *impl) Unlock(ctx context.Context, key string) error {
	if err := i.cli.Del(ctx, prefix+key).Err(); err != nil {
		log.ErrorContextf(ctx, "Redis Del err, err:%v, key:%s", err, prefix+key)

		return err
	}

	return nil
}

func (i *impl) LockWithRetry(ctx context.Context, key string, ttl, delay time.Duration, retry int) error {

	for attempt := 0; attempt < retry; attempt++ {
		if err := i.Lock(ctx, key, ttl); err == nil {
			return nil // 拿到锁立即返回
		}
		if attempt < retry-1 { // 最后一次不再 sleep
			time.Sleep(delay)
		}
	}

	return status.Error(codes.ResourceExhausted, "exhausted retries to acquire lock")
}
