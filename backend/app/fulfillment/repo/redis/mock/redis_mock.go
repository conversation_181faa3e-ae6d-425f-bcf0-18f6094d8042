// Code generated by MockGen. DO NOT EDIT.
// Source: ./redis/redis.go
//
// Generated by this command:
//
//	mockgen -source=./redis/redis.go -destination=./redis/mock/redis_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "go.uber.org/mock/gomock"
)

// MockAPI is a mock of API interface.
type MockAPI struct {
	ctrl     *gomock.Controller
	recorder *MockAPIMockRecorder
	isgomock struct{}
}

// MockAPIMockRecorder is the mock recorder for MockAPI.
type MockAPIMockRecorder struct {
	mock *MockAPI
}

// NewMockAPI creates a new mock instance.
func NewMockAPI(ctrl *gomock.Controller) *MockAPI {
	mock := &MockAPI{ctrl: ctrl}
	mock.recorder = &MockAPIMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAPI) EXPECT() *MockAPIMockRecorder {
	return m.recorder
}

// Get mocks base method.
func (m *MockAPI) Get(ctx context.Context, key string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, key)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockAPIMockRecorder) Get(ctx, key any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockAPI)(nil).Get), ctx, key)
}

// Lock mocks base method.
func (m *MockAPI) Lock(ctx context.Context, key string, ttl time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Lock", ctx, key, ttl)
	ret0, _ := ret[0].(error)
	return ret0
}

// Lock indicates an expected call of Lock.
func (mr *MockAPIMockRecorder) Lock(ctx, key, ttl any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Lock", reflect.TypeOf((*MockAPI)(nil).Lock), ctx, key, ttl)
}

// LockWithRetry mocks base method.
func (m *MockAPI) LockWithRetry(ctx context.Context, key string, ttl, delay time.Duration, retry int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LockWithRetry", ctx, key, ttl, delay, retry)
	ret0, _ := ret[0].(error)
	return ret0
}

// LockWithRetry indicates an expected call of LockWithRetry.
func (mr *MockAPIMockRecorder) LockWithRetry(ctx, key, ttl, delay, retry any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LockWithRetry", reflect.TypeOf((*MockAPI)(nil).LockWithRetry), ctx, key, ttl, delay, retry)
}

// Set mocks base method.
func (m *MockAPI) Set(ctx context.Context, key, value string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Set", ctx, key, value)
	ret0, _ := ret[0].(error)
	return ret0
}

// Set indicates an expected call of Set.
func (mr *MockAPIMockRecorder) Set(ctx, key, value any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Set", reflect.TypeOf((*MockAPI)(nil).Set), ctx, key, value)
}

// Unlock mocks base method.
func (m *MockAPI) Unlock(ctx context.Context, key string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Unlock", ctx, key)
	ret0, _ := ret[0].(error)
	return ret0
}

// Unlock indicates an expected call of Unlock.
func (mr *MockAPIMockRecorder) Unlock(ctx, key any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Unlock", reflect.TypeOf((*MockAPI)(nil).Unlock), ctx, key)
}
