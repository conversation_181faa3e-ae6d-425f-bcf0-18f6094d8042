package appointment

import (
	"context"
	"fmt"
	"time"

	"github.com/samber/lo"
	"google.golang.org/genproto/googleapis/type/interval"
	"google.golang.org/protobuf/types/known/timestamppb"

	appointmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	appointmentsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1"
	utilsv2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/http"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ReadWriter interface {
	GetPetDetailList(ctx context.Context, companyID, appointmentID int64) ([]*appointmentpb.PetDetailModel, error)
	GetAppointment(ctx context.Context,
		companyID, businessID, appointmentID int64) (*appointmentpb.AppointmentModel, error)
	GetNextCustomerPetAppointment(ctx context.Context, companyID, businessID, customerID, petID int64) (
		*appointmentpb.AppointmentModel, error)
	GetOBSetting(ctx context.Context, businessID int64) (*BookOnlineSetting, error)
	HandleReportBodyView(ctx context.Context, bodyViewQuestionParam *HandleReportBodyViewParams) (*BodyViewURL, error)
	GetAppointmentByDateRange(ctx context.Context, companyID, businessID int64,
		startTime, endTime time.Time) ([]*appointmentpb.AppointmentModel, error)
}

type impl struct {
	appointmentClient appointmentsvcpb.AppointmentServiceClient
	petDetailClient   appointmentsvcpb.PetDetailServiceClient
	groomingClient    http.Client
}

func New() ReadWriter {
	return &impl{
		appointmentClient: grpc.NewClient("moego-svc-appointment", appointmentsvcpb.NewAppointmentServiceClient),
		petDetailClient:   grpc.NewClient("moego-svc-appointment", appointmentsvcpb.NewPetDetailServiceClient),
		groomingClient:    http.NewClientProxy("moego-server-grooming"),
	}
}

func (i *impl) GetAppointment(ctx context.Context,
	companyID, businessID, appointmentID int64) (*appointmentpb.AppointmentModel, error) {
	response, err := i.appointmentClient.GetAppointment(ctx, &appointmentsvcpb.GetAppointmentRequest{
		AppointmentId: appointmentID,
		CompanyId:     companyID,
		BusinessId:    businessID,
	})
	if err != nil {
		log.ErrorContextf(ctx, "get appointment info err:%+v, appointment:%d", err, appointmentID)

		return nil, err
	}

	return response.GetAppointment(), nil
}

func (i *impl) GetPetDetailList(ctx context.Context,
	companyID, appointmentID int64) ([]*appointmentpb.PetDetailModel, error) {
	response, err := i.petDetailClient.GetPetDetailList(ctx, &appointmentsvcpb.GetPetDetailListRequest{
		CompanyId:       companyID,
		AppointmentIds:  []int64{appointmentID},
		WithActualDates: lo.ToPtr(true),
	})
	if err != nil {
		log.ErrorContextf(ctx, "get pet detail info err:%+v, appointment:%d", err, appointmentID)

		return nil, err
	}

	return response.GetPetDetails(), nil
}

func (i *impl) GetNextCustomerPetAppointment(ctx context.Context, companyID, businessID, customerID, petID int64) (
	*appointmentpb.AppointmentModel, error) {
	response, err := i.appointmentClient.ListAppointments(ctx, &appointmentsvcpb.ListAppointmentsRequest{
		CompanyId:   companyID,
		BusinessIds: []int64{businessID},
		Filter: &appointmentsvcpb.ListAppointmentsRequest_Filter{
			Status: []appointmentpb.AppointmentStatus{
				appointmentpb.AppointmentStatus_UNCONFIRMED,
				appointmentpb.AppointmentStatus_CONFIRMED,
				appointmentpb.AppointmentStatus_READY,
				appointmentpb.AppointmentStatus_CHECKED_IN,
				appointmentpb.AppointmentStatus_FINISHED,
			},
			CustomerIds: []int64{customerID},
			PetIds:      []int64{petID},
			StartTimeRange: &interval.Interval{
				StartTime: &timestamppb.Timestamp{
					Seconds: time.Now().Unix(),
				},
				EndTime: &timestamppb.Timestamp{
					Seconds: time.Now().Unix() + 60*24*60*60, // 60 day
				},
			},
		},
		OrderBys: []*utilsv2.OrderBy{
			{
				FieldName: "appointmentDate",
				Asc:       lo.ToPtr(true),
			},
			{
				FieldName: "appointmentStartTime",
				Asc:       lo.ToPtr(true),
			},
		},
	})

	if err != nil {
		// nolint: lll
		log.ErrorContextf(ctx, "get next customer pet appointment err:%+v, companyID:%d, businessID:%d, customerID:%d, petID:%d",
			err, companyID, businessID, customerID, petID)

		return nil, err
	}

	if len(response.GetAppointments()) == 0 {
		return nil, nil
	}

	// 返回最近的一个 appointment
	return response.GetAppointments()[0], nil
}

func (i *impl) GetAppointmentByDateRange(ctx context.Context, companyID, businessID int64,
	startTime, endTime time.Time) ([]*appointmentpb.AppointmentModel, error) {
	response, err := i.appointmentClient.ListAppointments(ctx, &appointmentsvcpb.ListAppointmentsRequest{
		CompanyId:   companyID,
		BusinessIds: []int64{businessID},
		Filter: &appointmentsvcpb.ListAppointmentsRequest_Filter{
			Status: []appointmentpb.AppointmentStatus{
				appointmentpb.AppointmentStatus_UNCONFIRMED,
				appointmentpb.AppointmentStatus_CONFIRMED,
				appointmentpb.AppointmentStatus_READY,
				appointmentpb.AppointmentStatus_CHECKED_IN,
				appointmentpb.AppointmentStatus_FINISHED,
			},
			StartTimeRange: &interval.Interval{
				StartTime: &timestamppb.Timestamp{
					Seconds: startTime.Unix(),
				},
				EndTime: &timestamppb.Timestamp{
					Seconds: endTime.Unix(),
				},
			},
		},
		OrderBys: []*utilsv2.OrderBy{
			{
				FieldName: "appointmentDate",
				Asc:       lo.ToPtr(true),
			},
			{
				FieldName: "appointmentStartTime",
				Asc:       lo.ToPtr(true),
			},
		},
	})
	if err != nil {
		// nolint: lll
		log.ErrorContextf(ctx, "get appointment by date range err:%+v, companyID:%d, businessID:%d, startTime:%s, endTime:%s",
			err, companyID, businessID, startTime, endTime)

		return nil, err
	}

	return response.GetAppointments(), nil
}

func (i *impl) GetOBSetting(ctx context.Context, businessID int64) (*BookOnlineSetting, error) {
	bookOnlineSetting := &BookOnlineSetting{}
	path := fmt.Sprintf("%s?businessId=%d", getOBSettingPath, businessID)
	err := i.groomingClient.Get(ctx, path, bookOnlineSetting)
	if err != nil {
		log.ErrorContextf(ctx, "get ob setting err:%+v, businessID:%d", err, businessID)

		return nil, err
	}

	return bookOnlineSetting, nil
}

func (i *impl) HandleReportBodyView(ctx context.Context, bodyViewQuestionParam *HandleReportBodyViewParams) (
	*BodyViewURL, error) {
	if bodyViewQuestionParam.PetTypeID == 0 || bodyViewQuestionParam.BodyViewQuestionParams == nil {
		return nil, nil
	}

	bodyViewURL := &BodyViewURL{}
	err := i.groomingClient.Post(ctx, handleReportBodyViewPath, bodyViewQuestionParam, bodyViewURL)
	if err != nil {
		log.ErrorContextf(ctx, "handle report body view err:%+v, bodyViewQuestionParam:%+v", err, bodyViewQuestionParam)

		return nil, err
	}

	return bodyViewURL, nil
}
