package appointment

import (
	"context"

	"github.com/samber/lo"

	appointmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	appointmentsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ReadWriter interface {
	GetPetDetailList(ctx context.Context, companyID, appointmentID int64) ([]*appointmentpb.PetDetailModel, error)
	GetAppointment(ctx context.Context,
		companyID, businessID, appointmentID int64) (*appointmentpb.AppointmentModel, error)
}

type impl struct {
	appointmentClient appointmentsvcpb.AppointmentServiceClient
	petDetailClient   appointmentsvcpb.PetDetailServiceClient
}

func New() ReadWriter {
	return &impl{
		appointmentClient: grpc.NewClient("moego-svc-appointment", appointmentsvcpb.NewAppointmentServiceClient),
		petDetailClient:   grpc.NewClient("moego-svc-appointment", appointmentsvcpb.NewPetDetailServiceClient),
	}
}

func (i *impl) GetAppointment(ctx context.Context,
	companyID, businessID, appointmentID int64) (*appointmentpb.AppointmentModel, error) {
	response, err := i.appointmentClient.GetAppointment(ctx, &appointmentsvcpb.GetAppointmentRequest{
		AppointmentId: appointmentID,
		CompanyId:     companyID,
		BusinessId:    businessID,
	})
	if err != nil {
		log.ErrorContextf(ctx, "get appointment info err:%+v, appointment:%d", err, appointmentID)

		return nil, err
	}

	return response.GetAppointment(), nil
}

func (i *impl) GetPetDetailList(ctx context.Context,
	companyID, appointmentID int64) ([]*appointmentpb.PetDetailModel, error) {
	response, err := i.petDetailClient.GetPetDetailList(ctx, &appointmentsvcpb.GetPetDetailListRequest{
		CompanyId:       companyID,
		AppointmentIds:  []int64{appointmentID},
		WithActualDates: lo.ToPtr(true),
	})
	if err != nil {
		log.ErrorContextf(ctx, "get pet detail info err:%+v, appointment:%d", err, appointmentID)

		return nil, err
	}

	return response.GetPetDetails(), nil
}
