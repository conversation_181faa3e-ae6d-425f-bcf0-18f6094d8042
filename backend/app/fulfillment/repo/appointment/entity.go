package appointment

const (
	getOBSettingPath         = "/service/grooming/bookOnline/getOBSetting"
	handleReportBodyViewPath = "/service/grooming/grooming-report/handleReportBodyView"
)

// BookOnlineSetting 字段对齐 server-grooming 中的 BookOnlineDTO 字段, 字段太多，剩余字段有需要再继续往下补充
type BookOnlineSetting struct {
	CompanyID      int64  `json:"CompanyId"`
	BusinessID     int64  `json:"BusinessId"`
	IsEnable       byte   `json:"IsEnable"`
	BookOnlineName string `json:"BookOnlineName"`
}

// handleReportBodyViewParam
type HandleReportBodyViewParams struct {
	PetTypeID              int64                         `json:"petTypeId"`
	BodyViewQuestionParams *GroomingReportQuestionParams `json:"bodyViewQuestionParams"`
}

type GroomingReportQuestionParams struct {
	BusinessID     int64       `json:"businessId"`
	CompanyID      int64       `json:"companyId"`
	ID             int64       `json:"id"`
	Category       int64       `json:"category"`
	Type           string      `json:"type"`
	Key            string      `json:"key"`
	Title          string      `json:"title"`
	Required       bool        `json:"required"`
	Sort           int64       `json:"sort"`
	Status         int64       `json:"status"`
	BuildInOptions []string    `json:"buildInOptions"`
	Options        []string    `json:"options"`
	CustomOptions  []string    `json:"customOptions"`
	Choices        []string    `json:"choices"`
	Show           bool        `json:"show"`
	Text           string      `json:"text"`
	Placeholder    string      `json:"placeholder"`
	Urls           BodyViewURL `json:"urls"`
}

type BodyViewURL struct {
	Left  string `json:"left"`
	Right string `json:"right"`
}
