load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "appointment",
    srcs = [
        "appointment.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/appointment",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/framework/http",
        "//backend/common/rpc/framework/log",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/appointment/v1:appointment",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/service/appointment/v1:appointment",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/utils/v2:utils",
        "@com_github_samber_lo//:lo",
        "@org_golang_google_genproto//googleapis/type/interval",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
