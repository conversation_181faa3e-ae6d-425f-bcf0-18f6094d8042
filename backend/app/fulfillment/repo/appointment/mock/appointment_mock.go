// Code generated by MockGen. DO NOT EDIT.
// Source: ./appointment/appointment.go
//
// Generated by this command:
//
//	mockgen -source=./appointment/appointment.go -destination=./appointment/mock/appointment_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	appointmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	appointment "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/appointment"
	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// GetAppointment mocks base method.
func (m *MockReadWriter) GetAppointment(ctx context.Context, companyID, businessID, appointmentID int64) (*appointmentpb.AppointmentModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAppointment", ctx, companyID, businessID, appointmentID)
	ret0, _ := ret[0].(*appointmentpb.AppointmentModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppointment indicates an expected call of GetAppointment.
func (mr *MockReadWriterMockRecorder) GetAppointment(ctx, companyID, businessID, appointmentID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppointment", reflect.TypeOf((*MockReadWriter)(nil).GetAppointment), ctx, companyID, businessID, appointmentID)
}

// GetNextCustomerPetAppointment mocks base method.
func (m *MockReadWriter) GetNextCustomerPetAppointment(ctx context.Context, companyID, businessID, customerID, petID int64) (*appointmentpb.AppointmentModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNextCustomerPetAppointment", ctx, companyID, businessID, customerID, petID)
	ret0, _ := ret[0].(*appointmentpb.AppointmentModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNextCustomerPetAppointment indicates an expected call of GetNextCustomerPetAppointment.
func (mr *MockReadWriterMockRecorder) GetNextCustomerPetAppointment(ctx, companyID, businessID, customerID, petID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNextCustomerPetAppointment", reflect.TypeOf((*MockReadWriter)(nil).GetNextCustomerPetAppointment), ctx, companyID, businessID, customerID, petID)
}

// GetOBSetting mocks base method.
func (m *MockReadWriter) GetOBSetting(ctx context.Context, businessID int64) (*appointment.BookOnlineSetting, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOBSetting", ctx, businessID)
	ret0, _ := ret[0].(*appointment.BookOnlineSetting)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOBSetting indicates an expected call of GetOBSetting.
func (mr *MockReadWriterMockRecorder) GetOBSetting(ctx, businessID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOBSetting", reflect.TypeOf((*MockReadWriter)(nil).GetOBSetting), ctx, businessID)
}

// GetPetDetailList mocks base method.
func (m *MockReadWriter) GetPetDetailList(ctx context.Context, companyID, appointmentID int64) ([]*appointmentpb.PetDetailModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPetDetailList", ctx, companyID, appointmentID)
	ret0, _ := ret[0].([]*appointmentpb.PetDetailModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPetDetailList indicates an expected call of GetPetDetailList.
func (mr *MockReadWriterMockRecorder) GetPetDetailList(ctx, companyID, appointmentID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPetDetailList", reflect.TypeOf((*MockReadWriter)(nil).GetPetDetailList), ctx, companyID, appointmentID)
}

// HandleReportBodyView mocks base method.
func (m *MockReadWriter) HandleReportBodyView(ctx context.Context, bodyViewQuestionParam *appointment.HandleReportBodyViewParams) (*appointment.BodyViewURL, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleReportBodyView", ctx, bodyViewQuestionParam)
	ret0, _ := ret[0].(*appointment.BodyViewURL)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleReportBodyView indicates an expected call of HandleReportBodyView.
func (mr *MockReadWriterMockRecorder) HandleReportBodyView(ctx, bodyViewQuestionParam any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleReportBodyView", reflect.TypeOf((*MockReadWriter)(nil).HandleReportBodyView), ctx, bodyViewQuestionParam)
}
