load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mock",
    srcs = ["appointment_mock.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/appointment/mock",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/fulfillment/repo/appointment",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/appointment/v1:appointment",
        "@org_uber_go_mock//gomock",
    ],
)
