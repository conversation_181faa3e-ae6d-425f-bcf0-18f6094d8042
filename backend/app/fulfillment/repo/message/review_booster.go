package message

const (
	getReviewBoosterConfigPath    = "/service/message/getReviewBoosterConfig"
	updateReviewBoosterConfigPath = "/service/message/updateReviewBoosterConfig"
)

type ReviewBooster struct {
	ID               int64  `json:"id"`
	CompanyID        int64  `json:"companyId"`
	BusinessID       int64  `json:"businessId"`
	EnableAuto       int64  `json:"enableAuto"`
	AutoWaitingMins  int64  `json:"autoWaitingMins"`
	ReviewBody       string `json:"reviewBody"`
	PositiveScore    int64  `json:"positiveScore"`
	PositiveBody     string `json:"positiveBody"`
	PositiveYelp     string `json:"positiveYelp"`
	PositiveFacebook string `json:"positiveFacebook"`
	PositiveGoogle   string `json:"positiveGoogle"`
	NegativeBody     string `json:"negativeBody"`
	Status           int64  `json:"status"`
	CreateTime       int64  `json:"createTime"`
	UpdateTime       int64  `json:"updateTime"`
}
