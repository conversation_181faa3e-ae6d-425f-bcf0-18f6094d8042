load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "message",
    srcs = [
        "message.go",
        "review_booster.go",
        "send_message.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/message",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/http",
        "//backend/common/rpc/framework/log",
    ],
)
