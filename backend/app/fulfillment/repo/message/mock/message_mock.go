// Code generated by MockGen. DO NOT EDIT.
// Source: ../message.go
//
// Generated by this command:
//
//	mockgen -source=../message.go -destination=./message_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	message "github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/message"
	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// GetReviewBoosterConfig mocks base method.
func (m *MockReadWriter) GetReviewBoosterConfig(ctx context.Context, businessID int64) (*message.ReviewBooster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReviewBoosterConfig", ctx, businessID)
	ret0, _ := ret[0].(*message.ReviewBooster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReviewBoosterConfig indicates an expected call of GetReviewBoosterConfig.
func (mr *MockReadWriterMockRecorder) GetReviewBoosterConfig(ctx, businessID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReviewBoosterConfig", reflect.TypeOf((*MockReadWriter)(nil).GetReviewBoosterConfig), ctx, businessID)
}

// SendDailyReportMessageByEmail mocks base method.
func (m *MockReadWriter) SendDailyReportMessageByEmail(ctx context.Context, sendMessages *message.SendMessageByEmailParams) (*message.SendMessageByEmailResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendDailyReportMessageByEmail", ctx, sendMessages)
	ret0, _ := ret[0].(*message.SendMessageByEmailResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendDailyReportMessageByEmail indicates an expected call of SendDailyReportMessageByEmail.
func (mr *MockReadWriterMockRecorder) SendDailyReportMessageByEmail(ctx, sendMessages any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendDailyReportMessageByEmail", reflect.TypeOf((*MockReadWriter)(nil).SendDailyReportMessageByEmail), ctx, sendMessages)
}

// SendServicesMessageToCustomer mocks base method.
func (m *MockReadWriter) SendServicesMessageToCustomer(ctx context.Context, sendMessages *message.SendMessages) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendServicesMessageToCustomer", ctx, sendMessages)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendServicesMessageToCustomer indicates an expected call of SendServicesMessageToCustomer.
func (mr *MockReadWriterMockRecorder) SendServicesMessageToCustomer(ctx, sendMessages any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendServicesMessageToCustomer", reflect.TypeOf((*MockReadWriter)(nil).SendServicesMessageToCustomer), ctx, sendMessages)
}

// UpdateReviewBoosterConfig mocks base method.
func (m *MockReadWriter) UpdateReviewBoosterConfig(ctx context.Context, reviewBooster *message.ReviewBooster) (*message.ReviewBooster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateReviewBoosterConfig", ctx, reviewBooster)
	ret0, _ := ret[0].(*message.ReviewBooster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateReviewBoosterConfig indicates an expected call of UpdateReviewBoosterConfig.
func (mr *MockReadWriterMockRecorder) UpdateReviewBoosterConfig(ctx, reviewBooster any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateReviewBoosterConfig", reflect.TypeOf((*MockReadWriter)(nil).UpdateReviewBoosterConfig), ctx, reviewBooster)
}
