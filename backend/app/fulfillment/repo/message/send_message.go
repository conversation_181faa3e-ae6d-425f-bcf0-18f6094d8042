package message

const (
	sendServicesMessageToCustomerPath = "/service/message/send/sendServicesMessageToCustomer"
	sendDailyReportByEmailPath        = "/service/message/sendFulfillmentDailyReportCardEmail"
)

type SendMessages struct {
	BusinessID  int32                      `json:"businessId"`
	StaffID     int32                      `json:"staffId"`
	Customer    *SendMessageCustomerParams `json:"customer"`
	Method      int32                      `json:"method"`
	TargetType  int32                      `json:"targetType"`
	TargetID    int32                      `json:"targetId"`
	MessageBody string                     `json:"messageBody"`
}

type SendMessageByEmailResult struct {
	ID           int64  `json:"id"`
	SendSucceed  bool   `json:"sendSucceed"`
	ErrorMessage string `json:"errorMessage"`
}

type SendMessageByEmailParams struct {
	ID                 int64    `json:"id"`
	BusinessID         int64    `json:"businessId"`
	CompanyID          int64    `json:"companyId"`
	StaffID            int64    `json:"staffId"`
	RecipientEmailList []string `json:"recipientsEmailList"`
	Subject            string   `json:"subject"`
}

type SendMessageCustomerParams struct {
	CustomerID     int32  `json:"customerId"`
	CustomerName   string `json:"customerName,omitempty"`
	CustomerNumber string `json:"customerNumber,omitempty"`
	CustomerEmail  string `json:"customerEmail,omitempty"`
	ContactID      int32  `json:"contactId,omitempty"`
}

// MessageMethod 消息发送方式
const (
	MessageMethodMsg   = 1 // msg
	MessageMethodEmail = 2 // email
	MessageMethodCall  = 4 // call
	MessageMethodApp   = 5 // app
)

// MessageTargetType 消息目标类型
const (
	MessageTargetTypeGroomingReport = 151 // grooming report
	MessageTargetTypeDailyReport    = 153 // daily report
)
