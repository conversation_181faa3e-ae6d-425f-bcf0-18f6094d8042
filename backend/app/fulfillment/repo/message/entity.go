package message

const (
	getArrivalWindowSettingPath       = "/service/message/getArrivalWindow"
	getReviewBoosterConfigPath        = "/service/message/getReviewBoosterConfig"
	updateReviewBoosterConfigPath     = "/service/message/updateReviewBoosterConfig"
	sendServicesMessageToCustomerPath = "/service/message/send/sendServicesMessageToCustomer"
	sendDailyReportByEmailPath        = "/service/message/sendFulfillmentDailyReportCardEmail"
	sendGroomingReportByEmailPath     = "/service/message/sendFulfillmentGroomingReportCardEmail"
	getReviewBoosterRecordPath        = "/service/message/reviewBooster/record"
)

const (
	MessageTargetTypeGroomingReport = 151 // grooming report
	MessageTargetTypeDailyReport    = 153 // daily report

	MessageMethodMsg   = 1 // msg
	MessageMethodEmail = 2 // email
	MessageMethodCall  = 4 // call
	MessageMethodApp   = 5 // app

	ReviewSourceSms            = 1
	ReviewSourceGroomingReport = 2
)

type ArrivalWindowSetting struct {
	ID                  int64 `json:"id"`
	BusinessID          int64 `json:"businessId"`
	Status              int64 `json:"status"`
	ArrivalBefore       int64 `json:"arrivalBefore"`
	ArrivalAfter        int64 `json:"arrivalAfter"`
	ArrivalWindowStatus bool  `json:"arrivalWindowStatus"`
}

type ReviewBooster struct {
	ID               *int64  `json:"id,omitempty"`
	CompanyID        *int64  `json:"companyId,omitempty"`
	BusinessID       *int64  `json:"businessId,omitempty"`
	EnableAuto       *int64  `json:"enableAuto,omitempty"`
	AutoWaitingMins  *int64  `json:"autoWaitingMins,omitempty"`
	ReviewBody       *string `json:"reviewBody,omitempty"`
	PositiveScore    *int64  `json:"positiveScore,omitempty"`
	PositiveBody     *string `json:"positiveBody,omitempty"`
	PositiveYelp     *string `json:"positiveYelp,omitempty"`
	PositiveFacebook *string `json:"positiveFacebook,omitempty"`
	PositiveGoogle   *string `json:"positiveGoogle,omitempty"`
	NegativeBody     *string `json:"negativeBody,omitempty"`
	Status           *int64  `json:"status,omitempty"`
}

type SendMessages struct {
	BusinessID  int32                      `json:"businessId"`
	StaffID     int32                      `json:"staffId"`
	Customer    *SendMessageCustomerParams `json:"customer"`
	Method      int32                      `json:"method"`
	TargetType  int32                      `json:"targetType"`
	TargetID    int32                      `json:"targetId"`
	MessageBody string                     `json:"messageBody"`
}

type ErrorResponse struct {
	Code     int32  `json:"code"`
	Message  string `json:"message"`
	Data     string `json:"data"`
	CausedBy string `json:"causedBy"`
	Success  bool   `json:"success"`
}

type SendMessageByEmailResult struct {
	ID           int64  `json:"id"`
	SendSucceed  bool   `json:"sendSucceed"`
	ErrorMessage string `json:"errorMessage"`
}

type SendMessageByEmailParams struct {
	ID                 int64    `json:"id"`
	BusinessID         int64    `json:"businessId"`
	CompanyID          int64    `json:"companyId"`
	StaffID            int64    `json:"staffId"`
	RecipientEmailList []string `json:"recipientEmailList"`
	Subject            string   `json:"subject"`
}

type SendMessageCustomerParams struct {
	CustomerID     int32  `json:"customerId"`
	CustomerName   string `json:"customerName,omitempty"`
	CustomerNumber string `json:"customerNumber,omitempty"`
	CustomerEmail  string `json:"customerEmail,omitempty"`
	ContactID      int32  `json:"contactId,omitempty"`
}

type ReviewBoosterRecord struct {
	ID              int64   `json:"id"`
	CompanyID       int64   `json:"companyId"`
	ReviewBoosterID int64   `json:"reviewBoosterId"`
	BusinessID      int64   `json:"businessId"`
	CustomerID      int64   `json:"customerId"`
	PositiveScore   int64   `json:"positiveScore"`
	AppointmentID   int64   `json:"appointmentId"`
	AppointmentDate string  `json:"appointmentDate"`
	ReviewContent   string  `json:"reviewContent"`
	ReviewTime      int64   `json:"reviewTime"`
	Source          int64   `json:"source"`
	StaffIDs        []int64 `json:"staffIds"`
	PetIDs          []int64 `json:"petIds"`
	CreateTime      int64   `json:"createTime"`
	UpdateTime      int64   `json:"updateTime"`
}
