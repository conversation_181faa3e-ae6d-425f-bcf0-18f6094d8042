package message

const (
	getArrivalWindowSettingPath       = "/service/message/getArrivalWindow"
	getReviewBoosterConfigPath        = "/service/message/getReviewBoosterConfig"
	updateReviewBoosterConfigPath     = "/service/message/updateReviewBoosterConfig"
	sendServicesMessageToCustomerPath = "/service/message/send/sendServicesMessageToCustomer"
	sendDailyReportByEmailPath        = "/service/message/sendFulfillmentDailyReportCardEmail"
	sendGroomingReportByEmailPath     = "/service/message/sendFulfillmentGroomingReportCardEmail"
	getReviewBoosterRecordPath        = "/service/message/reviewBooster/record"
)

const (
	MessageTargetTypeGroomingReport = 151 // grooming report
	MessageTargetTypeDailyReport    = 153 // daily report

	MessageMethodMsg   = 1 // msg
	MessageMethodEmail = 2 // email
	MessageMethodCall  = 4 // call
	MessageMethodApp   = 5 // app

	ReviewSourceSms            = 1
	ReviewSourceGroomingReport = 2
)

type ArrivalWindowSetting struct {
	ID                  int64 `json:"id"`
	BusinessID          int64 `json:"businessId"`
	Status              int64 `json:"status"`
	ArrivalBefore       int64 `json:"arrivalBefore"`
	ArrivalAfter        int64 `json:"arrivalAfter"`
	ArrivalWindowStatus bool  `json:"arrivalWindowStatus"`
}

type ReviewBooster struct {
	ID               int64  `json:"id"`
	CompanyID        int64  `json:"companyId"`
	BusinessID       int64  `json:"businessId"`
	EnableAuto       int64  `json:"enableAuto"`
	AutoWaitingMins  int64  `json:"autoWaitingMins"`
	ReviewBody       string `json:"reviewBody"`
	PositiveScore    int64  `json:"positiveScore"`
	PositiveBody     string `json:"positiveBody"`
	PositiveYelp     string `json:"positiveYelp"`
	PositiveFacebook string `json:"positiveFacebook"`
	PositiveGoogle   string `json:"positiveGoogle"`
	NegativeBody     string `json:"negativeBody"`
	Status           int64  `json:"status"`
	CreateTime       int64  `json:"createTime"`
	UpdateTime       int64  `json:"updateTime"`
}

type SendMessages struct {
	BusinessID  int32                      `json:"businessId"`
	StaffID     int32                      `json:"staffId"`
	Customer    *SendMessageCustomerParams `json:"customer"`
	Method      int32                      `json:"method"`
	TargetType  int32                      `json:"targetType"`
	TargetID    int32                      `json:"targetId"`
	MessageBody string                     `json:"messageBody"`
}

type SendMessageByEmailResult struct {
	ID           int64  `json:"id"`
	SendSucceed  bool   `json:"sendSucceed"`
	ErrorMessage string `json:"errorMessage"`
}

type SendMessageByEmailParams struct {
	ID                 int64    `json:"id"`
	BusinessID         int64    `json:"businessId"`
	CompanyID          int64    `json:"companyId"`
	StaffID            int64    `json:"staffId"`
	RecipientEmailList []string `json:"recipientsEmailList"`
	Subject            string   `json:"subject"`
}

type SendMessageCustomerParams struct {
	CustomerID     int32  `json:"customerId"`
	CustomerName   string `json:"customerName,omitempty"`
	CustomerNumber string `json:"customerNumber,omitempty"`
	CustomerEmail  string `json:"customerEmail,omitempty"`
	ContactID      int32  `json:"contactId,omitempty"`
}

type ReviewBoosterRecord struct {
	ID              int64   `json:"id"`
	CompanyID       int64   `json:"companyId"`
	ReviewBoosterID int64   `json:"reviewBoosterId"`
	BusinessID      int64   `json:"businessId"`
	CustomerID      int64   `json:"customerId"`
	PositiveScore   int64   `json:"positiveScore"`
	AppointmentID   int64   `json:"appointmentId"`
	AppointmentDate string  `json:"appointmentDate"`
	ReviewContent   string  `json:"reviewContent"`
	ReviewTime      int64   `json:"reviewTime"`
	Source          int64   `json:"source"`
	StaffIDs        []int64 `json:"staffIds"`
	PetIDs          []int64 `json:"petIds"`
	CreateTime      int64   `json:"createTime"`
	UpdateTime      int64   `json:"updateTime"`
}
