package message

import (
	"context"
	"fmt"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/http"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type impl struct {
	client http.Client
}

type ReadWriter interface {
	GetReviewBoosterConfig(ctx context.Context, businessID int64) (*ReviewBooster, error)
	UpdateReviewBoosterConfig(ctx context.Context, reviewBooster *ReviewBooster) (*ReviewBooster, error)
	SendServicesMessageToCustomer(ctx context.Context, sendMessages *SendMessages) (string, error)
	SendDailyReportMessageByEmail(ctx context.Context,
		sendMessages *SendMessageByEmailParams) (*SendMessageByEmailResult, error)
}

func New() ReadWriter {
	return &impl{
		client: http.NewClientProxy("moego-server-message"),
	}
}

func (i *impl) GetReviewBoosterConfig(ctx context.Context, businessID int64) (*ReviewBooster, error) {
	reviewBooster := &ReviewBooster{}
	path := fmt.Sprintf("%s?businessId=%d", getReviewBoosterConfigPath, businessID)

	err := i.client.Get(ctx, path, reviewBooster)
	if err != nil {
		log.ErrorContextf(ctx, "get review booster config err: %v", err)

		return nil, err
	}

	return reviewBooster, nil
}

func (i *impl) UpdateReviewBoosterConfig(ctx context.Context, reviewBooster *ReviewBooster) (*ReviewBooster, error) {
	reviewBoosterResp := &ReviewBooster{}

	err := i.client.Put(ctx, updateReviewBoosterConfigPath, reviewBooster, reviewBoosterResp)
	if err != nil {
		log.ErrorContextf(ctx, "update review booster config err: %v", err)

		return nil, err
	}

	return reviewBoosterResp, nil
}

func (i *impl) SendServicesMessageToCustomer(
	ctx context.Context, sendMessages *SendMessages) (string, error) {
	var sendMessagesResp string
	log.Infof("send messages: %v", sendMessages)

	err := i.client.Post(ctx, sendServicesMessageToCustomerPath, sendMessages, &sendMessagesResp)
	if err != nil {
		log.ErrorContextf(ctx, "send services message to customer err: %v", err)

		return "", err
	}

	log.Infof("send messages resp: %v", sendMessagesResp)

	return sendMessagesResp, nil
}

func (i *impl) SendDailyReportMessageByEmail(ctx context.Context,
	sendMessages *SendMessageByEmailParams) (*SendMessageByEmailResult, error) {
	sendMessagesResp := &SendMessageByEmailResult{}
	err := i.client.Post(ctx, sendDailyReportByEmailPath, sendMessages, sendMessagesResp)
	if err != nil {
		log.ErrorContextf(ctx, "send daily report message by email error: %v", err)

		return nil, err
	}
	log.Infof("send daily report message by email resp: %v", sendMessagesResp)

	return sendMessagesResp, nil
}
