load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "callback",
    srcs = ["callback.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/message_hub/repo/callback",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/proto/message_hub/v1:message_hub",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//credentials/insecure",
        "@org_golang_x_sync//singleflight",
    ],
)
