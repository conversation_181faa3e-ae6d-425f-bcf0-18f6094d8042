package main

import (
	"github.com/MoeGolibrary/moego/backend/app/pet/service"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/recovery"
	rpc "github.com/MoeGolibrary/moego/backend/common/rpc/framework"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	petpb "github.com/MoeGolibrary/moego/backend/proto/pet/v1"
)

func main() {
	s := rpc.NewServer()

	// 这里需要注册grpc服务
	grpc.Register(s, &petpb.PetService_ServiceDesc, service.NewPetService())

	if err := s.Serve(); err != nil {
		log.Fatalf("failed to serve: %v", err)
	}
}
