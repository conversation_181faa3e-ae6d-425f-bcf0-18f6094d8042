load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "pet",
    srcs = ["pet.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/pet/repo/gorm/pet",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/pet/repo/gorm",
        "//backend/common/rpc/framework/log",
        "//backend/common/utils/pagination",
        "@io_gorm_gorm//:gorm",
        "@org_uber_go_zap//:zap",
    ],
)
