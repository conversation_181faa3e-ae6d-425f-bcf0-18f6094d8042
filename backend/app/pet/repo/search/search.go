package search

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	searchpb "github.com/MoeGolibrary/moego/backend/proto/search/v1"
)

type ReadWriter interface {
	IndexPetDocument(ctx context.Context,
		request *searchpb.BulkDocumentRequest) (*searchpb.BulkDocumentResponse, error)
	SearchDocument(ctx context.Context,
		request *searchpb.SearchDocumentRequest) (*searchpb.SearchDocumentResponse, error)
}

type impl struct {
	search searchpb.SearchServiceClient
}

func New() ReadWriter {
	return &impl{
		search: grpc.NewClient("moego-search", searchpb.NewSearchServiceClient),
	}
}

func (i *impl) IndexPetDocument(ctx context.Context,
	request *searchpb.BulkDocumentRequest) (*searchpb.BulkDocumentResponse, error) {
	return i.search.BulkDocument(ctx, request)
}

func (i *impl) SearchDocument(ctx context.Context,
	request *searchpb.SearchDocumentRequest) (*searchpb.SearchDocumentResponse, error) {
	return i.search.SearchDocument(ctx, request)
}
