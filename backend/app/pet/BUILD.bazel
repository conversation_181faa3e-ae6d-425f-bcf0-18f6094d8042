load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "pet_lib",
    srcs = ["main.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/pet",
    visibility = ["//visibility:private"],
    deps = [
        "//backend/app/pet/service",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/filters/recovery",
        "//backend/common/rpc/framework",
        "//backend/common/rpc/framework/log",
        "//backend/proto/pet/v1:pet",
    ],
)

go_binary(
    name = "pet",
    embed = [":pet_lib"],
    visibility = ["//visibility:public"],
)
