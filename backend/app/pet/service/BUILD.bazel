load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "service",
    srcs = ["pet_service.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/pet/service",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/pet/logic/pet",
        "//backend/app/pet/logic/petsearch",
        "//backend/app/pet/logic/sync_customer",
        "//backend/common/rpc/framework/log",
        "//backend/proto/pet/v1:pet",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/emptypb",
        "@org_uber_go_zap//:zap",
    ],
)
