package petsearch

import (
	"context"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/structpb"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	searchpb "github.com/MoeGolibrary/moego/backend/proto/search/v1"
)

type TermSearchParam struct {
	CompanyID   int64
	Size        int32
	Term        string
	SearchAfter []float64
}

// ConvertToSearchPetTermRequest 将搜索参数转换为搜索请求
// 根据公司ID、页面大小和搜索词构建搜索文档请求
func (t *TermSearchParam) ConvertToSearchPetTermRequest(ctx context.Context) *searchpb.SearchDocumentRequest {

	searchAfter := make([]*structpb.Value, 0, len(t.SearchAfter))
	for _, v := range t.SearchAfter {
		searchAfter = append(searchAfter, structpb.NewNumberValue(v))
	}
	request := &searchpb.SearchDocumentRequest{
		PageSize:    t.Size,
		Sort:        createSortStrategy(),
		Index:       petIndex,
		SearchAfter: searchAfter,
		Strategy: &searchpb.Strategy{
			Strategy: &searchpb.Strategy_Bool{
				Bool: &searchpb.BoolStrategy{
					Must:   createMustConditions(t.CompanyID),
					Should: createShouldConditions(t.Term),
				},
			},
		},
	}
	log.DebugContext(ctx, "ConvertToSearchPetTermRequest", zap.Any("request", request))

	return request
}

// createSortStrategy 创建排序策略
func createSortStrategy() []*searchpb.SearchDocumentRequest_Sort {
	return []*searchpb.SearchDocumentRequest_Sort{
		{
			Field: "_score",
			Order: searchpb.SearchDocumentRequest_Sort_DESC,
		},
		{
			Field: "id",
			Order: searchpb.SearchDocumentRequest_Sort_ASC,
		},
	}
}

// createMustConditions 创建必须匹配的条件
func createMustConditions(companyID int64) []*searchpb.Strategy {
	return []*searchpb.Strategy{
		{
			Strategy: &searchpb.Strategy_Term{
				Term: &searchpb.TermStrategy{
					Field: "status",
					Value: structpb.NewNumberValue(float64(1)),
				},
			},
		},
		{
			Strategy: &searchpb.Strategy_Term{
				Term: &searchpb.TermStrategy{
					Field: "customer.status",
					Value: structpb.NewNumberValue(float64(1)),
				},
			},
		},
		{
			Strategy: &searchpb.Strategy_Term{
				Term: &searchpb.TermStrategy{
					Field: "company_id",
					Value: structpb.NewNumberValue(float64(companyID)),
				},
			},
		},
	}
}

// createShouldConditions 创建可选匹配条件
func createShouldConditions(term string) *searchpb.BoolStrategy_BoolShouldStrategy {
	const (
		BoostName         = 3.0
		BoostCustomerName = 1.0
	)

	return &searchpb.BoolStrategy_BoolShouldStrategy{
		MinimumMatch: 1,
		Strategies: []*searchpb.Strategy{
			createMatchStrategy("name", term, BoostName),
			createMatchStrategy("customer.name", term, BoostCustomerName),
		},
	}
}

func createMatchStrategy(field, term string, boost float32) *searchpb.Strategy {
	return &searchpb.Strategy{
		Strategy: &searchpb.Strategy_Match{
			Match: &searchpb.MatchStrategy{
				Field:    field,
				Query:    term,
				Operator: searchpb.MatchStrategy_OR,
				Boost:    boost,
			},
		},
	}
}
