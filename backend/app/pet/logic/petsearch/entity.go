package petsearch

type petDocument struct {
	ID        int64                `json:"id"`
	Name      string               `json:"name"`
	CompanyID int64                `json:"company_id"`
	Status    int8                 `json:"status"`
	Customers *petDocumentCustomer `json:"customer"`
}

type petDocumentCustomer struct {
	ID     int    `json:"id"`
	Status int8   `json:"status"`
	Name   string `json:"name"`
}
