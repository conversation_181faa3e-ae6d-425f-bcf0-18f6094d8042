package cdc

import (
	"context"
	"time"

	"github.com/IBM/sarama"
	"github.com/bytedance/sonic"

	sourcerepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/source"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type SourceConsumer struct {
	sourceRepo sourcerepo.ReadWriter
}

func NewSourceConsumer() *SourceConsumer {
	return &SourceConsumer{
		sourceRepo: sourcerepo.New(),
	}
}

func (c *SourceConsumer) SourceEventHandler(ctx context.Context, msg *sarama.ConsumerMessage) error {
	if msg == nil {
		return nil
	}
	// 接收消息

	var cdcMsg SourceMessage
	if err := sonic.Unmarshal(msg.Value, &cdcMsg); err != nil {
		log.ErrorContextf(ctx, "SourceEventHandler unmarshal error: %v", err)

		return err
	}

	switch cdcMsg.Op {
	case "c", "r":
		if cdcMsg.After == nil {
			return nil
		}
		model := toSourceModel(cdcMsg.After)
		if err := c.sourceRepo.Save(ctx, model); err != nil {
			log.ErrorContextf(ctx, "CDC CreateSource error: %v", err)

			return err
		}
	case "u":
		if cdcMsg.After == nil {
			return nil
		}
		model := toSourceModel(cdcMsg.After)
		if err := c.sourceRepo.Update(ctx, model); err != nil {
			log.ErrorContextf(ctx, "CDC UpdateSource error: %v", err)

			return err
		}
	case "d":
		var deleteID int64
		if cdcMsg.Before != nil {
			deleteID = cdcMsg.Before.ID
		} else if cdcMsg.After != nil {
			deleteID = cdcMsg.After.ID
		}
		if deleteID == 0 {
			return nil
		}
		// staffID 可选，如有需要可补充
		if err := c.sourceRepo.Delete(ctx, deleteID, 0); err != nil {
			log.ErrorContextf(ctx, "CDC DeleteSource error: %v", err)

			return err
		}
	default:
		log.ErrorContextf(ctx, "CDC unknown op: %s", cdcMsg.Op)
	}

	return nil
}

// 工具函数：将 SourceRow 转为 Source entity
func toSourceModel(row *SourceRow) *sourcerepo.Source {
	if row == nil {
		return nil
	}
	model := &sourcerepo.Source{
		ID:         row.ID,
		BusinessID: row.BusinessID,
		Name:       row.SourceName,
		CompanyID:  row.CompanyID,
		Sort:       row.Sort,
	}
	if row.Status == 2 {
		model.DeleteTime = customerutils.ToPointer(time.Unix(row.UpdateTime, 0))
	}
	model.CreateTime = time.Unix(row.CreateTime, 0)
	model.UpdateTime = time.Unix(row.UpdateTime, 0)

	return model
}
