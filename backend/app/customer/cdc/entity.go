package cdc

import (
	"time"
)

type Source struct {
	Version   string `json:"version"`
	Connector string `json:"connector"`
	Name      string `json:"name"`
	TSMs      int64  `json:"ts_ms"`
	Snapshot  string `json:"snapshot"`
	DB        string `json:"db"`
	Table     string `json:"table"`
}

type LifeCycleMessage struct {
	Before *LifeCycleRow `json:"before"`
	After  *LifeCycleRow `json:"after"`
	Source Source        `json:"source"`
	Op     string        `json:"op"`
	TSMs   int64         `json:"ts_ms"`
}

type LifeCycleRow struct {
	ID         int64  `json:"id"`
	CompanyID  int64  `json:"company_id"`
	BusinessID int64  `json:"business_id"`
	CreatedBy  int64  `json:"created_by"`
	UpdatedBy  int64  `json:"updated_by"`
	DeletedBy  *int64 `json:"deleted_by"`
	CreatedAt  int64  `json:"created_at"`
	UpdatedAt  int64  `json:"updated_at"`
	DeletedAt  *int64 `json:"deleted_at"`
	Name       string `json:"name"`
	Sort       int32  `json:"sort"`
	IsDefault  int32  `json:"is_default"`
}

type ActionStateMessage struct {
	Before *ActionStateRow `json:"before"`
	After  *ActionStateRow `json:"after"`
	Source Source          `json:"source"`
	Op     string          `json:"op"`
	TSMs   int64           `json:"ts_ms"`
}

type ActionStateRow struct {
	ID         int64  `json:"id"`
	CompanyID  int64  `json:"company_id"`
	BusinessID int64  `json:"business_id"`
	CreatedBy  int64  `json:"created_by"`
	UpdatedBy  int64  `json:"updated_by"`
	DeletedBy  *int64 `json:"deleted_by"`
	CreatedAt  int64  `json:"created_at"`
	UpdatedAt  int64  `json:"updated_at"`
	DeletedAt  *int64 `json:"deleted_at"`
	Name       string `json:"name"`
	Sort       int32  `json:"sort"`
	Color      string `json:"color"`
}

type HistoryLogMessage struct {
	Before *HistoryLogRow `json:"before"`
	After  *HistoryLogRow `json:"after"`
	Source Source         `json:"source"`
	Op     string         `json:"op"`
	TSMs   int64          `json:"ts_ms"`
}

type HistoryLogRow struct {
	ID                  int64  `json:"id"`
	CompanyID           int64  `json:"company_id"`
	BusinessID          int64  `json:"business_id"`
	CustomerID          int64  `json:"customer_id"`
	StaffID             int64  `json:"staff_id"`
	Type                string `json:"type"`
	Action              string `json:"action"`
	CreateTime          int64  `json:"create_time"`
	UpdateTime          int64  `json:"update_time"`
	Source              string `json:"source"`
	SourceID            int64  `json:"source_id"`
	CustomerName        string `json:"customer_name"`
	CustomerPhoneNumber string `json:"customer_phone_number"`
	SourceName          string `json:"source_name"`
}

type TaskMessage struct {
	Before *TaskRow `json:"before"`
	After  *TaskRow `json:"after"`
	Source Source   `json:"source"`
	Op     string   `json:"op"`
	TSMs   int64    `json:"ts_ms"`
}

type TaskRow struct {
	ID              int64  `json:"id"`
	CompanyID       int64  `json:"company_id"`
	BusinessID      int64  `json:"business_id"`
	CustomerID      int64  `json:"customer_id"`
	Name            string `json:"name"`
	AllocateStaffID *int64 `json:"allocate_staff_id"`
	CompleteTime    *int64 `json:"complete_time"`
	State           string `json:"state"`
	CreateBy        int64  `json:"create_by"`
	UpdateBy        int64  `json:"update_by"`
	DeleteBy        *int64 `json:"delete_by"`
	CreateTime      int64  `json:"create_time"`
	UpdateTime      int64  `json:"update_time"`
	DeleteTime      *int64 `json:"delete_time"`
}

// CDC 消息体结构
type NoteMessage struct {
	Before *NoteRow `json:"before"`
	After  *NoteRow `json:"after"`
	Source Source   `json:"source"`
	Op     string   `json:"op"`
	TSMs   int64    `json:"ts_ms"`
}

type NoteRow struct {
	ID          int64  `json:"id"`
	CustomerID  int64  `json:"customer_id"`
	Note        string `json:"note"`
	StaffID     int64  `json:"staff_id"`
	LastStaffID int64  `json:"last_staff_id"`
	Status      int32  `json:"status"`
	CreateTime  int64  `json:"create_time"`
	UpdateTime  int64  `json:"update_time"`
}

// CDC 消息体结构
type SourceMessage struct {
	Before *SourceRow `json:"before"`
	After  *SourceRow `json:"after"`
	Source Source     `json:"source"`
	Op     string     `json:"op"`
	TSMs   int64      `json:"ts_ms"`
}

type SourceRow struct {
	ID         int64  `json:"id"`
	BusinessID int64  `json:"business_id"`
	SourceName string `json:"source_name"`
	Status     int32  `json:"status"`
	CreateTime int64  `json:"create_time"`
	UpdateTime int64  `json:"update_time"`
	CompanyID  int64  `json:"company_id"`
	Sort       int32  `json:"sort"`
}

// CDC 消息体结构
type TagMessage struct {
	Before *TagRow `json:"before"`
	After  *TagRow `json:"after"`
	Source Source  `json:"source"`
	Op     string  `json:"op"`
	TSMs   int64   `json:"ts_ms"`
}

type TagRow struct {
	ID         int64  `json:"id"`
	Name       string `json:"name"`
	BusinessID int64  `json:"business_id"`
	Sort       int32  `json:"sort"`
	Status     int32  `json:"status"`
	CreateTime int64  `json:"create_time"`
	UpdateTime int64  `json:"update_time"`
	CompanyID  int64  `json:"company_id"`
}

type TagBindingMessage struct {
	Before *TagBindingRow `json:"before"`
	After  *TagBindingRow `json:"after"`
	Source Source         `json:"source"`
	Op     string         `json:"op"`
	TSMs   int64          `json:"ts_ms"`
}

type TagBindingRow struct {
	ID            int64 `json:"id"`
	CustomerID    int64 `json:"customer_id"`
	CustomerTagID int64 `json:"customer_tag_id"`
}

type CustomerMessage struct {
	Before *CustomerRow `json:"before"`
	After  *CustomerRow `json:"after"`
	Source Source       `json:"source"`
	Op     string       `json:"op"`
	TSMs   int64        `json:"ts_ms"`
}

type CustomerRow struct {
	ID                     uint64     `json:"id"`
	BusinessID             int64      `json:"business_id"`
	AvatarPath             string     `json:"avatar_path"`
	Email                  string     `json:"email"`
	FirstName              string     `json:"first_name"`
	LastName               string     `json:"last_name"`
	Status                 int32      `json:"status"`
	Inactive               int32      `json:"inactive"`
	ClientColor            string     `json:"client_color"`
	IsBlockMessage         int32      `json:"is_block_message"`
	IsBlockOnlineBooking   int32      `json:"is_block_online_booking"`
	LoginEmail             string     `json:"login_email"`
	ReferralSourceID       int64      `json:"referral_source_id"`
	ReferralSourceDesc     string     `json:"referral_source_desc"`
	SendAutoEmail          int32      `json:"send_auto_email"`
	SendAutoMessage        int32      `json:"send_auto_message"`
	SendAppAutoMessage     int32      `json:"send_app_auto_message"`
	UnconfirmedReminderBy  int8       `json:"unconfirmed_reminder_by"`
	PreferredGroomerID     int64      `json:"preferred_groomer_id"`
	PreferredFrequencyDay  int32      `json:"preferred_frequency_day"`
	PreferredFrequencyType int32      `json:"preferred_frequency_type"`
	LastServiceTime        string     `json:"last_service_time"` // 使用自定义unmarshal处理
	Source                 string     `json:"source"`            // 使用自定义unmarshal处理
	ExternalID             string     `json:"external_id"`       // 使用自定义unmarshal处理
	CreateTime             int64      `json:"create_time"`
	UpdateTime             int64      `json:"update_time"`
	CreateBy               *uint64    `json:"create_by"`
	UpdateBy               *uint64    `json:"update_by"`
	IsRecurring            *int32     `json:"is_recurring"`
	ShareApptStatus        int32      `json:"share_appt_status"`
	ShareRangeType         int32      `json:"share_range_type"`
	ShareRangeValue        int32      `json:"share_range_value"`
	ShareApptJSON          *string    `json:"share_appt_json"`
	PreferredDay           *string    `json:"preferred_day"`
	PreferredTime          *string    `json:"preferred_time"`
	AccountID              int64      `json:"account_id"`
	CustomerCode           string     `json:"customer_code"`
	IsUnsubscribed         int32      `json:"is_unsubscribed"`
	CompanyID              int64      `json:"company_id"`
	Birthday               *time.Time `json:"birthday"`
	Type                   string     `json:"type"`
	LifeCycle              string     `json:"life_cycle"`
	ActionState            string     `json:"action_state"`
	AllocateStaffID        *int64     `json:"allocate_staff_id"`
	CustomizeLifeCycleID   int64      `json:"customize_life_cycle_id"`
	CustomizeActionStateID int64      `json:"customize_action_state_id"`
}

type ContactMessage struct {
	Before *ContactRow `json:"before"`
	After  *ContactRow `json:"after"`
	Source Source      `json:"source"`
	Op     string      `json:"op"`
	TSMs   int64       `json:"ts_ms"`
}

type ContactRow struct {
	ID              int64  `json:"id"`
	BusinessID      int64  `json:"business_id"`
	CustomerID      int64  `json:"customer_id"`
	FirstName       string `json:"first_name"`
	LastName        string `json:"last_name"`
	PhoneNumber     string `json:"phone_number"`
	Email           string `json:"email"`
	Title           string `json:"title"`
	Type            int32  `json:"type"`
	IsPrimary       int32  `json:"is_primary"`
	Status          int32  `json:"status"`
	CreateTime      int64  `json:"create_time"`
	UpdateTime      int64  `json:"update_time"`
	CompanyID       int64  `json:"company_id"`
	E164PhoneNumber string `json:"e164_phone_number"`
}

type AddressMessage struct {
	Before *AddressRow `json:"before"`
	After  *AddressRow `json:"after"`
	Source Source      `json:"source"`
	Op     string      `json:"op"`
	TSMs   int64       `json:"ts_ms"`
}

type AddressRow struct {
	ID         uint64 `json:"id"`
	CustomerID int64  `json:"customer_id"`
	BusinessID int64  `json:"business_id"`
	Address1   string `json:"address1"`
	Address2   string `json:"address2"`
	City       string `json:"city"`
	State      string `json:"state"`
	Zipcode    string `json:"zipcode"`
	Country    string `json:"country"`
	Lat        string `json:"lat"`
	Lng        string `json:"lng"`
	Status     int8   `json:"status"`
	IsPrimary  int8   `json:"is_primary"`
	CreateTime int64  `json:"create_time"`
	UpdateTime int64  `json:"update_time"`
	CompanyID  int64  `json:"company_id"`
}
