package cdc

import (
	"context"
	"time"

	"github.com/IBM/sarama"
	"github.com/bytedance/sonic"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/task"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type TaskConsumer struct {
	taskRepo task.ReadWriter
}

func NewTaskConsumer() *TaskConsumer {
	return &TaskConsumer{
		taskRepo: task.New(),
	}
}

func (c *TaskConsumer) TaskEventHandler(ctx context.Context, msg *sarama.ConsumerMessage) error {
	if msg == nil {
		return nil
	}
	// 接收消息

	var cdcMsg TaskMessage
	if err := sonic.Unmarshal(msg.Value, &cdcMsg); err != nil {
		log.ErrorContextf(ctx, "TaskEventHandler unmarshal error: %v", err)

		return err
	}

	switch cdcMsg.Op {
	case "c", "r":
		if cdcMsg.After == nil {
			return nil
		}
		model := toTaskModel(cdcMsg.After)
		if err := c.taskRepo.Save(ctx, model); err != nil {
			log.ErrorContextf(ctx, "CDC CreateTask error: %v", err)

			return err
		}
	case "u":
		if cdcMsg.After == nil {
			return nil
		}
		model := toTaskModel(cdcMsg.After)
		if err := c.taskRepo.Update(ctx, model); err != nil {
			log.ErrorContextf(ctx, "CDC UpdateTask error: %v", err)

			return err
		}
	case "d":
		var deleteID int64
		var staffID int64
		if cdcMsg.Before != nil {
			deleteID = cdcMsg.Before.ID
			staffID = cdcMsg.Before.UpdateBy
		} else if cdcMsg.After != nil {
			deleteID = cdcMsg.After.ID
			staffID = cdcMsg.After.UpdateBy
		}
		if deleteID == 0 {
			return nil
		}
		if err := c.taskRepo.Delete(ctx, deleteID, staffID); err != nil {
			log.ErrorContextf(ctx, "CDC DeleteTask error: %v", err)

			return err
		}
	default:
		log.ErrorContextf(ctx, "CDC unknown op: %s", cdcMsg.Op)
	}

	return nil
}

func toTaskModel(row *TaskRow) *task.Task {
	if row == nil {
		return nil
	}
	model := &task.Task{
		ID:              row.ID,
		CompanyID:       row.CompanyID,
		BusinessID:      row.BusinessID,
		CustomerID:      row.CustomerID,
		Name:            row.Name,
		AllocateStaffID: row.AllocateStaffID,
		State:           customerpb.Task_State(customerpb.Task_State_value[row.State]),
		CreateBy:        row.CreateBy,
		UpdateBy:        row.UpdateBy,
		DeleteBy:        row.DeleteBy,
	}
	if row.CompleteTime != nil {
		t := time.UnixMilli(*row.CompleteTime)
		model.CompleteTime = &t
	}
	model.CreateTime = time.UnixMilli(row.CreateTime)
	model.UpdateTime = time.UnixMilli(row.UpdateTime)
	if row.DeleteTime != nil {
		t := time.UnixMilli(*row.DeleteTime)
		model.DeleteTime = &t
	}

	return model
}
