package cdc

import (
	"context"

	"github.com/IBM/sarama"
	"github.com/bytedance/sonic"

	contactrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact"
	contacttagrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact_tag"
	contacttagrel "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact_tag_rel"
	customerrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

var contactTagTypeMap = make(map[customerpb.ContactTag_Type]int64, 0)

type ContactConsumer struct {
	contactRepo       contactrepo.Repository
	contactTagRepo    contacttagrepo.Repository
	contactTagRelRepo contacttagrel.Repository
	customerRepo      customerrepo.Repository
}

func NewContactConsumer() *ContactConsumer {
	contactConsumer := &ContactConsumer{
		contactRepo:       contactrepo.New(),
		contactTagRepo:    contacttagrepo.New(),
		contactTagRelRepo: contacttagrel.New(),
		customerRepo:      customerrepo.New(),
	}

	contactTags, err := contactConsumer.contactTagRepo.ListByCursor(
		context.Background(),
		&contacttagrepo.ListFilter{
			Types: []customerpb.ContactTag_Type{
				customerpb.ContactTag_EMERGENCY,
				customerpb.ContactTag_PICKUP,
				customerpb.ContactTag_COMMUNICATION,
				customerpb.ContactTag_PRIMARY,
			},
		},
		&contacttagrepo.Pagination{
			PageSize: 100,
		},
		&contacttagrepo.OrderBy{
			Field:     customerpb.ListContactTagsRequest_Sorting_CREATED_TIME,
			Direction: customerpb.Direction_DESC,
		},
	)

	if err != nil {
		log.ErrorContextf(context.Background(), "NewContactConsumer ListByCursor error: %v", err)
	}

	for _, contactTag := range contactTags.Data {
		contactTagTypeMap[contactTag.Type] = contactTag.ID
	}

	return contactConsumer
}

func (c *ContactConsumer) ContactEventHandler(ctx context.Context, msg *sarama.ConsumerMessage) error {
	if msg == nil {
		return nil
	}
	// log.InfoContextf(ctx, "ContactEventHandler get message, key:%s, value:%s", string(msg.Key), string(msg.Value))

	// 反序列化
	var cdcMsg ContactMessage
	// log.InfoContextf(ctx, "ContactEventHandler unmarshal message, value:%s", string(msg.Value))
	if err := sonic.Unmarshal(msg.Value, &cdcMsg); err != nil {
		log.ErrorContextf(ctx, "ContactEventHandler unmarshal error: %v", err)

		return err
	}

	switch cdcMsg.Op {
	case "c", "r":
		if cdcMsg.After == nil {
			// log.InfoContextf(ctx, "CDC create op but after is nil")

			return nil
		}
		contactModel := toContactModels(cdcMsg.After)

		// 创建客户记录
		if _, err := c.contactRepo.Create(ctx, contactModel); err != nil {
			log.ErrorContextf(ctx, "CDC CreateContact error: %v", err)

			return err
		}

		// log.InfoContextf(ctx, "CDC success op=%s id=%d", cdcMsg.Op, contactModel.ID)
	case "u":
		if cdcMsg.After == nil {
			// log.InfoContextf(ctx, "CDC update op but after is nil")
			return nil
		}
		contactModel := toContactModels(cdcMsg.After)

		// 更新客户记录
		if _, err := c.contactRepo.Update(ctx, contactModel); err != nil {
			log.ErrorContextf(ctx, "CDC UpdateContact error: %v", err)

			return err
		}

		// log.InfoContextf(ctx, "CDC success op=%s id=%d", cdcMsg.Op, contactModel.ID)
	case "d":
		var id int64
		if cdcMsg.Before != nil {
			id = int64(cdcMsg.Before.ID)
		} else if cdcMsg.After != nil {
			id = int64(cdcMsg.After.ID)
		}
		if id == 0 {
			// log.InfoContextf(ctx, "CDC delete op but id is 0")

			return nil
		}

		// 删除客户记录
		if err := c.contactRepo.Delete(ctx, id); err != nil {
			log.ErrorContextf(ctx, "CDC DeleteContact error: %v", err)

			return err
		}

		// log.InfoContextf(ctx, "CDC success op=%s id=%d", cdcMsg.Op, id)
	default:
		log.ErrorContextf(ctx, "CDC unknown op: %s", cdcMsg.Op)
	}

	return nil
}

func toContactModels(row *ContactRow) *contactrepo.Contact {
	if row == nil {
		return nil
	}

	model := &contactrepo.Contact{
		ID:               row.ID,
		OrganizationID:   row.CompanyID,
		OrganizationType: customerpb.OrganizationRef_COMPANY,
		CustomerID:       row.CustomerID,
		GivenName:        row.FirstName,
		FamilyName:       row.LastName,
		Phone:            row.PhoneNumber,
		Title:            row.Title,
		IsSelf:           false,
		Email:            row.Email,
	}

	if row.E164PhoneNumber != "" {
		model.Phone = row.E164PhoneNumber
	}

	var tagID int64
	switch row.Type {
	case 1:
		// main
		tagID = contactTagTypeMap[customerpb.ContactTag_PRIMARY]
	case 3:
		// emergency
		tagID = contactTagTypeMap[customerpb.ContactTag_EMERGENCY]
	case 4:
		// pickup
		tagID = contactTagTypeMap[customerpb.ContactTag_PICKUP]
	}

	if tagID != 0 {
		model.Tags = append(model.Tags, &contacttagrepo.ContactTag{
			ID: tagID,
		})
	}

	if row.IsPrimary == 1 {
		model.Tags = append(model.Tags, &contacttagrepo.ContactTag{
			ID: contactTagTypeMap[customerpb.ContactTag_COMMUNICATION],
		})
	}

	switch row.Status {
	case 1:
		model.State = customerpb.Contact_ACTIVE
	case 2:
		model.State = customerpb.Contact_DELETED
	}

	return model
}
