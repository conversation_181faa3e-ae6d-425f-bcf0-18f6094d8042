package cdc

import (
	"context"
	"time"

	"github.com/IBM/sarama"
	"github.com/bytedance/sonic"

	lifecycle "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/life_cycle"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type LifeCycleConsumer struct {
	lifeCycleRepo lifecycle.ReadWriter
}

func NewLifeCycleConsumer() *LifeCycleConsumer {
	return &LifeCycleConsumer{
		lifeCycleRepo: lifecycle.New(),
	}
}

func (c *LifeCycleConsumer) LifeCycleEventHandler(ctx context.Context, msg *sarama.ConsumerMessage) error {
	if msg == nil {
		return nil
	}
	// 接收消息

	// 反序列化
	var cdcMsg LifeCycleMessage
	if err := sonic.Unmarshal(msg.Value, &cdcMsg); err != nil {
		log.ErrorContextf(ctx, "LifeCycleEventHandler unmarshal error: %v", err)

		return err
	}

	// 根据 op 类型分发
	switch cdcMsg.Op {
	case "c", "r":
		// insert or snapshot read
		if cdcMsg.After == nil {
			return nil
		}
		model := toLifeCycleModel(cdcMsg.After)
		if err := c.lifeCycleRepo.Save(ctx, model); err != nil {
			log.ErrorContextf(ctx, "CDC CreateLifeCycle error: %v", err)

			return err
		}
	case "u":
		// update
		if cdcMsg.After == nil {
			return nil
		}
		model := toLifeCycleModel(cdcMsg.After)
		if err := c.lifeCycleRepo.Update(ctx, model); err != nil {
			log.ErrorContextf(ctx, "CDC UpdateLifeCycle error: %v", err)

			return err
		}
	case "d":
		// delete
		var deleteID int64
		var staffID int64
		if cdcMsg.Before != nil {
			deleteID = cdcMsg.Before.ID
			staffID = cdcMsg.Before.UpdatedBy
		} else if cdcMsg.After != nil {
			deleteID = cdcMsg.After.ID
			staffID = cdcMsg.After.UpdatedBy
		}
		if deleteID == 0 {
			return nil
		}
		if err := c.lifeCycleRepo.Delete(ctx, deleteID, staffID); err != nil {
			log.ErrorContextf(ctx, "CDC DeleteLifeCycle error: %v", err)

			return err
		}
	default:
		log.ErrorContextf(ctx, "CDC unknown op: %s", cdcMsg.Op)
	}

	return nil
}

// 3. 工具函数：将 LifeCycleRow 转为 CustomerLifeCycle
func toLifeCycleModel(row *LifeCycleRow) *lifecycle.CustomerLifeCycle {
	if row == nil {
		return nil
	}
	model := &lifecycle.CustomerLifeCycle{
		ID:         row.ID,
		CompanyID:  row.CompanyID,
		BusinessID: row.BusinessID,
		CreatedBy:  row.CreatedBy,
		UpdatedBy:  row.UpdatedBy,
		DeletedBy:  row.DeletedBy,
		Name:       row.Name,
		Sort:       row.Sort,
		IsDefault:  row.IsDefault,
	}
	model.CreatedAt = time.UnixMilli(row.CreatedAt)
	model.UpdatedAt = time.UnixMilli(row.UpdatedAt)
	if row.DeletedAt != nil {
		t := time.UnixMilli(*row.DeletedAt)
		model.DeletedAt = &t
	}

	return model
}
