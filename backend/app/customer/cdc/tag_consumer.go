package cdc

import (
	"context"
	"time"

	"github.com/IBM/sarama"
	"github.com/bytedance/sonic"

	tagrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/tag"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type TagConsumer struct {
	tagRepo tagrepo.ReadWriter
}

func NewTagConsumer() *TagConsumer {
	return &TagConsumer{
		tagRepo: tagrepo.New(),
	}
}

func (c *TagConsumer) TagEventHandler(ctx context.Context, msg *sarama.ConsumerMessage) error {
	if msg == nil {
		return nil
	}
	// 接收消息

	var cdcMsg TagMessage
	if err := sonic.Unmarshal(msg.Value, &cdcMsg); err != nil {
		log.ErrorContextf(ctx, "TagEventHandler unmarshal error: %v", err)

		return err
	}

	switch cdcMsg.Op {
	case "c", "r":
		if cdcMsg.After == nil {
			return nil
		}
		model := toTagModel(cdcMsg.After)
		if err := c.tagRepo.Save(ctx, model); err != nil {
			log.ErrorContextf(ctx, "CDC CreateTag error: %v", err)

			return err
		}
	case "u":
		if cdcMsg.After == nil {
			return nil
		}
		model := toTagModel(cdcMsg.After)
		if err := c.tagRepo.Update(ctx, model); err != nil {
			log.ErrorContextf(ctx, "CDC UpdateTag error: %v", err)

			return err
		}
	case "d":
		var deleteID int64
		if cdcMsg.Before != nil {
			deleteID = cdcMsg.Before.ID
		} else if cdcMsg.After != nil {
			deleteID = cdcMsg.After.ID
		}
		if deleteID == 0 {
			return nil
		}
		// staffID 可选，如有需要可补充
		if err := c.tagRepo.Delete(ctx, deleteID, 0); err != nil {
			log.ErrorContextf(ctx, "CDC DeleteTag error: %v", err)

			return err
		}
	default:
		log.ErrorContextf(ctx, "CDC unknown op: %s", cdcMsg.Op)
	}

	return nil
}

// 工具函数：将 TagRow 转为 Tag entity
func toTagModel(row *TagRow) *tagrepo.Tag {
	if row == nil {
		return nil
	}
	model := &tagrepo.Tag{
		ID:         row.ID,
		Name:       row.Name,
		BusinessID: row.BusinessID,
		Sort:       row.Sort,
		CompanyID:  row.CompanyID,
	}
	if row.Status == 2 {
		model.DeleteTime = customerutils.ToPointer(time.Unix(row.UpdateTime, 0))
	}
	model.CreateTime = time.Unix(row.CreateTime, 0)
	model.UpdateTime = time.Unix(row.UpdateTime, 0)

	return model
}
