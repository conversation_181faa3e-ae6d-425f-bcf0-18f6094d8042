package cdc

import (
	"context"
	"strconv"
	"time"

	"github.com/IBM/sarama"
	"github.com/bytedance/sonic"

	addressrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/address"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type AddressConsumer struct {
	addressRepo addressrepo.Repository
}

func NewAddressConsumer() *AddressConsumer {
	addressConsumer := &AddressConsumer{
		addressRepo: addressrepo.New(),
	}

	return addressConsumer
}

func (c *AddressConsumer) AddressEventHandler(ctx context.Context, msg *sarama.ConsumerMessage) error {
	if msg == nil {
		return nil
	}

	// 反序列化
	var cdcMsg AddressMessage
	if err := sonic.Unmarshal(msg.Value, &cdcMsg); err != nil {
		log.ErrorContextf(ctx, "AddressEventHandler unmarshal error: %v", err)

		return err
	}

	switch cdcMsg.Op {
	case "c", "r":
		if cdcMsg.After == nil {
			return nil
		}
		addressModel := toAddressModels(cdcMsg.After)

		// 创建客户记录
		if _, err := c.addressRepo.Save(ctx, addressModel); err != nil {
			log.ErrorContextf(ctx, "CDC CreateAddress error: %v", err)

			return err
		}

		// log.InfoContextf(ctx, "CDC success op=%s id=%d", cdcMsg.Op, addressModel.ID)
	case "u":
		if cdcMsg.After == nil {
			// log.InfoContextf(ctx, "CDC update op but after is nil")

			return nil
		}
		addressModel := toAddressModels(cdcMsg.After)

		// 更新客户记录
		if _, err := c.addressRepo.Update(ctx, addressModel); err != nil {
			log.ErrorContextf(ctx, "CDC UpdateAddress error: %v", err)

			return err
		}

		// log.InfoContextf(ctx, "CDC success op=%s id=%d", cdcMsg.Op, addressModel.ID)
	case "d":
		var id int64
		if cdcMsg.Before != nil {
			id = int64(cdcMsg.Before.ID)
		} else if cdcMsg.After != nil {
			id = int64(cdcMsg.After.ID)
		}
		if id == 0 {
			// log.InfoContextf(ctx, "CDC delete op but id is 0")

			return nil
		}

		// 删除客户记录
		if err := c.addressRepo.Delete(ctx, id); err != nil {
			log.ErrorContextf(ctx, "CDC DeleteAddress error: %v", err)

			return err
		}

		// log.InfoContextf(ctx, "CDC success op=%s id=%d", cdcMsg.Op, id)
	default:
		log.ErrorContextf(ctx, "CDC unknown op: %s", cdcMsg.Op)
	}

	return nil
}

func toAddressModels(row *AddressRow) *addressrepo.Address {
	if row == nil {
		return nil
	}

	lat, _ := strconv.ParseFloat(row.Lat, 64)
	lng, _ := strconv.ParseFloat(row.Lng, 64)

	regionCode := convertRegionCode(row.Country)

	model := &addressrepo.Address{
		ID:                 int64(row.ID),
		CustomerID:         row.CustomerID,
		RegionCode:         regionCode,
		PostalCode:         row.Zipcode,
		AdministrativeArea: row.State,
		Locality:           row.City,
		Sublocality:        "",
		AddressLines:       []string{row.Address1, row.Address2},
		Recipients:         []string{},
		Latitude:           lat,
		Longitude:          lng,
		State:              customerpb.Address_ACTIVE,
		CreatedTime:        time.Unix(row.CreateTime, 0),
		UpdatedTime:        time.Unix(row.UpdateTime, 0),
		DeletedTime:        nil,
	}

	if row.IsPrimary == 1 {
		model.Type = customerpb.Address_PRIMARY
	} else {
		model.Type = customerpb.Address_ADDITIONAL
	}

	if row.Status == 1 {
		model.State = customerpb.Address_ACTIVE
	} else {
		model.State = customerpb.Address_DELETED
	}

	return model
}

var CountryMapping = map[string]string{
	// === 美国 (US) ===
	"US":                       "US",
	"USA":                      "US",
	"United States":            "US",
	"United States of America": "US",
	"Estados Unidos":           "US", // 西班牙语
	"EstadosUnidos":            "US",
	"United stated":            "US", // 拼写错误
	"美国":                       "US", // 中文简体
	"美國":                       "US", // 中文繁体
	"미국":                       "US", // 韩语
	"アメリカ合衆国":                  "US", // 日语
	"États-Unis":               "US", // 法语
	"Stati Uniti":              "US", // 意大利语
	"Соединенные Штаты Америки": "US", // 俄语
	"ארצות הברית":               "US", // 希伯来语
	"UnitedStates":              "US",
	" United States":            "US", // 前面有空格
	" USA":                      "US", // 前面有空格
	"U.s.":                      "US",
	"United State":              "US", // 单数形式
	"United":                    "US", // 不完整
	"America":                   "US",

	// === 澳大利亚 (AU) ===
	"AU":              "AU",
	"Australia":       "AU",
	"澳大利亚":            "AU", // 中文简体
	"澳洲":              "AU", // 中文简体
	"오스트레일리아":         "AU", // 韩语
	"オーストラリア":         "AU", // 日语
	"Australie":       "AU", // 法语
	"Aus":             "AU",
	"Aust":            "AU",
	"Qld":             "AU", // Queensland 州
	"NSW":             "AU", // New South Wales 州
	"New South Wales": "AU",

	// === 加拿大 (CA) ===
	"CA":           "CA",
	"Canada":       "CA",
	"加拿大":          "CA", // 中文简体
	"캐나다":          "CA", // 韩语
	"Канада":       "CA", // 俄语
	"AB":           "CA", // Alberta 省
	"Alberta":      "CA",
	"Edmonton, AB": "CA",

	// === 英国 (GB) ===
	"GB":                 "GB",
	"UK":                 "GB",
	"United Kingdom":     "GB",
	"英国":                 "GB", // 中文简体
	"Egyesült Királyság": "GB", // 匈牙利语
	"Wielka Brytania":    "GB", // 波兰语
	"Reino Unido":        "GB", // 西班牙语
	"England":            "GB",

	// === 新加坡 (SG) ===
	"SG":        "SG",
	"Singapore": "SG",
	"新加坡":       "SG", // 中文简体
	"シンガポール":    "SG", // 日语

	// === 阿联酋 (AE) ===
	"AE":                   "AE",
	"United Arab Emirates": "AE",
	"UAE":                  "AE",

	// === 卡塔尔 (QA) ===
	"QA":    "QA",
	"Qatar": "QA",
	"قطر":   "QA", // 阿拉伯语

	// === 南非 (ZA) ===
	"ZA":           "ZA",
	"South Africa": "ZA",
	"RSA":          "ZA", // Republic of South Africa

	// === 波多黎各 (PR) - 美国领土 ===
	"PR":          "PR",
	"Puerto Rico": "PR",

	// === 墨西哥 (MX) ===
	"MX":     "MX",
	"México": "MX", // 西班牙语

	// === 菲律宾 (PH) ===
	"PH":          "PH",
	"Philippines": "PH",

	// === 新西兰 (NZ) ===
	"NZ":          "NZ",
	"New Zealand": "NZ",

	// === 西班牙 (ES) ===
	"ES":     "ES",
	"España": "ES", // 西班牙语
	"Spain":  "ES",

	// === 巴拿马 (PA) ===
	"PA":     "PA",
	"Panamá": "PA", // 西班牙语

	// === 爱尔兰 (IE) ===
	"IE":      "IE",
	"Ireland": "IE",

	// === 意大利 (IT) ===
	"IT": "IT",

	// === 印度尼西亚 (ID) ===
	"ID": "ID",

	// === 危地马拉 (GT) ===
	"GT":        "GT",
	"Guatemala": "GT",

	// === 科威特 (KW) ===
	"Kuwait": "KW",

	// === 以色列 (IL) ===
	"IL": "IL",

	// === 印度 (IN) ===
	"IN":    "IN",
	"India": "IN",

	// === 阿根廷 (AR) ===
	"AR": "AR",

	// === 巴西 (BR) ===
	"BR": "BR",

	// === 韩国 (KR) ===
	"KR": "KR",

	// === 德国 (DE) ===
	"DE":      "DE",
	"Germany": "DE",

	// === 中国 (CN) ===
	"CN":    "CN",
	"China": "CN",
	"中国":    "CN", // 中文简体

	// === 塞尔维亚 (RS) ===
	"RS": "RS",

	// === 巴拉圭 (PY) ===
	"Paraguay": "PY",

	// === 关岛 (GU) - 美国领土 ===
	"Guam": "GU",

	// === 香港 (HK) - 特别行政区 ===
	"HK": "HK",
	"香港": "HK", // 中文简体

	// === 法国 (FR) ===
	"FR":     "FR",
	"France": "FR",

	// === 比利时 (BE) ===
	"BE": "BE",

	// === 马来西亚 (MY) ===
	"MY": "MY",

	// === 荷兰 (NL) ===
	"NL":               "NL",
	"Verenigde Staten": "NL", // 注意：这个可能是错误数据，应该是荷兰语的"美国"，但出现在荷兰相关记录中

	// === 智利 (CL) ===
	"Chile": "CL",

	// === 越南 (VN) ===
	"VN": "VN",

	// === 秘鲁 (PE) ===
	"PE": "PE",

	// === 特殊处理 ===
	"":   "", // 空值保持空值
	"??": "", // 未知值设为空

	// === 无效数据（邮政编码等）映射为空字符串 ===
	// 美国邮政编码 (Utah 州)
	"84116": "US", "84104": "US", "84103": "US", "84111": "US", "84119": "US",
	"84054": "US", "84115": "US", "84106": "US", "84105": "US", "84101": "US",
	"84102": "US", "84120": "US", "84107": "US", "84123": "US", "84108": "US",
	"84010": "US", "84128": "US", "84129": "US", "84047": "US", "84044": "US",
	"84118": "US", "84117": "US", "84081": "US", "84124": "US", "84020": "US",
	"84109": "US", "84070": "US", "84088": "US", "84084": "US", "84087": "US",
	"84094": "US", "84098": "US", "84074": "US", "84095": "US", "84043": "US",
	"84092": "US", "84096": "US", "84009": "US", "84065": "US", "84093": "US",
	"84025": "US", "84014": "US", "84045": "US", "84041": "US", "84037": "US",
	"84003": "US", "84404": "US",

	// 伊利诺伊州邮政编码
	"60532": "US", "60540": "US", "60565": "US", "60563": "US", "60515": "US",
	"60440": "US", "60564": "US", "60561": "US", "60516": "US", "60517": "US",

	// 加利福尼亚州邮政编码
	"92040": "US", "92021": "US", "91901": "US", "92019": "US", "92020": "US",
	"92065": "US",

	// 州名/地区名（这些不是国家）
	"TX":        "US", // Texas
	"Ga":        "US", // Georgia
	"Broward":   "US", // Broward County, Florida
	"King":      "US", // King County
	"Arkansas":  "US", // Arkansas state
	"Monroe":    "US", // Monroe (city/county)
	"West Palm": "US", // West Palm Beach
	"CO":        "US", // Colorado
}

func convertRegionCode(code string) string {

	if regionCode, ok := CountryMapping[code]; ok {
		return regionCode
	}

	log.ErrorContextf(context.Background(), "CDC unknown region code: %s", code)

	return ""
}
