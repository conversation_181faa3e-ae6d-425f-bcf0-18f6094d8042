package cdc

import (
	"context"
	"time"

	"github.com/IBM/sarama"
	"github.com/bytedance/sonic"
	"google.golang.org/protobuf/types/known/structpb"

	customerrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer"
	customerrelateddata "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer_related_data"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type CustomerConsumer struct {
	customerRepo        customerrepo.Repository
	customerRelatedRepo customerrelateddata.Repository
}

func NewCustomerConsumer() *CustomerConsumer {
	return &CustomerConsumer{
		customerRepo:        customerrepo.New(),
		customerRelatedRepo: customerrelateddata.New(),
	}
}

func (c *CustomerConsumer) CustomerEventHandler(ctx context.Context, msg *sarama.ConsumerMessage) error {
	if msg == nil {
		return nil
	}
	log.InfoContextf(ctx, "CustomerEventHandler get message, key:%s, value:%s", string(msg.Key), string(msg.Value))

	// 反序列化
	var cdcMsg CustomerMessage
	log.InfoContextf(ctx, "CustomerEventHandler unmarshal message, value:%s", string(msg.Value))
	if err := sonic.Unmarshal(msg.Value, &cdcMsg); err != nil {
		log.ErrorContextf(ctx, "CustomerEventHandler unmarshal error: %v", err)

		return err
	}

	switch cdcMsg.Op {
	case "c", "r":
		if cdcMsg.After == nil {
			log.InfoContextf(ctx, "CDC create op but after is nil")

			return nil
		}
		customerModel, customerRelatedData := toCustomerModels(cdcMsg.After)

		// 创建客户记录
		if _, err := c.customerRepo.Create(ctx, customerModel); err != nil {
			log.ErrorContextf(ctx, "CDC CreateCustomer error: %v", err)

			return err
		}

		// 创建客户相关数据记录
		if _, err := c.customerRelatedRepo.Create(ctx, customerRelatedData); err != nil {
			log.ErrorContextf(ctx, "CDC CreateCustomerRelatedData error: %v", err)

			return err
		}

		log.InfoContextf(ctx, "CDC success op=%s id=%d", cdcMsg.Op, customerModel.ID)
	case "u":
		if cdcMsg.After == nil {
			log.InfoContextf(ctx, "CDC update op but after is nil")

			return nil
		}
		customerModel, customerRelatedData := toCustomerModels(cdcMsg.After)

		// 更新客户记录
		if _, err := c.customerRepo.Update(ctx, customerModel); err != nil {
			log.ErrorContextf(ctx, "CDC UpdateCustomer error: %v", err)

			return err
		}

		// 更新客户相关数据记录
		if _, err := c.customerRelatedRepo.Update(ctx, customerRelatedData); err != nil {
			log.ErrorContextf(ctx, "CDC UpdateCustomerRelatedData error: %v", err)

			return err
		}

		log.InfoContextf(ctx, "CDC success op=%s id=%d", cdcMsg.Op, customerModel.ID)
	case "d":
		var id int64
		if cdcMsg.Before != nil {
			id = int64(cdcMsg.Before.ID)
		} else if cdcMsg.After != nil {
			id = int64(cdcMsg.After.ID)
		}
		if id == 0 {
			log.InfoContextf(ctx, "CDC delete op but id is 0")

			return nil
		}

		// 删除客户记录
		if err := c.customerRepo.Delete(ctx, id); err != nil {
			log.ErrorContextf(ctx, "CDC DeleteCustomer error: %v", err)

			return err
		}

		// 删除客户相关数据记录（通过customer_id查找）
		if relatedData, err := c.customerRelatedRepo.GetByCustomerID(ctx, id); err == nil && relatedData != nil {
			relatedData.State = customerpb.CustomerRelatedData_DELETED
			if _, err := c.customerRelatedRepo.Update(ctx, relatedData); err != nil {
				log.ErrorContextf(ctx, "CDC DeleteCustomerRelatedData error: %v", err)

				return err
			}
		}

		log.InfoContextf(ctx, "CDC success op=%s id=%d", cdcMsg.Op, id)
	default:
		log.InfoContextf(ctx, "CDC unknown op: %s", cdcMsg.Op)
	}

	return nil
}

func toCustomerModels(row *CustomerRow) (*customerrepo.Customer, *customerrelateddata.CustomerRelatedData) {
	if row == nil {
		return nil, nil
	}
	model := &customerrepo.Customer{
		ID:               int64(row.ID),
		OrganizationType: customerpb.OrganizationRef_COMPANY,
		OrganizationID:   row.CompanyID,
		LifeCycleID:      row.CustomizeLifeCycleID,
		ActionStateID:    row.CustomizeActionStateID,
		OwnerStaffID:     *row.AllocateStaffID,
		AvatarPath:       row.AvatarPath,
		ReferralSourceID: row.ReferralSourceID,
		CustomerCode:     row.CustomerCode,
		GivenName:        row.FirstName,
		FamilyName:       row.LastName,
		CreatedTime:      time.Unix(row.CreateTime, 0),
		UpdatedTime:      time.Unix(row.UpdateTime, 0),
		CustomFields:     &structpb.Struct{},
	}
	if row.Type == "LEAD" {
		model.CustomerType = customerpb.CustomerType_LEAD
	} else {
		model.CustomerType = customerpb.CustomerType_CUSTOMER
	}

	if row.Status == 2 {
		model.State = customerpb.Customer_DELETED
		deletedTime := time.Unix(row.UpdateTime, 0)
		model.DeletedTime = &deletedTime
	} else {
		model.State = customerpb.Customer_ACTIVE
	}

	if row.Inactive == 1 {
		model.State = customerpb.Customer_INACTIVE
	}

	unconfirmedReminderBy := byte(0)
	if row.UnconfirmedReminderBy != -127 {
		unconfirmedReminderBy = byte(row.UnconfirmedReminderBy)
	}

	// customer related data
	customerRelatedData := &customerrelateddata.CustomerRelatedData{
		CustomerID:             int64(row.ID),
		BusinessID:             row.BusinessID,
		CompanyID:              row.CompanyID,
		ClientColor:            row.ClientColor,
		IsBlockMessage:         row.IsBlockMessage,
		IsBlockOnlineBooking:   row.IsBlockOnlineBooking,
		LoginEmail:             row.LoginEmail,
		ReferralSourceID:       int32(row.ReferralSourceID),
		ReferralSourceDesc:     row.ReferralSourceDesc,
		SendAutoEmail:          row.SendAutoEmail,
		SendAutoMessage:        row.SendAutoMessage,
		SendAppAutoMessage:     row.SendAppAutoMessage,
		UnconfirmedReminderBy:  unconfirmedReminderBy,
		PreferredGroomerID:     int32(row.PreferredGroomerID),
		PreferredFrequencyDay:  row.PreferredFrequencyDay,
		PreferredFrequencyType: row.PreferredFrequencyType,
		LastServiceTime:        row.LastServiceTime,
		Source:                 row.Source,
		ExternalID:             row.ExternalID,
		IsRecurring:            row.IsRecurring,
		ShareApptStatus:        row.ShareApptStatus,
		ShareRangeType:         row.ShareRangeType,
		ShareRangeValue:        row.ShareRangeValue,
		ShareApptJSON:          row.ShareApptJSON,
		AccountID:              row.AccountID,
		CustomerCode:           row.CustomerCode,
		IsUnsubscribed:         row.IsUnsubscribed == 1,
		CustomizeLifeCycleID:   row.CustomizeLifeCycleID,
		CustomizeActionStateID: row.CustomizeActionStateID,
		CreatedTime:            time.Unix(row.CreateTime, 0),
		UpdatedTime:            time.Unix(row.UpdateTime, 0),
	}

	if row.Status == 1 {
		customerRelatedData.State = customerpb.CustomerRelatedData_ACTIVE
	} else {
		if row.Inactive == 1 {
			customerRelatedData.State = customerpb.CustomerRelatedData_INACTIVE
		} else {
			customerRelatedData.State = customerpb.CustomerRelatedData_DELETED
		}
	}

	// 处理可空字段
	if row.ActionState != "" {
		customerRelatedData.ActionState = row.ActionState
	}

	if row.PreferredDay != nil {
		customerRelatedData.PreferredDay = *row.PreferredDay
	}

	if row.PreferredTime != nil {
		customerRelatedData.PreferredTime = *row.PreferredTime
	}

	if row.Birthday != nil {
		customerRelatedData.Birthday = row.Birthday
	}

	if row.CreateBy != nil {
		customerRelatedData.CreateBy = int32(*row.CreateBy)
	}

	if row.UpdateBy != nil {
		customerRelatedData.UpdateBy = int32(*row.UpdateBy)
	}

	return model, customerRelatedData
}
