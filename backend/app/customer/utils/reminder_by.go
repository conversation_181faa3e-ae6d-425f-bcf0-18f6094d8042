package customerutils

// CustomerCommunicationPreferenceParam 客户通信偏好参数
type CustomerCommunicationPreferenceParam struct {
	Tenant     interface{} `json:"tenant"`
	CustomerID int64       `json:"customerId"`

	// 阻止消息字段
	BlockMessage *bool `json:"blockMessage,omitempty"`

	// 自动消息字段
	EnableAutoMessageSms   *bool `json:"enableAutoMessageSms,omitempty"`
	EnableAutoMessageEmail *bool `json:"enableAutoMessageEmail,omitempty"`

	// 预约提醒字段
	EnableAppointmentReminderSms   *bool `json:"enableAppointmentReminderSms,omitempty"`
	EnableAppointmentReminderEmail *bool `json:"enableAppointmentReminderEmail,omitempty"`
	EnableAppointmentReminderCall  *bool `json:"enableAppointmentReminderCall,omitempty"`

	// 营销字段
	EnableMarketingEmail *bool `json:"enableMarketingEmail,omitempty"`
}

// 常量定义
const (
	// 旧版本设计中的单一消息类型值
	BySmsLegacy   = 1
	ByEmailLegacy = 2
	ByCallLegacy  = 3

	// 新版本设计中的位标记值
	// BY_SMS = 0b10000001 = -127
	BySms = int8(-127)
	// BY_EMAIL = 0b10000010 = -126
	ByEmail = int8(-126)
	// BY_CALL = 0b10000100 = -124
	ByCall = int8(-124)

	// 移除标记的掩码
	// 移除 BY_SMS = 0b11111110
	RemoveBySms = int8(-2)
	// 移除 BY_EMAIL = 0b11111101
	RemoveByEmail = int8(-3)
	// 移除 BY_CALL = 0b11111011
	RemoveByCall = int8(-5)

	// 无发送标记
	NoSend = 0
	// 当所有标记都被清除时的反向标记 = 0b10000000 = -128
	NoSendReverse = int8(-128)
)

// BuildCommunicationPreferenceParam 构建客户通信偏好参数
// 将旧版本的数组格式转换为结构体字段设置
//
// 输入示例和对应的结构体字段设置：
//
//	[]int32{1}     → EnableAppointmentReminderSms = true
//	[]int32{2}     → EnableAppointmentReminderEmail = true
//	[]int32{3}     → EnableAppointmentReminderCall = true
//	[]int32{1,2}   → EnableAppointmentReminderSms = true, EnableAppointmentReminderEmail = true
//	[]int32{1,3}   → EnableAppointmentReminderSms = true, EnableAppointmentReminderCall = true
//	[]int32{2,3}   → EnableAppointmentReminderEmail = true, EnableAppointmentReminderCall = true
//	[]int32{1,2,3} → (EnableAppointmentReminderSms = true,
// EnableAppointmentReminderEmail = true, EnableAppointmentReminderCall = true)

// 配合 UpdateUnconfirmedReminderBy 使用后的最终数据库存储值：
//
//	[]int32{1}     → -127 (0b10000001)
//	[]int32{2}     → -126 (0b10000010)
//	[]int32{3}     → -124 (0b10000100)
//	[]int32{1,2}   → -125 (0b10000011)
//	[]int32{1,3}   → -123 (0b10000101)
//	[]int32{2,3}   → -122 (0b10000110)
//	[]int32{1,2,3} → -121 (0b10000111)
func BuildCommunicationPreferenceParam(unconfirmedReminderBy []int32) *CustomerCommunicationPreferenceParam {
	param := &CustomerCommunicationPreferenceParam{}
	t := true
	for _, reminderType := range unconfirmedReminderBy {
		switch reminderType {
		case BySmsLegacy:
			param.EnableAppointmentReminderSms = &t
		case ByEmailLegacy:
			param.EnableAppointmentReminderEmail = &t
		case ByCallLegacy:
			param.EnableAppointmentReminderCall = &t
		}
	}

	return param
}

// UpdateUnconfirmedReminderBy 更新未确认提醒方式
// 在旧设计中，unconfirmed_reminder_by 是正整数值表示单一消息类型：
//
//	0 = NO_SEND
//	1 = SMS
//	2 = EMAIL
//	3 = CALL
//
// 在新设计中，unconfirmed_reminder_by 是负字节值表示多种消息类型的组合：
//
//	00000000 =  0   = NO_SEND
//	10000001 = -127 = BY_SMS
//	10000010 = -126 = BY_EMAIL
//	10000100 = -124 = BY_CALL
//	10000011 = -125 = BY_SMS | BY_EMAIL
//	10000101 = -123 = BY_SMS | BY_CALL
//	10000110 = -122 = BY_EMAIL | BY_CALL
//	10000111 = -121 = BY_SMS | BY_EMAIL | BY_CALL
func (c *CustomerCommunicationPreferenceParam) UpdateUnconfirmedReminderBy(oldValue *int) int8 {
	var newValue int
	if oldValue != nil {
		newValue = *oldValue
	}

	// 如果是旧值，转换成新值
	if newValue == BySmsLegacy {
		newValue = int(BySms)
	}
	if newValue == ByEmailLegacy {
		newValue = int(ByEmail)
	}
	if newValue == ByCallLegacy {
		newValue = int(ByCall)
	}

	// 处理 SMS 提醒设置
	if c.EnableAppointmentReminderSms != nil {
		if *c.EnableAppointmentReminderSms {
			newValue = newValue | int(BySms)
		} else {
			newValue = newValue & int(RemoveBySms)
		}
	}

	// 处理 Email 提醒设置
	if c.EnableAppointmentReminderEmail != nil {
		if *c.EnableAppointmentReminderEmail {
			newValue = newValue | int(ByEmail)
		} else {
			newValue = newValue & int(RemoveByEmail)
		}
	}

	// 处理电话提醒设置
	if c.EnableAppointmentReminderCall != nil {
		if *c.EnableAppointmentReminderCall {
			newValue = newValue | int(ByCall)
		} else {
			newValue = newValue & int(RemoveByCall)
		}
	}

	// 当全部标记被置为 0 时，只剩下最高位的 1，需要转成 0
	if newValue == int(NoSendReverse) {
		newValue = NoSend
	}

	return int8(newValue)
}

// ParseUnconfirmedReminderBy 解析未确认提醒方式，将int8值转换回数组
// 支持新旧两种格式的解析，是 BuildCommunicationPreferenceParam 的逆向操作
//
// 输入示例和对应的输出数组：
//
// 旧版本格式（正数，单一类型）：
//
//	ParseUnconfirmedReminderBy(0) → []int32{}     (NO_SEND)
//	ParseUnconfirmedReminderBy(1) → []int32{1}    (SMS)
//	ParseUnconfirmedReminderBy(2) → []int32{2}    (Email)
//	ParseUnconfirmedReminderBy(3) → []int32{3}    (Call)
//
// 新版本格式（负数，位运算组合）：
//
//	ParseUnconfirmedReminderBy(-127) → []int32{1}     (0b10000001 = SMS)
//	ParseUnconfirmedReminderBy(-126) → []int32{2}     (0b10000010 = Email)
//	ParseUnconfirmedReminderBy(-124) → []int32{3}     (0b10000100 = Call)
//	ParseUnconfirmedReminderBy(-125) → []int32{1,2}   (0b10000011 = SMS + Email)
//	ParseUnconfirmedReminderBy(-123) → []int32{1,3}   (0b10000101 = SMS + Call)
//	ParseUnconfirmedReminderBy(-122) → []int32{2,3}   (0b10000110 = Email + Call)
//	ParseUnconfirmedReminderBy(-121) → []int32{1,2,3} (0b10000111 = SMS + Email + Call)
func ParseUnconfirmedReminderBy(value int8) []int32 {
	var result []int32

	// 处理 NO_SEND 情况
	if value == NoSend {
		return result
	}

	// 处理旧版本格式（正数）
	if value > 0 {
		switch value {
		case BySmsLegacy:
			result = append(result, BySmsLegacy)
		case ByEmailLegacy:
			result = append(result, ByEmailLegacy)
		case ByCallLegacy:
			result = append(result, ByCallLegacy)
		}

		return result
	}

	// 处理新版本格式（负数，位运算）
	// 检查每个位标记是否存在
	if value&0b00000001 != 0 { // 检查最低位 (SMS)
		result = append(result, BySmsLegacy)
	}
	if value&0b00000010 != 0 { // 检查第二位 (Email)
		result = append(result, ByEmailLegacy)
	}
	if value&0b00000100 != 0 { // 检查第三位 (Call)
		result = append(result, ByCallLegacy)
	}

	return result
}
