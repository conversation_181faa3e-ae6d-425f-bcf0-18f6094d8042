package customerutils

import (
	"fmt"
	"strconv"
	"strings"
)

// StringToInt64WithDefault 将字符串转换为 int64，如果转换失败，则返回默认值
func StringToInt64WithDefault(s string, defaultValue int64) int64 {
	value, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		return defaultValue
	}

	return value
}

func IntSliceToString(slice []int64) string {
	var strSlice []string
	for _, v := range slice {
		strSlice = append(strSlice, strconv.FormatInt(v, 10))
	}

	return strings.Join(strSlice, ",")
}

func StringToIntSlice(str string) []int64 {
	ints := make([]int64, 0)
	for _, s := range strings.FieldsFunc(str, func(r rune) bool {
		return r == ','
	}) {
		s = strings.TrimSpace(s)
		if v, err := strconv.ParseInt(s, 10, 64); err == nil {
			ints = append(ints, v)
		}
	}

	return ints
}

func ConvCustomerName(givenName string, familyName string) string {
	return fmt.Sprintf("%s_%s", givenName, familyName)
}

// IntersectInt64 返回两个 []int64 的交集（去重）
func IntersectInt64(a, b []int64) []int64 {
	set := make(map[int64]bool)
	result := make([]int64, 0)

	for _, v := range a {
		set[v] = true
	}

	for _, v := range b {
		if set[v] {
			result = append(result, v)
			delete(set, v) // 防止重复添加
		}
	}

	return result
}
