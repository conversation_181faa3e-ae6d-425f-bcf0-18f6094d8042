package service

import (
	"context"
	"fmt"

	"github.com/IBM/sarama"

	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	activitylog "github.com/MoeGolibrary/moego/backend/app/customer/logic/activity_log"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/customer"
	historylog "github.com/MoeGolibrary/moego/backend/app/customer/logic/history_log"
	customerrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/staff"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
	customerpbv2 "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type MessageEventConsumer struct {
	cl        *customer.Logic
	hll       *historylog.Logic
	staffRepo staff.ReadWriter

	// v2
	customerRepo customerrepo.Repository
	al           *activitylog.Logic
}

func NewMessageEventConsumer() *MessageEventConsumer {
	return &MessageEventConsumer{
		cl:           customer.New(),
		hll:          historylog.New(),
		staffRepo:    staff.New(),
		customerRepo: customerrepo.New(),
		al:           activitylog.New(),
	}
}

func (c *MessageEventConsumer) MessageEventHandle(ctx context.Context, msg *sarama.ConsumerMessage) error {
	if msg == nil {
		return nil
	}
	log.InfoContextf(ctx, "MessageEventHandle get message, key:%s, value:%s", string(msg.Key), string(msg.Value))

	// parse msg
	eventData, _, err := parseEventData(ctx, msg)
	if err != nil {
		log.ErrorContextf(ctx, "MessageEventHandle parseEventData err, err: %v", err)

		return err
	}
	log.InfoContextf(ctx, "MessageEventHandle parseEventData, eventData: %+v", eventData)

	// check
	var (
		tenant = eventData.GetTenant()
		event  = eventData.GetMessageSendEvent()
	)
	if tenant == nil || tenant.GetCompanyId() == 0 || tenant.GetBusinessId() == 0 {
		log.InfoContextf(ctx, "MessageEventHandle eventData invalid tenant data, tenant: %+v", tenant)

		return nil
	}
	if event == nil ||
		event.GetMessageId() == 0 ||
		event.GetCustomerId() == 0 ||
		event.GetPhoneNumber() == "" ||
		event.GetDirection() == eventbuspb.MessageSendEvent_DIRECTION_UNSPECIFIED ||
		event.GetText() == "" ||
		event.GetTime() == nil {
		log.InfoContextf(ctx, "MessageEventHandle eventData invalid event, event: %+v", event)

		return nil
	}

	//logID, err := c.saveLogV1(ctx, tenant, event)
	//if err != nil {
	//	return err
	//}

	logID, err := c.saveLogV2(ctx, tenant, event)
	if err != nil {
		return err
	}

	log.InfoContextf(ctx, "MessageEventHandle create history log success, logID:%d, customerID:%d",
		logID, event.GetCustomerId())

	return nil
}

//nolint:unused
func (c *MessageEventConsumer) saveLogV1(ctx context.Context, tenant *organizationpb.Tenant,
	event *eventbuspb.MessageSendEvent) (int64, error) {
	// get customer info
	customerID := event.GetCustomerId()
	customer, err := c.cl.Get(ctx, &customer.GetCustomerParams{ID: customerID})
	if err != nil {
		log.ErrorContextf(ctx, "MessageEventHandle get customer error, err: %v, customerID:%d", err, customerID)

		return 0, err
	}
	if customer == nil || customer.Type != customerpb.Customer_LEAD {
		log.InfoContextf(ctx, "MessageEventHandle get customer not found, customerID:%d", customerID)

		return 0, nil
	}

	// save to db
	logID, err := c.hll.Create(ctx, &historylog.CreateHistoryLogDatum{
		CustomerID:          customerID,
		CustomerName:        customerutils.ConvCustomerName(customer.GivenName, customer.FamilyName),
		CustomerPhoneNumber: customer.PhoneNumber,
		BusinessID:          tenant.GetBusinessId(),
		CompanyID:           tenant.GetCompanyId(),
		StaffID:             event.GetStaffId(),
		Action: &customerpb.HistoryLog_Action{
			Action: &customerpb.HistoryLog_Action_Message{
				Message: &customerpb.HistoryLog_Message{
					MessageId:  event.GetMessageId(),
					Text:       event.GetText(),
					State:      customerpb.HistoryLog_Message_State(event.GetStatus()),
					FailReason: event.GetFailReason(),
					Direction:  customerpb.HistoryLog_Message_Direction(event.GetDirection()),
				},
			},
		},
		Source:     customerpb.HistoryLog_STAFF.Enum(),
		SourceID:   &event.StaffId,
		SourceName: customerutils.ToPointer(c.getStaffName(ctx, event.GetStaffId(), tenant.GetCompanyId())),
	})
	if err != nil {
		log.ErrorContextf(ctx, "MessageEventHandle create history log error, err: %v, messageID:%d", err.Error(),
			event.GetMessageId())

		return 0, err
	}

	return logID, nil
}

func (c *MessageEventConsumer) getStaffName(ctx context.Context, id int64, companyID int64) string {
	detail, err := c.staffRepo.GetStaffDetail(ctx, id, companyID, 0)
	if err != nil {
		log.ErrorContextf(ctx, "MessageEventConsumer getStaffName err, id:%d, err: %v", id, err)

		return ""
	}

	return fmt.Sprintf("%s %s", detail.GetFirstName(), detail.GetLastName())
}

func (c *MessageEventConsumer) saveLogV2(ctx context.Context, tenant *organizationpb.Tenant,
	event *eventbuspb.MessageSendEvent) (int64, error) {
	// get customer info
	customerID := event.GetCustomerId()
	customer, err := c.customerRepo.GetByID(ctx, customerID)
	if err != nil {
		log.ErrorContextf(ctx, "MessageEventHandle saveLogV2 get customer error, err: %v, customerID:%d", err,
			customerID)

		return 0, err
	}
	if customer == nil || customer.ConvertToCustomerID > 0 {
		log.InfoContextf(ctx, "MessageEventHandle saveLogV2 get customer not found or is converted, customerID:%d",
			customerID)

		return 0, nil
	}

	// save to db
	logID, err := c.al.Create(ctx, &activitylog.CreateActivityLogDatum{
		CustomerID:          customerID,
		CustomerName:        customerutils.ConvCustomerName(customer.GivenName, customer.FamilyName),
		CustomerPhoneNumber: event.GetPhoneNumber(),
		BusinessID:          tenant.GetBusinessId(),
		CompanyID:           tenant.GetCompanyId(),
		StaffID:             event.GetStaffId(),
		Action: &customerpbv2.ActivityLog_Action{
			Action: &customerpbv2.ActivityLog_Action_Message{
				Message: &customerpbv2.ActivityLog_Message{
					MessageId:  event.GetMessageId(),
					Text:       event.GetText(),
					State:      customerpbv2.ActivityLog_Message_State(event.GetStatus()),
					FailReason: event.GetFailReason(),
					Direction:  customerpbv2.ActivityLog_Message_Direction(event.GetDirection()),
				},
			},
		},
		Source: &customerpbv2.SystemSource{
			Source:     customerpbv2.SystemSource_STAFF,
			SourceId:   event.StaffId,
			SourceName: c.getStaffName(ctx, event.GetStaffId(), tenant.GetCompanyId()),
		},
	})
	if err != nil {
		log.ErrorContextf(ctx, "MessageEventHandle saveLogV2 create activitylog log error, err: %v, messageID:%d",
			err.Error(),
			event.GetMessageId())

		return 0, err
	}

	return logID.Id, nil
}
