package service

import (
	"context"

	datamigration "github.com/MoeGolibrary/moego/backend/app/customer/logic/data_migration"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type DataMigrationService struct {
	dataMigration *datamigration.Logic
	customerpb.UnimplementedDataMigrationServiceServer
}

func NewDataMigrationService() *DataMigrationService {
	return &DataMigrationService{
		dataMigration: datamigration.New(),
	}
}

func (s *DataMigrationService) ImportCustomers(ctx context.Context,
	req *customerpb.ImportCustomersRequest) (*customerpb.ImportCustomersResponse, error) {
	if err := s.dataMigration.ImportCustomers(ctx, &datamigration.ImportCustomersRequest{
		UploadFileURI: req.GetUploadFileUri(),
		Organization:  req.GetOrganization(),
		Options:       req.GetOptions(),
		StaffID:       req.GetStaffId(),
	}); err != nil {
		return nil, err
	}

	return &customerpb.ImportCustomersResponse{}, nil
}

func (s *DataMigrationService) ListImportHistories(ctx context.Context,
	req *customerpb.ListImportHistoriesRequest) (*customerpb.ListImportHistoriesResponse, error) {
	resp, err := s.dataMigration.ListImportHistories(ctx, &datamigration.ListImportHistoriesRequest{
		IDs:           req.GetFilter().GetIds(),
		States:        req.GetFilter().GetStates(),
		Organizations: req.GetFilter().GetOrganizations(),
		PageSize:      req.GetPageSize(),
		PageToken:     req.GetPageToken(),
	})
	if err != nil {
		return nil, err
	}
	var pbs []*customerpb.ImportHistory
	for _, h := range resp.ImportHistories {
		pbs = append(pbs, h.ToPB())
	}

	return &customerpb.ListImportHistoriesResponse{
		ImportHistories: pbs,
		NextPageToken:   resp.NextToken,
		TotalSize:       resp.TotalSize,
	}, nil
}
