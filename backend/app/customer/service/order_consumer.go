package service

import (
	"context"

	"github.com/IBM/sarama"

	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/convert"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/customer"
	historylog "github.com/MoeGolibrary/moego/backend/app/customer/logic/history_log"
	customerrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
	customerpbv2 "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type OrderEventConsumer struct {
	cl  *customer.Logic
	hll *historylog.Logic

	// v2
	customerRepo customerrepo.Repository
	convertLogic *convert.Logic
}

func NewOrderEventConsumer() *OrderEventConsumer {
	return &OrderEventConsumer{
		cl:           customer.New(),
		hll:          historylog.New(),
		customerRepo: customerrepo.New(),
		convertLogic: convert.New(),
	}
}

func (c *OrderEventConsumer) OrderEventHandle(ctx context.Context, msg *sarama.ConsumerMessage) error {
	if msg == nil {
		return nil
	}
	log.InfoContextf(ctx, "OrderEventHandle get message, key:%s, value:%s", string(msg.Key), string(msg.Value))

	// parse msg
	eventData, eventType, err := parseEventData(ctx, msg)
	if err != nil {
		log.ErrorContextf(ctx, "OrderEventHandle parseEventData err, err: %v", err)

		return err
	}
	log.InfoContextf(ctx, "OrderEventHandle parseEventData, eventData: %+v", eventData)

	// check
	if !c.validateEvent(ctx, eventData, eventType) {
		return nil // Invalid data, stop processing
	}

	//customerID, err := c.convertV1(ctx, eventData.GetTenant(),  eventData.GetOrderEvent())
	//if err != nil {
	//	return err
	//}

	customerID, err := c.convertV2(ctx, eventData.GetOrderEvent())
	if err != nil {
		return err
	}

	log.InfoContextf(ctx, "OrderEventHandle convert customer success, leadID:%d, customerID:%d",
		eventData.GetOrderEvent().GetCustomerId(), customerID)

	return nil
}

//nolint:unused
func (c *OrderEventConsumer) convertV1(ctx context.Context, tenant *organizationpb.Tenant,
	orderEvent *eventbuspb.OrderEvent) (int64, error) {
	// check customer lead
	customerID := orderEvent.GetCustomerId()
	customerInfo, err := c.cl.Get(ctx, &customer.GetCustomerParams{ID: customerID})
	if err != nil {
		log.ErrorContextf(ctx, "OrderEventHandle get customer error, err: %v, customerID:%d", err, customerID)

		return 0, err
	}
	if customerInfo == nil || customerInfo.Type != customerpb.Customer_LEAD {
		log.InfoContextf(ctx, "OrderEventHandle get customer not found, customerID:%d", customerID)

		return 0, nil
	}

	// update business ID
	if customerInfo.PreferredBusinessID <= 0 {
		if err := c.cl.Update(ctx, &customer.Customer{
			ID:                  customerInfo.ID,
			PreferredBusinessID: tenant.GetBusinessId(),
		}); err != nil {
			log.ErrorContextf(ctx, "OrderEventHandle update customer error, err: %v, customerID:%d",
				err, customerID)

			return 0, err
		}
		customerInfo.PreferredBusinessID = tenant.GetBusinessId()
	}

	// convert customer
	if err := c.cl.ConvertCustomer(ctx, customerInfo.ID); err != nil {
		log.ErrorContextf(ctx, "OrderEventHandle convert customer error, err: %v, customerID:%d", err, customerID)

		return 0, err
	}

	// add convert history log
	if _, err := c.hll.Create(ctx, &historylog.CreateHistoryLogDatum{
		CustomerID:          customerInfo.ID,
		CustomerName:        customerutils.ConvCustomerName(customerInfo.GivenName, customerInfo.FamilyName),
		CustomerPhoneNumber: customerInfo.PhoneNumber,
		BusinessID:          customerInfo.PreferredBusinessID,
		CompanyID:           customerInfo.CompanyID,
		Action: &customerpb.HistoryLog_Action{
			Action: &customerpb.HistoryLog_Action_Convert{
				Convert: &customerpb.HistoryLog_Convert{
					OriginType: customerInfo.Type,
					TargetType: customerpb.Customer_CUSTOMER,
				},
			},
		},
		Source:   convOrderSourceType(orderEvent.SourceType).Enum(),
		SourceID: &orderEvent.Id,
	}); err != nil {
		log.ErrorContextf(ctx, "OrderEventHandle Create log error, err: %v, customerID:%d", err, customerID)

		return 0, err
	}

	return customerID, nil
}

//nolint:unused
func convOrderSourceType(sourceType orderpb.OrderSourceType) customerpb.HistoryLog_Source {
	switch sourceType {
	case orderpb.OrderSourceType_APPOINTMENT:
		return customerpb.HistoryLog_APPOINTMENT
	case orderpb.OrderSourceType_PRODUCT:
		return customerpb.HistoryLog_PRODUCT
	case orderpb.OrderSourceType_PACKAGE:
		return customerpb.HistoryLog_PACKAGE
	case orderpb.OrderSourceType_BOOKING_REQUEST:
		return customerpb.HistoryLog_ONLINE_BOOKING
	case orderpb.OrderSourceType_FULFILLMENT:
		return customerpb.HistoryLog_FULFILLMENT
	case orderpb.OrderSourceType_MEMBERSHIP:
		return customerpb.HistoryLog_MEMBERSHIP
	default:
		return customerpb.HistoryLog_SOURCE_UNSPECIFIED
	}
}

func convOrderSourceTypeV2(sourceType orderpb.OrderSourceType) customerpbv2.SystemSource_Source {
	switch sourceType {
	case orderpb.OrderSourceType_APPOINTMENT:
		return customerpbv2.SystemSource_APPOINTMENT
	case orderpb.OrderSourceType_PRODUCT:
		return customerpbv2.SystemSource_PRODUCT
	case orderpb.OrderSourceType_PACKAGE:
		return customerpbv2.SystemSource_PACKAGE
	case orderpb.OrderSourceType_BOOKING_REQUEST:
		return customerpbv2.SystemSource_ONLINE_BOOKING
	case orderpb.OrderSourceType_FULFILLMENT:
		return customerpbv2.SystemSource_FULFILLMENT
	case orderpb.OrderSourceType_MEMBERSHIP:
		return customerpbv2.SystemSource_MEMBERSHIP
	default:
		return customerpbv2.SystemSource_SOURCE_UNSPECIFIED
	}
}

func (c *OrderEventConsumer) validateEvent(ctx context.Context,
	eventData *eventbuspb.EventData, eventType eventbuspb.EventType) bool {
	// only consume order completed
	if eventType != eventbuspb.EventType_ORDER_COMPLETED {
		return false
	}

	// check event data
	tenant := eventData.GetTenant()
	orderEvent := eventData.GetOrderEvent()
	if tenant == nil || tenant.GetCompanyId() == 0 || tenant.GetBusinessId() == 0 {
		log.InfoContextf(ctx, "OrderEventConsumer eventData invalid tenant data, tenant: %+v", tenant)

		return false
	}
	if orderEvent == nil || orderEvent.GetCustomerId() == 0 {
		log.InfoContextf(ctx, "OrderEventConsumer orderEvent is invalid, orderEvent: %+v",
			orderEvent)

		return false
	}

	return true
}

func (c *OrderEventConsumer) convertV2(ctx context.Context,
	orderEvent *eventbuspb.OrderEvent) (int64, error) {
	// check customer lead
	customerID := orderEvent.GetCustomerId()
	customerInfo, err := c.customerRepo.GetByID(ctx, customerID)
	if err != nil {
		log.ErrorContextf(ctx, "OrderEventHandle convertV2 get customer error, err: %v, customerID:%d", err, customerID)

		return 0, err
	}
	if customerInfo == nil ||
		customerInfo.CustomerType != customerpbv2.CustomerType_LEAD ||
		customerInfo.ConvertToCustomerID > 0 {
		log.InfoContextf(ctx,
			"OrderEventHandle convertV2 get customer not found or not lead or lead converted,customerID:%d",
			customerID)

		return 0, nil
	}

	// convert customer
	convertCustomerID, err := c.convertLogic.ConvertCustomer(ctx, &convert.Datum{
		ID:      customerID,
		WithLog: true,
		LogSource: &customerpbv2.SystemSource{
			Source:   convOrderSourceTypeV2(orderEvent.SourceType),
			SourceId: orderEvent.Id,
		},
	})
	if err != nil {
		log.ErrorContextf(ctx, "OrderEventHandle convertV2 ConvertCustomer error, err: %v, customerID:%d", err,
			customerID)

		return 0, err
	}

	return convertCustomerID, nil
}
