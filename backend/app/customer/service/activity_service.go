package service

import (
	"context"
	"strconv"

	actionstate "github.com/MoeGolibrary/moego/backend/app/customer/logic/action_state"
	activitylog "github.com/MoeGolibrary/moego/backend/app/customer/logic/activity_log"
	activityrel "github.com/MoeGolibrary/moego/backend/app/customer/logic/activity_rel"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/convert"
	customerv2 "github.com/MoeGolibrary/moego/backend/app/customer/logic/customerv2"
	lifecycle "github.com/MoeGolibrary/moego/backend/app/customer/logic/life_cycle"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/note"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/source"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/tag"
	taskv2 "github.com/MoeGolibrary/moego/backend/app/customer/logic/task_v2"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type ActivityService struct {
	all *activitylog.Logic
	tl  *taskv2.Logic
	lcl *lifecycle.Logic
	asl *actionstate.Logic
	nl  *note.Logic
	sl  *source.Logic
	tgl *tag.Logic
	arl *activityrel.Logic
	cl  *customerv2.Logic
	ctl *convert.Logic
	customerpb.UnimplementedActivityServiceServer
}

func NewActivityService() *ActivityService {
	return &ActivityService{
		all: activitylog.New(),
		tl:  taskv2.New(),
		lcl: lifecycle.New(),
		asl: actionstate.New(),
		nl:  note.New(),
		sl:  source.New(),
		tgl: tag.New(),
		arl: activityrel.New(),
		cl:  customerv2.New(),
		ctl: convert.New(),
	}
}

func (s *ActivityService) CreateCustomerActivityLog(ctx context.Context,
	request *customerpb.CreateCustomerActivityLogRequest) (
	*customerpb.CreateCustomerActivityLogResponse, error) {
	activityLog, err := s.all.Create(ctx, &activitylog.CreateActivityLogDatum{
		CustomerID:          request.GetCustomerId(),
		CustomerName:        request.GetCustomerName(),
		CustomerPhoneNumber: request.GetCustomerPhoneNumber(),
		BusinessID:          request.GetBusinessId(),
		CompanyID:           request.GetCompanyId(),
		Action:              request.GetAction(),
		Source:              request.Source,
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.CreateCustomerActivityLogResponse{
		Log: activityLog,
	}, nil
}

func (s *ActivityService) UpdateCustomerActivityLog(ctx context.Context,
	request *customerpb.UpdateCustomerActivityLogRequest) (
	*customerpb.UpdateCustomerActivityLogResponse, error) {
	activityLog, err := s.all.Update(ctx, &activitylog.UpdateActivityLogDatum{
		LogID:   request.GetLogId(),
		StaffID: request.GetStaffId(),
		Action:  request.Action,
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.UpdateCustomerActivityLogResponse{
		Log: activityLog,
	}, nil
}

func (s *ActivityService) ListCustomerActivityLogs(ctx context.Context,
	request *customerpb.ListCustomerActivityLogsRequest) (
	*customerpb.ListCustomerActivityLogsResponse, error) {
	// conv list datum
	listDatum := &activitylog.ListActivityLogsDatum{}
	if pageNum := customerutils.StringToInt64WithDefault(request.GetPageToken(), 0); pageNum != 0 {
		listDatum.PageSize = int(request.GetPageSize())
		listDatum.PageNum = int(pageNum)
	} else {
		listDatum.PageSize = 10
		listDatum.PageNum = 1
	}
	if request.Filter != nil {
		listDatum.CustomerIDs = request.Filter.GetCustomerIds()
		listDatum.ActivityLogTypes = request.Filter.GetTypes()
		listDatum.CompanyIDs = convCompanyIDs(request.Filter.GetOrganizations())
		listDatum.IDs = request.Filter.GetIds()
	}

	// select
	historyLogs, total, err := s.all.List(ctx, listDatum)
	if err != nil {
		return nil, err
	}

	return &customerpb.ListCustomerActivityLogsResponse{
		ActivityLogs:  historyLogs,
		NextPageToken: strconv.Itoa(listDatum.PageNum + 1),
		TotalSize:     customerutils.ToPointer(int64(total)),
	}, nil
}

func convCompanyIDs(refs []*customerpb.OrganizationRef) []int64 {
	res := make([]int64, 0, len(refs))
	for _, ref := range refs {
		if ref.GetType() == customerpb.OrganizationRef_COMPANY {
			res = append(res, ref.GetId())
		}
	}

	return res
}

func (s *ActivityService) ConvertCustomer(ctx context.Context, request *customerpb.ConvertCustomerRequest) (
	*customerpb.ConvertCustomerResponse, error) {
	id, err := s.ctl.ConvertCustomer(ctx, &convert.Datum{
		ID:      request.GetCustomerId(),
		WithLog: false,
	})
	if err != nil {
		log.ErrorContextf(ctx, "ConvertCustomer err, customerID:%d, err:%+v", request.GetCustomerId(), err)

		return nil, err
	}

	return &customerpb.ConvertCustomerResponse{CustomerId: id}, nil
}

func (s *ActivityService) ConvertCustomersAttribute(ctx context.Context,
	request *customerpb.ConvertCustomersAttributeRequest) (
	*customerpb.ConvertCustomersAttributeResponse, error) {
	if err := s.cl.ConvertCustomersAttribute(ctx, &customerv2.ConvertCustomersAttributeDatum{
		CustomerIDs:            request.CustomerIds,
		CustomizeLifeCycleID:   request.CustomizeLifeCycleId,
		CustomizeActionStateID: request.CustomizeActionStateId,
	}); err != nil {
		log.ErrorContextf(ctx, "ConvertCustomersAttribute err, err:%+v", err)

		return nil, err
	}

	return &customerpb.ConvertCustomersAttributeResponse{}, nil
}

func (s *ActivityService) CreateCustomerTask(ctx context.Context, request *customerpb.CreateCustomerTaskRequest) (
	*customerpb.CreateCustomerTaskResponse, error) {
	task, err := s.tl.Create(ctx, &taskv2.CreateTaskDatum{
		CustomerID:      request.GetCustomerId(),
		BusinessID:      request.GetBusinessId(),
		CompanyID:       request.GetCompanyId(),
		StaffID:         request.GetStaffId(),
		Name:            request.GetName(),
		AllocateStaffID: request.AllocateStaffId,
		CompleteTime:    request.CompleteTime,
		State:           customerpb.Task_NEW,
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.CreateCustomerTaskResponse{
		Task: task,
	}, nil
}

func (s *ActivityService) UpdateCustomerTask(ctx context.Context, request *customerpb.UpdateCustomerTaskRequest) (
	*customerpb.UpdateCustomerTaskResponse, error) {
	task, err := s.tl.Update(ctx, &taskv2.UpdateTaskDatum{
		TaskID:          request.GetTaskId(),
		StaffID:         request.GetStaffId(),
		Name:            request.Name,
		AllocateStaffID: request.AllocateStaffId,
		CompleteTime:    request.CompleteTime,
		State:           request.State,
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.UpdateCustomerTaskResponse{
		Task: task,
	}, nil
}

func (s *ActivityService) ListCustomerTasks(ctx context.Context, request *customerpb.ListCustomerTasksRequest) (
	*customerpb.ListCustomerTasksResponse, error) {
	tasks, err := s.tl.List(ctx, &taskv2.ListTasksDatum{
		IDs:         request.GetFilter().GetIds(),
		CustomerIDs: request.GetFilter().GetCustomerIds(),
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.ListCustomerTasksResponse{
		Tasks: tasks,
	}, nil
}

func (s *ActivityService) DeleteCustomerTask(ctx context.Context, request *customerpb.DeleteCustomerTaskRequest) (
	*customerpb.DeleteCustomerTaskResponse, error) {
	err := s.tl.Delete(ctx, &taskv2.DeleteTasksDatum{
		TaskID:  request.GetTaskId(),
		StaffID: request.GetStaffId(),
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.DeleteCustomerTaskResponse{}, nil
}

func (s *ActivityService) CreateLifeCycle(ctx context.Context, request *customerpb.CreateLifeCycleRequest) (
	*customerpb.CreateLifeCycleResponse, error) {
	lifeCycle, err := s.lcl.Create(ctx, &lifecycle.CreateLifeCycleDatum{
		BusinessID: request.GetBusinessId(),
		CompanyID:  request.GetCompanyId(),
		StaffID:    request.GetStaffId(),
		Name:       request.GetName(),
		Sort:       request.GetSort(),
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.CreateLifeCycleResponse{
		LifeCycle: lifeCycle,
	}, nil
}

func (s *ActivityService) UpdateLifeCycles(ctx context.Context, request *customerpb.UpdateLifeCyclesRequest) (
	*customerpb.UpdateLifeCyclesResponse, error) {
	datumList := make([]*lifecycle.UpdateLifeCycleDatum, 0, len(request.GetUpdates()))
	for _, update := range request.GetUpdates() {
		if update == nil {
			continue
		}
		datumList = append(datumList, &lifecycle.UpdateLifeCycleDatum{
			ID:   update.GetId(),
			Name: update.Name,
			Sort: update.Sort,
		})
	}
	if err := s.lcl.Update(ctx, datumList, request.GetStaffId(), request.GetCompanyId()); err != nil {
		return nil, err
	}

	return &customerpb.UpdateLifeCyclesResponse{}, nil
}

func (s *ActivityService) ListLifeCycles(ctx context.Context, request *customerpb.ListLifeCyclesRequest) (
	*customerpb.ListLifeCyclesResponse, error) {
	lifeCycles, err := s.lcl.List(ctx, &lifecycle.ListLifeCyclesDatum{
		CompanyIDs: convCompanyIDs(request.GetFilter().GetOrganizations()),
		IDs:        request.GetFilter().GetIds(),
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.ListLifeCyclesResponse{
		LifeCycles: lifeCycles,
	}, nil
}

func (s *ActivityService) DeleteLifeCycle(ctx context.Context, request *customerpb.DeleteLifeCycleRequest) (
	*customerpb.DeleteLifeCycleResponse, error) {
	if err := s.lcl.Delete(ctx, &lifecycle.DeleteLifeCycleDatum{
		ID:      request.GetId(),
		StaffID: request.GetStaffId(),
	}); err != nil {
		return nil, err
	}

	return &customerpb.DeleteLifeCycleResponse{}, nil
}

func (s *ActivityService) CreateActionState(ctx context.Context, request *customerpb.CreateActionStateRequest) (
	*customerpb.CreateActionStateResponse, error) {
	actionState, err := s.asl.Create(ctx, &actionstate.CreateActionStateDatum{
		BusinessID: request.GetBusinessId(),
		CompanyID:  request.GetCompanyId(),
		StaffID:    request.GetStaffId(),
		Name:       request.GetName(),
		Sort:       request.GetSort(),
		Color:      request.GetColor(),
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.CreateActionStateResponse{
		ActionState: actionState,
	}, nil
}

func (s *ActivityService) UpdateActionStates(ctx context.Context, request *customerpb.UpdateActionStatesRequest) (
	*customerpb.UpdateActionsStatesResponse, error) {
	datumList := make([]*actionstate.UpdateActionStateDatum, 0, len(request.GetUpdates()))
	for _, update := range request.GetUpdates() {
		if update == nil {
			continue
		}
		datumList = append(datumList, &actionstate.UpdateActionStateDatum{
			ID:    update.GetId(),
			Name:  update.Name,
			Sort:  update.Sort,
			Color: update.Color,
		})
	}
	if err := s.asl.Update(ctx, datumList, request.GetStaffId(), request.GetCompanyId()); err != nil {
		return nil, err
	}

	return &customerpb.UpdateActionsStatesResponse{}, nil
}

func (s *ActivityService) ListActionStates(ctx context.Context, request *customerpb.ListActionStatesRequest) (
	*customerpb.ListActionStatesResponse, error) {
	actionStates, err := s.asl.List(ctx, &actionstate.ListActionStatesDatum{
		CompanyIDs: convCompanyIDs(request.GetFilter().GetOrganizations()),
		IDs:        request.GetFilter().GetIds(),
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.ListActionStatesResponse{
		ActionStates: actionStates,
	}, nil
}

func (s *ActivityService) DeleteActionState(ctx context.Context, request *customerpb.DeleteActionStateRequest) (
	*customerpb.DeleteActionStateResponse, error) {
	if err := s.asl.Delete(ctx, &actionstate.DeleteActionStateDatum{
		ID:      request.GetId(),
		StaffID: request.GetStaffId(),
	}); err != nil {
		return nil, err
	}

	return &customerpb.DeleteActionStateResponse{}, nil
}

func (s *ActivityService) CreateTag(ctx context.Context, request *customerpb.CreateTagRequest) (
	*customerpb.CreateTagResponse, error) {
	tag, err := s.tgl.Create(ctx, &tag.CreateTagDatum{
		CompanyID:  request.GetCompanyId(),
		BusinessID: request.GetBusinessId(),
		Name:       request.GetName(),
		Sort:       request.GetSort(),
		StaffID:    request.GetStaffId(),
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.CreateTagResponse{
		Tag: tag,
	}, nil
}

func (s *ActivityService) UpdateTags(ctx context.Context, request *customerpb.UpdateTagsRequest) (
	*customerpb.UpdateTagsResponse, error) {
	datumList := make([]*tag.UpdateTagDatum, 0, len(request.GetUpdates()))
	for _, update := range request.GetUpdates() {
		if update == nil {
			continue
		}
		datumList = append(datumList, &tag.UpdateTagDatum{
			TagID:   update.GetId(),
			Name:    update.Name,
			Sort:    update.Sort,
			StaffID: request.GetStaffId(),
		})
	}
	if err := s.tgl.Update(ctx, datumList, request.GetCompanyId()); err != nil {
		return nil, err
	}

	return &customerpb.UpdateTagsResponse{}, nil
}

func (s *ActivityService) ListTags(ctx context.Context, request *customerpb.ListTagsRequest) (
	*customerpb.ListTagsResponse, error) {
	datum := &tag.ListTagsDatum{
		CompanyIDs: convCompanyIDs(request.GetFilter().GetOrganizations()),
		IDs:        request.GetFilter().GetIds(),
	}
	tags, err := s.tgl.List(ctx, datum)
	if err != nil {
		return nil, err
	}

	return &customerpb.ListTagsResponse{
		Tags: tags,
	}, nil
}

func (s *ActivityService) DeleteTag(ctx context.Context, request *customerpb.DeleteTagRequest) (
	*customerpb.DeleteTagResponse, error) {
	err := s.tgl.Delete(ctx, &tag.DeleteTagDatum{
		TagID:   request.GetId(),
		StaffID: request.GetStaffId(),
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.DeleteTagResponse{}, nil
}

func (s *ActivityService) CoverCustomerTag(ctx context.Context,
	request *customerpb.CoverCustomerTagRequest) (*customerpb.CoverCustomerTagResponse, error) {
	if err := s.arl.CoverCustomerTags(ctx, &activityrel.CoverCustomerTagsDatum{
		CompanyID:  request.CompanyId,
		BusinessID: request.BusinessId,
		CustomerID: request.CustomerId,
		StaffID:    request.StaffId,
		TagIDs:     request.TagIds,
	}); err != nil {
		return nil, err
	}

	return &customerpb.CoverCustomerTagResponse{}, nil
}

// BatchAddCustomersTags 批量添加客户标签 upsert语义
func (s *ActivityService) BatchAddCustomersTags(ctx context.Context,
	request *customerpb.BatchAddCustomersTagsRequest) (*customerpb.BatchAddCustomersTagsResponse, error) {
	if err := s.arl.BatchAddCustomersTags(ctx, &activityrel.BatchAddCustomersTags{
		CompanyID:   request.CompanyId,
		BusinessID:  request.BusinessId,
		CustomerIDs: request.CustomerIds,
		StaffID:     request.StaffId,
		TagIDs:      request.TagIds,
	}); err != nil {
		return nil, err
	}

	return &customerpb.BatchAddCustomersTagsResponse{}, nil
}

func (s *ActivityService) ListCustomerTags(ctx context.Context,
	request *customerpb.ListCustomerTagsRequest) (*customerpb.ListCustomerTagsResponse, error) {
	customerTags, err := s.tgl.ListCustomerTags(ctx, request.GetFilter().GetCustomerIds())
	if err != nil {
		return nil, err
	}

	return &customerpb.ListCustomerTagsResponse{
		CustomerTags: customerTags,
	}, nil
}

func (s *ActivityService) CreateNote(ctx context.Context, request *customerpb.CreateNoteRequest) (
	*customerpb.CreateNoteResponse, error) {
	note, err := s.nl.Create(ctx, &note.CreateNoteDatum{
		CompanyID:    request.GetCompanyId(),
		BusinessID:   request.GetBusinessId(),
		CustomerID:   request.GetCustomerId(),
		Note:         request.GetText(),
		CreateSource: request.GetSource(),
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.CreateNoteResponse{
		Note: note,
	}, nil
}

func (s *ActivityService) UpdateNotes(ctx context.Context, request *customerpb.UpdateNotesRequest) (
	*customerpb.UpdateNotesResponse, error) {
	note, err := s.nl.Update(ctx, &note.UpdateNoteDatum{
		NoteID:       request.GetId(),
		Note:         request.Text,
		UpdateSource: request.Source,
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.UpdateNotesResponse{
		Note: note,
	}, nil
}

func (s *ActivityService) ListNotes(ctx context.Context, request *customerpb.ListNotesRequest) (
	*customerpb.ListNotesResponse, error) {
	notes, err := s.nl.List(ctx, &note.ListNotesDatum{
		CustomerIDs: request.GetFilter().GetCustomerIds(),
		IDs:         request.GetFilter().GetIds(),
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.ListNotesResponse{
		Notes: notes,
	}, nil
}

func (s *ActivityService) DeleteNote(ctx context.Context, request *customerpb.DeleteNoteRequest) (
	*customerpb.DeleteNoteResponse, error) {
	err := s.nl.Delete(ctx, &note.DeleteNoteDatum{
		NoteID:  request.GetId(),
		StaffID: request.GetStaffId(),
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.DeleteNoteResponse{}, nil
}

func (s *ActivityService) CreateSource(ctx context.Context, request *customerpb.CreateSourceRequest) (
	*customerpb.CreateSourceResponse, error) {
	source, err := s.sl.Create(ctx, &source.CreateSourceDatum{
		CompanyID:  request.GetCompanyId(),
		BusinessID: request.GetBusinessId(),
		Name:       request.GetName(),
		Sort:       request.GetSort(),
		StaffID:    request.GetStaffId(),
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.CreateSourceResponse{
		Source: source,
	}, nil
}

func (s *ActivityService) UpdateSources(ctx context.Context, request *customerpb.UpdateSourcesRequest) (
	*customerpb.UpdateSourcesResponse, error) {
	datumList := make([]*source.UpdateSourceDatum, 0, len(request.GetUpdates()))
	for _, update := range request.GetUpdates() {
		if update == nil {
			continue
		}
		datumList = append(datumList, &source.UpdateSourceDatum{
			SourceID: update.GetId(),
			Name:     update.Name,
			Sort:     update.Sort,
			StaffID:  request.GetStaffId(),
		})
	}
	if err := s.sl.Update(ctx, datumList, request.GetCompanyId()); err != nil {
		return nil, err
	}

	return &customerpb.UpdateSourcesResponse{}, nil
}

func (s *ActivityService) ListSources(ctx context.Context, request *customerpb.ListSourcesRequest) (
	*customerpb.ListSourcesResponse, error) {
	sources, err := s.sl.List(ctx, &source.ListSourcesDatum{
		CompanyIDs: convCompanyIDs(request.GetFilter().GetOrganizations()),
		IDs:        request.GetFilter().GetIds(),
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.ListSourcesResponse{
		Sources: sources,
	}, nil
}

func (s *ActivityService) DeleteSource(ctx context.Context, request *customerpb.DeleteSourceRequest) (
	*customerpb.DeleteSourceResponse, error) {
	err := s.sl.Delete(ctx, &source.DeleteSourceDatum{
		SourceID: request.GetId(),
		StaffID:  request.GetStaffId(),
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.DeleteSourceResponse{}, nil
}
