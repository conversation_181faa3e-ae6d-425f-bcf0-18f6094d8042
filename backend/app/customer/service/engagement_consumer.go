package service

import (
	"context"
	"fmt"

	"github.com/IBM/sarama"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	engagementpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/engagement/v1"
	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/customer"
	historylog "github.com/MoeGolibrary/moego/backend/app/customer/logic/history_log"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/staff"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
)

type EngagementEventConsumer struct {
	cl        *customer.Logic
	hll       *historylog.Logic
	staffRepo staff.ReadWriter
}

func NewEngagementEventConsumer() *EngagementEventConsumer {
	return &EngagementEventConsumer{
		cl:        customer.New(),
		hll:       historylog.New(),
		staffRepo: staff.New(),
	}
}

func (c *EngagementEventConsumer) EngagementEventHandle(ctx context.Context, msg *sarama.ConsumerMessage) error {
	if msg == nil {
		return nil
	}
	log.InfoContextf(ctx, "EngagementEventHandle get message, key:%s, value:%s", string(msg.Key), string(msg.Value))

	// parse msg
	eventData, _, err := parseEventData(ctx, msg)
	if err != nil {
		log.ErrorContextf(ctx, "EngagementEventHandle parseEventData err, err: %v", err)

		return err
	}
	log.InfoContextf(ctx, "EngagementEventHandle parseEventData, eventData: %+v", eventData)

	// check
	var (
		tenant = eventData.GetTenant()
		event  = eventData.GetCallUpdatedStatusEvent()
	)
	if tenant == nil || tenant.GetCompanyId() == 0 || tenant.GetBusinessId() == 0 {
		log.InfoContextf(ctx, "EngagementEventHandle eventData invalid tenant data, tenant: %+v", tenant)

		return nil
	}
	if event == nil ||
		event.GetCallLogId() == 0 ||
		event.GetCustomerId() == 0 ||
		event.GetStaffId() == 0 ||
		event.GetDirection() == engagementpb.CallingDirection_CALLING_DIRECTION_UNSPECIFIED ||
		event.GetStatus() == engagementpb.Status_CALLING_LOG_STATUS_UNSPECIFIED {
		log.InfoContextf(ctx, "EngagementEventHandle eventData invalid event, "+
			"event: %+v", event)

		return nil
	}

	// check customer lead
	customerID := event.GetCustomerId()
	customer, err := c.cl.Get(ctx, &customer.GetCustomerParams{ID: customerID})
	if err != nil {
		log.ErrorContextf(ctx, "EngagementEventHandle get customer error, err: %v, customerID:%d", err, customerID)

		return err
	}
	if customer == nil || customer.Type != customerpb.Customer_LEAD {
		log.InfoContextf(ctx, "EngagementEventHandle get customer not found, customerID:%d", customerID)

		return nil
	}

	// save to db
	logID, err := c.hll.Create(ctx, &historylog.CreateHistoryLogDatum{
		CustomerID:          customerID,
		CustomerName:        customerutils.ConvCustomerName(customer.GivenName, customer.FamilyName),
		CustomerPhoneNumber: customer.PhoneNumber,
		BusinessID:          tenant.GetBusinessId(),
		CompanyID:           tenant.GetCompanyId(),
		StaffID:             event.GetStaffId(),
		Action: &customerpb.HistoryLog_Action{
			Action: &customerpb.HistoryLog_Action_Call{ // 使用包装器类型
				Call: &customerpb.HistoryLog_Call{
					CallId:    event.GetCallLogId(),
					State:     customerpb.HistoryLog_Call_State(event.GetStatus()),
					Direction: customerpb.HistoryLog_Call_Direction(event.GetDirection()),
				},
			},
		},
		Source:     customerpb.HistoryLog_STAFF.Enum(),
		SourceID:   &event.StaffId,
		SourceName: customerutils.ToPointer(c.getStaffName(ctx, event.GetStaffId(), tenant.GetCompanyId())),
	})
	if err != nil {
		log.ErrorContextf(ctx, "EngagementEventHandle create history log error, err: %v, callID:%d", err.Error(),
			event.GetCallLogId())

		return err
	}

	log.InfoContextf(ctx, "EngagementEventHandle create history log success, logID:%d, customerID:%d",
		logID, customerID)

	return nil
}

func (c *EngagementEventConsumer) getStaffName(ctx context.Context, id int64, companyID int64) string {
	detail, err := c.staffRepo.GetStaffDetail(ctx, id, companyID, 0)
	if err != nil {
		log.ErrorContextf(ctx, "EngagementEventConsumer getStaffName err, id:%d, err: %v", id, err)

		return ""
	}

	return fmt.Sprintf("%s %s", detail.GetFirstName(), detail.GetLastName())
}

func parseEventData(ctx context.Context, msg *sarama.ConsumerMessage) (
	*eventbuspb.EventData, eventbuspb.EventType, error) {
	unmarshaler := protojson.UnmarshalOptions{
		DiscardUnknown: true, // new fields should not break old consumer.
	}
	var event eventbuspb.Event
	if err := unmarshaler.Unmarshal(msg.Value, &event); err != nil {
		log.ErrorContextf(ctx, "parseEventData unmarshal event err: %v, value: %v", err, string(msg.Value))

		return nil, eventbuspb.EventType_TYPE_UNSPECIFIED, err
	}

	var detail proto.Message
	var err error
	if detail, err = event.Detail.UnmarshalNew(); err != nil {
		log.ErrorContextf(ctx, "parseEventData unmarshal detail err: %v, value: %v", err, string(msg.Value))

		return nil, eventbuspb.EventType_TYPE_UNSPECIFIED, err
	}

	var eventData *eventbuspb.EventData
	if ed, ok := detail.(*eventbuspb.EventData); ok {
		eventData = ed
	} else {
		log.ErrorContextf(ctx, "parseEventData invalid detail type, expected *eventbuspb.EventData, got %T", detail)

		return nil, eventbuspb.EventType_TYPE_UNSPECIFIED, fmt.Errorf("invalid detail type")
	}

	return eventData, event.EventType, nil
}
