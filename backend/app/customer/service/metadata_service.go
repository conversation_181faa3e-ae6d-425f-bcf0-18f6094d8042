package service

import (
	"context"

	"github.com/lib/pq"
	"google.golang.org/grpc/codes"
	"google.golang.org/protobuf/types/known/emptypb"
	"gorm.io/datatypes"

	"github.com/MoeGolibrary/moego/backend/app/customer/logic/address"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/contact"
	contacttag "github.com/MoeGolibrary/moego/backend/app/customer/logic/contact_tag"
	customerrelateddata "github.com/MoeGolibrary/moego/backend/app/customer/logic/customer_related_data"
	customerv2 "github.com/MoeGolibrary/moego/backend/app/customer/logic/customerv2"
	customfieldlogic "github.com/MoeGolibrary/moego/backend/app/customer/logic/customfield"
	leadlogic "github.com/MoeGolibrary/moego/backend/app/customer/logic/lead"
	metadatalogic "github.com/MoeGolibrary/moego/backend/app/customer/logic/metadata"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/redis"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type MetadataService struct {
	customerLogic            *customerv2.Logic
	leadLogic                *leadlogic.Logic
	contactLogic             *contact.Logic
	addressLogic             *address.Logic
	contactTagLogic          *contacttag.Logic
	customFieldLogic         *customfieldlogic.Logic
	customerRelatedDataLogic *customerrelateddata.Logic
	metadataLogic            *metadatalogic.Logic
	redis                    redis.API
	customerpb.UnimplementedMetadataServiceServer
}

func NewMetadataService() *MetadataService {
	return &MetadataService{
		customerLogic:            customerv2.New(),
		leadLogic:                leadlogic.New(),
		contactLogic:             contact.New(),
		addressLogic:             address.New(),
		contactTagLogic:          contacttag.New(),
		customFieldLogic:         customfieldlogic.New(),
		customerRelatedDataLogic: customerrelateddata.New(),
		metadataLogic:            metadatalogic.New(),
		redis:                    redis.New(),
	}
}

func (s *MetadataService) CreateCustomer(ctx context.Context,
	req *customerpb.CreateCustomerRequest) (*customerpb.Customer, error) {
	customer := req.GetCustomer()
	if customer == nil {
		return nil, errs.Newm(codes.InvalidArgument, "customer is required")
	}

	if customer.Organization == nil {
		return nil, errs.Newm(codes.InvalidArgument, "organization is required")
	}

	logicCustomer := &customerv2.Customer{}
	logicCustomer.FromProto(customer)

	createdCustomer, err := s.customerLogic.CreateCustomer(ctx, logicCustomer)
	if err != nil {
		return nil, err
	}

	return createdCustomer.ToPB(), nil
}

func (s *MetadataService) GetCustomer(ctx context.Context,
	req *customerpb.GetCustomerRequest) (*customerpb.Customer, error) {
	customer, err := s.customerLogic.GetCustomer(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return customer.ToPB(), nil
}

func (s *MetadataService) ListCustomers(ctx context.Context,
	req *customerpb.ListCustomersRequest) (*customerpb.ListCustomersResponse, error) {

	// 构建过滤器，添加nil检查
	filter := &customerv2.ListCustomersFilter{
		CustomerType:      customerpb.CustomerType_CUSTOMER,
		States:            req.Filter.States,
		OwnerStaffIDs:     req.Filter.OwnerStaffIds,
		LifecycleIDs:      req.Filter.LifecycleIds,
		ReferralSourceIDs: req.Filter.ReferralSourceIds,
	}

	// 安全地访问Organization字段
	if req.Filter.Organization != nil {
		filter.OrganizationType = &req.Filter.Organization.Type
		filter.OrganizationID = req.Filter.Organization.Id
	}

	pagination := &customerv2.ListCustomersPagination{
		PageSize:        req.PageSize,
		ReturnTotalSize: req.ReturnTotalSize,
	}

	if req.PageToken != "" {
		pagination.Cursor = req.PageToken
	}

	orderBy := &customerv2.ListCustomersOrderBy{
		Field:     customerpb.ListCustomersRequest_Sorting_ID,
		Direction: customerpb.ListCustomersRequest_Sorting_ASC,
	}
	if req.Sorting != nil {
		orderBy.Field = req.Sorting.Field
		orderBy.Direction = req.Sorting.Direction
	}

	response, err := s.customerLogic.ListCustomers(ctx, &customerv2.ListCustomersRequest{
		Filter:     filter,
		Pagination: pagination,
		OrderBy:    orderBy,
	})
	if err != nil {
		return nil, err
	}
	customers := make([]*customerpb.Customer, 0, len(response.Customers))
	for _, customer := range response.Customers {
		customers = append(customers, customer.ToPB())
	}

	return &customerpb.ListCustomersResponse{
		Customers:     customers,
		NextPageToken: response.NextToken,
		TotalSize:     response.TotalSize,
	}, nil
}

func (s *MetadataService) UpdateCustomer(ctx context.Context,
	req *customerpb.UpdateCustomerRequest) (*customerpb.Customer, error) {
	ref := req.GetRef()
	if ref == nil {
		return nil, errs.Newm(codes.InvalidArgument, "ref is required")
	}

	// 验证 state 字段不能为 DELETED
	if ref.State != nil && *ref.State == customerpb.Customer_DELETED {
		return nil, errs.Newm(codes.InvalidArgument, "state cannot be DELETED")
	}

	customer, err := s.customerLogic.UpdateCustomer(ctx, req.Id, &customerv2.UpdateCustomerRequest{
		GivenName:        ref.GetGivenName(),
		FamilyName:       ref.GetFamilyName(),
		CustomFields:     datatypes.JSON(customerutils.JSONMarshalNoErr(ref.GetCustomFields().AsMap())),
		LifeCycleID:      ref.GetLifecycleId(),
		OwnerStaffID:     ref.GetOwnerStaffId(),
		ActionStateID:    ref.GetActionStateId(),
		AvatarPath:       ref.GetAvatarPath(),
		State:            ref.GetState(),
		ReferralSourceID: ref.GetReferralSourceId(),
	})
	if err != nil {
		return nil, err
	}

	return customer.ToPB(), nil
}

func (s *MetadataService) DeleteCustomer(ctx context.Context,
	req *customerpb.DeleteCustomerRequest) (*emptypb.Empty, error) {
	err := s.customerLogic.DeleteCustomer(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *MetadataService) CreateContact(ctx context.Context,
	req *customerpb.CreateContactRequest) (*customerpb.Contact, error) {
	pbContact := req.GetContact()
	if pbContact == nil {
		return nil, errs.Newm(codes.InvalidArgument, "contact is required")
	}
	metadata, err := s.metadataLogic.GetMetadata(ctx, pbContact.CustomerId)
	if err != nil {
		return nil, err
	}

	contact := &contact.Contact{
		ID:         pbContact.Id,
		CustomerID: metadata.ID,
		GivenName:  pbContact.GetGivenName(),
		FamilyName: pbContact.GetFamilyName(),
		IsSelf:     pbContact.GetIsSelf(),
		Email:      pbContact.GetEmail(),
		Note:       pbContact.GetNote(),
	}

	if len(pbContact.GetTags()) > 0 {
		for _, tag := range pbContact.GetTags() {
			contact.Tags = append(contact.Tags, &contacttag.ContactTag{
				ID:               tag.Id,
				Name:             tag.Name,
				State:            tag.State,
				Color:            tag.Color,
				SortOrder:        tag.SortOrder,
				Description:      tag.Description,
				Type:             tag.Type,
				OrganizationType: metadata.OrganizationType,
				OrganizationID:   metadata.OrganizationID,
			})
		}
	}

	if phone := pbContact.GetPhone(); phone != nil {
		contact.Phone = phone.GetE164Number()
	}

	createdContact, err := s.contactLogic.Create(ctx, contact)
	if err != nil {
		return nil, err
	}

	return createdContact.ToPB(), nil
}

func (s *MetadataService) GetContact(ctx context.Context,
	req *customerpb.GetContactRequest) (*customerpb.Contact, error) {
	contact, err := s.contactLogic.Get(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return contact.ToPB(), nil
}

func (s *MetadataService) ListContacts(ctx context.Context,
	req *customerpb.ListContactsRequest) (*customerpb.ListContactsResponse, error) {

	// 构建过滤器，添加nil检查
	filter := &contact.ListContactsFilter{
		States: req.Filter.GetStates(),
	}
	if req.Filter != nil {
		if len(req.Filter.Ids) > 0 {
			filter.IDs = req.Filter.Ids
		}
		if len(req.Filter.CustomerIds) > 0 {
			filter.CustomerIDs = req.Filter.CustomerIds
		}
	}

	pagination := &contact.ListContactsPagination{
		PageSize:        req.PageSize,
		ReturnTotalSize: req.ReturnTotalSize,
	}
	if req.PageToken != "" {
		pagination.Cursor = req.PageToken
	}

	orderBy := &contact.ListContactsOrderBy{
		Field:     customerpb.ListContactsRequest_Sorting_CREATED_TIME,
		Direction: customerpb.ListContactsRequest_Sorting_DESC,
	}
	if req.Sorting != nil {
		orderBy.Field = req.Sorting.Field
		orderBy.Direction = req.Sorting.Direction
	}

	response, err := s.contactLogic.List(ctx, &contact.ListContactsRequest{
		Filter:     filter,
		Pagination: pagination,
		OrderBy:    orderBy,
	})
	if err != nil {
		return nil, err
	}
	contacts := make([]*customerpb.Contact, 0, len(response.Contacts))
	for _, c := range response.Contacts {
		contacts = append(contacts, c.ToPB())
	}

	return &customerpb.ListContactsResponse{
		Contacts:      contacts,
		NextPageToken: response.NextToken,
		TotalSize:     response.TotalSize,
	}, nil
}

func (s *MetadataService) UpdateContact(ctx context.Context,
	req *customerpb.UpdateContactRequest) (*customerpb.Contact, error) {
	ref := req.GetRef()
	if ref == nil {
		return nil, errs.Newm(codes.InvalidArgument, "ref is required")
	}

	// 验证 state 字段不能为 DELETED
	if ref.State != nil && *ref.State == customerpb.Contact_DELETED {
		return nil, errs.Newm(codes.InvalidArgument, "state cannot be DELETED")
	}

	updateReq := &contact.UpdateContactRequest{
		ID:         req.Id,
		GivenName:  ref.GetGivenName(),
		FamilyName: ref.GetFamilyName(),
		Email:      ref.GetEmail(),
		TagIDs:     ref.GetTagIds(),
		Note:       ref.GetNote(),
		State:      ref.GetState(),
	}

	// 安全处理 phone 字段
	if ref.GetPhone() != nil {
		updateReq.Phone = ref.GetPhone().GetE164Number()
	}

	contact, err := s.contactLogic.Update(ctx, updateReq)

	if err != nil {
		return nil, err
	}

	return contact.ToPB(), nil
}

func (s *MetadataService) DeleteContact(ctx context.Context,
	req *customerpb.DeleteContactRequest) (*emptypb.Empty, error) {
	err := s.contactLogic.Delete(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *MetadataService) CreateContactTag(ctx context.Context,
	req *customerpb.CreateContactTagRequest) (*customerpb.ContactTag, error) {
	contactTag := req.GetContactTag()
	if contactTag == nil {
		return nil, errs.Newm(codes.InvalidArgument, "contact tag is required")
	}

	if contactTag.Organization == nil {
		return nil, errs.Newm(codes.InvalidArgument, "organization is required")
	}

	logicContactTag, err := s.contactTagLogic.Create(ctx, &contacttag.ContactTag{
		ID:               contactTag.GetId(),
		OrganizationType: contactTag.GetOrganization().GetType(),
		OrganizationID:   contactTag.GetOrganization().GetId(),
		Name:             contactTag.GetName(),
		State:            contactTag.GetState(),
		Color:            contactTag.GetColor(),
		SortOrder:        contactTag.GetSortOrder(),
		Description:      contactTag.GetDescription(),
		Type:             contactTag.GetType(),
	})
	if err != nil {
		return nil, err
	}

	return logicContactTag.ToPB(), nil
}

func (s *MetadataService) GetContactTag(ctx context.Context,
	req *customerpb.GetContactTagRequest) (*customerpb.ContactTag, error) {
	contactTag, err := s.contactTagLogic.Get(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return contactTag.ToPB(), nil
}

func (s *MetadataService) ListContactTags(ctx context.Context,
	req *customerpb.ListContactTagsRequest) (*customerpb.ListContactTagsResponse, error) {

	if req.Filter == nil || req.Filter.Organization == nil {
		return nil, errs.Newm(codes.InvalidArgument, "organization is required")
	}

	filter := &contacttag.ListContactTagsFilter{
		States:           req.Filter.GetStates(),
		Name:             req.Filter.GetName(),
		OrganizationType: req.Filter.GetOrganization().GetType(),
		OrganizationID:   req.Filter.GetOrganization().GetId(),
	}

	if len(req.Filter.GetIds()) > 0 {
		filter.IDs = req.Filter.GetIds()
	}

	pagination := &contacttag.ListContactTagsPagination{
		PageSize:        req.PageSize,
		ReturnTotalSize: req.ReturnTotalSize,
	}
	if req.PageToken != "" {
		pagination.Cursor = req.PageToken
	}

	orderBy := &contacttag.ListContactTagsOrderBy{
		Field:     customerpb.ListContactTagsRequest_Sorting_CREATED_TIME,
		Direction: customerpb.ListContactTagsRequest_Sorting_DESC,
	}
	if req.Sorting != nil {
		orderBy.Field = req.Sorting.Field
		orderBy.Direction = req.Sorting.Direction
	}
	response, err := s.contactTagLogic.List(ctx, &contacttag.ListContactTagsRequest{
		Filter:     filter,
		Pagination: pagination,
		OrderBy:    orderBy,
	})
	if err != nil {
		return nil, err
	}
	contactTags := make([]*customerpb.ContactTag, 0, len(response.ContactTags))
	for _, c := range response.ContactTags {
		contactTags = append(contactTags, c.ToPB())
	}

	return &customerpb.ListContactTagsResponse{
		ContactTags:   contactTags,
		NextPageToken: response.NextToken,
		TotalSize:     response.TotalSize,
	}, nil
}

func (s *MetadataService) UpdateContactTag(ctx context.Context,
	req *customerpb.UpdateContactTagRequest) (*customerpb.ContactTag, error) {
	ref := req.GetRef()
	if ref == nil {
		return nil, errs.Newm(codes.InvalidArgument, "ref is required")
	}

	// 验证 state 字段不能为 DELETED
	if ref.State != nil && *ref.State == customerpb.ContactTag_DELETED {
		return nil, errs.Newm(codes.InvalidArgument, "state cannot be DELETED")
	}

	contactTag, err := s.contactTagLogic.Update(ctx, &contacttag.UpdateContactTagRequest{
		ID:        req.Id,
		Name:      ref.GetName(),
		Color:     ref.GetColor(),
		SortOrder: ref.GetSortOrder(),
		State:     ref.GetState(),
	})
	if err != nil {
		return nil, err
	}

	return contactTag.ToPB(), nil
}

func (s *MetadataService) DeleteContactTag(ctx context.Context,
	req *customerpb.DeleteContactTagRequest) (*emptypb.Empty, error) {
	err := s.contactTagLogic.Delete(ctx, req.Id) // 不再使用 inactivate 参数，直接硬删除
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *MetadataService) CreateLead(ctx context.Context,
	req *customerpb.CreateLeadRequest) (*customerpb.Lead, error) {
	lead := req.GetLead()
	if lead == nil {
		return nil, errs.Newm(codes.InvalidArgument, "lead is required")
	}
	logicLead, err := s.leadLogic.Create(ctx, &leadlogic.Lead{
		ID:               lead.GetId(),
		OrganizationType: lead.GetOrganization().GetType(),
		OrganizationID:   lead.GetOrganization().GetId(),
		GivenName:        lead.GetGivenName(),
		FamilyName:       lead.GetFamilyName(),
		CustomFields:     datatypes.JSON(customerutils.JSONMarshalNoErr(lead.GetCustomFields().AsMap())),
		LifeCycleID:      lead.GetLifecycleId(),
		OwnerStaffID:     lead.GetOwnerStaffId(),
		ActionStateID:    lead.GetActionStateId(),
		AvatarPath:       lead.GetAvatarPath(),
		ReferralSourceID: lead.GetReferralSourceId(),
	})
	if err != nil {
		return nil, err
	}

	return logicLead.ToPB(), nil
}

func (s *MetadataService) GetLead(ctx context.Context,
	req *customerpb.GetLeadRequest) (*customerpb.Lead, error) {
	lead, err := s.leadLogic.Get(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return lead.ToPB(), nil
}

func (s *MetadataService) ListLeads(ctx context.Context,
	req *customerpb.ListLeadsRequest) (*customerpb.ListLeadsResponse, error) {
	filter := &leadlogic.ListLeadsFilter{}
	if req.Filter != nil {
		if req.Filter.Organization != nil {
			filter.OrganizationType = &req.Filter.Organization.Type
			filter.OrganizationID = req.Filter.Organization.Id
		}
		filter.States = req.Filter.GetStates()
		if len(req.Filter.Ids) > 0 {
			filter.IDs = req.Filter.Ids
		}
		filter.ConvertedCustomerIDs = req.Filter.ConvertedCustomerIds
		filter.IsConverted = req.Filter.IsConverted
	}
	pagination := &leadlogic.ListLeadsPagination{
		PageSize:        req.PageSize,
		ReturnTotalSize: req.ReturnTotalSize,
	}
	if req.PageToken != "" {
		pagination.Cursor = req.PageToken
	}

	orderBy := &leadlogic.ListLeadsOrderBy{
		Field:     customerpb.ListLeadsRequest_Sorting_CREATED_TIME,
		Direction: customerpb.ListLeadsRequest_Sorting_DESC,
	}
	if req.Sorting != nil {
		orderBy.Field = req.Sorting.Field
		orderBy.Direction = req.Sorting.Direction
	}

	response, err := s.leadLogic.List(ctx, &leadlogic.ListLeadsRequest{
		Filter:     filter,
		Pagination: pagination,
		OrderBy:    orderBy,
	})
	if err != nil {
		return nil, err
	}
	leads := make([]*customerpb.Lead, 0, len(response.Leads))
	for _, l := range response.Leads {
		leads = append(leads, l.ToPB())
	}

	return &customerpb.ListLeadsResponse{
		Leads:         leads,
		NextPageToken: response.NextToken,
		TotalSize:     response.TotalSize,
	}, nil
}

func (s *MetadataService) UpdateLead(ctx context.Context,
	req *customerpb.UpdateLeadRequest) (*customerpb.Lead, error) {
	ref := req.GetRef()
	if ref == nil {
		return nil, errs.Newm(codes.InvalidArgument, "ref is required")
	}

	// 验证 state 字段不能为 DELETED
	if ref.State != nil && *ref.State == customerpb.Lead_DELETED {
		return nil, errs.Newm(codes.InvalidArgument, "state cannot be DELETED")
	}

	lead, err := s.leadLogic.Update(ctx, req.Id, &leadlogic.UpdateLeadRequest{
		GivenName:    ref.GetGivenName(),
		FamilyName:   ref.GetFamilyName(),
		CustomFields: datatypes.JSON(customerutils.JSONMarshalNoErr(ref.GetCustomFields().AsMap())),
		LifeCycleID:  ref.GetLifecycleId(),
		OwnerStaffID: ref.GetOwnerStaffId(),
		State:        ref.GetState(),
		AvatarPath:   ref.GetAvatarPath(),
	})
	if err != nil {
		return nil, err
	}

	return lead.ToPB(), nil
}

func (s *MetadataService) DeleteLead(ctx context.Context,
	req *customerpb.DeleteLeadRequest) (*emptypb.Empty, error) {
	err := s.leadLogic.Delete(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *MetadataService) CreateAddress(ctx context.Context,
	req *customerpb.CreateAddressRequest) (*customerpb.Address, error) {
	pbAddress := req.GetAddress()
	if pbAddress == nil {
		return nil, errs.Newm(codes.InvalidArgument, "address is required")
	}

	// 验证客户是否存在
	metadata, err := s.metadataLogic.GetMetadata(ctx, pbAddress.CustomerId)
	if err != nil {
		return nil, err
	}

	// 构建地址请求
	addressReq := &address.Address{
		CustomerID: metadata.ID,
		Type:       pbAddress.Type,
	}

	if pbAddress.Address == nil {
		return nil, errs.Newm(codes.InvalidArgument, "address is required")
	}

	// 从PostalAddress中提取地址信息
	if pbAddress.Address != nil {
		addressReq.Revision = int64(pbAddress.Address.Revision)
		addressReq.Organization = pbAddress.Address.Organization
		addressReq.SortingCode = pbAddress.Address.SortingCode
		addressReq.RegionCode = pbAddress.Address.RegionCode
		addressReq.LanguageCode = pbAddress.Address.LanguageCode
		addressReq.PostalCode = pbAddress.Address.PostalCode
		addressReq.AdministrativeArea = pbAddress.Address.AdministrativeArea
		addressReq.Locality = pbAddress.Address.Locality
		addressReq.Sublocality = pbAddress.Address.Sublocality
		addressReq.AddressLines = pq.StringArray(pbAddress.Address.AddressLines)
		addressReq.Recipients = pq.StringArray(pbAddress.Address.Recipients)
	}

	// 从LatLng中提取坐标信息
	if pbAddress.Latlng != nil {
		addressReq.Latitude = pbAddress.Latlng.Latitude
		addressReq.Longitude = pbAddress.Latlng.Longitude
	}

	createdAddress, err := s.addressLogic.Create(ctx, addressReq)
	if err != nil {
		return nil, err
	}

	return createdAddress.ToPB(), nil
}

func (s *MetadataService) GetAddress(ctx context.Context,
	req *customerpb.GetAddressRequest) (*customerpb.Address, error) {
	address, err := s.addressLogic.Get(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return address.ToPB(), nil
}

func (s *MetadataService) ListAddresses(ctx context.Context,
	req *customerpb.ListAddressesRequest) (*customerpb.ListAddressesResponse, error) {
	// 构建过滤器
	filter := &address.ListAddressesFilter{
		States: req.Filter.GetStates(),
	}
	if req.Filter != nil {
		if len(req.Filter.Ids) > 0 {
			filter.IDs = req.Filter.Ids
		}
		if req.Filter.CustomerId != 0 {
			filter.CustomerID = req.Filter.CustomerId
		}
	}

	pagination := &address.ListAddressesPagination{
		PageSize:        req.PageSize,
		ReturnTotalSize: req.ReturnTotalSize,
	}
	if req.PageToken != "" {
		pagination.Cursor = req.PageToken
	}

	orderBy := &address.ListAddressesOrderBy{
		Field:     customerpb.ListAddressesRequest_Sorting_CREATED_TIME,
		Direction: customerpb.ListAddressesRequest_Sorting_DESC,
	}
	if req.Sorting != nil {
		orderBy.Field = req.Sorting.Field
		orderBy.Direction = req.Sorting.Direction
	}

	response, err := s.addressLogic.List(ctx, &address.ListAddressesRequest{
		Filter:     filter,
		Pagination: pagination,
		OrderBy:    orderBy,
	})
	if err != nil {
		return nil, err
	}

	addresses := make([]*customerpb.Address, 0, len(response.Addresses))
	for _, addr := range response.Addresses {
		addresses = append(addresses, addr.ToPB())
	}

	return &customerpb.ListAddressesResponse{
		Addresses:     addresses,
		NextPageToken: response.NextToken,
		TotalSize:     response.TotalSize,
	}, nil
}

func (s *MetadataService) UpdateAddress(ctx context.Context,
	req *customerpb.UpdateAddressRequest) (*customerpb.Address, error) {
	ref := req.GetRef()
	if ref == nil {
		return nil, errs.Newm(codes.InvalidArgument, "ref is required")
	}

	// 验证 state 字段不能为 DELETED
	if ref.State != nil && *ref.State == customerpb.Address_DELETED {
		return nil, errs.Newm(codes.InvalidArgument, "state cannot be DELETED")
	}

	reqAddress := ref.GetAddress()

	// 构建更新请求
	updateReq := &address.UpdateAddressRequest{}

	// 从UpdateRef中提取地址信息
	if reqAddress.GetAdministrativeArea() != "" {
		updateReq.AdministrativeArea = reqAddress.GetAdministrativeArea()
	}
	if reqAddress.GetLocality() != "" {
		updateReq.Locality = reqAddress.GetLocality()
	}
	if reqAddress.GetSublocality() != "" {
		updateReq.Sublocality = reqAddress.GetSublocality()
	}
	if len(reqAddress.GetAddressLines()) > 0 {
		updateReq.AddressLines = pq.StringArray(reqAddress.GetAddressLines())
	}
	if reqAddress.GetPostalCode() != "" {
		updateReq.PostalCode = reqAddress.GetPostalCode()
	}
	if reqAddress.GetOrganization() != "" {
		updateReq.Organization = reqAddress.GetOrganization()
	}
	if reqAddress.GetSortingCode() != "" {
		updateReq.SortingCode = reqAddress.GetSortingCode()
	}
	if reqAddress.GetRevision() != 0 {
		updateReq.Revision = int64(reqAddress.GetRevision())
	}
	if len(reqAddress.GetRecipients()) > 0 {
		updateReq.Recipients = pq.StringArray(reqAddress.GetRecipients())
	}

	if reqAddress.GetRegionCode() != "" {
		updateReq.RegionCode = reqAddress.GetRegionCode()
	}
	if reqAddress.GetLanguageCode() != "" {
		updateReq.LanguageCode = reqAddress.GetLanguageCode()
	}
	if ref.GetType() != customerpb.Address_TYPE_UNSPECIFIED {
		updateReq.Type = ref.GetType()
	}
	if ref.GetState() != customerpb.Address_STATE_UNSPECIFIED {
		updateReq.State = ref.GetState()
	}

	updatedAddress, err := s.addressLogic.Update(ctx, req.Id, updateReq)
	if err != nil {
		return nil, err
	}

	return updatedAddress.ToPB(), nil
}

func (s *MetadataService) DeleteAddress(ctx context.Context,
	req *customerpb.DeleteAddressRequest) (*emptypb.Empty, error) {
	err := s.addressLogic.Delete(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *MetadataService) CreateCustomField(ctx context.Context,
	req *customerpb.CreateCustomFieldRequest) (*customerpb.CustomField, error) {
	customField := req.GetCustomField()

	// 转换为 logic 层的实体
	dwo, err := s.convertToDefinitionWithOptions(customField)
	if err != nil {
		return nil, err
	}

	createdDwo, err := s.customFieldLogic.Create(ctx, dwo)
	if err != nil {
		return nil, err
	}

	return createdDwo.ToPB(), nil
}

func (s *MetadataService) GetCustomField(ctx context.Context,
	req *customerpb.GetCustomFieldRequest) (*customerpb.CustomField, error) {
	params := &customfieldlogic.GetDefinitionParams{
		ID: req.Id,
	}

	dwo, err := s.customFieldLogic.Get(ctx, params)
	if err != nil {
		return nil, err
	}

	return dwo.ToPB(), nil
}

func (s *MetadataService) ListCustomFields(ctx context.Context,
	req *customerpb.ListCustomFieldsRequest) (*customerpb.ListCustomFieldsResponse, error) {
	filter := req.GetFilter()
	if filter == nil {
		return nil, errs.Newm(codes.InvalidArgument, "filter is required")
	}

	if filter.Organization == nil {
		return nil, errs.Newm(codes.InvalidArgument, "organization is required")
	}

	// 构建过滤器
	listFilter := &customfieldlogic.ListDefinitionsFilter{
		IDs:              filter.Ids,
		OrganizationType: filter.Organization.Type,
		OrganizationID:   filter.Organization.Id,
		AssociationType:  filter.AssociationType,
		States:           filter.States,
	}

	// 构建分页
	pagination := &customfieldlogic.ListDefinitionsPagination{
		PageSize:        req.PageSize,
		Cursor:          req.PageToken,
		ReturnTotalSize: req.ReturnTotalSize,
	}

	// 构建排序
	var orderBy *customfieldlogic.ListDefinitionsOrderBy
	if req.Sorting != nil {
		orderBy = &customfieldlogic.ListDefinitionsOrderBy{
			Field:     req.Sorting.Field,
			Direction: req.Sorting.Direction,
		}
	}

	params := &customfieldlogic.ListDefinitionsParams{
		Filter:     listFilter,
		Pagination: pagination,
		OrderBy:    orderBy,
	}

	result, err := s.customFieldLogic.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 转换为 protobuf
	customFields := make([]*customerpb.CustomField, 0, len(result.Definitions))
	for _, dwo := range result.Definitions {
		customFields = append(customFields, dwo.ToPB())
	}

	response := &customerpb.ListCustomFieldsResponse{
		CustomFields:  customFields,
		NextPageToken: result.NextToken,
	}

	if result.TotalSize != nil {
		response.TotalSize = result.TotalSize
	}

	return response, nil
}

func (s *MetadataService) UpdateCustomField(ctx context.Context,
	req *customerpb.UpdateCustomFieldRequest) (*customerpb.CustomField, error) {
	// 先获取现有的自定义字段
	getParams := &customfieldlogic.GetDefinitionParams{
		ID: req.Id,
	}

	dwo, err := s.customFieldLogic.Get(ctx, getParams)
	if err != nil {
		return nil, err
	}

	// 应用更新
	err = s.applyUpdates(dwo, req.GetRef())
	if err != nil {
		return nil, err
	}

	updatedDwo, err := s.customFieldLogic.Update(ctx, dwo)
	if err != nil {
		return nil, err
	}

	return updatedDwo.ToPB(), nil
}

// ==================== Unified Metadata Methods ====================

func (s *MetadataService) CreateMetadata(ctx context.Context,
	req *customerpb.CreateMetadataRequest) (*customerpb.Metadata, error) {
	metadata := req.GetMetadata()
	if metadata == nil {
		return nil, errs.Newm(codes.InvalidArgument, "metadata is required")
	}

	if metadata.Organization == nil {
		return nil, errs.Newm(codes.InvalidArgument, "organization is required")
	}

	if metadata.CustomerType == customerpb.CustomerType_CUSTOMER_TYPE_UNSPECIFIED {
		return nil, errs.Newm(codes.InvalidArgument, "customer_type is required")
	}

	logicMetadata := &metadatalogic.Metadata{}
	logicMetadata.FromProto(metadata)

	createdMetadata, err := s.metadataLogic.CreateMetadata(ctx, logicMetadata)
	if err != nil {
		return nil, err
	}

	return createdMetadata.ToPB(), nil
}

func (s *MetadataService) GetMetadata(ctx context.Context,
	req *customerpb.GetMetadataRequest) (*customerpb.Metadata, error) {
	metadata, err := s.metadataLogic.GetMetadata(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return metadata.ToPB(), nil
}

func (s *MetadataService) ListMetadata(ctx context.Context,
	req *customerpb.ListMetadataRequest) (*customerpb.ListMetadataResponse, error) {
	if req.Filter == nil {
		return nil, errs.Newm(codes.InvalidArgument, "filter is required")
	}

	if req.Filter.CustomerType == customerpb.CustomerType_CUSTOMER_TYPE_UNSPECIFIED {
		return nil, errs.Newm(codes.InvalidArgument, "customer_type is required in filter")
	}

	if req.Filter.Organization == nil {
		return nil, errs.Newm(codes.InvalidArgument, "organization is required in filter")
	}

	logicReq := &metadatalogic.ListMetadataRequest{}
	logicReq.FromProto(req)

	response, err := s.metadataLogic.ListMetadata(ctx, logicReq)
	if err != nil {
		return nil, err
	}

	metadata := make([]*customerpb.Metadata, 0, len(response.Metadata))
	for _, m := range response.Metadata {
		metadata = append(metadata, m.ToPB())
	}

	return &customerpb.ListMetadataResponse{
		Metadata:      metadata,
		NextPageToken: response.NextToken,
		TotalSize:     response.TotalSize,
	}, nil
}

func (s *MetadataService) UpdateMetadata(ctx context.Context,
	req *customerpb.UpdateMetadataRequest) (*customerpb.Metadata, error) {
	ref := req.GetRef()
	if ref == nil {
		return nil, errs.Newm(codes.InvalidArgument, "ref is required")
	}

	if ref.State != nil && *ref.State == customerpb.Metadata_DELETED {
		return nil, errs.Newm(codes.InvalidArgument, "state cannot be DELETED")
	}

	updateReq := &metadatalogic.UpdateMetadataRequest{}
	updateReq.FromProto(ref)

	metadata, err := s.metadataLogic.UpdateMetadata(ctx, req.Id, updateReq)
	if err != nil {
		return nil, err
	}

	return metadata.ToPB(), nil
}

func (s *MetadataService) DeleteMetadata(ctx context.Context,
	req *customerpb.DeleteMetadataRequest) (*emptypb.Empty, error) {
	err := s.metadataLogic.DeleteMetadata(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *MetadataService) BatchUpdateCustomFields(ctx context.Context,
	req *customerpb.BatchUpdateCustomFieldsRequest) (*customerpb.BatchUpdateCustomFieldsResponse, error) {

	if len(req.Requests) == 0 {
		return nil, errs.Newm(codes.InvalidArgument, "requests is required")
	}

	if req.Parent == "" {
		return nil, errs.Newm(codes.InvalidArgument, "parent is required")
	}

	// 准备批量更新的实体列表
	var updates []*customfieldlogic.DefinitionWithOptions

	for _, updateReq := range req.Requests {
		// 先获取现有的自定义字段
		getParams := &customfieldlogic.GetDefinitionParams{
			ID: updateReq.Id,
		}

		dwo, err := s.customFieldLogic.Get(ctx, getParams)
		if err != nil {
			return nil, err
		}

		// 应用更新
		err = s.applyUpdates(dwo, updateReq.GetRef())
		if err != nil {
			return nil, err
		}

		updates = append(updates, dwo)
	}

	// 调用 logic 层的批量更新
	updatedDwos, err := s.customFieldLogic.BatchUpdate(ctx, req.Parent, updates)
	if err != nil {
		return nil, err
	}

	// 转换回 protobuf
	var updatedFields []*customerpb.CustomField
	for _, dwo := range updatedDwos {
		updatedFields = append(updatedFields, dwo.ToPB())
	}

	return &customerpb.BatchUpdateCustomFieldsResponse{
		CustomFields: updatedFields,
	}, nil
}

func (s *MetadataService) DeleteCustomField(ctx context.Context,
	req *customerpb.DeleteCustomFieldRequest) (*emptypb.Empty, error) {
	// 先获取现有的自定义字段
	getParams := &customfieldlogic.GetDefinitionParams{
		ID: req.Id,
	}

	dwo, err := s.customFieldLogic.Get(ctx, getParams)
	if err != nil {
		return nil, err
	}

	// 软删除：标记Definition和所有Options为删除状态
	dwo.Definition.State = customerpb.CustomField_DELETED
	for _, opt := range dwo.Options {
		opt.State = customerpb.CustomField_DELETED
	}

	_, err = s.customFieldLogic.Update(ctx, dwo)
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

// ==================== CustomerRelatedData Methods ====================

func (s *MetadataService) CreateCustomerRelatedData(ctx context.Context,
	req *customerpb.CreateCustomerRelatedDataRequest) (*customerpb.CustomerRelatedData, error) {
	data := req.GetCustomerRelatedData()
	if data == nil {
		return nil, errs.Newm(codes.InvalidArgument, "customer_related_data is required")
	}

	unconfirmedReminderBy := make([]byte, len(data.UnconfirmedReminderBy))
	for i, v := range data.UnconfirmedReminderBy {
		unconfirmedReminderBy[i] = byte(v)
	}

	logicData, err := s.customerRelatedDataLogic.Create(
		ctx, &customerrelateddata.CustomerRelatedData{
			ID:                             data.Id,
			CustomerID:                     data.CustomerId,
			PreferredBusinessID:            data.PreferredBusinessId,
			CompanyID:                      data.CompanyId,
			ClientColor:                    data.ClientColor,
			IsBlockMessage:                 data.IsBlockMessage,
			IsBlockOnlineBooking:           data.IsBlockOnlineBooking,
			LoginEmail:                     data.LoginEmail,
			ReferralSourceID:               data.ReferralSourceId,
			ReferralSourceDesc:             data.ReferralSourceDesc,
			SendAutoEmail:                  data.SendAutoEmail,
			SendAutoMessage:                data.SendAutoMessage,
			SendAppAutoMessage:             data.SendAppAutoMessage,
			UnconfirmedReminderBy:          unconfirmedReminderBy,
			PreferredGroomerID:             data.PreferredGroomerId,
			PreferredFrequencyDay:          data.PreferredFrequencyDay,
			PreferredFrequencyType:         data.PreferredFrequencyType,
			LastServiceTime:                data.LastServiceTime,
			Source:                         data.Source,
			ExternalID:                     data.ExternalId,
			CreateBy:                       data.CreateBy,
			UpdateBy:                       data.UpdateBy,
			ShareApptStatus:                data.ShareApptStatus,
			ShareRangeType:                 data.ShareRangeType,
			ShareRangeValue:                data.ShareRangeValue,
			PreferredDay:                   data.PreferredDay,
			PreferredTime:                  data.PreferredTime,
			AccountID:                      data.AccountId,
			CustomerCode:                   data.CustomerCode,
			IsUnsubscribed:                 data.IsUnsubscribed,
			ActionState:                    data.ActionState,
			CustomizeLifeCycleID:           data.CustomizeLifeCycleId,
			CustomizeActionStateID:         data.CustomizeActionStateId,
			PreferredTipEnable:             data.PreferredTipEnable,
			PreferredTipType:               data.PreferredTipType,
			PreferredTipAmount:             data.PreferredTipAmount,
			PreferredTipPercentage:         data.PreferredTipPercentage,
			DefaultPreferredFrequencyType:  data.DefaultPreferredFrequencyType,
			DefaultPreferredCalendarPeriod: data.DefaultPreferredCalendarPeriod,
			DefaultPreferredFrequencyValue: data.DefaultPreferredFrequencyValue,
		})

	if err != nil {
		return nil, err
	}

	return logicData.ToPB(), nil
}

func (s *MetadataService) GetCustomerRelatedData(ctx context.Context,
	req *customerpb.GetCustomerRelatedDataRequest) (*customerpb.CustomerRelatedData, error) {
	data, err := s.customerRelatedDataLogic.Get(ctx, req.CustomerId)
	if err != nil {
		return nil, err
	}

	return data.ToPB(), nil
}

func (s *MetadataService) ListCustomerRelatedData(ctx context.Context,
	req *customerpb.ListCustomerRelatedDataRequest) (*customerpb.ListCustomerRelatedDataResponse, error) {

	// 构建过滤器
	filter := &customerrelateddata.ListCustomerRelatedDataFilter{
		IDs:         req.Filter.Ids,
		CustomerIDs: req.Filter.CustomerIds,
		BusinessIDs: req.Filter.BusinessIds,
		CompanyIDs:  req.Filter.CompanyIds,
		States:      req.Filter.States,
	}

	pagination := &customerrelateddata.ListCustomerRelatedDataPagination{
		PageSize:        req.PageSize,
		ReturnTotalSize: req.ReturnTotalSize,
	}

	if req.PageToken != "" {
		pagination.Cursor = req.PageToken
	}

	orderBy := &customerrelateddata.ListCustomerRelatedDataOrderBy{
		Field:     customerpb.ListCustomerRelatedDataRequest_Sorting_ID,
		Direction: customerpb.ListCustomerRelatedDataRequest_Sorting_ASC,
	}
	if req.Sorting != nil {
		orderBy.Field = req.Sorting.Field
		orderBy.Direction = req.Sorting.Direction
	}

	response, err := s.customerRelatedDataLogic.List(
		ctx, &customerrelateddata.ListCustomerRelatedDataRequest{
			Filter:     filter,
			Pagination: pagination,
			OrderBy:    orderBy,
		})
	if err != nil {
		return nil, err
	}

	dataList := make([]*customerpb.CustomerRelatedData, 0, len(response.CustomerRelatedData))
	for _, data := range response.CustomerRelatedData {
		dataList = append(dataList, data.ToPB())
	}

	return &customerpb.ListCustomerRelatedDataResponse{
		CustomerRelatedData: dataList,
		NextPageToken:       response.NextToken,
		TotalSize:           response.TotalSize,
	}, nil
}

func (s *MetadataService) UpdateCustomerRelatedData(ctx context.Context,
	req *customerpb.UpdateCustomerRelatedDataRequest) (*customerpb.CustomerRelatedData, error) {
	ref := req.GetRef()
	if ref == nil {
		return nil, errs.Newm(codes.InvalidArgument, "ref is required")
	}

	customerID := req.Id
	relatedData, err := s.customerRelatedDataLogic.Get(ctx, customerID)
	if err != nil {
		return nil, err
	}

	if relatedData == nil {
		return nil, errs.Newm(codes.NotFound, "customer related data not found")
	}

	// 验证 state 字段不能为 DELETED
	if ref.State != nil && *ref.State == customerpb.CustomerRelatedData_DELETED {
		return nil, errs.Newm(codes.InvalidArgument, "state cannot be DELETED")
	}

	unconfirmedReminderBy := make([]byte, len(ref.UnconfirmedReminderBy))
	for i, v := range ref.UnconfirmedReminderBy {
		unconfirmedReminderBy[i] = byte(v)
	}

	updateReq := &customerrelateddata.UpdateCustomerRelatedDataRequest{
		ID:                             relatedData.ID,
		ClientColor:                    ref.GetClientColor(),
		IsBlockMessage:                 ref.GetIsBlockMessage(),
		IsBlockOnlineBooking:           ref.GetIsBlockOnlineBooking(),
		LoginEmail:                     ref.GetLoginEmail(),
		ReferralSourceID:               ref.GetReferralSourceId(),
		ReferralSourceDesc:             ref.GetReferralSourceDesc(),
		SendAutoEmail:                  ref.GetSendAutoEmail(),
		SendAutoMessage:                ref.GetSendAutoMessage(),
		SendAppAutoMessage:             ref.GetSendAppAutoMessage(),
		UnconfirmedReminderBy:          unconfirmedReminderBy,
		PreferredGroomerID:             ref.GetPreferredGroomerId(),
		PreferredFrequencyDay:          ref.GetPreferredFrequencyDay(),
		PreferredFrequencyType:         ref.GetPreferredFrequencyType(),
		PreferredDay:                   ref.GetPreferredDay(),
		PreferredTime:                  ref.GetPreferredTime(),
		IsUnsubscribed:                 ref.GetIsUnsubscribed(),
		CustomizeLifeCycleID:           ref.GetCustomizeLifeCycleId(),
		CustomizeActionStateID:         ref.GetCustomizeActionStateId(),
		PreferredBusinessID:            ref.GetPreferredBusinessId(),
		PreferredTipEnable:             ref.GetPreferredTipEnable(),
		PreferredTipType:               ref.GetPreferredTipType(),
		PreferredTipAmount:             ref.GetPreferredTipAmount(),
		PreferredTipPercentage:         ref.GetPreferredTipPercentage(),
		DefaultPreferredFrequencyType:  ref.GetDefaultPreferredFrequencyType(),
		DefaultPreferredCalendarPeriod: ref.GetDefaultPreferredCalendarPeriod(),
		DefaultPreferredFrequencyValue: ref.GetDefaultPreferredFrequencyValue(),
		// TODO: State 字段需要在 logic 层添加
	}

	if ref.Birthday != nil {
		birthday := ref.Birthday.AsTime()
		updateReq.Birthday = &birthday
	}

	data, err := s.customerRelatedDataLogic.Update(ctx, relatedData.ID, updateReq)
	if err != nil {
		return nil, err
	}

	return data.ToPB(), nil
}

func (s *MetadataService) DeleteCustomerRelatedData(ctx context.Context,
	req *customerpb.DeleteCustomerRelatedDataRequest) (*emptypb.Empty, error) {
	customerID := req.Id
	relatedData, err := s.customerRelatedDataLogic.Get(ctx, customerID)
	if err != nil {
		return nil, err
	}

	if relatedData == nil {
		return nil, errs.Newm(codes.NotFound, "customer related data not found")
	}

	err = s.customerRelatedDataLogic.Delete(ctx, relatedData.ID)
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *MetadataService) CreateCustomerAggregate(ctx context.Context,
	req *customerpb.CreateCustomerAggregateRequest) (*customerpb.CustomerAggregate, error) {
	if err := s.validateAggregateRequest(req); err != nil {
		return nil, err
	}

	aggregate := req.GetCustomerAggregate()

	logicRequest := &customerv2.AggregateRequest{}

	logicRequest.Customer = &customerv2.Customer{}
	logicRequest.Customer.FromProto(aggregate.GetCustomer())

	for _, addr := range aggregate.GetAddresses() {
		logicAddr := &address.Address{}
		logicAddr.FromProto(addr)
		logicRequest.Addresses = append(logicRequest.Addresses, logicAddr)
	}

	for _, cont := range aggregate.GetContacts() {
		logicContact := &contact.Contact{}
		logicContact.FromProto(cont)
		logicRequest.Contacts = append(logicRequest.Contacts, logicContact)
	}

	if relatedData := aggregate.GetRelatedData(); relatedData != nil {
		logicRequest.RelatedData = &customerrelateddata.CustomerRelatedData{}
		logicRequest.RelatedData.FromProto(relatedData)
	}

	result, err := s.customerLogic.CreateCustomerAggregate(ctx, logicRequest)
	if err != nil {
		return nil, err
	}

	response := &customerpb.CustomerAggregate{
		Customer: result.Customer.ToPB(),
	}

	for _, addr := range result.Addresses {
		response.Addresses = append(response.Addresses, addr.ToPB())
	}

	for _, cont := range result.Contacts {
		response.Contacts = append(response.Contacts, cont.ToPB())
	}

	if result.RelatedData != nil {
		response.RelatedData = result.RelatedData.ToPB()
	}

	return response, nil
}

func (s *MetadataService) validateAggregateRequest(req *customerpb.CreateCustomerAggregateRequest) error {
	aggregate := req.GetCustomerAggregate()
	if aggregate == nil {
		return errs.Newm(codes.InvalidArgument, "customer_aggregate is required")
	}

	customer := aggregate.GetCustomer()
	if customer == nil {
		return errs.Newm(codes.InvalidArgument, "customer is required")
	}

	if customer.Organization == nil {
		return errs.Newm(codes.InvalidArgument, "organization is required")
	}

	return nil
}

// convertToDefinitionWithOptions 将 protobuf 转换为 logic 层实体
func (s *MetadataService) convertToDefinitionWithOptions(
	customField *customerpb.CustomField,
) (*customfieldlogic.DefinitionWithOptions, error) {
	if customField == nil {
		return nil, errs.Newm(codes.InvalidArgument, "custom field is required")
	}
	if customField.Organization == nil {
		return nil, errs.Newm(codes.InvalidArgument, "organization is required")
	}

	// 构建定义
	def := &customfieldlogic.Definition{
		ID:               customField.Id,
		OrganizationType: customField.Organization.Type,
		OrganizationID:   customField.Organization.Id,
		AssociationType:  customField.AssociationType,
		FieldCode:        customField.Code,
		FieldLabel:       customField.Label,
		FieldType:        customField.Type,
		IsRequired:       customField.IsRequired,
		DisplayOrder:     int(customField.DisplayOrder),
		HelpText:         customField.HelpText,
		Source:           customField.Source,
	}

	// 设置默认值
	if customField.DefaultValue != nil {
		defaultValue, err := s.convertDefaultValue(customField.DefaultValue)
		if err != nil {
			return nil, err
		}
		def.DefaultValue = defaultValue
	}

	// 设置验证规则
	if customField.ValidationRules != nil {
		def.ValidationRules = customField.ValidationRules.AsMap()
	}

	// 转换选项
	var options []*customfieldlogic.Option
	for _, pbOpt := range customField.Options {
		opt, err := s.convertPBOption(pbOpt)
		if err != nil {
			return nil, err
		}
		options = append(options, opt)
	}

	return &customfieldlogic.DefinitionWithOptions{
		Definition: def,
		Options:    options,
	}, nil
}

// applyUpdates 应用更新到现有实体
func (s *MetadataService) applyUpdates(
	dwo *customfieldlogic.DefinitionWithOptions, ref *customerpb.UpdateCustomFieldRequest_UpdateRef,
) error {
	if ref == nil {
		return errs.Newm(codes.InvalidArgument, "ref is required")
	}

	// 更新字段
	if ref.Label != nil {
		dwo.Definition.FieldLabel = *ref.Label
	}

	if ref.IsRequired != nil {
		dwo.Definition.IsRequired = *ref.IsRequired
	}

	if ref.State != nil {
		// 验证枚举值，不允许UNSPECIFIED
		if *ref.State == customerpb.CustomField_STATE_UNSPECIFIED {
			return errs.Newm(codes.InvalidArgument, "state cannot be UNSPECIFIED")
		}
		dwo.Definition.State = *ref.State
	}

	// 更新默认值
	if ref.DefaultValue != nil {
		defaultValue, err := s.convertDefaultValue(ref.DefaultValue)
		if err != nil {
			return err
		}
		dwo.Definition.DefaultValue = defaultValue
	}

	// 更新选项
	if len(ref.Options) > 0 {
		// 验证字段类型是否支持选项
		needsOptions := dwo.Definition.FieldType == customerpb.CustomField_SELECT ||
			dwo.Definition.FieldType == customerpb.CustomField_MULTI_SELECT

		if !needsOptions {
			return errs.Newm(codes.InvalidArgument,
				"only SELECT and MULTI_SELECT fields can have options")
		}

		// 重新构建选项
		var opts []*customfieldlogic.Option
		for _, pbOpt := range ref.Options {
			opt, err := s.convertPBOption(pbOpt)
			if err != nil {
				return err
			}
			opts = append(opts, opt)
		}
		dwo.Options = opts
	}

	// 更新显示顺序
	if ref.DisplayOrder != nil {
		dwo.Definition.DisplayOrder = int(*ref.DisplayOrder)
	}

	// 更新帮助文本
	if ref.HelpText != nil {
		dwo.Definition.HelpText = *ref.HelpText
	}

	return nil
}

// convertDefaultValue 转换默认值
func (s *MetadataService) convertDefaultValue(defaultValue *customerpb.CustomField_Value) (any, error) {
	if defaultValue == nil {
		return nil, nil
	}
	switch v := defaultValue.Value.(type) {
	case *customerpb.CustomField_Value_String_:
		return v.String_, nil
	case *customerpb.CustomField_Value_Int64:
		return v.Int64, nil
	case *customerpb.CustomField_Value_DoubleValue:
		return v.DoubleValue, nil
	case *customerpb.CustomField_Value_Bool:
		return v.Bool, nil
	case *customerpb.CustomField_Value_Money:
		if v.Money == nil {
			return nil, errs.Newm(codes.InvalidArgument, "money value cannot be nil")
		}
		if v.Money.CurrencyCode == "" {
			return nil, errs.Newm(codes.InvalidArgument, "money currency_code is required")
		}

		return v.Money, nil
	case *customerpb.CustomField_Value_Relation_:
		if v.Relation == nil {
			return nil, errs.Newm(codes.InvalidArgument, "relation value cannot be nil")
		}
		if v.Relation.Entity == customerpb.CustomField_Value_Relation_ENTITY_UNSPECIFIED {
			return nil, errs.Newm(codes.InvalidArgument, "relation entity cannot be UNSPECIFIED")
		}
		if v.Relation.Id <= 0 {
			return nil, errs.Newm(codes.InvalidArgument, "relation id must be positive")
		}

		return v.Relation, nil
	case *customerpb.CustomField_Value_TimestampTime:
		if v.TimestampTime != nil {
			return v.TimestampTime.AsTime(), nil
		}

		return nil, nil
	case *customerpb.CustomField_Value_StringList_:
		if v.StringList != nil && len(v.StringList.Values) > 0 {
			return v.StringList.Values, nil
		}

		return nil, nil
	}

	return nil, nil
}

// convertPBOption 转换选项
func (s *MetadataService) convertPBOption(pbOpt *customerpb.CustomField_Option) (*customfieldlogic.Option, error) {
	if pbOpt == nil {
		return nil, nil
	}

	// 设置 option 状态，如果请求中有指定则使用，否则默认为 ACTIVE
	state := customerpb.CustomField_ACTIVE
	if pbOpt.State != customerpb.CustomField_STATE_UNSPECIFIED {
		state = pbOpt.State
	}

	opt := &customfieldlogic.Option{
		Label:     pbOpt.Label,
		SortOrder: int(pbOpt.SortOrder),
		State:     state,
	}

	if pbOpt.Value != nil {
		switch v := pbOpt.Value.Value.(type) {
		case *customerpb.CustomField_Value_String_:
			opt.ValueString = v.String_
		case *customerpb.CustomField_Value_Int64:
			opt.ValueInt64 = v.Int64
		case *customerpb.CustomField_Value_DoubleValue:
			opt.ValueDouble = v.DoubleValue
		case *customerpb.CustomField_Value_Bool:
			opt.ValueBool = v.Bool
		case *customerpb.CustomField_Value_Money:
			if v.Money == nil {
				return nil, errs.Newm(codes.InvalidArgument, "option money value cannot be nil")
			}
			if v.Money.CurrencyCode == "" {
				return nil, errs.Newm(codes.InvalidArgument, "option money currency_code is required")
			}
			opt.ValueMoney = v.Money
		case *customerpb.CustomField_Value_Relation_:
			if v.Relation == nil {
				return nil, errs.Newm(codes.InvalidArgument, "option relation value cannot be nil")
			}
			if v.Relation.Entity == customerpb.CustomField_Value_Relation_ENTITY_UNSPECIFIED {
				return nil, errs.Newm(codes.InvalidArgument, "option relation entity cannot be UNSPECIFIED")
			}
			if v.Relation.Id <= 0 {
				return nil, errs.Newm(codes.InvalidArgument, "option relation id must be positive")
			}
			opt.ValueRelation = v.Relation
		case *customerpb.CustomField_Value_StringList_:
			if v.StringList == nil {
				return nil, errs.Newm(codes.InvalidArgument, "option string_list value cannot be nil")
			}
			opt.ValueStringList = v.StringList.Values
		case *customerpb.CustomField_Value_TimestampTime:
			// ignore timestamp for options
		}
	}

	return opt, nil
}
