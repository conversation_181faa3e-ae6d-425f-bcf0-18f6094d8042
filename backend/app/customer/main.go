package main

import (
	"github.com/MoeGolibrary/moego/backend/app/customer/cdc"
	"github.com/MoeGolibrary/moego/backend/app/customer/service"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm/serializer"
	"github.com/MoeGolibrary/moego/backend/common/rpc/database/kafka"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/opentelemetry"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/recovery"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/validation"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
	customerpbv2 "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

func main() {
	s := framework.NewServer()

	// 这里需要注册grpc服务
	grpc.Register(s, &customerpb.CustomerService_ServiceDesc, service.NewCustomerService())
	grpc.Register(s, &customerpb.CustomerQueryService_ServiceDesc, service.NewCustomerQueryService())
	grpc.Register(s, &customerpbv2.MetadataService_ServiceDesc, service.NewMetadataService())
	grpc.Register(s, &customerpbv2.ActivityService_ServiceDesc, service.NewActivityService())

	kafka.RegisterKafkaHandlerService(s.Service("moego.crm.engagement.consumer"),
		service.NewEngagementEventConsumer().EngagementEventHandle)

	kafka.RegisterKafkaHandlerService(s.Service("moego.crm.message.consumer"),
		service.NewMessageEventConsumer().MessageEventHandle)

	kafka.RegisterKafkaHandlerService(s.Service("moego.erp.appointment.consumer"),
		service.NewAppointmentEventConsumer().AppointmentEventHandle)

	kafka.RegisterKafkaHandlerService(s.Service("moego.erp.online_booking.consumer"),
		service.NewOnlineBookingEventConsumer().OnlineBookingEventHandle)

	kafka.RegisterKafkaHandlerService(s.Service("moego.order.consumer"),
		service.NewOrderEventConsumer().OrderEventHandle)

	kafka.RegisterKafkaHandlerService(s.Service("moego.crm.customer.merge.consumer"),
		service.NewCustomerMergeMessageConsumer().CustomerMergeMessageHandle)

	// cdc
	kafka.RegisterKafkaHandlerService(s.Service("cdc.mysql.moe_customer.moe_customer_life_cycle.consumer"),
		cdc.NewLifeCycleConsumer().LifeCycleEventHandler)
	kafka.RegisterKafkaHandlerService(s.Service("cdc.mysql.moe_customer.moe_customer_action_state.consumer"),
		cdc.NewActionStateConsumer().ActionStateEventHandler)
	kafka.RegisterKafkaHandlerService(s.Service("cdc.mysql.moe_customer.moe_customer_history_log.consumer"),
		cdc.NewHistoryLogConsumer().HistoryLogEventHandler)
	kafka.RegisterKafkaHandlerService(s.Service("cdc.mysql.moe_customer.moe_customer_task.consumer"),
		cdc.NewTaskConsumer().TaskEventHandler)
	kafka.RegisterKafkaHandlerService(s.Service("cdc.mysql.moe_customer.moe_customer_note.consumer"),
		cdc.NewNoteConsumer().NoteEventHandler)
	kafka.RegisterKafkaHandlerService(s.Service("cdc.mysql.moe_customer.moe_customer_tag.consumer"),
		cdc.NewTagConsumer().TagEventHandler)
	kafka.RegisterKafkaHandlerService(s.Service("cdc.mysql.moe_customer.moe_customer_tag_binding.consumer"),
		cdc.NewTagBindingConsumer().TagBindingEventHandler)
	kafka.RegisterKafkaHandlerService(s.Service("cdc.mysql.moe_customer.moe_customer_source.consumer"),
		cdc.NewSourceConsumer().SourceEventHandler)
	kafka.RegisterKafkaHandlerService(s.Service("cdc.mysql.moe_customer.moe_business_customer.consumer"),
		cdc.NewCustomerConsumer().CustomerEventHandler)
	kafka.RegisterKafkaHandlerService(s.Service("cdc.mysql.moe_customer.moe_customer_contact.consumer"),
		cdc.NewContactConsumer().ContactEventHandler)
	kafka.RegisterKafkaHandlerService(s.Service("cdc.mysql.moe_customer.moe_customer_address.consumer"),
		cdc.NewAddressConsumer().AddressEventHandler)

	if err := s.Serve(); err != nil {
		log.Fatalf("failed to serve: %v", err)
	}
}
