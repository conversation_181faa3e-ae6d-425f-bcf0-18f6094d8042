-- customer table - 客户核心信息表
CREATE TABLE customer
(
    id                BIGSERIAL PRIMARY KEY,
    organization_type VARCHAR(50),
    organization_id   BIGINT,
    given_name        VARCHAR(100),
    family_name       VARCHAR(100),
    customer_type     VARCHAR(50) DEFAULT 'LEAD',
    state             VARCHAR(50) DEFAULT 'ACTIVE',

    custom_fields     JSONB       DEFAULT '{}',

    life_cycle_id     BIGINT,
    owner_staff_id    BIGINT,

    deleted_time      TIMESTAMP,
    created_time      TIMESTAMP   DEFAULT CURRENT_TIMESTAMP,
    updated_time      TIMESTAMP   DEFAULT CURRENT_TIMESTAMP,
);

-- 索引
CREATE INDEX idx_customer_owner_staff_id ON customer (owner_staff_id);
CREATE INDEX idx_customer_custom_fields ON customer USING GIN (custom_fields);
CREATE INDEX idx_customer_organization ON customer (organization_type, organization_id);

-- 注释
COMMENT ON TABLE customer IS '客户核心信息表';
COMMENT ON COLUMN customer.organization_type IS '组织数据类型';
COMMENT ON COLUMN customer.organization_id IS '组织数据ID';
COMMENT ON COLUMN customer.given_name IS '名';
COMMENT ON COLUMN customer.family_name IS '姓';
COMMENT ON COLUMN customer.customer_type IS '客户类型：LEAD线索, CUSTOMER客户';
COMMENT ON COLUMN customer.state IS '状态：ACTIVE活跃, INACTIVE非活跃, DELETED已删除';
COMMENT ON COLUMN customer.custom_fields IS '自定义字段JSON存储';
COMMENT ON COLUMN customer.life_cycle_id IS '生命周期ID';
COMMENT ON COLUMN customer.owner_staff_id IS '负责人员工ID';
COMMENT ON COLUMN customer.deleted_time IS '删除时间';
COMMENT ON COLUMN customer.created_time IS '创建时间';
COMMENT ON COLUMN customer.updated_time IS '更新时间';

-- 触发器
CREATE TRIGGER update_customer_updated_time
    BEFORE UPDATE
    ON customer
    FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();


-- contact table - 联系人核心信息表
CREATE TABLE contact
(
    id                BIGSERIAL PRIMARY KEY,
    customer_id       BIGINT,
    given_name        VARCHAR(255),
    family_name       VARCHAR(255),
    email             VARCHAR(255),
    phone             VARCHAR(255),
    state             VARCHAR(50) DEFAULT 'ACTIVE',

    custom_fields     JSONB       DEFAULT '{}',

    deleted_time      TIMESTAMP,
    created_time      TIMESTAMP   DEFAULT CURRENT_TIMESTAMP,
    updated_time      TIMESTAMP   DEFAULT CURRENT_TIMESTAMP,
);

-- 索引
CREATE INDEX idx_contact_customer_id ON contact (customer_id);
CREATE INDEX idx_contact_email ON contact (email);
CREATE INDEX idx_contact_phone ON contact (phone);
CREATE INDEX idx_contact_custom_fields ON contact USING GIN (custom_fields);

-- 注释
COMMENT ON TABLE contact IS '联系人核心信息表';
COMMENT ON COLUMN contact.customer_id IS '客户ID';
COMMENT ON COLUMN contact.given_name IS '名';
COMMENT ON COLUMN contact.family_name IS '姓';
COMMENT ON COLUMN contact.email IS '邮箱地址';
COMMENT ON COLUMN contact.phone IS '电话号码';
COMMENT ON COLUMN contact.state IS '状态：ACTIVE活跃, INACTIVE非活跃, DELETED已删除';
COMMENT ON COLUMN contact.custom_fields IS '自定义字段JSON存储';
COMMENT ON COLUMN contact.deleted_time IS '删除时间';
COMMENT ON COLUMN contact.created_time IS '创建时间';
COMMENT ON COLUMN contact.updated_time IS '更新时间';

-- 触发器
CREATE TRIGGER update_contact_updated_time
    BEFORE UPDATE
    ON contact
    FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TABLE public.contact_tag
(
    id                BIGSERIAL PRIMARY KEY,
    organization_type varchar(255) NOT NULL,
    organization_id   BIGINT       NOT NULL,

    name              VARCHAR(255) NOT NULL,
    color             VARCHAR(255) NOT NULL,
    sort              INTEGER      NOT NULL DEFAULT 0,
    description       TEXT,

    created_time      TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time      TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_time      TIMESTAMP
);
CREATE INDEX idx_organization ON contact_tag (organization_id, organization_type);
CREATE TRIGGER update_contact_tag_updated_time
    BEFORE UPDATE
    ON contact_tag
    FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TABLE public.contact_tag_rel
(
    id                BIGSERIAL PRIMARY KEY,
    organization_type varchar(255) NOT NULL,
    organization_id   BIGINT       NOT NULL,
    contact_id        BIGINT       NOT NULL,
    tag_id            BIGINT       NOT NULL,

    created_time      TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time      TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_time      TIMESTAMP
);
CREATE INDEX idx_contact_tag_contact_id ON contact_tag_rel (contact_id);
CREATE INDEX idx_contact_tag_tag_id ON contact_tag_rel (tag_id);
CREATE UNIQUE INDEX uniq_contact_tag_id_contact_id ON contact_tag_rel (contact_id, tag_id) WHERE deleted_time IS NULL;
CREATE TRIGGER update_contact_tag_rel_updated_time
    BEFORE UPDATE
    ON contact_tag_rel
    FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();


-- address table - 地址表
CREATE TABLE address
(
    id                  BIGSERIAL PRIMARY KEY,
    customer_id         BIGINT,
    revision            VARCHAR(20),
    region_code         VARCHAR(10),
    language_code       VARCHAR(10),
    organization        VARCHAR(255),
    postal_code         VARCHAR(20),
    sorting_code        VARCHAR(20),
    administrative_area VARCHAR(100),
    locality            VARCHAR(100),
    sublocality         VARCHAR(100),
    address_lines       TEXT[],         -- 多行地址
    recipients          TEXT[],         -- 收件人
    latitude            DOUBLE PRECISION, -- 纬度
    longitude           DOUBLE PRECISION, -- 经度
    state               VARCHAR(50) DEFAULT 'ACTIVE',
    custom_fields       JSONB DEFAULT '{}',
    deleted_time        TIMESTAMP,
    created_time        TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time        TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
);

-- 索引
CREATE INDEX idx_address_customer_id ON address (customer_id);
CREATE INDEX idx_address_custom_fields ON address USING GIN (custom_fields);

-- 注释
COMMENT ON TABLE address IS '地址表';
COMMENT ON COLUMN address.customer_id IS '客户ID';
COMMENT ON COLUMN address.revision IS '版本';
COMMENT ON COLUMN address.region_code IS '地区代码';
COMMENT ON COLUMN address.language_code IS '语言代码';
COMMENT ON COLUMN address.organization IS '组织';
COMMENT ON COLUMN address.postal_code IS '邮政编码';
COMMENT ON COLUMN address.sorting_code IS '排序代码';
COMMENT ON COLUMN address.administrative_area IS '行政区域';
COMMENT ON COLUMN address.locality IS '地点';
COMMENT ON COLUMN address.sublocality IS '子地点';
COMMENT ON COLUMN address.address_lines IS '地址行';
COMMENT ON COLUMN address.recipients IS '收件人';
COMMENT ON COLUMN address.latitude IS '纬度';
COMMENT ON COLUMN address.longitude IS '经度';
COMMENT ON COLUMN address.state IS '状态：ACTIVE活跃, INACTIVE非活跃, DELETED已删除';
COMMENT ON COLUMN address.custom_fields IS '自定义字段JSON存储';
COMMENT ON COLUMN address.deleted_time IS '删除时间';
COMMENT ON COLUMN address.created_time IS '创建时间';
COMMENT ON COLUMN address.updated_time IS '更新时间';

-- 触发器
CREATE TRIGGER update_address_updated_time
    BEFORE UPDATE
    ON address
    FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();


-- custom_field_definition table - 自定义字段定义表
drop table if exists custom_field_definition;
-- auto-generated definition
create table custom_field_definition
(
    id                bigserial
        primary key,
    organization_type varchar(50)  not null,
    organization_id   bigint       not null,
    association_type  varchar(50)  not null,
    field_code        varchar(100) not null,
    field_label       varchar(200) not null,
    field_type        varchar(50)  not null,
    is_required       boolean     default false,
    default_value     jsonb,
    display_order     integer     default 0,
    help_text         text,
    deleted_time      timestamp,
    state             varchar(50) default 'ACTIVE'::character varying,
    created_time      timestamp   default CURRENT_TIMESTAMP,
    updated_time      timestamp   default CURRENT_TIMESTAMP,
    source            text        default 'SYSTEM'::text,
    constraint custom_field_definition_organization_type_organization_id_a_key
        unique (organization_type, organization_id, association_type, field_code)
);

create unique index idx_custom_field_organization_code
    on custom_field_definition (organization_type, organization_id, field_code);

create index idx_custom_field_organization
    on custom_field_definition (organization_type, organization_id);

create index idx_custom_field_organization_order
    on custom_field_definition (organization_type, organization_id, display_order);





-- custom_field_option table - 自定义字段选项表
create table custom_field_option
(
    id           bigserial
        primary key,
    field_id     bigint       not null,
    value        jsonb,
    label        varchar(200) not null,
    sort_order   integer     default 0,
    state        varchar(50) default 'ACTIVE'::character varying,
    created_time timestamp   default CURRENT_TIMESTAMP,
    updated_time timestamp   default CURRENT_TIMESTAMP,
    deleted_time timestamp
);

comment on table custom_field_option is '自定义字段选项表';

comment on column custom_field_option.field_id is '自定义字段定义ID';

comment on column custom_field_option.value is '值';

comment on column custom_field_option.label is '选项标签';

comment on column custom_field_option.sort_order is '排序顺序';

comment on column custom_field_option.state is '状态：ACTIVE活跃, DELETED已删除';

comment on column custom_field_option.created_time is '创建时间';

comment on column custom_field_option.updated_time is '更新时间';

comment on column custom_field_option.deleted_time is '删除时间';

alter table custom_field_option
    owner to moego_developer_240310_eff7a0dc;

create index idx_option_field_id
    on custom_field_option (field_id);

create index idx_option_state
    on custom_field_option (state);

create index idx_option_value
    on custom_field_option using gin (value);

