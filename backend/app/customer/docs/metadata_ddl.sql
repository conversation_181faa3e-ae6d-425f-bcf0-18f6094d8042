-- =========================================================
-- Consolidated DDL (Grouped by Table)
-- Source DB: moego_customer
-- Generated At: 2024-12-19
-- NOTE: 若首次在目标库使用 trigram / GIN 索引, 需确保:
--   CREATE EXTENSION IF NOT EXISTS pg_trgm;
-- =========================================================

-- =========================================================
-- Table: public.action_state
-- =========================================================
CREATE TABLE public.action_state (
    id bigint NOT NULL,
    company_id bigint NOT NULL,
    business_id bigint NOT NULL,
    created_by bigint NOT NULL,
    updated_by bigint NOT NULL,
    deleted_by bigint,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp without time zone,
    name character varying(255) NOT NULL,
    sort integer DEFAULT 0 NOT NULL,
    color character varying(255) NOT NULL,
    CONSTRAINT action_state_pkey PRIMARY KEY (id)
);
-- Indexes
CREATE INDEX idx_action_state_company_id ON public.action_state (company_id);

-- =========================================================
-- Table: public.activity_log
-- =========================================================
CREATE TABLE public.activity_log (
    id bigint NOT NULL,
    company_id bigint NOT NULL,
    business_id bigint NOT NULL,
    customer_id bigint NOT NULL,
    customer_name character varying(255) NOT NULL,
    customer_phone_number character varying(255) NOT NULL,
    type character varying(255) NOT NULL,
    action text NOT NULL,
    source text NOT NULL,
    create_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    update_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT activity_log_pkey PRIMARY KEY (id)
);
-- Indexes
CREATE INDEX idx_action_log_company_id ON public.activity_log (company_id);
CREATE INDEX idx_action_log_customer_id ON public.activity_log (customer_id);

-- =========================================================
-- Table: public.activity_rel
-- =========================================================
CREATE TABLE public.activity_rel (
    id bigint NOT NULL,
    company_id bigint NOT NULL,
    business_id bigint NOT NULL,
    customer_id bigint NOT NULL,
    activity_id bigint NOT NULL,
    activity_type character varying(255) NOT NULL,
    create_by bigint NOT NULL,
    update_by bigint NOT NULL,
    delete_by bigint,
    create_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    update_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    delete_time timestamp without time zone,
    CONSTRAINT activity_rel_pkey PRIMARY KEY (id)
);
-- Indexes
CREATE INDEX idx_ar_activity_id_activity_type ON public.activity_rel (activity_id, activity_type);
CREATE INDEX idx_ar_customer_id_activity_type ON public.activity_rel (customer_id, activity_type);
CREATE UNIQUE INDEX uniq_ar_customer_activity_rel ON public.activity_rel (customer_id, activity_id, activity_type) WHERE (delete_time IS NULL);

-- =========================================================
-- Table: public.customer
-- =========================================================
CREATE TABLE public.customer (
    id bigint NOT NULL,
    organization_type character varying(50),
    organization_id bigint,
    given_name character varying(100),
    family_name character varying(100),
    customer_type character varying(50) DEFAULT 'LEAD'::character varying,
    state character varying(50) DEFAULT 'ACTIVE'::character varying,
    custom_fields jsonb DEFAULT '{}'::jsonb,
    life_cycle_id bigint,
    owner_staff_id bigint,
    deleted_time timestamp without time zone,
    created_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    action_state_id bigint DEFAULT 0 NOT NULL,
    avatar_path character varying(255) DEFAULT '' NOT NULL,
    customer_code character varying(255) DEFAULT '' NOT NULL,
    referral_source_id bigint DEFAULT 0 NOT NULL,
    convert_to_customer_id bigint DEFAULT 0 NOT NULL,
    CONSTRAINT customer_pkey PRIMARY KEY (id)
);
-- Indexes
CREATE INDEX idx_customer_custom_fields ON public.customer USING gin (custom_fields);
CREATE INDEX idx_customer_organization ON public.customer (organization_type, organization_id);
CREATE INDEX idx_customer_owner_staff_id ON public.customer (owner_staff_id);
CREATE INDEX idx_family_name_trgm ON public.customer USING gin (family_name public.gin_trgm_ops); -- requires pg_trgm
CREATE INDEX idx_given_name_trgm ON public.customer USING gin (given_name public.gin_trgm_ops); -- requires pg_trgm
-- Comments
COMMENT ON TABLE public.customer IS '客户核心信息表';
COMMENT ON COLUMN public.customer.organization_type IS '组织数据类型';
COMMENT ON COLUMN public.customer.organization_id IS '组织数据ID';
COMMENT ON COLUMN public.customer.given_name IS '名';
COMMENT ON COLUMN public.customer.family_name IS '姓';
COMMENT ON COLUMN public.customer.customer_type IS '客户类型：LEAD线索, CUSTOMER客户';
COMMENT ON COLUMN public.customer.state IS '状态：ACTIVE活跃, INACTIVE非活跃, DELETED已删除';
COMMENT ON COLUMN public.customer.custom_fields IS '自定义字段JSON存储';
COMMENT ON COLUMN public.customer.life_cycle_id IS '生命周期ID';
COMMENT ON COLUMN public.customer.owner_staff_id IS '负责人员工ID';
COMMENT ON COLUMN public.customer.deleted_time IS '删除时间';
COMMENT ON COLUMN public.customer.created_time IS '创建时间';
COMMENT ON COLUMN public.customer.updated_time IS '更新时间';
COMMENT ON COLUMN public.customer.action_state_id IS '行动状态ID';
COMMENT ON COLUMN public.customer.avatar_path IS '头像路径';
COMMENT ON COLUMN public.customer.customer_code IS '客户编码';
COMMENT ON COLUMN public.customer.referral_source_id IS '推荐来源ID';
COMMENT ON COLUMN public.customer.convert_to_customer_id IS '转换为客户的ID';

-- =========================================================
-- Table: public.contact
-- =========================================================
CREATE TABLE public.contact (
    id bigint NOT NULL,
    customer_id bigint,
    given_name character varying(255),
    family_name character varying(255),
    email character varying(255),
    phone character varying(255),
    state character varying(50) DEFAULT 'ACTIVE'::character varying,
    custom_fields jsonb DEFAULT '{}'::jsonb,
    deleted_time timestamp without time zone,
    created_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    is_self boolean DEFAULT false NOT NULL,
    title character varying(255) DEFAULT '' NOT NULL,
    note character varying(255) DEFAULT '' NOT NULL,
    organization_id bigint DEFAULT 0 NOT NULL,
    organization_type character varying(255) DEFAULT '' NOT NULL,
    CONSTRAINT chk_contact_state CHECK ((state)::text = ANY ((ARRAY['ACTIVE','INACTIVE','DELETED'])::text[])),
    CONSTRAINT contact_pkey PRIMARY KEY (id)
);
-- Indexes
CREATE INDEX contact_organization_index ON public.contact (organization_type, organization_id);
CREATE INDEX idx_contact_custom_fields ON public.contact USING gin (custom_fields);
CREATE INDEX idx_contact_customer_id ON public.contact (customer_id);
CREATE INDEX idx_contact_email ON public.contact (email);
CREATE INDEX idx_contact_phone ON public.contact (phone);
CREATE INDEX id_email_trgm ON public.contact USING gin (email public.gin_trgm_ops); -- requires pg_trgm
-- Comments
COMMENT ON TABLE public.contact IS '联系人核心信息表';
COMMENT ON COLUMN public.contact.customer_id IS '客户ID';
COMMENT ON COLUMN public.contact.given_name IS '名';
COMMENT ON COLUMN public.contact.family_name IS '姓';
COMMENT ON COLUMN public.contact.email IS '邮箱地址';
COMMENT ON COLUMN public.contact.phone IS '电话号码';
COMMENT ON COLUMN public.contact.state IS '状态：ACTIVE活跃, INACTIVE非活跃, DELETED已删除';
COMMENT ON COLUMN public.contact.custom_fields IS '自定义字段JSON存储';
COMMENT ON COLUMN public.contact.deleted_time IS '删除时间';
COMMENT ON COLUMN public.contact.created_time IS '创建时间';
COMMENT ON COLUMN public.contact.updated_time IS '更新时间';
COMMENT ON COLUMN public.contact.is_self IS '是否是本人的联系方式';
COMMENT ON COLUMN public.contact.title IS '职位头衔';
COMMENT ON COLUMN public.contact.note IS '备注';
COMMENT ON COLUMN public.contact.organization_id IS '组织ID';
COMMENT ON COLUMN public.contact.organization_type IS '组织类型';

-- =========================================================
-- Table: public.contact_tag
-- =========================================================
CREATE TABLE public.contact_tag (
    id bigint NOT NULL,
    organization_type character varying(255) DEFAULT '' NOT NULL,
    organization_id bigint DEFAULT 0 NOT NULL,
    name character varying(255) DEFAULT '' NOT NULL,
    color character varying(7) DEFAULT '#000000' NOT NULL,
    type character varying(50) DEFAULT 'CUSTOM' NOT NULL,
    sort_order integer DEFAULT 0 NOT NULL,
    description text,
    state character varying(50) DEFAULT 'ACTIVE'::character varying,
    created_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_time timestamp without time zone,
    CONSTRAINT chk_color_format CHECK ((color)::text ~ '^#[0-9A-Fa-f]{6}$'),
    CONSTRAINT chk_contact_state CHECK ((state)::text = ANY ((ARRAY['ACTIVE','INACTIVE','DELETED'])::text[])),
    CONSTRAINT contact_tag_pkey PRIMARY KEY (id)
);
-- Indexes
CREATE INDEX idx_organization ON public.contact_tag (organization_id, organization_type);
-- Comments
COMMENT ON COLUMN public.contact_tag.state IS '状态：ACTIVE活跃, INACTIVE非活跃, DELETED已删除';

-- =========================================================
-- Table: public.contact_tag_rel
-- =========================================================
CREATE TABLE public.contact_tag_rel (
    id bigint NOT NULL,
    organization_type character varying(255) NOT NULL,
    organization_id bigint NOT NULL,
    contact_id bigint NOT NULL,
    tag_id bigint NOT NULL,
    created_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_time timestamp without time zone,
    CONSTRAINT contact_tag_rel_pkey PRIMARY KEY (id)
);
-- Indexes
CREATE INDEX idx_contact_tag_rel_org_contact ON public.contact_tag_rel (organization_id, organization_type, contact_id);
CREATE INDEX idx_contact_tag_rel_org_tag ON public.contact_tag_rel (organization_id, organization_type, tag_id);
CREATE UNIQUE INDEX uniq_contact_tag_id_contact_id ON public.contact_tag_rel (contact_id, tag_id) WHERE (deleted_time IS NULL);

-- =========================================================
-- Table: public.address
-- =========================================================
CREATE TABLE public.address (
    id bigint NOT NULL,
    customer_id bigint,
    revision bigint,
    region_code character varying(10),
    language_code character varying(10),
    organization character varying(255),
    postal_code character varying(20),
    sorting_code character varying(20),
    administrative_area character varying(100),
    locality character varying(100),
    sublocality character varying(100),
    address_lines text[],
    recipients text[],
    latitude double precision,
    longitude double precision,
    state character varying(50) DEFAULT 'ACTIVE'::character varying,
    custom_fields jsonb DEFAULT '{}'::jsonb,
    deleted_time timestamp without time zone,
    created_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    type character varying(255) DEFAULT 'ADDITIONAL'::character varying NOT NULL,
    organization_type character varying(255) DEFAULT 'TYPE_UNSPECIFIED'::character varying NOT NULL,
    organization_id bigint DEFAULT 0 NOT NULL,
    CONSTRAINT chk_address_state CHECK ((state)::text = ANY ((ARRAY['ACTIVE','INACTIVE','DELETED'])::text[])),
    CONSTRAINT chk_address_type CHECK ((type)::text = ANY ((ARRAY['PRIMARY','ADDITIONAL'])::text[])),
    CONSTRAINT address_pkey PRIMARY KEY (id)
);
-- Indexes
CREATE INDEX idx_address_custom_fields ON public.address USING gin (custom_fields);
CREATE INDEX idx_address_customer_id ON public.address (customer_id);
-- Comments
COMMENT ON TABLE public.address IS '地址表';
COMMENT ON COLUMN public.address.customer_id IS '客户ID';
COMMENT ON COLUMN public.address.revision IS '版本';
COMMENT ON COLUMN public.address.region_code IS '地区代码';
COMMENT ON COLUMN public.address.language_code IS '语言代码';
COMMENT ON COLUMN public.address.organization IS '组织';
COMMENT ON COLUMN public.address.postal_code IS '邮政编码';
COMMENT ON COLUMN public.address.sorting_code IS '排序代码';
COMMENT ON COLUMN public.address.administrative_area IS '行政区域';
COMMENT ON COLUMN public.address.locality IS '地点';
COMMENT ON COLUMN public.address.sublocality IS '子地点';
COMMENT ON COLUMN public.address.address_lines IS '地址行';
COMMENT ON COLUMN public.address.recipients IS '收件人';
COMMENT ON COLUMN public.address.latitude IS '纬度';
COMMENT ON COLUMN public.address.longitude IS '经度';
COMMENT ON COLUMN public.address.state IS '状态：ACTIVE活跃, INACTIVE非活跃, DELETED已删除';
COMMENT ON COLUMN public.address.custom_fields IS '自定义字段JSON存储';
COMMENT ON COLUMN public.address.deleted_time IS '删除时间';
COMMENT ON COLUMN public.address.created_time IS '创建时间';
COMMENT ON COLUMN public.address.updated_time IS '更新时间';
COMMENT ON COLUMN public.address.type IS '地址类型：PRIMARY主要, ADDITIONAL附加';
COMMENT ON COLUMN public.address.organization_type IS '组织类型';
COMMENT ON COLUMN public.address.organization_id IS '组织ID';

-- =========================================================
-- Table: public.custom_field_definition
-- =========================================================
CREATE TABLE public.custom_field_definition (
    id bigint NOT NULL,
    organization_type character varying(50) NOT NULL,
    organization_id bigint NOT NULL,
    association_type character varying(50) NOT NULL,
    field_code character varying(100) NOT NULL,
    field_label character varying(200) NOT NULL,
    field_type character varying(50) NOT NULL,
    is_required boolean DEFAULT false,
    default_value jsonb,
    display_order integer DEFAULT 0,
    help_text text,
    deleted_time timestamp without time zone,
    state character varying(50) DEFAULT 'ACTIVE'::character varying,
    created_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    source text DEFAULT 'SYSTEM',
    CONSTRAINT custom_field_definition_pkey PRIMARY KEY (id),
    CONSTRAINT custom_field_definition_organization_type_organization_id_a_key UNIQUE (organization_type, organization_id, association_type, field_code)
);
-- Indexes
CREATE INDEX idx_custom_field_entity ON public.custom_field_definition (association_type);
CREATE INDEX idx_custom_field_organization ON public.custom_field_definition (organization_type, organization_id);
CREATE UNIQUE INDEX idx_custom_field_organization_code ON public.custom_field_definition (organization_type, organization_id, field_code);
CREATE INDEX idx_custom_field_organization_order ON public.custom_field_definition (organization_type, organization_id, display_order);

-- =========================================================
-- Table: public.custom_field_option
-- =========================================================
CREATE TABLE public.custom_field_option (
    id bigint NOT NULL,
    field_id bigint NOT NULL,
    value jsonb,
    label character varying(200) NOT NULL,
    sort_order integer DEFAULT 0,
    state character varying(50) DEFAULT 'ACTIVE'::character varying,
    created_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    deleted_time timestamp without time zone,
    CONSTRAINT custom_field_option_pkey PRIMARY KEY (id)
);
-- Indexes
CREATE INDEX idx_option_field_id ON public.custom_field_option (field_id);
CREATE INDEX idx_option_state ON public.custom_field_option (state);
CREATE INDEX idx_option_value ON public.custom_field_option USING gin (value);
-- Comments
COMMENT ON TABLE public.custom_field_option IS '自定义字段选项表';
COMMENT ON COLUMN public.custom_field_option.field_id IS '自定义字段定义ID';
COMMENT ON COLUMN public.custom_field_option.value IS '值';
COMMENT ON COLUMN public.custom_field_option.label IS '选项标签';
COMMENT ON COLUMN public.custom_field_option.sort_order IS '排序顺序';
COMMENT ON COLUMN public.custom_field_option.state IS '状态：ACTIVE活跃, DELETED已删除';
COMMENT ON COLUMN public.custom_field_option.created_time IS '创建时间';
COMMENT ON COLUMN public.custom_field_option.updated_time IS '更新时间';
COMMENT ON COLUMN public.custom_field_option.deleted_time IS '删除时间';

-- =========================================================
-- Table: public.customer_related_data
-- =========================================================
CREATE TABLE public.customer_related_data (
    id bigint NOT NULL,
    customer_id bigint NOT NULL,
    business_id integer DEFAULT 0 NOT NULL,
    company_id bigint DEFAULT 0 NOT NULL,
    client_color character varying(50) DEFAULT '#000000' NOT NULL,
    is_block_message smallint DEFAULT 0 NOT NULL,
    is_block_online_booking smallint DEFAULT 0 NOT NULL,
    login_email character varying(50) DEFAULT '' NOT NULL,
    referral_source_id integer DEFAULT 0 NOT NULL,
    referral_source_desc character varying(255) DEFAULT '' NOT NULL,
    send_auto_email smallint DEFAULT 0 NOT NULL,
    send_auto_message smallint DEFAULT 1 NOT NULL,
    send_app_auto_message smallint DEFAULT 1 NOT NULL,
    unconfirmed_reminder_by smallint DEFAULT 1 NOT NULL,
    preferred_groomer_id integer DEFAULT 0 NOT NULL,
    preferred_frequency_day integer DEFAULT 28 NOT NULL,
    preferred_frequency_type smallint DEFAULT 1 NOT NULL,
    last_service_time character varying(20) DEFAULT '' NOT NULL,
    source character varying(20) DEFAULT '' NOT NULL,
    external_id character varying(20) DEFAULT '' NOT NULL,
    create_by integer DEFAULT 0,
    update_by integer DEFAULT 0,
    is_recurring smallint,
    share_appt_status smallint DEFAULT 0 NOT NULL,
    share_range_type smallint DEFAULT 0 NOT NULL,
    share_range_value integer DEFAULT 0 NOT NULL,
    share_appt_json text,
    preferred_day character varying(50) DEFAULT '[0,1,2,3,4,5,6]',
    preferred_time character varying(50) DEFAULT '[0,1435]',
    account_id bigint DEFAULT 0 NOT NULL,
    customer_code character(16) NOT NULL,
    is_unsubscribed boolean DEFAULT false NOT NULL,
    birthday timestamp without time zone,
    action_state character varying(255) DEFAULT 'ACTION_STATE_UNSPECIFIED',
    customize_life_cycle_id bigint DEFAULT 0 NOT NULL,
    customize_action_state_id bigint DEFAULT 0 NOT NULL,
    deleted_time timestamp without time zone,
    created_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    state character varying(50) DEFAULT 'ACTIVE'::character varying,
    CONSTRAINT customer_related_data_pkey PRIMARY KEY (id),
    CONSTRAINT uk_customer_code UNIQUE (customer_code)
);
-- Indexes
CREATE INDEX idx_customer_related_data_account_id ON public.customer_related_data (account_id);
CREATE INDEX idx_customer_related_data_business_id ON public.customer_related_data (business_id);
CREATE INDEX idx_customer_related_data_company_id ON public.customer_related_data (company_id);
CREATE INDEX idx_customer_related_data_customer_id ON public.customer_related_data (customer_id);
CREATE INDEX idx_login_email_trgm ON public.customer_related_data USING gin (login_email public.gin_trgm_ops); -- requires pg_trgm
-- Comments
COMMENT ON TABLE public.customer_related_data IS '客户相关数据表，存储客户的扩展业务信息';
COMMENT ON COLUMN public.customer_related_data.customer_id IS '关联的客户ID';
COMMENT ON COLUMN public.customer_related_data.business_id IS '商家id';
COMMENT ON COLUMN public.customer_related_data.client_color IS '客户颜色';
COMMENT ON COLUMN public.customer_related_data.is_block_message IS '0-正常 1-is_block';
COMMENT ON COLUMN public.customer_related_data.is_block_online_booking IS '0-正常 1-is_block for grooming calender';
COMMENT ON COLUMN public.customer_related_data.login_email IS 'C端app登录email 只做显示用';
COMMENT ON COLUMN public.customer_related_data.referral_source_id IS 'referral_source';
COMMENT ON COLUMN public.customer_related_data.send_auto_email IS '顾客是否接受auto message自动发送的邮件 0 不接受 1接受';
COMMENT ON COLUMN public.customer_related_data.send_auto_message IS '顾客是否接受auto message自动发送的短信 0 不接受 1接受';
COMMENT ON COLUMN public.customer_related_data.send_app_auto_message IS '顾客是否接受app auto message自动发送的短信 0 不接受 1接受';
COMMENT ON COLUMN public.customer_related_data.unconfirmed_reminder_by IS '1text 2 email 3phone call';
COMMENT ON COLUMN public.customer_related_data.preferred_groomer_id IS 'businessAccountId 创建预约的时候，首选staff';
COMMENT ON COLUMN public.customer_related_data.preferred_frequency_day IS '提示时间 单位 天';
COMMENT ON COLUMN public.customer_related_data.preferred_frequency_type IS '0-by days , 1- by weeks';
COMMENT ON COLUMN public.customer_related_data.last_service_time IS '最后服务时间';
COMMENT ON COLUMN public.customer_related_data.source IS '来源';
COMMENT ON COLUMN public.customer_related_data.external_id IS '外部id 导数据时使用';
COMMENT ON COLUMN public.customer_related_data.create_by IS 'Create client staff id';
COMMENT ON COLUMN public.customer_related_data.update_by IS 'Update client staff id';
COMMENT ON COLUMN public.customer_related_data.is_recurring IS '客户是否为recurring, 1 true, 2 false';
COMMENT ON COLUMN public.customer_related_data.share_appt_status IS 'share appt 状态 0 all 1 unconfirm 2confirm 4 finished';
COMMENT ON COLUMN public.customer_related_data.share_range_type IS '0 all 1 in x days 2 next x appointment 3 manually apptids';
COMMENT ON COLUMN public.customer_related_data.share_range_value IS '不同type时的value';
COMMENT ON COLUMN public.customer_related_data.share_appt_json IS '当share_range_type为3时，记录的所有apptIds的json字符串';
COMMENT ON COLUMN public.customer_related_data.preferred_day IS 'customer preferred apt day, int array, range: 0-6';
COMMENT ON COLUMN public.customer_related_data.preferred_time IS 'customer preferred time in minutes array';
COMMENT ON COLUMN public.customer_related_data.account_id IS 'account id';
COMMENT ON COLUMN public.customer_related_data.customer_code IS 'customer code for frontend';
COMMENT ON COLUMN public.customer_related_data.is_unsubscribed IS 'whether the customer is unsubscribed';
COMMENT ON COLUMN public.customer_related_data.birthday IS '顾客生日';
COMMENT ON COLUMN public.customer_related_data.customize_life_cycle_id IS '关联的自定义life cycle id';
COMMENT ON COLUMN public.customer_related_data.customize_action_state_id IS '关联的自定义action state id';

-- =========================================================
-- Table: public.life_cycle
-- =========================================================
CREATE TABLE public.life_cycle (
    id bigint NOT NULL,
    company_id bigint NOT NULL,
    business_id bigint NOT NULL,
    created_by bigint NOT NULL,
    updated_by bigint NOT NULL,
    deleted_by bigint,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp without time zone,
    name character varying(255) NOT NULL,
    sort integer DEFAULT 0 NOT NULL,
    is_default integer DEFAULT 0 NOT NULL,
    CONSTRAINT life_cycle_pkey PRIMARY KEY (id)
);
-- Indexes
CREATE INDEX idx_life_cycle_company_id ON public.life_cycle (company_id);

-- =========================================================
-- Table: public.moe_customer_address
-- =========================================================
CREATE TABLE public.moe_customer_address (
    id bigint NOT NULL,
    account_id bigint DEFAULT 0 NOT NULL,
    address1 character varying(255) DEFAULT '' NOT NULL,
    address2 character varying(255) DEFAULT '' NOT NULL,
    country character varying(255) DEFAULT '' NOT NULL,
    city character varying(255) DEFAULT '' NOT NULL,
    state character varying(255) DEFAULT '' NOT NULL,
    zipcode character varying(10) DEFAULT '' NOT NULL,
    lat character varying(50) DEFAULT '' NOT NULL,
    lng character varying(50) DEFAULT '' NOT NULL,
    is_primary smallint DEFAULT 0 NOT NULL,
    status smallint DEFAULT 1 NOT NULL,
    create_time timestamp with time zone NOT NULL,
    update_time timestamp with time zone NOT NULL,
    CONSTRAINT moe_customer_address_pk PRIMARY KEY (id)
);
-- Indexes
CREATE INDEX index_address_account_id ON public.moe_customer_address (account_id);

-- =========================================================
-- Table: public.moe_customer_pet
-- =========================================================
CREATE TABLE public.moe_customer_pet (
    id bigint NOT NULL,
    account_id bigint DEFAULT 0 NOT NULL,
    pet_name character varying(50) DEFAULT '' NOT NULL,
    pet_type_metadata_id integer DEFAULT 0 NOT NULL,
    avatar_path character varying(255) DEFAULT '' NOT NULL,
    breed_id integer DEFAULT 0 NOT NULL,
    breed_mix smallint DEFAULT 0 NOT NULL,
    birthday character varying(50) DEFAULT '' NOT NULL,
    gender smallint DEFAULT 0 NOT NULL,
    hair_length_metadata_id integer DEFAULT 0 NOT NULL,
    behavior_metadata_id integer DEFAULT 0 NOT NULL,
    weight character varying(50) DEFAULT '' NOT NULL,
    weight_unit_metadata_id smallint DEFAULT 1 NOT NULL,
    fixed_metadata_id integer DEFAULT 0 NOT NULL,
    life_status smallint DEFAULT 1 NOT NULL,
    status smallint DEFAULT 1 NOT NULL,
    create_time timestamp with time zone NOT NULL,
    update_time timestamp with time zone NOT NULL,
    CONSTRAINT moe_customer_pet_pk PRIMARY KEY (id)
);
-- Indexes
CREATE INDEX index_pet_account_id ON public.moe_customer_pet (account_id);

-- =========================================================
-- Table: public.moe_customer_pet_breed
-- =========================================================
CREATE TABLE public.moe_customer_pet_breed (
    id integer NOT NULL,
    pet_type_metadata_id integer DEFAULT 0 NOT NULL,
    name character varying(50) DEFAULT '' NOT NULL,
    sort integer DEFAULT 0 NOT NULL,
    status smallint DEFAULT 1 NOT NULL,
    create_time timestamp with time zone NOT NULL,
    update_time timestamp with time zone NOT NULL,
    CONSTRAINT moe_customer_pet_breed_pk PRIMARY KEY (id)
);

-- =========================================================
-- Table: public.moe_customer_pet_metadata
-- =========================================================
CREATE TABLE public.moe_customer_pet_metadata (
    id integer NOT NULL,
    category smallint DEFAULT 0 NOT NULL,
    name character varying(50) DEFAULT '' NOT NULL,
    sort integer DEFAULT 0 NOT NULL,
    status smallint DEFAULT 1 NOT NULL,
    create_time timestamp with time zone NOT NULL,
    update_time timestamp with time zone NOT NULL,
    CONSTRAINT moe_customer_pet_metadata_pk PRIMARY KEY (id)
);

-- =========================================================
-- Table: public.moe_customer_pet_vaccine
-- =========================================================
CREATE TABLE public.moe_customer_pet_vaccine (
    id bigint NOT NULL,
    pet_id bigint DEFAULT 0 NOT NULL,
    vaccine_metadata_id integer DEFAULT 0 NOT NULL,
    expiration_date character varying(20) DEFAULT '' NOT NULL,
    document_urls character varying(1000) DEFAULT '[]' NOT NULL,
    status smallint DEFAULT 1 NOT NULL,
    create_time timestamp with time zone NOT NULL,
    update_time timestamp with time zone NOT NULL,
    CONSTRAINT moe_customer_pet_vaccine_pk PRIMARY KEY (id)
);
-- Indexes
CREATE INDEX index_pet_vaccine_customer_pet_id ON public.moe_customer_pet_vaccine (pet_id);

-- =========================================================
-- Table: public.moe_default_preferred_frequency
-- =========================================================
CREATE TABLE public.moe_default_preferred_frequency (
    id bigint NOT NULL,
    company_id bigint DEFAULT 0 NOT NULL,
    business_id bigint DEFAULT 0 NOT NULL,
    frequency_type integer NOT NULL,
    calendar_period integer NOT NULL,
    value integer NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    CONSTRAINT moe_default_preferred_frequency_pkey PRIMARY KEY (id),
    CONSTRAINT uk_company_id UNIQUE (company_id)
);

-- =========================================================
-- Table: public.moe_preferred_tip_config
-- =========================================================
CREATE TABLE public.moe_preferred_tip_config (
    id integer NOT NULL,
    business_id integer DEFAULT 0 NOT NULL,
    customer_id integer DEFAULT 0 NOT NULL,
    enable smallint DEFAULT 1 NOT NULL,
    tip_type smallint DEFAULT 1 NOT NULL,
    amount numeric(10,2) DEFAULT 0.00 NOT NULL,
    percentage integer DEFAULT 0 NOT NULL,
    create_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    update_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    company_id bigint DEFAULT 0 NOT NULL,
    CONSTRAINT moe_preferred_tip_config_pkey PRIMARY KEY (id),
    CONSTRAINT udx_biz_customer_id UNIQUE (business_id, customer_id)
);
-- Indexes
CREATE INDEX idx_coid_cuid ON public.moe_preferred_tip_config (company_id, customer_id);
-- Comments
COMMENT ON TABLE public.moe_preferred_tip_config IS 'customer preferred tip';
COMMENT ON COLUMN public.moe_preferred_tip_config.business_id IS 'business id';
COMMENT ON COLUMN public.moe_preferred_tip_config.customer_id IS 'customer id';
COMMENT ON COLUMN public.moe_preferred_tip_config.enable IS 'preferred tips enable, 0-closed 1-open';
COMMENT ON COLUMN public.moe_preferred_tip_config.tip_type IS 'preferred tips 0-by amount, 1-by percent, default 1';
COMMENT ON COLUMN public.moe_preferred_tip_config.amount IS 'by amount';
COMMENT ON COLUMN public.moe_preferred_tip_config.percentage IS 'by percentage [1,100]';
COMMENT ON COLUMN public.moe_preferred_tip_config.create_time IS 'create time';
COMMENT ON COLUMN public.moe_preferred_tip_config.update_time IS 'update time';
COMMENT ON COLUMN public.moe_preferred_tip_config.company_id IS 'company id';

-- =========================================================
-- Table: public.note
-- =========================================================
CREATE TABLE public.note (
    id bigint NOT NULL,
    company_id bigint NOT NULL,
    business_id bigint NOT NULL,
    customer_id bigint NOT NULL,
    note text NOT NULL,
    create_source text NOT NULL,
    update_source text,
    create_by bigint NOT NULL,
    update_by bigint NOT NULL,
    delete_by bigint,
    create_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    update_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    delete_time timestamp without time zone,
    CONSTRAINT note_pkey PRIMARY KEY (id)
);
-- Indexes
CREATE INDEX idx_note_customer_id ON public.note (customer_id);

-- =========================================================
-- Table: public.source
-- =========================================================
CREATE TABLE public.source (
    id bigint NOT NULL,
    company_id bigint NOT NULL,
    business_id bigint NOT NULL,
    name character varying(255) NOT NULL,
    sort integer DEFAULT 0 NOT NULL,
    create_by bigint NOT NULL,
    update_by bigint NOT NULL,
    delete_by bigint,
    create_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    update_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    delete_time timestamp without time zone,
    CONSTRAINT source_pkey PRIMARY KEY (id)
);
-- Indexes
CREATE INDEX idx_source_company_id ON public.source (company_id);

-- =========================================================
-- Table: public.tag
-- =========================================================
CREATE TABLE public.tag (
    id bigint NOT NULL,
    company_id bigint NOT NULL,
    business_id bigint NOT NULL,
    name character varying(255) NOT NULL,
    sort integer DEFAULT 0 NOT NULL,
    create_by bigint NOT NULL,
    update_by bigint NOT NULL,
    delete_by bigint,
    create_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    update_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    delete_time timestamp without time zone,
    CONSTRAINT tag_pkey PRIMARY KEY (id)
);
-- Indexes
CREATE INDEX idx_tag_company_id ON public.tag (company_id);

-- =========================================================
-- Table: public.task
-- =========================================================
CREATE TABLE public.task (
    id bigint NOT NULL,
    company_id bigint NOT NULL,
    business_id bigint NOT NULL,
    customer_id bigint NOT NULL,
    name character varying(255) NOT NULL,
    allocate_staff_id bigint,
    complete_time timestamp without time zone,
    state character varying(255) NOT NULL,
    create_by bigint NOT NULL,
    update_by bigint NOT NULL,
    delete_by bigint,
    create_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    update_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    delete_time timestamp without time zone,
    CONSTRAINT task_pkey PRIMARY KEY (id)
);
-- Indexes
CREATE INDEX idx_task_customer_id ON public.task (customer_id);

-- ===================== END OF FILE =======================
