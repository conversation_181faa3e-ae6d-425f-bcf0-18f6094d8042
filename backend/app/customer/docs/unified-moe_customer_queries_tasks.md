## 新服务统一管控：moe_customer 查询功能任务清单（含SQL）

目的：在新服务中承接 `moe_customer` 库的所有读取能力，完全覆盖 `listCustomerByCustomProperty` 相关功能。以下按数据域列出必须实现的查询接口与对应 SQL 模板，确保不遗漏任何对 `moe_customer` 的直接查询。

注意：
- 所有表均属于 MySQL 数据库：moe_customer。
- 参数采用命名占位，需在实现中使用预编译与字段白名单（尤其 ORDER BY、动态条件部分）。
- 涉及 AND/OR/NOT IN 等复杂条件时，建议采用表达式树生成安全 SQL 片段。

---

### 1) 客户（表：moe_business_customer）

- 关键词（姓名/邮箱，LIKE 模糊）
```sql
-- 输入：:companyId, 可选 :businessIds, :keyword
SELECT id
FROM moe_business_customer
WHERE status = 1
  AND company_id = :companyId
  AND (:businessIds IS NULL OR business_id IN (:businessIds))
  AND (
    email LIKE CONCAT('%', :keyword, '%')
    OR CONCAT(first_name, ' ', last_name) LIKE CONCAT('%', :keyword, '%')
  );
```

- 关键词（last_name 全文检索）
```sql
-- 输入：:companyId, 可选 :businessIds, :keyword
SELECT id
FROM moe_business_customer
WHERE status = 1
  AND company_id = :companyId
  AND (:businessIds IS NULL OR business_id IN (:businessIds))
  AND MATCH(last_name) AGAINST(:keyword IN NATURAL LANGUAGE MODE);
```

- 客户维度过滤（支持动态条件）
```sql
-- 输入：:companyId, 可选 :businessIds, 可选 :customerIds，动态条件见文末“统一过滤拼装”
SELECT id
FROM moe_business_customer
WHERE status = 1
  AND company_id = :companyId
  AND (:businessIds IS NULL OR business_id IN (:businessIds))
  AND (:customerIds IS NULL OR id IN (:customerIds))
  /* AND <dynamic_criteria> */;
```

- 排序 + 分页（client_id / first_name / last_name）
```sql
-- 输入：:companyId, 可选 :businessIds, 可选 :customerIds, :orderBy, :order, :offset, :limit
-- 建议在代码侧将 :orderBy 映射为白名单列（id/first_name/last_name）
SELECT id
FROM moe_business_customer
WHERE status = 1
  AND company_id = :companyId
  AND (:businessIds IS NULL OR business_id IN (:businessIds))
  AND (:customerIds IS NULL OR id IN (:customerIds))
ORDER BY
  CASE WHEN :orderBy = 'client_id' THEN id END,
  CASE WHEN :orderBy = 'first_name' THEN first_name END,
  CASE WHEN :orderBy = 'last_name' THEN last_name END,
  id DESC
LIMIT :offset, :limit;
```

- 有效客户校验（过滤失效ID）
```sql
-- 输入：:companyId, :customerIds
SELECT id
FROM moe_business_customer
WHERE company_id = :companyId
  AND status = 1
  AND id IN (:customerIds);
```

- 列表装配所需基础信息
```sql
-- 输入：:customerIds
SELECT
  id,
  business_id,
  first_name,
  last_name,
  avatar_path,
  client_color,
  email,
  preferred_frequency_type,
  preferred_frequency_day,
  inactive,
  is_unsubscribed,
  source,
  account_id
FROM moe_business_customer
WHERE id IN (:customerIds);
```

---

### 2) 联系方式（表：moe_customer_contact）

- 关键词（电话/邮箱/姓名）
```sql
-- 输入：:companyId, 可选 :businessIds, :keyword, :keywordOnlyNumber, :nameKeyword
SELECT customer_id
FROM moe_customer_contact
WHERE status = 1
  AND company_id = :companyId
  AND (:businessIds IS NULL OR business_id IN (:businessIds))
  AND (
    email LIKE CONCAT('%', :keyword, '%')
    OR phone_number LIKE CONCAT('%', :keywordOnlyNumber, '%')
    OR CONCAT(first_name, last_name) LIKE CONCAT('%', :nameKeyword, '%')
  );
```

- 条件过滤
```sql
-- 输入：:companyId, 可选 :businessIds, 可选 :customerIds，动态条件见文末
SELECT customer_id
FROM moe_customer_contact
WHERE status = 1
  AND company_id = :companyId
  AND (:businessIds IS NULL OR business_id IN (:businessIds))
  AND (:customerIds IS NULL OR customer_id IN (:customerIds))
  /* AND <dynamic_criteria> */;
```

- 计数型过滤（如 email_cnt > 0）
```sql
-- 输入：:companyId, 可选 :businessIds, 可选 :customerIds，HAVING 动态条件
SELECT mbc.id
FROM moe_business_customer mbc
LEFT JOIN moe_customer_contact mcc
  ON mbc.id = mcc.customer_id
  AND mcc.status = 1
WHERE mbc.status = 1
  AND mbc.company_id = :companyId
  AND (:businessIds IS NULL OR mbc.business_id IN (:businessIds))
  AND (:customerIds IS NULL OR mbc.id IN (:customerIds))
GROUP BY mbc.id
HAVING /* <dynamic_having_criteria> */;
```

- 主电话（主联系人）
```sql
-- 输入：:companyId, 可选 :businessIds, :customerIds
SELECT customer_id AS customerId, phone_number AS phoneNumber
FROM moe_customer_contact
WHERE status = 1
  AND is_primary = 1
  AND company_id = :companyId
  AND customer_id IN (:customerIds)
  AND (:businessIds IS NULL OR business_id IN (:businessIds))
GROUP BY customer_id;
```

---

### 3) 地址（表：moe_customer_address）

- 关键词（整行地址拼接模糊）
```sql
-- 输入：:companyId, 可选 :businessIds, :keyword
SELECT address.customer_id
FROM moe_customer_address address
LEFT JOIN moe_business_customer customer
  ON address.customer_id = customer.id
WHERE CONCAT(address1,' ',address2,', ',city,', ',state,', ',country,', ',zipcode)
      LIKE CONCAT('%', :keyword, '%')
  AND address.status = 1
  AND customer.status = 1
  AND address.company_id = :companyId
  AND (:businessIds IS NULL OR address.business_id IN (:businessIds));
```

- 条件过滤（如 zipcode IN / NOT IN）
```sql
-- 输入：:companyId, 可选 :businessIds, 可选 :customerIds, :zipList, :operator ('IN'|'NOT IN')
SELECT DISTINCT mbc.id
FROM moe_business_customer mbc
LEFT JOIN moe_customer_address mca
  ON mbc.id = mca.customer_id
  AND mca.status = 1
WHERE mbc.company_id = :companyId
  AND (:businessIds IS NULL OR mbc.business_id IN (:businessIds))
  AND (:customerIds IS NULL OR mbc.id IN (:customerIds))
  AND mbc.status = 1
  AND (
    (:operator = 'IN'     AND mca.zipcode IN (:zipList))
    OR
    (:operator = 'NOT IN' AND (mca.zipcode IS NULL OR mca.zipcode NOT IN (:zipList)))
  );
```

- 计数型过滤（地址计数）
```sql
-- 输入：:companyId, 可选 :businessIds, 可选 :customerIds，HAVING 动态条件
SELECT mbc.id
FROM moe_business_customer mbc
LEFT JOIN moe_customer_address mca
  ON mbc.id = mca.customer_id
  AND mca.status = 1
WHERE mbc.status = 1
  AND mbc.company_id = :companyId
  AND (:businessIds IS NULL OR mbc.business_id IN (:businessIds))
  AND (:customerIds IS NULL OR mbc.id IN (:customerIds))
GROUP BY mbc.id
HAVING /* <dynamic_having_criteria> */;
```

---

### 4) 宠物（表：moe_customer_pet）

- 关键词（宠物名/品种，LIKE 模糊）
```sql
-- 输入：:companyId, 可选 :businessIds, :keyword
SELECT customer_id
FROM moe_customer_pet
WHERE company_id = :companyId
  AND (:businessIds IS NULL OR business_id IN (:businessIds))
  AND (pet_name LIKE CONCAT('%', :keyword, '%') OR breed LIKE CONCAT('%', :keyword, '%'))
  AND life_status = 1 AND status = 1;
```

- 关键词（宠物名 FULLTEXT，用于“{pet_name} {client_last_name}”组合检索）
```sql
-- 输入：:companyId, 可选 :businessIds, :term
SELECT id, customer_id
FROM moe_customer_pet
WHERE MATCH(pet_name) AGAINST(:term IN NATURAL LANGUAGE MODE)
  AND company_id = :companyId
  AND status = 1
  AND life_status = 1
  AND (:businessIds IS NULL OR business_id IN (:businessIds));
```

- 条件过滤（宠物维度）
```sql
-- 输入：:companyId, 可选 :businessIds, 可选 :customerIds，动态条件见文末
SELECT DISTINCT customer_id
FROM moe_customer_pet
WHERE status = 1
  AND life_status = 1
  AND company_id = :companyId
  AND (:businessIds IS NULL OR business_id IN (:businessIds))
  AND (:customerIds IS NULL OR customer_id IN (:customerIds))
  /* AND <dynamic_criteria> */;
```

- 计数型过滤（宠物计数）
```sql
-- 输入：:companyId, 可选 :businessIds, 可选 :customerIds，HAVING 动态条件
SELECT mbc.id
FROM moe_business_customer mbc
LEFT JOIN moe_customer_pet mcp
  ON mbc.id = mcp.customer_id
  AND mcp.status = 1
  AND mcp.life_status = 1
WHERE mbc.status = 1
  AND mbc.company_id = :companyId
  AND (:businessIds IS NULL OR mbc.business_id IN (:businessIds))
  AND (:customerIds IS NULL OR mbc.id IN (:customerIds))
GROUP BY mbc.id
HAVING /* <dynamic_having_criteria> */;
```

- 宠物数量分布（按类型）
```sql
-- 输入：:companyId, 可选 :businessIds, :customerIds
SELECT mcp.pet_type_id AS petTypeId, COUNT(*) AS count
FROM moe_customer_pet mcp
WHERE mcp.status = 1
  AND mcp.life_status = 1
  AND mcp.company_id = :companyId
  AND (:businessIds IS NULL OR mcp.business_id IN (:businessIds))
  AND (:customerIds IS NULL OR mcp.customer_id IN (:customerIds))
GROUP BY mcp.pet_type_id;
```

- 宠物名/品种列表（列表装配）
```sql
-- 输入：:companyId, 可选 :businessIds, :customerIds
SELECT
  id           AS petId,
  customer_id  AS customerId,
  pet_name     AS petName,
  breed,
  life_status  AS lifeStatus,
  evaluation_status AS evaluationStatus
FROM moe_customer_pet
WHERE status = 1
  AND life_status = 1
  AND company_id = :companyId
  AND (:businessIds IS NULL OR business_id IN (:businessIds))
  AND customer_id IN (:customerIds);
```

---

### 5) 标签绑定（表：moe_customer_tag_binding）

- 过滤（按标签 IN / NOT IN）
```sql
-- 输入：:companyId, 可选 :businessIds, 可选 :customerIds, :tagIds, :operator ('IN'|'NOT IN')
SELECT DISTINCT mbc.id AS customer_id
FROM moe_business_customer mbc
LEFT JOIN moe_customer_tag_binding mctb
  ON mbc.id = mctb.customer_id
WHERE mbc.company_id = :companyId
  AND (:businessIds IS NULL OR mbc.business_id IN (:businessIds))
  AND (:customerIds IS NULL OR mbc.id IN (:customerIds))
  AND mbc.status = 1
  AND (
    (:operator = 'IN'     AND mctb.customer_tag_id IN (:tagIds))
    OR
    (:operator = 'NOT IN' AND (mctb.customer_id IS NULL OR mctb.customer_tag_id NOT IN (:tagIds)))
  );
```

---

### 6) 宠物芯片/编码绑定（表：moe_pet_pet_code_binding）

- 过滤（按编码/芯片 IN / NOT IN，经宠物关联到客户）
```sql
-- 输入：:companyId, 可选 :businessIds, 可选 :customerIds, :codeIds, :operator ('IN'|'NOT IN')
SELECT DISTINCT mcp.customer_id
FROM moe_customer_pet mcp
LEFT JOIN moe_pet_pet_code_binding cb
  ON cb.pet_id = mcp.id
WHERE mcp.company_id = :companyId
  AND mcp.status = 1
  AND mcp.life_status = 1
  AND (:businessIds IS NULL OR mcp.business_id IN (:businessIds))
  AND (:customerIds IS NULL OR mcp.customer_id IN (:customerIds))
  AND (
    (:operator = 'IN'     AND cb.pet_code_id IN (:codeIds))
    OR
    (:operator = 'NOT IN' AND (cb.pet_code_id IS NULL OR cb.pet_code_id NOT IN (:codeIds)))
  );
```

---

### 7) 宠物疫苗绑定（表：moe_pet_pet_vaccine_binding）

- 计数型过滤（如过期疫苗数 > 0）
```sql
-- 输入：:companyId, 可选 :businessIds, 可选 :customerIds, :currentDate，HAVING 动态条件
SELECT mcp.customer_id
FROM moe_customer_pet mcp
LEFT JOIN moe_pet_pet_vaccine_binding vb
  ON vb.pet_id = mcp.id
  AND vb.status = 1
WHERE mcp.company_id = :companyId
  AND (:businessIds IS NULL OR mcp.business_id IN (:businessIds))
  AND mcp.status = 1
  AND mcp.life_status = 1
  AND (:customerIds IS NULL OR mcp.customer_id IN (:customerIds))
  AND vb.expiration_date < :currentDate
GROUP BY mcp.customer_id
HAVING /* <dynamic_having_criteria> */;
```

---

## 统一过滤拼装（实现建议）
- 单值比较：`${column} ${op} :value`（op in =, <, >, <=, >=）
- 区间比较：`${column} BETWEEN :from AND :to`
- 列表比较：`${column} IN (:list)` / `NOT IN (:list)`
- NULL 判定：`${column} IS [NOT] NULL`
- 连接关系：组内 `AND`/`OR`，组间根据 filter group 嵌套；反选或需要“全量参与”的条件（NOT IN、Service Area 等）需先取全量 `customer_id` 再做集合运算。
- 安全：所有列名需做白名单映射；所有值参数化；分页/排序参数同样白名单控制。
