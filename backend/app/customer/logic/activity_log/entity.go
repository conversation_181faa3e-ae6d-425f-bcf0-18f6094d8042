package activitylog

import customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"

type CreateActivityLogDatum struct {
	CustomerID          int64
	BusinessID          int64
	CompanyID           int64
	StaffID             int64
	CustomerName        string
	CustomerPhoneNumber string

	Action *customerpb.ActivityLog_Action
	Source *customerpb.SystemSource
}

type ListActivityLogsDatum struct {
	// filter
	CustomerIDs      []int64
	ActivityLogTypes []customerpb.ActivityLog_Type
	CompanyIDs       []int64
	IDs              []int64

	// page
	PageSize int
	PageNum  int
}

type UpdateActivityLogDatum struct {
	LogID   int64
	StaffID int64

	Action *customerpb.ActivityLog_Action
}
