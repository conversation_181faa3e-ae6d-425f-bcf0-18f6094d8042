package convert

import (
	"context"
	"fmt"
	"time"

	"golang.org/x/sync/errgroup"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"

	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/address"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/contact"
	contacttag "github.com/MoeGolibrary/moego/backend/app/customer/logic/contact_tag"
	customerrelateddata "github.com/MoeGolibrary/moego/backend/app/customer/logic/customer_related_data"
	customfieldlogic "github.com/MoeGolibrary/moego/backend/app/customer/logic/customfield"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/pet"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	activitylog "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/activity_log"
	customerrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer"
	customermerge "github.com/MoeGolibrary/moego/backend/app/customer/repo/producer/customer_merge"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/utils/random"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
	petpb "github.com/MoeGolibrary/moego/backend/proto/pet/v1"
)

type Logic struct {
	customerRepo             customerrepo.Repository
	contactLogic             *contact.Logic
	addressLogic             *address.Logic
	contactTagLogic          *contacttag.Logic
	customFieldLogic         *customfieldlogic.Logic
	customerRelatedDataLogic *customerrelateddata.Logic
	activityLogRepo          activitylog.ReadWriter
	petService               pet.ReadWriter
	txManager                postgres.TransactionManager
	customerMergeProducer    customermerge.API
}

func New() *Logic {
	return &Logic{
		customerRepo:             customerrepo.New(),
		contactLogic:             contact.New(),
		addressLogic:             address.New(),
		contactTagLogic:          contacttag.New(),
		customFieldLogic:         customfieldlogic.New(),
		customerRelatedDataLogic: customerrelateddata.New(),
		activityLogRepo:          activitylog.New(),
		petService:               pet.New(),
		txManager:                postgres.NewTxManager(),
		customerMergeProducer:    customermerge.NewProducer(),
	}
}

type LeadInfo struct {
	lead      *customerrepo.Customer
	contacts  []*contact.Contact
	addresses []*address.Address
	rels      []*customerrelateddata.CustomerRelatedData
	pets      []*petpb.Pet
}

type Datum struct {
	ID int64

	// is collect history log
	WithLog   bool
	LogSource *customerpb.SystemSource // WithLog=true, it is require
}

func (l *Logic) ConvertCustomer(ctx context.Context, datum *Datum) (int64, error) {
	// check
	if datum == nil || datum.ID <= 0 {
		log.ErrorContextf(ctx, "ConvertCustomer ID is invalid, ID:%d", datum.ID)

		return 0, status.Errorf(codes.InvalidArgument, "params is invalid")
	}

	// load lead info
	leadInfo, err := l.loadLeadInfo(ctx, datum.ID)
	if err != nil {
		return 0, err
	}

	// create customer by lead info
	customerID, convertLogID, err := l.createCustomerInfo(ctx, datum, leadInfo)
	if err != nil {
		return 0, err
	}

	log.InfoContextf(ctx, "ConvertCustomer Success, leadID:%d, customerID:%d, contactsLen:%d, addressesLen:%d, "+
		"relsLen:%d, petsLen:%d, converLogID:%d", datum.ID, customerID, len(leadInfo.contacts), len(leadInfo.addresses),
		len(leadInfo.rels), len(leadInfo.pets), convertLogID)

	return customerID, nil
}

func (l *Logic) loadLeadInfo(ctx context.Context, id int64) (*LeadInfo, error) {
	// lead info
	var (
		res = &LeadInfo{}
		g   errgroup.Group
	)

	// load lead
	lead, err := l.customerRepo.Get(ctx, id, customerpb.CustomerType_LEAD)
	if err != nil {
		log.ErrorContextf(ctx, "ConvertCustomer Get Lead err, ID:%d, err:%v", id, err)

		return nil, err
	}
	res.lead = lead

	// load contacts
	g.Go(func() error {
		contactResp, err := l.contactLogic.List(ctx, &contact.ListContactsRequest{
			Filter: &contact.ListContactsFilter{
				CustomerIDs: []int64{id},
				States: []customerpb.Contact_State{
					customerpb.Contact_ACTIVE,
				},
			},
			Pagination: &contact.ListContactsPagination{
				PageSize: 1000,
			},
		})
		if err != nil {
			log.ErrorContextf(ctx, "ConvertCustomer List Lead Contacts err, ID:%d, err:%v", id, err)

			return err
		}
		res.contacts = contactResp.Contacts

		return nil
	})

	// load addresses
	g.Go(func() error {
		addressResp, err := l.addressLogic.List(ctx, &address.ListAddressesRequest{
			Filter: &address.ListAddressesFilter{
				CustomerID: id,
				States: []customerpb.Address_State{
					customerpb.Address_ACTIVE,
				},
			},
			Pagination: &address.ListAddressesPagination{
				PageSize: 1000,
			},
		})
		if err != nil {
			log.ErrorContextf(ctx, "ConvertCustomer List Lead Addresses err, ID:%d, err:%v", id, err)

			return err
		}
		res.addresses = addressResp.Addresses

		return nil
	})

	// load rels
	g.Go(func() error {
		relResp, err := l.customerRelatedDataLogic.List(ctx, &customerrelateddata.ListCustomerRelatedDataRequest{
			Filter: &customerrelateddata.ListCustomerRelatedDataFilter{
				CustomerIDs: []int64{id},
				States: []customerpb.CustomerRelatedData_State{
					customerpb.CustomerRelatedData_ACTIVE,
				},
			},
			Pagination: &customerrelateddata.ListCustomerRelatedDataPagination{
				PageSize: 1000,
			},
		})
		if err != nil {
			log.ErrorContextf(ctx, "ConvertCustomer List Lead Rels err, ID:%d, err:%v", id, err)

			return err
		}
		res.rels = relResp.CustomerRelatedData

		return nil
	})

	// load pets
	g.Go(func() error {
		p, err := l.petService.ListCustomerPet(ctx, id)
		if err != nil {
			log.ErrorContextf(ctx, "ConvertCustomer List Lead Pets err, ID:%d, err:%v", id, err)

			return err
		}
		res.pets = p

		return nil
	})

	// load all
	if err := g.Wait(); err != nil {
		log.ErrorContextf(ctx, "ConvertCustomer Load all err, err:%v", err)

		return nil, err
	}

	return res, nil
}

func (l *Logic) createCustomerInfo(ctx context.Context,
	datum *Datum, leadInfo *LeadInfo) (int64, int64, error) {
	// params
	var customerID int64
	var convertLogID int64
	petRels := make([]*businesscustomerpb.MergeRelationDef, 0, len(leadInfo.pets))

	// add ops
	ops := make([]func(context.Context, *gorm.DB) error, 0)
	ops = append(ops, l.buildCreateCustomerOp(datum, leadInfo, &customerID))
	ops = append(ops, l.buildCreateContactsOps(datum, leadInfo, &customerID)...)
	ops = append(ops, l.buildCreateAddressesOps(datum, leadInfo, &customerID)...)
	ops = append(ops, l.buildCreateRelsOps(datum, leadInfo, &customerID)...)
	ops = append(ops, l.buildUpdateLeadConvertOp(datum, &customerID))
	if datum.WithLog {
		ops = append(ops, l.buildCollectConvertLogOp(datum, leadInfo, &customerID, &convertLogID))
	}
	if len(leadInfo.pets) > 0 {
		ops = append(ops, l.buildCreatePetsOp(datum, leadInfo, &customerID, &petRels))
	}
	ops = append(ops, l.buildSendMergeMsgOp(datum, leadInfo, &customerID, &petRels))

	// execute ops
	if err := l.txManager.ExecuteInTransaction(ctx, ops); err != nil {
		log.ErrorContextf(ctx, "createCustomerInfo tx err, err:%v", err)

		return 0, 0, err
	}

	return customerID, convertLogID, nil
}

func getContactFirstPhoneNumber(contacts []*contact.Contact) string {
	if len(contacts) == 0 || contacts[0] == nil {
		return ""
	}

	return contacts[0].Phone
}

func getRelFirstBusinessID(rels []*customerrelateddata.CustomerRelatedData) int64 {
	if len(rels) == 0 || rels[0] == nil {
		return 0
	}

	return rels[0].PreferredBusinessID
}

// 创建 customer 的 op
func (l *Logic) buildCreateCustomerOp(datum *Datum, leadInfo *LeadInfo,
	customerID *int64) func(context.Context, *gorm.DB) error {
	return func(opCtx context.Context, tx *gorm.DB) error {
		leadInfo.lead.ID = 0
		leadInfo.lead.CustomerType = customerpb.CustomerType_CUSTOMER
		leadInfo.lead.CreatedTime = time.Now()
		leadInfo.lead.UpdatedTime = time.Now()
		customer, err := l.customerRepo.WithTx(tx).Create(opCtx, leadInfo.lead)
		if err != nil {
			log.ErrorContextf(opCtx, "ConvertCustomer Create Customer err, ID:%d, err:%v", datum.ID, err)

			return err
		}
		*customerID = customer.ID

		return nil
	}
}

// 创建 contacts 的 ops
func (l *Logic) buildCreateContactsOps(datum *Datum, leadInfo *LeadInfo,
	customerID *int64) []func(context.Context, *gorm.DB) error {
	ops := make([]func(context.Context, *gorm.DB) error, 0, len(leadInfo.contacts))
	for _, c := range leadInfo.contacts {
		contact := c
		ops = append(ops, func(opCtx context.Context, tx *gorm.DB) error {
			contact.ID = 0
			contact.CustomerID = *customerID
			contact.CreatedTime = time.Now()
			contact.UpdatedTime = time.Now()
			if _, err := l.contactLogic.WithTx(tx).CreateWithoutTX(opCtx, contact); err != nil {
				log.ErrorContextf(opCtx, "ConvertCustomer Create Contact err, ID:%d, err:%v", datum.ID, err)

				return err
			}

			return nil
		})
	}

	return ops
}

// 创建 addresses 的 ops
func (l *Logic) buildCreateAddressesOps(datum *Datum, leadInfo *LeadInfo,
	customerID *int64) []func(context.Context, *gorm.DB) error {
	ops := make([]func(context.Context, *gorm.DB) error, 0, len(leadInfo.addresses))
	for _, a := range leadInfo.addresses {
		address := a
		ops = append(ops, func(opCtx context.Context, tx *gorm.DB) error {
			address.ID = 0
			address.CustomerID = *customerID
			address.CreatedTime = time.Now()
			address.UpdatedTime = time.Now()
			if _, err := l.addressLogic.WithTx(tx).Create(opCtx, address); err != nil {
				log.ErrorContextf(opCtx, "ConvertCustomer Create Address err, ID:%d, err:%v", datum.ID, err)

				return err
			}

			return nil
		})
	}

	return ops
}

// 创建 rels 的 ops
func (l *Logic) buildCreateRelsOps(datum *Datum, leadInfo *LeadInfo,
	customerID *int64) []func(context.Context, *gorm.DB) error {
	ops := make([]func(context.Context, *gorm.DB) error, 0, len(leadInfo.rels))
	for _, r := range leadInfo.rels {
		rel := r
		ops = append(ops, func(opCtx context.Context, tx *gorm.DB) error {
			rel.ID = 0
			rel.CustomerID = *customerID
			rel.CreatedTime = time.Now()
			rel.UpdatedTime = time.Now()
			rel.CustomerCode = random.UpperString(8)
			if _, err := l.customerRelatedDataLogic.WithTx(tx).Create(opCtx, rel); err != nil {
				log.ErrorContextf(opCtx, "ConvertCustomer Create Rel err, ID:%d, err:%v", datum.ID, err)

				return err
			}

			return nil
		})
	}

	return ops
}

// 更新 lead convert 的 op
func (l *Logic) buildUpdateLeadConvertOp(datum *Datum,
	customerID *int64) func(context.Context, *gorm.DB) error {
	return func(opCtx context.Context, tx *gorm.DB) error {
		if _, err := l.customerRepo.WithTx(tx).Update(opCtx, &customerrepo.Customer{
			ID:                  datum.ID,
			ConvertToCustomerID: *customerID,
			UpdatedTime:         time.Now(),
		}); err != nil {
			log.ErrorContextf(opCtx, "ConvertCustomer Update Lead ConvertToCustomerID err, ID:%d, err:%v",
				datum.ID, err)

			return err
		}

		return nil
	}
}

// 收集 convert log 的 op
func (l *Logic) buildCollectConvertLogOp(datum *Datum, leadInfo *LeadInfo,
	customerID *int64, convertLogID *int64) func(context.Context, *gorm.DB) error {
	return func(opCtx context.Context, tx *gorm.DB) error {
		convertLog := &activitylog.ActivityLog{
			CompanyID:           leadInfo.lead.OrganizationID,
			BusinessID:          getRelFirstBusinessID(leadInfo.rels),
			CustomerID:          datum.ID,
			CustomerName:        fmt.Sprintf("%s %s", leadInfo.lead.GivenName, leadInfo.lead.FamilyName),
			CustomerPhoneNumber: getContactFirstPhoneNumber(leadInfo.contacts),
			Type:                customerpb.ActivityLog_CONVERT,
			Action: &customerpb.ActivityLog_Action{
				Action: &customerpb.ActivityLog_Action_Convert{
					Convert: &customerpb.ActivityLog_Convert{
						OriginType: customerpb.ActivityLog_Convert_LEAD,
						OriginId:   datum.ID,
						TargetType: customerpb.ActivityLog_Convert_CUSTOMER,
						TargetId:   *customerID,
					},
				},
			},
			Source:     datum.LogSource,
			CreateTime: time.Now(),
			UpdateTime: time.Now(),
		}
		if err := l.activityLogRepo.WithTx(tx).Create(opCtx, convertLog); err != nil {
			log.ErrorContextf(opCtx, "ConvertCustomer Create Convert Log err, ID:%d, err:%v", datum.ID, err)

			return err
		}
		*convertLogID = convertLog.ID

		return nil
	}
}

// 创建 pets 的 op
func (l *Logic) buildCreatePetsOp(datum *Datum, leadInfo *LeadInfo,
	customerID *int64, petRels *[]*businesscustomerpb.MergeRelationDef) func(context.Context, *gorm.DB) error {
	return func(opCtx context.Context, _ *gorm.DB) error {
		for _, pet := range leadInfo.pets {
			originPetID := pet.Id
			pet.Id = 0
			pet.CustomerId = *customerID
			newPetID, err := l.petService.CreatePet(opCtx, pet)
			if err != nil {
				log.ErrorContextf(opCtx, "ConvertCustomer Create Pet err, ID:%d, err:%v, originPetID:%d",
					datum.ID, err, originPetID)

				return err
			}
			*petRels = append(*petRels, &businesscustomerpb.MergeRelationDef{
				TargetId:  newPetID,
				SourceIds: []int64{originPetID},
			})
		}

		return nil
	}
}

// 发送 merge 消息的 op
func (l *Logic) buildSendMergeMsgOp(datum *Datum, leadInfo *LeadInfo,
	customerID *int64, petRels *[]*businesscustomerpb.MergeRelationDef) func(context.Context, *gorm.DB) error {
	return func(opCtx context.Context, _ *gorm.DB) error {
		if err := l.customerMergeProducer.SendCustomerMergeMessage(opCtx, &customermerge.MessageDatum{
			CompanyID: leadInfo.lead.OrganizationID,
			CustomerRel: &businesscustomerpb.MergeRelationDef{
				TargetId:  *customerID,
				SourceIds: []int64{datum.ID},
			},
			PetRels: *petRels,
		}); err != nil {
			log.ErrorContextf(opCtx, "ConvertCustomer SendCustomerMergeMessage err, ID:%d, err:%v", datum.ID, err)

			return err
		}

		return nil
	}
}
