load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "convert",
    srcs = ["convert.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/logic/convert",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/logic/address",
        "//backend/app/customer/logic/contact",
        "//backend/app/customer/logic/contact_tag",
        "//backend/app/customer/logic/customer_related_data",
        "//backend/app/customer/logic/customfield",
        "//backend/app/customer/repo/pet",
        "//backend/app/customer/repo/postgres",
        "//backend/app/customer/repo/postgres/activity_log",
        "//backend/app/customer/repo/postgres/customer",
        "//backend/app/customer/repo/producer/customer_merge",
        "//backend/common/rpc/framework/log",
        "//backend/common/utils/random",
        "//backend/proto/customer/v2:customer",
        "//backend/proto/pet/v1:pet",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/business_customer/v1:business_customer",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_x_sync//errgroup",
    ],
)
