load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "metadata",
    srcs = [
        "entity.go",
        "metadata.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/logic/metadata",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/logic/address",
        "//backend/app/customer/logic/contact",
        "//backend/app/customer/logic/customer_related_data",
        "//backend/app/customer/repo/postgres",
        "//backend/app/customer/repo/postgres/address",
        "//backend/app/customer/repo/postgres/customer",
        "//backend/app/customer/repo/postgres/customer_related_data",
        "//backend/app/customer/repo/postgres/default_preferred_frequency",
        "//backend/app/customer/repo/postgres/preferred_tip_config",
        "//backend/common/rpc/framework/errs",
        "//backend/common/rpc/framework/log",
        "//backend/common/utils/random",
        "//backend/proto/customer/v2:customer",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_protobuf//types/known/structpb",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
