package metadata

import (
	"context"
	"fmt"
	"time"

	"google.golang.org/grpc/codes"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/logic/address"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/contact"
	customerrelateddata "github.com/MoeGolibrary/moego/backend/app/customer/logic/customer_related_data"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	addressrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/address"
	customerrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer"
	customerrelateddatarepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer_related_data"
	defaultpreferredfrequencyrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/default_preferred_frequency"
	preferredtipconfigrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/preferred_tip_config"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/utils/random"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Logic struct {
	customerRepo             customerrepo.Repository
	addressRepo              addressrepo.Repository
	contactLogic             *contact.Logic
	customerRelatedDataRepo  customerrelateddatarepo.Repository
	preferredTipConfigRepo   preferredtipconfigrepo.Repository
	defaultPreferredFreqRepo defaultpreferredfrequencyrepo.Repository
	txManager                postgres.TransactionManager
}

func New() *Logic {
	return &Logic{
		customerRepo:             customerrepo.New(),
		addressRepo:              addressrepo.New(),
		contactLogic:             contact.New(),
		customerRelatedDataRepo:  customerrelateddatarepo.New(),
		preferredTipConfigRepo:   preferredtipconfigrepo.New(),
		defaultPreferredFreqRepo: defaultpreferredfrequencyrepo.New(),
		txManager:                postgres.NewTxManager(),
	}
}

func NewByParams(
	customerRepo customerrepo.Repository,
	addressRepo addressrepo.Repository,
	contactLogic *contact.Logic,
	customerRelatedDataRepo customerrelateddatarepo.Repository,
	preferredTipConfigRepo preferredtipconfigrepo.Repository,
	defaultPreferredFreqRepo defaultpreferredfrequencyrepo.Repository,
	txManager postgres.TransactionManager,
) *Logic {
	return &Logic{
		customerRepo:             customerRepo,
		addressRepo:              addressRepo,
		contactLogic:             contactLogic,
		customerRelatedDataRepo:  customerRelatedDataRepo,
		preferredTipConfigRepo:   preferredTipConfigRepo,
		defaultPreferredFreqRepo: defaultPreferredFreqRepo,
		txManager:                txManager,
	}
}

func (l *Logic) CreateMetadata(ctx context.Context, metadata *Metadata) (*Metadata, error) {
	// 设置创建时间和状态，参考 customer.go 和 lead.go 的实现
	now := time.Now().UTC()
	metadata.CreatedTime = now
	metadata.UpdatedTime = now

	// 根据类型设置默认状态
	switch metadata.CustomerType {
	case customerpb.CustomerType_CUSTOMER:
		metadata.State = customerpb.Metadata_State(customerpb.Customer_ACTIVE)
	case customerpb.CustomerType_LEAD:
		metadata.State = customerpb.Metadata_State(customerpb.Lead_ACTIVE)
	}

	dbCustomer := metadata.ToDB()

	createdCustomer, err := l.customerRepo.Create(ctx, dbCustomer)
	if err != nil {
		return nil, err
	}

	return FromCustomerRepo(createdCustomer), nil
}

func (l *Logic) GetMetadata(ctx context.Context, id int64) (*Metadata, error) {
	// 直接从数据库查询，不需要关心 customer_type
	customer, err := l.customerRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// 直接转换为 Metadata 返回，customer_type 信息已经包含在查询结果中
	return FromCustomerRepo(customer), nil
}

func (l *Logic) ListMetadata(ctx context.Context, req *ListMetadataRequest) (*ListMetadataResponse, error) {
	if req.Filter == nil {
		return nil, fmt.Errorf("filter is required")
	}
	if req.OrderBy == nil {
		req.OrderBy = &ListMetadataOrderBy{
			Field:     customerpb.ListMetadataRequest_Sorting_CREATED_TIME,
			Direction: customerpb.Direction_DESC,
		}
	}

	// 转换状态
	states := make([]customerpb.Customer_State, len(req.Filter.States))
	for i, state := range req.Filter.States {
		states[i] = customerpb.Customer_State(state)
	}

	// 简单的 cursor 处理，参考 lead.go 的实现
	var cursor *postgres.Cursor
	if req.Pagination != nil && req.Pagination.Cursor != "" {
		// 这里可以实现具体的 cursor 解码逻辑
		cursor = nil // 简化处理
	}

	dbCustomers, err := l.customerRepo.ListMetadataByCursor(ctx, &customerrepo.ListFilter{
		IDs:                  req.Filter.IDs,
		OrganizationType:     req.Filter.OrganizationType,
		OrganizationID:       req.Filter.OrganizationID,
		CustomerType:         req.Filter.CustomerType,
		States:               states,
		OwnerStaffIDs:        req.Filter.OwnerStaffIDs,
		LifecycleIDs:         req.Filter.LifecycleIDs,
		ReferralSourceIDs:    req.Filter.ReferralSourceIDs,
		ConvertedCustomerIDs: req.Filter.ConvertedCustomerIDs,
		IsConverted:          req.Filter.IsConverted,
	}, &customerrepo.Pagination{
		PageSize:        req.Pagination.PageSize,
		Cursor:          cursor,
		ReturnTotalSize: req.Pagination.ReturnTotalSize,
	}, &customerrepo.MetadataOrderBy{
		Field:     req.OrderBy.Field,
		Direction: req.OrderBy.Direction,
	})
	if err != nil {
		return nil, err
	}

	// 转换结果
	metadata := make([]*Metadata, len(dbCustomers.Data))
	for i, customer := range dbCustomers.Data {
		metadata[i] = FromCustomerRepo(customer)
	}

	result := &ListMetadataResponse{
		Metadata: metadata,
		HasNext:  dbCustomers.HasNext,
	}
	if dbCustomers.TotalCount != nil {
		result.TotalSize = dbCustomers.TotalCount
	}
	if dbCustomers.HasNext && len(dbCustomers.Data) > 0 {
		lastCustomer := dbCustomers.Data[len(dbCustomers.Data)-1]
		cursor := postgres.Cursor{
			ID:        lastCustomer.ID,
			CreatedAt: lastCustomer.CreatedTime,
		}
		result.NextToken = cursor.EncodeCursor()
	}

	return result, nil
}

func (l *Logic) UpdateMetadata(ctx context.Context, id int64, updateRef *UpdateMetadataRequest) (*Metadata, error) {
	// 先获取现有记录，参考 customer.go 和 lead.go 的实现
	metadata, err := l.GetMetadata(ctx, id)
	if err != nil {
		return nil, err
	}

	// 更新字段
	now := time.Now().UTC()
	metadata.UpdatedTime = now
	metadata.GivenName = updateRef.GivenName
	metadata.FamilyName = updateRef.FamilyName
	metadata.CustomFields = updateRef.CustomFields
	metadata.LifeCycleID = updateRef.LifeCycleID
	metadata.OwnerStaffID = updateRef.OwnerStaffID
	metadata.ActionStateID = updateRef.ActionStateID
	metadata.AvatarPath = updateRef.AvatarPath
	metadata.ReferralSourceID = updateRef.ReferralSourceID
	metadata.State = updateRef.State

	updatedCustomer, err := l.customerRepo.Update(ctx, metadata.ToDB())
	if err != nil {
		return nil, err
	}

	return FromCustomerRepo(updatedCustomer), nil
}

func (l *Logic) DeleteMetadata(ctx context.Context, id int64) error {
	// 直接删除，不需要关心 customer_type
	// 底层 repository 的 Delete 方法会处理软删除逻辑
	return l.customerRepo.Delete(ctx, id)
}

// CreateMetadataAggregate 创建聚合元数据，包括主记录、地址、联系人和相关数据
func (l *Logic) CreateMetadataAggregate(ctx context.Context,
	aggregate *AggregateRequest) (*AggregateResponse, error) {
	if aggregate == nil || aggregate.Metadata == nil {
		return nil, errs.Newm(codes.InvalidArgument, "metadata is required")
	}

	log.DebugContextf(ctx, "CreateMetadataAggregate: %+v", aggregate)
	var createdMetadata *Metadata
	var createdAddresses []*address.Address
	var createdContacts []*contact.Contact
	var createdRelatedData *customerrelateddata.CustomerRelatedData

	// 构建操作队列
	ops := make([]func(context.Context, *gorm.DB) error, 0)

	// 添加创建metadata的操作
	ops = append(ops, l.buildCreateMetadataOp(aggregate, &createdMetadata))

	// 添加创建相关数据的操作
	if aggregate.RelatedData != nil {
		ops = append(ops, l.buildCreateRelatedDataOp(aggregate, &createdMetadata, &createdRelatedData))
	}

	// 添加创建地址的操作
	ops = append(ops, l.buildCreateAddressesOps(aggregate, &createdMetadata, &createdAddresses)...)

	// 添加创建联系人的操作
	ops = append(ops, l.buildCreateContactsOps(aggregate, &createdMetadata, &createdContacts)...)

	// 在事务中执行所有操作
	if err := l.txManager.ExecuteInTransaction(ctx, ops); err != nil {
		return nil, err
	}

	return &AggregateResponse{
		Metadata:    createdMetadata,
		Addresses:   createdAddresses,
		Contacts:    createdContacts,
		RelatedData: createdRelatedData,
	}, nil
}

// buildCreateMetadataOp 构建创建metadata的操作
func (l *Logic) buildCreateMetadataOp(aggregate *AggregateRequest,
	createdMetadata **Metadata) func(context.Context, *gorm.DB) error {
	return func(opCtx context.Context, tx *gorm.DB) error {
		// 设置创建时间和状态
		now := time.Now().UTC()
		aggregate.Metadata.CreatedTime = now
		aggregate.Metadata.UpdatedTime = now
		aggregate.Metadata.CustomerCode = random.String(16)

		// 根据类型设置默认状态
		switch aggregate.Metadata.CustomerType {
		case customerpb.CustomerType_CUSTOMER:
			aggregate.Metadata.State = customerpb.Metadata_State(customerpb.Customer_ACTIVE)
		case customerpb.CustomerType_LEAD:
			aggregate.Metadata.State = customerpb.Metadata_State(customerpb.Lead_ACTIVE)
		}

		dbCustomer := aggregate.Metadata.ToDB()

		createdCustomer, err := l.customerRepo.WithTx(tx).Create(opCtx, dbCustomer)
		if err != nil {
			return err
		}

		*createdMetadata = FromCustomerRepo(createdCustomer)

		return nil
	}
}

// buildCreateRelatedDataOp 构建创建相关数据的操作
func (l *Logic) buildCreateRelatedDataOp(aggregate *AggregateRequest,
	createdMetadata **Metadata,
	createdRelatedData **customerrelateddata.CustomerRelatedData) func(context.Context, *gorm.DB) error {
	return func(opCtx context.Context, tx *gorm.DB) error {
		relData := aggregate.RelatedData
		relData.CustomerID = (*createdMetadata).ID
		// 使用已创建的metadata中的customer_code
		relData.CustomerCode = (*createdMetadata).CustomerCode
		now := time.Now().UTC()
		relData.CreatedTime = now
		relData.UpdatedTime = now
		relData.State = customerpb.CustomerRelatedData_ACTIVE

		createdRelData, err := l.customerRelatedDataRepo.WithTx(tx).Create(opCtx, relData.ToDB())
		if err != nil {
			return err
		}

		logicRelData := &customerrelateddata.CustomerRelatedData{}
		*logicRelData = *relData
		logicRelData.ID = createdRelData.ID
		logicRelData.CreatedTime = createdRelData.CreatedTime
		logicRelData.UpdatedTime = createdRelData.UpdatedTime

		// 创建 preferred_tip_config 如果有相关字段
		if relData.PreferredTipEnable != 0 || relData.PreferredTipType != 0 ||
			relData.PreferredTipAmount != 0 || relData.PreferredTipPercentage != 0 {
			tipConfig := &preferredtipconfigrepo.PreferredTipConfig{
				CustomerID: relData.CustomerID,
				BusinessID: relData.PreferredBusinessID,
				Enable:     relData.PreferredTipEnable,
				TipType:    relData.PreferredTipType,
				Amount:     relData.PreferredTipAmount,
				Percentage: relData.PreferredTipPercentage,
				CompanyID:  relData.CompanyID,
			}

			// 直接使用事务tx创建记录
			err = tx.WithContext(opCtx).Create(tipConfig).Error
			if err != nil {
				return err
			}

			logicRelData.PreferredTipEnable = relData.PreferredTipEnable
			logicRelData.PreferredTipType = relData.PreferredTipType
			logicRelData.PreferredTipAmount = relData.PreferredTipAmount
			logicRelData.PreferredTipPercentage = relData.PreferredTipPercentage
		}

		// 创建 default_preferred_frequency 如果有相关字段
		if relData.DefaultPreferredFrequencyType != 0 || relData.DefaultPreferredCalendarPeriod != 0 ||
			relData.DefaultPreferredFrequencyValue != 0 {
			defaultFreq := &defaultpreferredfrequencyrepo.DefaultPreferredFrequency{
				CompanyID:      relData.CompanyID,
				BusinessID:     relData.PreferredBusinessID,
				FrequencyType:  relData.DefaultPreferredFrequencyType,
				CalendarPeriod: relData.DefaultPreferredCalendarPeriod,
				Value:          relData.DefaultPreferredFrequencyValue,
			}

			// 直接使用事务tx创建记录
			err = tx.WithContext(opCtx).Create(defaultFreq).Error
			if err != nil {
				return err
			}

			logicRelData.DefaultPreferredFrequencyType = relData.DefaultPreferredFrequencyType
			logicRelData.DefaultPreferredCalendarPeriod = relData.DefaultPreferredCalendarPeriod
			logicRelData.DefaultPreferredFrequencyValue = relData.DefaultPreferredFrequencyValue
		}

		*createdRelatedData = logicRelData

		return nil
	}
}

// buildCreateAddressesOps 构建创建地址的操作列表
func (l *Logic) buildCreateAddressesOps(aggregate *AggregateRequest,
	createdMetadata **Metadata, createdAddresses *[]*address.Address) []func(context.Context, *gorm.DB) error {
	ops := make([]func(context.Context, *gorm.DB) error, 0, len(aggregate.Addresses))

	for _, addr := range aggregate.Addresses {
		currentAddr := addr // 避免闭包问题
		ops = append(ops, func(opCtx context.Context, tx *gorm.DB) error {
			currentAddr.ID = 0
			currentAddr.CustomerID = (*createdMetadata).ID
			currentAddr.State = customerpb.Address_ACTIVE

			createdAddr, err := l.addressRepo.WithTx(tx).Create(opCtx, currentAddr.ToDB())
			if err != nil {
				return err
			}

			logicAddr := &address.Address{}
			*logicAddr = *currentAddr
			logicAddr.ID = createdAddr.ID
			logicAddr.CreatedTime = createdAddr.CreatedTime
			logicAddr.UpdatedTime = createdAddr.UpdatedTime
			*createdAddresses = append(*createdAddresses, logicAddr)

			return nil
		})
	}

	return ops
}

// buildCreateContactsOps 构建创建联系人的操作列表
func (l *Logic) buildCreateContactsOps(aggregate *AggregateRequest,
	createdMetadata **Metadata, createdContacts *[]*contact.Contact) []func(context.Context, *gorm.DB) error {
	ops := make([]func(context.Context, *gorm.DB) error, 0, len(aggregate.Contacts))

	for _, cont := range aggregate.Contacts {
		currentContact := cont // 避免闭包问题
		ops = append(ops, func(opCtx context.Context, tx *gorm.DB) error {
			currentContact.ID = 0
			currentContact.CustomerID = (*createdMetadata).ID
			currentContact.CreatedTime = time.Now().UTC()
			currentContact.UpdatedTime = time.Now().UTC()

			createdContact, err := l.contactLogic.WithTx(tx).CreateWithoutTX(opCtx, currentContact)
			if err != nil {
				return err
			}

			logicContact := &contact.Contact{}
			*logicContact = *currentContact
			logicContact.ID = createdContact.ID
			logicContact.CreatedTime = createdContact.CreatedTime
			logicContact.UpdatedTime = createdContact.UpdatedTime
			logicContact.Tags = createdContact.Tags
			*createdContacts = append(*createdContacts, logicContact)

			return nil
		})
	}

	return ops
}

// ConvertMetadataAttribute 批量转换元数据属性（生命周期和行动状态）
func (l *Logic) ConvertMetadataAttribute(ctx context.Context, datum *ConvertMetadataAttributeDatum) error {
	// 参数校验
	if datum == nil || len(datum.MetadataIDs) == 0 {
		return errs.Newm(codes.InvalidArgument, "params is invalid: metadata_ids is required")
	}

	// 构建更新数据
	updates := make([]*customerrepo.Customer, 0, len(datum.MetadataIDs))
	for _, metadataID := range datum.MetadataIDs {
		if metadataID <= 0 || (datum.CustomizeLifeCycleID == nil && datum.CustomizeActionStateID == nil) {
			continue
		}
		update := &customerrepo.Customer{
			ID:          metadataID,
			UpdatedTime: time.Now(),
		}
		if datum.CustomizeLifeCycleID != nil {
			update.LifeCycleID = *datum.CustomizeLifeCycleID
		}
		if datum.CustomizeActionStateID != nil {
			update.ActionStateID = *datum.CustomizeActionStateID
		}
		updates = append(updates, update)
	}

	if len(updates) == 0 {
		return errs.Newm(codes.InvalidArgument, "no valid updates found")
	}

	// 执行批量更新
	if err := l.customerRepo.Tx(ctx, func(repo customerrepo.Repository) error {
		for _, update := range updates {
			if _, err := repo.Update(ctx, update); err != nil {
				return err
			}
		}

		return nil
	}); err != nil {
		return err
	}

	return nil
}
