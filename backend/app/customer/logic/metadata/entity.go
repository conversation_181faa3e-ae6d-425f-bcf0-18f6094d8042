package metadata

import (
	"time"

	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/moego/backend/app/customer/logic/address"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/contact"
	customerrelateddata "github.com/MoeGolibrary/moego/backend/app/customer/logic/customer_related_data"
	customerrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Metadata struct {
	ID                  int64                           `json:"id"`
	OrganizationType    customerpb.OrganizationRef_Type `json:"organization_type"`
	OrganizationID      int64                           `json:"organization_id"`
	CustomerType        customerpb.CustomerType         `json:"customer_type"`
	GivenName           string                          `json:"given_name"`
	FamilyName          string                          `json:"family_name"`
	State               customerpb.Metadata_State       `json:"state"`
	CustomFields        *structpb.Struct                `json:"custom_fields"`
	LifeCycleID         int64                           `json:"life_cycle_id"`
	OwnerStaffID        int64                           `json:"owner_staff_id"`
	ActionStateID       int64                           `json:"action_state_id"`
	AvatarPath          string                          `json:"avatar_path"`
	CustomerCode        string                          `json:"customer_code"`
	ReferralSourceID    int64                           `json:"referral_source_id"`
	ConvertToCustomerID *int64                          `json:"convert_to_customer_id,omitempty"`
	CreatedTime         time.Time                       `json:"created_time"`
	UpdatedTime         time.Time                       `json:"updated_time"`
	DeletedTime         *time.Time                      `json:"deleted_time"`
}

// ToDB 将 Metadata 转换为 customerrepo.Customer
func (m *Metadata) ToDB() *customerrepo.Customer {
	convertToCustomerID := int64(0)
	if m.ConvertToCustomerID != nil {
		convertToCustomerID = *m.ConvertToCustomerID
	}

	return &customerrepo.Customer{
		ID:                  m.ID,
		OrganizationType:    m.OrganizationType,
		OrganizationID:      m.OrganizationID,
		GivenName:           m.GivenName,
		FamilyName:          m.FamilyName,
		CustomerType:        m.CustomerType,
		State:               customerpb.Customer_State(m.State),
		CustomFields:        m.CustomFields,
		LifeCycleID:         m.LifeCycleID,
		OwnerStaffID:        m.OwnerStaffID,
		ActionStateID:       m.ActionStateID,
		AvatarPath:          m.AvatarPath,
		CustomerCode:        m.CustomerCode,
		ReferralSourceID:    m.ReferralSourceID,
		ConvertToCustomerID: convertToCustomerID,
		DeletedTime:         m.DeletedTime,
		CreatedTime:         m.CreatedTime,
		UpdatedTime:         m.UpdatedTime,
	}
}

func (m *Metadata) ToPB() *customerpb.Metadata {
	if m == nil {
		return nil
	}

	metadata := &customerpb.Metadata{
		Id: m.ID,
		Organization: &customerpb.OrganizationRef{
			Type: m.OrganizationType,
			Id:   m.OrganizationID,
		},
		CustomerType:     m.CustomerType,
		GivenName:        m.GivenName,
		FamilyName:       m.FamilyName,
		State:            m.State,
		CustomFields:     m.CustomFields,
		LifecycleId:      m.LifeCycleID,
		OwnerStaffId:     m.OwnerStaffID,
		ActionStateId:    m.ActionStateID,
		AvatarPath:       m.AvatarPath,
		CustomerCode:     m.CustomerCode,
		ReferralSourceId: m.ReferralSourceID,
		CreateTime: &timestamppb.Timestamp{
			Seconds: m.CreatedTime.Unix(),
			Nanos:   int32(m.CreatedTime.Nanosecond()),
		},
		UpdateTime: &timestamppb.Timestamp{
			Seconds: m.UpdatedTime.Unix(),
			Nanos:   int32(m.UpdatedTime.Nanosecond()),
		},
	}

	if m.ConvertToCustomerID != nil {
		metadata.ConvertedCustomerId = m.ConvertToCustomerID
	}

	if m.DeletedTime != nil {
		metadata.DeleteTime = &timestamppb.Timestamp{
			Seconds: m.DeletedTime.Unix(),
			Nanos:   int32(m.DeletedTime.Nanosecond()),
		}
	}

	return metadata
}

func (m *Metadata) FromProto(pb *customerpb.Metadata) {
	if pb == nil {
		return
	}

	m.ID = pb.Id
	if pb.Organization != nil {
		m.OrganizationType = pb.Organization.Type
		m.OrganizationID = pb.Organization.Id
	}
	m.CustomerType = pb.CustomerType
	m.GivenName = pb.GivenName
	m.FamilyName = pb.FamilyName
	m.State = pb.State
	m.LifeCycleID = pb.LifecycleId
	m.OwnerStaffID = pb.OwnerStaffId
	m.ActionStateID = pb.ActionStateId
	m.AvatarPath = pb.AvatarPath
	m.CustomerCode = pb.CustomerCode
	m.ReferralSourceID = pb.ReferralSourceId
	m.CustomFields = pb.CustomFields

	if pb.ConvertedCustomerId != nil {
		m.ConvertToCustomerID = pb.ConvertedCustomerId
	}

	if pb.CreateTime != nil {
		m.CreatedTime = pb.CreateTime.AsTime()
	}
	if pb.UpdateTime != nil {
		m.UpdatedTime = pb.UpdateTime.AsTime()
	}
	if pb.DeleteTime != nil {
		deleteTime := pb.DeleteTime.AsTime()
		m.DeletedTime = &deleteTime
	}
}

// FromCustomer 和 FromLead 方法已移除，因为不再依赖 customerv2 和 leadlogic

func FromCustomerRepo(customer *customerrepo.Customer) *Metadata {
	var convertToCustomerID *int64
	if customer.ConvertToCustomerID != 0 {
		convertToCustomerID = &customer.ConvertToCustomerID
	}

	return &Metadata{
		ID:                  customer.ID,
		OrganizationType:    customer.OrganizationType,
		OrganizationID:      customer.OrganizationID,
		CustomerType:        customer.CustomerType,
		GivenName:           customer.GivenName,
		FamilyName:          customer.FamilyName,
		State:               customerpb.Metadata_State(customer.State),
		CustomFields:        customer.CustomFields,
		LifeCycleID:         customer.LifeCycleID,
		OwnerStaffID:        customer.OwnerStaffID,
		ActionStateID:       customer.ActionStateID,
		AvatarPath:          customer.AvatarPath,
		CustomerCode:        customer.CustomerCode,
		ReferralSourceID:    customer.ReferralSourceID,
		ConvertToCustomerID: convertToCustomerID,
		DeletedTime:         customer.DeletedTime,
		CreatedTime:         customer.CreatedTime,
		UpdatedTime:         customer.UpdatedTime,
	}
}

type ListMetadataRequest struct {
	Filter     *ListMetadataFilter     `json:"filter"`
	Pagination *ListMetadataPagination `json:"pagination"`
	OrderBy    *ListMetadataOrderBy    `json:"order_by"`
}

type ListMetadataFilter struct {
	IDs                  []int64                          `json:"ids"`
	OrganizationType     *customerpb.OrganizationRef_Type `json:"organization_type"`
	OrganizationID       int64                            `json:"organization_id"`
	CustomerType         customerpb.CustomerType          `json:"customer_type"`
	States               []customerpb.Metadata_State      `json:"states"`
	LifecycleIDs         []int64                          `json:"lifecycle_ids"`
	OwnerStaffIDs        []int64                          `json:"owner_staff_ids"`
	ReferralSourceIDs    []int64                          `json:"referral_source_ids"`
	ConvertedCustomerIDs []int64                          `json:"converted_customer_ids"`
	IsConverted          *bool                            `json:"is_converted"`
}

type ListMetadataPagination struct {
	PageSize        int32  `json:"page_size"`
	Cursor          string `json:"cursor"`
	ReturnTotalSize bool   `json:"return_total_size"`
}

type ListMetadataOrderBy struct {
	Field     customerpb.ListMetadataRequest_Sorting_Field `json:"field"`
	Direction customerpb.Direction                         `json:"direction"`
}

type ListMetadataResponse struct {
	Metadata  []*Metadata `json:"metadata"`
	HasNext   bool        `json:"has_next"`
	NextToken string      `json:"next_token"`
	TotalSize *int64      `json:"total_size"`
}

func (req *ListMetadataRequest) FromProto(pb *customerpb.ListMetadataRequest) {
	if pb == nil {
		return
	}

	if pb.Filter != nil {
		req.Filter = &ListMetadataFilter{
			IDs: pb.Filter.Ids,
		}

		if len(pb.Filter.CustomerTypes) > 0 {
			req.Filter.CustomerType = pb.Filter.CustomerTypes[0]
		}

		if len(pb.Filter.Organizations) > 0 && pb.Filter.Organizations[0] != nil {
			req.Filter.OrganizationType = &pb.Filter.Organizations[0].Type
			req.Filter.OrganizationID = pb.Filter.Organizations[0].Id
		}

		req.Filter.States = pb.Filter.States
		req.Filter.LifecycleIDs = pb.Filter.LifecycleIds
		req.Filter.OwnerStaffIDs = pb.Filter.OwnerStaffIds
		req.Filter.ReferralSourceIDs = pb.Filter.ReferralSourceIds
		req.Filter.ConvertedCustomerIDs = pb.Filter.ConvertedCustomerIds
		req.Filter.IsConverted = pb.Filter.IsConverted
	}

	req.Pagination = &ListMetadataPagination{
		PageSize:        pb.PageSize,
		Cursor:          pb.PageToken,
		ReturnTotalSize: false,
	}

	if pb.Sorting != nil {
		req.OrderBy = &ListMetadataOrderBy{
			Field:     pb.Sorting.Field,
			Direction: pb.Sorting.Direction,
		}
	}
}

// ToCustomerListRequest, ToLeadListRequest, ListMetadataResponseFromCustomers,
// ListMetadataResponseFromLeads 方法已移除，因为不再依赖 customerv2 和 leadlogic

type UpdateMetadataRequest struct {
	GivenName        string                    `json:"given_name"`
	FamilyName       string                    `json:"family_name"`
	CustomFields     *structpb.Struct          `json:"custom_fields"`
	LifeCycleID      int64                     `json:"life_cycle_id"`
	OwnerStaffID     int64                     `json:"owner_staff_id"`
	ActionStateID    int64                     `json:"action_state_id"`
	AvatarPath       string                    `json:"avatar_path"`
	ReferralSourceID int64                     `json:"referral_source_id"`
	State            customerpb.Metadata_State `json:"state"`
}

func (req *UpdateMetadataRequest) FromProto(pb *customerpb.UpdateMetadataRequest) {
	if pb == nil {
		return
	}

	if pb.GivenName != nil {
		req.GivenName = *pb.GivenName
	}
	if pb.FamilyName != nil {
		req.FamilyName = *pb.FamilyName
	}
	if pb.LifecycleId != nil {
		req.LifeCycleID = *pb.LifecycleId
	}
	if pb.OwnerStaffId != nil {
		req.OwnerStaffID = *pb.OwnerStaffId
	}
	if pb.ActionStateId != nil {
		req.ActionStateID = *pb.ActionStateId
	}
	if pb.AvatarPath != nil {
		req.AvatarPath = *pb.AvatarPath
	}
	if pb.ReferralSourceId != nil {
		req.ReferralSourceID = *pb.ReferralSourceId
	}
	if pb.State != nil {
		req.State = *pb.State
	}
	req.CustomFields = pb.CustomFields
}

// ToCustomerUpdateRequest 和 ToLeadUpdateRequest 方法已移除，因为不再依赖 customerv2 和 leadlogic

// ==================== 聚合相关实体定义 ====================

// AggregateRequest 聚合创建请求
type AggregateRequest struct {
	Metadata    *Metadata                                `json:"metadata"`
	Addresses   []*address.Address                       `json:"addresses"`
	Contacts    []*contact.Contact                       `json:"contacts"`
	RelatedData *customerrelateddata.CustomerRelatedData `json:"related_data"`
}

// AggregateResponse 聚合创建响应
type AggregateResponse struct {
	Metadata    *Metadata                                `json:"metadata"`
	Addresses   []*address.Address                       `json:"addresses"`
	Contacts    []*contact.Contact                       `json:"contacts"`
	RelatedData *customerrelateddata.CustomerRelatedData `json:"related_data"`
}

// ConvertMetadataAttributeDatum 转换元数据属性的数据结构
type ConvertMetadataAttributeDatum struct {
	MetadataIDs            []int64 `json:"metadata_ids"`
	CustomizeLifeCycleID   *int64  `json:"customize_life_cycle_id"`
	CustomizeActionStateID *int64  `json:"customize_action_state_id"`
}
