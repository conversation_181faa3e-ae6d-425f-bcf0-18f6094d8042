package historylog

import (
	"context"
	"time"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	customerhistorylog "github.com/MoeGolibrary/moego/backend/app/customer/repo/db/customer_history_log"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
)

type Logic struct {
	historyLogRepo customerhistorylog.ReadWriter
}

func New() *Logic {
	return &Logic{
		historyLogRepo: customerhistorylog.New(),
	}
}

func (l *Logic) Create(ctx context.Context, datum *CreateHistoryLogDatum) (int64, error) {
	// check
	if datum == nil || datum.CompanyID <= 0 || datum.BusinessID <= 0 ||
		datum.CustomerID <= 0 || datum.Action == nil {
		log.ErrorContextf(ctx, "Create params is invalid: %+v", datum)

		return 0, status.Error(codes.InvalidArgument, "params is invalid")
	}

	// conv db
	db, err := convHistoryLogDB(ctx, datum)
	if err != nil {
		return 0, err
	}

	// save to db
	if err := l.historyLogRepo.Create(ctx, db); err != nil {
		return 0, err
	}

	return db.ID, nil
}

func (l *Logic) Update(ctx context.Context, datum *UpdateHistoryLogDatum) error {
	// check
	if datum == nil || datum.LogID <= 0 || datum.StaffID <= 0 {
		log.ErrorContext(ctx, "ListCustomerHistoryLogs params is invalid, datum:%+v", datum)

		return status.Error(codes.InvalidArgument, "params is invalid")
	}

	// get history log
	historyLog, err := l.historyLogRepo.Get(ctx, datum.LogID)
	if err != nil {
		return err
	}

	// convUpdateDB
	historyLogDB, err := convUpdateHistoryLogDB(ctx, datum)
	if err != nil {
		return err
	}

	// only support update note
	if historyLogDB.Type != customerpb.HistoryLog_TYPE_UNSPECIFIED {
		if historyLogDB.Type != customerpb.HistoryLog_NOTE || historyLog.Type != customerpb.HistoryLog_NOTE {
			return status.Error(codes.InvalidArgument, "only support update note")
		}
	}

	// update DB
	if err := l.historyLogRepo.Update(ctx, historyLogDB); err != nil {
		return err
	}

	return nil
}

func (l *Logic) List(ctx context.Context, datum *ListHistoryLogsDatum) (
	[]*customerpb.HistoryLog, int32, error) {
	// check
	if datum == nil || (customerutils.ToValue(datum.CustomerID) == 0 && customerutils.ToValue(datum.CompanyID) == 0) {
		log.ErrorContext(ctx, "ListCustomerHistoryLogs params is invalid", zap.Any("datum", datum))

		return nil, 0, status.Error(codes.InvalidArgument, "params is invalid")
	}
	if datum.PageNum == 0 {
		datum.PageNum = 1
	}
	if datum.PageSize == 0 {
		datum.PageSize = 10
	}

	// select data form db
	logs, total, err := l.historyLogRepo.List(ctx, convListDB(datum))
	if err != nil {
		return nil, 0, err
	}

	return convHistoryLogsPB(logs), int32(total), nil
}

func convListDB(datum *ListHistoryLogsDatum) *customerhistorylog.ListHistoryLogsDatum {
	return &customerhistorylog.ListHistoryLogsDatum{
		CustomerID:     datum.CustomerID,
		HistoryLogType: datum.HistoryLogType,
		PageSize:       datum.PageSize,
		PageNum:        datum.PageNum,
		CompanyID:      datum.CompanyID,
	}
}

func convHistoryLogsPB(logs []*customerhistorylog.CustomerHistoryLog) []*customerpb.HistoryLog {
	res := make([]*customerpb.HistoryLog, 0, len(logs))
	for _, log := range logs {
		if log == nil {
			continue
		}
		res = append(res, convHistoryLogPB(log))
	}

	return res
}

func convHistoryLogPB(log *customerhistorylog.CustomerHistoryLog) *customerpb.HistoryLog {
	return &customerpb.HistoryLog{
		Id:                  log.ID,
		CustomerId:          log.CustomerID,
		CustomerName:        log.CustomerName,
		CustomerPhoneNumber: log.CustomerPhoneNumber,
		Type:                log.Type,
		Action:              log.Action,
		StaffId:             log.StaffID,
		CreateTime:          timestamppb.New(log.CreateTime),
		Source:              log.Source,
		SourceId:            customerutils.ToValue(log.SourceID),
		SourceName:          log.SourceName,
	}
}

func convHistoryLogDB(ctx context.Context, datum *CreateHistoryLogDatum) (
	*customerhistorylog.CustomerHistoryLog, error) {
	res := &customerhistorylog.CustomerHistoryLog{
		CompanyID:           datum.CompanyID,
		BusinessID:          datum.BusinessID,
		CustomerID:          datum.CustomerID,
		CustomerName:        datum.CustomerName,
		CustomerPhoneNumber: datum.CustomerPhoneNumber,
		StaffID:             datum.StaffID,
		Action:              datum.Action,
		SourceID:            datum.SourceID,
		SourceName:          customerutils.ToValue(datum.SourceName),
	}
	historyLogType, err := getActionType(ctx, datum.Action)
	if err != nil {
		return nil, err
	}
	res.Type = historyLogType

	// init source
	if datum.Source != nil {
		res.Source = *datum.Source
	} else {
		res.Source = customerpb.HistoryLog_SOURCE_UNSPECIFIED
	}

	return res, nil
}

func getActionType(ctx context.Context, action *customerpb.HistoryLog_Action) (
	customerpb.HistoryLog_Type, error) {
	switch action.Action.(type) {
	case *customerpb.HistoryLog_Action_Message:
		return customerpb.HistoryLog_MESSAGE, nil
	case *customerpb.HistoryLog_Action_Call:
		return customerpb.HistoryLog_CALL, nil
	case *customerpb.HistoryLog_Action_Note:
		return customerpb.HistoryLog_NOTE, nil
	case *customerpb.HistoryLog_Action_Task:
		return customerpb.HistoryLog_TASK, nil
	case *customerpb.HistoryLog_Action_Convert:
		return customerpb.HistoryLog_CONVERT, nil
	case *customerpb.HistoryLog_Action_Create:
		return customerpb.HistoryLog_CREATE, nil
	default:
		log.ErrorContextf(ctx, "getActionType action is unsupported, action:%+v", action)

		return customerpb.HistoryLog_TYPE_UNSPECIFIED, status.Error(codes.InvalidArgument, "action is unsupported")
	}
}

func convUpdateHistoryLogDB(ctx context.Context, datum *UpdateHistoryLogDatum) (
	*customerhistorylog.CustomerHistoryLog, error) {
	res := &customerhistorylog.CustomerHistoryLog{
		ID:         datum.LogID,
		StaffID:    datum.StaffID,
		UpdateTime: time.Now(),
	}
	if datum.Action != nil {
		res.Action = datum.Action
		historyLogType, err := getActionType(ctx, datum.Action)
		if err != nil {
			return nil, err
		}
		res.Type = historyLogType
	}

	return res, nil
}
