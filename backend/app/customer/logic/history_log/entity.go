package historylog

import (
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
)

type CreateHistoryLogDatum struct {
	CustomerID          int64
	BusinessID          int64
	CompanyID           int64
	StaffID             int64
	CustomerName        string
	CustomerPhoneNumber string

	Action     *customerpb.HistoryLog_Action
	Source     *customerpb.HistoryLog_Source
	SourceID   *int64
	SourceName *string
}

type ListHistoryLogsDatum struct {
	// filter
	CustomerID     *int64
	HistoryLogType *customerpb.HistoryLog_Type
	CompanyID      *int64

	// page
	PageSize int
	PageNum  int
}

type UpdateHistoryLogDatum struct {
	LogID   int64
	StaffID int64

	Action *customerpb.HistoryLog_Action
}
