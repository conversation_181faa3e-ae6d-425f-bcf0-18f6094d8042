package customerrelateddata

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	customerrelateddatarepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer_related_data"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

const (
	DefaultClientColor   = "#000000"
	DefaultPreferredDay  = "[0,1,2,3,4,5,6]"
	DefaultPreferredTime = "[0,1435]"
)

type CustomerRelatedData struct {
	ID                     int64                                `json:"id"`
	CustomerID             int64                                `json:"customer_id"`
	PreferredBusinessID    int64                                `json:"preferred_business_id"`
	CompanyID              int64                                `json:"company_id"`
	ClientColor            string                               `json:"client_color"`
	IsBlockMessage         int32                                `json:"is_block_message"`
	IsBlockOnlineBooking   int32                                `json:"is_block_online_booking"`
	LoginEmail             string                               `json:"login_email"`
	ReferralSourceID       int32                                `json:"referral_source_id"`
	ReferralSourceDesc     string                               `json:"referral_source_desc"`
	SendAutoEmail          int32                                `json:"send_auto_email"`
	SendAutoMessage        int32                                `json:"send_auto_message"`
	SendAppAutoMessage     int32                                `json:"send_app_auto_message"`
	UnconfirmedReminderBy  []int32                              `json:"unconfirmed_reminder_by"`
	PreferredGroomerID     int32                                `json:"preferred_groomer_id"`
	PreferredFrequencyDay  int32                                `json:"preferred_frequency_day"`
	PreferredFrequencyType int32                                `json:"preferred_frequency_type"`
	LastServiceTime        string                               `json:"last_service_time"`
	Source                 string                               `json:"source"`
	ExternalID             string                               `json:"external_id"`
	CreateBy               int32                                `json:"create_by"`
	UpdateBy               int32                                `json:"update_by"`
	IsRecurring            *int32                               `json:"is_recurring"`
	ShareApptStatus        int32                                `json:"share_appt_status"`
	ShareRangeType         int32                                `json:"share_range_type"`
	ShareRangeValue        int32                                `json:"share_range_value"`
	ShareApptJSON          *string                              `json:"share_appt_json"`
	PreferredDay           string                               `json:"preferred_day"`
	PreferredTime          string                               `json:"preferred_time"`
	AccountID              int64                                `json:"account_id"`
	CustomerCode           string                               `json:"customer_code"`
	IsUnsubscribed         bool                                 `json:"is_unsubscribed"`
	Birthday               *time.Time                           `json:"birthday"`
	ActionState            string                               `json:"action_state"`
	CustomizeLifeCycleID   int64                                `json:"customize_life_cycle_id"`
	CustomizeActionStateID int64                                `json:"customize_action_state_id"`
	State                  customerpb.CustomerRelatedData_State `json:"state"`
	DeletedTime            *time.Time                           `json:"deleted_time"`
	CreatedTime            time.Time                            `json:"created_time"`
	UpdatedTime            time.Time                            `json:"updated_time"`

	PreferredTipEnable     int32   `json:"preferred_tip_enable"`
	PreferredTipType       int32   `json:"preferred_tip_type"`
	PreferredTipAmount     float64 `json:"preferred_tip_amount"`
	PreferredTipPercentage int32   `json:"preferred_tip_percentage"`
}

func GetApptReminderListForDB(apptReminderByList []byte) *byte {
	var originByte byte
	for _, choice := range apptReminderByList {
		switch choice {
		case ApptReminderByMsg:
			originByte = originByte | ByMsg
		case ApptReminderByEmail:
			originByte = originByte | ByEmail
		case ApptReminderByCall:
			originByte = originByte | ByCall
		case ApptReminderByApp:
			originByte = originByte | ByApp
		default:
			// 在 Go 中我们可以使用 log 包或者返回 error，这里先忽略未知类型
		}
	}

	return &originByte
}

func (c *CustomerRelatedData) ToDB() *customerrelateddatarepo.CustomerRelatedData {
	return c.ToDBWithDefaults(true)
}

// ToDBForUpdate 转换为数据库实体，用于更新操作（不应用默认值）
func (c *CustomerRelatedData) ToDBForUpdate() *customerrelateddatarepo.CustomerRelatedData {
	return c.ToDBWithDefaults(false)
}

// ToDBUpdateWithPointers 转换为指针更新结构，支持零值更新
func (c *CustomerRelatedData) ToDBUpdateWithPointers(
	updateRef *UpdateCustomerRelatedDataRequest,
) *customerrelateddatarepo.Update {
	converter := customerutils.BuildCommunicationPreferenceParam(c.UnconfirmedReminderBy)
	unconfirmedReminderBy := converter.UpdateUnconfirmedReminderBy(nil)

	updateData := &customerrelateddatarepo.Update{
		ID: c.ID,
	}

	// 只设置需要更新的字段
	if updateRef.ClientColor != nil {
		updateData.ClientColor = updateRef.ClientColor
	}
	if updateRef.IsBlockMessage != nil {
		updateData.IsBlockMessage = updateRef.IsBlockMessage
	}
	if updateRef.IsBlockOnlineBooking != nil {
		updateData.IsBlockOnlineBooking = updateRef.IsBlockOnlineBooking
	}
	if updateRef.LoginEmail != nil {
		updateData.LoginEmail = updateRef.LoginEmail
	}
	if updateRef.ReferralSourceID != nil {
		updateData.ReferralSourceID = updateRef.ReferralSourceID
	}
	if updateRef.ReferralSourceDesc != nil {
		updateData.ReferralSourceDesc = updateRef.ReferralSourceDesc
	}
	if updateRef.SendAutoEmail != nil {
		updateData.SendAutoEmail = updateRef.SendAutoEmail
	}
	if updateRef.SendAutoMessage != nil {
		updateData.SendAutoMessage = updateRef.SendAutoMessage
	}
	if updateRef.SendAppAutoMessage != nil {
		updateData.SendAppAutoMessage = updateRef.SendAppAutoMessage
	}
	if updateRef.PreferredGroomerID != nil {
		updateData.PreferredGroomerID = updateRef.PreferredGroomerID
	}
	if updateRef.PreferredFrequencyDay != nil {
		updateData.PreferredFrequencyDay = updateRef.PreferredFrequencyDay
	}
	if updateRef.PreferredFrequencyType != nil {
		updateData.PreferredFrequencyType = updateRef.PreferredFrequencyType
	}
	if updateRef.PreferredDay != nil {
		updateData.PreferredDay = updateRef.PreferredDay
	}
	if updateRef.PreferredTime != nil {
		updateData.PreferredTime = updateRef.PreferredTime
	}
	if updateRef.IsUnsubscribed != nil {
		updateData.IsUnsubscribed = updateRef.IsUnsubscribed
	}
	if updateRef.Birthday != nil {
		updateData.Birthday = updateRef.Birthday
	}
	if updateRef.CustomizeLifeCycleID != nil {
		updateData.CustomizeLifeCycleID = updateRef.CustomizeLifeCycleID
	}
	if updateRef.CustomizeActionStateID != nil {
		updateData.CustomizeActionStateID = updateRef.CustomizeActionStateID
	}
	if updateRef.PreferredBusinessID != nil {
		updateData.BusinessID = updateRef.PreferredBusinessID
	}
	if updateRef.UnconfirmedReminderBy != nil {
		int8Val := int8(unconfirmedReminderBy)
		updateData.UnconfirmedReminderBy = &int8Val
	}

	// 总是更新时间
	now := time.Now().UTC()
	updateData.UpdatedTime = &now

	return updateData
}

// ToDBWithDefaults 转换为数据库实体，可选择是否应用默认值
func (c *CustomerRelatedData) ToDBWithDefaults(applyDefaults bool) *customerrelateddatarepo.CustomerRelatedData {
	converter := customerutils.BuildCommunicationPreferenceParam(c.UnconfirmedReminderBy)
	unconfirmedReminderBy := converter.UpdateUnconfirmedReminderBy(nil)
	data := &customerrelateddatarepo.CustomerRelatedData{
		ID:                     c.ID,
		CustomerID:             c.CustomerID,
		BusinessID:             c.PreferredBusinessID,
		CompanyID:              c.CompanyID,
		ClientColor:            c.ClientColor,
		IsBlockMessage:         c.IsBlockMessage,
		IsBlockOnlineBooking:   c.IsBlockOnlineBooking,
		LoginEmail:             c.LoginEmail,
		ReferralSourceID:       c.ReferralSourceID,
		ReferralSourceDesc:     c.ReferralSourceDesc,
		SendAutoEmail:          c.SendAutoEmail,
		SendAutoMessage:        c.SendAutoMessage,
		SendAppAutoMessage:     c.SendAppAutoMessage,
		UnconfirmedReminderBy:  unconfirmedReminderBy,
		PreferredGroomerID:     c.PreferredGroomerID,
		PreferredFrequencyDay:  c.PreferredFrequencyDay,
		PreferredFrequencyType: c.PreferredFrequencyType,
		LastServiceTime:        c.LastServiceTime,
		Source:                 c.Source,
		ExternalID:             c.ExternalID,
		CreateBy:               c.CreateBy,
		UpdateBy:               c.UpdateBy,
		IsRecurring:            c.IsRecurring,
		ShareApptStatus:        c.ShareApptStatus,
		ShareRangeType:         c.ShareRangeType,
		ShareRangeValue:        c.ShareRangeValue,
		ShareApptJSON:          c.ShareApptJSON,
		PreferredDay:           c.PreferredDay,
		PreferredTime:          c.PreferredTime,
		AccountID:              c.AccountID,
		CustomerCode:           c.CustomerCode,
		IsUnsubscribed:         c.IsUnsubscribed,
		Birthday:               c.Birthday,
		ActionState:            c.ActionState,
		CustomizeLifeCycleID:   c.CustomizeLifeCycleID,
		CustomizeActionStateID: c.CustomizeActionStateID,
		State:                  c.State,
		DeletedTime:            c.DeletedTime,
		CreatedTime:            c.CreatedTime,
		UpdatedTime:            c.UpdatedTime,
	}

	// 只在创建时应用默认值，更新时不应用
	if applyDefaults {
		if c.ClientColor == "" {
			data.ClientColor = DefaultClientColor
		}
		if c.SendAutoMessage == 0 {
			data.SendAutoMessage = 1
		}
		if c.SendAppAutoMessage == 0 {
			data.SendAppAutoMessage = 1
		}
		if unconfirmedReminderBy == 0 {
			data.UnconfirmedReminderBy = -127
		}
		if c.PreferredFrequencyDay == 0 {
			data.PreferredFrequencyDay = 28
		}
		if c.PreferredFrequencyType == 0 {
			data.PreferredFrequencyType = 1
		}
		if c.PreferredDay == "" {
			data.PreferredDay = DefaultPreferredDay
		}
		if c.PreferredTime == "" {
			data.PreferredTime = DefaultPreferredTime
		}
	}

	return data
}

func (c *CustomerRelatedData) ToPB() *customerpb.CustomerRelatedData {
	if c == nil {
		return nil
	}

	unconfirmedReminderBy := c.UnconfirmedReminderBy

	result := &customerpb.CustomerRelatedData{
		Id:                     c.ID,
		CustomerId:             c.CustomerID,
		PreferredBusinessId:    c.PreferredBusinessID,
		CompanyId:              c.CompanyID,
		ClientColor:            c.ClientColor,
		IsBlockMessage:         c.IsBlockMessage,
		IsBlockOnlineBooking:   c.IsBlockOnlineBooking,
		LoginEmail:             c.LoginEmail,
		ReferralSourceId:       c.ReferralSourceID,
		ReferralSourceDesc:     c.ReferralSourceDesc,
		SendAutoEmail:          c.SendAutoEmail,
		SendAutoMessage:        c.SendAutoMessage,
		SendAppAutoMessage:     c.SendAppAutoMessage,
		UnconfirmedReminderBy:  unconfirmedReminderBy,
		PreferredGroomerId:     c.PreferredGroomerID,
		PreferredFrequencyDay:  c.PreferredFrequencyDay,
		PreferredFrequencyType: c.PreferredFrequencyType,
		LastServiceTime:        c.LastServiceTime,
		Source:                 c.Source,
		ExternalId:             c.ExternalID,
		CreateBy:               c.CreateBy,
		UpdateBy:               c.UpdateBy,
		ShareApptStatus:        c.ShareApptStatus,
		ShareRangeType:         c.ShareRangeType,
		ShareRangeValue:        c.ShareRangeValue,
		PreferredDay:           c.PreferredDay,
		PreferredTime:          c.PreferredTime,
		AccountId:              c.AccountID,
		CustomerCode:           c.CustomerCode,
		IsUnsubscribed:         c.IsUnsubscribed,
		ActionState:            c.ActionState,
		CustomizeLifeCycleId:   c.CustomizeLifeCycleID,
		CustomizeActionStateId: c.CustomizeActionStateID,
		State:                  c.State,
		CreateTime: &timestamppb.Timestamp{
			Seconds: c.CreatedTime.Unix(),
			Nanos:   int32(c.CreatedTime.Nanosecond()),
		},
		UpdateTime: &timestamppb.Timestamp{
			Seconds: c.UpdatedTime.Unix(),
			Nanos:   int32(c.UpdatedTime.Nanosecond()),
		},
	}

	if c.IsRecurring != nil {
		result.IsRecurring = c.IsRecurring
	}

	if c.ShareApptJSON != nil {
		result.ShareApptJson = c.ShareApptJSON
	}

	if c.Birthday != nil && !c.Birthday.IsZero() && c.Birthday.Unix() != 0 {
		result.Birthday = &timestamppb.Timestamp{
			Seconds: c.Birthday.Unix(),
			Nanos:   int32(c.Birthday.Nanosecond()),
		}
	}

	if c.DeletedTime != nil {
		result.DeleteTime = &timestamppb.Timestamp{
			Seconds: c.DeletedTime.Unix(),
			Nanos:   int32(c.DeletedTime.Nanosecond()),
		}
	}

	result.PreferredTipEnable = c.PreferredTipEnable
	result.PreferredTipType = c.PreferredTipType
	result.PreferredTipAmount = c.PreferredTipAmount
	result.PreferredTipPercentage = c.PreferredTipPercentage

	return result
}

// ************ ListCustomerRelatedData ************
type ListCustomerRelatedDataRequest struct {
	Filter     *ListCustomerRelatedDataFilter     `json:"filter"`
	Pagination *ListCustomerRelatedDataPagination `json:"pagination"`
	OrderBy    *ListCustomerRelatedDataOrderBy    `json:"order_by"`
}

type ListCustomerRelatedDataPagination struct {
	PageSize        int32  `json:"page_size"`
	Cursor          string `json:"cursor"`
	ReturnTotalSize bool   `json:"return_total_size"`
}

type ListCustomerRelatedDataOrderBy struct {
	Field     customerpb.ListCustomerRelatedDataRequest_Sorting_Field `json:"field"`
	Direction customerpb.Direction                                    `json:"direction"`
}

type ListCustomerRelatedDataFilter struct {
	IDs           []int64                                `json:"ids"`
	CustomerIDs   []int64                                `json:"customer_ids"`
	BusinessIDs   []int32                                `json:"business_ids"`
	CompanyIDs    []int64                                `json:"company_ids"`
	States        []customerpb.CustomerRelatedData_State `json:"states"`
	CustomerCodes []string
	AccountIDs    []int64

	Sources              []string
	IsLapsed             bool
	IsBlockMessage       []int32
	IsBlockOnlineBooking []int32
	LastServiceTimeGap   int32
	PreferredGroomerIDs  []int64
}

type ListCustomerRelatedDataResponse struct {
	CustomerRelatedData []*CustomerRelatedData `json:"customer_related_data"`
	HasNext             bool                   `json:"has_next"`
	NextToken           string                 `json:"next_token"`
	TotalSize           *int64                 `json:"total_size"`
}

func (c *ListCustomerRelatedDataResponse) GetCustomerRelatedData() []*CustomerRelatedData {
	if c != nil {
		return c.CustomerRelatedData
	}

	return nil
}

// ************ UpdateCustomerRelatedData ************
type UpdateCustomerRelatedDataRequest struct {
	ID                     int64      `json:"id"`
	ClientColor            *string    `json:"client_color"`
	IsBlockMessage         *int32     `json:"is_block_message"`
	IsBlockOnlineBooking   *int32     `json:"is_block_online_booking"`
	LoginEmail             *string    `json:"login_email"`
	ReferralSourceID       *int32     `json:"referral_source_id"`
	ReferralSourceDesc     *string    `json:"referral_source_desc"`
	SendAutoEmail          *int32     `json:"send_auto_email"`
	SendAutoMessage        *int32     `json:"send_auto_message"`
	SendAppAutoMessage     *int32     `json:"send_app_auto_message"`
	PreferredGroomerID     *int32     `json:"preferred_groomer_id"`
	PreferredFrequencyDay  *int32     `json:"preferred_frequency_day"`
	PreferredFrequencyType *int32     `json:"preferred_frequency_type"`
	PreferredDay           *string    `json:"preferred_day"`
	PreferredTime          *string    `json:"preferred_time"`
	IsUnsubscribed         *bool      `json:"is_unsubscribed"`
	Birthday               *time.Time `json:"birthday"`
	CustomizeLifeCycleID   *int64     `json:"customize_life_cycle_id"`
	CustomizeActionStateID *int64     `json:"customize_action_state_id"`
	PreferredBusinessID    *int64     `json:"preferred_business_id"`
	UnconfirmedReminderBy  []int32    `json:"unconfirmed_reminder_by"`

	PreferredTipEnable     *int32   `json:"preferred_tip_enable"`
	PreferredTipType       *int32   `json:"preferred_tip_type"`
	PreferredTipAmount     *float64 `json:"preferred_tip_amount"`
	PreferredTipPercentage *int32   `json:"preferred_tip_percentage"`
}

func convertTimestampToTime(ts *timestamppb.Timestamp) *time.Time {
	if ts == nil {
		return nil
	}
	t := ts.AsTime()

	return &t
}

// 辅助函数：将值转换为指针
func StringPtr(s string) *string {
	return &s
}

func Int32Ptr(i int32) *int32 {
	return &i
}

func Int64Ptr(i int64) *int64 {
	return &i
}

func BoolPtr(b bool) *bool {
	return &b
}

func Float64Ptr(f float64) *float64 {
	return &f
}

// 辅助函数：安全地获取指针的值，如果指针为nil则返回零值
func SafeStringValue(ptr *string) string {
	if ptr == nil {
		return ""
	}

	return *ptr
}

func SafeInt32Value(ptr *int32) int32 {
	if ptr == nil {
		return 0
	}

	return *ptr
}

func SafeInt64Value(ptr *int64) int64 {
	if ptr == nil {
		return 0
	}

	return *ptr
}

func SafeBoolValue(ptr *bool) bool {
	if ptr == nil {
		return false
	}

	return *ptr
}

func SafeFloat64Value(ptr *float64) float64 {
	if ptr == nil {
		return 0.0
	}

	return *ptr
}

func (c *CustomerRelatedData) FromProto(pb *customerpb.CustomerRelatedData) {
	if pb == nil {
		return
	}
	c.ID = pb.Id
	c.CustomerID = pb.CustomerId
	c.PreferredBusinessID = pb.PreferredBusinessId
	c.CompanyID = pb.CompanyId
	c.ClientColor = pb.ClientColor
	c.IsBlockMessage = pb.IsBlockMessage
	c.IsBlockOnlineBooking = pb.IsBlockOnlineBooking
	c.LoginEmail = pb.LoginEmail
	c.ReferralSourceID = pb.ReferralSourceId
	c.ReferralSourceDesc = pb.ReferralSourceDesc
	c.SendAutoEmail = pb.SendAutoEmail
	c.SendAutoMessage = pb.SendAutoMessage
	c.SendAppAutoMessage = pb.SendAppAutoMessage
	c.UnconfirmedReminderBy = make([]int32, len(pb.UnconfirmedReminderBy))
	copy(c.UnconfirmedReminderBy, pb.UnconfirmedReminderBy)
	c.PreferredGroomerID = pb.PreferredGroomerId
	c.PreferredFrequencyDay = pb.PreferredFrequencyDay
	c.PreferredFrequencyType = pb.PreferredFrequencyType
	c.LastServiceTime = pb.LastServiceTime
	c.Source = pb.Source
	c.ExternalID = pb.ExternalId
	c.CreateBy = pb.CreateBy
	c.UpdateBy = pb.UpdateBy
	c.ShareApptStatus = pb.ShareApptStatus
	c.ShareRangeType = pb.ShareRangeType
	c.ShareRangeValue = pb.ShareRangeValue
	c.PreferredDay = pb.PreferredDay
	c.PreferredTime = pb.PreferredTime
	c.AccountID = pb.AccountId
	c.CustomerCode = pb.CustomerCode
	c.IsUnsubscribed = pb.IsUnsubscribed
	c.ActionState = pb.ActionState
	c.CustomizeLifeCycleID = pb.CustomizeLifeCycleId
	c.CustomizeActionStateID = pb.CustomizeActionStateId
	c.Birthday = convertTimestampToTime(pb.Birthday)

	if pb.IsRecurring != nil {
		c.IsRecurring = pb.IsRecurring
	}

	if pb.ShareApptJson != nil {
		c.ShareApptJSON = pb.ShareApptJson
	}

	c.PreferredTipEnable = pb.PreferredTipEnable
	c.PreferredTipType = pb.PreferredTipType
	c.PreferredTipAmount = pb.PreferredTipAmount
	c.PreferredTipPercentage = pb.PreferredTipPercentage
}
