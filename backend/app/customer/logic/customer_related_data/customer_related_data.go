package customerrelateddata

import (
	"context"
	"errors"
	"time"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	customerrelateddatarepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer_related_data"
	defaultpreferredfrequencyrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/default_preferred_frequency"
	preferredtipconfigrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/preferred_tip_config"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

// ApptReminderBy 常量定义 - 对应 Java 中的位运算常量和枚举值
const (
	// 位运算常量 - 用于数据库存储
	ByApp   byte = 0x88 // 1000 1000
	ByCall  byte = 0x84 // 1000 0100
	ByEmail byte = 0x82 // 1000 0010
	ByMsg   byte = 0x81 // 1000 0001

	// 枚举值常量 - 对应 Java 中的 ApptReminderBy 枚举
	ApptReminderByMsg   byte = 1
	ApptReminderByEmail byte = 2
	ApptReminderByCall  byte = 3
	ApptReminderByApp   byte = 4
)

type Logic struct {
	customerRelatedDataRepo  customerrelateddatarepo.Repository
	preferredTipConfigRepo   preferredtipconfigrepo.Repository
	defaultPreferredFreqRepo defaultpreferredfrequencyrepo.Repository
}

func New() *Logic {
	return &Logic{
		customerRelatedDataRepo:  customerrelateddatarepo.New(),
		preferredTipConfigRepo:   preferredtipconfigrepo.New(),
		defaultPreferredFreqRepo: defaultpreferredfrequencyrepo.New(),
	}
}

func NewByParams(
	customerRelatedDataRepo customerrelateddatarepo.Repository,
	preferredTipConfigRepo preferredtipconfigrepo.Repository,
	defaultPreferredFreqRepo defaultpreferredfrequencyrepo.Repository,
) *Logic {
	return &Logic{
		customerRelatedDataRepo:  customerRelatedDataRepo,
		preferredTipConfigRepo:   preferredTipConfigRepo,
		defaultPreferredFreqRepo: defaultPreferredFreqRepo,
	}
}

func (l *Logic) WithTx(tx *gorm.DB) *Logic {
	return &Logic{
		customerRelatedDataRepo: l.customerRelatedDataRepo.WithTx(tx),
	}
}

func (l *Logic) Create(
	ctx context.Context, data *CustomerRelatedData,
) (*CustomerRelatedData, error) {
	// create customer related data, set output only columns
	now := time.Now().UTC()
	data.CreatedTime = now
	data.UpdatedTime = now
	data.State = customerpb.CustomerRelatedData_ACTIVE

	dbData := data.ToDB()

	dbData, err := l.customerRelatedDataRepo.Create(ctx, dbData)
	if err != nil {
		return nil, errs.New(customerpb.ErrCode_ERR_CODE_CREATE_CUSTOMER_FAILED)
	}

	result := convertToCustomerRelatedData(dbData)

	if data.PreferredTipEnable != 0 || data.PreferredTipType != 0 ||
		data.PreferredTipAmount != 0 || data.PreferredTipPercentage != 0 {
		tipConfig := &preferredtipconfigrepo.PreferredTipConfig{
			CustomerID: data.CustomerID,
			BusinessID: data.PreferredBusinessID,
			Enable:     data.PreferredTipEnable,
			TipType:    data.PreferredTipType,
			Amount:     data.PreferredTipAmount,
			Percentage: data.PreferredTipPercentage,
			CompanyID:  data.CompanyID,
		}

		_, err = l.preferredTipConfigRepo.Create(ctx, tipConfig)
		if err != nil {
			return nil, err
		}

		result.PreferredTipEnable = data.PreferredTipEnable
		result.PreferredTipType = data.PreferredTipType
		result.PreferredTipAmount = data.PreferredTipAmount
		result.PreferredTipPercentage = data.PreferredTipPercentage
	}

	if data.DefaultPreferredFrequencyType != 0 || data.DefaultPreferredCalendarPeriod != 0 ||
		data.DefaultPreferredFrequencyValue != 0 {
		defaultFreq := &defaultpreferredfrequencyrepo.DefaultPreferredFrequency{
			CompanyID:      data.CompanyID,
			BusinessID:     data.PreferredBusinessID,
			FrequencyType:  data.DefaultPreferredFrequencyType,
			CalendarPeriod: data.DefaultPreferredCalendarPeriod,
			Value:          data.DefaultPreferredFrequencyValue,
		}

		_, err = l.defaultPreferredFreqRepo.Create(ctx, defaultFreq)
		if err != nil {
			return nil, err
		}

		result.DefaultPreferredFrequencyType = data.DefaultPreferredFrequencyType
		result.DefaultPreferredCalendarPeriod = data.DefaultPreferredCalendarPeriod
		result.DefaultPreferredFrequencyValue = data.DefaultPreferredFrequencyValue
	}

	return result, nil
}

func (l *Logic) Get(ctx context.Context, customerID int64) (*CustomerRelatedData, error) {
	dbData, err := l.customerRelatedDataRepo.GetByCustomerID(ctx, customerID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errs.New(customerpb.ErrCode_ERR_CODE_CUSTOMER_RELATED_DATA_NOT_FOUND)
		}

		return nil, err
	}

	result := convertToCustomerRelatedData(dbData)

	tipConfig, err := l.preferredTipConfigRepo.GetByCustomerID(ctx, customerID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	if tipConfig != nil {
		result.PreferredTipEnable = tipConfig.Enable
		result.PreferredTipType = tipConfig.TipType
		result.PreferredTipAmount = tipConfig.Amount
		result.PreferredTipPercentage = tipConfig.Percentage
	}

	defaultFreq, err := l.defaultPreferredFreqRepo.GetByCompanyID(ctx, result.CompanyID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	if defaultFreq != nil {
		result.DefaultPreferredFrequencyType = defaultFreq.FrequencyType
		result.DefaultPreferredCalendarPeriod = defaultFreq.CalendarPeriod
		result.DefaultPreferredFrequencyValue = defaultFreq.Value
	}

	return result, nil
}

func (l *Logic) List(
	ctx context.Context, req *ListCustomerRelatedDataRequest,
) (*ListCustomerRelatedDataResponse, error) {
	// 构建过滤器
	filter := &customerrelateddatarepo.ListFilter{
		IDs:           req.Filter.IDs,
		CustomerIDs:   req.Filter.CustomerIDs,
		BusinessIDs:   req.Filter.BusinessIDs,
		CompanyIDs:    req.Filter.CompanyIDs,
		States:        req.Filter.States,
		CustomerCodes: req.Filter.CustomerCodes,
		AccountIDs:    req.Filter.AccountIDs,
	}

	// 构建分页
	pagination := &customerrelateddatarepo.Pagination{
		PageSize:        req.Pagination.PageSize,
		ReturnTotalSize: req.Pagination.ReturnTotalSize,
	}

	// 解析游标
	if req.Pagination.Cursor != "" {
		pagination.Cursor = customerutils.DecodeCursor(req.Pagination.Cursor)
	}

	// 构建排序
	var orderBy *customerrelateddatarepo.OrderBy
	if req.OrderBy != nil {
		orderBy = &customerrelateddatarepo.OrderBy{
			Field:     req.OrderBy.Field,
			Direction: req.OrderBy.Direction,
		}
	}

	// 查询数据
	result, err := l.customerRelatedDataRepo.ListByCursor(ctx, filter, pagination, orderBy)
	if err != nil {
		return nil, err
	}

	// 转换数据
	dataList := make([]*CustomerRelatedData, 0, len(result.Data))
	for _, data := range result.Data {
		dataList = append(dataList, convertToCustomerRelatedData(data))
	}

	// 生成下一页游标
	var nextToken string
	if result.HasNext && len(result.Data) > 0 {
		lastItem := result.Data[len(result.Data)-1]
		cursor := &postgres.Cursor{ID: lastItem.ID, CreatedAt: lastItem.CreatedTime}
		nextToken = cursor.EncodeCursor()
	}

	response := &ListCustomerRelatedDataResponse{
		CustomerRelatedData: dataList,
		HasNext:             result.HasNext,
		NextToken:           nextToken,
		TotalSize:           result.TotalCount,
	}

	return response, nil
}

func (l *Logic) Update(
	ctx context.Context, id int64, updateRef *UpdateCustomerRelatedDataRequest,
) (*CustomerRelatedData, error) {
	// check customer related data exists
	dbData, err := l.customerRelatedDataRepo.GetByCustomerID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errs.New(customerpb.ErrCode_ERR_CODE_CUSTOMER_RELATED_DATA_NOT_FOUND)
		}

		return nil, err
	}

	data := convertToCustomerRelatedData(dbData)

	// Load tip config and default frequency data
	tipConfig, err := l.preferredTipConfigRepo.GetByCustomerID(ctx, data.CustomerID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	if tipConfig != nil {
		data.PreferredTipEnable = tipConfig.Enable
		data.PreferredTipType = tipConfig.TipType
		data.PreferredTipAmount = tipConfig.Amount
		data.PreferredTipPercentage = tipConfig.Percentage
	}

	defaultFreq, err := l.defaultPreferredFreqRepo.GetByCompanyID(ctx, data.CompanyID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	if defaultFreq != nil {
		data.DefaultPreferredFrequencyType = defaultFreq.FrequencyType
		data.DefaultPreferredCalendarPeriod = defaultFreq.CalendarPeriod
		data.DefaultPreferredFrequencyValue = defaultFreq.Value
	}

	l.updateBasicFields(data, updateRef)

	updatedData, err := l.customerRelatedDataRepo.Update(ctx, data.ToDB())
	if err != nil {
		return nil, err
	}

	result := convertToCustomerRelatedData(updatedData)

	err = l.handlePreferredTipConfig(ctx, data, updateRef, result)
	if err != nil {
		return nil, err
	}

	err = l.handleDefaultPreferredFrequency(ctx, data, updateRef, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (l *Logic) updateBasicFields(data *CustomerRelatedData, updateRef *UpdateCustomerRelatedDataRequest) {
	now := time.Now().UTC()
	data.UpdatedTime = now
	data.ClientColor = updateRef.ClientColor
	data.IsBlockMessage = updateRef.IsBlockMessage
	data.IsBlockOnlineBooking = updateRef.IsBlockOnlineBooking
	data.LoginEmail = updateRef.LoginEmail
	data.ReferralSourceID = updateRef.ReferralSourceID
	data.ReferralSourceDesc = updateRef.ReferralSourceDesc
	data.SendAutoEmail = updateRef.SendAutoEmail
	data.SendAutoMessage = updateRef.SendAutoMessage
	data.SendAppAutoMessage = updateRef.SendAppAutoMessage
	data.PreferredGroomerID = updateRef.PreferredGroomerID
	data.PreferredFrequencyDay = updateRef.PreferredFrequencyDay
	data.PreferredFrequencyType = updateRef.PreferredFrequencyType
	data.PreferredDay = updateRef.PreferredDay
	data.PreferredTime = updateRef.PreferredTime
	data.IsUnsubscribed = updateRef.IsUnsubscribed
	data.Birthday = updateRef.Birthday
	data.CustomizeLifeCycleID = updateRef.CustomizeLifeCycleID
	data.CustomizeActionStateID = updateRef.CustomizeActionStateID
	data.UnconfirmedReminderBy = updateRef.UnconfirmedReminderBy
	data.PreferredBusinessID = updateRef.PreferredBusinessID
	data.PreferredTipEnable = updateRef.PreferredTipEnable
	data.PreferredTipType = updateRef.PreferredTipType
	data.PreferredTipAmount = updateRef.PreferredTipAmount
	data.PreferredTipPercentage = updateRef.PreferredTipPercentage
	data.DefaultPreferredFrequencyType = updateRef.DefaultPreferredFrequencyType
	data.DefaultPreferredCalendarPeriod = updateRef.DefaultPreferredCalendarPeriod
	data.DefaultPreferredFrequencyValue = updateRef.DefaultPreferredFrequencyValue
}

func (l *Logic) handlePreferredTipConfig(
	ctx context.Context,
	data *CustomerRelatedData,
	updateRef *UpdateCustomerRelatedDataRequest,
	result *CustomerRelatedData,
) error {
	if updateRef.PreferredTipEnable != 0 || updateRef.PreferredTipType != 0 ||
		updateRef.PreferredTipAmount != 0 || updateRef.PreferredTipPercentage != 0 {

		tipConfig, err := l.preferredTipConfigRepo.GetByCustomerID(ctx, data.CustomerID)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}

		if tipConfig == nil {
			tipConfig = &preferredtipconfigrepo.PreferredTipConfig{
				CustomerID: data.CustomerID,
				BusinessID: data.PreferredBusinessID,
				Enable:     updateRef.PreferredTipEnable,
				TipType:    updateRef.PreferredTipType,
				Amount:     updateRef.PreferredTipAmount,
				Percentage: updateRef.PreferredTipPercentage,
				CompanyID:  data.CompanyID,
			}
			_, err = l.preferredTipConfigRepo.Create(ctx, tipConfig)
		} else {
			tipConfig.Enable = updateRef.PreferredTipEnable
			tipConfig.TipType = updateRef.PreferredTipType
			tipConfig.Amount = updateRef.PreferredTipAmount
			tipConfig.Percentage = updateRef.PreferredTipPercentage
			_, err = l.preferredTipConfigRepo.Update(ctx, tipConfig)
		}
		if err != nil {
			return err
		}

		result.PreferredTipEnable = tipConfig.Enable
		result.PreferredTipType = tipConfig.TipType
		result.PreferredTipAmount = tipConfig.Amount
		result.PreferredTipPercentage = tipConfig.Percentage
	} else {
		tipConfig, err := l.preferredTipConfigRepo.GetByCustomerID(ctx, data.CustomerID)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}

		if tipConfig != nil {
			result.PreferredTipEnable = tipConfig.Enable
			result.PreferredTipType = tipConfig.TipType
			result.PreferredTipAmount = tipConfig.Amount
			result.PreferredTipPercentage = tipConfig.Percentage
		}
	}

	return nil
}

func (l *Logic) handleDefaultPreferredFrequency(
	ctx context.Context,
	data *CustomerRelatedData,
	updateRef *UpdateCustomerRelatedDataRequest,
	result *CustomerRelatedData,
) error {
	if updateRef.DefaultPreferredFrequencyType != 0 || updateRef.DefaultPreferredCalendarPeriod != 0 ||
		updateRef.DefaultPreferredFrequencyValue != 0 {

		defaultFreq, err := l.defaultPreferredFreqRepo.GetByCompanyID(ctx, data.CompanyID)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}

		if defaultFreq == nil {
			defaultFreq = &defaultpreferredfrequencyrepo.DefaultPreferredFrequency{
				CompanyID:      data.CompanyID,
				BusinessID:     data.PreferredBusinessID,
				FrequencyType:  updateRef.DefaultPreferredFrequencyType,
				CalendarPeriod: updateRef.DefaultPreferredCalendarPeriod,
				Value:          updateRef.DefaultPreferredFrequencyValue,
			}
			_, err = l.defaultPreferredFreqRepo.Create(ctx, defaultFreq)
		} else {
			defaultFreq.FrequencyType = updateRef.DefaultPreferredFrequencyType
			defaultFreq.CalendarPeriod = updateRef.DefaultPreferredCalendarPeriod
			defaultFreq.Value = updateRef.DefaultPreferredFrequencyValue
			_, err = l.defaultPreferredFreqRepo.Update(ctx, defaultFreq)
		}
		if err != nil {
			return err
		}

		result.DefaultPreferredFrequencyType = defaultFreq.FrequencyType
		result.DefaultPreferredCalendarPeriod = defaultFreq.CalendarPeriod
		result.DefaultPreferredFrequencyValue = defaultFreq.Value
	} else {
		defaultFreq, err := l.defaultPreferredFreqRepo.GetByCompanyID(ctx, data.CompanyID)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}

		if defaultFreq != nil {
			result.DefaultPreferredFrequencyType = defaultFreq.FrequencyType
			result.DefaultPreferredCalendarPeriod = defaultFreq.CalendarPeriod
			result.DefaultPreferredFrequencyValue = defaultFreq.Value
		}
	}

	return nil
}

func (l *Logic) Delete(ctx context.Context, id int64) error {
	dbData, err := l.Get(ctx, id)
	if err != nil {
		return err
	}

	now := time.Now().UTC()
	dbData.UpdatedTime = now
	dbData.DeletedTime = &now
	dbData.State = customerpb.CustomerRelatedData_DELETED

	_, err = l.customerRelatedDataRepo.Update(ctx, dbData.ToDB())
	if err != nil {
		return err
	}

	return nil
}

// GetApptReminderListFromDB 根据查询出的数据库原始值进行转换，构造多选数组
func GetApptReminderListFromDB(unconfirmedReminderBy *byte) []byte {
	var choices []byte

	if unconfirmedReminderBy == nil || *unconfirmedReminderBy == 0 {
		return choices
	}

	// 检查是否为负数（在 Go 中，byte 是无符号的，所以我们检查是否大于 127，即最高位为 1）
	if *unconfirmedReminderBy >= 128 {
		// 新值转换 - 使用位运算检查每个标志位
		originByte := *unconfirmedReminderBy
		if (originByte & ByMsg) == ByMsg {
			choices = append(choices, ApptReminderByMsg)
		}
		if (originByte & ByEmail) == ByEmail {
			choices = append(choices, ApptReminderByEmail)
		}
		if (originByte & ByCall) == ByCall {
			choices = append(choices, ApptReminderByCall)
		}
		if (originByte & ByApp) == ByApp {
			choices = append(choices, ApptReminderByApp)
		}
	} else {
		// 旧值处理 - 正数值
		choices = append(choices, *unconfirmedReminderBy)
	}

	return choices
}

func convertToCustomerRelatedData(dbData *customerrelateddatarepo.CustomerRelatedData) *CustomerRelatedData {
	return &CustomerRelatedData{
		ID:                     dbData.ID,
		CustomerID:             dbData.CustomerID,
		PreferredBusinessID:    dbData.BusinessID,
		CompanyID:              dbData.CompanyID,
		ClientColor:            dbData.ClientColor,
		IsBlockMessage:         dbData.IsBlockMessage,
		IsBlockOnlineBooking:   dbData.IsBlockOnlineBooking,
		LoginEmail:             dbData.LoginEmail,
		ReferralSourceID:       dbData.ReferralSourceID,
		ReferralSourceDesc:     dbData.ReferralSourceDesc,
		SendAutoEmail:          dbData.SendAutoEmail,
		SendAutoMessage:        dbData.SendAutoMessage,
		SendAppAutoMessage:     dbData.SendAppAutoMessage,
		UnconfirmedReminderBy:  GetApptReminderListFromDB(&dbData.UnconfirmedReminderBy),
		PreferredGroomerID:     dbData.PreferredGroomerID,
		PreferredFrequencyDay:  dbData.PreferredFrequencyDay,
		PreferredFrequencyType: dbData.PreferredFrequencyType,
		LastServiceTime:        dbData.LastServiceTime,
		Source:                 dbData.Source,
		ExternalID:             dbData.ExternalID,
		CreateBy:               dbData.CreateBy,
		UpdateBy:               dbData.UpdateBy,
		IsRecurring:            dbData.IsRecurring,
		ShareApptStatus:        dbData.ShareApptStatus,
		ShareRangeType:         dbData.ShareRangeType,
		ShareRangeValue:        dbData.ShareRangeValue,
		ShareApptJSON:          dbData.ShareApptJSON,
		PreferredDay:           dbData.PreferredDay,
		PreferredTime:          dbData.PreferredTime,
		AccountID:              dbData.AccountID,
		CustomerCode:           dbData.CustomerCode,
		IsUnsubscribed:         dbData.IsUnsubscribed,
		Birthday:               dbData.Birthday,
		ActionState:            dbData.ActionState,
		CustomizeLifeCycleID:   dbData.CustomizeLifeCycleID,
		CustomizeActionStateID: dbData.CustomizeActionStateID,
		State:                  dbData.State,
		DeletedTime:            dbData.DeletedTime,
		CreatedTime:            dbData.CreatedTime,
		UpdatedTime:            dbData.UpdatedTime,
	}
}
