// greeter/entity.go 包含了该logic层内会用到的所有实体
// 请注意, 为了可迭代和可维护, 即使logic entity 和 repo entity 是同一个实体, 也请分开定义
package customer

import (
	"time"

	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	customerrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/db/customer"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/utils/pointer"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
)

type Contact struct {
	// contact id
	ID              int                                  `json:"id"`
	BusinessID      int                                  `json:"business_id"`
	CustomerID      int                                  `json:"customer_id"`
	GivenName       string                               `json:"given_name"`
	FamilyName      string                               `json:"family_name"`
	PhoneNumber     string                               `json:"phone_number"`
	Email           string                               `json:"email"`
	Title           string                               `json:"title"`
	Type            customerpb.CustomerContact_Type      `json:"type"`
	IsPrimary       customerpb.CustomerContact_IsPrimary `json:"is_primary"`
	State           customerpb.CustomerContact_State     `json:"state"`
	CreateTime      time.Time                            `json:"create_time"`
	UpdateTime      time.Time                            `json:"update_time"`
	CompanyID       int64                                `json:"company_id"`
	E164PhoneNumber string                               `json:"e164_phone_number"`
}

func (c *Contact) ConvIsPrimary() int8 {
	if c == nil {
		return 0
	}

	if c.IsPrimary == customerpb.CustomerContact_PRIMARY {
		return 1
	}

	return 0
}

// pb 定义和数据库定义不一致, 需要转换, 数据库是0和1, pb是枚举
func (c *Contact) GetIsPrimary() customerpb.CustomerContact_IsPrimary {
	if c == nil {
		return customerpb.CustomerContact_IS_PRIMARY_UNSPECIFIED
	}
	if c.IsPrimary == 1 {
		return customerpb.CustomerContact_PRIMARY
	}

	return customerpb.CustomerContact_NO_PRIMARY
}

func (c *Contact) ToPB() *customerpb.CustomerContact {
	return &customerpb.CustomerContact{
		Id:              int64(c.ID),
		BusinessId:      int64(c.BusinessID),
		CustomerId:      int64(c.CustomerID),
		GivenName:       c.GivenName,
		FamilyName:      c.FamilyName,
		PhoneNumber:     c.PhoneNumber,
		Email:           c.Email,
		Title:           c.Title,
		Type:            c.Type,
		IsPrimary:       c.GetIsPrimary(),
		E164PhoneNumber: c.E164PhoneNumber,
		CreateTime:      timestamppb.New(c.CreateTime),
		UpdateTime:      timestamppb.New(c.UpdateTime),
		CompanyId:       c.CompanyID,
		State:           c.State,
	}
}

type Address struct {
	// address id
	ID         int                           `json:"id"`
	CustomerID int64                         `json:"customer_id"`
	Address1   string                        `json:"address1"`
	Address2   string                        `json:"address2"`
	City       string                        `json:"city"`
	State      string                        `json:"state"`
	Zipcode    string                        `json:"zipcode"`
	Country    string                        `json:"country"`
	CompanyID  int64                         `json:"company_id"`
	Lat        string                        `json:"lat"`
	Lng        string                        `json:"lng"`
	Status     customerpb.Address_State      `json:"status"`
	IsPrimary  *customerpb.Address_IsPrimary `json:"is_primary"`
	CreateTime time.Time                     `json:"create_time"`
	UpdateTime time.Time                     `json:"update_time"`
}

func (a *Address) ToPB() *customerpb.Address {
	return &customerpb.Address{
		Id:         int64(a.ID),
		CustomerId: a.CustomerID,
		Address1:   a.Address1,
		Address2:   a.Address2,
		City:       a.City,
		State:      a.State,
		Zipcode:    a.Zipcode,
		RegionCode: a.Country,
		Status:     a.Status,
		IsPrimary:  a.GetIsPrimary(),
	}
}

func (a *Address) ConvIsPrimaryInt() *int8 {
	if a == nil || a.IsPrimary == nil {
		return nil
	}

	if *a.IsPrimary == customerpb.Address_PRIMARY {
		return pointer.Get(int8(1))
	}

	return pointer.Get(int8(0))
}

// pb 定义和数据库定义不一致, 需要转换, 数据库是0和1, pb是枚举
func (a *Address) GetIsPrimary() customerpb.Address_IsPrimary {
	if a == nil || a.IsPrimary == nil {
		return customerpb.Address_IS_PRIMARY_UNSPECIFIED
	}
	if *a.IsPrimary == 1 {
		return customerpb.Address_PRIMARY
	}

	return customerpb.Address_NO_PRIMARY
}

type Customer struct {
	ID                     int64                           `json:"id"`
	CompanyID              int64                           `json:"company_id"`
	PreferredBusinessID    int64                           `json:"preferred_business_id"`
	AccountID              int64                           `json:"account_id"`
	GivenName              string                          `json:"given_name"`
	FamilyName             string                          `json:"family_name"`
	Email                  *string                         `json:"email"`
	PhoneNumber            string                          `json:"phone_number"`
	AvatarPath             string                          `json:"avatar_path"`
	BirthTime              *time.Time                      `json:"birth_time"`
	CustomerCode           string                          `json:"customer_code"`
	Source                 string                          `json:"source"`
	State                  customerpb.Customer_State       `json:"state"`
	Type                   customerpb.Customer_Type        `json:"type"`
	Address                *Address                        `json:"address"`
	Contact                *Contact                        `json:"contact"`
	ReferralSourceID       int64                           `json:"referral_source_id"`
	ReferralSourceDesc     string                          `json:"referral_source_desc"`
	PreferredGroomerID     int64                           `json:"preferred_groomer_id"`
	PreferredFrequencyDay  int64                           `json:"preferred_frequency_day"`
	PreferredFrequencyType int64                           `json:"preferred_frequency_type"`
	PreferredDay           []int64                         `json:"preferred_day"`
	PreferredTime          []int64                         `json:"preferred_time"`
	LifeCycle              customerpb.Customer_LifeCycle   `json:"life_cycle"`
	ActionState            customerpb.Customer_ActionState `json:"action_state"`
	AllocateStaffID        int64                           `json:"allocate_staff_id"`
	CreateTime             time.Time                       `json:"create_time"`
	UpdateTime             time.Time                       `json:"update_time"`

	CustomizeLifeCycleID   *int64 `json:"customize_life_cycle_id"`
	CustomizeActionStateID *int64 `json:"customize_action_state_id"`

	ClientColor string `json:"client_color"`

	CustomFields *structpb.Struct `json:"custom_fields"`
}

func (c *Customer) GetCustomizeLifeCycleID() int64 {
	if c == nil || c.CustomizeLifeCycleID == nil {
		return 0
	}

	return *c.CustomizeLifeCycleID
}

func (c *Customer) GetCustomizeActionStateID() int64 {
	if c == nil || c.CustomizeActionStateID == nil {
		return 0
	}

	return *c.CustomizeActionStateID
}

func (c *Customer) ToDB() *customerrepo.BusinessCustomer {
	return &customerrepo.BusinessCustomer{
		CompanyID:       c.CompanyID,
		BusinessID:      int(c.PreferredBusinessID),
		AccountID:       c.AccountID,
		FirstName:       c.GivenName,
		LastName:        c.FamilyName,
		Email:           c.Email,
		AvatarPath:      c.AvatarPath,
		Birthday:        c.BirthTime,
		Source:          c.Source,
		Type:            c.Type,
		LifeCycle:       c.LifeCycle,
		ActionState:     c.ActionState,
		AllocateStaffID: c.AllocateStaffID,
	}
}

func (c *Customer) ToPB() *customerpb.Customer {
	if c == nil {
		return nil
	}
	var birthTimePb *timestamppb.Timestamp
	if c.BirthTime != nil && !c.BirthTime.IsZero() {
		birthTimePb = timestamppb.New(*c.BirthTime)
	}

	customerPB := &customerpb.Customer{
		Id:                  c.ID,
		CompanyId:           c.CompanyID,
		PreferredBusinessId: c.PreferredBusinessID,
		AccountId:           c.AccountID,
		GivenName:           c.GivenName,
		FamilyName:          c.FamilyName,
		Email:               customerutils.ToValue(c.Email),
		PhoneNumber:         c.PhoneNumber,
		AvatarPath:          c.AvatarPath,
		BirthTime:           birthTimePb,
		CustomerCode:        c.CustomerCode,
		Source:              c.Source,
		Type:                c.Type,
		LifeCycle:           c.LifeCycle,
		ActionState:         c.ActionState,
		AllocateStaffId:     c.AllocateStaffID,
		CreateTime:          timestamppb.New(c.CreateTime),
		UpdateTime:          timestamppb.New(c.UpdateTime),
		State:               c.State,
		AdditionalInfo: &customerpb.Customer_AdditionalInfo{
			ReferralSourceId:       c.ReferralSourceID,
			ReferralSourceDesc:     c.ReferralSourceDesc,
			PreferredGroomerId:     c.PreferredGroomerID,
			PreferredFrequencyDay:  c.PreferredFrequencyDay,
			PreferredFrequencyType: c.PreferredFrequencyType,
			PreferredDay:           c.PreferredDay,
			PreferredTime:          c.PreferredTime,
		},
		CustomizeLifeCycleId:   c.GetCustomizeLifeCycleID(),
		CustomizeActionStateId: c.GetCustomizeActionStateID(),
		ClientColor:            c.ClientColor,
		CustomFields:           c.CustomFields,
	}

	if c.Address != nil {
		customerPB.Address = c.Address.ToPB()
	}

	if c.Contact != nil {
		customerPB.Contact = c.Contact.ToPB()
	}

	return customerPB
}

type GetCustomerParams struct {
	ID int64
}

type ListCustomersParams struct {
	CompanyID              int64
	PageSize               int
	PageNum                int
	OrderField             customerpb.ListCustomersRequest_OrderField
	OrderDirection         customerpb.ListCustomersRequest_OrderDirection
	CustomerIDs            []int64
	Type                   *customerpb.Customer_Type
	ActionState            *customerpb.Customer_ActionState
	LifeCycle              *customerpb.Customer_LifeCycle
	CustomizeLifeCycleID   *int64
	CustomizeActionStateID *int64
	MainPhoneNumber        *string
}

func (l *ListCustomersParams) GetOrderField() *string {
	if l.OrderField == customerpb.ListCustomersRequest_ORDER_FIELD_UNSPECIFIED {
		l.OrderField = customerpb.ListCustomersRequest_CREATE_TIME
	}
	// 小写
	orderField := l.OrderField.String()

	return &orderField
}

func (l *ListCustomersParams) GetOrderDirection() string {
	if l.OrderDirection != customerpb.ListCustomersRequest_ORDER_DIRECTION_UNSPECIFIED {
		return l.OrderDirection.String()
	}

	return customerpb.ListAddressesRequest_DESC.String()
}

// ListCustomersResult 分页查询结果
type ListCustomersResult struct {
	Total int64       `json:"total"`
	Items []*Customer `json:"items"`
}

type ListAddressesParams struct {
	CustomerID     int64
	PageSize       int
	PageNum        int
	OrderField     customerpb.ListAddressesRequest_OrderField
	OrderDirection customerpb.ListAddressesRequest_OrderDirection
}

func (l *ListAddressesParams) GetOrderField() *string {
	if l.OrderField == customerpb.ListAddressesRequest_ORDER_FIELD_UNSPECIFIED {
		l.OrderField = customerpb.ListAddressesRequest_CREATE_TIME
	}
	// 小写
	orderField := l.OrderField.String()

	return &orderField
}

func (l *ListAddressesParams) GetOrderDirection() string {
	if l.OrderDirection != customerpb.ListAddressesRequest_ORDER_DIRECTION_UNSPECIFIED {
		return l.OrderDirection.String()
	}

	return customerpb.ListAddressesRequest_DESC.String()
}

type ConvertCustomersAttributeDatum struct {
	CustomerIDs            []int64
	CustomizeLifeCycleID   *int64
	CustomizeActionStateID *int64
}
