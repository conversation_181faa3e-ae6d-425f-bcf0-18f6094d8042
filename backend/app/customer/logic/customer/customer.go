package customer

import (
	"context"
	"time"

	"github.com/bytedance/sonic"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/db"
	customerrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/db/customer"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/sms"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/utils/pointer"
	"github.com/MoeGolibrary/moego/backend/common/utils/random"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
)

type Logic struct {
	customerRepo customerrepo.Repository
	txManager    db.TransactionManager
	smsRPC       sms.ReadWriter
}

func New() *Logic {
	return &Logic{
		customerRepo: customerrepo.New(),
		txManager:    db.NewTxManager(),
		smsRPC:       sms.New(),
	}
}

func NewByParams(
	customerRepo customerrepo.Repository,
	txManager db.TransactionManager,
) *Logic {
	return &Logic{
		customerRepo: customerRepo,
		txManager:    txManager,
	}
}

// convertCustomerFields 将请求参数转换为数据库字段
func convertCustomerFields(req *Customer, existing *customerrepo.BusinessCustomer) *customerrepo.BusinessCustomer {
	var preferredDay string
	var preferredTime string

	if req.PreferredDay != nil {
		preferredDay = customerutils.JSONMarshalNoErr(req.PreferredDay)
	}

	if req.PreferredTime != nil {
		preferredTime = customerutils.JSONMarshalNoErr(req.PreferredTime)
	}

	customer := &customerrepo.BusinessCustomer{
		FirstName:              req.GivenName,
		CompanyID:              req.CompanyID,
		BusinessID:             int(req.PreferredBusinessID),
		AccountID:              req.AccountID,
		LastName:               req.FamilyName,
		Email:                  req.Email,
		AvatarPath:             req.AvatarPath,
		Birthday:               req.BirthTime,
		Source:                 req.Source,
		Type:                   req.Type,
		LifeCycle:              req.LifeCycle,
		ActionState:            req.ActionState,
		ReferralSourceID:       int(req.ReferralSourceID),
		ReferralSourceDesc:     req.ReferralSourceDesc,
		PreferredGroomerID:     int(req.PreferredGroomerID),
		PreferredFrequencyDay:  int(req.PreferredFrequencyDay),
		PreferredFrequencyType: int8(req.PreferredFrequencyType),
		PreferredDay:           preferredDay,
		PreferredTime:          preferredTime,
		AllocateStaffID:        req.AllocateStaffID,
		CustomizeLifeCycleID:   req.CustomizeLifeCycleID,
		CustomizeActionStateID: req.CustomizeActionStateID,
		ClientColor:            req.ClientColor,
		UpdateTime:             time.Now().Unix(),
	}

	if existing != nil {
		// update
		customer.ID = existing.ID
		if existing.CustomFields != nil {
			customer.CustomFields = existing.CustomFields
		}
	} else {
		customer.Status = int8(customerpb.Customer_NORMAL.Number())
		customer.CreateTime = time.Now().Unix()
		customer.CustomerCode = random.UpperString(8)
	}

	return customer
}

func convertCustomerToDB(req *Customer) *customerrepo.BusinessCustomer {
	return convertCustomerFields(req, nil)
}

func convertCustomerToUpdate(req *Customer, existing *customerrepo.BusinessCustomer) *customerrepo.BusinessCustomer {
	return convertCustomerFields(req, existing)
}

func (l *Logic) Create(ctx context.Context, req *Customer) (*Customer, error) {
	phoneNumber := req.PhoneNumber
	if phoneNumber == "" {
		return nil, status.Errorf(codes.InvalidArgument, "phone number is required")
	}

	// 使用get方法查询客户
	existing, err := l.getByPhoneNumber(ctx, phoneNumber, req.CompanyID)
	if err != nil {
		return nil, err
	}

	if existing != nil {
		log.DebugContextf(ctx, "customer already exists, customer_id: %d", existing.ID)

		return convertCustomerToBusinessLogic(existing), status.Errorf(codes.AlreadyExists, "customer already exists")
	}

	// create customer
	customerToCreate := convertCustomerToDB(req)

	// create contact
	if req.Contact != nil {
		contact := &customerrepo.Contact{
			FirstName:   req.Contact.GivenName,
			LastName:    req.Contact.FamilyName,
			PhoneNumber: phoneNumber,
			Email:       req.Contact.Email,
			Title:       req.Contact.Title,
			Type:        int8(req.Contact.Type),
			IsPrimary:   req.Contact.ConvIsPrimary(),
			Status:      int8(customerpb.CustomerContact_NORMAL),
			CompanyID:   req.CompanyID,
			CreateTime:  time.Now().Unix(),
			UpdateTime:  time.Now().Unix(),
		}
		customerToCreate.Contacts = []*customerrepo.Contact{contact}
	}

	// create address
	if req.Address != nil {
		address := &customerrepo.Address{
			Address1:   req.Address.Address1,
			Address2:   req.Address.Address2,
			City:       req.Address.City,
			State:      req.Address.State,
			Zipcode:    req.Address.Zipcode,
			Country:    req.Address.Country,
			Lat:        req.Address.Lat,
			Lng:        req.Address.Lng,
			IsPrimary:  req.Address.ConvIsPrimaryInt(),
			CompanyID:  req.CompanyID,
			Status:     int8(customerpb.Address_NORMAL),
			CreateTime: time.Now().Unix(),
			UpdateTime: time.Now().Unix(),
		}
		customerToCreate.Addresses = []*customerrepo.Address{address}
	}

	if req.CustomFields != nil {
		customerToCreate.CustomFields = &customerrepo.CustomFields{
			CompanyID: req.CompanyID,
			Fields:    req.CustomFields,
		}
	}

	// create customer
	customer, err := l.customerRepo.CreateCustomer(ctx, customerToCreate)
	if err != nil {
		return nil, err
	}

	return convertCustomerToBusinessLogic(customer), nil
}

// Update 更新客户信息及其关联数据
func (l *Logic) Update(ctx context.Context, req *Customer) error {
	if req.ID <= 0 {
		return status.Errorf(codes.InvalidArgument, "customer id is required")
	}

	if phone := req.PhoneNumber; phone != "" {
		phoneNumberCustomer, err := l.getByPhoneNumber(ctx, req.PhoneNumber, req.CompanyID)
		if err != nil {
			return err
		}
		if phoneNumberCustomer != nil && phoneNumberCustomer.ID != req.ID {
			return status.Errorf(codes.InvalidArgument, "phone number already exists")
		}
	}

	// 获取当前客户信息
	existingCustomer, err := l.customerRepo.GetCustomer(ctx, &customerrepo.GetCustomerParams{
		ID: req.ID,
	})

	if err != nil {
		return err
	}

	if existingCustomer == nil {
		return status.Errorf(codes.NotFound, "customer not found")
	}

	// update customer
	customerToUpdate := convertCustomerToUpdate(req, existingCustomer)

	// 处理联系人信息
	updateContact := make([]*customerrepo.Contact, 0, len(existingCustomer.Contacts))

	// 1. 首先处理主要联系人（MAIN）的电话号码/Name更新
	phoneNumber := req.PhoneNumber
	givenName := req.GivenName
	familyName := req.FamilyName
	if phoneNumber != "" || givenName != "" || familyName != "" {
		for _, contact := range existingCustomer.Contacts {
			if contact.Type == int8(customerpb.CustomerContact_MAIN) {
				contact.PhoneNumber = phoneNumber
				contact.FirstName = givenName
				contact.LastName = familyName
				updateContact = append(updateContact, contact)

				break
			}
		}
	}

	// 2. 处理请求中的联系人信息
	if req.Contact != nil {
		updateContact, err = l.updateContact(ctx, req.Contact, existingCustomer.ID)
		if err != nil {
			return err
		}
	}

	// 3. 处理地址信息
	if req.Address != nil {
		err = l.updateAddress(ctx, req.Address)
		if err != nil {
			return err
		}
	}

	// 4. 设置更新后的联系人列表
	if len(updateContact) > 0 {
		customerToUpdate.Contacts = updateContact
	}

	// 5. 处理自定义字段信息
	if req.CustomFields != nil {
		if customerToUpdate.CustomFields == nil {
			customerToUpdate.CustomFields = &customerrepo.CustomFields{
				CustomerID: req.ID,
				CompanyID:  req.CompanyID,
				Fields:     req.CustomFields,
			}
		} else {
			customerToUpdate.CustomFields.CustomerID = req.ID
			customerToUpdate.CustomFields.CompanyID = req.CompanyID
			customerToUpdate.CustomFields.Fields = req.CustomFields
		}
	}

	return l.customerRepo.UpdateCustomer(ctx, customerToUpdate)
}

func (l *Logic) List(ctx context.Context, params *ListCustomersParams) (*ListCustomersResult, error) {
	// select by main contact
	if params.MainPhoneNumber != nil && *params.MainPhoneNumber != "" {
		customer, err := l.getByPhoneNumber(ctx, *params.MainPhoneNumber, params.CompanyID)
		if err != nil {
			log.ErrorContext(ctx, "List Customer getByPhoneNumber err, err:%v", err)

			return nil, err
		}
		if customer == nil {
			return &ListCustomersResult{Total: 0, Items: []*Customer{}}, nil
		}

		return &ListCustomersResult{Total: 1, Items: []*Customer{convertCustomerToBusinessLogic(customer)}}, nil
	}

	query := &customerrepo.Query{
		CompanyID:   params.CompanyID,
		CustomerIDs: params.CustomerIDs,
	}

	filter := &customerrepo.Filter{
		ActionState:            params.ActionState,
		Type:                   params.Type,
		Status:                 customerpb.Customer_NORMAL.Enum(),
		LifeCycle:              params.LifeCycle,
		CustomizeLifeCycleID:   params.CustomizeLifeCycleID,
		CustomizeActionStateID: params.CustomizeActionStateID,
	}

	// 获取总数
	total, err := l.customerRepo.CountCustomers(ctx, query, filter)
	if err != nil {
		return nil, err
	}

	// 计算分页参数
	pagination := &db.Pagination{
		Offset:  (params.PageNum - 1) * params.PageSize,
		Limit:   params.PageSize,
		OrderBy: params.GetOrderField(),
		Desc:    params.GetOrderDirection(),
	}

	// 使用聚合仓库的方法一次查询获取所有相关数据
	customers, err := l.customerRepo.ListCustomers(ctx, query, filter, pagination, &customerrepo.LoadOptions{
		WithAddresses:    true,
		WithContacts:     true,
		WithCustomFields: true,
	})

	if err != nil {
		return nil, err
	}

	if len(customers) == 0 {
		return &ListCustomersResult{
			Total: total,
			Items: []*Customer{},
		}, nil
	}

	// 转换为业务对象
	items := make([]*Customer, 0, len(customers))
	for _, customer := range customers {
		items = append(items, convertCustomerToBusinessLogic(customer))
	}

	return &ListCustomersResult{
		Total: total,
		Items: items,
	}, nil
}

func (l *Logic) Get(ctx context.Context, params *GetCustomerParams) (*Customer, error) {
	// 使用聚合仓库的方法查询客户
	customer, err := l.customerRepo.GetCustomer(ctx, &customerrepo.GetCustomerParams{
		ID: params.ID,
	})

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}

		return nil, err
	}

	if customer == nil {
		return nil, nil
	}

	// 转换为业务对象
	return convertCustomerToBusinessLogic(customer), nil
}

func (l *Logic) Delete(ctx context.Context, customerID int64) error {
	// 获取当前客户信息
	customer, err := l.customerRepo.GetCustomer(ctx, &customerrepo.GetCustomerParams{
		ID: customerID,
	})

	if err != nil {
		return err
	}

	if customer == nil {
		return status.Errorf(codes.NotFound, "customer not found")
	}

	// 更新状态为已删除
	customer.Status = int8(customerpb.Customer_DELETED.Number())
	customer.UpdateTime = time.Now().Unix()

	// 同步删除contact 和 address
	if customer.Contacts != nil {
		for _, contact := range customer.Contacts {
			contact.Status = int8(customerpb.CustomerContact_DELETED.Number())
			contact.UpdateTime = time.Now().Unix()
		}
	}

	if customer.Addresses != nil {
		for _, address := range customer.Addresses {
			address.Status = int8(customerpb.Address_DELETED.Number())
			address.UpdateTime = time.Now().Unix()
		}
	}

	return l.customerRepo.UpdateCustomer(ctx, customer)
}

/**
 * address
 */
func (l *Logic) CreateAddress(ctx context.Context, address *Address) (int64, error) {
	return l.createAddress(ctx, address)
}

func (l *Logic) UpdateAddress(ctx context.Context, address *Address) error {
	return l.updateAddress(ctx, address)
}

func (l *Logic) DeleteAddress(ctx context.Context, addressID int64) error {
	return l.customerRepo.DeleteAddress(ctx, addressID)
}

func (l *Logic) ListAddresses(ctx context.Context, params *ListAddressesParams) ([]*Address, error) {
	addresses, err := l.customerRepo.GetAddresses(ctx, params.CustomerID, &db.Pagination{
		Offset:  (params.PageNum - 1) * params.PageSize,
		Limit:   params.PageSize,
		OrderBy: params.GetOrderField(),
		Desc:    params.GetOrderDirection(),
	})
	if err != nil {
		return nil, err
	}

	result := make([]*Address, 0, len(addresses))
	for _, address := range addresses {
		result = append(result, convertAddressToBusinessLogic(address))
	}

	return result, nil
}

// 将仓库层客户对象转换为业务逻辑层客户对象
func convertCustomerToBusinessLogic(customer *customerrepo.BusinessCustomer) *Customer {
	var preferredDay []int64
	var preferredTime []int64
	if customer.PreferredDay != "" {
		_ = sonic.UnmarshalString(customer.PreferredDay, &preferredDay)
	}
	if customer.PreferredTime != "" {
		_ = sonic.UnmarshalString(customer.PreferredTime, &preferredTime)
	}
	result := &Customer{
		ID:                     customer.ID,
		CompanyID:              customer.CompanyID,
		PreferredBusinessID:    int64(customer.BusinessID),
		AccountID:              customer.AccountID,
		GivenName:              customer.FirstName,
		FamilyName:             customer.LastName,
		Email:                  customer.Email,
		AvatarPath:             customer.AvatarPath,
		BirthTime:              customer.Birthday,
		CustomerCode:           customer.CustomerCode,
		Source:                 customer.Source,
		CreateTime:             time.Unix(customer.CreateTime, 0),
		UpdateTime:             time.Unix(customer.UpdateTime, 0),
		Type:                   customer.Type,
		LifeCycle:              customer.LifeCycle,
		ActionState:            customer.ActionState,
		AllocateStaffID:        customer.AllocateStaffID,
		ReferralSourceID:       int64(customer.ReferralSourceID),
		ReferralSourceDesc:     customer.ReferralSourceDesc,
		PreferredGroomerID:     int64(customer.PreferredGroomerID),
		PreferredFrequencyDay:  int64(customer.PreferredFrequencyDay),
		PreferredFrequencyType: int64(customer.PreferredFrequencyType),
		PreferredDay:           preferredDay,
		PreferredTime:          preferredTime,
		State:                  customerpb.Customer_State(customer.Status),
		CustomizeLifeCycleID:   customer.CustomizeLifeCycleID,
		CustomizeActionStateID: customer.CustomizeActionStateID,
		ClientColor:            customer.ClientColor,
	}

	// 获取主要联系人
	for _, c := range customer.Contacts {
		if c.IsPrimary == int8(customerpb.CustomerContact_PRIMARY.Number()) {
			result.Contact = &Contact{
				ID:              c.ID,
				CustomerID:      c.CustomerID,
				CompanyID:       c.CompanyID,
				GivenName:       c.FirstName,
				FamilyName:      c.LastName,
				PhoneNumber:     c.PhoneNumber,
				Email:           c.Email,
				Title:           c.Title,
				State:           customerpb.CustomerContact_State(c.Status),
				Type:            customerpb.CustomerContact_Type(c.Type),
				IsPrimary:       customerpb.CustomerContact_IsPrimary(c.IsPrimary),
				E164PhoneNumber: c.E164PhoneNumber,
				CreateTime:      time.Unix(c.CreateTime, 0),
				UpdateTime:      time.Unix(c.UpdateTime, 0),
			}
			result.PhoneNumber = c.PhoneNumber

			break
		}
	}

	// 获取主要地址
	for _, a := range customer.Addresses {
		if *a.IsPrimary == int8(customerpb.Address_PRIMARY.Number()) {
			result.Address = convertAddressToBusinessLogic(a)

			break
		}
	}

	// 获取自定义字段
	if customer.CustomFields != nil {
		result.CustomFields = customer.CustomFields.Fields
	}

	return result
}

// 将仓库层地址对象转换为业务逻辑层地址对象
func convertAddressToBusinessLogic(address *customerrepo.Address) *Address {
	return &Address{
		ID:         address.ID,
		CustomerID: int64(address.CustomerID),
		CompanyID:  address.CompanyID,
		Address1:   address.Address1,
		Address2:   address.Address2,
		City:       address.City,
		State:      address.State,
		Zipcode:    address.Zipcode,
		Country:    address.Country,
		Lat:        address.Lat,
		Lng:        address.Lng,
		IsPrimary:  pointer.Get(customerpb.Address_IsPrimary(*address.IsPrimary)),
		Status:     customerpb.Address_State(address.Status),
		CreateTime: time.Unix(address.CreateTime, 0),
		UpdateTime: time.Unix(address.UpdateTime, 0),
	}
}

func (l *Logic) getByPhoneNumber(ctx context.Context,
	phoneNumber string, companyID int64) (*customerrepo.BusinessCustomer, error) {
	contacts, err := l.customerRepo.GetContacts(ctx, &customerrepo.ContactFilter{
		Type:   customerpb.CustomerContact_MAIN,
		Status: customerpb.CustomerContact_NORMAL,
	}, &customerrepo.GetContactParams{
		PhoneNumber: phoneNumber,
		CompanyID:   companyID,
	})

	if err != nil {
		return nil, err
	}

	if len(contacts) == 0 {
		return nil, nil
	}

	// the phone number is unique, so we can get the customer by the contact id
	customer, err := l.customerRepo.GetCustomer(ctx, &customerrepo.GetCustomerParams{
		ID: int64(contacts[0].CustomerID),
	})

	if err != nil {
		if status.Code(err) == codes.NotFound {
			return nil, nil
		}

		return nil, err
	}

	return customer, nil
}

func (l *Logic) updateContact(ctx context.Context, req *Contact, customerID int64) ([]*customerrepo.Contact, error) {
	existingContacts, err := l.customerRepo.GetContacts(ctx, &customerrepo.ContactFilter{
		Status: customerpb.CustomerContact_NORMAL,
	}, &customerrepo.GetContactParams{
		CustomerID: customerID,
	})
	if err != nil {
		return nil, err
	}
	// 如果只有 1 条 contact, 则不能编辑为非 primary
	if len(existingContacts) == 1 && req.ID == existingContacts[0].ID {
		if req.IsPrimary != customerpb.CustomerContact_PRIMARY {
			return nil, status.Errorf(codes.InvalidArgument, "only one contact, must be primary")
		}
	}

	// 创建 or 更新新的Contact
	contact := &customerrepo.Contact{
		ID:          req.ID,
		FirstName:   req.GivenName,
		LastName:    req.FamilyName,
		PhoneNumber: req.PhoneNumber,
		Email:       req.Email,
		Title:       req.Title,
		Type:        int8(req.Type),
		IsPrimary:   req.ConvIsPrimary(),
		CompanyID:   req.CompanyID,
		UpdateTime:  time.Now().Unix(),
	}
	// 如果这是第一条, 则必须为 primary
	if len(existingContacts) == 0 {
		contact.IsPrimary = 1 // todo: db里是用1表示primary, 后续要改成pb
	}

	contacts := make([]*customerrepo.Contact, 0, len(existingContacts)+1)
	// 如果原来已经有一个 primary contact 了, 新插入一条 contact 如果要设置成 primary, 就要把前者变成非 primary
	if len(existingContacts) > 0 && req.GetIsPrimary() == customerpb.CustomerContact_PRIMARY {
		var primaryContact *customerrepo.Contact
		for _, contact := range existingContacts {
			if contact.IsPrimary == int8(customerpb.CustomerContact_PRIMARY) {
				primaryContact = contact

				break
			}
		}
		if primaryContact != nil && primaryContact.ID != req.ID {
			primaryContact.IsPrimary = 0 // todo: db里是用0表示非primary, 后续要改成pb
			primaryContact.UpdateTime = time.Now().Unix()
			contacts = append(contacts, primaryContact)
		}
	}
	contacts = append(contacts, contact)

	return contacts, nil
}

func (l *Logic) updateAddress(ctx context.Context, req *Address) error {
	if req.ID == 0 {
		return status.Errorf(codes.InvalidArgument, "address id not 0")
	}

	address, err := l.customerRepo.GetAddress(ctx, int64(req.ID))
	if err != nil {
		return err
	}

	// 如果地址是 primary, 则不能设置为非 primary
	if *address.IsPrimary == int8(customerpb.Address_PRIMARY) &&
		req.IsPrimary != nil &&
		req.GetIsPrimary() == customerpb.Address_NO_PRIMARY {
		return status.Errorf(codes.InvalidArgument, "primary address cannot be set to non-primary")
	}

	// 客户的所有地址
	existingAddresses, err := l.customerRepo.GetAddresses(ctx, int64(address.CustomerID), nil)
	if err != nil {
		return err
	}

	// 如果只有 1 条 address, 则不能编辑为非 primary
	if len(existingAddresses) == 1 && address.ID == req.ID {
		if req.IsPrimary != nil && *req.IsPrimary == customerpb.Address_NO_PRIMARY {
			return status.Errorf(codes.InvalidArgument, "only one address, must be primary")
		}
	}

	// 更新 Address
	updateAddress := &customerrepo.Address{
		ID:         req.ID,
		Address1:   req.Address1,
		Address2:   req.Address2,
		City:       req.City,
		State:      req.State,
		Zipcode:    req.Zipcode,
		Country:    req.Country,
		Lat:        req.Lat,
		Lng:        req.Lng,
		IsPrimary:  req.ConvIsPrimaryInt(), // 这里需要转	换
		CompanyID:  req.CompanyID,
		UpdateTime: time.Now().Unix(),
	}

	// 要更新的列表
	addresses := make([]*customerrepo.Address, 0, len(existingAddresses)+1)

	// 如果原来已经有一个 primary address 了, 更新一条 address 为 primary, 就要把old primary 设置成非 primary
	if len(existingAddresses) > 0 && req.GetIsPrimary() == customerpb.Address_PRIMARY {
		var primaryAddress *customerrepo.Address
		for _, addr := range existingAddresses {
			if *addr.IsPrimary == int8(customerpb.Address_PRIMARY) {
				primaryAddress = addr

				break
			}
		}
		if primaryAddress != nil && primaryAddress.ID != req.ID {
			primaryAddress.IsPrimary = pointer.Get(int8(0)) // todo: db里是用0表示非primary, 后续要改成pb
			primaryAddress.UpdateTime = time.Now().Unix()
			addresses = append(addresses, primaryAddress)
		}
	}
	addresses = append(addresses, updateAddress)

	return l.customerRepo.BatchUpdateAddress(ctx, addresses)
}

// create address, if address params ID is 0, create new address.
func (l *Logic) createAddress(ctx context.Context, req *Address) (int64, error) {
	if req.ID != 0 {
		return 0, status.Errorf(codes.InvalidArgument, "address id not 0")
	}

	existingAddresses, err := l.customerRepo.GetAddresses(ctx, req.CustomerID, nil)
	if err != nil {
		return 0, err
	}

	// 创建 or 更新新的Address
	address := &customerrepo.Address{
		CustomerID: int(req.CustomerID),
		Address1:   req.Address1,
		Address2:   req.Address2,
		City:       req.City,
		State:      req.State,
		Zipcode:    req.Zipcode,
		Country:    req.Country,
		Lat:        req.Lat,
		Lng:        req.Lng,
		IsPrimary:  req.ConvIsPrimaryInt(), // 这里需要转换
		CompanyID:  req.CompanyID,
		Status:     int8(customerpb.Address_NORMAL.Number()),
		UpdateTime: time.Now().Unix(),
		CreateTime: time.Now().Unix(),
	}

	if len(existingAddresses) == 0 {
		address.IsPrimary = pointer.Get(int8(1)) // todo: db里是用1表示primary, 后续要改成pb
	}

	// 使用事务来包装更新和创建操作
	var addressID int64
	if err := l.customerRepo.Tx(ctx, func(repo customerrepo.Repository) error {
		// 如果存在 primary address, 新插入的也是 primary address, 就会把原先的primary Address 改成 not primary
		if len(existingAddresses) > 0 && req.GetIsPrimary() == customerpb.Address_PRIMARY {
			var primaryAddress *customerrepo.Address
			for _, addr := range existingAddresses {
				if *addr.IsPrimary == int8(customerpb.Address_PRIMARY) {
					primaryAddress = addr

					break
				}
			}
			if primaryAddress != nil {
				primaryAddress.IsPrimary = pointer.Get(int8(0)) // todo: db里是用0表示非primary, 后续要改成pb
				primaryAddress.UpdateTime = time.Now().Unix()

				err = repo.BatchUpdateAddress(ctx, []*customerrepo.Address{primaryAddress})
				if err != nil {
					return err
				}
			}
		}
		// 创建新的 address
		addressID, err = repo.CreateAddress(ctx, address)
		if err != nil {
			return err
		}

		return nil
	}); err != nil {
		return 0, err
	}

	return addressID, nil
}

func (l *Logic) ConvertCustomer(ctx context.Context, customerID int64) error {
	// check
	if customerID <= 0 {
		return status.Errorf(codes.InvalidArgument, "customerID is invalid")
	}

	// get lead
	lead, err := l.customerRepo.GetCustomer(ctx, &customerrepo.GetCustomerParams{ID: customerID})
	if err != nil {
		log.ErrorContextf(ctx, "ConvertCustomer GetCustomer lead err, customerID:%d, err:%+v", customerID, err)

		return err
	}

	// check lead
	if lead == nil ||
		lead.BusinessID == 0 ||
		lead.CompanyID == 0 ||
		lead.Type != customerpb.Customer_LEAD ||
		lead.Status != int8(customerpb.Customer_NORMAL) {
		log.ErrorContextf(ctx, "ConvertCustomer lead data is invalid, lead:%+v", lead)

		return status.Errorf(codes.InvalidArgument, "lead data is invalid")
	}

	// fill customer default value
	customerUpdateDB := &customerrepo.BusinessCustomer{
		ID:         lead.ID,
		Type:       customerpb.Customer_CUSTOMER,
		UpdateTime: time.Now().Unix(),
	}
	if lead.ClientColor == "" {
		customerUpdateDB.ClientColor = "#000000"
	}
	if lead.SendAutoMessage == 0 {
		customerUpdateDB.SendAutoMessage = 1
	}
	if lead.SendAppAutoMessage == 0 {
		customerUpdateDB.SendAppAutoMessage = 1
	}
	if lead.UnconfirmedReminderBy == 0 {
		customerUpdateDB.UnconfirmedReminderBy = -127
	}
	if lead.PreferredFrequencyDay == 0 {
		customerUpdateDB.PreferredFrequencyDay = 28
	}
	if lead.PreferredFrequencyType == 0 {
		customerUpdateDB.PreferredFrequencyType = 1
	}
	if lead.PreferredDay == "" {
		customerUpdateDB.PreferredDay = "[0,1,2,3,4,5,6]"
	}
	if lead.PreferredTime == "" {
		customerUpdateDB.PreferredTime = "[0,1435]"
	}

	// update lead
	if err := l.customerRepo.UpdateCustomer(ctx, customerUpdateDB); err != nil {
		log.ErrorContextf(ctx, "ConvertCustomer UpdateCustomer err, customerUpdateDB:%+v, err:%+v",
			customerUpdateDB, err)

		return err
	}

	log.InfoContextf(ctx, "ConvertCustomer success, customerID:%d", customerID)

	return nil
}

func (l *Logic) ConvertCustomersAttribute(ctx context.Context, datum *ConvertCustomersAttributeDatum) error {
	// check
	if datum == nil || len(datum.CustomerIDs) == 0 {
		log.ErrorContextf(ctx, "ConvertCustomersAttribute params is invalid, datum:%+v", datum)

		return status.Errorf(codes.InvalidArgument, "params is invalid")
	}

	// conv updates
	updates := make([]*customerrepo.BusinessCustomer, 0, len(datum.CustomerIDs))
	for _, customerID := range datum.CustomerIDs {
		if customerID <= 0 || (datum.CustomizeLifeCycleID == nil && datum.CustomizeActionStateID == nil) {
			continue
		}
		update := &customerrepo.BusinessCustomer{
			ID:         customerID,
			UpdateTime: time.Now().Unix(),
		}
		if datum.CustomizeLifeCycleID != nil {
			update.CustomizeLifeCycleID = datum.CustomizeLifeCycleID
		}
		if datum.CustomizeActionStateID != nil {
			update.CustomizeActionStateID = datum.CustomizeActionStateID
		}
		updates = append(updates, update)
	}
	if len(updates) == 0 {
		log.ErrorContextf(ctx, "ConvertCustomersAttribute updates is empty, datum:%+v", datum)

		return status.Errorf(codes.InvalidArgument, "params is invalid")
	}

	// update
	if err := l.customerRepo.Tx(ctx, func(repo customerrepo.Repository) error {
		for _, update := range updates {
			if err := repo.UpdateCustomer(ctx, update); err != nil {
				return err
			}
		}

		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "ConvertCustomersAttribute Tx err, err:%+v", err)

		return err
	}

	return nil
}
