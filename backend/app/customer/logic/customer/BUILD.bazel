load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "customer",
    srcs = [
        "customer.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/logic/customer",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/db",
        "//backend/app/customer/repo/db/customer",
        "//backend/app/customer/repo/sms",
        "//backend/app/customer/utils",
        "//backend/common/rpc/framework/log",
        "//backend/common/utils/pointer",
        "//backend/common/utils/random",
        "//backend/proto/customer/v1:customer",
        "@com_github_bytedance_sonic//:sonic",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/structpb",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "customer_test",
    srcs = ["customer_test.go"],
    deps = [
        ":customer",
        "//backend/app/customer/repo/db",
        "//backend/app/customer/repo/db/customer",
        "//backend/app/customer/repo/db/customer/mock",
        "//backend/app/customer/repo/db/mock",
        "//backend/app/customer/repo/pet/mock",
        "//backend/app/customer/repo/search/mock",
        "//backend/app/customer/utils",
        "//backend/common/utils/pointer",
        "//backend/proto/customer/v1:customer",
        "@com_github_stretchr_testify//require",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_uber_go_mock//gomock",
    ],
)
