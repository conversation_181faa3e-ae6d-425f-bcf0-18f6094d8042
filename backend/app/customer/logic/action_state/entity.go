package actionstate

type CreateActionStateDatum struct {
	CompanyID  int64
	BusinessID int64
	StaffID    int64
	Name       string
	Sort       int32
	Color      string
}

type UpdateActionStateDatum struct {
	ID    int64
	Name  *string
	Sort  *int32
	Color *string
}

type ListActionStatesDatum struct {
	CompanyIDs []int64
	IDs        []int64
}

type DeleteActionStateDatum struct {
	ID      int64
	StaffID int64
}
