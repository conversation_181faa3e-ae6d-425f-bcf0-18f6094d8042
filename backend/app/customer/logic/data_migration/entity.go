package datamigration

import (
	"strconv"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	importhistory "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/import_history"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type ImportCustomersRequest struct {
	UploadFileURI string
	Organization  *customerpb.OrganizationRef
	Options       *customerpb.ImportCustomersRequest_Options
	StaffID       int64
}

type ImportHistory struct {
	ID            int64
	Organization  *customerpb.OrganizationRef
	State         customerpb.ImportHistory_State
	UploadFileURI string
	ErrorMessage  string
	CreateTime    time.Time
	UpdateTime    time.Time
	DeletedTime   *time.Time
}

func (i *ImportHistory) ToPB() *customerpb.ImportHistory {
	var deleteTime *timestamppb.Timestamp
	if i.DeletedTime != nil {
		deleteTime = timestamppb.New(*i.DeletedTime)
	}

	return &customerpb.ImportHistory{
		Id:            i.ID,
		Organization:  i.Organization,
		State:         i.State,
		UploadFileUri: i.UploadFileURI,
		ErrorMessage:  i.ErrorMessage,
		CreateTime:    timestamppb.New(i.CreateTime),
		UpdateTime:    timestamppb.New(i.UpdateTime),
		DeleteTime:    deleteTime,
	}
}

func (i *ImportHistory) FromDB(entity *importhistory.ImportHistory) {
	if i == nil || entity == nil {
		return
	}
	i.ID = entity.ID
	i.Organization = &customerpb.OrganizationRef{
		Type: entity.OrganizationType,
		Id:   entity.OrganizationID,
	}
	i.State = entity.State
	i.UploadFileURI = entity.UploadFileURI
	i.ErrorMessage = entity.ErrorMessage
	i.CreateTime = entity.CreateTime
	i.UpdateTime = entity.UpdateTime
	i.DeletedTime = entity.DeleteTime
}

type ListImportHistoriesRequest struct {
	IDs           []int64
	States        []customerpb.ImportHistory_State
	Organizations []*customerpb.OrganizationRef
	PageToken     string
	PageSize      int32
}

func (r *ListImportHistoriesRequest) buildFilterAndPagination() (*importhistory.ListFilter,
	*importhistory.Pagination, error) {
	filter := &importhistory.ListFilter{
		IDs:           r.IDs,
		States:        r.States,
		Organizations: r.Organizations,
	}
	if r.PageToken == "" && r.PageSize == 0 {
		return filter, nil, nil
	}
	pageNumber, err := strconv.ParseInt(r.PageToken, 10, 64)
	if err != nil {
		return nil, nil, err
	}
	pagination := &importhistory.Pagination{
		PageNumber: int32(pageNumber),
		PageSize:   r.PageSize,
	}

	return filter, pagination, nil
}

type ListImportHistoriesResponse struct {
	ImportHistories []*ImportHistory
	TotalSize       *int64
	NextToken       *string
}
