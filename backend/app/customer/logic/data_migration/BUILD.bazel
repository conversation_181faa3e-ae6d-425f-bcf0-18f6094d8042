load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "data_migration",
    srcs = [
        "data_migration.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/logic/data_migration",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/logic/customer_search",
        "//backend/app/customer/logic/history_log",
        "//backend/app/customer/repo/db/customer",
        "//backend/app/customer/repo/db/customer_action_state",
        "//backend/app/customer/repo/db/customer_life_cycle",
        "//backend/app/customer/repo/db/customer_source",
        "//backend/app/customer/repo/file",
        "//backend/app/customer/repo/organization:staff",
        "//backend/app/customer/repo/postgres/customfield",
        "//backend/app/customer/repo/postgres/import_history",
        "//backend/app/customer/repo/producer/customer_producer",
        "//backend/app/customer/utils",
        "//backend/common/rpc/framework/errs",
        "//backend/common/rpc/framework/log",
        "//backend/common/utils/pointer",
        "//backend/common/utils/random",
        "//backend/proto/customer/v1:customer",
        "//backend/proto/customer/v2:customer",
        "//backend/proto/search/v1:search",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/organization/v1:organization",
        "@com_github_panjf2000_ants_v2//:ants",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/structpb",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
