package datamigration

import (
	"bytes"
	"context"
	"encoding/csv"
	"encoding/json"
	"strconv"
	"strings"
	"time"

	ants "github.com/panjf2000/ants/v2"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	customersearch "github.com/MoeGolibrary/moego/backend/app/customer/logic/customer_search"
	historylog "github.com/MoeGolibrary/moego/backend/app/customer/logic/history_log"
	customer "github.com/MoeGolibrary/moego/backend/app/customer/repo/db/customer"
	actionstate "github.com/MoeGolibrary/moego/backend/app/customer/repo/db/customer_action_state"
	lifecycle "github.com/MoeGolibrary/moego/backend/app/customer/repo/db/customer_life_cycle"
	source "github.com/MoeGolibrary/moego/backend/app/customer/repo/db/customer_source"
	file "github.com/MoeGolibrary/moego/backend/app/customer/repo/file"
	organization "github.com/MoeGolibrary/moego/backend/app/customer/repo/organization"
	customfield "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customfield"
	importhistory "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/import_history"
	customerproducer "github.com/MoeGolibrary/moego/backend/app/customer/repo/producer/customer_producer"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	pointer "github.com/MoeGolibrary/moego/backend/common/utils/pointer"
	"github.com/MoeGolibrary/moego/backend/common/utils/random"
	customerpbv1 "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
	searchpb "github.com/MoeGolibrary/moego/backend/proto/search/v1"
)

var pool *ants.Pool

func init() {
	var err error
	pool, err = ants.NewPool(5120)
	if err != nil {
		panic(err)
	}
}

// Logic 数据迁移逻辑
type Logic struct {
	file          file.Repository
	importHistory importhistory.Repository
	customField   customfield.Repository
	source        source.Repository
	lifecycle     lifecycle.ReadWriter
	actionState   actionstate.ReadWriter
	organization  organization.Repository
	customer      customer.Repository
	historylog    *historylog.Logic
	search        *customersearch.Logic
	producer      customerproducer.API
}

func New() *Logic {
	return &Logic{
		file:          file.New(),
		importHistory: importhistory.New(),
		customField:   customfield.New(),
		source:        source.New(),
		lifecycle:     lifecycle.New(),
		actionState:   actionstate.New(),
		organization:  organization.New(),
		customer:      customer.New(),
		historylog:    historylog.New(),
		search:        customersearch.New(),
		producer:      customerproducer.NewCustomerProducer(),
	}
}

func (l *Logic) ImportCustomers(ctx context.Context, req *ImportCustomersRequest) (finalError error) {
	if req.Organization.GetType() != customerpb.OrganizationRef_COMPANY || req.Organization.GetId() <= 0 {
		return errs.Newf(customerpb.ErrCode_ERR_CODE_INVALID_ORGANIZATION, "invalid organization")
	}

	// 初始化 import history
	history := &importhistory.ImportHistory{
		OrganizationID:   req.Organization.GetId(),
		OrganizationType: req.Organization.GetType(),
		State:            customerpb.ImportHistory_IN_PROGRESS,
		UploadFileURI:    req.UploadFileURI,
	}
	if err := l.importHistory.Create(ctx, history); err != nil {
		return errs.Newf(customerpb.ErrCode_ERR_CODE_INITIALIZE_IMPORT_TASK_FAILED,
			"failed to create import history: %v", err)
	}

	defer func() {
		if finalError != nil {
			_ = l.importHistory.Update(ctx, &importhistory.UpdateParams{
				ID:           history.ID,
				State:        customerpb.ImportHistory_FAILED,
				ErrorMessage: proto.String(finalError.Error())})
		}
	}()
	// 获取原始数据
	originalData, err := l.getOriginalData(ctx, req.UploadFileURI)
	if err != nil {
		finalError = errs.Newf(customerpb.ErrCode_ERR_CODE_GET_ORIGINAL_DATA_FAILED,
			"failed to get original data: %v", err)

		return
	}
	// 获取依赖数据
	deps, err := l.getImportDependency(ctx, originalData, req.Organization, req.StaffID)
	if err != nil {
		finalError = errs.Newf(customerpb.ErrCode_ERR_CODE_QUERY_DEPENDENCY_DATA_FAILED,
			"failed to get import dependency: %v", err)

		return
	}

	// 校验并预处理数据
	preImportData, err := l.handleData(ctx, req.Organization, originalData, deps, req.Options)
	if err != nil {
		finalError = errs.Newf(customerpb.ErrCode_ERR_CODE_DATA_VALIDATION_FAILED,
			"failed to handle data: %v", err)

		return
	}
	// 导入数据
	if err := l.importData(ctx, history, preImportData, deps.Operator); err != nil {
		finalError = errs.Newf(customerpb.ErrCode_ERR_CODE_WRITE_DATA_FAILED,
			"failed to import data: %v", err)

		return
	}

	return nil
}

func (l *Logic) getOriginalData(ctx context.Context, uploadFileURI string) ([]map[string]string, error) {
	// 获取 bytes 形式的 csv 文件内容并解析为 []map[string]string
	fileBytes, err := l.file.DownloadFile(ctx, uploadFileURI)
	if err != nil {
		return nil, err
	}

	// 将 []byte 转换为 CSV reader
	reader := csv.NewReader(bytes.NewReader(fileBytes))
	reader.TrimLeadingSpace = true // 去除字段前的空格

	// 读取所有 CSV 数据
	records, err := reader.ReadAll()
	if err != nil {
		return nil, errs.Newf(customerpb.ErrCode_ERR_CODE_PARSE_CSV_FILE_FAILED,
			"failed to parse CSV file: %v", err)
	}

	if len(records) <= 1 {
		return nil, errs.Newf(customerpb.ErrCode_ERR_CODE_PARSE_CSV_FILE_FAILED, "CSV file has no data")
	}

	// 第一行作为表头
	headers := records[0]
	originalData := []map[string]string{{}}
	log.InfoContextf(ctx, "headers: %v", headers)
	// 将表头处理为小写的 header 并移除尾部的 ' (required)'
	for i, header := range headers {
		standardHeader := strings.TrimSuffix(strings.ToLower(strings.TrimSpace(header)), " (required)")
		headers[i] = standardHeader
		originalData[0][standardHeader] = ""
	}

	// 将剩余行转换为 []map[string]string 格式
	for i := 1; i < len(records); i++ {
		record := records[i]
		rowData := make(map[string]string)

		// 为每一行创建 header -> value 的映射
		for j, header := range headers {
			var value string
			if j < len(record) {
				value = record[j]
			}
			rowData[header] = strings.TrimSpace(value)
		}
		originalData = append(originalData, rowData)
	}
	log.InfoContextf(ctx, "originalData: %v", originalData)

	return originalData, nil
}

type importDependency struct {
	labelToCustomField map[string]*customfield.DefinitionWithOptions
	nameToLifeCycle    map[string]*lifecycle.CustomerLifeCycle
	nameToActionState  map[string]*actionstate.CustomerActionState
	nameToSource       map[string]*source.CustomerSource
	nameToStaff        map[string]*organizationpb.StaffModel
	nameToBusiness     map[string]*organizationpb.LocationModel
	emailToCustomer    map[string]*customer.BusinessCustomer
	Operator           *organizationpb.StaffModel
}

func (l *Logic) getImportDependency(ctx context.Context, originalData []map[string]string,
	organization *customerpb.OrganizationRef, operatorStaffID int64) (*importDependency, error) {
	// 查询 organization 下的相关依赖数据
	customFields, err := l.customField.List(ctx, &customfield.DefinitionListFilter{
		Organizations:   []*customerpb.OrganizationRef{organization},
		AssociationType: []customerpb.CustomField_AssociationType{customerpb.CustomField_LEAD},
		States:          []customerpb.CustomField_State{customerpb.CustomField_ACTIVE},
	}, &customfield.Pagination{PageSize: 1000}, nil)
	if err != nil {
		return nil, err
	}
	staffs, err := l.organization.ListStaffs(ctx, organization.GetId())
	if err != nil {
		return nil, err
	}
	businesses, err := l.organization.ListBusinesses(ctx, organization.GetId())
	if err != nil {
		return nil, err
	}
	sources, err := l.source.List(ctx, &source.ListFilter{CompanyIDs: []int64{organization.GetId()}})
	if err != nil {
		return nil, err
	}
	lifeCycles, err := l.lifecycle.List(ctx,
		&lifecycle.ListLifeCyclesDatum{CompanyIDs: []int64{organization.GetId()}})
	if err != nil {
		return nil, err
	}
	actionStates, err := l.actionState.List(ctx,
		&actionstate.ListActionStatesDatum{CompanyIDs: []int64{organization.GetId()}})
	if err != nil {
		return nil, err
	}
	// 查询 email 已存在的客户
	var emails []string
	for _, row := range originalData[1:] {
		if email := row["email"]; email != "" {
			emails = append(emails, email)
		}
	}
	// 组装为 importDependency
	deps := &importDependency{
		labelToCustomField: make(map[string]*customfield.DefinitionWithOptions),
		nameToSource:       make(map[string]*source.CustomerSource),
		nameToLifeCycle:    make(map[string]*lifecycle.CustomerLifeCycle),
		nameToActionState:  make(map[string]*actionstate.CustomerActionState),
		nameToStaff:        make(map[string]*organizationpb.StaffModel),
		nameToBusiness:     make(map[string]*organizationpb.LocationModel),
		emailToCustomer:    make(map[string]*customer.BusinessCustomer),
	}

	for _, customField := range customFields.Data {
		deps.labelToCustomField[customField.Definition.FieldLabel] = customField
	}
	for _, staff := range staffs {
		deps.nameToStaff[staff.GetFirstName()+" "+staff.GetLastName()] = staff
		if staff.GetId() == operatorStaffID {
			deps.Operator = staff
		}
	}
	for _, business := range businesses {
		deps.nameToBusiness[business.GetName()] = business
	}
	for _, source := range sources {
		deps.nameToSource[source.SourceName] = source
	}
	for _, lifeCycle := range lifeCycles {
		deps.nameToLifeCycle[lifeCycle.Name] = lifeCycle
	}
	for _, actionState := range actionStates {
		deps.nameToActionState[actionState.Name] = actionState
	}
	if len(emails) > 0 {
		customers, err := l.customer.ListCustomers(ctx, &customer.Query{},
			&customer.Filter{Emails: emails, Type: customerpbv1.Customer_LEAD.Enum()}, nil,
			&customer.LoadOptions{
				WithAddresses:    true,
				WithContacts:     true,
				WithCustomFields: true})
		if err != nil {
			return nil, errs.Newf(customerpb.ErrCode_ERR_CODE_QUERY_DEPENDENCY_DATA_FAILED,
				"failed to check existing customers: %v", err)
		}
		for _, customer := range customers {
			deps.emailToCustomer[*customer.Email] = customer
		}
	}

	log.InfoContextf(ctx, "deps: %v", deps)

	return deps, nil
}

type preImportData struct {
	customers []*customer.BusinessCustomer
}

func (l *Logic) handleData(ctx context.Context, organization *customerpb.OrganizationRef,
	originalData []map[string]string, deps *importDependency, options *customerpb.ImportCustomersRequest_Options) (
	*preImportData, error) {
	// 1. 校验表头是否匹配 Built in 和 custom field，以及依赖字段是否存在
	if err := l.validateData(originalData, deps); err != nil {
		return nil, err
	}

	// 2. 根据 Options 组装客户数据
	preImportData := &preImportData{
		customers: make([]*customer.BusinessCustomer, 0, len(originalData[1:])),
	}
	now := time.Now().Unix()

	for _, row := range originalData[1:] {
		email := row["email"]

		existingCustomer, exists := deps.emailToCustomer[email]
		if exists && options.GetOverwriteOnConflict() {
			overwrittenCustomer, err := overwriteCustomer(existingCustomer, row, deps, now)
			if err != nil {
				return nil, err
			}
			preImportData.customers = append(preImportData.customers, overwrittenCustomer)

			continue
		}
		if exists {
			continue
		}
		createCustomer, err := createCustomer(row, organization, deps, now)
		if err != nil {
			return nil, err
		}
		preImportData.customers = append(preImportData.customers, createCustomer)
	}

	log.InfoContextf(ctx, "preImportData: %v", preImportData)

	return preImportData, nil
}

var builtInHeaderRequired = map[string]bool{
	"first name":         false,
	"last name":          false,
	"phone":              false,
	"email":              true,
	"life cycle":         true,
	"action state":       false,
	"preferred business": true,
	"source":             false,
	"agent":              false,
	"address line 1":     false,
	"address line 2":     false,
	"city":               false,
	"state":              false,
	"zipcode":            false,
	"country":            false,
}

// validateData 校验表头是否匹配 Built in 和 custom field, 并校验必填字段
func (l *Logic) validateData(originalData []map[string]string, deps *importDependency) error {
	for header := range originalData[0] {
		// 检查是否是内置字段
		if _, isBuiltIn := builtInHeaderRequired[header]; isBuiltIn {
			continue
		}

		// 检查是否是自定义字段
		if _, isCustomField := deps.labelToCustomField[header]; isCustomField {
			continue
		}

		// 如果都不是，返回错误
		return errs.Newf(customerpb.ErrCode_ERR_CODE_DATA_VALIDATION_FAILED, "unknown header: %s", header)
	}
	for _, row := range originalData[1:] {
		for field, required := range builtInHeaderRequired {
			if required && row[field] == "" {
				return errs.Newf(customerpb.ErrCode_ERR_CODE_DATA_VALIDATION_FAILED,
					"required built-in field '%s' is empty", field)
			}
		}
		for field, customField := range deps.labelToCustomField {
			if customField.Definition.IsRequired && row[field] == "" {
				return errs.Newf(customerpb.ErrCode_ERR_CODE_DATA_VALIDATION_FAILED,
					"required custom field '%s' is empty", field)
			}
			if customField.Definition.FieldType == customerpb.CustomField_SELECT {
				for _, option := range customField.Options {
					if option.Value.GetString_() == row[field] {
						break
					}
				}

				return errs.Newf(customerpb.ErrCode_ERR_CODE_DATA_VALIDATION_FAILED,
					"invalid custom field option '%s' value: %s", field, row[field])
			}
		}
		if row["life cycle"] != "" && deps.nameToLifeCycle[row["life cycle"]] == nil {
			return errs.Newf(customerpb.ErrCode_ERR_CODE_DATA_VALIDATION_FAILED,
				"life cycle '%s' not found", row["life cycle"])
		}
		if row["preferred business"] != "" && deps.nameToBusiness[row["preferred business"]] == nil {
			return errs.Newf(customerpb.ErrCode_ERR_CODE_DATA_VALIDATION_FAILED,
				"business '%s' not found", row["preferred business"])
		}
		if row["action state"] != "" && deps.nameToActionState[row["action state"]] == nil {
			return errs.Newf(customerpb.ErrCode_ERR_CODE_DATA_VALIDATION_FAILED,
				"action state '%s' not found", row["action state"])
		}
		if row["source"] != "" && deps.nameToSource[row["source"]] == nil {
			return errs.Newf(customerpb.ErrCode_ERR_CODE_DATA_VALIDATION_FAILED,
				"source '%s' not found", row["source"])
		}
		if row["agent"] != "" && deps.nameToStaff[row["agent"]] == nil {
			return errs.Newf(customerpb.ErrCode_ERR_CODE_DATA_VALIDATION_FAILED,
				"agent '%s' not found", row["agent"])
		}
	}

	return nil
}

func overwriteCustomer(existingCustomer *customer.BusinessCustomer, row map[string]string,
	deps *importDependency, now int64) (*customer.BusinessCustomer, error) {
	// 更新基本信息
	existingCustomer.FirstName = row["first name"]
	existingCustomer.LastName = row["last name"]
	existingCustomer.UpdateTime = now

	if row["preferred business"] != "" {
		existingCustomer.BusinessID = int(deps.nameToBusiness[row["preferred business"]].GetId())
	}
	if row["life cycle"] != "" {
		existingCustomer.CustomizeLifeCycleID = &deps.nameToLifeCycle[row["life cycle"]].ID
	}
	if row["source"] != "" {
		existingCustomer.ReferralSourceID = int(deps.nameToSource[row["source"]].ID)
	}
	if row["action state"] != "" {
		existingCustomer.CustomizeActionStateID = &deps.nameToActionState[row["action state"]].ID
	}
	if row["agent"] != "" {
		existingCustomer.AllocateStaffID = deps.nameToStaff[row["agent"]].GetId()
	}
	// 如果没有地址信息，则不更新地址
	if hasAddressInfo(row) {
		// 如果原来没有地址，则创建一个地址
		if len(existingCustomer.Addresses) == 0 {
			address := &customer.Address{
				BusinessID: existingCustomer.BusinessID,
				Address1:   row["address line 1"],
				Address2:   row["address line 2"],
				City:       row["city"],
				State:      row["state"],
				Zipcode:    row["zipcode"],
				Country:    row["country"],
				Status:     int8(customerpbv1.Address_NORMAL.Number()),
				IsPrimary:  pointer.Get(int8(customerpbv1.Address_PRIMARY.Number())),
				CreateTime: now,
				UpdateTime: now,
				CompanyID:  existingCustomer.CompanyID,
			}
			existingCustomer.Addresses = []*customer.Address{address}
		} else {
			// 如果有地址，则仅更新 primary address
			for _, addr := range existingCustomer.Addresses {
				if *addr.IsPrimary == int8(customerpbv1.Address_PRIMARY.Number()) {
					addr.Address1 = row["address line 1"]
					addr.Address2 = row["address line 2"]
					addr.City = row["city"]
					addr.State = row["state"]
					addr.Zipcode = row["zipcode"]
					addr.Country = row["country"]
					addr.UpdateTime = now

					break
				}
			}
		}
	}
	// 更新联系方式
	if row["phone"] != "" {
		for _, contact := range existingCustomer.Contacts {
			if contact.IsPrimary == int8(customerpbv1.CustomerContact_PRIMARY.Number()) {
				contact.PhoneNumber = row["phone"]
				contact.UpdateTime = now

				break
			}
		}
	}
	customFields, err := getCustomFields(row, deps)
	if err != nil {
		return nil, err
	}
	if existingCustomer.CustomFields != nil {
		existingCustomer.CustomFields.Fields = customFields
	} else {
		existingCustomer.CustomFields = &customer.CustomFields{
			CustomerID: existingCustomer.ID,
			CompanyID:  existingCustomer.CompanyID,
			Fields:     customFields,
		}
	}

	return existingCustomer, nil
}

func getCustomFields(row map[string]string, deps *importDependency) (*structpb.Struct, error) {
	customFields := make(map[string]any)
	for _, field := range deps.labelToCustomField {
		switch field.Definition.FieldType {
		case customerpb.CustomField_TIME, customerpb.CustomField_DATETIME:
			t, err := time.Parse(time.RFC3339, row[field.Definition.FieldLabel])
			if err != nil {
				return nil, err
			}
			v, err := protoToNativeJSON(&customerpb.CustomField_Value{
				Value: &customerpb.CustomField_Value_TimestampTime{
					TimestampTime: timestamppb.New(t),
				},
			})
			if err != nil {
				return nil, err
			}
			customFields[field.Definition.FieldCode] = v
		case customerpb.CustomField_DATE:
			t, err := time.Parse(time.DateOnly, row[field.Definition.FieldLabel])
			if err != nil {
				// 格外兼容一下 2023/01/01 这种格式
				t, err = time.Parse("2006/01/02", row[field.Definition.FieldLabel])
				if err != nil {
					return nil, err
				}
			}
			v, err := protoToNativeJSON(&customerpb.CustomField_Value{
				Value: &customerpb.CustomField_Value_TimestampTime{
					TimestampTime: timestamppb.New(t),
				},
			})
			if err != nil {
				return nil, err
			}
			customFields[field.Definition.FieldCode] = v
		default:
			v, err := protoToNativeJSON(&customerpb.CustomField_Value{
				Value: &customerpb.CustomField_Value_String_{
					String_: row[field.Definition.FieldLabel],
				},
			})
			if err != nil {
				return nil, err
			}
			customFields[field.Definition.FieldCode] = v

		}
	}

	fields, err := structpb.NewStruct(customFields)
	if err != nil {
		return nil, err
	}

	return fields, nil
}

// 将任意 protobuf message 转成 JSON 原生类型（map/string/float/bool/[]any/...）
func protoToNativeJSON(msg proto.Message) (any, error) {
	b, err := protojson.Marshal(msg)
	if err != nil {
		return nil, err
	}
	var v any
	if err := json.Unmarshal(b, &v); err != nil {
		return nil, err
	}

	return v, nil
}

// hasAddressInfo 检查是否提供了地址信息
func hasAddressInfo(row map[string]string) bool {
	return row["address line 1"] != "" || row["city"] != ""
}

// createCustomer 从行数据创建新客户
func createCustomer(row map[string]string, organization *customerpb.OrganizationRef,
	deps *importDependency, now int64) (*customer.BusinessCustomer, error) {
	customFields, err := getCustomFields(row, deps)
	if err != nil {
		return nil, err
	}
	create := &customer.BusinessCustomer{
		FirstName:    row["first name"],
		LastName:     row["last name"],
		Email:        proto.String(row["email"]),
		CompanyID:    organization.GetId(),
		Type:         customerpbv1.Customer_LEAD,
		Status:       int8(customerpbv1.Customer_NORMAL.Number()),
		CustomerCode: random.UpperString(8),
		CreateTime:   now,
		UpdateTime:   now,
		CustomFields: &customer.CustomFields{
			CompanyID: organization.GetId(),
			Fields:    customFields,
		},
		CustomizeActionStateID: proto.Int64(0),
	}

	if hasAddressInfo(row) {
		create.Addresses = []*customer.Address{{
			Address1:   row["address line 1"],
			Address2:   row["address line 2"],
			City:       row["city"],
			State:      row["state"],
			Zipcode:    row["zipcode"],
			Country:    row["country"],
			Status:     int8(customerpbv1.Address_NORMAL.Number()),
			IsPrimary:  pointer.Get(int8(customerpbv1.Address_PRIMARY.Number())),
			CreateTime: now,
			UpdateTime: now,
			CompanyID:  organization.GetId(),
		}}
	}
	if row["phone"] != "" {
		create.Contacts = []*customer.Contact{{
			FirstName:       row["first name"],
			LastName:        row["last name"],
			PhoneNumber:     row["phone"],
			Email:           row["email"],
			Type:            int8(customerpbv1.CustomerContact_MAIN.Number()),
			IsPrimary:       int8(customerpbv1.CustomerContact_PRIMARY.Number()),
			Status:          int8(customerpbv1.CustomerContact_NORMAL.Number()),
			CreateTime:      now,
			UpdateTime:      now,
			CompanyID:       organization.GetId(),
			E164PhoneNumber: row["phone"],
		}}
	}

	if row["preferred business"] != "" {
		create.BusinessID = int(deps.nameToBusiness[row["preferred business"]].GetId())
	}
	if row["life cycle"] != "" {
		create.CustomizeLifeCycleID = &deps.nameToLifeCycle[row["life cycle"]].ID
	}
	if row["source"] != "" {
		create.ReferralSourceID = int(deps.nameToSource[row["source"]].ID)
	}
	if row["action state"] != "" {
		create.CustomizeActionStateID = &deps.nameToActionState[row["action state"]].ID
	}
	if row["agent"] != "" {
		create.AllocateStaffID = deps.nameToStaff[row["agent"]].GetId()
	}

	return create, nil
}

func (l *Logic) importData(ctx context.Context, history *importhistory.ImportHistory,
	preImportData *preImportData, operator *organizationpb.StaffModel) error {
	if preImportData == nil || len(preImportData.customers) == 0 {
		return l.importHistory.Update(ctx, &importhistory.UpdateParams{
			ID:    history.ID,
			State: customerpb.ImportHistory_SUCCEEDED,
		})
	}
	var customerToCreate []*customer.BusinessCustomer
	var customerToUpdate []*customer.BusinessCustomer
	for _, customer := range preImportData.customers {
		if customer.ID > 0 {
			customerToUpdate = append(customerToUpdate, customer)
		} else {
			customerToCreate = append(customerToCreate, customer)
		}
	}
	log.InfoContextf(ctx, "customerToCreate: %v", customerToCreate)
	log.InfoContextf(ctx, "customerToUpdate: %v", customerToUpdate)
	// 使用事务批量导入数据
	if err := l.customer.Tx(ctx, func(repo customer.Repository) error {
		if err := repo.ImportCustomers(ctx, preImportData.customers); err != nil {
			return err
		}

		return l.importHistory.Update(ctx, &importhistory.UpdateParams{
			ID:    history.ID,
			State: customerpb.ImportHistory_SUCCEEDED,
		})
	}); err != nil {
		return err
	}

	return l.syncData(ctx, customerToCreate, customerToUpdate, operator)
}

func (l *Logic) syncData(ctx context.Context, customerToCreate []*customer.BusinessCustomer,
	customerToUpdate []*customer.BusinessCustomer, operator *organizationpb.StaffModel) error {
	for _, customer := range customerToCreate {
		_ = pool.Submit(func() {
			newCtx := context.WithoutCancel(ctx)
			// collect history log
			if _, err := l.historylog.Create(newCtx, &historylog.CreateHistoryLogDatum{
				CustomerID:          customer.ID,
				CustomerName:        customerutils.ConvCustomerName(customer.FirstName, customer.LastName),
				CustomerPhoneNumber: customer.Contacts[0].PhoneNumber,
				BusinessID:          int64(customer.BusinessID),
				CompanyID:           customer.CompanyID,
				Action: &customerpbv1.HistoryLog_Action{
					Action: &customerpbv1.HistoryLog_Action_Create{
						Create: &customerpbv1.HistoryLog_Create{},
					},
				},
				StaffID:    customer.AllocateStaffID,
				Source:     customerpbv1.HistoryLog_STAFF.Enum(),
				SourceID:   proto.Int64(operator.GetId()),
				SourceName: proto.String(operator.GetFirstName() + " " + operator.GetLastName()),
			}); err != nil {
				log.ErrorContextf(newCtx, "Create Log err, customerID:%d, err:%+v", customer.ID, err)
			}
			if err := l.search.SyncES(newCtx, customer.ID, searchpb.OperationType_CREATE); err != nil {
				log.ErrorContextf(newCtx, "SyncES err, customerID:%d, err:%+v", customer.ID, err)
			}
			if err := l.producer.SendCustomerCreateMessage(newCtx, &customerproducer.CustomerCreateMessageDatum{
				CustomerID: customer.ID,
				CompanyID:  customer.CompanyID,
				BusinessID: int64(customer.BusinessID),
			}); err != nil {
				log.ErrorContextf(newCtx, "SendCustomerCreateMessage err, customerID:%d, err:%+v", customer.ID, err)
			}
		})
	}
	for _, customer := range customerToUpdate {
		_ = pool.Submit(func() {
			newCtx := context.WithoutCancel(ctx)
			if err := l.search.SyncES(newCtx, customer.ID, searchpb.OperationType_UPDATE); err != nil {
				log.ErrorContextf(ctx, "SyncES err, customerID:%d, err:%+v", customer.ID, err)
			}
		})
	}

	return nil
}

func (l *Logic) ListImportHistories(ctx context.Context, req *ListImportHistoriesRequest) (
	*ListImportHistoriesResponse, error) {
	filter, pagination, err := req.buildFilterAndPagination()
	if err != nil {
		return nil, err
	}
	result, err := l.importHistory.List(ctx, filter, pagination)
	if err != nil {
		return nil, err
	}

	return buildListImportHistoriesResponse(result, pagination), nil
}

func buildListImportHistoriesResponse(result *importhistory.ListResult,
	pagination *importhistory.Pagination) *ListImportHistoriesResponse {
	importHistories := make([]*ImportHistory, 0, len(result.ImportHistories))
	for _, history := range result.ImportHistories {
		h := &ImportHistory{}
		h.FromDB(history)
		importHistories = append(importHistories, h)
	}
	var nextToken *string
	if pagination != nil && *result.Total > int64(pagination.PageNumber*pagination.PageSize) {
		nextToken = proto.String(strconv.FormatInt(int64(pagination.PageNumber+1), 10))
	}

	return &ListImportHistoriesResponse{
		ImportHistories: importHistories,
		TotalSize:       result.Total,
		NextToken:       nextToken,
	}
}
