load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "contact_tag",
    srcs = [
        "contact_tag.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/logic/contact_tag",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres",
        "//backend/app/customer/repo/postgres/contact_tag",
        "//backend/common/rpc/framework/errs",
        "//backend/proto/customer/v2:customer",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "contact_tag_test",
    srcs = ["contact_tag_test.go"],
    embed = [":contact_tag"],
    deps = [
        "//backend/app/customer/repo/postgres",
        "//backend/app/customer/repo/postgres/contact_tag",
        "//backend/app/customer/repo/postgres/contact_tag/mock",
        "//backend/proto/customer/v2:customer",
        "@com_github_stretchr_testify//require",
        "@io_gorm_gorm//:gorm",
        "@org_uber_go_mock//gomock",
    ],
)
