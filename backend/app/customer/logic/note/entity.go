package note

import customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"

// CreateNoteDatum 创建参数
type CreateNoteDatum struct {
	CompanyID    int64
	BusinessID   int64
	CustomerID   int64
	Note         string
	CreateSource *customerpb.SystemSource
}

// UpdateNoteDatum 更新参数
type UpdateNoteDatum struct {
	NoteID       int64
	Note         *string
	UpdateSource *customerpb.SystemSource
}

// ListNotesDatum 查询参数
type ListNotesDatum struct {
	CustomerIDs []int64
	IDs         []int64
}

// DeleteNoteDatum 删除参数
type DeleteNoteDatum struct {
	NoteID  int64
	StaffID int64
}
