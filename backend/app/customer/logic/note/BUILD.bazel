load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "note",
    srcs = [
        "entity.go",
        "note.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/logic/note",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres/note",
        "//backend/common/rpc/framework/log",
        "//backend/proto/customer/v2:customer",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
