package contact

import (
	"context"
	"errors"
	"time"

	"google.golang.org/grpc/codes"
	"gorm.io/gorm"

	contacttag "github.com/MoeGolibrary/moego/backend/app/customer/logic/contact_tag"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	contactrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact"
	contacttagrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact_tag"
	contacttagrel "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact_tag_rel"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Logic struct {
	contactRepo       contactrepo.Repository
	contactTagRepo    contacttagrepo.Repository
	contactTagRelRepo contacttagrel.Repository
	txManager         postgres.TransactionManager
}

func New() *Logic {
	return &Logic{
		contactRepo:       contactrepo.New(),
		contactTagRepo:    contacttagrepo.New(),
		contactTagRelRepo: contacttagrel.New(),
		txManager:         postgres.NewTxManager(),
	}
}

func NewByParams(
	contactRepo contactrepo.Repository,
	contactTagRepo contacttagrepo.Repository,
	contactTagRelRepo contacttagrel.Repository,
) *Logic {
	return &Logic{
		contactRepo:       contactRepo,
		contactTagRepo:    contactTagRepo,
		contactTagRelRepo: contactTagRelRepo,
	}
}

func (l *Logic) WithTx(tx *gorm.DB) *Logic {
	return &Logic{
		contactRepo:       l.contactRepo.WithTx(tx),
		contactTagRepo:    l.contactTagRepo.WithTx(tx),
		contactTagRelRepo: l.contactTagRelRepo.WithTx(tx),
		txManager:         l.txManager,
	}
}

func (l *Logic) Create(ctx context.Context, contact *Contact) (*Contact, error) {
	// todo: check if the contact already exists, wait tag develop
	now := time.Now().UTC()
	contact.State = customerpb.Contact_ACTIVE
	contact.CreatedTime = now
	contact.UpdatedTime = now
	dbContact := contact.ToDB()

	var resContact *contactrepo.Contact
	ops := make([]func(context.Context, *gorm.DB) error, 0)
	var tagIDs []int64

	// 第一步：创建contact
	ops = append(ops, func(ctx context.Context, tx *gorm.DB) error {
		var err error
		resContact, err = l.contactRepo.WithTx(tx).Create(ctx, dbContact)

		return err
	})

	// 第二步：处理tags（查找系统内置tag或创建自定义tag）
	if len(dbContact.Tags) > 0 {
		ops = append(ops, func(ctx context.Context, tx *gorm.DB) error {
			tagBindingOps, bindingTagIDs, err := l.processTagBinding(dbContact.Tags, resContact)
			if err != nil {
				return err
			}
			tagIDs = bindingTagIDs

			// 执行tag处理操作
			for _, tagOp := range tagBindingOps {
				if err := tagOp(ctx, tx); err != nil {
					return err
				}
			}

			return nil
		})
	}

	// 第三步：创建contact-tag关系
	if len(dbContact.Tags) > 0 {
		for i := range dbContact.Tags {
			idx := i
			ops = append(ops, func(ctx context.Context, tx *gorm.DB) error {
				if tagIDs[idx] > 0 {
					rel := &contacttagrel.ContactTagRel{
						OrganizationType: resContact.OrganizationType,
						OrganizationID:   resContact.OrganizationID,
						ContactID:        resContact.ID,
						TagID:            tagIDs[idx],
					}
					_, err := l.contactTagRelRepo.WithTx(tx).Create(ctx, rel)

					return err
				}

				return nil
			})
		}
	}

	// 执行事务
	if err := l.txManager.ExecuteInTransaction(ctx, ops); err != nil {
		return nil, err
	}

	// 转换结果
	resultContact := convertToContact(resContact)
	tags, err := l.associateTags(ctx, resultContact)
	if err != nil {
		return nil, err
	}
	resultContact.Tags = tags

	return resultContact, nil
}

// CreateWithoutTX 非事务方法，需要上层保证事务
func (l *Logic) CreateWithoutTX(ctx context.Context, contact *Contact) (*Contact, error) {
	// todo: check if the contact already exists, wait tag develop
	// add contact
	now := time.Now().UTC()
	contact.State = customerpb.Contact_ACTIVE
	contact.CreatedTime = now
	contact.UpdatedTime = now
	dbContact := contact.ToDB()
	resContact, err := l.contactRepo.Create(ctx, dbContact)
	if err != nil {
		return nil, err
	}

	// add tag and rel
	for _, tag := range dbContact.Tags {
		tagID := tag.ID
		if tagID == 0 {
			// add tag
			resTag, err := l.contactTagRepo.Create(ctx, tag)
			if err != nil {
				return nil, err
			}
			tagID = resTag.ID
		}

		// add tag rel contact
		rel := &contacttagrel.ContactTagRel{
			OrganizationType: resContact.OrganizationType,
			OrganizationID:   resContact.OrganizationID,
			ContactID:        resContact.ID,
			TagID:            tagID,
		}
		if _, err := l.contactTagRelRepo.Create(ctx, rel); err != nil {
			return nil, err
		}
	}

	// convert to res
	resultContact := convertToContact(resContact)
	tags, err := l.associateTags(ctx, resultContact)
	if err != nil {
		return nil, err
	}
	resultContact.Tags = tags

	return resultContact, nil
}

func (l *Logic) Get(ctx context.Context, id int64) (*Contact, error) {
	dbContact, err := l.contactRepo.Get(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errs.New(customerpb.ErrCode_ERR_CODE_CONTACT_NOT_FOUND)
		}

		return nil, err
	}
	contact := convertToContact(dbContact)
	tags, err := l.associateTags(ctx, contact)
	if err != nil {
		return nil, err
	}
	contact.Tags = tags

	return contact, nil
}

func (l *Logic) List(ctx context.Context, req *ListContactsRequest) (*ListContactsResponse, error) {
	if req.Filter == nil {
		return nil, errs.Newm(codes.InvalidArgument, "filter is required")
	}
	if req.OrderBy == nil {
		req.OrderBy = &ListContactsOrderBy{
			Field:     customerpb.ListContactsRequest_Sorting_CREATED_TIME,
			Direction: customerpb.Direction_DESC,
		}
	}
	dbContacts, err := l.contactRepo.ListByCursor(ctx, &contactrepo.ListFilter{
		IDs:             req.Filter.IDs,
		CustomerIDs:     req.Filter.CustomerIDs,
		States:          req.Filter.States,
		Phones:          req.Filter.Phones,
		Emails:          req.Filter.Emails,
		OrganizationRef: req.Filter.OrganizationRef,
	}, &contactrepo.Pagination{
		PageSize:        req.Pagination.PageSize,
		Cursor:          customerutils.DecodeCursor(req.Pagination.Cursor),
		ReturnTotalSize: req.Pagination.ReturnTotalSize,
	}, &contactrepo.OrderBy{
		Field:     req.OrderBy.Field,
		Direction: req.OrderBy.Direction,
	})
	if err != nil {
		return nil, err
	}
	contacts := convertToContacts(dbContacts.Data)
	// 组装tag 到contact
	for _, c := range contacts {
		tags, err := l.associateTags(ctx, c)
		if err != nil {
			return nil, err
		}
		c.Tags = tags
	}

	result := &ListContactsResponse{
		Contacts: contacts,
		HasNext:  dbContacts.HasNext,
	}
	if dbContacts.TotalCount != nil {
		result.TotalSize = dbContacts.TotalCount
	}

	if dbContacts.HasNext && len(dbContacts.Data) > 0 {
		lastContact := dbContacts.Data[len(dbContacts.Data)-1]
		cursor := postgres.Cursor{
			ID:        lastContact.ID,
			CreatedAt: lastContact.CreatedTime,
		}
		result.NextToken = cursor.EncodeCursor()
	}

	return result, nil
}

func (l *Logic) Update(ctx context.Context, updateRef *UpdateContactRequest) (*Contact, error) {
	dbContact, err := l.Get(ctx, updateRef.ID)
	if err != nil {
		return nil, err
	}

	now := time.Now().UTC()
	dbContact.Email = updateRef.Email
	dbContact.Phone = updateRef.Phone
	dbContact.GivenName = updateRef.GivenName
	dbContact.FamilyName = updateRef.FamilyName
	dbContact.UpdatedTime = now
	dbContact.Note = updateRef.Note
	dbContact.State = updateRef.State
	dbContact.Tags = make([]*contacttag.ContactTag, 0, len(updateRef.TagIDs))
	for _, tagID := range updateRef.TagIDs {
		tag, err := l.contactTagRepo.Get(ctx, tagID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, errs.New(customerpb.ErrCode_ERR_CODE_CONTACT_TAG_NOT_FOUND)
			}

			return nil, err
		}
		t := &contacttag.ContactTag{}
		dbContact.Tags = append(dbContact.Tags, t.Load(tag))
	}
	// update contact
	updatedContact, err := l.contactRepo.Update(ctx, dbContact.ToDB())
	log.DebugContextf(ctx, "updatedContact: %+v", updatedContact)
	if err != nil {
		return nil, err
	}
	result := convertToContact(updatedContact)
	result.Tags, err = l.associateTags(ctx, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (l *Logic) Delete(ctx context.Context, id int64) error {
	_, err := l.Get(ctx, id)
	if err != nil {
		return err
	}
	err = l.contactRepo.Delete(ctx, id)
	if err != nil {
		return err
	}

	return nil
}

func convertToContacts(dbContacts []*contactrepo.Contact) []*Contact {
	contacts := make([]*Contact, 0, len(dbContacts))
	for _, dbContact := range dbContacts {
		contacts = append(contacts, convertToContact(dbContact))
	}

	return contacts
}

func convertToContact(dbContact *contactrepo.Contact) *Contact {
	return &Contact{
		ID:          dbContact.ID,
		CustomerID:  dbContact.CustomerID,
		GivenName:   dbContact.GivenName,
		FamilyName:  dbContact.FamilyName,
		Email:       dbContact.Email,
		Phone:       dbContact.Phone,
		IsSelf:      dbContact.IsSelf,
		State:       dbContact.State,
		Note:        dbContact.Note,
		Title:       dbContact.Title,
		DeletedTime: dbContact.DeletedTime,
		CreatedTime: dbContact.CreatedTime,
		UpdatedTime: dbContact.UpdatedTime,
	}
}

func (l *Logic) associateTags(ctx context.Context, contact *Contact) ([]*contacttag.ContactTag, error) {
	contactID := contact.ID
	tagRels, err := l.contactTagRelRepo.List(ctx, &contacttagrel.ListFilter{
		ContactID: contactID,
	})
	if err != nil {
		return nil, err
	}
	tagIDs := make([]int64, 0, len(tagRels))
	for _, tagRel := range tagRels {
		tagIDs = append(tagIDs, tagRel.TagID)
	}
	dbTags, err := l.contactTagRepo.ListByCursor(ctx, &contacttagrepo.ListFilter{
		IDs: tagIDs,
	}, &contacttagrepo.Pagination{
		PageSize: int32(len(tagRels)),
	}, &contacttagrepo.OrderBy{
		Field:     customerpb.ListContactTagsRequest_Sorting_CREATED_TIME,
		Direction: customerpb.Direction_ASC,
	})
	if err != nil {
		return nil, err
	}

	tags := make([]*contacttag.ContactTag, 0, len(dbTags.Data))
	for _, tag := range dbTags.Data {
		t := &contacttag.ContactTag{}
		tags = append(tags, t.Load(tag))
	}

	return tags, nil
}

// processTagBinding 处理tag绑定逻辑
func (l *Logic) processTagBinding(tags []*contacttagrepo.ContactTag,
	resContact *contactrepo.Contact) ([]func(context.Context, *gorm.DB) error, []int64, error) {
	if len(tags) == 0 {
		return nil, nil, nil
	}

	ops := make([]func(context.Context, *gorm.DB) error, 0)
	tagIDs := make([]int64, len(tags))

	for i, tag := range tags {
		idx := i
		t := tag

		if t.Type != customerpb.ContactTag_CUSTOM {
			// 系统内置类型：查找现有的系统tag，找不到则创建
			ops = append(ops, func(ctx context.Context, tx *gorm.DB) error {
				existingTags, err := l.contactTagRepo.WithTx(tx).ListByCursor(ctx, &contacttagrepo.ListFilter{
					Types:            []customerpb.ContactTag_Type{t.Type},
					OrganizationType: customerpb.OrganizationRef_SYSTEM,
					OrganizationID:   0,
					States:           []customerpb.ContactTag_State{customerpb.ContactTag_ACTIVE},
				}, &contacttagrepo.Pagination{
					PageSize: 1,
				}, &contacttagrepo.OrderBy{
					Field:     customerpb.ListContactTagsRequest_Sorting_CREATED_TIME,
					Direction: customerpb.Direction_DESC,
				})

				if err != nil {
					return err
				}

				if len(existingTags.Data) > 0 {
					// 找到现有的系统tag，直接使用
					tagIDs[idx] = existingTags.Data[0].ID
				}

				return nil
			})
		} else {
			// 自定义类型：根据ID判断是否需要创建
			if t.ID == 0 {
				// ID为0，创建新的自定义tag
				ops = append(ops, func(ctx context.Context, tx *gorm.DB) error {
					if t.OrganizationType == customerpb.OrganizationRef_TYPE_UNSPECIFIED {
						t.OrganizationType = resContact.OrganizationType
					}
					if t.OrganizationID == 0 {
						t.OrganizationID = resContact.OrganizationID
					}

					newTag, err := l.contactTagRepo.WithTx(tx).Create(ctx, t)
					if err != nil {
						return err
					}
					tagIDs[idx] = newTag.ID

					return nil
				})
			} else {
				// ID不为0，直接使用该ID
				tagIDs[i] = t.ID
			}
		}
	}

	return ops, tagIDs, nil
}
