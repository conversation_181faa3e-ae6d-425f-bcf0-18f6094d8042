package tag

import (
	"context"
	"time"

	funk "github.com/thoas/go-funk"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	activityrel "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/activity_rel"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/tag"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

// Logic 逻辑层
type Logic struct {
	tagRepo         tag.ReadWriter
	activityRelRepo activityrel.ReadWriter
}

// New 构造函数
func New() *Logic {
	return &Logic{
		tagRepo:         tag.New(),
		activityRelRepo: activityrel.New(),
	}
}

// Create 创建 Tag
func (l *Logic) Create(ctx context.Context, datum *CreateTagDatum) (*customerpb.Tag, error) {
	if datum == nil || datum.CompanyID <= 0 || datum.Name == "" {
		log.ErrorContextf(ctx, "Create Tag params is invalid, datum:%+v", datum)

		return nil, status.Error(codes.InvalidArgument, "params is invalid")
	}

	// check name
	existNameList, err := l.tagRepo.List(ctx, &tag.ListTagsDatum{
		CompanyIDs: []int64{datum.CompanyID},
		Names:      []string{datum.Name},
	})
	if err != nil {
		log.ErrorContextf(ctx, "Create Tag List same name err, err: %+v", err)

		return nil, err
	}
	if len(existNameList) > 0 {
		log.InfoContextf(ctx, "Create Tag name is exist, datum: %+v", datum)

		return nil, errs.New(customerpb.ErrCode_ERR_CODE_TAG_NAME_EXIST)
	}

	dbTag := &tag.Tag{
		CompanyID:  datum.CompanyID,
		BusinessID: datum.BusinessID,
		Name:       datum.Name,
		Sort:       datum.Sort,
		CreateBy:   datum.StaffID,
		UpdateBy:   datum.StaffID,
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
	}
	if err := l.tagRepo.Create(ctx, dbTag); err != nil {
		return nil, err
	}

	return convTagPB(dbTag), nil
}

// Update 更新 Tag
func (l *Logic) Update(ctx context.Context, datumList []*UpdateTagDatum, companyID int64) error {
	// check
	if companyID <= 0 || len(datumList) == 0 {
		log.ErrorContextf(ctx, "Update Tag params is empty")

		return status.Error(codes.InvalidArgument, "params is empty")
	}

	// conv db entity
	names := make([]string, 0, len(datumList))
	idChangeNameMap := make(map[int64]*UpdateTagDatum, len(datumList))
	updates := make([]*tag.Tag, 0, len(datumList))
	for _, datum := range datumList {
		if datum == nil || datum.TagID <= 0 ||
			(datum.Name == nil && datum.Sort == nil) {
			log.ErrorContextf(ctx, "Update Tag params is invalid, datum:%+v", datum)

			return status.Error(codes.InvalidArgument, "params is invalid")
		}
		dbTag := &tag.Tag{
			ID:         datum.TagID,
			UpdateBy:   datum.StaffID,
			UpdateTime: time.Now(),
		}
		if datum.Name != nil {
			if *datum.Name == "" {
				log.ErrorContextf(ctx, "Update Tag name is empty")

				return status.Error(codes.InvalidArgument, "name is empty")
			}
			dbTag.Name = *datum.Name
			names = append(names, *datum.Name)
			idChangeNameMap[datum.TagID] = datum
		}
		if datum.Sort != nil {
			if *datum.Sort <= 0 {
				log.ErrorContextf(ctx, "Update Tag sort is invalid, sort:%d", *datum.Sort)

				return status.Error(codes.InvalidArgument, "sort is invalid")
			}
			dbTag.Sort = *datum.Sort
		}
		updates = append(updates, dbTag)
	}

	// check names
	if len(names) > 0 {
		existNameList, err := l.tagRepo.List(ctx, &tag.ListTagsDatum{
			CompanyIDs: []int64{companyID},
			Names:      names,
		})
		if err != nil {
			log.ErrorContextf(ctx, "Update Tag List same name err, err: %+v", err)

			return err
		}
		for _, existName := range existNameList {
			// existName source is the same update id and name, skip
			if idChangeNameMap[existName.ID] != nil && *idChangeNameMap[existName.ID].Name == existName.Name {
				continue
			}
			log.InfoContextf(ctx, "Update Tag name is exist, names: %+v", names)

			return errs.New(customerpb.ErrCode_ERR_CODE_TAG_NAME_EXIST)
		}
	}

	// save to db
	if err := l.tagRepo.WithTransaction(ctx, func(api tag.ReadWriter) error {
		for _, update := range updates {
			if err := api.Update(ctx, update); err != nil {
				return err
			}
		}

		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "Update Tag WithTransaction err, err:%+v", err)

		return err
	}

	return nil
}

// List 查询 Tag 列表
func (l *Logic) List(ctx context.Context, datum *ListTagsDatum) ([]*customerpb.Tag, error) {
	if datum == nil || (len(datum.CompanyIDs) == 0 && len(datum.IDs) == 0) {
		log.ErrorContextf(ctx, "List Tag params is invalid, datum:%+v", datum)

		return nil, status.Error(codes.InvalidArgument, "params is invalid")
	}

	tags, err := l.tagRepo.List(ctx, &tag.ListTagsDatum{
		CompanyIDs: datum.CompanyIDs,
		IDs:        datum.IDs,
	})
	if err != nil {
		return nil, err
	}

	return convTagsPB(tags), nil
}

// Delete 删除 Tag
func (l *Logic) Delete(ctx context.Context, datum *DeleteTagDatum) error {
	if datum == nil || datum.TagID <= 0 {
		log.ErrorContextf(ctx, "Delete Tag params is invali, ddatum:%+v", datum)

		return status.Error(codes.InvalidArgument, "params is invalid")
	}
	if err := l.tagRepo.Delete(ctx, datum.TagID, datum.StaffID); err != nil {
		return err
	}

	return nil
}

func (l *Logic) ListCustomerTags(ctx context.Context, customerIDs []int64) ([]*customerpb.CustomerTag, error) {
	// check
	if len(customerIDs) == 0 {
		log.ErrorContextf(ctx, "ListCustomerTags params invalid, customerIDs:%+v", customerIDs)

		return nil, status.Error(codes.InvalidArgument, "params is invalid")
	}

	// get rels
	rels, err := l.activityRelRepo.List(ctx, &activityrel.ListActivityRelDatum{
		CustomerIDs:  customerIDs,
		ActivityType: customerutils.ToPointer(activityrel.TagActivityRelType),
	})
	if err != nil {
		return nil, err
	}
	if len(rels) == 0 {
		return []*customerpb.CustomerTag{}, nil
	}

	// get tags map
	tagIDs := make([]int64, 0, len(rels))
	for _, rel := range rels {
		tagIDs = append(tagIDs, rel.ActivityID)
	}
	tags, err := l.tagRepo.List(ctx, &tag.ListTagsDatum{
		IDs: funk.UniqInt64(tagIDs),
	})
	if err != nil {
		return nil, err
	}
	tagMap := make(map[int64]*tag.Tag, len(tags))
	for _, t := range tags {
		tagMap[t.ID] = t
	}

	return convCustomerTagsPB(rels, tagMap), nil
}

func convCustomerTagsPB(rels []*activityrel.ActivityRel, tagMap map[int64]*tag.Tag) []*customerpb.CustomerTag {
	res := make([]*customerpb.CustomerTag, 0, len(rels))
	for _, rel := range rels {
		res = append(res, convCustomerTagPB(rel, tagMap))
	}

	return res
}

func convCustomerTagPB(rel *activityrel.ActivityRel, tagMap map[int64]*tag.Tag) *customerpb.CustomerTag {
	return &customerpb.CustomerTag{
		Id:         rel.ID,
		Tag:        convTagPB(tagMap[rel.ActivityID]),
		CustomerId: rel.CustomerID,
	}
}

// convTagsPB repo Tag -> pb Tag
func convTagsPB(tags []*tag.Tag) []*customerpb.Tag {
	res := make([]*customerpb.Tag, 0, len(tags))
	for _, t := range tags {
		if t == nil {
			continue
		}
		res = append(res, convTagPB(t))
	}

	return res
}

func convTagPB(t *tag.Tag) *customerpb.Tag {
	if t == nil {
		return nil
	}

	return &customerpb.Tag{
		Id:   t.ID,
		Name: t.Name,
		Sort: t.Sort,
	}
}
