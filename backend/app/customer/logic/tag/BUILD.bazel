load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "tag",
    srcs = [
        "entity.go",
        "tag.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/logic/tag",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres/activity_rel",
        "//backend/app/customer/repo/postgres/tag",
        "//backend/app/customer/utils",
        "//backend/common/rpc/framework/errs",
        "//backend/common/rpc/framework/log",
        "//backend/proto/customer/v2:customer",
        "@com_github_thoas_go_funk//:go-funk",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
    ],
)
