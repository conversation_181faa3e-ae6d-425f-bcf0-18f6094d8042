package customfield

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Definition struct {
	ID               int64
	OrganizationType customerpb.OrganizationRef_Type
	OrganizationID   int64
	AssociationType  customerpb.CustomField_AssociationType
	FieldCode        string
	FieldLabel       string
	FieldType        customerpb.CustomField_Type
	IsRequired       bool
	DefaultValue     *customerpb.CustomField_Value
	DisplayOrder     int
	HelpText         string
	State            customerpb.CustomField_State
	DeletedTime      *time.Time
	CreatedTime      time.Time
	UpdatedTime      time.Time
	Source           customerpb.CustomField_Source
}

func (d *Definition) ToPB() *customerpb.CustomField {
	if d == nil {
		return nil
	}
	customField := &customerpb.CustomField{
		Id: d.ID,
		Organization: &customerpb.OrganizationRef{
			Type: d.OrganizationType,
			Id:   d.OrganizationID,
		},
		AssociationType: d.AssociationType,
		Code:            d.FieldCode,
		Label:           d.Field<PERSON>,
		Type:            d.FieldType,
		IsRequired:      d.IsRequired,
		State:           d.State,
		DisplayOrder:    int32(d.DisplayOrder),
		HelpText:        d.HelpText,
		Source:          d.Source,
		DefaultValue:    d.DefaultValue,
		CreateTime:      timestamppb.New(d.CreatedTime),
		UpdateTime:      timestamppb.New(d.UpdatedTime),
	}
	// 处理删除时间
	if d.DeletedTime != nil {
		customField.DeleteTime = timestamppb.New(*d.DeletedTime)
	}

	return customField
}

type Option struct {
	ID          int64
	FieldID     int64
	Value       *customerpb.CustomField_Value
	Label       string
	SortOrder   int
	State       customerpb.CustomField_State
	CreatedTime time.Time
	UpdatedTime time.Time
	DeletedTime *time.Time
}

func (o *Option) ToPB() *customerpb.CustomField_Option {
	if o == nil {
		return nil
	}

	return &customerpb.CustomField_Option{
		Value:     o.Value,
		Label:     o.Label,
		SortOrder: int32(o.SortOrder),
		State:     o.State,
	}
}

type DefinitionWithOptions struct {
	Definition *Definition
	Options    []*Option
}

func (o *DefinitionWithOptions) ToPB() *customerpb.CustomField {
	if o == nil || o.Definition == nil {
		return nil
	}
	customField := o.Definition.ToPB()
	if customField == nil {
		return nil
	}
	for _, opt := range o.Options {
		if pbOpt := opt.ToPB(); pbOpt != nil {
			customField.Options = append(customField.Options, pbOpt)
		}
	}

	return customField
}

type GetDefinitionParams struct {
	ID int64
}

type ListDefinitionsFilter struct {
	IDs              []int64
	OrganizationType customerpb.OrganizationRef_Type
	OrganizationID   int64
	AssociationType  customerpb.CustomField_AssociationType
	FieldNames       []string
	States           []customerpb.CustomField_State
}

type ListDefinitionsPagination struct {
	PageSize        int32
	Cursor          string
	ReturnTotalSize bool
}

type ListDefinitionsOrderBy struct {
	Field     customerpb.ListCustomFieldsRequest_Sorting_Field
	Direction customerpb.Direction
}

type ListDefinitionsParams struct {
	Filter     *ListDefinitionsFilter
	Pagination *ListDefinitionsPagination
	OrderBy    *ListDefinitionsOrderBy
}

type ListDefinitionsResponse struct {
	Definitions []*DefinitionWithOptions `json:"definitions"`
	HasNext     bool                     `json:"has_next"`
	NextToken   string                   `json:"next_token"`
	TotalSize   *int64                   `json:"total_size"`
}
