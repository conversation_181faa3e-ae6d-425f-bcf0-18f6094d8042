package customfield

import (
	"context"
	"errors"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	repocf "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customfield"
	customfieldmock "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customfield/mock"
	redismock "github.com/MoeGolibrary/moego/backend/app/customer/repo/redis/mock"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

func newTestDefinitionWithOptions() *DefinitionWithOptions {
	return &DefinitionWithOptions{
		Definition: &Definition{
			ID:               1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
			OrganizationID:   1001,
			AssociationType:  customerpb.CustomField_CUSTOMER,
			FieldCode:        "test_field",
			FieldLabel:       "测试字段",
			FieldType:        customerpb.CustomField_SELECT, // 修改为SELECT类型以匹配选项
			IsRequired:       true,
			DefaultValue: &customerpb.CustomField_Value{
				Value: &customerpb.CustomField_Value_String_{String_: "option1"},
			}, // 修改默认值为选项值
			DisplayOrder: 1,
			HelpText:     "帮助文本",
			State:        customerpb.CustomField_ACTIVE,
			CreatedTime:  time.Now(),
			UpdatedTime:  time.Now(),
		},
		Options: []*Option{
			{
				ID:      11,
				FieldID: 1,
				Value: &customerpb.CustomField_Value{
					Value: &customerpb.CustomField_Value_String_{String_: "option1"},
				}, // 添加选项值
				Label:       "选项1",
				SortOrder:   1,
				State:       customerpb.CustomField_ACTIVE,
				CreatedTime: time.Now(),
				UpdatedTime: time.Now(),
			},
		},
	}
}

// newTestDefinitionWithoutOptions 创建一个不带选项的测试数据（用于非SELECT类型字段）
func newTestDefinitionWithoutOptions() *DefinitionWithOptions {
	return &DefinitionWithOptions{
		Definition: &Definition{
			ID:               1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
			OrganizationID:   1001,
			AssociationType:  customerpb.CustomField_CUSTOMER,
			FieldCode:        "test_text_field",
			FieldLabel:       "测试文本字段",
			FieldType:        customerpb.CustomField_SHORT_TEXT,
			IsRequired:       true,
			DefaultValue: &customerpb.CustomField_Value{
				Value: &customerpb.CustomField_Value_String_{String_: "default text"},
			},
			DisplayOrder: 1,
			HelpText:     "文本字段帮助",
			State:        customerpb.CustomField_ACTIVE,
			CreatedTime:  time.Now(),
			UpdatedTime:  time.Now(),
		},
		Options: []*Option{}, // 空选项列表
	}
}

func TestNew(t *testing.T) {
	t.Run("New方法", func(t *testing.T) {
		postgres.SetDB(&gorm.DB{})
		logic := New()
		require.NotNil(t, logic)
	})
}

func TestLogic_Create(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	repo := customfieldmock.NewMockRepository(ctrl)
	redisMock := redismock.NewMockAPI(ctrl)
	logic := NewByParams(repo, redisMock)

	t.Run("创建SELECT字段成功", func(t *testing.T) {
		in := newTestDefinitionWithOptions()
		repo.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, dwo *repocf.DefinitionWithOptions) (*repocf.DefinitionWithOptions, error) {
				// 验证传入参数的关键字段
				require.NotNil(t, dwo)
				require.NotNil(t, dwo.Definition)
				require.Equal(t, "test_field", dwo.Definition.FieldCode)
				require.Equal(t, "测试字段", dwo.Definition.FieldLabel)
				require.Equal(t, customerpb.CustomField_SELECT, dwo.Definition.FieldType)
				require.Len(t, dwo.Options, 1)
				require.Equal(t, "选项1", dwo.Options[0].Label)
				return toRepoDwoForTest(in), nil
			},
		)
		out, err := logic.Create(context.Background(), in)
		require.NoError(t, err)
		require.NotNil(t, out)
		require.Equal(t, in.Definition.FieldCode, out.Definition.FieldCode)
	})

	t.Run("创建文本字段成功", func(t *testing.T) {
		in := newTestDefinitionWithoutOptions()
		repo.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, dwo *repocf.DefinitionWithOptions) (*repocf.DefinitionWithOptions, error) {
				// 验证传入参数的关键字段
				require.NotNil(t, dwo)
				require.NotNil(t, dwo.Definition)
				require.Equal(t, "test_text_field", dwo.Definition.FieldCode)
				require.Equal(t, "测试文本字段", dwo.Definition.FieldLabel)
				require.Equal(t, customerpb.CustomField_SHORT_TEXT, dwo.Definition.FieldType)
				require.Empty(t, dwo.Options)
				return toRepoDwoForTest(in), nil
			},
		)
		out, err := logic.Create(context.Background(), in)
		require.NoError(t, err)
		require.NotNil(t, out)
		require.Equal(t, in.Definition.FieldCode, out.Definition.FieldCode)
	})

	t.Run("repo返回错误", func(t *testing.T) {
		in := newTestDefinitionWithOptions()
		repo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, errors.New("db error"))
		out, err := logic.Create(context.Background(), in)
		require.Error(t, err)
		require.Nil(t, out)
	})

	t.Run("validation error", func(t *testing.T) {
		in := newTestDefinitionWithOptions()
		// 让验证失败，触发错误
		in.Definition.FieldLabel = "" // 空标签会触发验证错误
		out, err := logic.Create(context.Background(), in)
		require.Error(t, err)
		require.Nil(t, out)
	})
}

func TestLogic_Update(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	repo := customfieldmock.NewMockRepository(ctrl)
	redisMock := redismock.NewMockAPI(ctrl)
	logic := NewByParams(repo, redisMock)

	t.Run("更新SELECT字段成功", func(t *testing.T) {
		in := newTestDefinitionWithOptions()
		repo.EXPECT().Update(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, dwo *repocf.DefinitionWithOptions) (*repocf.DefinitionWithOptions, error) {
				// 验证更新参数
				require.NotNil(t, dwo)
				require.NotNil(t, dwo.Definition)
				require.Equal(t, "test_field", dwo.Definition.FieldCode)
				require.Equal(t, customerpb.CustomField_SELECT, dwo.Definition.FieldType)
				require.Len(t, dwo.Options, 1)
				return toRepoDwoForTest(in), nil
			},
		)
		out, err := logic.Update(context.Background(), in)
		require.NoError(t, err)
		require.NotNil(t, out)
		require.Equal(t, in.Definition.FieldCode, out.Definition.FieldCode)
	})

	t.Run("更新文本字段成功", func(t *testing.T) {
		in := newTestDefinitionWithoutOptions()
		repo.EXPECT().Update(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, dwo *repocf.DefinitionWithOptions) (*repocf.DefinitionWithOptions, error) {
				// 验证更新参数
				require.NotNil(t, dwo)
				require.NotNil(t, dwo.Definition)
				require.Equal(t, "test_text_field", dwo.Definition.FieldCode)
				require.Equal(t, customerpb.CustomField_SHORT_TEXT, dwo.Definition.FieldType)
				require.Empty(t, dwo.Options)
				return toRepoDwoForTest(in), nil
			},
		)
		out, err := logic.Update(context.Background(), in)
		require.NoError(t, err)
		require.NotNil(t, out)
		require.Equal(t, in.Definition.FieldCode, out.Definition.FieldCode)
	})

	t.Run("repo返回错误", func(t *testing.T) {
		in := newTestDefinitionWithOptions()
		repo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil, errors.New("db error"))
		out, err := logic.Update(context.Background(), in)
		require.Error(t, err)
		require.Nil(t, out)
	})

	t.Run("validation error", func(t *testing.T) {
		in := newTestDefinitionWithOptions()
		// 让验证失败，触发错误
		in.Definition.FieldLabel = "" // 空标签会触发验证错误
		out, err := logic.Update(context.Background(), in)
		require.Error(t, err)
		require.Nil(t, out)
	})
}

func TestLogic_Get(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	repo := customfieldmock.NewMockRepository(ctrl)
	redisMock := redismock.NewMockAPI(ctrl)
	logic := NewByParams(repo, redisMock)

	t.Run("获取成功", func(t *testing.T) {
		in := newTestDefinitionWithOptions()
		repo.EXPECT().Get(gomock.Any(), int64(1)).DoAndReturn(
			func(ctx context.Context, id int64) (*repocf.DefinitionWithOptions, error) {
				// 验证传入的ID
				require.Equal(t, int64(1), id)
				return toRepoDwoForTest(in), nil
			},
		)
		out, err := logic.Get(context.Background(), &GetDefinitionParams{ID: 1})
		require.NoError(t, err)
		require.NotNil(t, out)
		require.Equal(t, in.Definition.FieldCode, out.Definition.FieldCode)
	})

	t.Run("repo返回错误", func(t *testing.T) {
		repo.EXPECT().Get(gomock.Any(), int64(1)).Return(nil, errors.New("db error"))
		out, err := logic.Get(context.Background(), &GetDefinitionParams{ID: 1})
		require.Error(t, err)
		require.Nil(t, out)
	})

	t.Run("记录不存在", func(t *testing.T) {
		repo.EXPECT().Get(gomock.Any(), int64(1)).Return(nil, gorm.ErrRecordNotFound)
		out, err := logic.Get(context.Background(), &GetDefinitionParams{ID: 1})
		require.Error(t, err)
		require.Nil(t, out)
		// 检查是否返回了正确的错误码（检查错误码119550）
		require.Contains(t, err.Error(), "119550")
	})
}

func TestLogic_List(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	repo := customfieldmock.NewMockRepository(ctrl)
	redisMock := redismock.NewMockAPI(ctrl)
	logic := NewByParams(repo, redisMock)

	t.Run("列表成功", func(t *testing.T) {
		in := newTestDefinitionWithOptions()
		repo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, filter *repocf.DefinitionListFilter, pagination *repocf.Pagination, orderBy *repocf.DefinitionOrderBy) (*repocf.DefinitionCursorResult, error) {
				// 验证传入的参数
				require.NotNil(t, filter)
				require.Equal(t, customerpb.OrganizationRef_BUSINESS, filter.OrganizationType)
				require.Equal(t, int64(1001), filter.OrganizationID)
				require.Equal(t, customerpb.CustomField_CUSTOMER, filter.AssociationType)
				require.NotNil(t, pagination)
				require.Equal(t, int32(10), pagination.PageSize)
				require.NotNil(t, orderBy)

				return &repocf.DefinitionCursorResult{
					Data:       []*repocf.DefinitionWithOptions{toRepoDwoForTest(in)},
					HasNext:    false,
					TotalCount: nil,
				}, nil
			},
		)
		params := &ListDefinitionsParams{
			Filter: &ListDefinitionsFilter{
				OrganizationType: customerpb.OrganizationRef_BUSINESS,
				OrganizationID:   1001,
				AssociationType:  customerpb.CustomField_CUSTOMER,
			},
			Pagination: &ListDefinitionsPagination{
				PageSize: 10,
			},
			OrderBy: &ListDefinitionsOrderBy{
				Field:     customerpb.ListCustomFieldsRequest_Sorting_FIELD_UNSPECIFIED,
				Direction: customerpb.Direction_DIRECTION_UNSPECIFIED,
			},
		}
		out, err := logic.List(context.Background(), params)
		require.NoError(t, err)
		require.Len(t, out.Definitions, 1)
		require.Equal(t, in.Definition.FieldCode, out.Definitions[0].Definition.FieldCode)
	})

	t.Run("列表成功-有下一页和总数", func(t *testing.T) {
		in := newTestDefinitionWithOptions()
		totalCount := int64(100)
		repo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&repocf.DefinitionCursorResult{
			Data:       []*repocf.DefinitionWithOptions{toRepoDwoForTest(in)},
			HasNext:    true,
			TotalCount: &totalCount,
		}, nil)
		params := &ListDefinitionsParams{
			Filter: &ListDefinitionsFilter{
				OrganizationType: customerpb.OrganizationRef_BUSINESS,
				OrganizationID:   1001,
				AssociationType:  customerpb.CustomField_CUSTOMER,
			},
			Pagination: &ListDefinitionsPagination{
				PageSize: 10,
			},
		}
		out, err := logic.List(context.Background(), params)
		require.NoError(t, err)
		require.Len(t, out.Definitions, 1)
		require.True(t, out.HasNext)
		require.NotNil(t, out.TotalSize)
		require.Equal(t, int64(100), *out.TotalSize)
		require.NotEmpty(t, out.NextToken)
	})

	t.Run("repo返回错误", func(t *testing.T) {
		repo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("db error"))
		params := &ListDefinitionsParams{
			Filter: &ListDefinitionsFilter{
				OrganizationType: customerpb.OrganizationRef_BUSINESS,
				OrganizationID:   1001,
				AssociationType:  customerpb.CustomField_CUSTOMER,
			},
		}
		out, err := logic.List(context.Background(), params)
		require.Error(t, err)
		require.Nil(t, out)
	})

	t.Run("toLogic error", func(t *testing.T) {
		// 构造一个会导致toLogic错误的情况
		in := newTestDefinitionWithOptions()
		repoDwo := toRepoDwoForTest(in)
		// 创建一个会导致转换错误的option
		repoDwo.Options = append(repoDwo.Options, &repocf.Option{
			ID:      999,
			FieldID: 1,
			Label:   "错误选项",
		})

		repo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&repocf.DefinitionCursorResult{
			Data:       []*repocf.DefinitionWithOptions{repoDwo},
			HasNext:    false,
			TotalCount: nil,
		}, nil)

		params := &ListDefinitionsParams{
			Filter: &ListDefinitionsFilter{
				OrganizationType: customerpb.OrganizationRef_BUSINESS,
				OrganizationID:   1001,
			},
		}
		out, err := logic.List(context.Background(), params)
		// 由于toLogic不会返回错误（会吞掉错误），这里应该成功
		require.NoError(t, err)
		require.NotNil(t, out)
	})
}

func TestEntityConvert(t *testing.T) {
	t.Run("toRepo/toLogic全流程", func(t *testing.T) {
		in := newTestDefinitionWithOptions()
		repoDwo, err := toRepo(in)
		require.NoError(t, err)
		logicDwo := toLogic(repoDwo)
		require.Equal(t, in.Definition.FieldCode, logicDwo.Definition.FieldCode)
		// 检查 option
		require.Equal(t, in.Options[0].Label, logicDwo.Options[0].Label)
	})

	t.Run("toRepoDef/toLogicDef序列化反序列化", func(t *testing.T) {
		def := &Definition{
			ID:               1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
			OrganizationID:   1001,
			AssociationType:  customerpb.CustomField_CUSTOMER,
			FieldCode:        "test_field",
			FieldLabel:       "测试字段",
			FieldType:        customerpb.CustomField_SHORT_TEXT,
			IsRequired:       true,
			DefaultValue: &customerpb.CustomField_Value{
				Value: &customerpb.CustomField_Value_String_{String_: "test_value"},
			},
			DisplayOrder: 1,
			HelpText:     "帮助文本",
			State:        customerpb.CustomField_ACTIVE,
			CreatedTime:  time.Now(),
			UpdatedTime:  time.Now(),
		}
		repoDef, err := toRepoDef(def)
		require.NoError(t, err)
		logicDef := toLogicDef(repoDef)
		require.Equal(t, def.FieldCode, logicDef.FieldCode)
		// 检查 DefaultValue 反序列化
		require.NotNil(t, logicDef.DefaultValue)
	})

	t.Run("toRepoOpt/toLogicOpt序列化反序列化", func(t *testing.T) {
		opt := &Option{
			ID:      1,
			FieldID: 1,
			Value: &customerpb.CustomField_Value{
				Value: &customerpb.CustomField_Value_String_{String_: "option_value"},
			},
			Label:     "选项1",
			SortOrder: 1,
			State:     customerpb.CustomField_ACTIVE,
		}
		repoOpt, err := toRepoOpt(opt)
		require.NoError(t, err)
		logicOpt := toLogicOpt(repoOpt)
		require.Equal(t, opt.Label, logicOpt.Label)
		require.NotNil(t, logicOpt.Value)
	})

	t.Run("toRepoDef/toLogicDef nil安全", func(t *testing.T) {
		def, err := toRepoDef(nil)
		require.NoError(t, err)
		require.Nil(t, def)
		logicDef := toLogicDef(nil)
		require.Nil(t, logicDef)
	})
}

func TestEntityConvert_Errors(t *testing.T) {
	t.Run("toRepoDef marshal error", func(t *testing.T) {
		// 由于 DefaultValue 现在是 *customerpb.CustomField_Value 类型，
		// 这个测试用例不再适用，因为 protobuf 类型的序列化不会失败
		// 我们可以跳过这个测试用例或者创建一个不同的错误场景
		def := &Definition{
			FieldCode:  "test",
			FieldLabel: "test",
			FieldType:  customerpb.CustomField_SHORT_TEXT,
		}
		_, err := toRepoDef(def)
		require.NoError(t, err) // 正常情况下不会出错
	})

	t.Run("toLogicDef unmarshal error", func(t *testing.T) {
		// 由于 DefaultValue 现在是 *customerpb.CustomField_Value 类型，
		// 不再需要反序列化JSON，所以这个测试用例不再适用
		repoDef := &repocf.Definition{
			FieldCode: "test",
		}
		// toLogicDef不再返回错误，直接复制字段值
		logicDef := toLogicDef(repoDef)
		require.NotNil(t, logicDef)
	})

	t.Run("toRepoOpt marshal error", func(t *testing.T) {
		// 由于 Option 结构已简化，不再有 ValueMoney 等字段需要序列化
		// 这个测试用例不再适用
		opt := &Option{
			Label: "test",
		}
		_, err := toRepoOpt(opt)
		require.NoError(t, err) // 正常情况下不会出错
	})

	t.Run("toRepoOpt ValueRelation marshal error", func(t *testing.T) {
		// 由于 Option 结构已简化，不再有 ValueRelation 字段需要序列化
		// 这个测试用例不再适用
		opt := &Option{
			Label: "test",
		}
		_, err := toRepoOpt(opt)
		require.NoError(t, err) // 正常情况下不会出错
	})

	t.Run("toRepo nil", func(t *testing.T) {
		out, err := toRepo(nil)
		require.NoError(t, err)
		require.Nil(t, out)
	})

	t.Run("toLogic nil", func(t *testing.T) {
		out := toLogic(nil)
		require.Nil(t, out)
	})

	// toRepo: dwo.Definition 不能序列化
	t.Run("toRepo definition marshal error", func(t *testing.T) {
		// 由于 DefaultValue 现在是 protobuf 类型，不会有序列化错误
		in := &DefinitionWithOptions{
			Definition: &Definition{FieldCode: "test", FieldLabel: "test", FieldType: customerpb.CustomField_SHORT_TEXT},
			Options:    []*Option{},
		}
		out, err := toRepo(in)
		require.NoError(t, err) // 正常情况下不会出错
		require.NotNil(t, out)
	})

	// toRepo: dwo.Options 里有一个 option 不能序列化
	t.Run("toRepo option marshal error", func(t *testing.T) {
		// 由于 Option 结构简化，不会有序列化错误
		in := &DefinitionWithOptions{
			Definition: &Definition{FieldCode: "f", FieldLabel: "test", FieldType: customerpb.CustomField_SHORT_TEXT},
			Options: []*Option{
				{Label: "ok"},
				{Label: "good"},
			},
		}
		out, err := toRepo(in)
		require.NoError(t, err) // 正常情况下不会出错
		require.NotNil(t, out)
	})

	// toLogic: dwo.Definition 反序列化失败
	t.Run("toLogic definition unmarshal error", func(t *testing.T) {
		// 由于 DefaultValue 现在是 protobuf 类型，不需要反序列化
		dwo := &repocf.DefinitionWithOptions{
			Definition: &repocf.Definition{FieldCode: "test"},
			Options:    []*repocf.Option{},
		}
		out := toLogic(dwo)
		require.NotNil(t, out) // toLogic不再返回错误
	})
}

func TestEntityConvert_DeepBranches(t *testing.T) {
	t.Run("toRepo with empty options", func(t *testing.T) {
		in := &DefinitionWithOptions{Definition: &Definition{FieldCode: "f", FieldLabel: "test", FieldType: customerpb.CustomField_SHORT_TEXT}}
		out, err := toRepo(in)
		require.NoError(t, err)
		require.NotNil(t, out)
		require.Empty(t, out.Options)
	})

	t.Run("toRepo with nil option in slice", func(t *testing.T) {
		in := &DefinitionWithOptions{
			Definition: &Definition{FieldCode: "f", FieldLabel: "test", FieldType: customerpb.CustomField_SHORT_TEXT},
			Options:    []*Option{nil},
		}
		out, err := toRepo(in)
		require.NoError(t, err)
		require.NotNil(t, out)
		require.Len(t, out.Options, 1)
		require.Nil(t, out.Options[0])
	})

	t.Run("toLogic with nil option in slice", func(t *testing.T) {
		repoDwo := &repocf.DefinitionWithOptions{
			Definition: &repocf.Definition{FieldCode: "f"},
			Options:    []*repocf.Option{nil},
		}
		out := toLogic(repoDwo)
		require.NotNil(t, out)
		require.Len(t, out.Options, 1)
		require.Nil(t, out.Options[0])
	})

	t.Run("toLogicDef with empty DefaultValue", func(t *testing.T) {
		repoDef := &repocf.Definition{
			FieldCode: "test",
		}
		out := toLogicDef(repoDef)
		require.NotNil(t, out)
	})

	t.Run("toLogicOpt with simple option", func(t *testing.T) {
		repoOpt := &repocf.Option{
			Label: "test",
		}
		out := toLogicOpt(repoOpt)
		require.NotNil(t, out)
	})

	t.Run("toRepoDef with simple definition", func(t *testing.T) {
		def := &Definition{FieldCode: "f", FieldLabel: "test", FieldType: customerpb.CustomField_SHORT_TEXT}
		out, err := toRepoDef(def)
		require.NoError(t, err)
		require.NotNil(t, out)
	})

	t.Run("toRepoOpt with simple option", func(t *testing.T) {
		out, err := toRepoOpt(&Option{Label: "l"})
		require.NoError(t, err)
		require.NotNil(t, out)
	})

	t.Run("toRepoOpt with nil option", func(t *testing.T) {
		out, err := toRepoOpt(nil)
		require.NoError(t, err)
		require.Nil(t, out)
	})

	t.Run("toRepoOpt with protobuf value", func(t *testing.T) {
		opt := &Option{
			Label: "l",
			Value: &customerpb.CustomField_Value{
				Value: &customerpb.CustomField_Value_String_{String_: "test"},
			},
		}
		out, err := toRepoOpt(opt)
		require.NoError(t, err)
		require.NotNil(t, out)
	})

	t.Run("toLogicDef with partial fields", func(t *testing.T) {
		repoDef := &repocf.Definition{FieldCode: "f"}
		out := toLogicDef(repoDef)
		require.NotNil(t, out)
	})

	t.Run("toLogicOpt with partial fields", func(t *testing.T) {
		repoOpt := &repocf.Option{Label: "l"}
		out := toLogicOpt(repoOpt)
		require.NotNil(t, out)
	})

	t.Run("toRepoOpt with complex protobuf value", func(t *testing.T) {
		opt := &Option{
			Label: "l",
			Value: &customerpb.CustomField_Value{
				Value: &customerpb.CustomField_Value_Int64{Int64: 123},
			},
		}
		out, err := toRepoOpt(opt)
		require.NoError(t, err)
		require.NotNil(t, out)
	})
}

func TestLogic_NilParams(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	repo := customfieldmock.NewMockRepository(ctrl)
	redisMock := redismock.NewMockAPI(ctrl)
	logic := NewByParams(repo, redisMock)

	t.Run("Create nil", func(t *testing.T) {
		out, err := logic.Create(context.Background(), nil)
		require.Error(t, err)
		require.Contains(t, err.Error(), "definition with options is nil")
		require.Nil(t, out)
	})

	t.Run("Update nil", func(t *testing.T) {
		out, err := logic.Update(context.Background(), nil)
		require.Error(t, err)
		require.Contains(t, err.Error(), "definition with options is nil")
		require.Nil(t, out)
	})

	t.Run("Get nil params", func(t *testing.T) {
		out, err := logic.Get(context.Background(), nil)
		require.Error(t, err)
		require.Contains(t, err.Error(), "get params is nil")
		require.Nil(t, out)
	})

	t.Run("List nil params", func(t *testing.T) {
		out, err := logic.List(context.Background(), nil)
		require.Error(t, err)
		require.Contains(t, err.Error(), "list params is nil")
		require.Nil(t, out)
	})

	t.Run("repo返回nil", func(t *testing.T) {
		repo.EXPECT().Get(gomock.Any(), int64(2)).Return(nil, nil)
		out, err := logic.Get(context.Background(), &GetDefinitionParams{ID: 2})
		require.NoError(t, err)
		require.Nil(t, out)
	})
}

func TestLogic_BatchUpdate(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	repo := customfieldmock.NewMockRepository(ctrl)
	redisMock := redismock.NewMockAPI(ctrl)
	logic := NewByParams(repo, redisMock)

	t.Run("批量更新成功", func(t *testing.T) {
		in1 := newTestDefinitionWithOptions()
		in1.Definition.ID = 1
		in1.Definition.FieldCode = "select_field_1"
		in2 := newTestDefinitionWithoutOptions()
		in2.Definition.ID = 2
		in2.Definition.FieldCode = "text_field_2"
		updates := []*DefinitionWithOptions{in1, in2}

		// Mock Redis Lock/Unlock
		redisMock.EXPECT().Lock(gomock.Any(), "custom_field:batch_update:lock:org123", gomock.Any()).Return(nil)
		redisMock.EXPECT().Unlock(gomock.Any(), "custom_field:batch_update:lock:org123").Return(nil)

		// Mock repo Update calls
		repo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(toRepoDwoForTest(in1), nil).Times(1)
		repo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(toRepoDwoForTest(in2), nil).Times(1)

		out, err := logic.BatchUpdate(context.Background(), "org123", updates)
		require.NoError(t, err)
		require.Len(t, out, 2)
		require.Equal(t, in1.Definition.FieldCode, out[0].Definition.FieldCode)
		require.Equal(t, in2.Definition.FieldCode, out[1].Definition.FieldCode)
	})

	t.Run("空更新列表", func(t *testing.T) {
		out, err := logic.BatchUpdate(context.Background(), "org123", []*DefinitionWithOptions{})
		require.Error(t, err)
		require.Contains(t, err.Error(), "updates is required")
		require.Nil(t, out)
	})

	t.Run("空parent参数", func(t *testing.T) {
		in := newTestDefinitionWithOptions()
		updates := []*DefinitionWithOptions{in}
		out, err := logic.BatchUpdate(context.Background(), "", updates)
		require.Error(t, err)
		require.Contains(t, err.Error(), "parent is required")
		require.Nil(t, out)
	})

	t.Run("Redis锁获取失败", func(t *testing.T) {
		in := newTestDefinitionWithOptions()
		updates := []*DefinitionWithOptions{in}

		redisMock.EXPECT().Lock(gomock.Any(), "custom_field:batch_update:lock:org123", gomock.Any()).Return(errors.New("lock failed"))

		out, err := logic.BatchUpdate(context.Background(), "org123", updates)
		require.Error(t, err)
		require.Contains(t, err.Error(), "failed to acquire batch update lock")
		require.Nil(t, out)
	})

	t.Run("单个更新失败", func(t *testing.T) {
		in := newTestDefinitionWithOptions()
		updates := []*DefinitionWithOptions{in}

		redisMock.EXPECT().Lock(gomock.Any(), "custom_field:batch_update:lock:org123", gomock.Any()).Return(nil)
		redisMock.EXPECT().Unlock(gomock.Any(), "custom_field:batch_update:lock:org123").Return(nil)
		repo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil, errors.New("update failed"))

		out, err := logic.BatchUpdate(context.Background(), "org123", updates)
		require.Error(t, err)
		require.Contains(t, err.Error(), "update failed")
		require.Nil(t, out)
	})

	t.Run("解锁失败但不影响主流程", func(t *testing.T) {
		in := newTestDefinitionWithOptions()
		updates := []*DefinitionWithOptions{in}

		redisMock.EXPECT().Lock(gomock.Any(), "custom_field:batch_update:lock:org123", gomock.Any()).Return(nil)
		redisMock.EXPECT().Unlock(gomock.Any(), "custom_field:batch_update:lock:org123").Return(errors.New("unlock failed"))
		repo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(toRepoDwoForTest(in), nil)

		out, err := logic.BatchUpdate(context.Background(), "org123", updates)
		require.NoError(t, err) // 解锁失败不应该影响主流程
		require.Len(t, out, 1)
	})
}

func TestLogic_ValidationMethods(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	repo := customfieldmock.NewMockRepository(ctrl)
	redisMock := redismock.NewMockAPI(ctrl)
	logic := NewByParams(repo, redisMock)

	t.Run("validateDefinition - 成功", func(t *testing.T) {
		def := &Definition{
			OrganizationID:  1001,
			AssociationType: customerpb.CustomField_CUSTOMER,
			FieldType:       customerpb.CustomField_SHORT_TEXT,
			FieldLabel:      "测试字段",
		}
		err := logic.validateDefinition(def)
		require.NoError(t, err)
	})

	t.Run("validateDefinition - 空定义", func(t *testing.T) {
		err := logic.validateDefinition(nil)
		require.Error(t, err)
		require.Contains(t, err.Error(), "definition is required")
	})

	t.Run("validateDefinition - 无效组织ID", func(t *testing.T) {
		def := &Definition{
			OrganizationID: 0,
		}
		err := logic.validateDefinition(def)
		require.Error(t, err)
		require.Contains(t, err.Error(), "organization_id is required")
	})

	t.Run("validateDefinition - 无效关联类型", func(t *testing.T) {
		def := &Definition{
			OrganizationID:  1001,
			AssociationType: customerpb.CustomField_ASSOCIATION_TYPE_UNSPECIFIED,
		}
		err := logic.validateDefinition(def)
		require.Error(t, err)
		require.Contains(t, err.Error(), "association_type cannot be UNSPECIFIED")
	})

	t.Run("validateDefinition - 无效字段类型", func(t *testing.T) {
		def := &Definition{
			OrganizationID:  1001,
			AssociationType: customerpb.CustomField_CUSTOMER,
			FieldType:       customerpb.CustomField_TYPE_UNSPECIFIED,
		}
		err := logic.validateDefinition(def)
		require.Error(t, err)
		require.Contains(t, err.Error(), "type cannot be UNSPECIFIED")
	})

	t.Run("validateDefinition - 空字段标签", func(t *testing.T) {
		def := &Definition{
			OrganizationID:  1001,
			AssociationType: customerpb.CustomField_CUSTOMER,
			FieldType:       customerpb.CustomField_SHORT_TEXT,
			FieldLabel:      "",
		}
		err := logic.validateDefinition(def)
		require.Error(t, err)
		require.Contains(t, err.Error(), "field_label is required")
	})

	t.Run("validateOptions - SELECT字段需要选项", func(t *testing.T) {
		err := logic.validateOptions(customerpb.CustomField_SELECT, []*Option{})
		require.Error(t, err)
		require.Contains(t, err.Error(), "SELECT and MULTI_SELECT fields must have options")
	})

	t.Run("validateOptions - MULTI_SELECT字段需要选项", func(t *testing.T) {
		err := logic.validateOptions(customerpb.CustomField_MULTI_SELECT, []*Option{})
		require.Error(t, err)
		require.Contains(t, err.Error(), "SELECT and MULTI_SELECT fields must have options")
	})

	t.Run("validateOptions - 非SELECT字段不能有选项", func(t *testing.T) {
		options := []*Option{{Label: "选项1"}}
		err := logic.validateOptions(customerpb.CustomField_SHORT_TEXT, options)
		require.Error(t, err)
		require.Contains(t, err.Error(), "only SELECT and MULTI_SELECT fields can have options")
	})

	t.Run("validateOptions - SELECT字段有选项成功", func(t *testing.T) {
		options := []*Option{{Label: "选项1"}}
		err := logic.validateOptions(customerpb.CustomField_SELECT, options)
		require.NoError(t, err)
	})

	t.Run("validateOptions - 非SELECT字段无选项成功", func(t *testing.T) {
		err := logic.validateOptions(customerpb.CustomField_SHORT_TEXT, []*Option{})
		require.NoError(t, err)
	})
}

// ToPB方法测试 - 这是之前缺失的主要覆盖率
func TestEntityToPB(t *testing.T) {
	t.Run("Option.ToPB - 完整测试", func(t *testing.T) {
		now := time.Now()

		// 测试空值
		var nilOpt *Option
		require.Nil(t, nilOpt.ToPB())

		// 测试字符串值
		opt := &Option{
			ID:      1,
			FieldID: 10,
			Value: &customerpb.CustomField_Value{
				Value: &customerpb.CustomField_Value_String_{String_: "test_value"},
			},
			Label:       "测试标签",
			SortOrder:   1,
			State:       customerpb.CustomField_ACTIVE,
			CreatedTime: now,
			UpdatedTime: now,
		}
		pb := opt.ToPB()
		require.NotNil(t, pb)
		require.NotNil(t, pb.Value)
		require.Equal(t, "test_value", pb.Value.GetString_())
		require.Equal(t, "测试标签", pb.Label)
		require.Equal(t, int32(1), pb.SortOrder)
		require.Equal(t, customerpb.CustomField_ACTIVE, pb.State)

		// 测试整数值
		opt.Value = &customerpb.CustomField_Value{
			Value: &customerpb.CustomField_Value_Int64{Int64: 123},
		}
		pb = opt.ToPB()
		require.Equal(t, int64(123), pb.Value.GetInt64())

		// 测试浮点值
		opt.Value = &customerpb.CustomField_Value{
			Value: &customerpb.CustomField_Value_DoubleValue{DoubleValue: 123.45},
		}
		pb = opt.ToPB()
		require.Equal(t, 123.45, pb.Value.GetDoubleValue())

		// 测试布尔值
		opt.Value = &customerpb.CustomField_Value{
			Value: &customerpb.CustomField_Value_Bool{Bool: true},
		}
		pb = opt.ToPB()
		require.True(t, pb.Value.GetBool())

		// 测试Money值
		moneyValue := &money.Money{
			CurrencyCode: "USD",
			Units:        100,
			Nanos:        500000000,
		}
		opt.Value = &customerpb.CustomField_Value{
			Value: &customerpb.CustomField_Value_Money{Money: moneyValue},
		}
		pb = opt.ToPB()
		require.NotNil(t, pb.Value.GetMoney())
		require.Equal(t, "USD", pb.Value.GetMoney().CurrencyCode)
		require.Equal(t, int64(100), pb.Value.GetMoney().Units)
		require.Equal(t, int32(500000000), pb.Value.GetMoney().Nanos)

		// 测试Relation值
		relationValue := &customerpb.CustomField_Value_Relation{
			Entity: customerpb.CustomField_Value_Relation_CUSTOMER,
			Id:     456,
		}
		opt.Value = &customerpb.CustomField_Value{
			Value: &customerpb.CustomField_Value_Relation_{Relation: relationValue},
		}
		pb = opt.ToPB()
		require.NotNil(t, pb.Value.GetRelation())
		require.Equal(t, customerpb.CustomField_Value_Relation_CUSTOMER, pb.Value.GetRelation().Entity)
		require.Equal(t, int64(456), pb.Value.GetRelation().Id)

		// 测试LEAD类型的Relation
		leadRelationValue := &customerpb.CustomField_Value_Relation{
			Entity: customerpb.CustomField_Value_Relation_LEAD,
			Id:     789,
		}
		opt.Value = &customerpb.CustomField_Value{
			Value: &customerpb.CustomField_Value_Relation_{Relation: leadRelationValue},
		}
		pb = opt.ToPB()
		require.Equal(t, customerpb.CustomField_Value_Relation_LEAD, pb.Value.GetRelation().Entity)
		require.Equal(t, int64(789), pb.Value.GetRelation().Id)

		// 测试没有任何值的情况
		optEmpty := &Option{
			Label:       "空选项",
			SortOrder:   1,
			State:       customerpb.CustomField_ACTIVE,
			CreatedTime: now,
			UpdatedTime: now,
		}
		pbEmpty := optEmpty.ToPB()
		require.NotNil(t, pbEmpty)
		require.Nil(t, pbEmpty.Value)
		require.Equal(t, "空选项", pbEmpty.Label)
	})

	t.Run("Definition.ToPB - 完整测试", func(t *testing.T) {
		now := time.Now()
		deletedTime := time.Now().Add(time.Hour)

		// 测试空值
		var nilDef *Definition
		require.Nil(t, nilDef.ToPB())

		// 测试完整定义
		def := &Definition{
			ID:               1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
			OrganizationID:   1001,
			AssociationType:  customerpb.CustomField_CUSTOMER,
			FieldCode:        "test_field",
			FieldLabel:       "测试字段",
			FieldType:        customerpb.CustomField_SHORT_TEXT,
			IsRequired:       true,
			DefaultValue: &customerpb.CustomField_Value{
				Value: &customerpb.CustomField_Value_String_{String_: "default_value"},
			},
			DisplayOrder: 5,
			HelpText:     "帮助文本",
			State:        customerpb.CustomField_ACTIVE,
			DeletedTime:  &deletedTime,
			CreatedTime:  now,
			UpdatedTime:  now,
		}

		pb := def.ToPB()
		require.NotNil(t, pb)
		require.Equal(t, int64(1), pb.Id)
		require.Equal(t, customerpb.OrganizationRef_BUSINESS, pb.Organization.Type)
		require.Equal(t, int64(1001), pb.Organization.Id)
		require.Equal(t, customerpb.CustomField_CUSTOMER, pb.AssociationType)
		require.Equal(t, "test_field", pb.Code)
		require.Equal(t, "测试字段", pb.Label)
		require.Equal(t, customerpb.CustomField_SHORT_TEXT, pb.Type)
		require.True(t, pb.IsRequired)
		require.Equal(t, int32(5), pb.DisplayOrder)
		require.Equal(t, "帮助文本", pb.HelpText)
		require.Equal(t, customerpb.CustomField_ACTIVE, pb.State)

		// 检查默认值
		require.NotNil(t, pb.DefaultValue)
		require.Equal(t, "default_value", pb.DefaultValue.GetString_())

		// 检查时间戳
		require.NotNil(t, pb.CreateTime)
		require.NotNil(t, pb.UpdateTime)
		require.NotNil(t, pb.DeleteTime)

		// 测试不同类型的默认值
		// 整数默认值
		def.DefaultValue = &customerpb.CustomField_Value{
			Value: &customerpb.CustomField_Value_Int64{Int64: 123},
		}
		pb = def.ToPB()
		require.Equal(t, int64(123), pb.DefaultValue.GetInt64())

		// 浮点默认值
		def.DefaultValue = &customerpb.CustomField_Value{
			Value: &customerpb.CustomField_Value_DoubleValue{DoubleValue: 123.45},
		}
		pb = def.ToPB()
		require.Equal(t, 123.45, pb.DefaultValue.GetDoubleValue())

		// 布尔默认值
		def.DefaultValue = &customerpb.CustomField_Value{
			Value: &customerpb.CustomField_Value_Bool{Bool: true},
		}
		pb = def.ToPB()
		require.True(t, pb.DefaultValue.GetBool())

		// Money默认值
		moneyObj := &money.Money{
			CurrencyCode: "USD",
			Units:        100,
			Nanos:        500000000,
		}
		def.DefaultValue = &customerpb.CustomField_Value{
			Value: &customerpb.CustomField_Value_Money{Money: moneyObj},
		}
		pb = def.ToPB()
		require.Equal(t, moneyObj, pb.DefaultValue.GetMoney())

		// Relation默认值
		relationObj := &customerpb.CustomField_Value_Relation{
			Entity: customerpb.CustomField_Value_Relation_CUSTOMER,
			Id:     456,
		}
		def.DefaultValue = &customerpb.CustomField_Value{
			Value: &customerpb.CustomField_Value_Relation_{Relation: relationObj},
		}
		pb = def.ToPB()
		require.Equal(t, relationObj, pb.DefaultValue.GetRelation())

		// 时间类型默认值
		timeVal := time.Now()
		def.DefaultValue = &customerpb.CustomField_Value{
			Value: &customerpb.CustomField_Value_TimestampTime{TimestampTime: timestamppb.New(timeVal)},
		}
		pb = def.ToPB()
		require.NotNil(t, pb.DefaultValue.GetTimestampTime())
		require.Equal(t, timeVal.Unix(), pb.DefaultValue.GetTimestampTime().Seconds)

		// 测试没有删除时间的情况
		def.DeletedTime = nil
		pb = def.ToPB()
		require.Nil(t, pb.DeleteTime)

		// 测试没有默认值的情况
		def.DefaultValue = nil
		pb = def.ToPB()
		require.Nil(t, pb.DefaultValue)
	})

	t.Run("DefinitionWithOptions.ToPB - 完整测试", func(t *testing.T) {
		now := time.Now()

		// 测试空值
		var nilDwo *DefinitionWithOptions
		require.Nil(t, nilDwo.ToPB())

		// 测试没有定义的情况
		dwo := &DefinitionWithOptions{}
		require.Nil(t, dwo.ToPB())

		// 测试完整的DefinitionWithOptions
		dwo = &DefinitionWithOptions{
			Definition: &Definition{
				ID:               1,
				OrganizationType: customerpb.OrganizationRef_BUSINESS,
				OrganizationID:   1001,
				AssociationType:  customerpb.CustomField_CUSTOMER,
				FieldCode:        "test_select",
				FieldLabel:       "测试选择字段",
				FieldType:        customerpb.CustomField_SELECT,
				IsRequired:       true,
				DefaultValue: &customerpb.CustomField_Value{
					Value: &customerpb.CustomField_Value_String_{String_: "option1"},
				},
				DisplayOrder: 1,
				HelpText:     "选择帮助",
				State:        customerpb.CustomField_ACTIVE,
				CreatedTime:  now,
				UpdatedTime:  now,
			},
			Options: []*Option{
				{
					ID:      11,
					FieldID: 1,
					Value: &customerpb.CustomField_Value{
						Value: &customerpb.CustomField_Value_String_{String_: "option1"},
					},
					Label:       "选项1",
					SortOrder:   1,
					State:       customerpb.CustomField_ACTIVE,
					CreatedTime: now,
					UpdatedTime: now,
				},
				{
					ID:      12,
					FieldID: 1,
					Value: &customerpb.CustomField_Value{
						Value: &customerpb.CustomField_Value_String_{String_: "option2"},
					},
					Label:       "选项2",
					SortOrder:   2,
					State:       customerpb.CustomField_ACTIVE,
					CreatedTime: now,
					UpdatedTime: now,
				},
			},
		}

		pb := dwo.ToPB()
		require.NotNil(t, pb)
		require.Equal(t, "test_select", pb.Code)
		require.Equal(t, "测试选择字段", pb.Label)
		require.Equal(t, customerpb.CustomField_SELECT, pb.Type)

		// 检查选项
		require.Len(t, pb.Options, 2)
		require.Equal(t, "选项1", pb.Options[0].Label)
		require.Equal(t, "option1", pb.Options[0].Value.GetString_())
		require.Equal(t, int32(1), pb.Options[0].SortOrder)
		require.Equal(t, "选项2", pb.Options[1].Label)
		require.Equal(t, "option2", pb.Options[1].Value.GetString_())
		require.Equal(t, int32(2), pb.Options[1].SortOrder)

		// 测试空选项的情况
		dwo.Options = nil
		pb = dwo.ToPB()
		require.Empty(t, pb.Options)

		// 测试有nil选项的情况
		dwo.Options = []*Option{nil, {Label: "valid"}}
		pb = dwo.ToPB()
		require.Len(t, pb.Options, 1) // nil的选项会被过滤掉
		require.Equal(t, "valid", pb.Options[0].Label)

		// 测试Definition.ToPB返回nil的情况（这种情况实际不会发生，但为了覆盖所有分支）
		dwoBroken := &DefinitionWithOptions{Definition: nil}
		require.Nil(t, dwoBroken.ToPB())
	})
}

// 添加更多边界情况的测试
func TestEntityToPB_EdgeCases(t *testing.T) {
	t.Run("Option ToPB with nil value", func(t *testing.T) {
		opt := &Option{
			Label: "测试",
			Value: nil, // 没有值
		}
		pb := opt.ToPB()
		require.NotNil(t, pb)
		require.Nil(t, pb.Value) // 应该没有值
	})

	t.Run("Option ToPB with complex value", func(t *testing.T) {
		opt := &Option{
			Label: "测试",
			Value: &customerpb.CustomField_Value{
				Value: &customerpb.CustomField_Value_Money{
					Money: &money.Money{
						CurrencyCode: "USD",
						Units:        100,
					},
				},
			},
		}
		pb := opt.ToPB()
		require.NotNil(t, pb)
		require.NotNil(t, pb.Value)
		require.Equal(t, "USD", pb.Value.GetMoney().CurrencyCode)
	})

	t.Run("Definition ToPB with complex default value", func(t *testing.T) {
		def := &Definition{
			ID:         1,
			FieldCode:  "test",
			FieldLabel: "测试",
			FieldType:  customerpb.CustomField_SHORT_TEXT,
			DefaultValue: &customerpb.CustomField_Value{
				Value: &customerpb.CustomField_Value_Money{
					Money: &money.Money{
						CurrencyCode: "EUR",
						Units:        200,
					},
				},
			},
		}
		pb := def.ToPB()
		require.NotNil(t, pb)
		require.NotNil(t, pb.DefaultValue)
		require.Equal(t, "EUR", pb.DefaultValue.GetMoney().CurrencyCode)
	})
}

// 添加更多的转换函数测试以达到100%覆盖率
func TestConversionFunctions_CompleteCoverage(t *testing.T) {
	t.Run("List函数的所有分支", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := customfieldmock.NewMockRepository(ctrl)
		redisMock := redismock.NewMockAPI(ctrl)
		logic := NewByParams(repo, redisMock)

		// 测试没有过滤器的情况
		repo.EXPECT().List(gomock.Any(), nil, gomock.Any(), gomock.Any()).Return(&repocf.DefinitionCursorResult{
			Data:       []*repocf.DefinitionWithOptions{},
			HasNext:    false,
			TotalCount: nil,
		}, nil)

		params := &ListDefinitionsParams{
			Filter: nil, // 没有过滤器
			Pagination: &ListDefinitionsPagination{
				PageSize: 10,
			},
		}
		out, err := logic.List(context.Background(), params)
		require.NoError(t, err)
		require.Empty(t, out.Definitions)

		// 测试没有分页的情况
		repo.EXPECT().List(gomock.Any(), gomock.Any(), nil, gomock.Any()).Return(&repocf.DefinitionCursorResult{
			Data:       []*repocf.DefinitionWithOptions{},
			HasNext:    false,
			TotalCount: nil,
		}, nil)

		params = &ListDefinitionsParams{
			Filter: &ListDefinitionsFilter{
				OrganizationType: customerpb.OrganizationRef_BUSINESS,
			},
			Pagination: nil, // 没有分页
		}
		out, err = logic.List(context.Background(), params)
		require.NoError(t, err)
		require.Empty(t, out.Definitions)

		// 测试没有排序的情况
		repo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any(), nil).Return(&repocf.DefinitionCursorResult{
			Data:       []*repocf.DefinitionWithOptions{},
			HasNext:    false,
			TotalCount: nil,
		}, nil)

		params = &ListDefinitionsParams{
			Filter: &ListDefinitionsFilter{
				OrganizationType: customerpb.OrganizationRef_BUSINESS,
			},
			Pagination: &ListDefinitionsPagination{
				PageSize: 10,
			},
			OrderBy: nil, // 没有排序
		}
		out, err = logic.List(context.Background(), params)
		require.NoError(t, err)
		require.Empty(t, out.Definitions)
	})

	t.Run("toRepoDef的所有分支", func(t *testing.T) {
		// 测试简单定义
		def := &Definition{
			FieldCode:  "test",
			FieldLabel: "test",
			FieldType:  customerpb.CustomField_SHORT_TEXT,
		}
		repoDef, err := toRepoDef(def)
		require.NoError(t, err)
		require.NotNil(t, repoDef)

		// 测试空的DefaultValue
		def.DefaultValue = nil
		repoDef, err = toRepoDef(def)
		require.NoError(t, err)
		require.NotNil(t, repoDef)
		require.Nil(t, repoDef.DefaultValue)
	})

	t.Run("toLogic的所有分支", func(t *testing.T) {
		// 测试dwo.Definition为nil的情况（虽然实际不会发生）
		repoDwo := &repocf.DefinitionWithOptions{
			Definition: nil,
			Options:    []*repocf.Option{},
		}
		out := toLogic(repoDwo)
		require.NotNil(t, out)
		require.Nil(t, out.Definition)
	})

	t.Run("toLogicOpt和toRepoOpt的nil情况", func(t *testing.T) {
		// 测试toLogicOpt的nil情况
		logicOpt := toLogicOpt(nil)
		require.Nil(t, logicOpt)

		// 测试toRepoOpt的nil情况
		repoOpt, err := toRepoOpt(nil)
		require.NoError(t, err)
		require.Nil(t, repoOpt)
	})

	t.Run("List函数HasNext但数据为空的情况", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := customfieldmock.NewMockRepository(ctrl)
		redisMock := redismock.NewMockAPI(ctrl)
		logic := NewByParams(repo, redisMock)

		// 测试HasNext为true但数据为空的情况（不应该生成NextToken）
		repo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&repocf.DefinitionCursorResult{
			Data:       []*repocf.DefinitionWithOptions{}, // 空数据
			HasNext:    true,                              // 但HasNext为true
			TotalCount: nil,
		}, nil)

		params := &ListDefinitionsParams{
			Filter: &ListDefinitionsFilter{
				OrganizationType: customerpb.OrganizationRef_BUSINESS,
			},
			Pagination: &ListDefinitionsPagination{
				PageSize: 10,
			},
		}
		out, err := logic.List(context.Background(), params)
		require.NoError(t, err)
		require.Empty(t, out.Definitions)
		require.True(t, out.HasNext)
		require.Empty(t, out.NextToken) // 数据为空时不应该生成NextToken
	})

	t.Run("toLogic转换错误的情况", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		repo := customfieldmock.NewMockRepository(ctrl)
		redisMock := redismock.NewMockAPI(ctrl)
		logic := NewByParams(repo, redisMock)

		// 构造一个会导致toLogic失败的Definition（虽然实际上toLogic不会失败）
		// 我们可以通过传入nil Definition来测试这个分支，但实际上toLogicDef会处理nil情况
		// 这里我们构造一个正常的情况，因为toLogic实际上不会返回错误
		in := newTestDefinitionWithOptions()
		repo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&repocf.DefinitionCursorResult{
			Data:       []*repocf.DefinitionWithOptions{toRepoDwoForTest(in)},
			HasNext:    false,
			TotalCount: nil,
		}, nil)

		params := &ListDefinitionsParams{
			Filter: &ListDefinitionsFilter{
				OrganizationType: customerpb.OrganizationRef_BUSINESS,
			},
		}
		out, err := logic.List(context.Background(), params)
		require.NoError(t, err)
		require.NotNil(t, out)
	})
}

// GenerateFieldCode 相关函数测试
func TestGenerateFieldCode(t *testing.T) {
	t.Run("空字符串", func(t *testing.T) {
		code := GenerateFieldCode("")
		require.True(t, len(code) > 0)
		require.True(t, strings.HasPrefix(code, "field_"))
		require.Equal(t, 11, len(code)) // "field_" + 5位随机后缀
	})

	t.Run("空白字符串", func(t *testing.T) {
		code := GenerateFieldCode("   ")
		require.True(t, len(code) > 0)
		require.True(t, strings.HasPrefix(code, "field_"))
	})

	t.Run("正常英文标签", func(t *testing.T) {
		code := GenerateFieldCode("User Name")
		require.True(t, len(code) > 0)
		require.True(t, strings.HasPrefix(code, "user_name_"))
		require.Equal(t, 15, len(code)) // "user_name_" + 5位随机后缀
	})

	t.Run("中文标签", func(t *testing.T) {
		code := GenerateFieldCode("用户姓名")
		require.True(t, len(code) > 0)
		// 中文字符会被替换为下划线，然后合并，最终变成空字符串，所以会生成 "field_" + 后缀
		require.True(t, strings.HasPrefix(code, "field_"))
		require.Equal(t, 11, len(code)) // "field_" + 5位随机后缀
	})

	t.Run("特殊字符处理", func(t *testing.T) {
		code := GenerateFieldCode("user@name#test!")
		require.True(t, len(code) > 0)
		require.True(t, strings.Contains(code, "user_name_test_"))
		require.False(t, strings.Contains(code, "@"))
		require.False(t, strings.Contains(code, "#"))
		require.False(t, strings.Contains(code, "!"))
	})

	t.Run("多个下划线合并", func(t *testing.T) {
		code := GenerateFieldCode("user___name")
		require.True(t, len(code) > 0)
		require.True(t, strings.HasPrefix(code, "user_name_"))
		require.False(t, strings.Contains(code, "___"))
	})

	t.Run("数字开头的标签", func(t *testing.T) {
		code := GenerateFieldCode("123test")
		require.True(t, len(code) > 0)
		require.True(t, strings.HasPrefix(code, "f_123test_"))
	})

	t.Run("长标签截断", func(t *testing.T) {
		longLabel := strings.Repeat("a", 50)
		code := GenerateFieldCode(longLabel)
		require.True(t, len(code) > 0)
		// 基础部分最多35个字符 + "_" + 5位后缀 = 最多41个字符
		require.True(t, len(code) <= 41)
		require.True(t, strings.HasSuffix(code, "_"+code[len(code)-5:]))
	})

	t.Run("以下划线结尾的长标签", func(t *testing.T) {
		longLabel := strings.Repeat("a", 34) + "_"
		code := GenerateFieldCode(longLabel)
		require.True(t, len(code) > 0)
		// 应该去掉结尾的下划线
		require.False(t, strings.Contains(code, "__"))
	})

	t.Run("生成的代码唯一性", func(t *testing.T) {
		codes := make(map[string]bool)
		for i := 0; i < 100; i++ {
			code := GenerateFieldCode("test")
			require.False(t, codes[code], "生成了重复的代码: %s", code)
			codes[code] = true
		}
	})
}

func TestToRepoDef_FieldCodeGeneration(t *testing.T) {
	t.Run("FieldCode为空时自动生成", func(t *testing.T) {
		def := &Definition{
			FieldCode:  "", // 空字段代码
			FieldLabel: "测试字段",
			FieldType:  customerpb.CustomField_SHORT_TEXT,
		}
		repoDef, err := toRepoDef(def)
		require.NoError(t, err)
		require.NotNil(t, repoDef)
		require.NotEmpty(t, repoDef.FieldCode)
		require.True(t, strings.HasPrefix(repoDef.FieldCode, "field_"))
	})

	t.Run("FieldCode为空白字符时自动生成", func(t *testing.T) {
		def := &Definition{
			FieldCode:  "   ", // 空白字符
			FieldLabel: "测试字段",
			FieldType:  customerpb.CustomField_SHORT_TEXT,
		}
		repoDef, err := toRepoDef(def)
		require.NoError(t, err)
		require.NotNil(t, repoDef)
		require.NotEmpty(t, repoDef.FieldCode)
		require.True(t, strings.HasPrefix(repoDef.FieldCode, "field_"))
	})

	t.Run("FieldCode不为空时保持原值", func(t *testing.T) {
		def := &Definition{
			FieldCode:  "custom_field_code",
			FieldLabel: "测试字段",
			FieldType:  customerpb.CustomField_SHORT_TEXT,
		}
		repoDef, err := toRepoDef(def)
		require.NoError(t, err)
		require.NotNil(t, repoDef)
		require.Equal(t, "custom_field_code", repoDef.FieldCode)
	})
}

func TestIsLetter(t *testing.T) {
	t.Run("小写字母", func(t *testing.T) {
		require.True(t, isLetter('a'))
		require.True(t, isLetter('z'))
		require.True(t, isLetter('m'))
	})

	t.Run("大写字母", func(t *testing.T) {
		require.True(t, isLetter('A'))
		require.True(t, isLetter('Z'))
		require.True(t, isLetter('M'))
	})

	t.Run("非字母", func(t *testing.T) {
		require.False(t, isLetter('0'))
		require.False(t, isLetter('9'))
		require.False(t, isLetter('_'))
		require.False(t, isLetter('@'))
		require.False(t, isLetter(' '))
	})
}

func TestGenerateShortSuffix(t *testing.T) {
	t.Run("长度正确", func(t *testing.T) {
		suffix := generateShortSuffix()
		require.Equal(t, 5, len(suffix))
	})

	t.Run("只包含十六进制字符", func(t *testing.T) {
		suffix := generateShortSuffix()
		for _, char := range suffix {
			require.True(t, (char >= '0' && char <= '9') || (char >= 'a' && char <= 'f'),
				"字符 %c 不是有效的十六进制字符", char)
		}
	})

	t.Run("唯一性", func(t *testing.T) {
		suffixes := make(map[string]bool)
		for i := 0; i < 100; i++ {
			suffix := generateShortSuffix()
			require.False(t, suffixes[suffix], "生成了重复的后缀: %s", suffix)
			suffixes[suffix] = true
		}
	})
}

func TestGenerateRandomCode(t *testing.T) {
	t.Run("格式正确", func(t *testing.T) {
		code := generateRandomCode()
		require.True(t, strings.HasPrefix(code, "field_"))
		require.Equal(t, 11, len(code)) // "field_" + 5位后缀
	})

	t.Run("唯一性", func(t *testing.T) {
		codes := make(map[string]bool)
		for i := 0; i < 100; i++ {
			code := generateRandomCode()
			require.False(t, codes[code], "生成了重复的代码: %s", code)
			codes[code] = true
		}
	})
}

// toRepoDwoForTest 用于 mock repo 层返回
func toRepoDwoForTest(in *DefinitionWithOptions) *repocf.DefinitionWithOptions {
	repoDef, _ := toRepoDef(in.Definition)
	var repoOpts []*repocf.Option
	for _, opt := range in.Options {
		repoOpt, _ := toRepoOpt(opt)
		repoOpts = append(repoOpts, repoOpt)
	}
	return &repocf.DefinitionWithOptions{
		Definition: repoDef,
		Options:    repoOpts,
	}
}
