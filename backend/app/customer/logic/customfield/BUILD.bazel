load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "customfield",
    srcs = [
        "customfield.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/logic/customfield",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres",
        "//backend/app/customer/repo/postgres/customfield",
        "//backend/app/customer/repo/redis",
        "//backend/app/customer/utils",
        "//backend/common/rpc/framework/errs",
        "//backend/common/rpc/framework/log",
        "//backend/proto/customer/v2:customer",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "customfield_test",
    srcs = ["customfield_test.go"],
    embed = [":customfield"],
    deps = [
        "//backend/app/customer/repo/postgres",
        "//backend/app/customer/repo/postgres/customfield",
        "//backend/app/customer/repo/postgres/customfield/mock",
        "//backend/app/customer/repo/redis/mock",
        "//backend/proto/customer/v2:customer",
        "@com_github_stretchr_testify//require",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_genproto//googleapis/type/money",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_mock//gomock",
    ],
)
