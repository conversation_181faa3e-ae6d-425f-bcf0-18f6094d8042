package customfield

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"time"

	"google.golang.org/grpc/codes"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	repocf "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customfield"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/redis"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

const (
	maxCodeBaseLen = 35
	suffixLen      = 5
)

var (
	nonAlphaNum     = regexp.MustCompile(`[^a-zA-Z0-9]+`)
	multiUnderscore = regexp.MustCompile(`_+`)

	// Redis 锁相关常量 - 只保留批量更新锁
	CustomFieldBatchUpdateLockKey = "custom_field:batch_update:lock:%s" // parent (organization_id)
	CustomFieldLockTTL            = 30 * time.Second
)

// Logic 提供自定义字段的业务逻辑服务
// 只暴露 DefinitionWithOptions 相关接口
type Logic struct {
	customFieldRepo repocf.Repository
	redis           redis.API
}

func New() *Logic {
	return &Logic{
		customFieldRepo: repocf.New(),
		redis:           redis.New(),
	}
}

func NewByParams(customFieldRepo repocf.Repository, redisAPI redis.API) *Logic {
	return &Logic{
		customFieldRepo: customFieldRepo,
		redis:           redisAPI,
	}
}

// Create 创建自定义字段及其选项，包含完整的验证和业务逻辑
func (l *Logic) Create(ctx context.Context, dwo *DefinitionWithOptions) (*DefinitionWithOptions, error) {
	if dwo == nil {
		return nil, errs.Newm(codes.InvalidArgument, "definition with options is nil")
	}

	// 验证请求
	if err := l.validateDefinition(dwo.Definition); err != nil {
		return nil, err
	}

	// 验证选项
	if err := l.validateOptions(dwo.Definition.FieldType, dwo.Options); err != nil {
		return nil, err
	}

	// 设置创建时的默认值
	now := time.Now().UTC()
	dwo.Definition.State = customerpb.CustomField_ACTIVE
	dwo.Definition.CreatedTime = now
	dwo.Definition.UpdatedTime = now

	// 如果没有设置 Source，默认为 CUSTOM
	if dwo.Definition.Source == customerpb.CustomField_SOURCE_UNSPECIFIED {
		dwo.Definition.Source = customerpb.CustomField_CUSTOM
	}

	// 如果没有设置 FieldCode，自动生成
	if strings.TrimSpace(dwo.Definition.FieldCode) == "" {
		dwo.Definition.FieldCode = GenerateFieldCode(dwo.Definition.FieldLabel)
	}

	for _, opt := range dwo.Options {
		if opt.State == customerpb.CustomField_STATE_UNSPECIFIED {
			opt.State = customerpb.CustomField_ACTIVE
		}
		opt.CreatedTime = now
		opt.UpdatedTime = now
	}

	repoDwo, err := toRepo(dwo)
	if err != nil {
		return nil, err
	}
	created, err := l.customFieldRepo.Create(ctx, repoDwo)
	if err != nil {
		return nil, errs.New(customerpb.ErrCode_ERR_CODE_CREATE_CUSTOM_FIELD_FAILED)
	}

	return toLogic(created), nil
}

// Update 更新自定义字段及其选项，包含完整的验证和业务逻辑
func (l *Logic) Update(ctx context.Context, dwo *DefinitionWithOptions) (*DefinitionWithOptions, error) {
	if dwo == nil {
		return nil, errs.Newm(codes.InvalidArgument, "definition with options is nil")
	}

	// 验证定义
	if err := l.validateDefinition(dwo.Definition); err != nil {
		return nil, err
	}

	// 验证选项
	if err := l.validateOptions(dwo.Definition.FieldType, dwo.Options); err != nil {
		return nil, err
	}

	// 设置更新时间
	now := time.Now().UTC()
	dwo.Definition.UpdatedTime = now
	for _, opt := range dwo.Options {
		opt.UpdatedTime = now
	}

	repoDwo, err := toRepo(dwo)
	if err != nil {
		return nil, err
	}
	updated, err := l.customFieldRepo.Update(ctx, repoDwo)
	if err != nil {
		return nil, err
	}

	return toLogic(updated), nil
}

// BatchUpdate 批量更新自定义字段
func (l *Logic) BatchUpdate(
	ctx context.Context, parent string, updates []*DefinitionWithOptions,
) ([]*DefinitionWithOptions, error) {
	if len(updates) == 0 {
		return nil, errs.Newm(codes.InvalidArgument, "updates is required")
	}
	if parent == "" {
		return nil, errs.Newm(codes.InvalidArgument, "parent is required")
	}

	// 获取分布式锁 - 基于 parent (organization_id) 加锁
	lockKey := fmt.Sprintf(CustomFieldBatchUpdateLockKey, parent)
	err := l.redis.Lock(ctx, lockKey, CustomFieldLockTTL)
	if err != nil {
		return nil, errs.Newm(codes.ResourceExhausted, fmt.Sprintf("failed to acquire batch update lock: %v", err))
	}
	defer func() {
		if unlockErr := l.redis.Unlock(ctx, lockKey); unlockErr != nil {
			// 记录解锁失败，但不影响主流程
			log.ErrorContextf(ctx, "failed to unlock custom field batch update lock: %v", unlockErr)
		}
	}()

	var updatedFields []*DefinitionWithOptions
	for _, dwo := range updates {
		updatedField, err := l.Update(ctx, dwo)
		if err != nil {
			return nil, err
		}
		updatedFields = append(updatedFields, updatedField)
	}

	return updatedFields, nil
}

// Get 查询单个自定义字段及其选项
func (l *Logic) Get(ctx context.Context, params *GetDefinitionParams) (*DefinitionWithOptions, error) {
	if params == nil {
		return nil, errs.Newm(codes.InvalidArgument, "get params is nil")
	}
	dwo, err := l.customFieldRepo.Get(ctx, params.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errs.New(customerpb.ErrCode_ERR_CODE_CUSTOM_FIELD_NOT_FOUND)
		}

		return nil, err
	}
	// toLogic永远不会返回错误，所以移除错误处理
	return toLogic(dwo), nil
}

// List 查询自定义字段定义列表及其选项
func (l *Logic) List(ctx context.Context, params *ListDefinitionsParams) (*ListDefinitionsResponse, error) {
	if params == nil {
		return nil, errs.Newm(codes.InvalidArgument, "list params is nil")
	}
	var filter *repocf.DefinitionListFilter
	if params.Filter != nil {
		filter = &repocf.DefinitionListFilter{
			IDs:              params.Filter.IDs,
			OrganizationType: params.Filter.OrganizationType,
			OrganizationID:   params.Filter.OrganizationID,
			AssociationType:  params.Filter.AssociationType,
			FieldNames:       params.Filter.FieldNames,
			States:           params.Filter.States,
		}
	}
	var pagination *repocf.Pagination
	if params.Pagination != nil {
		pagination = &repocf.Pagination{
			PageSize:        params.Pagination.PageSize,
			Cursor:          customerutils.DecodeCursor(params.Pagination.Cursor),
			ReturnTotalSize: params.Pagination.ReturnTotalSize,
		}
	}
	var orderBy *repocf.DefinitionOrderBy
	if params.OrderBy != nil {
		orderBy = &repocf.DefinitionOrderBy{
			Field:     params.OrderBy.Field,
			Direction: params.OrderBy.Direction,
		}
	}

	dbResult, err := l.customFieldRepo.List(ctx, filter, pagination, orderBy)
	if err != nil {
		return nil, err
	}

	var result []*DefinitionWithOptions
	for _, d := range dbResult.Data {
		// toLogic永远不会返回错误，所以移除错误处理
		logicDef := toLogic(d)
		result = append(result, logicDef)
	}

	response := &ListDefinitionsResponse{
		Definitions: result,
		HasNext:     dbResult.HasNext,
		TotalSize:   dbResult.TotalCount,
	}

	// 生成下一页令牌
	if dbResult.HasNext && len(dbResult.Data) > 0 {
		lastDef := dbResult.Data[len(dbResult.Data)-1].Definition
		cursor := postgres.Cursor{
			ID:        lastDef.ID,
			CreatedAt: lastDef.CreatedTime,
		}
		response.NextToken = cursor.EncodeCursor()
	}

	return response, nil
}

// entity转换
func toRepo(dwo *DefinitionWithOptions) (*repocf.DefinitionWithOptions, error) {
	if dwo == nil {
		return nil, nil
	}
	repoDef, err := toRepoDef(dwo.Definition)
	if err != nil {
		return nil, err
	}
	var repoOpts []*repocf.Option
	for _, opt := range dwo.Options {
		repoOpt, err := toRepoOpt(opt)
		if err != nil {
			return nil, err
		}
		repoOpts = append(repoOpts, repoOpt)
	}

	return &repocf.DefinitionWithOptions{
		Definition: repoDef,
		Options:    repoOpts,
	}, nil
}

func toRepoDef(def *Definition) (*repocf.Definition, error) {
	if def == nil {
		return nil, nil
	}
	fieldCode := def.FieldCode
	if strings.TrimSpace(fieldCode) == "" {
		fieldCode = GenerateFieldCode(def.FieldLabel)
	}

	return &repocf.Definition{
		ID:               def.ID,
		OrganizationType: def.OrganizationType,
		OrganizationID:   def.OrganizationID,
		AssociationType:  def.AssociationType,
		FieldCode:        fieldCode,
		FieldLabel:       def.FieldLabel,
		FieldType:        def.FieldType,
		IsRequired:       def.IsRequired,
		DefaultValue:     def.DefaultValue,
		DisplayOrder:     def.DisplayOrder,
		HelpText:         def.HelpText,
		State:            def.State,
		DeletedTime:      def.DeletedTime,
		CreatedTime:      def.CreatedTime,
		UpdatedTime:      def.UpdatedTime,
		Source:           def.Source,
	}, nil
}

func toLogic(dwo *repocf.DefinitionWithOptions) *DefinitionWithOptions {
	if dwo == nil {
		return nil
	}
	// toLogicDef永远不会返回错误，所以移除错误处理
	logicDef := toLogicDef(dwo.Definition)

	return &DefinitionWithOptions{
		Definition: logicDef,
		Options:    toLogicOptions(dwo.Options),
	}
}

func toLogicDef(def *repocf.Definition) *Definition {
	if def == nil {
		return nil
	}

	return &Definition{
		ID:               def.ID,
		OrganizationType: def.OrganizationType,
		OrganizationID:   def.OrganizationID,
		AssociationType:  def.AssociationType,
		FieldCode:        def.FieldCode,
		FieldLabel:       def.FieldLabel,
		FieldType:        def.FieldType,
		IsRequired:       def.IsRequired,
		DefaultValue:     def.DefaultValue,
		DisplayOrder:     def.DisplayOrder,
		HelpText:         def.HelpText,
		State:            def.State,
		DeletedTime:      def.DeletedTime,
		CreatedTime:      def.CreatedTime,
		UpdatedTime:      def.UpdatedTime,
		Source:           def.Source,
	}
}

func toLogicOptions(opts []*repocf.Option) []*Option {
	var result []*Option
	for _, o := range opts {
		logicOpt := toLogicOpt(o)
		result = append(result, logicOpt)
	}

	return result
}

func toLogicOpt(opt *repocf.Option) *Option {
	if opt == nil {
		return nil
	}

	return &Option{
		ID:          opt.ID,
		FieldID:     opt.FieldID,
		Value:       opt.Value,
		Label:       opt.Label,
		SortOrder:   opt.SortOrder,
		State:       opt.State,
		CreatedTime: opt.CreatedTime,
		UpdatedTime: opt.UpdatedTime,
		DeletedTime: opt.DeletedTime,
	}
}

func toRepoOpt(opt *Option) (*repocf.Option, error) {
	if opt == nil {
		return nil, nil
	}

	return &repocf.Option{
		ID:          opt.ID,
		FieldID:     opt.FieldID,
		Value:       opt.Value,
		Label:       opt.Label,
		SortOrder:   opt.SortOrder,
		State:       opt.State,
		CreatedTime: opt.CreatedTime,
		UpdatedTime: opt.UpdatedTime,
		DeletedTime: opt.DeletedTime,
	}, nil
}

func GenerateFieldCode(label string) string {
	if strings.TrimSpace(label) == "" {
		return generateRandomCode()
	}

	code := strings.TrimSpace(label)
	code = strings.ToLower(code)

	code = nonAlphaNum.ReplaceAllString(code, "_")
	code = multiUnderscore.ReplaceAllString(code, "_")
	code = strings.Trim(code, "_")

	if len(code) == 0 {
		return generateRandomCode()
	}

	if len(code) > 0 && !isLetter(code[0]) {
		code = "f_" + code
	}

	if len(code) > maxCodeBaseLen {
		code = code[:maxCodeBaseLen]
		code = strings.TrimRight(code, "_")
	}

	suffix := generateShortSuffix()

	return code + "_" + suffix
}

func isLetter(b byte) bool {
	return (b >= 'a' && b <= 'z') || (b >= 'A' && b <= 'Z')
}

func generateShortSuffix() string {
	bytes := make([]byte, 3) // 3字节 = 6位hex，取前5位
	_, err := rand.Read(bytes)
	if err != nil {
		return generateRandomCode()
	}

	return hex.EncodeToString(bytes)[:suffixLen]
}

func generateRandomCode() string {
	return "field_" + generateShortSuffix()
}

// validateDefinition 验证定义
func (l *Logic) validateDefinition(def *Definition) error {
	if def == nil {
		return errs.Newm(codes.InvalidArgument, "definition is required")
	}
	if def.OrganizationID <= 0 {
		return errs.Newm(codes.InvalidArgument, "organization_id is required")
	}
	if def.AssociationType == customerpb.CustomField_ASSOCIATION_TYPE_UNSPECIFIED {
		return errs.Newm(codes.InvalidArgument, "association_type cannot be UNSPECIFIED")
	}
	if def.FieldType == customerpb.CustomField_TYPE_UNSPECIFIED {
		return errs.Newm(codes.InvalidArgument, "type cannot be UNSPECIFIED")
	}
	if strings.TrimSpace(def.FieldLabel) == "" {
		return errs.Newm(codes.InvalidArgument, "field_label is required")
	}

	return nil
}

// validateOptions 验证选项
func (l *Logic) validateOptions(fieldType customerpb.CustomField_Type, options []*Option) error {
	hasOptions := len(options) > 0
	needsOptions := fieldType == customerpb.CustomField_SELECT ||
		fieldType == customerpb.CustomField_MULTI_SELECT

	if needsOptions && !hasOptions {
		return errs.Newm(codes.InvalidArgument, "SELECT and MULTI_SELECT fields must have options")
	}
	if !needsOptions && hasOptions {
		return errs.Newm(codes.InvalidArgument, "only SELECT and MULTI_SELECT fields can have options")
	}

	return nil
}
