package address

import (
	"encoding/base64"
	"encoding/json"
	"time"

	"google.golang.org/genproto/googleapis/type/latlng"
	"google.golang.org/genproto/googleapis/type/postaladdress"
	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	addressrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/address"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Address struct {
	ID                 int64                           `json:"id"`
	CustomerID         int64                           `json:"customer_id"`
	OrganizationID     int64                           `json:"organization_id"`
	OrganizationType   customerpb.OrganizationRef_Type `json:"organization_type"`
	Revision           int32                           `json:"revision"`
	RegionCode         string                          `json:"region_code"`
	LanguageCode       string                          `json:"language_code"`
	Organization       string                          `json:"organization"`
	PostalCode         string                          `json:"postal_code"`
	SortingCode        string                          `json:"sorting_code"`
	AdministrativeArea string                          `json:"administrative_area"`
	Locality           string                          `json:"locality"`
	Sublocality        string                          `json:"sublocality"`
	AddressLines       []string                        `json:"address_lines"`
	Recipients         []string                        `json:"recipients"`
	Latitude           float64                         `json:"latitude"`
	Longitude          float64                         `json:"longitude"`
	Type               customerpb.Address_Type         `json:"type"`
	State              customerpb.Address_State        `json:"state"`
	CustomFields       *structpb.Struct                `json:"custom_fields"`
	DeletedTime        *time.Time                      `json:"deleted_time"`
	CreatedTime        time.Time                       `json:"created_time"`
	UpdatedTime        time.Time                       `json:"updated_time"`
}

func (a *Address) ToDB() *addressrepo.Address {
	return &addressrepo.Address{
		ID:                 a.ID,
		CustomerID:         a.CustomerID,
		OrganizationID:     a.OrganizationID,
		OrganizationType:   a.OrganizationType,
		Revision:           a.Revision,
		RegionCode:         a.RegionCode,
		LanguageCode:       a.LanguageCode,
		Organization:       a.Organization,
		PostalCode:         a.PostalCode,
		SortingCode:        a.SortingCode,
		AdministrativeArea: a.AdministrativeArea,
		Locality:           a.Locality,
		Sublocality:        a.Sublocality,
		AddressLines:       a.AddressLines,
		Recipients:         a.Recipients,
		Latitude:           a.Latitude,
		Longitude:          a.Longitude,
		Type:               a.Type,
		State:              a.State,
		DeletedTime:        a.DeletedTime,
		CreatedTime:        a.CreatedTime,
		UpdatedTime:        a.UpdatedTime,
	}
}

func (a *Address) ToPB() *customerpb.Address {
	if a == nil {
		return nil
	}

	postalAddress := &postaladdress.PostalAddress{
		Revision:           int32(a.Revision),
		Organization:       a.Organization,
		SortingCode:        a.SortingCode,
		RegionCode:         a.RegionCode,
		LanguageCode:       a.LanguageCode,
		PostalCode:         a.PostalCode,
		AdministrativeArea: a.AdministrativeArea,
		Locality:           a.Locality,
		Sublocality:        a.Sublocality,
		AddressLines:       []string(a.AddressLines),
		Recipients:         []string(a.Recipients),
	}
	latlng := &latlng.LatLng{
		Latitude:  a.Latitude,
		Longitude: a.Longitude,
	}

	address := &customerpb.Address{
		Id:         a.ID,
		CustomerId: a.CustomerID,
		Organization: &customerpb.OrganizationRef{
			Type: customerpb.OrganizationRef_Type(a.OrganizationType),
			Id:   a.OrganizationID,
		},
		Address: postalAddress,
		Latlng:  latlng,
		State:   a.State,
		Type:    a.Type,
		CreateTime: &timestamppb.Timestamp{
			Seconds: a.CreatedTime.Unix(),
			Nanos:   int32(a.CreatedTime.Nanosecond()),
		},
		UpdateTime: &timestamppb.Timestamp{
			Seconds: a.UpdatedTime.Unix(),
			Nanos:   int32(a.UpdatedTime.Nanosecond()),
		},
	}
	if a.DeletedTime != nil {
		address.DeleteTime = &timestamppb.Timestamp{
			Seconds: a.DeletedTime.Unix(),
			Nanos:   int32(a.DeletedTime.Nanosecond()),
		}
	}

	return address
}

// ************ ListAddresses ************
type ListAddressesRequest struct {
	Filter     *ListAddressesFilter     `json:"filter"`
	Pagination *ListAddressesPagination `json:"pagination"`
	OrderBy    *ListAddressesOrderBy    `json:"order_by"`
}

type ListAddressesPagination struct {
	PageSize        int32  `json:"page_size"`
	Cursor          string `json:"cursor"`
	ReturnTotalSize bool   `json:"return_total_size"`
}

func (p *ListAddressesPagination) DecodeCursor() *postgres.Cursor {
	if p.Cursor == "" {
		return nil
	}
	bytes, err := base64.StdEncoding.DecodeString(p.Cursor)
	if err != nil {
		return nil
	}
	cursor := &postgres.Cursor{}
	_ = json.Unmarshal(bytes, cursor)

	return cursor
}

type ListAddressesOrderBy struct {
	Field     customerpb.ListAddressesRequest_Sorting_Field `json:"field"`
	Direction customerpb.Direction                          `json:"direction"`
}

type ListAddressesFilter struct {
	IDs              []int64                         `json:"ids"`
	CustomerID       int64                           `json:"customer_id"`
	OrganizationID   int64                           `json:"organization_id"`
	OrganizationType customerpb.OrganizationRef_Type `json:"organization_type"`
	States           []customerpb.Address_State      `json:"states"`
	CustomerIDs      []int64
	Types            []customerpb.Address_Type
}

type ListAddressesResponse struct {
	Addresses []*Address `json:"addresses"`
	HasNext   bool       `json:"has_next"`
	NextToken string     `json:"next_token"`
	TotalSize *int64     `json:"total_size"`
}

// ************ UpdateAddress ************
type UpdateAddressRequest struct {
	Revision           int32                    `json:"revision"`
	RegionCode         string                   `json:"region_code"`
	LanguageCode       string                   `json:"language_code"`
	Organization       string                   `json:"organization"`
	PostalCode         string                   `json:"postal_code"`
	SortingCode        string                   `json:"sorting_code"`
	AdministrativeArea string                   `json:"administrative_area"`
	Locality           string                   `json:"locality"`
	Sublocality        string                   `json:"sublocality"`
	AddressLines       []string                 `json:"address_lines"`
	Recipients         []string                 `json:"recipients"`
	Latitude           float64                  `json:"latitude"`
	Longitude          float64                  `json:"longitude"`
	Type               customerpb.Address_Type  `json:"type"`
	State              customerpb.Address_State `json:"state"`
}

func (a *Address) FromProto(pb *customerpb.Address) {
	a.ID = pb.Id
	a.Type = pb.Type

	if postalAddr := pb.GetAddress(); postalAddr != nil {
		a.Revision = postalAddr.GetRevision()
		a.RegionCode = postalAddr.GetRegionCode()
		a.LanguageCode = postalAddr.GetLanguageCode()
		a.Organization = postalAddr.GetOrganization()
		a.PostalCode = postalAddr.GetPostalCode()
		a.SortingCode = postalAddr.GetSortingCode()
		a.AdministrativeArea = postalAddr.GetAdministrativeArea()
		a.Locality = postalAddr.GetLocality()
		a.Sublocality = postalAddr.GetSublocality()
		a.AddressLines = postalAddr.GetAddressLines()
		a.Recipients = postalAddr.GetRecipients()
	}

	if latlng := pb.GetLatlng(); latlng != nil {
		a.Latitude = latlng.GetLatitude()
		a.Longitude = latlng.GetLongitude()
	}
}
