secrets:
  - name: 'moego/production/datasource'
    prefix: 'secret.datasource.'
  - name: 'moego/production/mq'
    prefix: 'secret.mq.'
  - name: "moego/production/aws"
    prefix: "secret.aws."
  - name: "moego/production/growthbook"
    prefix: "secret.growth_book."
  - name: "moego/production/redis"
    prefix: "secret.redis."
server:
  filter:
    - opentelemetry
    - recovery
    - debuglog
    - validation
  service:
    - name: backend.proto.customer.v1.CustomerService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
    - name: backend.proto.customer.v1.CustomerQueryService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
    - name: backend.proto.customer.v2.MetadataService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
    - name: backend.proto.customer.v2.ActivityService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
    - name: moego.crm.engagement.consumer
      address: ${secret.mq.kafka.broker_url_0},${secret.mq.kafka.broker_url_1},${secret.mq.kafka.broker_url_2}?topics=moego.crm.engagement.production&group=moego&maxRetry=3
      protocol: kafka
      timeout: 5000
    - name: moego.crm.message.consumer
      address: ${secret.mq.kafka.broker_url_0},${secret.mq.kafka.broker_url_1},${secret.mq.kafka.broker_url_2}?topics=moego.crm.message.production&group=moego&maxRetry=3
      protocol: kafka
      timeout: 5000
    - name: moego.erp.appointment.consumer
      address: ${secret.mq.kafka.broker_url_0},${secret.mq.kafka.broker_url_1},${secret.mq.kafka.broker_url_2}?topics=moego.erp.appointment.production&group=moego&maxRetry=3
      protocol: kafka
      timeout: 5000
    - name: moego.erp.online_booking.consumer
      address: ${secret.mq.kafka.broker_url_0},${secret.mq.kafka.broker_url_1},${secret.mq.kafka.broker_url_2}?topics=moego.erp.online_booking.production&group=moego&maxRetry=3
      protocol: kafka
      timeout: 5000
    - name: moego.order.consumer
      address: ${secret.mq.kafka.broker_url_0},${secret.mq.kafka.broker_url_1},${secret.mq.kafka.broker_url_2}?topics=moego.order.production&group=moego&maxRetry=3
      protocol: kafka
      timeout: 5000
    - name: moego.crm.customer.merge.consumer
      address: ${secret.mq.kafka.broker_url_0},${secret.mq.kafka.broker_url_1},${secret.mq.kafka.broker_url_2}?topics=moego.crm.customer.merge.testing&group=moego-customer&maxRetry=3
      timeout: 5000
      protocol: kafka
client:
  network: tcp
  protocol: grpc
  filter:
    - opentelemetry
    - debuglog
  transport: grpc
  timeout: 600000
  service:
    - callee: moego-svc-sms
      target: dns://moego-svc-sms:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: mysql.moe_customer
      target: dsn://${secret.datasource.mysql.username}:${secret.datasource.mysql.password}@tcp(${secret.datasource.mysql.url}:${secret.datasource.mysql.port})/moe_customer?charset=utf8mb4&parseTime=true&timeout=30s
      protocol: gorm
      transport: gorm
    - callee: postgres.moego_customer
      target: dsn://postgresql://${secret.datasource.postgres.moego_customer.username}:${secret.datasource.postgres.moego_customer.password}@${secret.datasource.postgres.url.master}:${secret.datasource.postgres.port}/moego_customer?sslmode=disable
      protocol: gorm
      transport: gorm
    - callee: moego-search
      target: dns://moego-search:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-pet
      target: dns://moego-pet:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-svc-organization
      target: dns://moego-svc-organization:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego.crm.customer.producer
      target: kafka://${secret.mq.kafka.broker_url_0},${secret.mq.kafka.broker_url_1},${secret.mq.kafka.broker_url_2}?clientid=moego&partitioner=hash&topic=moego.crm.customer.production
      timeout: 10000
    - callee: moego-svc-appointment
      target: dns://moego-svc-appointment:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-svc-membership
      target: dns://moego-svc-membership:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-svc-order
      target: dns://moego-svc-order:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-svc-payment
      target: dns://moego-svc-payment:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: moego-service-grooming
      target: http://moego-service-grooming:9206
      protocol: http
    - callee: moego.crm.customer.merge.producer
      target: kafka://${secret.mq.kafka.broker_url_0},${secret.mq.kafka.broker_url_1},${secret.mq.kafka.broker_url_2}?clientid=moego&partitioner=hash&topic=moego.crm.customer.merge.testing
      timeout: 10000
plugins:
  auth:
    validation:
      enable_error_log: false
  database:
    gorm:
      max_idle: 20
      max_open: 100
      max_lifetime: 180000 # millisecond
      service:
        - name: mysql.moego_customer
          max_idle: 10
          max_open: 50
          max_lifetime: 180000
    kafka:
      credential:
        region: ${secret.aws.region}
        access_key_id: ${secret.aws.access_key_id}
        secret_access_key: ${secret.aws.secret_access_key}
  config:
    growth_book:
      host: ${secret.growth_book.host}
      client_key: ${secret.growth_book.client_key}
      timeout: 2s
    redis:
      host: ${secret.redis.host}
      port: ${secret.redis.port}
      password: ${secret.redis.password}
      tls: ${secret.redis.tls}