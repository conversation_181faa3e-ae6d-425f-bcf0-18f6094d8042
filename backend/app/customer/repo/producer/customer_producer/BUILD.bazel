load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "customer_producer",
    srcs = ["customer_producer.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/producer/customer_producer",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/database/kafka",
        "//backend/common/rpc/framework/log",
        "@com_github_google_uuid//:uuid",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/event_bus/v1:event_bus",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/organization/v1:organization",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//types/known/anypb",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
