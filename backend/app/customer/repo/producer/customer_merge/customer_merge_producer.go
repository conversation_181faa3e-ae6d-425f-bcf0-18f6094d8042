package customermerge

import (
	"context"
	"sync"

	"github.com/bytedance/sonic"
	"github.com/google/uuid"

	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/database/kafka"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type API interface {
	SendCustomerMergeMessage(ctx context.Context, datum *MessageDatum) error
}

type impl struct {
	producer kafka.Client
}

var (
	initOnce sync.Once
	producer kafka.Client
)

func NewProducer() API {
	initOnce.Do(func() {
		producer = kafka.NewClientProxy("moego.crm.customer.merge.producer")
	})

	return &impl{producer: producer}
}

type MessageDatum struct {
	CompanyID   int64
	CustomerRel *businesscustomerpb.MergeRelationDef
	PetRels     []*businesscustomerpb.MergeRelationDef
}

func (i *impl) SendCustomerMergeMessage(ctx context.Context, datum *MessageDatum) error {
	// get key id
	id, err := uuid.NewV7()
	if err != nil {
		log.ErrorContextf(ctx, "SendCustomerMergeMessage new uuid err, err:%+v", err)

		return err
	}

	// build msg
	msg, err := sonic.Marshal(datum)
	if err != nil {
		log.ErrorContextf(ctx, "SendCustomerMergeMessage Marshal err, err:%+v, datum:%+v", err, datum)

		return err
	}

	// send message
	if err := i.producer.Produce(ctx, []byte(id.String()), msg); err != nil {
		log.ErrorContextf(ctx, "SendCustomerCreateMessage SendMessage err, err:%+v, value:%s", err, msg)

		return err
	}

	log.InfoContextf(ctx, "SendCustomerCreateMessage SendMessage success, value:%s", msg)

	return nil
}
