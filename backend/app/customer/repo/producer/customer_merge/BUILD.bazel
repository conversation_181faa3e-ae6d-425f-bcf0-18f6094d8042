load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "customer_merge",
    srcs = ["customer_merge_producer.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/producer/customer_merge",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/database/kafka",
        "//backend/common/rpc/framework/log",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_google_uuid//:uuid",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/business_customer/v1:business_customer",
    ],
)
