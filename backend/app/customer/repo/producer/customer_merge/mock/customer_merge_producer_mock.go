// Code generated by MockGen. DO NOT EDIT.
// Source: ./producer/customer_merge/customer_merge_producer.go
//
// Generated by this command:
//
//	mockgen -source=./producer/customer_merge/customer_merge_producer.go -destination=./producer/customer_merge/mock/customer_merge_producer_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	customermerge "github.com/MoeGolibrary/moego/backend/app/customer/repo/producer/customer_merge"
	gomock "go.uber.org/mock/gomock"
)

// MockAPI is a mock of API interface.
type MockAPI struct {
	ctrl     *gomock.Controller
	recorder *MockAPIMockRecorder
	isgomock struct{}
}

// MockAPIMockRecorder is the mock recorder for MockAPI.
type MockAPIMockRecorder struct {
	mock *MockAPI
}

// NewMockAPI creates a new mock instance.
func NewMockAPI(ctrl *gomock.Controller) *MockAPI {
	mock := &MockAPI{ctrl: ctrl}
	mock.recorder = &MockAPIMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAPI) EXPECT() *MockAPIMockRecorder {
	return m.recorder
}

// SendCustomerMergeMessage mocks base method.
func (m *MockAPI) SendCustomerMergeMessage(ctx context.Context, datum *customermerge.MessageDatum) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendCustomerMergeMessage", ctx, datum)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendCustomerMergeMessage indicates an expected call of SendCustomerMergeMessage.
func (mr *MockAPIMockRecorder) SendCustomerMergeMessage(ctx, datum any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendCustomerMergeMessage", reflect.TypeOf((*MockAPI)(nil).SendCustomerMergeMessage), ctx, datum)
}
