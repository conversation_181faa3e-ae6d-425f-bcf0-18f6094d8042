package appointment

import (
	"context"

	appointmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	appointmentsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ReadWriter interface {
	MergeCustomerAppointment(ctx context.Context, companyID int64,
		customerRel *businesscustomerpb.MergeRelationDef, petRels []*businesscustomerpb.MergeRelationDef) (bool, error)
	ListAppointmentsForCustomers(ctx context.Context, companyID, customerID int64) (
		[]*appointmentpb.AppointmentModel, error)
}

type impl struct {
	appointmentMergeService appointmentsvcpb.AppointmentMergeServiceClient
	appointmentService      appointmentsvcpb.AppointmentServiceClient
}

func New() ReadWriter {
	return &impl{
		appointmentMergeService: grpc.NewClient("moego-svc-appointment",
			appointmentsvcpb.NewAppointmentMergeServiceClient),
		appointmentService: grpc.NewClient("moego-svc-appointment",
			appointmentsvcpb.NewAppointmentServiceClient),
	}
}

func (i *impl) MergeCustomerAppointment(ctx context.Context, companyID int64,
	customerRel *businesscustomerpb.MergeRelationDef, petRels []*businesscustomerpb.MergeRelationDef) (bool, error) {
	resp, err := i.appointmentMergeService.MergeCustomerAppointmentData(ctx,
		&appointmentsvcpb.MergeCustomerAppointmentDataRequest{
			CompanyId: companyID,
			MergeRelation: &businesscustomerpb.BusinessCustomerMergeRelationDef{
				CustomerMergeRelation: customerRel,
				PetMergeRelations:     petRels,
			},
		})
	if err != nil {
		log.InfoContextf(ctx, "MergeCustomerAppointment err:%v, companyID:%d, customerRel:%+v", err, companyID,
			customerRel)

		return false, err
	}

	return resp.GetSuccess(), nil
}

func (i *impl) ListAppointmentsForCustomers(ctx context.Context, companyID, customerID int64) (
	[]*appointmentpb.AppointmentModel, error) {
	resp, err := i.appointmentService.ListAppointmentsForCustomers(ctx,
		&appointmentsvcpb.ListAppointmentsForCustomersRequest{
			CompanyId:   companyID,
			CustomerIds: []int64{customerID},
			Filter: &appointmentsvcpb.ListAppointmentsForCustomersRequest_Filter{
				DateType: appointmentsvcpb.AppointmentDateType_NEXT,
				Statuses: []appointmentpb.AppointmentStatus{
					appointmentpb.AppointmentStatus_UNCONFIRMED,
					appointmentpb.AppointmentStatus_CONFIRMED,
					appointmentpb.AppointmentStatus_READY,
					appointmentpb.AppointmentStatus_CHECKED_IN},
			},
		})
	if err != nil {
		log.InfoContextf(ctx, "ListAppointmentsForCustomers err:%v, companyID:%d, customerID:%+v",
			err, companyID, customerID)

		return nil, err
	}

	return resp.GetAppointments(), nil
}
