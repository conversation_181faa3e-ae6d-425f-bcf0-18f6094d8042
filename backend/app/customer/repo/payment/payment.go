package payment

import (
	"context"

	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	paymentsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/payment/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ReadWriter interface {
	MergeCustomerPaymentData(ctx context.Context, companyID int64,
		customerRel *businesscustomerpb.MergeRelationDef, petRels []*businesscustomerpb.MergeRelationDef) (bool, error)
}

type impl struct {
	paymentMergeService paymentsvcpb.PaymentMergeServiceClient
}

func New() ReadWriter {
	return &impl{
		paymentMergeService: grpc.NewClient("moego-svc-payment",
			paymentsvcpb.NewPaymentMergeServiceClient),
	}
}

func (i *impl) MergeCustomerPaymentData(ctx context.Context, companyID int64,
	customerRel *businesscustomerpb.MergeRelationDef, petRels []*businesscustomerpb.MergeRelationDef) (bool, error) {
	resp, err := i.paymentMergeService.MergeCustomerPaymentData(ctx, &paymentsvcpb.MergeCustomerPaymentDataRequest{
		CompanyId: companyID,
		MergeRelation: &businesscustomerpb.BusinessCustomerMergeRelationDef{
			CustomerMergeRelation: customerRel,
			PetMergeRelations:     petRels,
		},
	})
	if err != nil {
		log.InfoContextf(ctx, "MergeCustomerPaymentData err:%v, companyID:%d, customerRel:%+v", err, companyID,
			customerRel)

		return false, err
	}

	return resp.GetSuccess(), nil
}
