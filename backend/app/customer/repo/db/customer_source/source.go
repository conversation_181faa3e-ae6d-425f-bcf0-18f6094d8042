package customersource

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/db"
)

// Repository defines the interface for CustomerSource database operations.
type Repository interface {
	List(ctx context.Context, filter *ListFilter) ([]*CustomerSource, error)
}

// impl is the implementation of the Repository interface.
type impl struct {
	db *gorm.DB
}

// New creates and returns a new Repository instance.
func New() Repository {
	return &impl{
		db: db.GetDB(), // Assuming db.GetDB() provides the GORM DB instance.
	}
}

// List retrieves CustomerSource records based on the provided filter.
func (i *impl) List(ctx context.Context, filter *ListFilter) ([]*CustomerSource, error) {
	var sources []*CustomerSource
	if err := i.db.WithContext(ctx).Model(&CustomerSource{}).
		Scopes(filter.Apply).
		Find(&sources).Error; err != nil {
		return nil, err
	}

	return sources, nil
}
