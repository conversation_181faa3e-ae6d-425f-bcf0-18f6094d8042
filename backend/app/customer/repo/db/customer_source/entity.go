package customersource

import (
	"gorm.io/gorm"
)

//-- auto-generated definition
//create table moe_customer_source
//(
//    id          int unsigned auto_increment
//        primary key,
//    business_id int          default 0  not null comment '商家ID',
//    source_name varchar(255) default '' not null,
//    status      tinyint      default 1  not null comment '1正常 2删除',
//    create_time bigint       default 0  not null,
//    update_time bigint       default 0  not null,
//    company_id  bigint       default 0  not null comment 'company id',
//    sort        int          default 0  not null
//);

type CustomerSource struct {
	ID         int64  `gorm:"column:id"`
	BusinessID int64  `gorm:"column:business_id"`
	SourceName string `gorm:"column:source_name"`
	Status     int32  `gorm:"column:status"`
	CreateTime int64  `gorm:"column:create_time"`
	UpdateTime int64  `gorm:"column:update_time"`
	CompanyID  int64  `gorm:"column:company_id"`
	Sort       int32  `gorm:"column:sort"`
}

// TableName sets the insert table name for this struct type
func (*CustomerSource) TableName() string {
	return "moe_customer_source"
}

// ListFilter
type ListFilter struct {
	IDs        []int64
	CompanyIDs []int64
}

func (f *ListFilter) Apply(query *gorm.DB) *gorm.DB {
	if len(f.IDs) > 0 {
		query = query.Where("id IN (?)", f.IDs)
	}
	if len(f.CompanyIDs) > 0 {
		query = query.Where("company_id IN (?)", f.CompanyIDs)
	}

	return query
}
