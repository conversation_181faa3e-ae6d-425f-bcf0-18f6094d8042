// Code generated by MockGen. DO NOT EDIT.
// Source: ./db/customertask/customer_task.go
//
// Generated by this command:
//
//	mockgen -source=./db/customertask/customer_task.go -destination=./db/customertask/mock/customer_task_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	customertask "github.com/MoeGolibrary/moego/backend/app/customer/repo/db/customertask"
	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockReadWriter) Create(arg0 context.Context, task *customertask.CustomerTask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", arg0, task)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockReadWriterMockRecorder) Create(arg0, task any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockReadWriter)(nil).Create), arg0, task)
}

// Delete mocks base method.
func (m *MockReadWriter) Delete(arg0 context.Context, id, staffID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", arg0, id, staffID)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockReadWriterMockRecorder) Delete(arg0, id, staffID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockReadWriter)(nil).Delete), arg0, id, staffID)
}

// List mocks base method.
func (m *MockReadWriter) List(arg0 context.Context, datum *customertask.ListTasksDatum) ([]*customertask.CustomerTask, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", arg0, datum)
	ret0, _ := ret[0].([]*customertask.CustomerTask)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockReadWriterMockRecorder) List(arg0, datum any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockReadWriter)(nil).List), arg0, datum)
}

// Update mocks base method.
func (m *MockReadWriter) Update(arg0 context.Context, task *customertask.CustomerTask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", arg0, task)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockReadWriterMockRecorder) Update(arg0, task any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockReadWriter)(nil).Update), arg0, task)
}
