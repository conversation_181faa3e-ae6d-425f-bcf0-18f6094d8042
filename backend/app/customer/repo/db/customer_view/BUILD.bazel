load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "customer_view",
    srcs = [
        "customer_view.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/db/customer_view",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/db",
        "//backend/common/rpc/framework/log",
        "//backend/proto/customer/v1:customer",
        "@io_gorm_gorm//:gorm",
    ],
)
