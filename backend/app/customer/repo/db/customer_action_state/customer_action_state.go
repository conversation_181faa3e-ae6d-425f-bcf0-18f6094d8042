package customeractionstate

import (
	"context"
	"time"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/db"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ReadWriter interface {
	WithTransaction(context.Context, func(api ReadWriter) error) error
	Create(ctx context.Context, actionState *CustomerActionState) error
	CreateBatch(ctx context.Context, actionStates []*CustomerActionState) error
	Update(ctx context.Context, actionState *CustomerActionState) error
	List(ctx context.Context, datum *ListActionStatesDatum) ([]*CustomerActionState, error)
	Delete(ctx context.Context, id int64, staffID int64) error
}

type impl struct {
	db *gorm.DB
}

func New() ReadWriter {
	return &impl{
		db: db.GetDB(),
	}
}

func (i *impl) WithTransaction(ctx context.Context, fn func(api ReadWriter) error) error {
	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		txReadWriter := &impl{db: tx}

		return fn(txReadWriter)
	})
}

func (i *impl) Create(ctx context.Context, actionState *CustomerActionState) error {
	if err := i.db.WithContext(ctx).Create(actionState).Error; err != nil {
		log.ErrorContextf(ctx, "Create CustomerActionState err, err:%+v", err)

		return err
	}

	return nil
}

func (i *impl) CreateBatch(ctx context.Context, actionStates []*CustomerActionState) error {
	if err := i.db.WithContext(ctx).Create(actionStates).Error; err != nil {
		log.ErrorContextf(ctx, "CreateBatch CustomerActionState err, err:%+v", err)

		return err
	}

	return nil
}

func (i *impl) Update(ctx context.Context, actionState *CustomerActionState) error {
	if err := i.db.WithContext(ctx).Updates(actionState).Error; err != nil {
		log.ErrorContextf(ctx, "Update CustomerActionState err, err:%+v, actionState:%+v", err, actionState)

		return err
	}

	return nil
}

type ListActionStatesDatum struct {
	CompanyIDs []int64
	Names      []string
	IDs        []int64
}

func (i *impl) List(ctx context.Context, datum *ListActionStatesDatum) ([]*CustomerActionState, error) {
	query := i.db.WithContext(ctx).Table("moe_customer_action_state").Where("deleted_at IS NULL")

	if len(datum.CompanyIDs) > 0 {
		query = query.Where("company_id IN (?)", datum.CompanyIDs)
	}
	if len(datum.Names) > 0 {
		query = query.Where("name IN ?", datum.Names)
	}
	if len(datum.IDs) > 0 {
		query = query.Where("id IN (?)", datum.IDs)
	}

	var res []*CustomerActionState
	if err := query.Order("sort asc").Find(&res).Error; err != nil {
		log.ErrorContextf(ctx, "List CustomerActionState err, err:%+v", err)

		return nil, err
	}

	return res, nil
}

func (i *impl) Delete(ctx context.Context, id int64, staffID int64) error {
	if err := i.db.WithContext(ctx).Table("moe_customer_action_state").
		Where("id = ?", id).
		Update("deleted_by", staffID).
		Update("deleted_at", time.Now()).
		Error; err != nil {
		log.ErrorContextf(ctx, "Delete CustomerActionState err, id:%d, staffID:%d, err:%v", id, staffID, err)

		return err
	}

	return nil
}
