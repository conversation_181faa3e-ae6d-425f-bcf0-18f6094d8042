package customer

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/db"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
)

// Repository 聚合的Customer Repository接口
type Repository interface {
	Tx(ctx context.Context, fn func(repo Repository) error) error

	// GetCustomer 获取完整的客户聚合（包含地址和联系人）
	GetCustomer(ctx context.Context, params *GetCustomerParams) (*BusinessCustomer, error)

	// GetCustomerWithOptions 获取客户聚合，可选择是否包含地址和联系人
	GetCustomerWithOptions(ctx context.Context, params *GetCustomerParams, opts *LoadOptions) (*BusinessCustomer, error)

	// ListCustomers 获取客户列表（可选择是否包含地址和联系人）
	ListCustomers(ctx context.Context, query *Query,
		filter *Filter, pagination *db.Pagination, opts *LoadOptions) ([]*BusinessCustomer, error)

	// CountCustomers 获取客户总数
	CountCustomers(ctx context.Context, query *Query, filter *Filter) (int64, error)

	// CreateCustomer 创建客户（包括关联的地址和联系人）
	CreateCustomer(ctx context.Context, customer *BusinessCustomer) (*BusinessCustomer, error)

	// UpdateCustomer 更新客户（包括关联的地址和联系人）
	UpdateCustomer(ctx context.Context, customer *BusinessCustomer) error

	// 地址相关方法
	CreateAddress(ctx context.Context, address *Address) (int64, error)
	BatchUpdateAddress(ctx context.Context, addresses []*Address) error
	DeleteAddress(ctx context.Context, addressID int64) error
	GetAddress(ctx context.Context, addressID int64) (*Address, error)
	GetAddresses(ctx context.Context, customerID int64, pagination *db.Pagination) ([]*Address, error)

	// 联系人相关方法
	CreateContact(ctx context.Context, customerID int64, contact *Contact) (int64, error)
	UpdateContact(ctx context.Context, contact *Contact) error
	DeleteContact(ctx context.Context, contactID int64) error
	GetContacts(ctx context.Context, filter *ContactFilter, params *GetContactParams) ([]*Contact, error)

	GetCustomerForCDC(ctx context.Context, id int64) (*BusinessCustomer, error)

	ImportCustomers(ctx context.Context, customers []*BusinessCustomer) error
}

// LoadOptions 加载选项
type LoadOptions struct {
	WithAddresses    bool
	WithContacts     bool
	WithCustomFields bool
}

// DefaultLoadOptions 默认加载选项 - 加载所有关联数据
func DefaultLoadOptions() *LoadOptions {
	return &LoadOptions{
		WithAddresses:    true,
		WithContacts:     true,
		WithCustomFields: true,
	}
}

// impl 具体实现
type impl struct {
	db                 *gorm.DB
	transactionManager db.TransactionManager
}

// New 创建聚合Repository
func New() Repository {
	database := db.GetDB()

	return &impl{
		db:                 database,
		transactionManager: db.NewTxManager(),
	}
}

// NewWithDB 使用指定的DB创建聚合Repository
func NewWithDB(database *gorm.DB) Repository {
	return &impl{
		db:                 database,
		transactionManager: db.NewTxManager(),
	}
}

func (i *impl) Tx(ctx context.Context, fn func(repo Repository) error) error {
	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		return fn(&impl{db: tx, transactionManager: db.NewTxManager()})
	})
}

// GetCustomer 获取完整的客户聚合
func (i *impl) GetCustomer(ctx context.Context, params *GetCustomerParams) (*BusinessCustomer, error) {
	return i.GetCustomerWithOptions(ctx, params, DefaultLoadOptions())
}

// GetCustomerWithOptions 根据选项获取客户聚合
func (i *impl) GetCustomerWithOptions(ctx context.Context,
	params *GetCustomerParams, opts *LoadOptions) (*BusinessCustomer, error) {
	// 获取基本客户信息
	var customer BusinessCustomer

	if err := i.db.WithContext(ctx).Where(params).
		Where("status = ?", customerpb.Customer_NORMAL.Number()).
		First(&customer).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, status.Error(codes.NotFound, "customer not found")
		}
		log.ErrorContextf(ctx, "GetCustomer err", zap.Any("params", params), zap.Error(err))

		return nil, err
	}

	// 根据选项决定是否加载地址
	if opts != nil && opts.WithAddresses {
		addresses, err := i.GetAddresses(ctx, customer.ID, nil)
		if err != nil {
			log.ErrorContextf(ctx, "GetAddresses err", zap.Int64("customerID", customer.ID), zap.Error(err))

			return nil, err
		}
		customer.Addresses = addresses
	}

	// 根据选项决定是否加载联系人
	if opts != nil && opts.WithContacts {
		contacts, err := i.GetContacts(ctx, nil, &GetContactParams{
			CustomerID: customer.ID,
		})
		if err != nil {
			log.ErrorContextf(ctx, "GetContacts err", zap.Int64("customerID", customer.ID), zap.Error(err))

			return nil, err
		}
		customer.Contacts = contacts
	}

	if opts != nil && opts.WithCustomFields {
		customFields, err := i.GetCustomFields(ctx, customer.ID)
		if err != nil {
			log.ErrorContextf(ctx, "GetCustomFields err", zap.Int64("customerID", customer.ID), zap.Error(err))

			return nil, err
		}
		customer.CustomFields = customFields
	}

	return &customer, nil
}

// applyCustomerFilters 应用客户查询和过滤条件
func (i *impl) applyCustomerFilters(db *gorm.DB, query *Query, filter *Filter) *gorm.DB {
	// 应用查询条件
	if query.CompanyID != 0 {
		db = db.Where("company_id = ?", query.CompanyID)
	}

	if len(query.CustomerIDs) > 0 {
		db = db.Where("id IN ?", query.CustomerIDs)
	}

	if query.PhoneNumber != "" {
		db = db.Where("phone_number = ?", query.PhoneNumber)
	}

	// 应用过滤条件
	if filter != nil {
		if filter.ActionState != nil {
			db = db.Where("action_state = ?", filter.ActionState.String())
		}
		if filter.Type != nil {
			db = db.Where("type = ?", filter.Type.String())
		}
		if filter.Status != nil {
			db = db.Where("status = ?", filter.Status.Number())
		}
		if filter.LifeCycle != nil {
			db = db.Where("life_cycle = ?", filter.LifeCycle.String())
		}
		if filter.CustomizeLifeCycleID != nil {
			db = db.Where("customize_life_cycle_id = ?", *filter.CustomizeLifeCycleID)
		}
		if filter.CustomizeActionStateID != nil {
			db = db.Where("customize_action_state_id = ?", *filter.CustomizeActionStateID)
		}
		if len(filter.Emails) > 0 {
			db = db.Where("email IN ?", filter.Emails)
		}
	}

	return db
}

// applyPagination 应用分页参数
func (i *impl) applyPagination(db *gorm.DB, pagination *db.Pagination) *gorm.DB {
	if pagination != nil {
		if pagination.Offset > 0 {
			db = db.Offset(pagination.Offset)
		}
		if pagination.Limit > 0 {
			db = db.Limit(pagination.Limit)
		} else {
			db = db.Limit(10) // 默认限制
		}

		if pagination.OrderBy != nil {
			order := fmt.Sprintf("%s %s", *pagination.OrderBy, pagination.Desc)
			db = db.Order(order)
		}
	}

	return db
}

// loadRelatedAddresses 批量加载客户地址
func (i *impl) loadRelatedAddresses(ctx context.Context, customers []*BusinessCustomer) error {
	customerIDs := make([]int64, len(customers))
	for i, c := range customers {
		customerIDs[i] = c.ID
	}

	var addresses []*Address
	if err := i.db.WithContext(ctx).
		Where("customer_id IN ?", customerIDs).
		Where("status = ?", customerpb.Address_NORMAL.Number()).
		Find(&addresses).Error; err != nil {
		log.ErrorContextf(ctx, "ListAddresses err", zap.Any("customerIDs", customerIDs), zap.Error(err))

		return err
	}

	// 将地址分配给对应的客户
	addressMap := make(map[int64][]*Address)
	for _, addr := range addresses {
		custID := int64(addr.CustomerID)
		addressMap[custID] = append(addressMap[custID], addr)
	}

	for i, c := range customers {
		if addrs, ok := addressMap[c.ID]; ok {
			customers[i].Addresses = addrs
		}
	}

	return nil
}

// loadRelatedContacts 批量加载客户联系人
func (i *impl) loadRelatedContacts(ctx context.Context, customers []*BusinessCustomer) error {
	customerIDs := make([]int64, len(customers))
	for i, c := range customers {
		customerIDs[i] = c.ID
	}

	var contacts []*Contact
	if err := i.db.WithContext(ctx).
		Where("customer_id IN ?", customerIDs).
		Where("status = ?", customerpb.CustomerContact_NORMAL.Number()).
		Find(&contacts).Error; err != nil {
		log.ErrorContextf(ctx, "ListContacts err", zap.Any("customerIDs", customerIDs), zap.Error(err))

		return err
	}

	// 将联系人分配给对应的客户
	contactMap := make(map[int64][]*Contact)
	for _, contact := range contacts {
		custID := int64(contact.CustomerID)
		contactMap[custID] = append(contactMap[custID], contact)
	}

	for i, c := range customers {
		if conts, ok := contactMap[c.ID]; ok {
			customers[i].Contacts = conts
		}
	}

	return nil
}

// loadRelatedCustomFields 批量加载客户自定义字段
func (i *impl) loadRelatedCustomFields(ctx context.Context, customers []*BusinessCustomer) error {
	customerIDs := make([]int64, len(customers))
	for i, c := range customers {
		customerIDs[i] = c.ID
	}

	var customFields []*CustomFields
	if err := i.db.WithContext(ctx).
		Where("customer_id IN ?", customerIDs).
		Find(&customFields).Error; err != nil {
		return err
	}

	customFieldsMap := make(map[int64]*CustomFields)
	for _, cf := range customFields {
		customFieldsMap[cf.CustomerID] = cf
	}
	for i, c := range customers {
		if customFields, ok := customFieldsMap[c.ID]; ok {
			customers[i].CustomFields = customFields
		}
	}

	return nil
}

// ListCustomers 获取客户列表
func (i *impl) ListCustomers(ctx context.Context, query *Query,
	filter *Filter, pagination *db.Pagination, opts *LoadOptions) ([]*BusinessCustomer, error) {

	// 获取客户基本信息列表
	var customers []*BusinessCustomer
	db := i.db.WithContext(ctx)

	// 应用过滤和分页
	db = i.applyCustomerFilters(db, query, filter)
	db = i.applyPagination(db, pagination)

	// 执行查询
	if err := db.Find(&customers).Error; err != nil {
		log.ErrorContextf(ctx, "ListCustomers err", zap.Error(err))

		return nil, err
	}

	// 如果不需要加载关联数据，直接返回
	if opts == nil || (!opts.WithAddresses && !opts.WithContacts) {
		return customers, nil
	}

	// 加载关联数据
	if opts.WithAddresses {
		if err := i.loadRelatedAddresses(ctx, customers); err != nil {
			return nil, err
		}
	}

	if opts.WithContacts {
		if err := i.loadRelatedContacts(ctx, customers); err != nil {
			return nil, err
		}
	}

	if opts.WithCustomFields {
		if err := i.loadRelatedCustomFields(ctx, customers); err != nil {
			return nil, err
		}
	}

	return customers, nil
}

// CountCustomers 获取客户总数
func (i *impl) CountCustomers(ctx context.Context, query *Query, filter *Filter) (int64, error) {
	var count int64
	db := i.db.WithContext(ctx)

	// 应用过滤条件
	db = i.applyCustomerFilters(db, query, filter)

	if err := db.Model(&BusinessCustomer{}).Count(&count).Error; err != nil {
		log.ErrorContextf(ctx, "CountCustomers err", zap.Error(err))

		return 0, err
	}

	return count, nil
}

// CreateCustomer 创建客户及其关联数据
func (i *impl) CreateCustomer(ctx context.Context, customer *BusinessCustomer) (*BusinessCustomer, error) {
	var customerID int64

	// 使用事务来保证原子性
	err := i.transactionManager.ExecuteInTransaction(ctx, []func(opCtx context.Context, tx *gorm.DB) error{
		// 创建客户
		func(opCtx context.Context, tx *gorm.DB) error {
			// 确保关联数据不会被保存
			customerToCreate := *customer
			customerToCreate.Addresses = nil
			customerToCreate.Contacts = nil

			if err := tx.WithContext(opCtx).Create(&customerToCreate).Error; err != nil {
				return err
			}

			// 更新聚合中客户ID
			customerID = customerToCreate.ID
			customer.ID = customerID

			return nil
		},

		// 创建地址
		func(opCtx context.Context, tx *gorm.DB) error {
			if len(customer.Addresses) == 0 {
				return nil
			}

			for _, addr := range customer.Addresses {
				addr.CustomerID = int(customerID)
				if err := tx.WithContext(opCtx).Create(addr).Error; err != nil {
					return err
				}
			}

			return nil
		},

		// 创建联系人
		func(opCtx context.Context, tx *gorm.DB) error {
			if len(customer.Contacts) == 0 {
				return nil
			}

			for _, contact := range customer.Contacts {
				contact.CustomerID = int(customerID)
				if err := tx.WithContext(opCtx).Create(contact).Error; err != nil {
					return err
				}
			}

			return nil
		},

		// 创建自定义字段
		func(opCtx context.Context, tx *gorm.DB) error {
			if customer.CustomFields == nil {
				return nil
			}

			customer.CustomFields.CustomerID = customerID
			if err := tx.WithContext(opCtx).Create(customer.CustomFields).Error; err != nil {
				return err
			}

			return nil
		},
	})

	if err != nil {
		log.ErrorContextf(ctx, "CreateCustomer err", zap.Error(err))

		return nil, err
	}

	return customer, nil
}

// UpdateCustomer 更新客户及其关联数据
func (i *impl) UpdateCustomer(ctx context.Context, customer *BusinessCustomer) error {
	return i.transactionManager.ExecuteInTransaction(ctx, []func(opCtx context.Context, tx *gorm.DB) error{
		// 更新客户基本信息
		func(opCtx context.Context, tx *gorm.DB) error {
			// 确保关联数据不会被更新
			customerToUpdate := *customer
			customerToUpdate.Addresses = nil
			customerToUpdate.Contacts = nil
			customerToUpdate.CustomFields = nil

			return tx.WithContext(opCtx).Model(&BusinessCustomer{ID: customer.ID}).Updates(&customerToUpdate).Error
		},

		// 更新地址
		func(opCtx context.Context, tx *gorm.DB) error {
			if len(customer.Addresses) == 0 {
				return nil
			}

			for _, addr := range customer.Addresses {
				if addr.ID > 0 {
					// 更新现有地址
					if err := tx.WithContext(opCtx).
						Model(&Address{ID: addr.ID}).Updates(addr).Error; err != nil {
						return err
					}
				} else {
					// 创建新地址
					addr.CustomerID = int(customer.ID)
					if err := tx.WithContext(opCtx).Create(addr).Error; err != nil {
						return err
					}
				}
			}

			return nil
		},

		// 更新联系人
		func(opCtx context.Context, tx *gorm.DB) error {
			if len(customer.Contacts) == 0 {
				return nil
			}

			for _, contact := range customer.Contacts {
				if contact.ID > 0 {
					// 更新现有联系人
					if err := tx.WithContext(opCtx).
						Model(&Contact{ID: contact.ID}).Updates(contact).Error; err != nil {
						return err
					}
				} else {
					// 创建新联系人
					contact.CustomerID = int(customer.ID)
					if err := tx.WithContext(opCtx).Create(contact).Error; err != nil {
						return err
					}
				}
			}

			return nil
		},

		// 更新自定义字段
		func(opCtx context.Context, tx *gorm.DB) error {
			if customer.CustomFields == nil {
				return nil
			}

			if err := tx.WithContext(opCtx).Save(customer.CustomFields).Error; err != nil {
				return err
			}

			return nil
		},
	})
}

// CreateAddress 创建客户地址
func (i *impl) CreateAddress(ctx context.Context, address *Address) (int64, error) {
	if err := i.db.WithContext(ctx).Create(address).Error; err != nil {
		return 0, err
	}

	return int64(address.ID), nil
}

// UpdateAddress 更新客户地址
func (i *impl) BatchUpdateAddress(ctx context.Context, addresses []*Address) error {
	if len(addresses) == 0 {
		return nil
	}

	return i.db.Transaction(func(tx *gorm.DB) error {
		for _, addr := range addresses {
			if err := tx.WithContext(ctx).Model(&Address{ID: addr.ID}).Updates(addr).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

// DeleteAddress 删除客户地址
func (i *impl) DeleteAddress(ctx context.Context, addressID int64) error {
	return i.db.WithContext(ctx).Model(&Address{}).Where("id = ?", addressID).Updates(map[string]interface{}{
		"status":      2,
		"update_time": time.Now(),
	}).Error
}

// GetAddress 获取客户地址
func (i *impl) GetAddress(ctx context.Context, addressID int64) (*Address, error) {
	var address Address
	if err := i.db.WithContext(ctx).Where("id = ?", addressID).First(&address).Error; err != nil {
		return nil, err
	}

	return &address, nil
}

// GetAddresses 获取客户所有地址
func (i *impl) GetAddresses(ctx context.Context, customerID int64, pagination *db.Pagination) ([]*Address, error) {
	var addresses []*Address
	db := i.db.WithContext(ctx)
	if pagination != nil {
		if pagination.Offset > 0 {
			db = db.Offset(pagination.Offset)
		}
		if pagination.Limit > 0 {
			db = db.Limit(pagination.Limit)
		}
	}
	if err := db.
		Where("customer_id = ?", customerID).
		Where("status = ?", customerpb.Address_NORMAL.Number()).
		Find(&addresses).Error; err != nil {
		return nil, err
	}

	return addresses, nil
}

// CreateContact 创建客户联系人
func (i *impl) CreateContact(ctx context.Context, customerID int64, contact *Contact) (int64, error) {
	contact.CustomerID = int(customerID)
	if err := i.db.WithContext(ctx).Create(contact).Error; err != nil {
		return 0, err
	}

	return int64(contact.ID), nil
}

// UpdateContact 更新客户联系人
func (i *impl) UpdateContact(ctx context.Context, contact *Contact) error {
	return i.db.WithContext(ctx).Model(&Contact{ID: contact.ID}).Updates(contact).Error
}

// DeleteContact 删除客户联系人
func (i *impl) DeleteContact(ctx context.Context, contactID int64) error {
	return i.db.WithContext(ctx).Model(&Contact{}).Where("id = ?", contactID).Update("status", 2).Error
}

// GetContacts 获取客户所有联系人
func (i *impl) GetContacts(ctx context.Context, filter *ContactFilter, params *GetContactParams) ([]*Contact, error) {
	var contacts []*Contact
	db := i.db.WithContext(ctx)

	if params.CustomerID > 0 {
		db = db.Where("customer_id = ?", params.CustomerID)
	}

	if params.PhoneNumber != "" {
		db = db.Where("phone_number = ?", params.PhoneNumber)
	}

	if params.CompanyID > 0 {
		db = db.Where("company_id = ?", params.CompanyID)
	}

	if filter != nil {
		if filter.Status != customerpb.CustomerContact_STATE_UNSPECIFIED {
			db = db.Where("status = ?", filter.Status.Number())
		}
		if filter.IsPrimary != nil {
			db = db.Where("is_primary = ?", filter.IsPrimary)
		}
		if filter.Type != customerpb.CustomerContact_TYPE_UNSPECIFIED {
			db = db.Where("type = ?", filter.Type.Number())
		}
	}

	if err := db.Find(&contacts).Error; err != nil {
		return nil, err
	}

	return contacts, nil
}

func (i *impl) GetCustomFields(ctx context.Context, customerID int64) (*CustomFields, error) {
	var customFields *CustomFields
	err := i.db.WithContext(ctx).Where("customer_id = ?", customerID).First(&customFields).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return customFields, nil
	}
	if err != nil {
		return nil, err
	}

	return customFields, nil
}

func (i *impl) GetCustomerForCDC(ctx context.Context, id int64) (*BusinessCustomer, error) {
	var customer BusinessCustomer
	if err := i.db.WithContext(ctx).Where("id = ?", id).First(&customer).Error; err != nil {
		log.ErrorContextf(ctx, "GetCustomerForCDC err, id:%d, err:%v", id, err)

		return nil, err
	}

	return &customer, nil
}

func (i *impl) ImportCustomers(ctx context.Context, customers []*BusinessCustomer) error {
	if err := i.db.WithContext(ctx).Save(customers).Error; err != nil {
		return err
	}
	var (
		addresses    []*Address
		contacts     []*Contact
		customFields []*CustomFields
	)
	for _, customer := range customers {
		for _, addr := range customer.Addresses {
			addr.CustomerID = int(customer.ID)
			addresses = append(addresses, addr)
		}
		for _, contact := range customer.Contacts {
			contact.CustomerID = int(customer.ID)
			contacts = append(contacts, contact)
		}
		if customer.CustomFields != nil {
			customer.CustomFields.CustomerID = customer.ID
			customFields = append(customFields, customer.CustomFields)
		}
	}
	if err := i.db.WithContext(ctx).Save(addresses).Error; err != nil {
		return err
	}
	if err := i.db.WithContext(ctx).Save(contacts).Error; err != nil {
		return err
	}
	if err := i.db.WithContext(ctx).Save(customFields).Error; err != nil {
		return err
	}

	return nil
}
