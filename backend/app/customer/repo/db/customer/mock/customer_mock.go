// Code generated by MockGen. DO NOT EDIT.
// Source: ./db/customer/customer.go
//
// Generated by this command:
//
//	mockgen -source=./db/customer/customer.go -destination=./db/customer/mock/customer_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	db "github.com/MoeGolibrary/moego/backend/app/customer/repo/db"
	customer "github.com/MoeGolibrary/moego/backend/app/customer/repo/db/customer"
	gomock "go.uber.org/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
	isgomock struct{}
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// BatchUpdateAddress mocks base method.
func (m *MockRepository) BatchUpdateAddress(ctx context.Context, addresses []*customer.Address) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateAddress", ctx, addresses)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpdateAddress indicates an expected call of BatchUpdateAddress.
func (mr *MockRepositoryMockRecorder) BatchUpdateAddress(ctx, addresses any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateAddress", reflect.TypeOf((*MockRepository)(nil).BatchUpdateAddress), ctx, addresses)
}

// CountCustomers mocks base method.
func (m *MockRepository) CountCustomers(ctx context.Context, query *customer.Query, filter *customer.Filter) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountCustomers", ctx, query, filter)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountCustomers indicates an expected call of CountCustomers.
func (mr *MockRepositoryMockRecorder) CountCustomers(ctx, query, filter any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountCustomers", reflect.TypeOf((*MockRepository)(nil).CountCustomers), ctx, query, filter)
}

// CreateAddress mocks base method.
func (m *MockRepository) CreateAddress(ctx context.Context, address *customer.Address) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAddress", ctx, address)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAddress indicates an expected call of CreateAddress.
func (mr *MockRepositoryMockRecorder) CreateAddress(ctx, address any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAddress", reflect.TypeOf((*MockRepository)(nil).CreateAddress), ctx, address)
}

// CreateContact mocks base method.
func (m *MockRepository) CreateContact(ctx context.Context, customerID int64, contact *customer.Contact) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateContact", ctx, customerID, contact)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateContact indicates an expected call of CreateContact.
func (mr *MockRepositoryMockRecorder) CreateContact(ctx, customerID, contact any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateContact", reflect.TypeOf((*MockRepository)(nil).CreateContact), ctx, customerID, contact)
}

// CreateCustomer mocks base method.
func (m *MockRepository) CreateCustomer(ctx context.Context, c *customer.BusinessCustomer) (*customer.
	BusinessCustomer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateCustomer", ctx, c)
	ret0, _ := ret[0].(*customer.BusinessCustomer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateCustomer indicates an expected call of CreateCustomer.
func (mr *MockRepositoryMockRecorder) CreateCustomer(ctx, customer any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCustomer", reflect.TypeOf((*MockRepository)(nil).CreateCustomer), ctx, customer)
}

// DeleteAddress mocks base method.
func (m *MockRepository) DeleteAddress(ctx context.Context, addressID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAddress", ctx, addressID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteAddress indicates an expected call of DeleteAddress.
func (mr *MockRepositoryMockRecorder) DeleteAddress(ctx, addressID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAddress", reflect.TypeOf((*MockRepository)(nil).DeleteAddress), ctx, addressID)
}

// DeleteContact mocks base method.
func (m *MockRepository) DeleteContact(ctx context.Context, contactID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteContact", ctx, contactID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteContact indicates an expected call of DeleteContact.
func (mr *MockRepositoryMockRecorder) DeleteContact(ctx, contactID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteContact", reflect.TypeOf((*MockRepository)(nil).DeleteContact), ctx, contactID)
}

// GetAddress mocks base method.
func (m *MockRepository) GetAddress(ctx context.Context, addressID int64) (*customer.Address, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAddress", ctx, addressID)
	ret0, _ := ret[0].(*customer.Address)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAddress indicates an expected call of GetAddress.
func (mr *MockRepositoryMockRecorder) GetAddress(ctx, addressID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAddress", reflect.TypeOf((*MockRepository)(nil).GetAddress), ctx, addressID)
}

// GetAddresses mocks base method.
func (m *MockRepository) GetAddresses(ctx context.Context, customerID int64, pagination *db.Pagination) ([]*customer.Address, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAddresses", ctx, customerID, pagination)
	ret0, _ := ret[0].([]*customer.Address)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAddresses indicates an expected call of GetAddresses.
func (mr *MockRepositoryMockRecorder) GetAddresses(ctx, customerID, pagination any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAddresses", reflect.TypeOf((*MockRepository)(nil).GetAddresses), ctx, customerID, pagination)
}

// GetContacts mocks base method.
func (m *MockRepository) GetContacts(ctx context.Context, filter *customer.ContactFilter, params *customer.GetContactParams) ([]*customer.Contact, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetContacts", ctx, filter, params)
	ret0, _ := ret[0].([]*customer.Contact)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetContacts indicates an expected call of GetContacts.
func (mr *MockRepositoryMockRecorder) GetContacts(ctx, filter, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContacts", reflect.TypeOf((*MockRepository)(nil).GetContacts), ctx, filter, params)
}

// GetCustomer mocks base method.
func (m *MockRepository) GetCustomer(ctx context.Context, params *customer.GetCustomerParams) (*customer.BusinessCustomer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCustomer", ctx, params)
	ret0, _ := ret[0].(*customer.BusinessCustomer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCustomer indicates an expected call of GetCustomer.
func (mr *MockRepositoryMockRecorder) GetCustomer(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCustomer", reflect.TypeOf((*MockRepository)(nil).GetCustomer), ctx, params)
}

// GetCustomerForCDC mocks base method.
func (m *MockRepository) GetCustomerForCDC(ctx context.Context, id int64) (*customer.BusinessCustomer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCustomerForCDC", ctx, id)
	ret0, _ := ret[0].(*customer.BusinessCustomer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCustomerForCDC indicates an expected call of GetCustomerForCDC.
func (mr *MockRepositoryMockRecorder) GetCustomerForCDC(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCustomerForCDC", reflect.TypeOf((*MockRepository)(nil).GetCustomerForCDC), ctx, id)
}

// GetCustomerWithOptions mocks base method.
func (m *MockRepository) GetCustomerWithOptions(ctx context.Context, params *customer.GetCustomerParams, opts *customer.LoadOptions) (*customer.BusinessCustomer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCustomerWithOptions", ctx, params, opts)
	ret0, _ := ret[0].(*customer.BusinessCustomer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCustomerWithOptions indicates an expected call of GetCustomerWithOptions.
func (mr *MockRepositoryMockRecorder) GetCustomerWithOptions(ctx, params, opts any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCustomerWithOptions", reflect.TypeOf((*MockRepository)(nil).GetCustomerWithOptions), ctx, params, opts)
}

// ListCustomers mocks base method.
func (m *MockRepository) ListCustomers(ctx context.Context, query *customer.Query, filter *customer.Filter, pagination *db.Pagination, opts *customer.LoadOptions) ([]*customer.BusinessCustomer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListCustomers", ctx, query, filter, pagination, opts)
	ret0, _ := ret[0].([]*customer.BusinessCustomer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListCustomers indicates an expected call of ListCustomers.
func (mr *MockRepositoryMockRecorder) ListCustomers(ctx, query, filter, pagination, opts any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListCustomers", reflect.TypeOf((*MockRepository)(nil).ListCustomers), ctx, query, filter, pagination, opts)
}

// Tx mocks base method.
func (m *MockRepository) Tx(ctx context.Context, fn func(customer.Repository) error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Tx", ctx, fn)
	ret0, _ := ret[0].(error)
	return ret0
}

// Tx indicates an expected call of Tx.
func (mr *MockRepositoryMockRecorder) Tx(ctx, fn any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Tx", reflect.TypeOf((*MockRepository)(nil).Tx), ctx, fn)
}

// UpdateContact mocks base method.
func (m *MockRepository) UpdateContact(ctx context.Context, contact *customer.Contact) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateContact", ctx, contact)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateContact indicates an expected call of UpdateContact.
func (mr *MockRepositoryMockRecorder) UpdateContact(ctx, contact any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateContact", reflect.TypeOf((*MockRepository)(nil).UpdateContact), ctx, contact)
}

// UpdateCustomer mocks base method.
func (m *MockRepository) UpdateCustomer(ctx context.Context, customer *customer.BusinessCustomer) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCustomer", ctx, customer)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateCustomer indicates an expected call of UpdateCustomer.
func (mr *MockRepositoryMockRecorder) UpdateCustomer(ctx, customer any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCustomer", reflect.TypeOf((*MockRepository)(nil).UpdateCustomer), ctx, customer)
}
