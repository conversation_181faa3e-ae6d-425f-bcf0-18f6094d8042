// Code generated by MockGen. DO NOT EDIT.
// Source: ./db/customer_history_log/customer_history_log.go
//
// Generated by this command:
//
//	mockgen -source=./db/customer_history_log/customer_history_log.go -destination=./db/customer_history_log/mock/customer_history_log_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	customerhistorylog "github.com/MoeGolibrary/moego/backend/app/customer/repo/db/customer_history_log"
	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockReadWriter) Create(arg0 context.Context, historyLog *customerhistorylog.CustomerHistoryLog) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", arg0, historyLog)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockReadWriterMockRecorder) Create(arg0, historyLog any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockReadWriter)(nil).Create), arg0, historyLog)
}

// Get mocks base method.
func (m *MockReadWriter) Get(arg0 context.Context, id int64) (*customerhistorylog.CustomerHistoryLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", arg0, id)
	ret0, _ := ret[0].(*customerhistorylog.CustomerHistoryLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockReadWriterMockRecorder) Get(arg0, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockReadWriter)(nil).Get), arg0, id)
}

// List mocks base method.
func (m *MockReadWriter) List(arg0 context.Context, datum *customerhistorylog.ListHistoryLogsDatum) ([]*customerhistorylog.CustomerHistoryLog, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", arg0, datum)
	ret0, _ := ret[0].([]*customerhistorylog.CustomerHistoryLog)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// List indicates an expected call of List.
func (mr *MockReadWriterMockRecorder) List(arg0, datum any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockReadWriter)(nil).List), arg0, datum)
}

// Update mocks base method.
func (m *MockReadWriter) Update(ctx context.Context, historyLog *customerhistorylog.CustomerHistoryLog) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, historyLog)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockReadWriterMockRecorder) Update(ctx, historyLog any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockReadWriter)(nil).Update), ctx, historyLog)
}
