package file

import (
	"context"
	"io"
	stdhttp "net/http"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/http"
)

type Repository interface {
	DownloadFile(ctx context.Context, uri string) ([]byte, error)
}

type impl struct {
	cli *stdhttp.Client
}

func New() Repository {
	return &impl{
		cli: http.NewStdHTTPClient("moego-file"),
	}
}

// DownloadFile
// uri example: https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/
// 1757325202f0f61f7f0ef84ea1a653bb389328fc7a.csv?name=lead-import-sample%20(13).csv
func (i *impl) DownloadFile(ctx context.Context, uri string) ([]byte, error) {
	request, err := stdhttp.NewRequestWithContext(ctx, stdhttp.MethodGet, uri, nil)
	if err != nil {
		return nil, err
	}
	var body []byte
	response, err := i.cli.Do(request)
	if err != nil {
		return nil, err
	}
	defer response.Body.Close()
	body, err = io.ReadAll(response.Body)
	if err != nil {
		return nil, err
	}

	return body, nil
}
