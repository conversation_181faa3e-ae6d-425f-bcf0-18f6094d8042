load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mock",
    srcs = ["default_preferred_frequency_mock.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/default_preferred_frequency/mock",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres/default_preferred_frequency",
        "@org_uber_go_mock//gomock",
    ],
)
