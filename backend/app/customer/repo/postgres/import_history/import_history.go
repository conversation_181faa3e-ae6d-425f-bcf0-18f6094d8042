package importhistory

import (
	"context"

	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
)

type Repository interface {
	WithTransaction(ctx context.Context, fn func(api Repository) error) error
	Create(ctx context.Context, history *ImportHistory) error
	Update(ctx context.Context, history *UpdateParams) error
	List(ctx context.Context, filter *ListFilter, pagination *Pagination) (*ListResult, error)
}

type impl struct {
	db *gorm.DB
}

func New() Repository {
	return &impl{db: postgres.GetDB()}
}

func (i *impl) WithTransaction(ctx context.Context, fn func(api Repository) error) error {
	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		return fn(&impl{db: tx})
	})
}

func (i *impl) Create(ctx context.Context, history *ImportHistory) error {
	return i.db.WithContext(ctx).Create(history).Error
}

func (i *impl) Update(ctx context.Context, history *UpdateParams) error {
	return i.db.WithContext(ctx).Model(&ImportHistory{ID: history.ID}).Updates(history).Error
}

func (i *impl) List(ctx context.Context, filter *ListFilter, pagination *Pagination) (*ListResult, error) {
	res := &ListResult{
		ImportHistories: make([]*ImportHistory, 0),
	}
	query := i.db.WithContext(ctx).Model(&ImportHistory{}).
		Scopes(filter.Apply).
		Order("id DESC")
	if pagination != nil {
		res.Total = proto.Int64(0)
		query = query.Count(res.Total).Scopes(pagination.Apply)
	}
	if err := query.Find(&res.ImportHistories).Error; err != nil {
		return nil, err
	}

	return res, nil
}
