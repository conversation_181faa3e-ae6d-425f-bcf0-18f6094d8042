load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "import_history",
    srcs = [
        "entity.go",
        "import_history.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/import_history",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres",
        "//backend/proto/customer/v2:customer",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_protobuf//proto",
    ],
)
