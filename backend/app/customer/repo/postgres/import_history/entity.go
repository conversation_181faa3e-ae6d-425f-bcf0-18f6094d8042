package importhistory

import (
	"time"

	"gorm.io/gorm"

	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type ImportHistory struct {
	ID               int64                           `gorm:"column:id"`
	OrganizationID   int64                           `gorm:"column:organization_id"`
	OrganizationType customerpb.OrganizationRef_Type `gorm:"column:organization_type;serializer:proto_enum"`
	State            customerpb.ImportHistory_State  `gorm:"column:state;serializer:proto_enum"`
	UploadFileURI    string                          `gorm:"column:upload_file_uri"`
	ErrorMessage     string                          `gorm:"column:error_message"`
	CreateTime       time.Time                       `gorm:"column:create_time;autoCreateTime"`
	UpdateTime       time.Time                       `gorm:"column:update_time;autoUpdateTime"`
	DeleteTime       *time.Time                      `gorm:"column:delete_time"`
}

// TableName 指定表名
func (*ImportHistory) TableName() string {
	return "import_history"
}

type UpdateParams struct {
	ID           int64                          `gorm:"column:id"`
	State        customerpb.ImportHistory_State `gorm:"column:state;serializer:proto_enum"`
	ErrorMessage *string                        `gorm:"column:error_message"`
	CreateTime   time.Time                      `gorm:"column:create_time;autoCreateTime"`
	UpdateTime   time.Time                      `gorm:"column:update_time;autoUpdateTime"`
	DeletedTime  *time.Time                     `gorm:"column:delete_time"`
}

type ListFilter struct {
	IDs           []int64
	States        []customerpb.ImportHistory_State
	Organizations []*customerpb.OrganizationRef
}

func (f *ListFilter) Apply(q *gorm.DB) *gorm.DB {
	if f == nil {
		return q
	}
	if len(f.IDs) > 0 {
		q = q.Where("id IN ?", f.IDs)
	}
	if len(f.States) > 0 {
		q = q.Where("state IN ?", f.States)
	}
	if len(f.Organizations) > 0 {
		var orgs [][]any
		for _, org := range f.Organizations {
			orgs = append(orgs, []any{org.GetId(), org.GetType()})
		}
		q = q.Where("(organization_id, organization_type) IN ?", orgs)
	}

	return q
}

type Pagination struct {
	PageSize   int32
	PageNumber int32
}

func (p *Pagination) Apply(q *gorm.DB) *gorm.DB {
	if p == nil {
		return q
	}
	q = q.Limit(int(p.PageSize)).Offset(int((p.PageNumber - 1) * p.PageSize))

	return q
}

type ListResult struct {
	ImportHistories []*ImportHistory
	Total           *int64
}
