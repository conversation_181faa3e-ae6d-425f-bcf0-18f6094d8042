// Code generated by MockGen. DO NOT EDIT.
// Source: ./postgres/contact_tag_rel/contact_tag_rel.go
//
// Generated by this command:
//
//	mockgen -source=./postgres/contact_tag_rel/contact_tag_rel.go -destination=./postgres/contact_tag_rel/mock/contact_tag_rel_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	contacttagrel "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact_tag_rel"
	gomock "go.uber.org/mock/gomock"
	gorm "gorm.io/gorm"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
	isgomock struct{}
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockRepository) Create(ctx context.Context, contactTagRel *contacttagrel.ContactTagRel) (*contacttagrel.ContactTagRel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, contactTagRel)
	ret0, _ := ret[0].(*contacttagrel.ContactTagRel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockRepositoryMockRecorder) Create(ctx, contactTagRel any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRepository)(nil).Create), ctx, contactTagRel)
}

// Delete mocks base method.
func (m *MockRepository) Delete(ctx context.Context, contactID, tagID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, contactID, tagID)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockRepositoryMockRecorder) Delete(ctx, contactID, tagID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockRepository)(nil).Delete), ctx, contactID, tagID)
}

// Get mocks base method.
func (m *MockRepository) Get(ctx context.Context, contactID, tagID int64) (*contacttagrel.ContactTagRel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, contactID, tagID)
	ret0, _ := ret[0].(*contacttagrel.ContactTagRel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockRepositoryMockRecorder) Get(ctx, contactID, tagID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockRepository)(nil).Get), ctx, contactID, tagID)
}

// List mocks base method.
func (m *MockRepository) List(ctx context.Context, filter *contacttagrel.ListFilter) ([]*contacttagrel.ContactTagRel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, filter)
	ret0, _ := ret[0].([]*contacttagrel.ContactTagRel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockRepositoryMockRecorder) List(ctx, filter any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockRepository)(nil).List), ctx, filter)
}

// WithTx mocks base method.
func (m *MockRepository) WithTx(tx *gorm.DB) contacttagrel.Repository {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTx", tx)
	ret0, _ := ret[0].(contacttagrel.Repository)
	return ret0
}

// WithTx indicates an expected call of WithTx.
func (mr *MockRepositoryMockRecorder) WithTx(tx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTx", reflect.TypeOf((*MockRepository)(nil).WithTx), tx)
}
