package customerrelateddata

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

//go:generate mockgen -source=customer_related_data.go -destination=mocks/mock_customer_related_data_repo.go -package=mocks

type Repository interface {
	Create(ctx context.Context, data *CustomerRelatedData) (*CustomerRelatedData, error)
	Save(ctx context.Context, data *CustomerRelatedData) (*CustomerRelatedData, error)
	Get(ctx context.Context, id int64) (*CustomerRelatedData, error)
	GetByCustomerID(ctx context.Context, customerID int64) (*CustomerRelatedData, error)
	ListByCursor(ctx context.Context,
		filter *ListFilter, pagination *Pagination, orderBy *OrderBy) (*CursorResult, error)
	Update(ctx context.Context, updateData *Update) (*CustomerRelatedData, error)
	Delete(ctx context.Context, id int64) error
	WithTx(tx *gorm.DB) Repository
}

func (i *impl) WithTx(tx *gorm.DB) Repository {
	return &impl{db: tx}
}

type impl struct {
	db *gorm.DB
}

func New() Repository {
	return &impl{
		db: postgres.GetDB(),
	}
}

func (i *impl) Create(ctx context.Context, data *CustomerRelatedData) (*CustomerRelatedData, error) {
	now := time.Now().UTC()
	data.CreatedTime = now
	data.UpdatedTime = now
	data.State = customerpb.CustomerRelatedData_ACTIVE

	err := i.db.WithContext(ctx).Create(data).Error
	if err != nil {
		return nil, err
	}

	return data, nil
}

func (i *impl) Save(ctx context.Context, data *CustomerRelatedData) (*CustomerRelatedData, error) {
	now := time.Now().UTC()
	data.CreatedTime = now
	data.UpdatedTime = now
	data.State = customerpb.CustomerRelatedData_ACTIVE

	err := i.db.WithContext(ctx).Save(data).Error
	if err != nil {
		return nil, err
	}

	return data, nil
}

func (i *impl) Get(ctx context.Context, id int64) (*CustomerRelatedData, error) {
	var data CustomerRelatedData
	err := i.db.WithContext(ctx).
		Where("id = ? AND state != ?", id, customerpb.CustomerRelatedData_DELETED.String()).
		First(&data).Error
	if err != nil {
		return nil, err
	}

	return &data, nil
}

func (i *impl) GetByCustomerID(ctx context.Context, customerID int64) (*CustomerRelatedData, error) {
	var data CustomerRelatedData
	err := i.db.WithContext(ctx).
		Where("customer_id = ? AND state != ?", customerID, customerpb.CustomerRelatedData_DELETED.String()).
		First(&data).Error
	if err != nil {
		return nil, err
	}

	return &data, nil
}

// ListByCursor 基于游标分页的列表查询
func (i *impl) ListByCursor(ctx context.Context,
	filter *ListFilter, pagination *Pagination, orderBy *OrderBy) (*CursorResult, error) {
	var total *int64
	if pagination.ReturnTotalSize {
		count, err := i.getTotalCount(ctx, filter)
		if err != nil {
			return nil, err
		}
		total = &count
	}

	query := i.db.WithContext(ctx).Model(&CustomerRelatedData{})
	query = i.applyFilter(query, filter)

	// 如果使用offset分页，则不应用cursor
	if pagination.Offset != nil && *pagination.Offset > 0 {
		query = query.Offset(int(*pagination.Offset))
	} else {
		query = i.applyCursor(query, pagination.Cursor, orderBy)
	}

	query = i.applyOrderBy(query, orderBy)

	// 当 PageSize=0 时返回所有数据，否则多查一条用于判断是否有下一页
	var hasNext bool
	if pagination.PageSize > 0 {
		query = query.Limit(int(pagination.PageSize + 1))
	}

	var dataList []*CustomerRelatedData
	if err := query.Find(&dataList).Error; err != nil {
		return nil, err
	}

	// 判断是否有下一页
	if pagination.PageSize > 0 {
		hasNext = len(dataList) > int(pagination.PageSize)
		if hasNext {
			dataList = dataList[:pagination.PageSize]
		}
	}

	return &CursorResult{
		Data:       dataList,
		HasNext:    hasNext,
		TotalCount: total,
	}, nil
}

func (i *impl) applyFilter(query *gorm.DB, filter *ListFilter) *gorm.DB {
	if filter == nil {
		return query
	}

	if len(filter.IDs) > 0 {
		query = query.Where("id IN ?", filter.IDs)
	}

	if len(filter.CustomerIDs) > 0 {
		query = query.Where("customer_id IN ?", filter.CustomerIDs)
	}

	if len(filter.BusinessIDs) > 0 {
		query = query.Where("business_id IN ?", filter.BusinessIDs)
	}

	if len(filter.CompanyIDs) > 0 {
		query = query.Where("company_id IN ?", filter.CompanyIDs)
	}

	if len(filter.States) > 0 {
		states := make([]string, 0, len(filter.States))
		for _, state := range filter.States {
			states = append(states, state.String())
		}
		query = query.Where("state IN ?", states)
	} else {
		// 默认排除已删除的记录
		query = query.Where("state != ?", customerpb.CustomerRelatedData_DELETED.String())
	}

	if len(filter.CustomerCodes) > 0 {
		query = query.Where("customer_code IN ?", filter.CustomerCodes)
	}

	if len(filter.AccountIDs) > 0 {
		query = query.Where("account_id IN ?", filter.AccountIDs)
	}

	if len(filter.Sources) > 0 {
		query = query.Where("source IN ?", filter.Sources)
	}

	if filter.IsLapsed {
		query = query.Where(
			"last_service_time ~ '^\\d{4}-\\d{2}-\\d{2}$' AND CURRENT_DATE > TO_DATE(last_service_time, " +
				"'YYYY-MM-DD') + (preferred_frequency_day || ' days')::INTERVAL")
	}

	if len(filter.IsBlockMessage) > 0 {
		query = query.Where("is_block_message IN ?", filter.IsBlockMessage)
	}

	if len(filter.IsBlockOnlineBooking) > 0 {
		query = query.Where("is_block_online_booking IN ?", filter.IsBlockOnlineBooking)
	}

	if filter.LastServiceTimeGap > 0 {
		query = query.Where(fmt.Sprintf(`last_service_time ~ '^\d{4}-\d{2}-\d{2}$' AND
        CURRENT_DATE >= TO_DATE(last_service_time, 'YYYY-MM-DD') + (%d || ' days')::INTERVAL`,
			filter.LastServiceTimeGap))
	}

	if len(filter.PreferredGroomerIDs) > 0 {
		query = query.Where("preferred_groomer_id IN (?)", filter.PreferredGroomerIDs)
	}

	return query
}

func (i *impl) applyCursor(query *gorm.DB, cursor *postgres.Cursor, orderBy *OrderBy) *gorm.DB {
	if cursor == nil {
		return query
	}

	if orderBy != nil && orderBy.Direction == customerpb.Direction_DESC {
		query = query.Where("(created_time, id) < (?, ?)", cursor.CreatedAt, cursor.ID)
	} else {
		query = query.Where("(created_time, id) > (?, ?)", cursor.CreatedAt, cursor.ID)
	}

	return query
}

func (i *impl) applyOrderBy(query *gorm.DB, orderBy *OrderBy) *gorm.DB {
	if orderBy != nil {

		query = query.Order(fmt.Sprintf("%s %s, id %s", orderBy.Field.String(),
			orderBy.Direction.String(), orderBy.Direction.String()))
	} else {
		query = query.Order("created_time DESC, id DESC")
	}

	return query
}

func (i *impl) getTotalCount(ctx context.Context, filter *ListFilter) (int64, error) {
	query := i.db.WithContext(ctx).Model(&CustomerRelatedData{})
	query = i.applyFilter(query, filter)

	var count int64
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}

func (i *impl) Update(ctx context.Context, updateData *Update) (*CustomerRelatedData, error) {
	err := i.db.WithContext(ctx).Model(&CustomerRelatedData{}).
		Where("id = ?", updateData.ID).
		Updates(updateData).Error
	if err != nil {
		return nil, err
	}

	// 返回更新后的数据
	return i.Get(ctx, updateData.ID)
}

func (i *impl) Delete(ctx context.Context, id int64) error {
	now := time.Now().UTC()

	return i.db.WithContext(ctx).Model(&CustomerRelatedData{}).
		Where("id = ?", id).
		Updates(&CustomerRelatedData{
			State:       customerpb.CustomerRelatedData_DELETED,
			DeletedTime: &now,
		}).Error
}
