// Code generated by MockGen. DO NOT EDIT.
// Source: customer_related_data.go
//
// Generated by this command:
//
//	mockgen -source=customer_related_data.go -destination=mocks/mock_customer_related_data_repo.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	customerrelateddata "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer_related_data"
	gomock "go.uber.org/mock/gomock"
	gorm "gorm.io/gorm"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
	isgomock struct{}
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockRepository) Create(ctx context.Context, data *customerrelateddata.CustomerRelatedData) (*customerrelateddata.CustomerRelatedData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, data)
	ret0, _ := ret[0].(*customerrelateddata.CustomerRelatedData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockRepositoryMockRecorder) Create(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRepository)(nil).Create), ctx, data)
}

// Delete mocks base method.
func (m *MockRepository) Delete(ctx context.Context, id int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockRepositoryMockRecorder) Delete(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockRepository)(nil).Delete), ctx, id)
}

// Get mocks base method.
func (m *MockRepository) Get(ctx context.Context, id int64) (*customerrelateddata.CustomerRelatedData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, id)
	ret0, _ := ret[0].(*customerrelateddata.CustomerRelatedData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockRepositoryMockRecorder) Get(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockRepository)(nil).Get), ctx, id)
}

// GetByCustomerID mocks base method.
func (m *MockRepository) GetByCustomerID(ctx context.Context, customerID int64) (*customerrelateddata.CustomerRelatedData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByCustomerID", ctx, customerID)
	ret0, _ := ret[0].(*customerrelateddata.CustomerRelatedData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByCustomerID indicates an expected call of GetByCustomerID.
func (mr *MockRepositoryMockRecorder) GetByCustomerID(ctx, customerID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByCustomerID", reflect.TypeOf((*MockRepository)(nil).GetByCustomerID), ctx, customerID)
}

// ListByCursor mocks base method.
func (m *MockRepository) ListByCursor(ctx context.Context, filter *customerrelateddata.ListFilter, pagination *customerrelateddata.Pagination, orderBy *customerrelateddata.OrderBy) (*customerrelateddata.CursorResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByCursor", ctx, filter, pagination, orderBy)
	ret0, _ := ret[0].(*customerrelateddata.CursorResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByCursor indicates an expected call of ListByCursor.
func (mr *MockRepositoryMockRecorder) ListByCursor(ctx, filter, pagination, orderBy any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByCursor", reflect.TypeOf((*MockRepository)(nil).ListByCursor), ctx, filter, pagination, orderBy)
}

// Save mocks base method.
func (m *MockRepository) Save(ctx context.Context, data *customerrelateddata.CustomerRelatedData) (*customerrelateddata.CustomerRelatedData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", ctx, data)
	ret0, _ := ret[0].(*customerrelateddata.CustomerRelatedData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Save indicates an expected call of Save.
func (mr *MockRepositoryMockRecorder) Save(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockRepository)(nil).Save), ctx, data)
}

// Update mocks base method.
func (m *MockRepository) Update(ctx context.Context, updateData *customerrelateddata.Update) (*customerrelateddata.CustomerRelatedData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, updateData)
	ret0, _ := ret[0].(*customerrelateddata.CustomerRelatedData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockRepositoryMockRecorder) Update(ctx, updateData any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockRepository)(nil).Update), ctx, updateData)
}

// WithTx mocks base method.
func (m *MockRepository) WithTx(tx *gorm.DB) customerrelateddata.Repository {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTx", tx)
	ret0, _ := ret[0].(customerrelateddata.Repository)
	return ret0
}

// WithTx indicates an expected call of WithTx.
func (mr *MockRepositoryMockRecorder) WithTx(tx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTx", reflect.TypeOf((*MockRepository)(nil).WithTx), tx)
}
