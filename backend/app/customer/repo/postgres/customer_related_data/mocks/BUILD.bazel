load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mocks",
    srcs = ["mock_customer_related_data_repo.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer_related_data/mocks",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres/customer_related_data",
        "@io_gorm_gorm//:gorm",
        "@org_uber_go_mock//gomock",
    ],
)
