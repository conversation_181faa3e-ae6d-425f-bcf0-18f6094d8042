package lifecycle

import (
	"context"
	"time"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ReadWriter interface {
	WithTransaction(context.Context, func(api ReadWriter) error) error
	Create(ctx context.Context, lifeCycle *CustomerLifeCycle) error
	CreateBatch(ctx context.Context, lifeCycle []*CustomerLifeCycle) error
	Update(ctx context.Context, lifeCycle *CustomerLifeCycle) error
	List(ctx context.Context, datum *ListLifeCyclesDatum) ([]*CustomerLifeCycle, error)
	Delete(ctx context.Context, id int64, staffID int64) error
}

type impl struct {
	db *gorm.DB
}

func New() ReadWriter {
	return &impl{
		db: postgres.GetDB(),
	}
}

func (i *impl) WithTransaction(ctx context.Context, fn func(api ReadWriter) error) error {
	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		txReadWriter := &impl{db: tx}

		return fn(txReadWriter)
	})
}

func (i *impl) Create(ctx context.Context, lifeCycle *CustomerLifeCycle) error {
	if err := i.db.WithContext(ctx).Create(lifeCycle).Error; err != nil {
		log.ErrorContextf(ctx, "Create CustomerLifeCycle err, err:%+v", err)

		return err
	}

	return nil
}

func (i *impl) CreateBatch(ctx context.Context, lifeCycles []*CustomerLifeCycle) error {
	if err := i.db.WithContext(ctx).Create(lifeCycles).Error; err != nil {
		log.ErrorContextf(ctx, "CreateBatch CustomerLifeCycle err, err:%+v", err)

		return err
	}

	return nil
}

func (i *impl) Update(ctx context.Context, lifeCycle *CustomerLifeCycle) error {
	if err := i.db.WithContext(ctx).Updates(lifeCycle).Error; err != nil {
		log.ErrorContextf(ctx, "Update CustomerLifeCycle err, err:%+v, lifeCycle:%+v", err, lifeCycle)

		return err
	}

	return nil
}

type ListLifeCyclesDatum struct {
	CompanyIDs []int64
	Names      []string
	IsDefault  *int32
	IDs        []int64
}

func (i *impl) List(ctx context.Context, datum *ListLifeCyclesDatum) ([]*CustomerLifeCycle, error) {
	query := i.db.WithContext(ctx).Table("life_cycle").Where("deleted_at IS NULL")

	if len(datum.CompanyIDs) > 0 {
		query = query.Where("company_id IN (?)", datum.CompanyIDs)
	}
	if len(datum.Names) > 0 {
		query = query.Where("name IN ?", datum.Names)
	}
	if datum.IsDefault != nil {
		query = query.Where("is_default = ?", *datum.IsDefault)
	}
	if len(datum.IDs) > 0 {
		query = query.Where("id IN ?", datum.IDs)
	}

	var res []*CustomerLifeCycle
	if err := query.Order("sort asc").Find(&res).Error; err != nil {
		log.ErrorContextf(ctx, "List CustomerLifeCycle err, err:%+v", err)

		return nil, err
	}

	return res, nil
}

func (i *impl) Delete(ctx context.Context, id int64, staffID int64) error {
	if err := i.db.WithContext(ctx).Table("life_cycle").
		Where("id = ?", id).
		Update("deleted_by", staffID).
		Update("deleted_at", time.Now()).
		Error; err != nil {
		log.ErrorContextf(ctx, "Delete CustomerLifeCycle err, id:%d, staffID:%d, err:%v", id, staffID, err)

		return err
	}

	return nil
}
