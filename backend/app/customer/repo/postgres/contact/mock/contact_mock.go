// Code generated by MockGen. DO NOT EDIT.
// Source: ./postgres/contact/contact.go
//
// Generated by this command:
//
//	mockgen -source=./postgres/contact/contact.go -destination=./postgres/contact/mock/contact_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	contact "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact"
	gomock "go.uber.org/mock/gomock"
	gorm "gorm.io/gorm"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
	isgomock struct{}
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// CountFilterContactCustomerIDs mocks base method.
func (m *MockRepository) CountFilterContactCustomerIDs(ctx context.Context, companyID int64, businessIDs, customerIDs []int64, havingConditions []contact.HavingCondition) ([]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountFilterContactCustomerIDs", ctx, companyID, businessIDs, customerIDs, havingConditions)
	ret0, _ := ret[0].([]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountFilterContactCustomerIDs indicates an expected call of CountFilterContactCustomerIDs.
func (mr *MockRepositoryMockRecorder) CountFilterContactCustomerIDs(ctx, companyID, businessIDs, customerIDs, havingConditions any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountFilterContactCustomerIDs", reflect.TypeOf((*MockRepository)(nil).CountFilterContactCustomerIDs), ctx, companyID, businessIDs, customerIDs, havingConditions)
}

// Create mocks base method.
func (m *MockRepository) Create(ctx context.Context, arg1 *contact.Contact) (*contact.Contact, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, arg1)
	ret0, _ := ret[0].(*contact.Contact)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockRepositoryMockRecorder) Create(ctx, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRepository)(nil).Create), ctx, arg1)
}

// Delete mocks base method.
func (m *MockRepository) Delete(ctx context.Context, id int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockRepositoryMockRecorder) Delete(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockRepository)(nil).Delete), ctx, id)
}

// FilterContactCustomerIDs mocks base method.
func (m *MockRepository) FilterContactCustomerIDs(ctx context.Context, filter *contact.FilterConditions) ([]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FilterContactCustomerIDs", ctx, filter)
	ret0, _ := ret[0].([]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FilterContactCustomerIDs indicates an expected call of FilterContactCustomerIDs.
func (mr *MockRepositoryMockRecorder) FilterContactCustomerIDs(ctx, filter any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FilterContactCustomerIDs", reflect.TypeOf((*MockRepository)(nil).FilterContactCustomerIDs), ctx, filter)
}

// Get mocks base method.
func (m *MockRepository) Get(ctx context.Context, id int64) (*contact.Contact, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, id)
	ret0, _ := ret[0].(*contact.Contact)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockRepositoryMockRecorder) Get(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockRepository)(nil).Get), ctx, id)
}

// GetPrimaryPhones mocks base method.
func (m *MockRepository) GetPrimaryPhones(ctx context.Context, companyID int64, businessIDs, customerIDs []int64) ([]contact.CustomerPhone, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPrimaryPhones", ctx, companyID, businessIDs, customerIDs)
	ret0, _ := ret[0].([]contact.CustomerPhone)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPrimaryPhones indicates an expected call of GetPrimaryPhones.
func (mr *MockRepositoryMockRecorder) GetPrimaryPhones(ctx, companyID, businessIDs, customerIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPrimaryPhones", reflect.TypeOf((*MockRepository)(nil).GetPrimaryPhones), ctx, companyID, businessIDs, customerIDs)
}

// ListByCursor mocks base method.
func (m *MockRepository) ListByCursor(ctx context.Context, filter *contact.ListFilter, pagination *contact.Pagination, orderBy *contact.OrderBy) (*contact.CursorResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByCursor", ctx, filter, pagination, orderBy)
	ret0, _ := ret[0].(*contact.CursorResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByCursor indicates an expected call of ListByCursor.
func (mr *MockRepositoryMockRecorder) ListByCursor(ctx, filter, pagination, orderBy any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByCursor", reflect.TypeOf((*MockRepository)(nil).ListByCursor), ctx, filter, pagination, orderBy)
}

// SearchContactCustomerIDs mocks base method.
func (m *MockRepository) SearchContactCustomerIDs(ctx context.Context, companyID int64, businessIDs []int64, keyword, nameKeyword string) ([]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchContactCustomerIDs", ctx, companyID, businessIDs, keyword, nameKeyword)
	ret0, _ := ret[0].([]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchContactCustomerIDs indicates an expected call of SearchContactCustomerIDs.
func (mr *MockRepositoryMockRecorder) SearchContactCustomerIDs(ctx, companyID, businessIDs, keyword, nameKeyword any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchContactCustomerIDs", reflect.TypeOf((*MockRepository)(nil).SearchContactCustomerIDs), ctx, companyID, businessIDs, keyword, nameKeyword)
}

// Update mocks base method.
func (m *MockRepository) Update(ctx context.Context, arg1 *contact.Contact, isInTx bool) (*contact.Contact, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, arg1, isInTx)
	ret0, _ := ret[0].(*contact.Contact)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockRepositoryMockRecorder) Update(ctx, arg1, isInTx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockRepository)(nil).Update), ctx, arg1, isInTx)
}

// WithTx mocks base method.
func (m *MockRepository) WithTx(tx *gorm.DB) contact.Repository {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTx", tx)
	ret0, _ := ret[0].(contact.Repository)
	return ret0
}

// WithTx indicates an expected call of WithTx.
func (mr *MockRepositoryMockRecorder) WithTx(tx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTx", reflect.TypeOf((*MockRepository)(nil).WithTx), tx)
}
