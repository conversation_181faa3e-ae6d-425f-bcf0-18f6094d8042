load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "postgres",
    srcs = [
        "postgres.go",
        "tx_manager.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/database/gorm",
        "@com_github_bytedance_sonic//:sonic",
        "@io_gorm_gorm//:gorm",
    ],
)
