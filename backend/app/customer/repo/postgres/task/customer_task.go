package task

import (
	"context"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ReadWriter interface {
	Create(context context.Context, task *Task) error
	Update(context context.Context, task *Task) error
	List(context context.Context, datum *ListTasksDatum) ([]*Task, error)
	Delete(context context.Context, id int64, staffID int64) error
}

type impl struct {
	db *gorm.DB
}

func New() ReadWriter {
	return &impl{
		db: postgres.GetDB(),
	}
}

func (i *impl) Create(ctx context.Context, task *Task) error {
	if err := i.db.WithContext(ctx).Create(task).Error; err != nil {
		log.ErrorContextf(ctx, "Create Task err, err:%+v", err)

		return err
	}

	return nil
}

func (i *impl) Update(ctx context.Context, task *Task) error {
	if err := i.db.WithContext(ctx).Clauses(clause.Returning{}).Updates(task).Error; err != nil {
		log.ErrorContextf(ctx, "Update Task err, err:%+v", err)

		return err
	}

	return nil
}

type ListTasksDatum struct {
	CustomerIDs []int64
	IDs         []int64
}

func (i *impl) List(ctx context.Context, datum *ListTasksDatum) ([]*Task, error) {
	query := i.db.WithContext(ctx).Table("task").Where("delete_time IS NULL")

	// filter
	if len(datum.CustomerIDs) > 0 {
		query = query.Where("customer_id IN (?)", datum.CustomerIDs)
	}
	if len(datum.IDs) > 0 {
		query = query.Where("id IN (?)", datum.IDs)
	}

	var res []*Task
	if err := query.
		Order("update_time desc").
		Find(&res).Error; err != nil {
		log.ErrorContextf(ctx, "List Task err, err:%+v", err)

		return nil, err
	}

	return res, nil
}

func (i *impl) Delete(ctx context.Context, id int64, staffID int64) error {
	if err := i.db.WithContext(ctx).Table("task").
		Where("id = ?", id).
		Update("delete_by", staffID).
		Update("delete_time", time.Now()).
		Error; err != nil {
		log.ErrorContextf(ctx, "Delete Task err, id:%d, staffID:%d", id, staffID)

		return err
	}

	return nil
}
