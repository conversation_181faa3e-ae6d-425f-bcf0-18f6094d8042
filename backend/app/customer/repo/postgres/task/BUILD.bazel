load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "task",
    srcs = [
        "customer_task.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/task",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres",
        "//backend/common/rpc/framework/log",
        "//backend/proto/customer/v2:customer",
        "@io_gorm_gorm//:gorm",
        "@io_gorm_gorm//clause",
    ],
)
