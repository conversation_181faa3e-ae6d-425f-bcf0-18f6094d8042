load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "customfield",
    srcs = [
        "entity.go",
        "repo.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customfield",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres",
        "//backend/proto/customer/v2:customer",
        "@io_gorm_datatypes//:datatypes",
        "@io_gorm_gorm//:gorm",
    ],
)
