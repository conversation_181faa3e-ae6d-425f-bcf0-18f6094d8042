package customfield

import (
	"context"
	"fmt"
	"strings"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Repository interface {
	// Definition相关
	Create(ctx context.Context, dwo *DefinitionWithOptions) (*DefinitionWithOptions, error)
	Update(ctx context.Context, dwo *DefinitionWithOptions) (*DefinitionWithOptions, error)
	Get(ctx context.Context, id int64) (*DefinitionWithOptions, error)
	List(ctx context.Context,
		filter *DefinitionListFilter,
		pagination *Pagination,
		orderBy *DefinitionOrderBy,
	) (*DefinitionCursorResult, error)

	// Option相关（只保留增改，查都通过 DefinitionWithOptions 聚合返回）
	CreateOption(ctx context.Context, opt *Option) (*Option, error)
	UpdateOption(ctx context.Context, opt *Option) (*Option, error)
}

type impl struct {
	db *gorm.DB
}

func New() Repository {
	return &impl{db: postgres.GetDB()}
}

// Definition相关
func (i *impl) Create(ctx context.Context, dwo *DefinitionWithOptions) (*DefinitionWithOptions, error) {
	return i.createDefinitionWithOptionsTx(ctx, dwo, i.db)
}

func (i *impl) Update(ctx context.Context, dwo *DefinitionWithOptions) (*DefinitionWithOptions, error) {
	tx := i.db.Begin()
	if tx.Error != nil {
		return nil, tx.Error
	}
	defer tx.Rollback()
	def := dwo.Definition
	if err := tx.WithContext(ctx).Save(def).Error; err != nil {
		return nil, err
	}
	oldOptions, err := i.getOptionsByFieldID(ctx, tx, def.ID)
	if err != nil {
		return nil, err
	}
	oldIDMap := make(map[int64]*Option)
	for _, o := range oldOptions {
		oldIDMap[o.ID] = &o
	}
	newIDMap := make(map[int64]*Option)
	for _, opt := range dwo.Options {
		if opt.ID != 0 {
			newIDMap[opt.ID] = opt
		}
	}
	var toDeleteIDs []int64
	for id := range oldIDMap {
		if _, found := newIDMap[id]; !found {
			toDeleteIDs = append(toDeleteIDs, id)
		}
	}
	if err := i.deleteOptionsByIDs(ctx, tx, toDeleteIDs); err != nil {
		return nil, err
	}
	updatedOpts, err := i.upsertOptions(ctx, tx, def.ID, dwo.Options, oldIDMap)
	if err != nil {
		return nil, err
	}
	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	return &DefinitionWithOptions{
		Definition: def,
		Options:    updatedOpts,
	}, nil
}

func (i *impl) Get(ctx context.Context, id int64) (*DefinitionWithOptions, error) {
	var def Definition
	err := i.db.WithContext(ctx).Where("id = ?", id).First(&def).Error
	if err != nil {
		return nil, err
	}
	var opts []*Option
	err = i.db.WithContext(ctx).Where("field_id = ?", id).Order("sort_order").Find(&opts).Error
	if err != nil {
		return nil, err
	}

	return &DefinitionWithOptions{
		Definition: &def,
		Options:    opts,
	}, nil
}

func (i *impl) List(ctx context.Context,
	filter *DefinitionListFilter, pagination *Pagination,
	orderBy *DefinitionOrderBy) (*DefinitionCursorResult, error) {

	var total *int64
	if pagination != nil && pagination.ReturnTotalSize {
		count, err := i.getTotalCount(ctx, filter)
		if err != nil {
			return nil, err
		}
		total = &count
	}

	query := i.db.WithContext(ctx).Model(&Definition{})
	query = applyDefinitionFilter(query, filter)
	query = i.applyCursor(query, pagination.Cursor, orderBy)
	query = applyDefinitionOrderBy(query, orderBy)

	// 多查一条用于判断是否有下一页
	pageSize := int(pagination.PageSize)
	if pageSize == 0 {
		pageSize = 50 // 默认分页大小
	}
	query = query.Limit(pageSize + 1)

	var defs []*Definition
	if err := query.Find(&defs).Error; err != nil {
		return nil, err
	}

	hasNext := len(defs) > pageSize
	if hasNext {
		defs = defs[:pageSize] // 移除多查的那一条
	}

	// 批量查询所有相关的 options，避免 N+1 问题
	var defIDs []int64
	for _, def := range defs {
		defIDs = append(defIDs, def.ID)
	}

	var options []*Option
	if len(defIDs) > 0 {
		if err := i.db.WithContext(ctx).
			Where("field_id IN ? AND deleted_time IS NULL", defIDs).
			Order("field_id, sort_order").
			Find(&options).Error; err != nil {
			return nil, err
		}
	}

	// 按 field_id 分组 options
	optionsByFieldID := make(map[int64][]*Option)
	for _, opt := range options {
		optionsByFieldID[opt.FieldID] = append(optionsByFieldID[opt.FieldID], opt)
	}

	// 构建结果
	var result []*DefinitionWithOptions
	for _, def := range defs {
		dwo := &DefinitionWithOptions{
			Definition: def,
			Options:    optionsByFieldID[def.ID],
		}
		result = append(result, dwo)
	}

	return &DefinitionCursorResult{
		Data:       result,
		HasNext:    hasNext,
		TotalCount: total,
	}, nil
}

// getTotalCount 获取总数量
func (i *impl) getTotalCount(ctx context.Context, filter *DefinitionListFilter) (int64, error) {
	query := i.db.WithContext(ctx).Model(&Definition{})
	query = applyDefinitionFilter(query, filter)

	var count int64
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}

// Option相关
func (i *impl) CreateOption(ctx context.Context, opt *Option) (*Option, error) {
	err := i.db.WithContext(ctx).Create(opt).Error
	if err != nil {
		return nil, err
	}

	return opt, nil
}

func (i *impl) UpdateOption(ctx context.Context, opt *Option) (*Option, error) {
	err := i.db.WithContext(ctx).Updates(opt).Error
	if err != nil {
		return nil, err
	}

	return opt, nil
}

func (i *impl) createDefinitionWithOptionsTx(ctx context.Context,
	dwo *DefinitionWithOptions, db *gorm.DB) (*DefinitionWithOptions, error) {
	returnValue := &DefinitionWithOptions{}
	tx := db.Begin()
	if tx.Error != nil {
		return nil, tx.Error
	}
	// 创建 definition
	def := dwo.Definition
	if err := tx.WithContext(ctx).Create(def).Error; err != nil {
		tx.Rollback()

		return nil, err
	}
	returnValue.Definition = def
	// 创建 options
	var createdOpts []*Option
	for _, opt := range dwo.Options {
		opt.FieldID = def.ID
		if err := tx.WithContext(ctx).Create(opt).Error; err != nil {
			tx.Rollback()

			return nil, err
		}
		createdOpts = append(createdOpts, opt)
	}
	returnValue.Options = createdOpts
	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	return returnValue, nil
}

// getOptionsByFieldID 查询未被删除的 options
func (i *impl) getOptionsByFieldID(ctx context.Context, db *gorm.DB, fieldID int64) ([]Option, error) {
	var opts []Option
	if err := db.WithContext(ctx).
		Where("field_id = ? AND state != ?", fieldID, customerpb.CustomField_DELETED.String()).
		Order("sort_order").
		Find(&opts).Error; err != nil {
		return nil, err
	}

	return opts, nil
}

// deleteOptionsByIDs 物理删除 options
func (i *impl) deleteOptionsByIDs(ctx context.Context, db *gorm.DB, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}

	return db.WithContext(ctx).Where("id IN (?)", ids).Delete(&Option{}).Error
}

// upsertOptions 新增/更新 options
func (i *impl) upsertOptions(ctx context.Context,
	db *gorm.DB, fieldID int64, newOpts []*Option,
	oldIDMap map[int64]*Option) ([]*Option, error) {
	var updatedOpts []*Option
	for _, opt := range newOpts {
		opt.FieldID = fieldID
		if opt.ID == 0 {
			if err := db.WithContext(ctx).Create(opt).Error; err != nil {
				return nil, err
			}
			updatedOpts = append(updatedOpts, opt)
		} else if _, found := oldIDMap[opt.ID]; !found {
			if err := db.WithContext(ctx).Create(opt).Error; err != nil {
				return nil, err
			}
			updatedOpts = append(updatedOpts, opt)
		} else {
			if err := db.WithContext(ctx).Save(opt).Error; err != nil {
				return nil, err
			}
			updatedOpts = append(updatedOpts, opt)
		}
	}

	return updatedOpts, nil
}

// Filter、分页、排序结构体和辅助方法

func applyDefinitionFilter(query *gorm.DB, filter *DefinitionListFilter) *gorm.DB {
	if filter == nil {
		return query
	}
	if len(filter.IDs) > 0 {
		query = query.Where("id IN (?)", filter.IDs)
	}
	if filter.OrganizationType != 0 {
		query = query.Where("organization_type = ?", filter.OrganizationType)
	}
	if filter.OrganizationID != 0 {
		query = query.Where("organization_id = ?", filter.OrganizationID)
	}
	if filter.AssociationType != customerpb.CustomField_ASSOCIATION_TYPE_UNSPECIFIED {
		query = query.Where("association_type = ?", filter.AssociationType.String())
	}
	if len(filter.FieldNames) > 0 {
		query = query.Where("field_code IN (?)", filter.FieldNames)
	}
	if len(filter.States) > 0 {
		states := make([]string, 0, len(filter.States))
		for _, state := range filter.States {
			states = append(states, state.String())
		}
		query = query.Where("state IN (?)", states)
	}

	return query
}

// applyCursor 应用游标分页
func (i *impl) applyCursor(query *gorm.DB, cursor *postgres.Cursor, orderBy *DefinitionOrderBy) *gorm.DB {
	if cursor == nil {
		return query
	}

	if orderBy != nil && orderBy.Direction == customerpb.Direction_DESC {
		query = query.Where("(created_time, id) < (?, ?)", cursor.CreatedAt, cursor.ID)
	} else {
		query = query.Where("(created_time, id) > (?, ?)", cursor.CreatedAt, cursor.ID)
	}

	return query
}

func applyDefinitionOrderBy(query *gorm.DB, orderBy *DefinitionOrderBy) *gorm.DB {
	if orderBy != nil {
		// 使用 String() 方法获取枚举字符串，然后转换为数据库字段名
		fieldStr := orderBy.Field.String()
		dbField := strings.ToLower(fieldStr)

		// 特殊处理：FIELD_UNSPECIFIED 默认使用 created_time
		if fieldStr == "FIELD_UNSPECIFIED" {
			dbField = "created_time"
		}

		// 使用 String() 方法获取方向字符串
		directionStr := orderBy.Direction.String()
		direction := directionStr

		// 特殊处理：DIRECTION_UNSPECIFIED 默认使用 DESC
		if directionStr == "DIRECTION_UNSPECIFIED" {
			direction = "DESC"
		}

		query = query.Order(fmt.Sprintf("%s %s, id %s", dbField, direction, direction))
	} else {
		query = query.Order("created_time DESC, id DESC")
	}

	return query
}
