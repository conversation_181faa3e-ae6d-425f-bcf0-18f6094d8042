package customer

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

const (
	directionASC     = "ASC"
	directionDESC    = "DESC"
	fieldCreatedTime = "created_time"
)

type Repository interface {
	Create(ctx context.Context, customer *Customer) (*Customer, error)
	Get(ctx context.Context, id int64, customerType customerpb.CustomerType) (*Customer, error)
	GetByID(ctx context.Context, id int64) (*Customer, error)
	ListByCursor(ctx context.Context,
		filter *ListFilter, pagination *Pagination, orderBy *OrderBy) (*CursorResult, error)
	ListMetadataByCursor(ctx context.Context,
		filter *ListFilter, pagination *Pagination, orderBy *MetadataOrderBy) (*CursorResult, error)

	Update(ctx context.Context, customer *Customer) (*Customer, error)
	Delete(ctx context.Context, id int64) error
	Tx(ctx context.Context, fn func(repo Repository) error) error
	WithTx(tx *gorm.DB) Repository

	// SearchIDsByKeyword 在给定作用域内按关键词搜索客户ID（匹配邮箱或全名）
	SearchIDsByKeyword(ctx context.Context, companyID int64, businessIDs []int64, keyword string) ([]int64, error)

	// SearchIDsByLastName 在给定作用域内按姓氏模糊匹配客户ID
	SearchIDsByLastName(ctx context.Context, companyID int64, businessIDs []int64, lastName string) ([]int64, error)

	// FilterCustomerIDs 根据过滤条件查询客户ID
	FilterCustomerIDs(ctx context.Context, filter *FilterConditions) ([]int64, error)

	// FilterCustomerIDsByTag 根据标签过滤客户ID
	FilterCustomerIDsByTag(ctx context.Context, companyID int64, businessIDs []int64, customerIDs []int64,
		tagIDs []int64, operator string) ([]int64, error)

	// ListCustomerIDs 支持按给定字段/方向排序并进行 offset/limit 分页
	ListCustomerIDs(ctx context.Context, companyID int64, businessIDs []int64, customerIDs []int64,
		orderByField string, direction string, offset int, limit int) ([]int64, error)

	// ValidateActiveCustomerIDs 过滤出在指定公司下仍有效的客户ID
	ValidateActiveCustomerIDs(ctx context.Context, companyID int64, customerIDs []int64) ([]int64, error)

	// GetCustomersBasicRows 查询客户基础信息（用于装配列表），包含邮箱（如存在）
	GetCustomersBasicRows(ctx context.Context, companyID int64, customerIDs []int64) ([]BasicRow, error)
}

type impl struct {
	db *gorm.DB
}

func New() Repository {
	return &impl{
		db: postgres.GetDB(),
	}
}

func (i *impl) Create(ctx context.Context, customer *Customer) (*Customer, error) {
	err := i.db.WithContext(ctx).Create(customer).Error
	if err != nil {
		return nil, err
	}

	return customer, nil
}

func (i *impl) Get(ctx context.Context, id int64, customerType customerpb.CustomerType) (*Customer, error) {
	var customer Customer
	err := i.db.WithContext(ctx).
		Where("id = ?", id).
		Where("customer_type = ?", customerType.String()).
		Where("state != ?", customerpb.Customer_DELETED.String()).
		First(&customer).Error
	if err != nil {
		return nil, err
	}

	return &customer, nil
}

func (i *impl) GetByID(ctx context.Context, id int64) (*Customer, error) {
	var customer Customer
	err := i.db.WithContext(ctx).
		Where("id = ?", id).
		Where("state != ?", customerpb.Customer_DELETED.String()).
		First(&customer).Error
	if err != nil {
		return nil, err
	}

	return &customer, nil
}

// ListWithCursor 基于游标分页的列表查询
func (i *impl) ListByCursor(ctx context.Context,
	filter *ListFilter, pagination *Pagination, orderBy *OrderBy) (*CursorResult, error) {
	var total *int64
	if pagination.ReturnTotalSize {
		count, err := i.getTotalCount(ctx, filter)
		if err != nil {
			return nil, err
		}
		total = &count
	}
	query := i.db.WithContext(ctx).Model(&Customer{})
	query = i.applyFilter(query, filter)
	query = i.applyCursor(query, pagination.Cursor, orderBy)
	query = i.applyOrderBy(query, orderBy)

	// 多查一条用于判断是否有下一页
	query = query.Limit(int(pagination.PageSize + 1))

	var customers []*Customer
	if err := query.Find(&customers).Error; err != nil {
		return nil, err
	}

	hasNext := len(customers) > int(pagination.PageSize)
	if hasNext {
		customers = customers[:pagination.PageSize]
	}

	return &CursorResult{
		Data:       customers,
		HasNext:    hasNext,
		TotalCount: total,
	}, nil
}

func (i *impl) applyFilter(query *gorm.DB, filter *ListFilter) *gorm.DB {
	if filter == nil {
		return query
	}

	if len(filter.IDs) > 0 {
		query = query.Where("id IN (?)", filter.IDs)
	}

	if filter.OrganizationType != nil {
		query = query.Where("organization_type = ?", filter.OrganizationType.String())
	}

	if filter.OrganizationID != 0 {
		query = query.Where("organization_id = ?", filter.OrganizationID)
	}

	if filter.CustomerType != 0 {
		query = query.Where("customer_type = ?", filter.CustomerType.String())
	}
	if len(filter.ReferralSourceIDs) > 0 {
		query = query.Where("referral_source_id IN (?)", filter.ReferralSourceIDs)
	}
	if len(filter.ConvertedCustomerIDs) > 0 {
		query = query.Where("convert_to_customer_id IN (?)", filter.ConvertedCustomerIDs)
	}
	if filter.IsConverted != nil {
		if *filter.IsConverted {
			query = query.Where("convert_to_customer_id > 0")
		} else {
			query = query.Where("convert_to_customer_id = 0")
		}
	}

	if len(filter.States) > 0 {
		states := make([]string, 0, len(filter.States))
		for _, state := range filter.States {
			states = append(states, state.String())
		}
		query = query.Where("state IN (?)", states)
	}

	if len(filter.OwnerStaffIDs) > 0 {
		query = query.Where("owner_staff_id IN (?)", filter.OwnerStaffIDs)
	}

	if len(filter.LifecycleIDs) > 0 {
		query = query.Where("life_cycle_id IN (?)", filter.LifecycleIDs)
	}

	return query
}

func (i *impl) applyCursor(query *gorm.DB, cursor *postgres.Cursor, orderBy *OrderBy) *gorm.DB {
	if cursor == nil {
		return query
	}

	if orderBy != nil && orderBy.Direction == customerpb.Direction_DESC {
		query = query.Where("(created_time, id) < (?, ?)", cursor.CreatedAt, cursor.ID)
	} else {
		query = query.Where("(created_time, id) > (?, ?)", cursor.CreatedAt, cursor.ID)
	}

	return query
}

func (i *impl) applyOrderBy(query *gorm.DB, orderBy *OrderBy) *gorm.DB {
	if orderBy != nil {
		query = query.
			Order(fmt.Sprintf("%s %s, id %s", orderBy.Field.String(),
				orderBy.Direction.String(), orderBy.Direction.String()))
	} else {
		query = query.Order("created_time DESC, id DESC")
	}

	return query
}

func (i *impl) applyMetadataOrderBy(query *gorm.DB, orderBy *MetadataOrderBy) *gorm.DB {
	if orderBy == nil {
		return query.Order("created_time DESC, id DESC")
	}

	var orderField string
	switch orderBy.Field {
	case customerpb.ListMetadataRequest_Sorting_ID:
		orderField = "id"
	case customerpb.ListMetadataRequest_Sorting_CREATED_TIME:
		orderField = fieldCreatedTime
	case customerpb.ListMetadataRequest_Sorting_UPDATED_TIME:
		orderField = "updated_time"
	default:
		orderField = fieldCreatedTime
	}

	direction := "DESC"
	if orderBy.Direction == customerpb.Direction_ASC {
		direction = "ASC"
	}

	return query.Order(fmt.Sprintf("%s %s, id %s", orderField, direction, direction))
}

func (i *impl) getTotalCount(ctx context.Context, filter *ListFilter) (int64, error) {
	query := i.db.WithContext(ctx).Model(&Customer{})
	query = i.applyFilter(query, filter)

	var count int64
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}

func (i *impl) ListMetadataByCursor(ctx context.Context,
	filter *ListFilter, pagination *Pagination, orderBy *MetadataOrderBy) (*CursorResult, error) {
	var total *int64
	if pagination.ReturnTotalSize {
		count, err := i.getTotalCount(ctx, filter)
		if err != nil {
			return nil, err
		}
		total = &count
	}
	query := i.db.WithContext(ctx).Model(&Customer{})
	query = i.applyFilter(query, filter)
	var cursorOrderBy *OrderBy
	switch orderBy.Field {
	case customerpb.ListMetadataRequest_Sorting_ID:
		cursorOrderBy = &OrderBy{
			Field:     customerpb.ListCustomersRequest_Sorting_ID,
			Direction: orderBy.Direction,
		}
	case customerpb.ListMetadataRequest_Sorting_CREATED_TIME:
		cursorOrderBy = &OrderBy{
			Field:     customerpb.ListCustomersRequest_Sorting_CREATED_TIME,
			Direction: orderBy.Direction,
		}
	case customerpb.ListMetadataRequest_Sorting_UPDATED_TIME:
		cursorOrderBy = &OrderBy{
			Field:     customerpb.ListCustomersRequest_Sorting_UPDATED_TIME,
			Direction: orderBy.Direction,
		}
	default:
		cursorOrderBy = &OrderBy{
			Field:     customerpb.ListCustomersRequest_Sorting_CREATED_TIME,
			Direction: orderBy.Direction,
		}
	}
	query = i.applyCursor(query, pagination.Cursor, cursorOrderBy)
	query = i.applyMetadataOrderBy(query, orderBy)

	query = query.Limit(int(pagination.PageSize + 1))

	var customers []*Customer
	if err := query.Find(&customers).Error; err != nil {
		return nil, err
	}

	hasNext := len(customers) > int(pagination.PageSize)
	if hasNext {
		customers = customers[:pagination.PageSize]
	}

	return &CursorResult{
		Data:       customers,
		HasNext:    hasNext,
		TotalCount: total,
	}, nil
}

func (i *impl) Update(ctx context.Context, customer *Customer) (*Customer, error) {
	err := i.db.WithContext(ctx).Updates(customer).Error
	if err != nil {
		return nil, err
	}

	return customer, nil
}

func (i *impl) Delete(ctx context.Context, id int64) error {
	now := time.Now().UTC()

	return i.db.WithContext(ctx).Model(&Customer{}).
		Where("id = ?", id).
		Updates(&Customer{
			DeletedTime: &now,
			State:       customerpb.Customer_DELETED,
		}).Error
}

func (i *impl) Tx(ctx context.Context, fn func(repo Repository) error) error {
	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		return fn(&impl{db: tx})
	})
}

func (i *impl) WithTx(tx *gorm.DB) Repository {
	return &impl{db: tx}
}

// SearchIDsByKeyword 在给定作用域内按关键词搜索客户ID（匹配邮箱或全名）
func (i *impl) SearchIDsByKeyword(
	ctx context.Context,
	companyID int64,
	businessIDs []int64,
	keyword string,
) ([]int64, error) {
	like := "%" + keyword + "%"
	var ids []int64

	q := i.db.WithContext(ctx).
		Model(&Customer{}).
		Joins(
			"JOIN customer_related_data crd ON crd.customer_id = customer.id AND crd.state != ?",
			customerpb.CustomerRelatedData_DELETED.String(),
		).
		Joins(
			`LEFT JOIN contact ct ON ct.customer_id = customer.id 
				AND ct.state = ? AND ct.organization_type = ? AND ct.organization_id = ?`,
			customerpb.Contact_ACTIVE.String(),
			customerpb.OrganizationRef_COMPANY.String(),
			companyID,
		).
		Where("customer.state != ?", customerpb.Customer_DELETED.String()).
		Where("customer.organization_type = ?", customerpb.OrganizationRef_COMPANY.String()).
		Where("customer.organization_id = ?", companyID).
		Where("crd.company_id = ?", companyID)

	if len(businessIDs) > 0 {
		q = q.Where("crd.business_id IN ?", businessIDs)
	}

	if err := q.
		Where(
			"(ct.email ILIKE ? OR crd.login_email ILIKE ? OR "+
				"(customer.given_name || ' ' || customer.family_name) ILIKE ?)",
			like,
			like,
			like,
		).
		Distinct("customer.id").
		Pluck("customer.id", &ids).Error; err != nil {
		return nil, err
	}

	return ids, nil
}

// SearchIDsByLastName 在给定作用域内按姓氏模糊匹配客户ID
func (i *impl) SearchIDsByLastName(
	ctx context.Context,
	companyID int64,
	businessIDs []int64,
	lastName string,
) ([]int64, error) {
	like := "%" + lastName + "%"
	var ids []int64

	q := i.db.WithContext(ctx).
		Model(&Customer{}).
		Joins(
			"JOIN customer_related_data crd ON crd.customer_id = customer.id AND crd.state != ?",
			customerpb.CustomerRelatedData_DELETED.String(),
		).
		Where("customer.state != ?", customerpb.Customer_DELETED.String()).
		Where("customer.organization_type = ?", customerpb.OrganizationRef_COMPANY.String()).
		Where("customer.organization_id = ?", companyID).
		Where("crd.company_id = ?", companyID)

	if len(businessIDs) > 0 {
		q = q.Where("crd.business_id IN ?", businessIDs)
	}

	if err := q.
		Where("customer.family_name ILIKE ?", like).
		Distinct("customer.id").
		Pluck("customer.id", &ids).Error; err != nil {
		return nil, err
	}

	return ids, nil
}

// FilterCustomerIDs 根据过滤条件查询客户ID
func (i *impl) FilterCustomerIDs(ctx context.Context, filter *FilterConditions) ([]int64, error) {
	if filter == nil || filter.CompanyID <= 0 {
		return nil, fmt.Errorf("company_id is required")
	}

	var ids []int64

	// 基础查询：从 customer_related_data 表查询
	q := i.db.WithContext(ctx).
		Table("customer_related_data crd").
		Joins("JOIN customer c ON c.id = crd.customer_id AND c.state != ?", customerpb.Customer_DELETED.String()).
		Where("c.organization_type = ?", customerpb.OrganizationRef_COMPANY.String()).
		Where("c.organization_id = ?", filter.CompanyID).
		Where("crd.state != ? AND crd.company_id = ?",
			customerpb.CustomerRelatedData_DELETED.String(), filter.CompanyID)

	// 应用 Scope 约束
	if len(filter.BusinessIDs) > 0 {
		q = q.Where("crd.business_id IN ?", filter.BusinessIDs)
	}
	if len(filter.CustomerIDs) > 0 {
		q = q.Where("crd.customer_id IN ?", filter.CustomerIDs)
	}

	// 构建过滤条件
	if len(filter.Conditions) > 0 {
		conditionSQL, args, err := i.buildFilterConditions(filter.Conditions, filter.Combiner)
		if err != nil {
			return nil, fmt.Errorf("build filter conditions: %w", err)
		}
		if conditionSQL != "" {
			q = q.Where(conditionSQL, args...)
		}
	}

	if err := q.Distinct("crd.customer_id").Pluck("crd.customer_id", &ids).Error; err != nil {
		return nil, fmt.Errorf("query customer ids: %w", err)
	}

	return ids, nil
}

// buildFilterConditions 构建过滤条件SQL
func (i *impl) buildFilterConditions(conditions []FilterCondition, combiner string) (string, []interface{}, error) {
	if len(conditions) == 0 {
		return "", nil, nil
	}

	var sqlParts []string
	var args []interface{}

	for _, condition := range conditions {
		conditionSQL, conditionArgs, err := i.buildSingleCondition(condition)
		if err != nil {
			return "", nil, err
		}
		if conditionSQL != "" {
			sqlParts = append(sqlParts, conditionSQL)
			args = append(args, conditionArgs...)
		}
	}

	if len(sqlParts) == 0 {
		return "", nil, nil
	}

	// 默认使用 AND 连接
	if combiner != "OR" {
		combiner = "AND"
	}

	return "(" + strings.Join(sqlParts, " "+combiner+" ") + ")", args, nil
}

// buildSingleCondition 构建单个条件
func (i *impl) buildSingleCondition(condition FilterCondition) (string, []interface{}, error) {
	column, err := i.mapFieldToColumn(condition.Field)
	if err != nil {
		return "", nil, err
	}

	switch condition.Operator {
	case "EQUAL":
		return column + " = ?", []interface{}{condition.Value}, nil
	case "NOT_EQUAL":
		return column + " != ?", []interface{}{condition.Value}, nil
	case "LESS_THAN":
		return column + " < ?", []interface{}{condition.Value}, nil
	case "LESS_THAN_OR_EQUAL":
		return column + " <= ?", []interface{}{condition.Value}, nil
	case "GREATER_THAN":
		return column + " > ?", []interface{}{condition.Value}, nil
	case "GREATER_THAN_OR_EQUAL":
		return column + " >= ?", []interface{}{condition.Value}, nil
	case "FILTER_IN":
		if len(condition.Values) == 0 {
			return "", nil, fmt.Errorf("IN operator requires values")
		}
		placeholders := make([]string, len(condition.Values))
		args := make([]interface{}, len(condition.Values))
		for i, v := range condition.Values {
			placeholders[i] = "?"
			args[i] = v
		}

		return column + " IN (" + strings.Join(placeholders, ",") + ")", args, nil
	case "FILTER_NOT_IN":
		if len(condition.Values) == 0 {
			return "", nil, fmt.Errorf("NOT_IN operator requires values")
		}
		placeholders := make([]string, len(condition.Values))
		args := make([]interface{}, len(condition.Values))
		for i, v := range condition.Values {
			placeholders[i] = "?"
			args[i] = v
		}

		return column + " NOT IN (" + strings.Join(placeholders, ",") + ")", args, nil
	case "LIKE":
		// 清理可能存在的额外引号
		value := strings.Trim(condition.Value, "'\"")

		return column + " ILIKE CONCAT('%', ?::text, '%')", []interface{}{value}, nil
	case "IS_NULL":
		return column + " IS NULL", nil, nil
	case "IS_NOT_NULL":
		return column + " IS NOT NULL", nil, nil
	case "BETWEEN":
		if condition.Start == "" || condition.End == "" {
			return "", nil, fmt.Errorf("BETWEEN operator requires both start and end values")
		}

		return column + " BETWEEN ? AND ?", []interface{}{condition.Start, condition.End}, nil
	default:
		return "", nil, fmt.Errorf("unsupported operator: %s", condition.Operator)
	}
}

// mapFieldToColumn 将前端字段名映射到数据库列名
func (i *impl) mapFieldToColumn(field string) (string, error) {
	fieldMap := map[string]string{
		// Client - 来自 customer_related_data 表
		"client_id":                     "crd.customer_id",
		"first_name":                    "c.given_name",
		"last_name":                     "c.family_name",
		"client_created_from":           "crd.source",
		"referral_source":               "c.referral_source_id",
		"preferred_frequency_day":       "crd.preferred_frequency_day",
		"preferred_groomer":             "crd.preferred_groomer_id",
		"preferred_weekday":             "crd.preferred_day",
		"unsubscribed_marketing_emails": "crd.is_unsubscribed",
		"primary_email":                 "crd.login_email",
		"preferred_business":            "crd.business_id",
		"account_id":                    "crd.account_id",
		"creation_date":                 "c.created_time",
		"customer_type":                 "c.customer_type",
		"allocate_staff_id":             "c.owner_staff_id",
		"life_cycle_id":                 "c.life_cycle_id",
		"action_state_id":               "c.action_state_id",
		// Status
		"blocked_from_message": "crd.is_block_message",
		"blocked_from_ob":      "crd.is_block_online_booking",
		"inactive_client":      "c.state",
		"lapsed_client": "(crd.last_service_time::timestamp + " +
			"(crd.preferred_frequency_day || ' days')::interval)",
	}

	column, exists := fieldMap[field]
	if !exists {
		return "", fmt.Errorf("unsupported field: %s", field)
	}

	// 处理特殊字段
	switch field {
	case "has_account":
		return "", fmt.Errorf("has_account field should be handled specially")
	case "client_status", "client_type":
		return "", fmt.Errorf("field %s not implemented yet", field)
	}

	return column, nil
}

// FilterCustomerIDsByTag 根据标签过滤客户ID
func (i *impl) FilterCustomerIDsByTag(
	ctx context.Context,
	companyID int64,
	businessIDs []int64,
	customerIDs []int64,
	tagIDs []int64,
	operator string,
) ([]int64, error) {
	if companyID <= 0 || len(tagIDs) == 0 {
		return nil, fmt.Errorf("company_id and tag_ids are required")
	}

	var ids []int64

	// 基础查询：通过 customer_related_data 查询
	q := i.db.WithContext(ctx).
		Table("customer_related_data crd").
		Joins("JOIN customer c ON c.id = crd.customer_id AND c.state != ?", customerpb.Customer_DELETED.String()).
		Where("crd.state != ? AND crd.company_id = ?",
			customerpb.CustomerRelatedData_DELETED.String(), companyID)

	// 应用业务范围过滤
	if len(businessIDs) > 0 {
		q = q.Where("crd.business_id IN ?", businessIDs)
	}

	// 应用客户范围过滤
	if len(customerIDs) > 0 {
		q = q.Where("crd.customer_id IN ?", customerIDs)
	}

	// 根据操作符类型选择不同的连接方式，使用 activity_rel 表查询标签关系
	switch strings.ToUpper(operator) {
	case "IN":
		// 内连接：只返回有指定标签的客户
		q = q.Joins(`
			JOIN activity_rel ar ON ar.customer_id = crd.customer_id 
				AND ar.activity_type = 'TAG' 
				AND ar.delete_time IS NULL
		`).
			Where("ar.activity_id IN ?", tagIDs)

	case "NOT_IN":
		// 左连接：返回没有指定标签的客户，或者没有任何标签的客户
		q = q.Joins(`
			LEFT JOIN activity_rel ar ON ar.customer_id = crd.customer_id 
				AND ar.activity_type = 'TAG' 
				AND ar.delete_time IS NULL 
				AND ar.activity_id IN ?
		`, tagIDs).
			Where("ar.activity_id IS NULL")

	default:
		return nil, fmt.Errorf("unsupported operator: %s", operator)
	}

	if err := q.Distinct("crd.customer_id").Pluck("crd.customer_id", &ids).Error; err != nil {
		return nil, fmt.Errorf("query customer ids by tag: %w", err)
	}

	return ids, nil
}

// CustomerBasicRow 用于列表装配的客户基础信息数据行
type BasicRow struct {
	ID                     int64  `gorm:"column:id"`
	BusinessID             int64  `gorm:"column:business_id"`
	FirstName              string `gorm:"column:first_name"`
	LastName               string `gorm:"column:last_name"`
	AvatarPath             string `gorm:"column:avatar_path"`
	ClientColor            string `gorm:"column:client_color"`
	PreferredFrequencyType int64  `gorm:"column:preferred_frequency_type"`
	PreferredFrequencyDay  int64  `gorm:"column:preferred_frequency_day"`
	Inactive               int32  `gorm:"column:inactive"`
	IsUnsubscribed         bool   `gorm:"column:is_unsubscribed"`
	Source                 string `gorm:"column:source"`
	AccountID              int64  `gorm:"column:account_id"`
	CompanyID              int64  `gorm:"column:company_id"`
	Email                  string `gorm:"column:email"`
}

// ListCustomerIDs 支持排序与分页（offset, limit），按 id 二级排序稳定输出
func (i *impl) ListCustomerIDs(
	ctx context.Context,
	companyID int64,
	businessIDs []int64,
	customerIDs []int64,
	orderByField string,
	direction string,
	offset int,
	limit int,
) ([]int64, error) {
	if companyID <= 0 {
		return nil, fmt.Errorf("company_id is required")
	}

	dir := strings.ToUpper(strings.TrimSpace(direction))
	if dir != directionASC && dir != directionDESC {
		dir = directionASC
	}

	db := i.db.WithContext(ctx)

	// 构建基础查询条件
	baseQuery := db.Table("customer_related_data").
		Where("customer_related_data.state != ?", customerpb.CustomerRelatedData_DELETED.String()).
		Where("company_id = ?", companyID)
	if len(businessIDs) > 0 {
		baseQuery = baseQuery.Where("business_id IN ?", businessIDs)
	}
	if len(customerIDs) > 0 {
		baseQuery = baseQuery.Where("customer_id IN ?", customerIDs)
	}

	var ids []int64
	var err error

	switch strings.ToLower(orderByField) {
	case "first_name":
		sub := baseQuery.
			Joins("JOIN customer c ON c.id = customer_related_data.customer_id").
			Select("customer_related_data.customer_id, c.given_name, c.id as customer_sort_id").
			Order("c.given_name " + dir).
			Order("c.id DESC").
			Offset(offset).
			Limit(limit)

		err = db.Table("(?) as sub", sub).
			Distinct("customer_id").
			Pluck("customer_id", &ids).Error

	case "last_name":
		sub := baseQuery.
			Joins("JOIN customer c ON c.id = customer_related_data.customer_id").
			Select("customer_related_data.customer_id, c.family_name, c.id as customer_sort_id").
			Order("c.family_name " + dir).
			Order("c.id DESC").
			Offset(offset).
			Limit(limit)

		err = db.Table("(?) as sub", sub).
			Distinct("customer_id").
			Pluck("customer_id", &ids).Error

	default:
		err = baseQuery.
			Order("customer_id "+dir).
			Offset(offset).
			Limit(limit).
			Distinct("customer_id").
			Pluck("customer_id", &ids).Error
	}

	if err != nil {
		return nil, err
	}

	return ids, nil
}

// ValidateActiveCustomerIDs 过滤出有效客户ID（存在且未删除）
func (i *impl) ValidateActiveCustomerIDs(ctx context.Context, companyID int64, customerIDs []int64) ([]int64, error) {
	if companyID <= 0 {
		return nil, fmt.Errorf("company_id is required")
	}

	var ids []int64
	if err := i.db.WithContext(ctx).Table("customer_related_data").
		Where("company_id = ?", companyID).
		Where("customer_related_data.state != ?", customerpb.CustomerRelatedData_DELETED.String()).
		Where("customer_id IN ?", customerIDs).
		Distinct("customer_id").
		Pluck("customer_id", &ids).Error; err != nil {
		return nil, err
	}

	return ids, nil
}

// GetCustomersBasicRows 查询列表装配所需的客户基础信息（包含首个邮箱，如存在）
func (i *impl) GetCustomersBasicRows(ctx context.Context, companyID int64, customerIDs []int64) ([]BasicRow, error) {
	if len(customerIDs) == 0 {
		return []BasicRow{}, nil
	}

	db := i.db.WithContext(ctx)

	var rows []BasicRow

	// 基础信息（不包含 email）
	q := db.Table("customer c").
		Joins(
			"JOIN customer_related_data crd ON crd.customer_id = c.id AND crd.state != ?",
			customerpb.CustomerRelatedData_DELETED.String(),
		).
		Where("c.id IN ?", customerIDs)
	if companyID > 0 {
		q = q.Where("c.organization_type = ?", customerpb.OrganizationRef_COMPANY.String()).
			Where("c.organization_id = ?", companyID)
	}

	if err := q.Select(
		"c.id, crd.business_id, c.given_name as first_name, c.family_name as last_name, " +
			"c.avatar_path, crd.client_color, " +
			"crd.preferred_frequency_type, crd.preferred_frequency_day, " +
			"0 as inactive, crd.is_unsubscribed, crd.source, crd.account_id, crd.company_id",
	).Scan(&rows).Error; err != nil {
		return nil, err
	}

	// 获取每个客户的第一个非空 email
	type emailRow struct {
		CustomerID int64  `gorm:"column:customer_id"`
		Email      string `gorm:"column:email"`
	}
	var emailRows []emailRow
	emailQuery := db.Table("contact").
		Where("customer_id IN ?", customerIDs).
		Where("email != '' AND email IS NOT NULL").
		Where("deleted_time IS NULL")
	if companyID > 0 {
		emailQuery = emailQuery.Where("organization_type = ?", customerpb.OrganizationRef_COMPANY.String()).
			Where("organization_id = ?", companyID)
	}

	emailQuery = emailQuery.Select("DISTINCT ON (customer_id) customer_id, email").
		Order("customer_id, created_time ASC")

	if err := emailQuery.Scan(&emailRows).Error; err != nil {
		return nil, err
	}

	emailMap := make(map[int64]string, len(emailRows))
	for _, er := range emailRows {
		emailMap[er.CustomerID] = er.Email
	}

	// 回填 email
	for idx := range rows {
		if v, ok := emailMap[rows[idx].ID]; ok {
			rows[idx].Email = v
		}
	}

	return rows, nil
}
