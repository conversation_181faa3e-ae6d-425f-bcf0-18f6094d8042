package customer

import (
	"time"

	"google.golang.org/protobuf/types/known/structpb"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

// 客户核心信息表
type Customer struct {
	ID                  int64                           `gorm:"primaryKey;column:id;autoIncrement"`
	OrganizationType    customerpb.OrganizationRef_Type `gorm:"column:organization_type;serializer:proto_enum"`
	OrganizationID      int64                           `gorm:"column:organization_id"`
	GivenName           string                          `gorm:"column:given_name"`
	FamilyName          string                          `gorm:"column:family_name"`
	CustomerType        customerpb.CustomerType         `gorm:"column:customer_type;serializer:proto_enum"`
	State               customerpb.Customer_State       `gorm:"column:state;serializer:proto_enum"`
	CustomFields        *structpb.Struct                `gorm:"column:custom_fields;serializer:proto_json"`
	LifeCycleID         int64                           `gorm:"column:life_cycle_id"`
	OwnerStaffID        int64                           `gorm:"column:owner_staff_id"`
	ActionStateID       int64                           `gorm:"column:action_state_id;default:0"`
	AvatarPath          string                          `gorm:"column:avatar_path;default:''"`
	CustomerCode        string                          `gorm:"column:customer_code;default:''"`
	ReferralSourceID    int64                           `gorm:"column:referral_source_id;default:0"`
	ConvertToCustomerID int64                           `gorm:"column:convert_to_customer_id;default:0"`
	CreatedTime         time.Time                       `gorm:"column:created_time;autoCreateTime"`
	UpdatedTime         time.Time                       `gorm:"column:updated_time;autoUpdateTime"`
	DeletedTime         *time.Time                      `gorm:"column:deleted_time"`
}

// TableName 设置表名
func (Customer) TableName() string {
	return "customer"
}

type ListFilter struct {
	IDs                  []int64
	OrganizationType     *customerpb.OrganizationRef_Type
	OrganizationID       int64
	CustomerType         customerpb.CustomerType
	States               []customerpb.Customer_State
	OwnerStaffIDs        []int64
	LifecycleIDs         []int64
	ActionStateIDs       []int64
	CustomerCodes        []string
	ReferralSourceIDs    []int64
	ConvertedCustomerIDs []int64
	IsConverted          *bool
}

type Pagination struct {
	PageSize        int32
	Cursor          *postgres.Cursor
	ReturnTotalSize bool
}

type OrderBy struct {
	Field     customerpb.ListCustomersRequest_Sorting_Field
	Direction customerpb.Direction
}

type MetadataOrderBy struct {
	Field     customerpb.ListMetadataRequest_Sorting_Field
	Direction customerpb.Direction
}

// CursorResult 游标分页结果
type CursorResult struct {
	Data       []*Customer `json:"data"`     // 数据列表
	HasNext    bool        `json:"has_next"` // 是否有下一页
	TotalCount *int64      `json:"total_count"`
}

// FilterConditions 过滤条件
type FilterConditions struct {
	CompanyID   int64             `json:"company_id"`
	BusinessIDs []int64           `json:"business_ids"`
	CustomerIDs []int64           `json:"customer_ids"`
	Conditions  []FilterCondition `json:"conditions"`
	Combiner    string            `json:"combiner"` // AND 或 OR
}

// FilterCondition 单个过滤条件
type FilterCondition struct {
	Field    string   `json:"field"`
	Operator string   `json:"operator"`
	Value    string   `json:"value"`
	Values   []string `json:"values"`
	Start    string   `json:"start"`
	End      string   `json:"end"`
}
