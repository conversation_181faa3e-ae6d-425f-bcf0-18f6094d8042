// Code generated by MockGen. DO NOT EDIT.
// Source: ./postgres/customer/customer.go
//
// Generated by this command:
//
//	mockgen -source=./postgres/customer/customer.go -destination=./postgres/customer/mock/customer_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	customer "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
	gomock "go.uber.org/mock/gomock"
	gorm "gorm.io/gorm"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
	isgomock struct{}
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockRepository) Create(ctx context.Context, arg1 *customer.Customer) (*customer.Customer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, arg1)
	ret0, _ := ret[0].(*customer.Customer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockRepositoryMockRecorder) Create(ctx, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRepository)(nil).Create), ctx, arg1)
}

// Delete mocks base method.
func (m *MockRepository) Delete(ctx context.Context, id int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockRepositoryMockRecorder) Delete(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockRepository)(nil).Delete), ctx, id)
}

// FilterCustomerIDs mocks base method.
func (m *MockRepository) FilterCustomerIDs(ctx context.Context, filter *customer.FilterConditions) ([]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FilterCustomerIDs", ctx, filter)
	ret0, _ := ret[0].([]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FilterCustomerIDs indicates an expected call of FilterCustomerIDs.
func (mr *MockRepositoryMockRecorder) FilterCustomerIDs(ctx, filter any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FilterCustomerIDs", reflect.TypeOf((*MockRepository)(nil).FilterCustomerIDs), ctx, filter)
}

// FilterCustomerIDsByTag mocks base method.
func (m *MockRepository) FilterCustomerIDsByTag(ctx context.Context, companyID int64, businessIDs, customerIDs, tagIDs []int64, operator string) ([]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FilterCustomerIDsByTag", ctx, companyID, businessIDs, customerIDs, tagIDs, operator)
	ret0, _ := ret[0].([]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FilterCustomerIDsByTag indicates an expected call of FilterCustomerIDsByTag.
func (mr *MockRepositoryMockRecorder) FilterCustomerIDsByTag(ctx, companyID, businessIDs, customerIDs, tagIDs, operator any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FilterCustomerIDsByTag", reflect.TypeOf((*MockRepository)(nil).FilterCustomerIDsByTag), ctx, companyID, businessIDs, customerIDs, tagIDs, operator)
}

// Get mocks base method.
func (m *MockRepository) Get(ctx context.Context, id int64, customerType customerpb.CustomerType) (*customer.Customer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, id, customerType)
	ret0, _ := ret[0].(*customer.Customer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockRepositoryMockRecorder) Get(ctx, id, customerType any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockRepository)(nil).Get), ctx, id, customerType)
}

// GetByID mocks base method.
func (m *MockRepository) GetByID(ctx context.Context, id int64) (*customer.Customer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, id)
	ret0, _ := ret[0].(*customer.Customer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockRepositoryMockRecorder) GetByID(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockRepository)(nil).GetByID), ctx, id)
}

// ListByCursor mocks base method.
func (m *MockRepository) ListByCursor(ctx context.Context, filter *customer.ListFilter, pagination *customer.Pagination, orderBy *customer.OrderBy) (*customer.CursorResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByCursor", ctx, filter, pagination, orderBy)
	ret0, _ := ret[0].(*customer.CursorResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByCursor indicates an expected call of ListByCursor.
func (mr *MockRepositoryMockRecorder) ListByCursor(ctx, filter, pagination, orderBy any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByCursor", reflect.TypeOf((*MockRepository)(nil).ListByCursor), ctx, filter, pagination, orderBy)
}

// SearchIDsByKeyword mocks base method.
func (m *MockRepository) SearchIDsByKeyword(ctx context.Context, companyID int64, businessIDs []int64, keyword string) ([]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchIDsByKeyword", ctx, companyID, businessIDs, keyword)
	ret0, _ := ret[0].([]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchIDsByKeyword indicates an expected call of SearchIDsByKeyword.
func (mr *MockRepositoryMockRecorder) SearchIDsByKeyword(ctx, companyID, businessIDs, keyword any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchIDsByKeyword", reflect.TypeOf((*MockRepository)(nil).SearchIDsByKeyword), ctx, companyID, businessIDs, keyword)
}

// SearchIDsByLastName mocks base method.
func (m *MockRepository) SearchIDsByLastName(ctx context.Context, companyID int64, businessIDs []int64, lastName string) ([]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchIDsByLastName", ctx, companyID, businessIDs, lastName)
	ret0, _ := ret[0].([]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchIDsByLastName indicates an expected call of SearchIDsByLastName.
func (mr *MockRepositoryMockRecorder) SearchIDsByLastName(ctx, companyID, businessIDs, lastName any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchIDsByLastName", reflect.TypeOf((*MockRepository)(nil).SearchIDsByLastName), ctx, companyID, businessIDs, lastName)
}

// Tx mocks base method.
func (m *MockRepository) Tx(ctx context.Context, fn func(customer.Repository) error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Tx", ctx, fn)
	ret0, _ := ret[0].(error)
	return ret0
}

// Tx indicates an expected call of Tx.
func (mr *MockRepositoryMockRecorder) Tx(ctx, fn any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Tx", reflect.TypeOf((*MockRepository)(nil).Tx), ctx, fn)
}

// Update mocks base method.
func (m *MockRepository) Update(ctx context.Context, arg1 *customer.Customer) (*customer.Customer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, arg1)
	ret0, _ := ret[0].(*customer.Customer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockRepositoryMockRecorder) Update(ctx, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockRepository)(nil).Update), ctx, arg1)
}

// WithTx mocks base method.
func (m *MockRepository) WithTx(tx *gorm.DB) customer.Repository {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTx", tx)
	ret0, _ := ret[0].(customer.Repository)
	return ret0
}

// WithTx indicates an expected call of WithTx.
func (mr *MockRepositoryMockRecorder) WithTx(tx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTx", reflect.TypeOf((*MockRepository)(nil).WithTx), tx)
}
