package activityrel

import (
	"context"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	"github.com/MoeGolibrary/moego/backend/common/rpc/filters/opentelemetry/logs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ReadWriter interface {
	WithTransaction(context.Context, func(api ReadWriter) error) error
	Create(ctx context.Context, rel *ActivityRel) error
	CreateBatch(ctx context.Context, rel []*ActivityRel) error
	Update(ctx context.Context, rel *ActivityRel) error
	List(ctx context.Context, datum *ListActivityRelDatum) ([]*ActivityRel, error)
	Delete(ctx context.Context, id int64, staffID int64) error
	DeleteCustomerActivityRel(ctx context.Context, datum *DeleteCustomerActivityRel) error
	UpsertBatch(ctx context.Context, rel []*ActivityRel) error
}

type impl struct {
	db *gorm.DB
}

func New() ReadWriter {
	return &impl{
		db: postgres.GetDB(),
	}
}

func (i *impl) WithTransaction(ctx context.Context, fn func(api ReadWriter) error) error {
	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		txReadWriter := &impl{db: tx}

		return fn(txReadWriter)
	})
}

func (i *impl) Create(ctx context.Context, rel *ActivityRel) error {
	if err := i.db.WithContext(ctx).Create(rel).Error; err != nil {
		log.ErrorContextf(ctx, "Create ActivityRel err, err:%+v", err)

		return err
	}

	return nil
}

func (i *impl) CreateBatch(ctx context.Context, rel []*ActivityRel) error {
	if err := i.db.WithContext(ctx).Create(&rel).Error; err != nil {
		log.ErrorContextf(ctx, "CreateBatch ActivityRel err, err:%+v", err)

		return err
	}

	return nil
}

func (i *impl) Update(ctx context.Context, rel *ActivityRel) error {
	if err := i.db.WithContext(ctx).Updates(rel).Error; err != nil {
		log.ErrorContextf(ctx, "Update ActivityRel err, err:%+v", err)

		return err
	}

	return nil
}

type ListActivityRelDatum struct {
	ID           *int64
	CustomerIDs  []int64
	ActivityID   *int64
	ActivityType *ActivityRelType
}

func (i *impl) List(ctx context.Context, datum *ListActivityRelDatum) ([]*ActivityRel, error) {
	query := i.db.WithContext(ctx).Table("activity_rel").Where("delete_time IS NULL")

	if datum.ID != nil {
		query = query.Where("id = ?", *datum.ID)
	}
	if len(datum.CustomerIDs) > 0 {
		query = query.Where("customer_id IN (?)", datum.CustomerIDs)
	}
	if datum.ActivityID != nil {
		query = query.Where("activity_id = ?", *datum.ActivityID)
	}
	if datum.ActivityType != nil {
		query = query.Where("activity_type = ?", *datum.ActivityType)
	}

	var res []*ActivityRel
	if err := query.Order("update_time desc").Find(&res).Error; err != nil {
		log.ErrorContextf(ctx, "List ActivityRel err, err:%+v", err)

		return nil, err
	}

	return res, nil
}

func (i *impl) Delete(ctx context.Context, id int64, staffID int64) error {
	if err := i.db.WithContext(ctx).Table("activity_rel").
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"delete_by":   staffID,
			"delete_time": time.Now(),
		}).Error; err != nil {
		log.ErrorContextf(ctx, "Delete ActivityRel err, id:%d, staffID:%d", id, staffID)

		return err
	}

	return nil
}

type DeleteCustomerActivityRel struct {
	CustomerID   int64
	ActivityType ActivityRelType
	StaffID      int64
}

func (i *impl) DeleteCustomerActivityRel(ctx context.Context, datum *DeleteCustomerActivityRel) error {
	if err := i.db.WithContext(ctx).Table("activity_rel").
		Where("customer_id = ? AND activity_type = ? AND delete_time IS NULL",
			datum.CustomerID, datum.ActivityType).
		Updates(map[string]interface{}{
			"delete_by":   datum.StaffID,
			"delete_time": time.Now(),
		}).Error; err != nil {
		log.ErrorContextf(ctx, "DeleteCustomerActivityRel err, customerID:%d, activityType:%s, staffID:%d, err:%+v",
			datum.CustomerID, datum.ActivityType, datum.StaffID, err)

		return err
	}

	return nil
}

func (i *impl) UpsertBatch(ctx context.Context, rel []*ActivityRel) error {
	if err := i.db.Clauses(clause.OnConflict{
		Columns: []clause.Column{
			{Name: "customer_id"},
			{Name: "activity_id"},
			{Name: "activity_type"},
		},
		TargetWhere: clause.Where{
			Exprs: []clause.Expression{
				clause.Expr{SQL: "delete_time IS NULL"},
			},
		},
		DoUpdates: clause.Assignments(map[string]interface{}{
			"update_time": time.Now(),
			"update_by":   gorm.Expr("EXCLUDED.create_by"),
		}),
	}).Create(&rel).Error; err != nil {
		logs.Errorf(ctx, "UpsertBatch err, err:%+v, rel:%+v", err, rel)

		return err
	}

	return nil
}
