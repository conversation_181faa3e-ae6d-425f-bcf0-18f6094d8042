package activitylog

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ReadWriter interface {
	Create(context context.Context, historyLog *ActivityLog) error
	List(context context.Context, datum *ListActivityLogsDatum) ([]*ActivityLog, int64, error)
	Get(context context.Context, id int64) (*ActivityLog, error)
	Update(ctx context.Context, historyLog *ActivityLog) error
	WithTx(tx *gorm.DB) ReadWriter
}

type impl struct {
	db *gorm.DB
}

func New() ReadWriter {
	return &impl{
		db: postgres.GetDB(),
	}
}

func (i *impl) WithTx(tx *gorm.DB) ReadWriter {
	return &impl{db: tx}
}

func (i *impl) Create(ctx context.Context, historyLog *ActivityLog) error {
	if err := i.db.WithContext(ctx).Create(historyLog).Error; err != nil {
		log.ErrorContextf(ctx, "Create ActivityLog err: %v", err)

		return err
	}

	return nil
}

func (i *impl) List(ctx context.Context, datum *ListActivityLogsDatum) ([]*ActivityLog, int64, error) {
	query := i.db.WithContext(ctx).Table("activity_log")
	// filter
	if len(datum.CustomerIDs) > 0 {
		query = query.Where("customer_id IN (?)", datum.CustomerIDs)
	}
	if len(datum.ActivityLogTypes) > 0 {
		types := make([]string, 0, len(datum.ActivityLogTypes))
		for _, t := range datum.ActivityLogTypes {
			types = append(types, t.String())
		}
		query = query.Where("type IN (?)", types)
	}
	if len(datum.CompanyIDs) > 0 {
		query = query.Where("company_id IN (?)", datum.CompanyIDs)
	}
	if len(datum.IDs) > 0 {
		query = query.Where("id IN (?)", datum.IDs)
	}

	var total int64
	var res []*ActivityLog
	if err := query.Count(&total).
		Offset((datum.PageNum - 1) * (datum.PageSize)).
		Limit(datum.PageSize).
		Order("create_time desc").
		Find(&res).Error; err != nil {
		log.ErrorContextf(ctx, "List ActivityLog err, err:%+v", err)

		return nil, 0, err
	}

	return res, total, nil
}

func (i *impl) Get(ctx context.Context, id int64) (*ActivityLog, error) {
	var res *ActivityLog
	if err := i.db.WithContext(ctx).
		Where("id = ?", id).
		First(&res).Error; err != nil {
		log.ErrorContextf(ctx, "Get ActivityLog err, id:%d, err:%+v", id, err)

		return nil, err
	}

	return res, nil
}

func (i *impl) Update(ctx context.Context, historyLog *ActivityLog) error {
	if err := i.db.WithContext(ctx).Clauses(clause.Returning{}).Updates(historyLog).Error; err != nil {
		log.ErrorContextf(ctx, "Update ActivityLog err, historyLog:%+v, err:%+v", historyLog, err)

		return err
	}

	return nil
}
