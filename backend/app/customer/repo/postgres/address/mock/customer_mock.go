// Code generated by MockGen. DO NOT EDIT.
// Source: ./postgres/address/customer.go
//
// Generated by this command:
//
//	mockgen -source=./postgres/address/customer.go -destination=./postgres/address/mock/customer_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	address "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/address"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
	gomock "go.uber.org/mock/gomock"
	gorm "gorm.io/gorm"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
	isgomock struct{}
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// CountFilterAddressCustomerIDs mocks base method.
func (m *MockRepository) CountFilterAddressCustomerIDs(ctx context.Context, companyID int64, businessIDs, customerIDs []int64, havingConditions []address.HavingCondition, organizationID int64, organizationType customerpb.OrganizationRef_Type) ([]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountFilterAddressCustomerIDs", ctx, companyID, businessIDs, customerIDs, havingConditions, organizationID, organizationType)
	ret0, _ := ret[0].([]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountFilterAddressCustomerIDs indicates an expected call of CountFilterAddressCustomerIDs.
func (mr *MockRepositoryMockRecorder) CountFilterAddressCustomerIDs(ctx, companyID, businessIDs, customerIDs, havingConditions, organizationID, organizationType any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountFilterAddressCustomerIDs", reflect.TypeOf((*MockRepository)(nil).CountFilterAddressCustomerIDs), ctx, companyID, businessIDs, customerIDs, havingConditions, organizationID, organizationType)
}

// Create mocks base method.
func (m *MockRepository) Create(ctx context.Context, arg1 *address.Address) (*address.Address, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, arg1)
	ret0, _ := ret[0].(*address.Address)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockRepositoryMockRecorder) Create(ctx, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRepository)(nil).Create), ctx, arg1)
}

// Delete mocks base method.
func (m *MockRepository) Delete(ctx context.Context, id int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockRepositoryMockRecorder) Delete(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockRepository)(nil).Delete), ctx, id)
}

// FilterCustomerIDsByZip mocks base method.
func (m *MockRepository) FilterCustomerIDsByZip(ctx context.Context, companyID int64, businessIDs, customerIDs []int64, zipList []string, operator string, organizationID int64, organizationType customerpb.OrganizationRef_Type) ([]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FilterCustomerIDsByZip", ctx, companyID, businessIDs, customerIDs, zipList, operator, organizationID, organizationType)
	ret0, _ := ret[0].([]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FilterCustomerIDsByZip indicates an expected call of FilterCustomerIDsByZip.
func (mr *MockRepositoryMockRecorder) FilterCustomerIDsByZip(ctx, companyID, businessIDs, customerIDs, zipList, operator, organizationID, organizationType any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FilterCustomerIDsByZip", reflect.TypeOf((*MockRepository)(nil).FilterCustomerIDsByZip), ctx, companyID, businessIDs, customerIDs, zipList, operator, organizationID, organizationType)
}

// Get mocks base method.
func (m *MockRepository) Get(ctx context.Context, id int64) (*address.Address, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, id)
	ret0, _ := ret[0].(*address.Address)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockRepositoryMockRecorder) Get(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockRepository)(nil).Get), ctx, id)
}

// GetPrimaryAddress mocks base method.
func (m *MockRepository) GetPrimaryAddress(ctx context.Context, customerID int64) (*address.Address, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPrimaryAddress", ctx, customerID)
	ret0, _ := ret[0].(*address.Address)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPrimaryAddress indicates an expected call of GetPrimaryAddress.
func (mr *MockRepositoryMockRecorder) GetPrimaryAddress(ctx, customerID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPrimaryAddress", reflect.TypeOf((*MockRepository)(nil).GetPrimaryAddress), ctx, customerID)
}

// ListByCursor mocks base method.
func (m *MockRepository) ListByCursor(ctx context.Context, filter *address.ListFilter, pagination *address.Pagination, orderBy *address.OrderBy) (*address.CursorResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByCursor", ctx, filter, pagination, orderBy)
	ret0, _ := ret[0].(*address.CursorResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByCursor indicates an expected call of ListByCursor.
func (mr *MockRepositoryMockRecorder) ListByCursor(ctx, filter, pagination, orderBy any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByCursor", reflect.TypeOf((*MockRepository)(nil).ListByCursor), ctx, filter, pagination, orderBy)
}

// Save mocks base method.
func (m *MockRepository) Save(ctx context.Context, arg1 *address.Address) (*address.Address, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", ctx, arg1)
	ret0, _ := ret[0].(*address.Address)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Save indicates an expected call of Save.
func (mr *MockRepositoryMockRecorder) Save(ctx, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockRepository)(nil).Save), ctx, arg1)
}

// SearchAddressCustomerIDs mocks base method.
func (m *MockRepository) SearchAddressCustomerIDs(ctx context.Context, companyID int64, businessIDs []int64, keyword string, organizationID int64, organizationType customerpb.OrganizationRef_Type) ([]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchAddressCustomerIDs", ctx, companyID, businessIDs, keyword, organizationID, organizationType)
	ret0, _ := ret[0].([]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchAddressCustomerIDs indicates an expected call of SearchAddressCustomerIDs.
func (mr *MockRepositoryMockRecorder) SearchAddressCustomerIDs(ctx, companyID, businessIDs, keyword, organizationID, organizationType any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchAddressCustomerIDs", reflect.TypeOf((*MockRepository)(nil).SearchAddressCustomerIDs), ctx, companyID, businessIDs, keyword, organizationID, organizationType)
}

// Update mocks base method.
func (m *MockRepository) Update(ctx context.Context, arg1 *address.Address) (*address.Address, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, arg1)
	ret0, _ := ret[0].(*address.Address)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockRepositoryMockRecorder) Update(ctx, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockRepository)(nil).Update), ctx, arg1)
}

// WithTx mocks base method.
func (m *MockRepository) WithTx(tx *gorm.DB) address.Repository {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTx", tx)
	ret0, _ := ret[0].(address.Repository)
	return ret0
}

// WithTx indicates an expected call of WithTx.
func (mr *MockRepositoryMockRecorder) WithTx(tx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTx", reflect.TypeOf((*MockRepository)(nil).WithTx), tx)
}
