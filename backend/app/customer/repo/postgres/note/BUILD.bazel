load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "note",
    srcs = [
        "entity.go",
        "note.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/note",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres",
        "//backend/common/rpc/framework/log",
        "//backend/proto/customer/v2:customer",
        "@io_gorm_gorm//:gorm",
        "@io_gorm_gorm//clause",
    ],
)
