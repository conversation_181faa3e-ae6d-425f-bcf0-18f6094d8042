package membership

import (
	"context"

	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	membershipsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/membership/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ReadWriter interface {
	MergeSubscriptions(ctx context.Context, companyID int64,
		customerRel *businesscustomerpb.MergeRelationDef, petRels []*businesscustomerpb.MergeRelationDef) (bool, error)
}

type impl struct {
	subscriptionService membershipsvcpb.SubscriptionServiceClient
}

func New() ReadWriter {
	return &impl{
		subscriptionService: grpc.NewClient("moego-svc-membership",
			membershipsvcpb.NewSubscriptionServiceClient),
	}
}

func (i *impl) MergeSubscriptions(ctx context.Context, companyID int64,
	customerRel *businesscustomerpb.MergeRelationDef, petRels []*businesscustomerpb.MergeRelationDef) (bool, error) {
	resp, err := i.subscriptionService.MergeSubscriptions(ctx, &membershipsvcpb.MergeSubscriptionsRequest{
		CompanyId: companyID,
		MergeRelation: &businesscustomerpb.BusinessCustomerMergeRelationDef{
			CustomerMergeRelation: customerRel,
			PetMergeRelations:     petRels,
		},
	})
	if err != nil {
		log.InfoContextf(ctx, "MergeSubscriptions err:%v, companyID:%d, customerRel:%+v", err, companyID,
			customerRel)

		return false, err
	}

	return resp.GetSuccess(), nil
}
