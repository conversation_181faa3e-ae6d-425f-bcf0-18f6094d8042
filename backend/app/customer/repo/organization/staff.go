package organization

import (
	"context"

	"google.golang.org/protobuf/proto"

	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	organizationsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type Repository interface {
	GetStaffDetail(ctx context.Context, staffID, companyID, enterpriseID int64) (
		*organizationpb.StaffModel, error)
	ListStaffs(ctx context.Context, companyID int64) ([]*organizationpb.StaffModel, error)
	ListBusinesses(ctx context.Context, companyID int64) ([]*organizationpb.LocationModel, error)
}

type impl struct {
	staff    organizationsvcpb.StaffServiceClient
	business organizationsvcpb.BusinessServiceClient
}

func New() Repository {
	return &impl{
		staff:    grpc.NewClient("moego-svc-organization", organizationsvcpb.NewStaffServiceClient),
		business: grpc.NewClient("moego-svc-organization", organizationsvcpb.NewBusinessServiceClient),
	}
}

func (i *impl) GetStaffDetail(ctx context.Context, staffID, companyID, enterpriseID int64) (
	*organizationpb.StaffModel, error) {
	resp, err := i.staff.GetStaffDetail(ctx, &organizationsvcpb.GetStaffDetailRequest{
		Id:           staffID,
		CompanyId:    companyID,
		EnterpriseId: enterpriseID,
	})
	if err != nil {
		log.ErrorContextf(ctx, "GetStaffDetail err, staffID:%d, companyID:%d, enterpriseID:%d, err:%v",
			staffID, companyID, enterpriseID, err)

		return nil, err
	}

	return resp.GetStaff(), err
}

func (i *impl) ListStaffs(ctx context.Context, companyID int64) ([]*organizationpb.StaffModel, error) {
	resp, err := i.staff.QueryStaffByCompanyId(ctx, &organizationsvcpb.QueryStaffByCompanyIdRequest{
		CompanyId: companyID,
		Pagination: &utilsV2.PaginationRequest{
			PageSize: proto.Int32(1000),
			PageNum:  proto.Int32(1),
		},
	})
	if err != nil {
		log.ErrorContextf(ctx, "ListStaffs err, companyID:%+v, err:%v", companyID, err)

		return nil, err
	}

	return resp.GetStaffs(), nil
}

func (i *impl) ListBusinesses(ctx context.Context, companyID int64) ([]*organizationpb.LocationModel, error) {
	resp, err := i.business.ListLocations(ctx, &organizationsvcpb.ListLocationsRequest{
		Filter: &organizationsvcpb.ListLocationsRequest_Filter{
			CompanyIds: []int64{companyID},
		},
		Pagination: &utilsV2.PaginationRequest{
			PageSize: proto.Int32(1000),
			PageNum:  proto.Int32(1),
		},
	})
	if err != nil {
		log.ErrorContextf(ctx, "ListBusinesses err, companyID:%+v, err:%v", companyID, err)

		return nil, err
	}

	return resp.GetLocations(), nil
}
