// Code generated by MockGen. DO NOT EDIT.
// Source: ./organization/staff.go
//
// Generated by this command:
//
//	mockgen -source=./organization/staff.go -destination=./organization/mock/staff_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	gomock "go.uber.org/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
	isgomock struct{}
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// GetStaffDetail mocks base method.
func (m *MockRepository) GetStaffDetail(ctx context.Context, staffID, companyID, enterpriseID int64) (*organizationpb.StaffModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStaffDetail", ctx, staffID, companyID, enterpriseID)
	ret0, _ := ret[0].(*organizationpb.StaffModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStaffDetail indicates an expected call of GetStaffDetail.
func (mr *MockRepositoryMockRecorder) GetStaffDetail(ctx, staffID, companyID, enterpriseID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStaffDetail", reflect.TypeOf((*MockRepository)(nil).GetStaffDetail), ctx, staffID, companyID, enterpriseID)
}

// ListBusinesses mocks base method.
func (m *MockRepository) ListBusinesses(ctx context.Context, companyID int64) ([]*organizationpb.LocationModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListBusinesses", ctx, companyID)
	ret0, _ := ret[0].([]*organizationpb.LocationModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListBusinesses indicates an expected call of ListBusinesses.
func (mr *MockRepositoryMockRecorder) ListBusinesses(ctx, companyID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListBusinesses", reflect.TypeOf((*MockRepository)(nil).ListBusinesses), ctx, companyID)
}

// ListStaffs mocks base method.
func (m *MockRepository) ListStaffs(ctx context.Context, companyID int64) ([]*organizationpb.StaffModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListStaffs", ctx, companyID)
	ret0, _ := ret[0].([]*organizationpb.StaffModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListStaffs indicates an expected call of ListStaffs.
func (mr *MockRepositoryMockRecorder) ListStaffs(ctx, companyID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListStaffs", reflect.TypeOf((*MockRepository)(nil).ListStaffs), ctx, companyID)
}
