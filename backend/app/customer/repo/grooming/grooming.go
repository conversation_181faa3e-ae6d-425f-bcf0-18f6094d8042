package grooming

import (
	"context"

	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/http"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ReadWriter interface {
	MergeGroomingObConfigPackage(ctx context.Context, companyID int64,
		customerRel *businesscustomerpb.MergeRelationDef, petRels []*businesscustomerpb.MergeRelationDef) (bool, error)
}

type impl struct {
	cli http.Client
}

func New() ReadWriter {
	return &impl{
		cli: http.NewClientProxy("moego-service-grooming"),
	}
}

const (
	mergeGroomingObConfigPackagePath = "/service/grooming/merge/customer"
)

type MergeGroomingObConfigPackageReq struct {
	CompanyID         int64              `json:"companyId" binding:"required"`
	TargetCustomerID  int64              `json:"targetCustomerId" binding:"required"`
	SourceCustomerIDs []int64            `json:"sourceCustomerIds" binding:"required"`
	PetMergeRelations []PetMergeRelation `json:"petMergeRelations,omitempty"`
}

type PetMergeRelation struct {
	TargetPetID  int64   `json:"targetPetId,omitempty"`
	SourcePetIDs []int64 `json:"sourcePetIds,omitempty"`
}

func (i *impl) MergeGroomingObConfigPackage(ctx context.Context, companyID int64,
	customerRel *businesscustomerpb.MergeRelationDef, petRels []*businesscustomerpb.MergeRelationDef) (bool, error) {
	// build req
	petReqs := make([]PetMergeRelation, 0, len(petRels))
	for _, rel := range petRels {
		petReqs = append(petReqs, PetMergeRelation{
			TargetPetID:  rel.TargetId,
			SourcePetIDs: rel.SourceIds,
		})
	}
	req := &MergeGroomingObConfigPackageReq{
		CompanyID:         companyID,
		TargetCustomerID:  customerRel.TargetId,
		SourceCustomerIDs: customerRel.SourceIds,
	}
	if len(petReqs) > 0 {
		req.PetMergeRelations = petReqs
	}

	var body any
	if err := i.cli.Post(ctx, mergeGroomingObConfigPackagePath, petReqs, &body); err != nil {
		log.ErrorContextf(ctx, "MergeGroomingObConfigPackage Post err, err:%v", err)

		return false, err
	}

	return true, nil
}
