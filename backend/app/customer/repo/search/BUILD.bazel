load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "search",
    srcs = [
        "entity.go",
        "search.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/search",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/utils",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/framework/log",
        "//backend/proto/search/v1:search",
        "@org_golang_google_protobuf//types/known/structpb",
    ],
)
