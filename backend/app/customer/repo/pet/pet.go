package pet

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	petpb "github.com/MoeGolibrary/moego/backend/proto/pet/v1"
)

type ReadWriter interface {
	ListCustomerPet(ctx context.Context, customerID int64) ([]*petpb.Pet, error)
	BatchCreatePets(ctx context.Context, customerID int64, pets []*petpb.Pet) ([]*petpb.Pet, error)
	CreatePet(ctx context.Context, pet *petpb.Pet) (int64, error)
}

type impl struct {
	pet petpb.PetServiceClient
}

func New() ReadWriter {
	return &impl{
		pet: grpc.NewClient("moego-pet", petpb.NewPetServiceClient),
	}
}

func (i *impl) ListCustomerPet(ctx context.Context, customerID int64) ([]*petpb.Pet, error) {
	pets, err := i.pet.ListPet(ctx, &petpb.ListPetRequest{
		Parent:   customerID,
		PageSize: 100,
	})
	if err != nil {
		log.ErrorContextf(ctx, "ListCustomerPet err:%+v, customerID:%d", err, customerID)

		return nil, err
	}

	return pets.GetPets(), nil
}

func (i *impl) BatchCreatePets(ctx context.Context, customerID int64, pets []*petpb.Pet) ([]*petpb.Pet, error) {
	requests := make([]*petpb.CreatePetRequest, 0, len(pets))
	for _, pet := range pets {
		requests = append(requests, &petpb.CreatePetRequest{
			Pet: pet,
		})
	}
	resp, err := i.pet.BatchCreatePets(ctx, &petpb.BatchCreatePetsRequest{
		Requests: requests,
		Parent:   customerID,
	})
	if err != nil {
		log.ErrorContextf(ctx, "BatchCreatePets err:%+v, customerID:%d, pets:%+v",
			err, customerID, pets)

		return nil, err
	}

	return resp.GetPets(), nil
}

func (i *impl) CreatePet(ctx context.Context, pet *petpb.Pet) (int64, error) {
	resp, err := i.pet.CreatePet(ctx, &petpb.CreatePetRequest{Pet: pet})
	if err != nil {
		log.ErrorContextf(ctx, "CreatePet err:%+v, pet:%+v",
			err, pet)

		return 0, err
	}

	return resp.GetId(), nil
}
