load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mock",
    srcs = ["order_mock.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/repo/order/mock",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/business_customer/v1:business_customer",
        "@org_uber_go_mock//gomock",
    ],
)
