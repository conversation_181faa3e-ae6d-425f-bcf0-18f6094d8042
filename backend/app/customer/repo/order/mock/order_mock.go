// Code generated by MockGen. DO NOT EDIT.
// Source: ./order/order.go
//
// Generated by this command:
//
//	mockgen -source=./order/order.go -destination=./order/mock/order_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// MergeCustomerOrderData mocks base method.
func (m *MockReadWriter) MergeCustomerOrderData(ctx context.Context, companyID int64, customerRel *businesscustomerpb.MergeRelationDef, petRels []*businesscustomerpb.MergeRelationDef) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MergeCustomerOrderData", ctx, companyID, customerRel, petRels)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MergeCustomerOrderData indicates an expected call of MergeCustomerOrderData.
func (mr *MockReadWriterMockRecorder) MergeCustomerOrderData(ctx, companyID, customerRel, petRels any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MergeCustomerOrderData", reflect.TypeOf((*MockReadWriter)(nil).MergeCustomerOrderData), ctx, companyID, customerRel, petRels)
}
