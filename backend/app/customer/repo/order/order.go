package order

import (
	"context"

	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ReadWriter interface {
	MergeCustomerOrderData(ctx context.Context, companyID int64,
		customerRel *businesscustomerpb.MergeRelationDef, petRels []*businesscustomerpb.MergeRelationDef) (bool, error)
}

type impl struct {
	orderMergeService ordersvcpb.OrderMergeServiceClient
}

func New() ReadWriter {
	return &impl{
		orderMergeService: grpc.NewClient("moego-svc-order",
			ordersvcpb.NewOrderMergeServiceClient),
	}
}

func (i *impl) MergeCustomerOrderData(ctx context.Context, companyID int64,
	customerRel *businesscustomerpb.MergeRelationDef, petRels []*businesscustomerpb.MergeRelationDef) (bool, error) {
	resp, err := i.orderMergeService.MergeCustomerOrderData(ctx, &ordersvcpb.MergeCustomerOrderDataRequest{
		CompanyId: companyID,
		MergeRelation: &businesscustomerpb.BusinessCustomerMergeRelationDef{
			CustomerMergeRelation: customerRel,
			PetMergeRelations:     petRels,
		},
	})
	if err != nil {
		log.InfoContextf(ctx, "MergeCustomerOrderData err:%v, companyID:%d, customerRel:%+v", err, companyID,
			customerRel)

		return false, err
	}

	return resp.GetSuccess(), nil
}
