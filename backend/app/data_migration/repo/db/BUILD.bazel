load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "db",
    srcs = [
        "db.go",
        "id_mapping.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/data_migration/repo/db",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/data_migration/repo/db/entity",
        "//backend/common/rpc/database/gorm",
        "@io_gorm_gorm//:gorm",
        "@io_gorm_gorm//clause",
    ],
)
