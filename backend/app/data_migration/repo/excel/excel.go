package excel

import (
	"bytes"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	excelize "github.com/xuri/excelize/v2"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// Read is a generic function that parses an Excel file into a given struct pointer.
// The struct fields should be tagged with `sheet:"<sheet_name>"` to specify which sheet to parse.
// The target field must be a slice of structs, and each struct should have fields tagged with `xlsx:"<column_name>"`
func Read(content []byte, data any) error {
	ptrVal := reflect.ValueOf(data)
	if ptrVal.Kind() != reflect.Ptr {
		return fmt.Errorf("data must be a pointer to a struct")
	}

	structVal := ptrVal.Elem()
	if structVal.Kind() != reflect.Struct {
		return fmt.Errorf("data must be a pointer to a struct")
	}

	f, err := excelize.OpenReader(bytes.NewReader(content))
	if err != nil {
		return fmt.Errorf("failed to open excel file: %w", err)
	}
	defer f.Close()

	structType := structVal.Type()
	for i := 0; i < structVal.NumField(); i++ {
		fieldVal := structVal.Field(i)
		fieldType := structType.Field(i)

		if fieldVal.Kind() != reflect.Slice {
			continue
		}

		sheetName := fieldType.Tag.Get(SheetTag)
		if sheetName == "" {
			continue
		}

		rows, err := f.GetRows(sheetName)
		if err != nil {
			return fmt.Errorf("failed to get rows from sheet %s: %w", sheetName, err)
		}

		sliceVal, err := parseSheetRowsToSlice(rows, fieldVal.Type(), sheetName)
		if err != nil {
			return fmt.Errorf("failed to parse sheet %s: %w", sheetName, err)
		}

		if fieldVal.CanSet() {
			fieldVal.Set(sliceVal)
		}
	}

	return nil
}

func parseSheetRowsToSlice(rows [][]string, sliceType reflect.Type, sheetName string) (reflect.Value, error) {
	if len(rows) < 1 {
		return reflect.MakeSlice(sliceType, 0, 0), nil
	}

	header := rows[0]
	colMap := make(map[string]int)
	for i, colName := range header {
		colMap[colName] = i
	}

	elemType := sliceType.Elem()
	slice := reflect.MakeSlice(sliceType, 0, len(rows)-1)

	for rowIdx, row := range rows[1:] {
		elemPtr := reflect.New(elemType)
		elem := elemPtr.Elem()

		for i := 0; i < elem.NumField(); i++ {
			field := elemType.Field(i)
			value := elem.Field(i)
			colName := field.Tag.Get(ColumnTag)
			if colName == "" {
				continue
			}

			colIdx, ok := colMap[colName]
			if !ok || colIdx >= len(row) {
				continue
			}

			rawValue := row[colIdx]

			if err := setFieldValue(field, value, rawValue); err != nil {
				return reflect.Value{}, fmt.Errorf("error in sheet '%s' row %d, for field '%s' with value '%s': %w",
					sheetName, rowIdx+2, field.Name, rawValue, err)
			}
		}
		slice = reflect.Append(slice, elem)
	}

	return slice, nil
}

// parseTimeValue 封装 time.Time 的 RFC3339 解析
func parseTimeValue(raw string) (reflect.Value, error) {
	raw = strings.TrimSpace(raw)
	if raw == "" {
		return reflect.ValueOf(time.Time{}), nil
	}
	t, err := time.Parse(time.RFC3339, raw)
	if err != nil {
		return reflect.Value{}, fmt.Errorf("could not parse '%s' as time.Time (RFC3339): %w", raw, err)
	}

	return reflect.ValueOf(t), nil
}

func parseValue(raw string, targetType reflect.Type) (reflect.Value, error) {
	raw = strings.TrimSpace(raw)

	if raw == "" && targetType.Kind() != reflect.String {
		return reflect.Zero(targetType), nil
	}

	if targetType == reflect.TypeOf(time.Time{}) {
		return parseTimeValue(raw)
	}

	// protobuf 枚举本质是 int32，但有自己的类型名和包路径
	if targetType.Kind() == reflect.Int32 && targetType.PkgPath() != "" {
		// 尝试用 proto.Enum 解析
		val, err := EnumFromString(targetType, raw)
		if err == nil {
			return val, nil
		}
		// 如果不是合法枚举，继续往下走（或直接 return err）
		return reflect.Zero(targetType), err
	}

	switch targetType.Kind() {
	case reflect.String:
		return reflect.ValueOf(raw), nil
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		i, err := strconv.ParseInt(raw, 10, 64)
		if err != nil {
			return reflect.Value{}, fmt.Errorf("could not parse '%s' as int: %w", raw, err)
		}
		val := reflect.New(targetType).Elem()
		val.SetInt(i)

		return val, nil
	case reflect.Float32, reflect.Float64:
		f, err := strconv.ParseFloat(raw, 64)
		if err != nil {
			return reflect.Value{}, fmt.Errorf("could not parse '%s' as float: %w", raw, err)
		}
		val := reflect.New(targetType).Elem()
		val.SetFloat(f)

		return val, nil
	case reflect.Bool:
		valStr := strings.ToLower(raw)
		switch valStr {
		case "true", "1":
			return reflect.ValueOf(true), nil
		case "false", "0", "":
			return reflect.ValueOf(false), nil
		}

		return reflect.Value{}, fmt.Errorf("unsupported bool value: %s", raw)
	default:
		return reflect.Value{}, fmt.Errorf("unhandled type for parsing: %s", targetType.Kind())
	}
}

func setFieldValue(field reflect.StructField, value reflect.Value, raw string) error {
	if !value.CanSet() {
		return nil
	}

	// Handle slice with "split" tag
	if value.Kind() == reflect.Slice {
		separator := field.Tag.Get("split")
		if separator != "" {
			sliceElemType := value.Type().Elem()

			if strings.TrimSpace(raw) == "" {
				value.Set(reflect.MakeSlice(value.Type(), 0, 0))

				return nil
			}

			parts := strings.Split(raw, separator)
			newSlice := reflect.MakeSlice(value.Type(), 0, len(parts))

			for _, part := range parts {
				parsedPart, err := parseValue(part, sliceElemType)
				if err != nil {
					return fmt.Errorf("failed to parse slice element '%s': %w", part, err)
				}
				newSlice = reflect.Append(newSlice, parsedPart)
			}
			value.Set(newSlice)

			return nil
		}
	}

	// Handle single value
	parsedValue, err := parseValue(raw, value.Type())
	if err != nil {
		return err
	}

	if parsedValue.IsValid() {
		value.Set(parsedValue)
	}

	return nil
}

func EnumFromString(enumType reflect.Type, valueStr string) (reflect.Value, error) {
	// 创建枚举类型的零值指针
	enumPtr := reflect.New(enumType)
	enumIface := enumPtr.Interface()
	if enumVal, ok := enumIface.(protoreflect.Enum); ok {
		enumTypeDesc := enumVal.Descriptor()
		valueDesc := enumTypeDesc.Values().ByName(protoreflect.Name(strings.ToUpper(valueStr)))
		if valueDesc == nil {
			return reflect.Zero(enumType), fmt.Errorf("invalid enum value '%s'", valueStr)
		}
		v := int32(valueDesc.Number())

		return reflect.ValueOf(v).Convert(enumType), nil
	}

	return reflect.Zero(enumType), fmt.Errorf("type %s does not implement protoreflect.Enum", enumType.Name())
}
