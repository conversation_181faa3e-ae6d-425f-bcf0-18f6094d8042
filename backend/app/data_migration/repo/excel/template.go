package excel

import (
	"fmt"
	"reflect"

	excelize "github.com/xuri/excelize/v2"
)

// 生成 ReadMe sheet
func generateReadMeSheet(f *excelize.File, dataType reflect.Type) {
	readmeRows := [][]interface{}{{"field", "desc"}}
	for i := 0; i < dataType.NumField(); i++ {
		sheetField := dataType.Field(i)
		sheetName := sheetField.Tag.Get(SheetTag)
		if sheetName == "" {
			continue
		}
		elemType := sheetField.Type.Elem()
		if elemType.Kind() != reflect.Struct {
			continue
		}
		for j := 0; j < elemType.NumField(); j++ {
			field := elemType.Field(j)
			col := field.Tag.Get(ColumnTag)
			desc := field.Tag.Get(DescTag)
			if col != "" {
				row := []interface{}{fmt.Sprintf("%s.%s", sheetName, col), desc}
				readmeRows = append(readmeRows, row)
			}
		}
		readmeRows = append(readmeRows, []interface{}{"", ""})
	}
	_, _ = f.NewSheet("ReadMe")
	for i, row := range readmeRows {
		cell, _ := excelize.CoordinatesToCellName(1, i+1)
		_ = f.SetSheetRow("ReadMe", cell, &row)
	}
}

// 生成 model sheet
func generateModelSheets(f *excelize.File, dataType reflect.Type) error {
	for i := 0; i < dataType.NumField(); i++ {
		sheetField := dataType.Field(i)
		sheetName := sheetField.Tag.Get(SheetTag)
		if sheetName == "" {
			continue
		}
		if _, err := f.NewSheet(sheetName); err != nil {
			return err
		}
		elemType := sheetField.Type.Elem()
		if elemType.Kind() != reflect.Struct {
			continue
		}
		var headers []interface{}
		for j := 0; j < elemType.NumField(); j++ {
			headerField := elemType.Field(j)
			headerName := headerField.Tag.Get(ColumnTag)
			if headerName != "" {
				headers = append(headers, headerName)
			}
		}
		if err := f.SetSheetRow(sheetName, "A1", &headers); err != nil {
			return fmt.Errorf("failed to write header to sheet %s: %w", sheetName, err)
		}
		for j, header := range headers {
			colName, err := excelize.ColumnNumberToName(j + 1)
			if err != nil {
				return fmt.Errorf("failed to get column name for index %d: %w", j+1, err)
			}
			width := float64(len(fmt.Sprintf("%v", header)) + 2)
			if err := f.SetColWidth(sheetName, colName, colName, width); err != nil {
				return fmt.Errorf("failed to set column width for %s in sheet %s: %w", colName, sheetName, err)
			}
		}
	}

	return nil
}

func MakeTemplateFile(filePath string) error {
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()

	dataType := reflect.TypeOf(SheetData{})
	generateReadMeSheet(f, dataType)
	if err := generateModelSheets(f, dataType); err != nil {
		return err
	}
	if err := f.DeleteSheet("Sheet1"); err != nil {
		return fmt.Errorf("failed to delete default sheet: %w", err)
	}

	return f.SaveAs(filePath)
}
