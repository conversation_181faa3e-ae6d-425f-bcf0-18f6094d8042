package excel

import (
	"time"

	"google.golang.org/genproto/googleapis/type/calendarperiod"
	"google.golang.org/genproto/googleapis/type/dayofweek"

	membershippb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1"
)

const (
	SheetTag  = "sheet"
	ColumnTag = "column"
	DescTag   = "desc"
)

type SheetData struct {
	Memberships                      []Membership                `sheet:"Membership"`
	MembershipDiscountBenefits       []MembershipDiscountBenefit `sheet:"Membership.DiscountBenefit"`
	MembershipQuantityBenefits       []MembershipQuantityBenefit `sheet:"Membership.QuantityBenefit"`
	Subscriptions                    []Subscription              `sheet:"Subscription"`
	SubscriptionDiscountEntitlements []SubscriptionEntitlement   `sheet:"Subscription.Entitlement"`
}

//nolint:lll
type Membership struct {
	MID                     string                        `column:"mid"                        desc:"mapping id of membership"`
	Name                    string                        `column:"name"                       desc:"membership name"`
	Description             string                        `column:"description"                desc:"membership description"`
	Price                   string                        `column:"price"                      desc:"membership price, without thousands separator and unit (e.g. 10000.5)"`
	TaxName                 string                        `column:"Tax.name"                   desc:"tax name"`
	BillingCyclePeriodUnit  calendarperiod.CalendarPeriod `column:"billing cycle period unit"  desc:"billing cycle period unit, uppercase letters (e.g. MONTH / YEAR)"`
	BillingCyclePeriodValue int32                         `column:"billing cycle period value" desc:"billing cycle period value (e.g. 1)"`
	BillingCycleDayOfWeek   dayofweek.DayOfWeek           `column:"billing cycle day of week"  desc:"billing cycle day of week, uppercase letters (e.g. MONDAY / TUESDAY / ...)"`
}

//nolint:lll
type MembershipDiscountBenefit struct {
	MID           string                    `column:"mid"                    desc:"mapping id of discount benefit"`
	MembershipMID string                    `column:"Membership.mid"         desc:"mapping id of membership"`
	TargetType    membershippb.TargetType   `column:"target type"            desc:"target type (e.g. SERVICE, ADDON)"`
	TargetNames   []string                  `column:"target names" split:"|" desc:"target names, separated by |, (e.g. service1|service2)"`
	DiscountUnit  membershippb.DiscountUnit `column:"discount unit"          desc:"discount unit (e.g. percent)"`
	DiscountValue float64                   `column:"discount value"         desc:"discount value (e.g. 10.0)"`
}

//nolint:lll
type MembershipQuantityBenefit struct {
	MID                  string                        `column:"mid"                    desc:"mapping id of quantity benefit"`
	MembershipMID        string                        `column:"Membership.mid"         desc:"mapping id of membership"`
	TargetType           membershippb.TargetType       `column:"target type"            desc:"target type (e.g. SERVICE, ADDON)"`
	TargetName           string                        `column:"target name"            desc:"target name (service name or addon name)"`
	IsLimited            bool                          `column:"is limited"             desc:"is quantity limited"`
	LimitedValue         int64                         `column:"limited value"          desc:"limited value if is limited, otherwise 0"`
	PeriodType           membershippb.PeriodType       `column:"period type"            desc:"period type, FOLLOW_MEMBERSHIP or SPECIFIED"`
	SpecifiedPeriodUnit  calendarperiod.CalendarPeriod `column:"specified period unit"  desc:"specified period unit, uppercase letters (e.g. MONTH / YEAR), only need if period type is SPECIFIED."`
	SpecifiedPeriodValue int32                         `column:"specified period value" desc:"specified period value (e.g. 1)"`
}

//nolint:lll
type Subscription struct {
	MID           string    `column:"mid"            desc:"mapping id of subscription"`
	MembershipMID string    `column:"Membership.mid" desc:"related membership id"`
	CustomerMID   string    `column:"Customer.mid"   desc:"mapping id of customer, temporarily you can use customer id as mapping id"`
	BusinessID    int64     `column:"business id"    desc:"business id"`
	StartTime     time.Time `column:"start time"     desc:"subscription start time, RFC3339 format, e.g. 2025-07-03T15:04:05+08:00"`
	EndTime       time.Time `column:"end time"       desc:"subscription end time, RFC3339 format, e.g. 2025-07-03T15:04:05+08:00"`
}

//nolint:lll
type SubscriptionEntitlement struct {
	MID                string `column:"mid"                            desc:"mapping id of entitlement"`
	SubscriptionMID    string `column:"Subscription.mid"               desc:"mapping id of subscription"`
	QuantityBenefitMID string `column:"Membership.QuantityBenefit.mid" desc:"mapping id of quantity benefit"`
	Remaining          int64  `column:"remaining"                      desc:"remaining limited value if is limited, otherwise 0"`
	IsLimited          bool   `column:"is limited"                     desc:"is quantity limited, true or false"`
}
