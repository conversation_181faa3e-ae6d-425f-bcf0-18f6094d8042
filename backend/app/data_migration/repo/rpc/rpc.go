package rpc

import (
	"context"
	"sync"

	membershipsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/membership/v1"
	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	organizationsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/http"
)

type MoeGoRPCClient struct {
	MembershipDM membershipsvcpb.DataMigrationServiceClient
	Tax          organizationsvcpb.TaxRuleServiceClient
	Offering     offeringsvcpb.ServiceManagementServiceClient
	retail       http.Client
}

var (
	client = &MoeGoRPCClient{}
	once   sync.Once
)

func GetClient() *MoeGoRPCClient {
	once.Do(func() {
		client.MembershipDM = grpc.NewClient("moego-svc-membership", membershipsvcpb.NewDataMigrationServiceClient)
		client.Tax = grpc.NewClient("moego-svc-organization", organizationsvcpb.NewTaxRuleServiceClient)
		client.Offering = grpc.NewClient("moego-svc-offering", offeringsvcpb.NewServiceManagementServiceClient)
		client.retail = http.NewClientProxy("moego-server-retail")
	})

	return client
}

const (
	retailPathListProducts = "/service/retail/product/listProducts"
)

func (c *MoeGoRPCClient) ListProducts(ctx context.Context, companyID int64) ([]*Product, error) {
	req := &ListProductsRequest{
		CompanyID: companyID,
	}
	resp := &ListProductsResponse{}

	if err := c.retail.Post(ctx, retailPathListProducts, req, resp); err != nil {
		return nil, err
	}

	return resp.Products, nil
}
