package action

import (
	"fmt"
	"strconv"

	"google.golang.org/protobuf/types/known/timestamppb"

	membershippb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1"
	membershipsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/membership/v1"
	"github.com/MoeGolibrary/moego/backend/app/data_migration/repo/db/entity"
	"github.com/MoeGolibrary/moego/backend/app/data_migration/repo/excel"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type SubscriptionAction struct {
	ctx                *Context
	Subscriptions      []excel.Subscription
	MembershipMappings map[string]int64
	CustomerMappings   map[string]int64
}

func NewSubscriptionAction(ctx *Context, s []excel.Subscription) *SubscriptionAction {
	return &SubscriptionAction{
		ctx:                ctx,
		Subscriptions:      s,
		MembershipMappings: map[string]int64{},
		CustomerMappings:   map[string]int64{},
	}
}

func (a *SubscriptionAction) Prepare() error {
	if len(a.Subscriptions) == 0 {
		log.InfoContextf(a.ctx, "Subscription action: no data to prepare, skipping")

		return nil
	}
	// load membership mappings
	mappings, err := a.ctx.GetMappingRW().
		List(a.ctx,
			(&entity.IDMapping{}).WithCompanyID(a.ctx.CompanyID),
			(&entity.IDMapping{}).WithModel((&MembershipAction{}).Name()),
			(&entity.IDMapping{}).WithoutDeleted(),
		)
	if err != nil {
		return err
	}

	for _, m := range mappings {
		realID, err := strconv.ParseInt(m.RealID, 10, 64)
		if err != nil {
			return err
		}
		a.MembershipMappings[m.MID] = realID
	}

	// check membership exist
	for _, s := range a.Subscriptions {
		if _, ok := a.MembershipMappings[s.MembershipMID]; !ok {
			return fmt.Errorf("membership not found: %s", s.MembershipMID)
		}
	}

	// load customer mappings
	// TODO: 本期是直接用 real id 当作 mid，后面需要改造，因此这里直接把 mid 当作 real id
	for _, s := range a.Subscriptions {
		reelID, err := strconv.ParseInt(s.CustomerMID, 10, 64)
		if err != nil {
			return err
		}
		a.CustomerMappings[s.CustomerMID] = reelID
	}

	return nil
}

func (a *SubscriptionAction) Import() (map[string]string, error) {
	if len(a.Subscriptions) == 0 {
		log.InfoContextf(a.ctx, "Subscription action: no data to import, skipping")

		return nil, nil
	}
	models := make([]*membershippb.SubscriptionData, len(a.Subscriptions))
	for i, s := range a.Subscriptions {
		models[i] = &membershippb.SubscriptionData{
			Mid:          s.MID,
			CompanyId:    a.ctx.CompanyID,
			MembershipId: a.MembershipMappings[s.MembershipMID],
			CustomerId:   a.CustomerMappings[s.CustomerMID],
			BusinessId:   s.BusinessID,
			StartTime:    timestamppb.New(s.StartTime),
			EndTime:      timestamppb.New(s.EndTime),
		}
	}

	log.InfoContextf(a.ctx, "Subscription action: importing %d subscriptions", len(models))
	resp, err := a.ctx.GetRPC().MembershipDM.ImportSubscriptions(a.ctx, &membershipsvcpb.ImportSubscriptionsRequest{
		Data: models,
	})
	if err != nil {
		return nil, err
	}
	log.InfoContextf(a.ctx, "Subscription action: imported %d subscriptions successfully, %d failed",
		len(resp.GetImported()), len(resp.GetFailed()))

	mappings := map[string]string{}
	for _, m := range resp.GetImported() {
		mappings[m.Mid] = strconv.FormatInt(m.Id, 10)
	}

	for _, m := range resp.GetFailed() {
		log.ErrorContextf(a.ctx, "import subscription failed, mid: %s", m.Mid)
		// TODO: handle failure
	}

	return mappings, nil
}

func (a *SubscriptionAction) Name() string {
	return "Subscription"
}

func (a *SubscriptionAction) DependOn() []string {
	if len(a.Subscriptions) == 0 {
		return nil
	}

	return []string{"Membership"}
}
