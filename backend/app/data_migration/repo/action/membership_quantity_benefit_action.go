package action

import (
	"fmt"
	"strconv"

	membershippb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1"
	membershipsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/membership/v1"
	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	utilspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	"github.com/MoeGolibrary/moego/backend/app/data_migration/repo/db/entity"
	"github.com/MoeGolibrary/moego/backend/app/data_migration/repo/excel"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type MembershipQuantityBenefitAction struct {
	ctx                *Context
	QuantityBenefits   []excel.MembershipQuantityBenefit
	MembershipMappings map[string]int64
	ServiceMappings    map[string]int64
	ProductMappings    map[string]int64
}

func NewMembershipQuantityBenefitAction(ctx *Context,
	quantityBenefits []excel.MembershipQuantityBenefit) *MembershipQuantityBenefitAction {
	return &MembershipQuantityBenefitAction{
		ctx:                ctx,
		QuantityBenefits:   quantityBenefits,
		MembershipMappings: map[string]int64{},
		ServiceMappings:    map[string]int64{},
		ProductMappings:    map[string]int64{},
	}
}

func (a *MembershipQuantityBenefitAction) Prepare() error {
	if len(a.QuantityBenefits) == 0 {
		log.InfoContextf(a.ctx, "MembershipQuantityBenefit action: no data to prepare, skipping")

		return nil
	}
	// load membership mappings
	mappings, err := a.ctx.GetMappingRW().
		List(a.ctx,
			(&entity.IDMapping{}).WithCompanyID(a.ctx.CompanyID),
			(&entity.IDMapping{}).WithModel((&MembershipAction{}).Name()),
			(&entity.IDMapping{}).WithoutDeleted(),
		)
	if err != nil {
		return err
	}

	for _, m := range mappings {
		realID, err := strconv.ParseInt(m.RealID, 10, 64)
		if err != nil {
			return err
		}
		a.MembershipMappings[m.MID] = realID
	}

	// check membership exist
	for _, db := range a.QuantityBenefits {
		if _, ok := a.MembershipMappings[db.MembershipMID]; !ok {
			return fmt.Errorf("membership not found: %s", db.MembershipMID)
		}
	}

	// load service
	resp, err := a.ctx.GetRPC().Offering.GetServiceList(
		a.ctx,
		&offeringsvcpb.GetServiceListRequest{
			TokenCompanyId: a.ctx.CompanyID,
		},
	)
	if err != nil {
		return err
	}

	// build service mappings
	for _, category := range resp.GetCategoryList() {
		for _, service := range category.GetServices() {
			a.ServiceMappings[service.Name] = service.ServiceId
		}
	}

	// check service exist
	for _, db := range a.QuantityBenefits {
		if db.TargetType != membershippb.TargetType_SERVICE && db.TargetType != membershippb.TargetType_ADDON {
			continue
		}
		if _, ok := a.ServiceMappings[db.TargetName]; !ok {
			return fmt.Errorf("service not found: %s", db.TargetName)
		}
	}

	// load product
	products, err := a.ctx.GetRPC().ListProducts(a.ctx, a.ctx.CompanyID)
	if err != nil {
		return err
	}

	// build product mappings
	for _, product := range products {
		a.ProductMappings[product.Name] = product.ID
	}

	// check product exist
	for _, db := range a.QuantityBenefits {
		if db.TargetType != membershippb.TargetType_PRODUCT {
			continue
		}
		if _, ok := a.ProductMappings[db.TargetName]; !ok {
			return fmt.Errorf("product not found: %s", db.TargetName)
		}
	}

	return nil
}

func (a *MembershipQuantityBenefitAction) Import() (map[string]string, error) {
	if len(a.QuantityBenefits) == 0 {
		log.InfoContextf(a.ctx, "MembershipQuantityBenefit action: no data to import, skipping")

		return nil, nil
	}
	models := make([]*membershippb.QuantityBenefitData, len(a.QuantityBenefits))
	for i, qb := range a.QuantityBenefits {

		// parse target type and id
		var targetID int64
		switch qb.TargetType {
		case membershippb.TargetType_SERVICE, membershippb.TargetType_ADDON:
			targetID = a.ServiceMappings[qb.TargetName]
		case membershippb.TargetType_PRODUCT:
			targetID = a.ProductMappings[qb.TargetName]
		default:
			return nil, fmt.Errorf("do not support target type: %s", qb.TargetType)
		}

		var period *utilspb.TimePeriod
		if qb.PeriodType == membershippb.PeriodType_SPECIFIED {
			period = &utilspb.TimePeriod{
				Period: qb.SpecifiedPeriodUnit,
				Value:  qb.SpecifiedPeriodValue,
			}
		}
		models[i] = &membershippb.QuantityBenefitData{
			Mid:             qb.MID,
			MembershipId:    a.MembershipMappings[qb.MembershipMID],
			TargetType:      qb.TargetType,
			TargetId:        targetID,
			IsLimited:       qb.IsLimited,
			LimitedValue:    qb.LimitedValue,
			PeriodType:      qb.PeriodType,
			SpecifiedPeriod: period,
		}
	}
	log.InfoContextf(a.ctx, "MembershipQuantityBenefit action: importing %d quantity benefits", len(models))
	resp, err := a.ctx.GetRPC().MembershipDM.ImportQuantityBenefits(a.ctx,
		&membershipsvcpb.ImportQuantityBenefitsRequest{
			Data: models,
		})
	if err != nil {
		return nil, err
	}
	log.InfoContextf(a.ctx,
		"MembershipQuantityBenefit action: imported %d quantity benefits successfully, %d failed",
		len(resp.GetImported()), len(resp.GetFailed()))

	mappings := map[string]string{}
	for _, m := range resp.GetImported() {
		mappings[m.Mid] = strconv.FormatInt(m.Id, 10)
	}
	for _, m := range resp.GetFailed() {
		log.ErrorContextf(a.ctx, "import membership quantity benefit failed, mid: %s", m.Mid)
		// TODO: handle failure
	}

	return mappings, nil
}

func (a *MembershipQuantityBenefitAction) Name() string {
	return "MembershipQuantityBenefit"
}

func (a *MembershipQuantityBenefitAction) DependOn() []string {
	if len(a.QuantityBenefits) == 0 {
		return nil
	}

	return []string{"Membership"}
}
