package action

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/data_migration/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/data_migration/repo/db/entity"
	"github.com/MoeGolibrary/moego/backend/app/data_migration/repo/rpc"
)

type Context struct {
	context.Context
	CompanyID int64
}

func (c *Context) GetRPC() *rpc.MoeGoRPCClient {
	return rpc.GetClient()
}

func (c *Context) GetMappingRW() db.IDMappingReadWriter {
	return db.NewIDMappingReadWriter()
}

func (c *Context) SaveMapping(model string, mappings map[string]string) error {
	if len(mappings) == 0 {
		return nil
	}
	idMappings := make([]*entity.IDMapping, 0, len(mappings))
	for mid, id := range mappings {
		idMappings = append(idMappings, &entity.IDMapping{
			CompanyID: c.CompanyID,
			MID:       mid,
			Model:     model,
			RealID:    id,
		})
	}

	return c.GetMappingRW().BatchCreate(c.Context, idMappings)
}
