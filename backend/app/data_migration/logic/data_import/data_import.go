package dataimport

import (
	"context"
	"fmt"

	"github.com/MoeGolibrary/moego/backend/app/data_migration/repo/action"
	excelrepo "github.com/MoeGolibrary/moego/backend/app/data_migration/repo/excel"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type Logic struct{}

func NewLogic() *Logic {
	return &Logic{}
}

// ParseAndRunExcelImport 解析 excel 文件，构建 action DAG 并依次执行
func (l *Logic) ParseAndRunExcelImport(ctx context.Context, companyID int64, content []byte) error {
	var sheetData excelrepo.SheetData
	if err := excelrepo.Read(content, &sheetData); err != nil {
		return fmt.Errorf("failed to parse excel: %w", err)
	}

	actCtx := &action.Context{
		Context:   ctx,
		CompanyID: companyID,
	}

	// 构造 action
	actions, err := l.BuildActions(actCtx, &sheetData)
	if err != nil {
		return err
	}

	for _, act := range actions {
		log.InfoContextf(ctx, "Start to execute action: %s", act.Name())

		if err := act.Prepare(); err != nil {
			log.ErrorContextf(ctx, "failed to preare %s: %v", act.Name(), err)

			return err
		}
		mappings, err := act.Import()
		if err != nil {
			log.ErrorContextf(ctx, "failed to import %s: %v", act.Name(), err)

			return err
		}
		if err := actCtx.SaveMapping(act.Name(), mappings); err != nil {
			log.ErrorContextf(ctx, "failed to save mapping of %s: %v", act.Name(), err)

			return err
		}
	}

	return nil
}

func (l *Logic) BuildActions(ctx *action.Context, data *excelrepo.SheetData) ([]action.Action, error) {
	// 构造 action
	membershipAction := action.NewMembershipAction(ctx, data.Memberships)
	membershipDiscountBenefitAction := action.NewMembershipDiscountBenefitAction(ctx, data.MembershipDiscountBenefits)
	membershipQuantityBenefitAction := action.NewMembershipQuantityBenefitAction(ctx, data.MembershipQuantityBenefits)
	subscriptionAction := action.NewSubscriptionAction(ctx, data.Subscriptions)
	subscriptionEntitlementAction := action.NewSubscriptionEntitlementAction(ctx, data.SubscriptionDiscountEntitlements)

	actions := []action.Action{
		membershipAction,
		membershipDiscountBenefitAction,
		membershipQuantityBenefitAction,
		subscriptionAction,
		subscriptionEntitlementAction,
	}

	// 处理顺序
	sorted, err := action.SortActions(actions)
	if err != nil {
		return nil, fmt.Errorf("action dependency analysis failed: %w", err)
	}

	return sorted, nil
}
