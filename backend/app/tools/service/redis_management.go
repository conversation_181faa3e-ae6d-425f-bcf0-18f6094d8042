package service

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/moego/backend/app/tools/repo/redismng"
	toolspb "github.com/MoeGolibrary/moego/backend/proto/tools/v1"
)

type RedisManagementService struct {
	toolspb.UnimplementedRedisManagementServiceServer
	repo redismng.RedisManager
}

// NewRedisManagementService creates a new RedisManagementService.
func NewRedisManagementService() *RedisManagementService {
	repo := redismng.NewRedisManager()

	return &RedisManagementService{
		repo: repo,
	}
}

// LoginRedis validates Redis credentials
func (s *RedisManagementService) LoginRedis(
	ctx context.Context,
	req *toolspb.LoginRedisRequest,
) (*toolspb.LoginRedisResponse, error) {
	// Validate required fields
	if req.Username == "" {
		return nil, status.Error(codes.InvalidArgument, "username is required")
	}

	if req.Password == "" {
		return nil, status.Error(codes.InvalidArgument, "password is required")
	}

	if req.Env == "" {
		return nil, status.Error(codes.InvalidArgument, "env is required")
	}

	// Check Redis login credentials
	success, err := s.repo.CheckRedisLogin(ctx, req.Username, req.Password, req.Env)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to check Redis login: %v", err)
	}

	return &toolspb.LoginRedisResponse{
		Success: success,
	}, nil
}
