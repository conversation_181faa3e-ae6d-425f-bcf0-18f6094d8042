load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "redismng",
    srcs = ["redis_management.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/tools/repo/redismng",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/tools/repo/redismng/entity",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/framework/log",
        "@io_gorm_gorm//:gorm",
    ],
)
