package datadog

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"github.com/MoeGolibrary/moego/backend/app/tools/configinit"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

// OpenAIClient represents a client for interacting with OpenAI API
type OpenAIClient struct {
	apiKey     string
	baseURL    string
	httpClient *http.Client
	prompt     string
}

// OpenAIRequest represents the request structure for OpenAI API
type OpenAIRequest struct {
	Model    string    `json:"model"`
	Messages []Message `json:"messages"`
	Stream   bool      `json:"stream"`
}

// Message represents a single message in the conversation
type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// OpenAIResponse represents the response structure from OpenAI API
type OpenAIResponse struct {
	ID      string   `json:"id"`
	Object  string   `json:"object"`
	Created int64    `json:"created"`
	Model   string   `json:"model"`
	Choices []Choice `json:"choices"`
}

// Choice represents a single choice in the response
type Choice struct {
	Index        int     `json:"index"`
	Message      Message `json:"message"`
	FinishReason string  `json:"finish_reason"`
}

// NewAuditClient creates a new OpenAI client
func NewAuditClient(config *configinit.MessageConfig) *OpenAIClient {
	return &OpenAIClient{
		apiKey:     config.MessageAlert.OpenAIToken,
		baseURL:    "https://llmproxy-dev.devops.moego.pet/v1",
		httpClient: &http.Client{},
		prompt:     config.MessageAlert.AuditPrompt,
	}
}

// AuditLogs sends log data to OpenAI for analysis and returns the last message from the response
func (client *OpenAIClient) AuditLogs(ctx context.Context, logs []map[string]interface{}) (string, error) {
	// Convert logs to JSON string for content
	logsText := ""
	for _, logEntry := range logs {
		messageBody := fmt.Sprintf("%v", logEntry["messageBody"])
		// Escape newlines in messageBody to prevent UI line breaks
		escapedMessageBody := strings.ReplaceAll(messageBody, "\n", "\t")
		logsText += fmt.Sprintf("moe_account_id:%v, messageBody:%v\n",
			logEntry["moe_account_id"], escapedMessageBody)
	}

	// Create the request message
	messageContent := fmt.Sprintf(client.prompt, string(logsText))
	messages := []Message{
		{
			Role:    "user",
			Content: messageContent,
		},
	}
	log.InfoContextf(ctx, "AuditLogs messageContent: %s", messageContent)

	// Create the OpenAI request
	request := OpenAIRequest{
		Model:    "gpt-5",
		Messages: messages,
		Stream:   false,
	}

	// Marshal the request
	requestBody, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	url := fmt.Sprintf("%s/chat/completions", client.baseURL)
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Authorization", "Bearer "+client.apiKey)
	req.Header.Set("Content-Type", "application/json")

	// Send the request
	resp, err := client.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// Check status code
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("OpenAI API request failed with status code: %d", resp.StatusCode)
	}

	// Parse the response
	var response OpenAIResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return "", fmt.Errorf("failed to decode response: %w", err)
	}

	// Extract the last message from the response
	if len(response.Choices) > 0 {
		respContent := response.Choices[0].Message.Content
		log.InfoContextf(ctx, "AuditLogs Content: %s", respContent)

		return respContent, nil
	}

	return "", fmt.Errorf("no choices returned from OpenAI API")
}
