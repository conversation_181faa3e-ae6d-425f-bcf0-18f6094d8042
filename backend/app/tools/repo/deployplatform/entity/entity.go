package entity

import (
	"database/sql"
	"time"

	"gorm.io/datatypes"
)

// DeployTask represents a deployment task.
type DeployTask struct {
	// ID is the unique identifier for the deployment task
	ID int64 `gorm:"primary_key"`

	// Name is the unique name for the deployment task
	Name string `gorm:"type:varchar(255);not null;uniqueIndex:name"`

	// Title is the title of the deployment task
	Title string `gorm:"type:varchar(255);not null"`

	// Description is the description of the deployment task
	Description string `gorm:"type:varchar(5000);not null"`

	// <PERSON><PERSON><PERSON> is the user who created the deployment task
	CreatedBy string `gorm:"type:varchar(255);not null"`

	// CreatedAt is the timestamp when the deployment task was created
	CreatedAt time.Time `gorm:"default:CURRENT_TIMESTAMP;index:idx_deploytask_update_at_ended_at"`

	// UpdatedAt is the timestamp when the deployment task was last updated
	UpdatedAt time.Time `gorm:"default:CURRENT_TIMESTAMP;index:idx_deploytask_update_at_ended_at"`

	// EndedAt is the timestamp when the deployment task ended (nullable)
	EndedAt sql.NullTime `gorm:"default:NULL;index"`

	// State is the current state of the deployment task
	State string `gorm:"type:varchar(50);not null"`

	// CurrentPhase is the current phase of the deployment task
	CurrentPhase string `gorm:"type:varchar(50)"`

	// Parameters are the parameters for the deployment task
	Parameters JSONB `gorm:"type:jsonb"`
}

func (t *DeployTask) TableName() string {
	return "moego_tools.public.deploy_task"
}

// IsEnd
func (t *DeployTask) IsEnd() bool {
	return t.EndedAt.Valid
}

// DeployPhase represents a phase within a deployment task.
type DeployPhase struct {
	// ID is the unique identifier for the deployment phase
	ID int64 `gorm:"primary_key"`

	// DeployTaskID is the ID of the associated deployment task
	DeployTaskID int64 `gorm:"not null"`

	// Name is the name of the deployment phase
	Name string `gorm:"type:varchar(255);not null"`

	// Type is the type of the deployment phase, CI/Canary
	Type string `gorm:"type:varchar(255);not null"`

	// State is the current state of the deployment phase
	State string `gorm:"type:varchar(255);not null"`

	// Parameters are the parameters for the deployment task
	Parameters JSONB `gorm:"type:jsonb"`

	// StartedAt is the timestamp when the phase started
	StartedAt time.Time `gorm:"default:CURRENT_TIMESTAMP"`

	// EndedAt is the timestamp when the phase ended (nullable)
	EndedAt sql.NullTime `gorm:"default:NULL"`
}

func (p *DeployPhase) TableName() string {
	return "moego_tools.public.deploy_phase"
}

// IsEnd is a method to check if the deployment phase has ended.
func (p *DeployPhase) IsEnd() bool {
	return p.EndedAt.Valid
}

// DeployLog represents a log entry for a deployment task.
type DeployLog struct {
	// ID is the unique identifier for the log entry
	ID int64 `gorm:"primary_key"`

	// DeployTaskID is the ID of the associated deployment task
	DeployTaskID int64 `gorm:"not null"`

	// LogTime is the timestamp of the log entry
	LogTime time.Time `gorm:"default:CURRENT_TIMESTAMP"`

	// Type is the type of the log entry
	Type string `gorm:"type:varchar(50);not null"`

	// State is the state of the deployment task at the time of the log entry
	State string `gorm:"type:varchar(50);not null"`

	// Phase is the phase of the deployment task at the time of the log entry
	Phase string `gorm:"type:varchar(50)"`

	// Message is the message associated with the log entry
	Message JSONB `gorm:"type:jsonb"`
}

func (DeployLog) TableName() string {
	return "moego_tools.public.deploy_log"
}

type JSONB = datatypes.JSON
