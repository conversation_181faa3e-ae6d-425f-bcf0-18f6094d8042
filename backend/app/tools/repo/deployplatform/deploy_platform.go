//go:generate mockery --name Deployer --output ./mocks --outpkg mocks --filename mock_Deployer.go

package deployplatform

import (
	"context"
	"errors"
	"sync"
	"time"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform/entity"
	igorm "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type Deployer interface {
	GetPhasesAndLogs(ctx context.Context, task *entity.DeployTask) ([]*entity.DeployPhase, []*entity.DeployLog, error)
	GetDeployPhase(ctx context.Context, taskID int64, phaseType string) (*entity.DeployPhase, error)
	GetDeployTaskByID(ctx context.Context, taskID int64) (*entity.DeployTask, error)
	GetDeployTasksNotEnded(ctx context.Context, after time.Time) ([]*entity.DeployTask, error)
	GetDeployTaskByName(ctx context.Context, name string) (*entity.DeployTask, error)
	ListDeployTasks(ctx context.Context, pageSize, pageNum int) ([]*entity.DeployTask, int64, int64, error)
	UpdatePhaseAndTaskState(ctx context.Context, phase *entity.DeployPhase, state string, message string) error
	UpdatePhase(ctx context.Context, phase *entity.DeployPhase, message string) error
	CreatePhaseAndUpdateTask(ctx context.Context, task *entity.DeployTask,
		phaseType, phaseName, phaseState, message string) error
	GetOrCreateDeployTask(
		ctx context.Context, name, title, description, createdBy, phaseType, phaseName, message string,
	) (*entity.DeployTask, error)
	UpdateTask(ctx context.Context, task *entity.DeployTask, message string) error
	EndDeployTask(ctx context.Context, taskID int64, message string) error
	EndDeployPhase(ctx context.Context, phase *entity.DeployPhase, message string) error
	DeleteDeployTask(ctx context.Context, taskID int64) error
	CreateDeployLog(ctx context.Context, taskID int64, logType, state, phaseType string, message string) error
}

type impl struct {
	db *gorm.DB
}

var (
	once             sync.Once
	instanceDeployer Deployer
)

func NewDeployer() Deployer {
	once.Do(func() {
		db, err := igorm.NewClientProxy("postgres")
		if err != nil {
			panic(err)
		}
		instanceDeployer = &impl{
			db: db,
		}
	})

	return instanceDeployer
}

const (
	Init                = "Init"
	Running             = "Running"
	Waiting             = "Waiting"
	End                 = "End"
	Failed              = "Failed"
	Rollback            = "Rollback"
	Skipped             = "Skipped"
	Cancelled           = "Cancelled"
	Succeeded           = "Succeeded"
	PhaseCI             = "CI"
	PhaseCanary         = "Canary"
	PhaseFullDeployment = "FullDeployment"
	TaskBatch           = 50
	EmptyMessage        = "{}"
)

var ErrTaskHasAlreadyEnded = errors.New("task has already ended")

func (i *impl) GetPhasesAndLogs(ctx context.Context, task *entity.DeployTask) (
	phases []*entity.DeployPhase, logs []*entity.DeployLog, err error) {
	db := i.db.WithContext(ctx)

	if err = db.Where("deploy_task_id = ?", task.ID).
		Order("id asc").
		Find(&phases).Error; err != nil {
		return
	}

	if err = db.Where("deploy_task_id = ?", task.ID).
		Order("id asc").
		Find(&logs).Error; err != nil {
		return
	}

	return
}

func (i *impl) GetDeployPhase(ctx context.Context, taskID int64, phaseType string) (
	phase *entity.DeployPhase, err error) {
	db := i.db.WithContext(ctx)

	phase = &entity.DeployPhase{}
	err = db.Where("deploy_task_id = ? AND type = ?", taskID, phaseType).First(phase).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Errorf("failed to get DeployPhase: %v", err)
	}

	return
}

func (i *impl) GetDeployTaskByID(ctx context.Context, taskID int64) (
	task *entity.DeployTask, err error) {
	db := i.db.WithContext(ctx)

	task = &entity.DeployTask{}
	err = db.Where("id = ?", taskID).First(task).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Errorf("failed to get DeployTask: %v", err)
	}

	return
}

func (i *impl) GetDeployTasksNotEnded(ctx context.Context, after time.Time) (
	tasks []*entity.DeployTask, err error) {
	db := i.db.WithContext(ctx)

	err = db.Where("ended_at IS NULL AND updated_at > ?", after).
		Order("updated_at asc").Limit(TaskBatch).Find(&tasks).Error
	if err != nil {
		log.Errorf("failed to get DeployTasksNotEnded: %v", err)
	}

	return
}

func (i *impl) GetDeployTaskByName(ctx context.Context, name string) (
	task *entity.DeployTask, err error) {
	db := i.db.WithContext(ctx)

	task = &entity.DeployTask{}
	err = db.Where("name = ?", name).First(task).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Errorf("failed to get DeployTaskByName: %v", err)
	}

	return
}

func (i *impl) ListDeployTasks(ctx context.Context, pageSize, pageNum int) (
	tasks []*entity.DeployTask, total, totalPage int64, err error) {
	db := i.db.WithContext(ctx)

	if err = db.Model(&entity.DeployTask{}).Count(&total).Error; err != nil {
		log.Errorf("failed to count DeployTasks: %v", err)

		return
	}

	totalPage = total / int64(pageSize)
	if total%int64(pageSize) != 0 {
		totalPage++
	}

	if err = db.Limit(pageSize).Offset(pageSize * (pageNum - 1)).
		Order("created_at desc").Find(&tasks).Error; err != nil {
		log.Errorf("failed to list DeployTasks: %v", err)

		return
	}

	return
}

// ListNotEndDeployPhases list deploy phases
func (i *impl) ListNotEndDeployPhases(
	ctx context.Context, phaseType, phaseName string,
) (
	phases []*entity.DeployPhase, err error,
) {
	db := i.db.WithContext(ctx)

	if phaseType != "" {
		db = db.Where("type = ?", phaseType)
	}

	if phaseName != "" {
		db = db.Where("name = ?", phaseName)
	}

	err = db.Where("ended_at IS NULL").Find(&phases).Error
	if err != nil {
		log.Errorf("failed to list DeployPhases: %v", err)

		return
	}

	return
}

func (i *impl) UpdatePhaseAndTaskState(
	ctx context.Context, phase *entity.DeployPhase, state, message string,
) error {
	return i.transaction(ctx, func(tx *gorm.DB) error {
		// 更新 DeployPhase 的状态
		if err := tx.Model(&entity.DeployPhase{}).
			Where("id = ?", phase.ID).
			Update("state", state).Error; err != nil {
			return err
		}

		// 更新 DeployTask 的状态和当前阶段
		if err := tx.Model(&entity.DeployTask{}).
			Where("id = ?", phase.DeployTaskID).
			Updates(map[string]interface{}{
				"state":         state,
				"current_phase": phase.Type,
				"updated_at":    time.Now(),
			}).Error; err != nil {
			return err
		}

		// 创建 DeployLog
		go i.createDeployLogTx(ctx, tx, phase.DeployTaskID, "UpdatePhase", state, phase.Type, message)

		return nil
	})
}

func (i *impl) UpdatePhase(ctx context.Context, phase *entity.DeployPhase, message string) error {
	return i.transaction(ctx, func(tx *gorm.DB) error {
		if err := tx.Model(phase).
			Where("id = ?", phase.ID).
			Updates(map[string]interface{}{
				"state":      phase.State,
				"parameters": phase.Parameters,
			}).Error; err != nil {
			return err
		}

		// Create a DeployLog
		go i.createDeployLogTx(ctx, tx, phase.DeployTaskID, "UpdatePhase", phase.State, phase.Type, message)

		return nil
	})
}

func (i *impl) CreatePhaseAndUpdateTask(
	ctx context.Context, task *entity.DeployTask, phaseType, phaseName, phaseState, message string,
) error {
	return i.transaction(ctx, func(tx *gorm.DB) error {
		// 合并更新操作
		if err := tx.Model(task).
			Where("id = ?", task.ID).
			Updates(map[string]interface{}{
				"state":         phaseState,
				"current_phase": phaseType,
				"updated_at":    time.Now(),
			}).Error; err != nil {
			return err
		}

		if err := i.createPhaseWithTx(ctx, tx, task, phaseType, phaseName, phaseState, message); err != nil {
			return err
		}

		return nil
	})
}

func (i *impl) GetOrCreateDeployTask(
	ctx context.Context, name, title, description, createdBy, phaseType, phaseName, message string,
) (task *entity.DeployTask, err error) {

	// Start a transaction
	tx := i.db.WithContext(ctx).Begin()
	defer func() {
		if err != nil {
			log.Errorf("failed to create DeployPhase: %v", err)
			tx.Rollback()
		}
	}()
	// Check if the project exists
	task = &entity.DeployTask{}
	if err = tx.Where("name = ?", name).First(task).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 记录未找到，继续执行
			log.Infof("Record not found, continue execution")
		} else {
			log.Errorf("failed to check if DeployTask exists: %v", err)
			// 其他错误，返回
			return
		}
	}

	if task.ID != 0 {
		// 如果任务已经存在
		log.Debugf("DeployTask already exists, task: %+v", task)

		return
	}

	// Create a DeployTask
	task = &entity.DeployTask{
		Name:         name,
		Title:        title,
		Description:  description,
		CreatedBy:    createdBy,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
		State:        Init,
		CurrentPhase: phaseType,
		Parameters:   entity.JSONB(`{}`),
	}
	create := tx.Create(task)
	if err = create.Error; err != nil {
		log.Errorf("failed to create DeployTask: %v", err)

		return
	}

	// Create a DeployPhase
	err = i.createPhaseWithTx(ctx, tx, task, phaseType, phaseName, Init, EmptyMessage)
	if err != nil {
		return
	}

	// Create a DeployLog
	go i.createDeployLogTx(ctx, i.db, task.ID, "InitPhase", Init, phaseType, message)

	// Commit the transaction
	if err = tx.Commit().Error; err != nil {
		log.Errorf("failed to commit transaction: %v", err)
	}

	return
}

// UpdateTask 更新任务
func (i *impl) UpdateTask(ctx context.Context, task *entity.DeployTask, message string) error {
	return i.transaction(ctx, func(tx *gorm.DB) error {
		if err := tx.Model(task).
			Where("id = ?", task.ID).
			Updates(map[string]interface{}{
				"state":      task.State,
				"parameters": task.Parameters,
				"updated_at": time.Now(),
			}).Error; err != nil {
			return err
		}
		go i.createDeployLogTx(ctx, tx, task.ID, "UpdateTask",
			task.State, task.CurrentPhase, message)

		return nil
	})
}

func (i *impl) EndDeployTask(ctx context.Context, taskID int64, message string) error {
	return i.transaction(ctx, func(tx *gorm.DB) error {
		task, err := i.GetDeployTaskByID(ctx, taskID)
		if err != nil {
			log.Errorf("failed to get DeployTask: %v", err)

			return err
		}

		if task.IsEnd() {
			log.Errorf("task has already ended: %v", ErrTaskHasAlreadyEnded)

			return ErrTaskHasAlreadyEnded
		}

		if err := tx.Model(&entity.DeployTask{}).Where("id = ?", taskID).
			Update("ended_at", time.Now()).Error; err != nil {
			log.Errorf("failed to update DeployTask: %v", err)

			return err
		}

		if err := tx.Model(&entity.DeployPhase{}).
			Where("deploy_task_id = ? AND type = ?", taskID, task.CurrentPhase).
			Update("ended_at", time.Now()).Error; err != nil {
			log.Errorf("failed to update DeployPhase: %v", err)

			return err
		}

		// Create a DeployLog
		go i.createDeployLogTx(ctx, tx, task.ID, "EndTask", End, task.CurrentPhase, message)

		return nil
	})
}

func (i *impl) EndDeployPhase(ctx context.Context, phase *entity.DeployPhase, message string) error {
	return i.transaction(ctx, func(tx *gorm.DB) error {
		if err := tx.Model(&entity.DeployPhase{}).
			Where("id = ?", phase.ID).Update("ended_at", time.Now()).Error; err != nil {
			log.Errorf("failed to update DeployPhase: %v", err)

			return err
		}

		// Create a DeployLog
		go i.createDeployLogTx(ctx, tx, phase.DeployTaskID, "EndPhase", End, phase.Type, message)

		return nil
	})
}

func (i *impl) DeleteDeployTask(ctx context.Context, taskID int64) error {
	return i.transaction(ctx, func(tx *gorm.DB) error {
		if err := tx.Where("id = ?", taskID).Delete(&entity.DeployTask{}).Error; err != nil {
			log.Errorf("failed to delete DeployTask: %v", err)

			return err
		}

		if err := tx.Where("deploy_task_id = ?", taskID).Delete(&entity.DeployPhase{}).Error; err != nil {
			log.Errorf("failed to delete DeployPhase: %v", err)

			return err
		}

		if err := tx.Where("deploy_task_id = ?", taskID).Delete(&entity.DeployLog{}).Error; err != nil {
			log.Errorf("failed to delete DeployLog: %v", err)

			return err
		}

		return nil
	})
}

func (i *impl) CreateDeployLog(
	ctx context.Context, taskID int64, logType, state, phaseType string, message string,
) error {
	if message == "" {
		message = EmptyMessage
	}
	zlogEntry := entity.DeployLog{
		DeployTaskID: taskID,
		LogTime:      time.Now(),
		Type:         logType,
		State:        state,
		Phase:        phaseType,
		Message:      entity.JSONB(message),
	}
	if err := i.db.WithContext(ctx).Create(&zlogEntry).Error; err != nil {
		log.Errorf("failed to create DeployLog: %v", err)

		return err
	}

	return nil
}

func (i *impl) transaction(ctx context.Context, fn func(*gorm.DB) error) error {
	tx := i.db.WithContext(ctx).Begin()
	defer func() {
		if err := recover(); err != nil {
			log.Errorf("panic in transaction, error: %+v", err)
			tx.Rollback()
		}
	}()

	if err := fn(tx); err != nil {
		log.Errorf("transaction failed: %v", err)
		tx.Rollback()

		return err
	}

	if err := tx.Commit().Error; err != nil {
		log.Errorf("commit failed: %v", err)

		return err
	}

	return nil
}

func (i *impl) createPhaseWithTx(
	ctx context.Context, tx *gorm.DB,
	task *entity.DeployTask, phaseType, phaseName, phaseState, message string,
) (
	err error,
) {
	// Create a DeployPhase
	phase := entity.DeployPhase{
		DeployTaskID: task.ID,
		Name:         phaseName,
		Type:         phaseType,
		State:        phaseState,
		Parameters:   task.Parameters,
		StartedAt:    time.Now(),
	}
	if err = tx.Create(&phase).Error; err != nil {
		log.Errorf("failed to create DeployPhase: %v", err)

		return
	}

	// Create a DeployLog
	go i.createDeployLogTx(ctx, tx, task.ID, "CreatePhase", phaseState, phaseType, message)

	return
}

func (i *impl) createDeployLogTx(ctx context.Context, tx *gorm.DB,
	taskID int64, logType, state, phaseType string, message string,
) {
	if message == "" {
		message = EmptyMessage
	}
	zlogEntry := entity.DeployLog{
		DeployTaskID: taskID,
		LogTime:      time.Now(),
		Type:         logType,
		State:        state,
		Phase:        phaseType,
		Message:      entity.JSONB(message),
	}
	if err := tx.WithContext(ctx).Create(&zlogEntry).Error; err != nil {
		log.Errorf("failed to create DeployLog %v", err)
	}
}
