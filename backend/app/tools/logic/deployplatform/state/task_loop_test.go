package state

import (
	"context"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform"
	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform/entity"
	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform/mocks"
)

func TestTaskLoop_TryDequeue(t *testing.T) {
	// 创建 mock 对象
	repo := &mocks.Deployer{}
	factory := &PhaseTaskFactory{}

	// 创建 TaskLoop 实例
	taskLoop := &TaskLoop{
		queue:   make(chan Task, QueueBatch),
		after:   time.Unix(0, 0),
		seen:    sync.Map{}, // 使用 sync.Map 确保线程安全
		repo:    repo,
		factory: factory,
		ticker:  time.NewTicker(time.Minute),
	}

	// 模拟的 DeployTask
	deployTask := &entity.DeployTask{
		ID:           112233,
		State:        deployplatform.Init,
		CurrentPhase: deployplatform.PhaseCI,
		Name:         "test",
		Title:        "123456",
		CreatedBy:    "test",
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
		Parameters:   entity.JSONB(`{}`),
	}

	// 模拟 GetDeployTasksNotEnded 返回任务列表
	repo.On("GetDeployTasksNotEnded", mock.Anything, mock.Anything).Return([]*entity.DeployTask{deployTask}, nil)

	// 模拟 NewPhaseTask 返回 PhaseTask
	repo.On("GetDeployTaskByID", mock.Anything, mock.Anything).Return(deployTask, nil)
	repo.On("GetPhasesAndLogs", mock.Anything, mock.Anything).Return([]*entity.DeployPhase{}, []*entity.DeployLog{}, nil)

	// 测试 TryDequeue
	task, get := taskLoop.TryDequeue(context.Background())
	assert.False(t, get)
	task, get = taskLoop.TryDequeue(context.Background())
	assert.True(t, get)
	assert.NotNil(t, task)
	assert.Equal(t, deployTask.ID, task.ID())
}

func TestTaskLoop_Loop(t *testing.T) {
	// 创建 mock 对象
	repo := &mocks.Deployer{}
	factory := &PhaseTaskFactory{}

	// 创建 TaskLoop 实例
	taskLoop := &TaskLoop{
		queue:   make(chan Task, QueueBatch),
		after:   time.Unix(0, 0),
		seen:    sync.Map{}, // 使用 sync.Map 确保线程安全
		repo:    repo,
		factory: factory,
		ticker:  time.NewTicker(time.Minute),
	}

	// 模拟的 DeployTask
	deployTask := &entity.DeployTask{
		ID:           112233,
		State:        deployplatform.Init,
		CurrentPhase: deployplatform.PhaseCI,
		Name:         "test",
		Title:        "123456",
		CreatedBy:    "test",
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
		Parameters:   entity.JSONB(`{}`),
	}

	// 模拟 GetDeployTasksNotEnded 返回任务列表
	repo.On("GetDeployTasksNotEnded", mock.Anything, mock.Anything).Return([]*entity.DeployTask{deployTask}, nil)

	// 模拟 NewPhaseTask 返回 PhaseTask
	repo.On("GetDeployTaskByID", mock.Anything, mock.Anything).Return(deployTask, nil)
	repo.On("GetPhasesAndLogs", mock.Anything, mock.Anything).Return([]*entity.DeployPhase{}, []*entity.DeployLog{}, nil)

	// 模拟任务状态变更
	repo.On("UpdatePhaseAndTaskState", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)

	// 使用 context.WithCancel 控制循环退出
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 启动任务循环
	go taskLoop.Loop(ctx)

	// 等待一段时间，确保任务循环处理了任务
	time.Sleep(100 * time.Millisecond)

	// 取消上下文，终止任务循环
	cancel()
}
