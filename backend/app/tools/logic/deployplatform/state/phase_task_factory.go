package state

import (
	"errors"

	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform"
	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform/entity"
)

var ErrPhaseNotFound = errors.New("phase not found")

type PhaseTaskFactory struct {
}

func (f *PhaseTaskFactory) NewPhaseTask(
	repo deployplatform.Deployer, task *entity.DeployTask,
) (
	ret Task, err error,
) {
	switch task.CurrentPhase {
	case deployplatform.PhaseCI:
		ret, err = NewCIPhase(repo, task)
	case deployplatform.PhaseCanary:
		ret, err = NewCanaryPhase(repo, task)
	case deployplatform.PhaseFullDeployment:
		ret, err = NewFullDeploymentPhase(repo, task)
	default:
		err = ErrPhaseNotFound
	}

	return
}
