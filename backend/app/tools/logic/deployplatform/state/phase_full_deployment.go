package state

import (
	"context"
	"sync"
	"time"

	"github.com/samber/lo"

	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform"
	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform/entity"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

func NewFullDeploymentPhase(
	repo deployplatform.Deployer, task *entity.DeployTask,
) (
	fullDeploymentPhase *FullDeploymentPhase, err error,
) {
	fullDeploymentPhase = &FullDeploymentPhase{
		Phase: Phase{
			Repo: repo,
			Task: task,
		},
	}

	return
}

type FullDeploymentPhase struct {
	Phase
	mu sync.Mutex
}

func (p *FullDeploymentPhase) InitState() (err error) {
	err = p.SyncStateFromDB()
	if err != nil {
		log.Errorf("failed to sync state from DB, taskID: %d", p.Task.ID)
	}

	if !lo.ContainsBy(p.Phases, func(phase *entity.DeployPhase) bool {
		return phase.Type == deployplatform.PhaseFullDeployment
	}) {
		err = p.Repo.CreatePhaseAndUpdateTask(
			context.Background(), p.Task, deployplatform.PhaseFullDeployment, p.Task.Name, deployplatform.Init, "{}")
		if err != nil {
			log.Errorf("failed to create phase and update task, taskID: %d", p.Task.ID)

			return err
		}
	}

	return
}

func (p *FullDeploymentPhase) Change(_ map[string]string) (ret Task, err error) {
	// 从数据库中同步当前任务的状态
	if err := p.SyncStateFromDB(); err != nil {
		return nil, err
	}
	// 如果任务已经1小时没有更新，则结束任务。
	if !p.Task.IsEnd() && p.Task.UpdatedAt.Add(time.Hour).Before(time.Now()) {
		p.End(true)

		return p, nil
	}

	currentPhase, err := p.GetCurrentPhase()
	if err != nil {
		return nil, err
	}
	if currentPhase.EndedAt.Valid {
		return nil, nil
	}

	switch currentPhase.State {
	case deployplatform.Init:
		// 执行
		log.Debugf("full deployment Init->Running, taskID: %d", p.Task.ID)
		err = p.SetState(deployplatform.Running)
		if err != nil {
			return nil, err
		}
	case deployplatform.Running:
		// TODO: 跑我们的running逻辑
		if false {
			// TODO: 循环检测Running状态是否结束，是否rollback了，还在Running就丢回去
			return p, nil
		}
		// 跑完了，切换到Succeeded，结束喽
		err = p.SetState(deployplatform.Succeeded)
		if err != nil {
			return nil, err
		}
		log.Debugf("full deployment Running->End, taskID: %d", p.Task.ID)
	default:
		p.End(true)
	}

	return p, nil
}

// End Phase, Maybe End Task
func (p *FullDeploymentPhase) End(endTask bool) {
	p.mu.Lock()
	defer p.mu.Unlock()

	for _, phase := range p.Phases {
		if phase.Type == deployplatform.PhaseFullDeployment {
			p.endPhaseOrTask(phase, endTask)

			break
		}
	}
}

// CheckStatus 检查CI状态
func (p *FullDeploymentPhase) CheckStatus() (err error) {
	return
}
