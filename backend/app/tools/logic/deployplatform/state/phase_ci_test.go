package state

import (
	"database/sql"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"

	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform"
	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform/entity"
	"github.com/MoeGolibrary/moego/backend/app/tools/repo/deployplatform/mocks"
)

type CIPhaseTestSuite struct {
	suite.Suite
}

func TestCIPhasePhaseSuite(t *testing.T) {
	suite.Run(t, new(CIPhaseTestSuite))
}

func (t *CIPhaseTestSuite) TestNewCIPhase() {
	manager := &mocks.Deployer{}
	phase := t.getPhase(deployplatform.Init, deployplatform.Init, deployplatform.PhaseCI, "{}", manager)

	manager.On("UpdatePhaseAndTaskState", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Run(func(args mock.Arguments) {}).Return(nil)

	manager.On("GetDeployTaskByID", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		phase.Task.CurrentPhase = deployplatform.PhaseCI
		phase.Task.State = deployplatform.Failed
	}).Return(phase.Task, nil)

	manager.On("GetPhasesAndLogs", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		t.T().Logf("GetPhasesAndLogs run")

		phase.Phases[0].State = deployplatform.Failed
	}).Return(phase.Phases, phase.Logs, nil)

	ciPhase, err := NewCIPhase(manager, phase.Task)
	t.Nil(err)
	t.NotNil(ciPhase)
	t.Equal(phase.Task.CurrentPhase, ciPhase.Task.CurrentPhase)
	t.Equal(phase.Task.ID, ciPhase.Task.ID)
	t.Equal(phase.Task.State, ciPhase.Task.State)
}

func (t *CIPhaseTestSuite) getPhase(taskType, phaseType string, currentPhase string, parameters string, repo deployplatform.Deployer) Phase {
	return Phase{
		Task: &entity.DeployTask{
			ID:           112233,
			State:        taskType,
			CurrentPhase: currentPhase,
			Name:         "test",
			Title:        "123456",
			CreatedBy:    "test",
			CreatedAt:    time.Now(),
			UpdatedAt:    time.Now(),
			Parameters:   entity.JSONB(`{}`),
		},
		Phases: []*entity.DeployPhase{
			&entity.DeployPhase{
				ID:           112233,
				DeployTaskID: 112233,
				Type:         currentPhase,
				State:        phaseType,
				StartedAt:    time.Now(),
				EndedAt:      sql.NullTime{Time: time.Now()},
				Parameters:   entity.JSONB(parameters),
			},
		},
		Logs: []*entity.DeployLog{
			&entity.DeployLog{
				ID:           112233,
				DeployTaskID: 112233,
				LogTime:      time.Now(),
				Type:         "CIInit",
				Message:      entity.JSONB(`{}`),
				State:        deployplatform.Init,
				Phase:        deployplatform.PhaseCI,
			},
		},
		Repo: repo,
	}
}

func (t *CIPhaseTestSuite) getNewPhasesAndLogs(phase Phase) (newPhases []*entity.DeployPhase, newLogs []*entity.DeployLog) {
	newPhases = append(newPhases, phase.Phases...)
	newPhases = append(newPhases, &entity.DeployPhase{
		DeployTaskID: phase.Task.ID,
		Type:         deployplatform.PhaseCanary,
		State:        deployplatform.Init,
		Parameters:   entity.JSONB(`{}`),
		StartedAt:    time.Now(),
	})
	newLogs = append(newLogs, phase.Logs...)
	newLogs = append(newLogs, &entity.DeployLog{
		DeployTaskID: phase.Task.ID,
		LogTime:      time.Now(),
		Type:         "CreatePhase",
		State:        deployplatform.Init,
		Phase:        deployplatform.PhaseCanary,
		Message: entity.JSONB(fmt.Sprintf(`{
			"taskID": %v,
			"phase": "%v",
			"status": "%v"
		}`, phase.Task.ID, deployplatform.PhaseCanary, deployplatform.Init)),
	})
	return newPhases, newLogs
}

func (t *CIPhaseTestSuite) TestSuccess2End() {
	manager := &mocks.Deployer{}
	phase := t.getPhase(deployplatform.Succeeded, deployplatform.Succeeded, deployplatform.PhaseCI, "{}", manager)

	manager.On("EndDeployTask", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		phase.Task.EndedAt = sql.NullTime{Time: time.Now(), Valid: true}
		phase.Phases[0].EndedAt = sql.NullTime{Time: time.Now(), Valid: true}
	}).Return(nil)

	manager.On("EndDeployPhase", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		phase.Phases[0].EndedAt = sql.NullTime{Time: time.Now(), Valid: true}
	}).Return(nil)

	manager.On("CreatePhaseAndUpdateTask", mock.Anything, mock.Anything,
		mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		t.T().Logf("CreatePhaseAndUpdateTask run")
	}).Return(nil)

	manager.On("GetDeployTaskByID", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		phase.Task.CurrentPhase = deployplatform.PhaseCI
		phase.Task.State = deployplatform.Succeeded
		phase.Task.EndedAt = sql.NullTime{Time: time.Now(), Valid: true}
	}).Return(phase.Task, nil)

	newPhases, newLogs := t.getNewPhasesAndLogs(phase)
	manager.On("GetPhasesAndLogs", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		t.T().Logf("GetPhasesAndLogs run")
	}).Return(newPhases, newLogs, nil)

	manager.On("UpdatePhaseAndTaskState", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Run(func(args mock.Arguments) {}).Return(nil)

	p := &CIPhase{
		Phase: phase,
	}
	got, err := p.Change(nil)
	t.NotNil(got)
	t.T().Logf("task:%v", got)
	t.Nil(err)
	currentPhase, err := got.GetCurrentPhase()
	t.T().Logf("currentPhase:%v", currentPhase)
	t.Nil(err)
	t.Equal(deployplatform.PhaseCI, currentPhase.Type)
	t.Equal(deployplatform.Succeeded, got.StateType())
	t.True(currentPhase.EndedAt.Valid)
}

func (t *CIPhaseTestSuite) TestInit2Failed() {
	manager := &mocks.Deployer{}
	phase := t.getPhase(deployplatform.Init, deployplatform.Init, deployplatform.PhaseCI, "{}", manager)

	manager.On("EndDeployTask", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		phase.Task.EndedAt = sql.NullTime{Time: time.Now(), Valid: true}
		phase.Phases[0].EndedAt = sql.NullTime{Time: time.Now(), Valid: true}
	}).Return(nil)
	manager.On("EndDeployPhase", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		phase.Phases[0].EndedAt = sql.NullTime{Time: time.Now(), Valid: true}
	}).Return(nil)

	manager.On("UpdatePhaseAndTaskState", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Run(func(args mock.Arguments) {}).Return(nil)

	manager.On("GetDeployTaskByID", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		phase.Task.CurrentPhase = deployplatform.PhaseCI
		phase.Task.State = deployplatform.Failed
	}).Return(phase.Task, nil)

	manager.On("GetPhasesAndLogs", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		t.T().Logf("GetPhasesAndLogs run")

		phase.Phases[0].State = deployplatform.Failed
	}).Return(phase.Phases, phase.Logs, nil)

	p := &CIPhase{
		Phase: phase,
	}
	got, err := p.Change(nil)
	t.Nil(err)
	t.Equal(deployplatform.Failed, got.StateType())
}

func (t *CIPhaseTestSuite) TestChange() {
	tests := []struct {
		name      string
		state     string
		params    []string
		wantState string
		wantErr   bool
	}{
		{
			name:      "init to running",
			state:     deployplatform.Init,
			params:    []string{"{}"},
			wantState: deployplatform.Running,
			wantErr:   false,
		},
		{
			name:  "running to failed",
			state: deployplatform.Running,
			params: []string{
				"{\"status\":\"completed\",\"conclusion\":\"failure\"}",
				"{\"status\":\"completed\",\"conclusion\":\"timed_out\"}",
				"{\"status\":\"completed\",\"conclusion\":\"startup_failure\"}",
			},
			wantState: deployplatform.Failed,
			wantErr:   false,
		},
		{
			name:  "running to running",
			state: deployplatform.Running,
			params: []string{
				"{\"status\":\"in_progress\",\"conclusion\":\"null\"}",
			},
			wantState: deployplatform.Running,
			wantErr:   false,
		},
		{
			name:  "running to succeeded",
			state: deployplatform.Running,
			params: []string{
				"{\"status\":\"completed\",\"conclusion\":\"success\"}",
				"{\"status\":\"completed\",\"conclusion\":\"neutral\"}",
			},
			wantState: deployplatform.Succeeded,
			wantErr:   false,
		},
		{
			name:      "running to skipped",
			state:     deployplatform.Running,
			params:    []string{"{\"status\":\"completed\",\"conclusion\":\"skipped\"}"},
			wantState: deployplatform.Skipped,
			wantErr:   false,
		},
		{
			name:      "running to cancelled",
			state:     deployplatform.Running,
			params:    []string{"{\"status\":\"completed\",\"conclusion\":\"cancelled\"}"},
			wantState: deployplatform.Cancelled,
			wantErr:   false,
		},
		{
			name:      "skipped to end",
			state:     deployplatform.Skipped,
			params:    []string{},
			wantState: deployplatform.End,
			wantErr:   false,
		},
		{
			name:      "cancelled to end",
			state:     deployplatform.Cancelled,
			params:    []string{},
			wantState: deployplatform.End,
			wantErr:   false,
		},
		{
			name:      "failed to end",
			state:     deployplatform.Failed,
			params:    []string{},
			wantState: deployplatform.End,
			wantErr:   false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func() {
			for _, param := range tt.params {
				manager := &mocks.Deployer{}
				phase := t.getPhase(tt.state, tt.state, deployplatform.PhaseCI, param, manager)

				manager.On("GetDeployTaskByID", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					phase.Task.CurrentPhase = deployplatform.PhaseCI
					phase.Task.State = tt.state
				}).Return(phase.Task, nil).Once()

				manager.On("GetPhasesAndLogs", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					t.T().Logf("GetPhasesAndLogs run")
					phase.Phases[0].State = tt.state
				}).Return(phase.Phases, phase.Logs, nil).Once()

				manager.On("EndDeployTask", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					phase.Task.EndedAt = sql.NullTime{Time: time.Now(), Valid: true}
					phase.Phases[0].EndedAt = sql.NullTime{Time: time.Now(), Valid: true}
				}).Return(nil)
				manager.On("EndDeployPhase", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					phase.Phases[0].EndedAt = sql.NullTime{Time: time.Now(), Valid: true}
				}).Return(nil)

				manager.On("CreatePhaseAndUpdateTask", mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					t.T().Logf("CreatePhaseAndUpdateTask run")
				}).Return(nil)

				manager.On("UpdatePhaseAndTaskState", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
					Run(func(args mock.Arguments) {}).Return(nil)

				manager.On("GetDeployTaskByID", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					phase.Task.CurrentPhase = deployplatform.PhaseCI
					phase.Task.State = tt.wantState
				}).Return(phase.Task, nil)

				manager.On("GetPhasesAndLogs", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					t.T().Logf("GetPhasesAndLogs run")
					phase.Phases[0].State = tt.wantState
				}).Return(phase.Phases, phase.Logs, nil)

				p := &CIPhase{
					Phase: phase,
				}
				got, err := p.Change(nil)
				if (err != nil) != tt.wantErr {
					t.T().Errorf("Change() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if got.StateType() != tt.wantState {
					t.T().Errorf("Change() got = %v, want %v", got.StateType(), tt.wantState)
				}
				if tt.wantState == deployplatform.End {
					t.True(p.Phases[0].EndedAt.Valid)
				} else {
					t.False(p.Phases[0].EndedAt.Valid)
				}
			}
		})
	}
}

func (t *CIPhaseTestSuite) TestSetState() {
	manager := &mocks.Deployer{}
	phase := t.getPhase(deployplatform.Init, deployplatform.Init, deployplatform.PhaseCI, "{}", manager)

	manager.On("UpdatePhaseAndTaskState", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)
	manager.On("GetDeployTaskByID", mock.Anything, mock.Anything).Return(phase.Task, nil)
	manager.On("GetPhasesAndLogs", mock.Anything, mock.Anything).Return(phase.Phases, phase.Logs, nil)

	p := &CIPhase{
		Phase: phase,
	}
	err := p.SetState(deployplatform.Running)
	t.Nil(err)
}

func (t *CIPhaseTestSuite) TestEnd() {
	manager := &mocks.Deployer{}
	phase := t.getPhase(deployplatform.End, deployplatform.End, deployplatform.PhaseCI, "{}", manager)

	manager.On("EndDeployTask", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		phase.Task.EndedAt = sql.NullTime{Time: time.Now(), Valid: true}
		phase.Phases[0].EndedAt = sql.NullTime{Time: time.Now(), Valid: true}
	}).Return(nil)
	manager.On("EndDeployPhase", mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		phase.Phases[0].EndedAt = sql.NullTime{Time: time.Now(), Valid: true}
	}).Return(nil)

	manager.On("GetDeployTaskByID", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		phase.Task.CurrentPhase = deployplatform.PhaseCI
		phase.Task.State = deployplatform.End
	}).Return(phase.Task, nil)

	manager.On("GetPhasesAndLogs", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		t.T().Logf("GetPhasesAndLogs run")
	}).Return(phase.Phases, phase.Logs, nil)

	p := &CIPhase{
		Phase: phase,
	}
	got, err := p.Change(nil)
	t.Nil(err)
	t.Equal(deployplatform.End, got.StateType())
	t.True(p.Task.EndedAt.Valid)
	t.True(p.Phases[0].EndedAt.Valid)
}
