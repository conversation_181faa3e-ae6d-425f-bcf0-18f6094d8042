load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "utils",
    srcs = ["utils.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/tools/utils",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_google_uuid//:uuid",
        "@com_github_samber_lo//:lo",
        "@org_golang_google_grpc//metadata",
    ],
)

go_test(
    name = "utils_test",
    srcs = ["utils_test.go"],
    embed = [":utils"],
    deps = ["@com_github_stretchr_testify//require"],
)
