package configinit

import (
	"context"

	yaml "gopkg.in/yaml.v2"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/config"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type MessageConfig struct {
	MessageAlert struct {
		DatadogAPIToken string `yaml:"datadog_api_token"`
		DatadogAPPToken string `yaml:"datadog_app_token"`
		OpenAIToken     string `yaml:"openai_token"`
		SlackToken      string `yaml:"slack_token"`
		SlackChannel    string `yaml:"slack_channel"`
		AuditPrompt     string `yaml:"audit_prompt"`
	} `yaml:"message_alert"`
}

func InitMessageAlertLocalConfig() *MessageConfig {
	// Load configuration
	c := new(MessageConfig)
	r, _ := config.Get("nacos").Get(context.Background(), "message-alert.yaml")
	// 反序列化为对应格式（json/toml等等）的struct
	if err := yaml.Unmarshal([]byte(r.Value()), c); err != nil {
		log.Errorf("load config error:%s", err.Error())
		panic(err)
	}

	return c
}
