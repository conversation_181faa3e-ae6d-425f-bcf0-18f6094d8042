package platformsales_test

import (
	"os"
	"testing"

	"github.com/stretchr/testify/require"
	decimalpb "google.golang.org/genproto/googleapis/type/decimal"
	"google.golang.org/genproto/googleapis/type/money"

	"github.com/MoeGolibrary/moego/backend/app/adminopenapi/logic/platformsales"
	platformsalespb "github.com/MoeGolibrary/moego/backend/proto/openapi/admin/platform_sales/v1"
)

func TestBuildSalesLink(t *testing.T) {
	_ = os.Setenv("MOEGO_ENVIRONMENT", "testing")
	link := platformsales.BuildSalesLink("abc")
	require.Equal(t, "https://go.t2.moego.dev/sales/setup?salesCode=abc", link)

	_ = os.Setenv("MOEGO_ENVIRONMENT", "staging")
	link = platformsales.BuildSalesLink("abc")
	require.Equal(t, "https://go.s1.moego.dev/sales/setup?salesCode=abc", link)

	_ = os.Setenv("MOEGO_ENVIRONMENT", "production")
	link = platformsales.BuildSalesLink("abc")
	require.Equal(t, "https://go.moego.pet/sales/setup?salesCode=abc", link)

	_ = os.Setenv("MOEGO_ENVIRONMENT", "xxxx")
	link = platformsales.BuildSalesLink("abc")
	require.Equal(t, "https://go.t2.moego.dev/sales/setup?salesCode=abc", link)
}

func TestGetCompanyType(t *testing.T) {
	tests := []struct {
		companyType           int32
		vanCount              int32
		groomingLocationCount int32
		bdLocationCount       int32
		hasError              bool
	}{
		{0, 0, 0, 0, true},  // error
		{0, 1, 0, 0, false}, // only mobile
		{1, 0, 1, 0, false}, // only salon
		{1, 0, 0, 1, false}, // only salon
		{1, 0, 1, 1, false}, // only salon
		{2, 1, 1, 0, false}, // mobile + salon
		{2, 1, 0, 1, false}, // mobile + salon
		{2, 1, 1, 1, false}, // mobile + salon
	}

	for _, test := range tests {
		result, err := platformsales.GetCompanyType(test.vanCount, test.groomingLocationCount, test.bdLocationCount)
		if test.hasError {
			require.Error(t, err)
		} else {
			require.NoError(t, err)
			require.Equal(t, test.companyType, result)
		}
	}
}

func TestGetPremiumType(t *testing.T) {
	require.Equal(t, 2, platformsales.GetPremiumType("growth"))
	require.Equal(t, 3, platformsales.GetPremiumType("ultimate"))
}

func TestGetRateString(t *testing.T) {
	tests := []struct {
		expected string
		rate     *decimalpb.Decimal
		fee      *money.Money
	}{
		{"", nil, nil},
		{"1% + $1", &decimalpb.Decimal{Value: "1"}, &money.Money{CurrencyCode: "USD", Units: 1}},
		{"1.5% + $1.10", &decimalpb.Decimal{Value: "1.5"}, &money.Money{CurrencyCode: "USD", Units: 1, Nanos: *********}},
		{"1%", &decimalpb.Decimal{Value: "1"}, nil},
		{"$1", nil, &money.Money{CurrencyCode: "USD", Units: 1}},
	}

	for _, test := range tests {
		require.Equal(t, test.expected, platformsales.GetRateString(test.rate, test.fee))
	}
}

func TestDecimalToPercentString(t *testing.T) {
	f := platformsales.DecimalToPercentString
	require.Equal(t, "10.00%", f(&decimalpb.Decimal{Value: "10.00"}))
	require.Equal(t, "10.0%", f(&decimalpb.Decimal{Value: "10.0"}))
	require.Equal(t, "10%", f(&decimalpb.Decimal{Value: "10"}))
	require.Equal(t, "1%", f(&decimalpb.Decimal{Value: "1"}))
	require.Equal(t, "0.1%", f(&decimalpb.Decimal{Value: "0.1"}))
	require.Equal(t, "", f(&decimalpb.Decimal{Value: ""}))
	require.Equal(t, "", f(nil))
}

func TestMoneyToString(t *testing.T) {
	f := platformsales.MoneyToString
	require.Equal(t, "", f(&money.Money{CurrencyCode: "USD", Units: 0, Nanos: 0}))
	require.Equal(t, "$2", f(&money.Money{CurrencyCode: "USD", Units: 2}))
	require.Equal(t, "$2", f(&money.Money{CurrencyCode: "USD", Units: 2, Nanos: 0}))
	require.Equal(t, "$2", f(&money.Money{CurrencyCode: "USD", Units: 2, Nanos: 000000001}))
	require.Equal(t, "$2.75", f(&money.Money{CurrencyCode: "USD", Units: 2, Nanos: 750000000}))
	require.Equal(t, "$2.75", f(&money.Money{CurrencyCode: "USD", Units: 2, Nanos: 751000000}))
	require.Equal(t, "$2.75", f(&money.Money{CurrencyCode: "USD", Units: 2, Nanos: 751111111}))
	require.Equal(t, "$2.70", f(&money.Money{CurrencyCode: "USD", Units: 2, Nanos: 700000000}))
}

func TestDecimalToInt(t *testing.T) {
	tests := []struct {
		expected int64
		input    *decimalpb.Decimal
		hasError bool
	}{
		{0, nil, false},
		{10, &decimalpb.Decimal{Value: "10.00"}, false},
		{10, &decimalpb.Decimal{Value: "10.01"}, false},
		{10, &decimalpb.Decimal{Value: "10.0"}, false},
		{10, &decimalpb.Decimal{Value: "10.1"}, false},
		{10, &decimalpb.Decimal{Value: "10"}, false},
		{1, &decimalpb.Decimal{Value: "1"}, false},
		{0, &decimalpb.Decimal{Value: "0.1"}, false},
		{1, &decimalpb.Decimal{Value: "1."}, false},
		{0, &decimalpb.Decimal{Value: "1.1."}, true},
		{0, &decimalpb.Decimal{Value: "1.."}, true},
		{0, &decimalpb.Decimal{Value: ""}, true},
	}
	for _, test := range tests {
		result, err := platformsales.DecimalToInt(test.input)
		if test.hasError {
			require.Error(t, err, test.input)
		} else {
			require.NoError(t, err)
			require.Equal(t, test.expected, result)
		}
	}
}

func TestBuildParams(t *testing.T) {
	// test annul plan fields
	req := &platformsalespb.CreateSalesLinkRequest{
		IsAnnualPlan:       true,
		ContractTermMonths: 12,
		AnnualPlanDiscount: &decimalpb.Decimal{Value: "10"},
		VanCount:           1,
	}
	params, err := platformsales.BuildParams(req)
	require.NoError(t, err)
	checkFieldExistAndValue(t, params, "showAnnuallyTerm", true)
	checkFieldExistAndValue(t, params, "subTerm", int32(12))
	checkFieldExistAndValue(t, params, "subPriceDiscount", int64(10))
	checkFieldExistAndValue(t, params, "showMonthlyTerm", false)

	req = &platformsalespb.CreateSalesLinkRequest{
		IsAnnualPlan:       true,
		ContractTermMonths: 12,
		VanCount:           1,
	}
	params, err = platformsales.BuildParams(req)
	require.NoError(t, err)
	checkFieldExistAndValue(t, params, "showAnnuallyTerm", true)
	checkFieldExistAndValue(t, params, "subTerm", int32(12))
	checkFieldExistAndValue(t, params, "subPriceDiscount", int64(0))
	checkFieldExistAndValue(t, params, "showMonthlyTerm", false)

	req = &platformsalespb.CreateSalesLinkRequest{
		IsAnnualPlan: false,
		VanCount:     1,
	}
	params, err = platformsales.BuildParams(req)
	require.NoError(t, err)
	checkFieldExistAndValue(t, params, "showMonthlyTerm", true)
	checkFieldExistAndValue(t, params, "showAnnuallyTerm", false)
	checkFieldNotExist(t, params, "subTerm")
	checkFieldNotExist(t, params, "subPriceDiscount")
}

func checkFieldExistAndValue(t *testing.T, m map[string]interface{}, fieldName string, value any) {
	v, ok := m[fieldName]
	require.True(t, ok)
	require.Equal(t, value, v)
}

func checkFieldNotExist(t *testing.T, m map[string]interface{}, fieldName string) {
	_, ok := m[fieldName]
	require.False(t, ok, fieldName)
}
