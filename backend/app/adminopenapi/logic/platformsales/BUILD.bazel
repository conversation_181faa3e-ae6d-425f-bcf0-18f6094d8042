load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "platformsales",
    srcs = [
        "entity.go",
        "platform_sales.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/adminopenapi/logic/platformsales",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/adminopenapi/repo",
        "//backend/common/rpc/framework/log",
        "//backend/proto/openapi/admin/platform_sales/v1:platform_sales",
        "@com_github_shopspring_decimal//:decimal",
        "@org_golang_google_genproto//googleapis/type/decimal",
        "@org_golang_google_genproto//googleapis/type/money",
    ],
)

go_test(
    name = "platformsales_test",
    srcs = ["platform_sales_test.go"],
    deps = [
        ":platformsales",
        "//backend/proto/openapi/admin/platform_sales/v1:platform_sales",
        "@com_github_stretchr_testify//require",
        "@org_golang_google_genproto//googleapis/type/decimal",
        "@org_golang_google_genproto//googleapis/type/money",
    ],
)
