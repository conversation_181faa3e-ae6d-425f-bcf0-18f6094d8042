package service

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/authn/logic/greeter"
	authnpb "github.com/MoeGolibrary/moego/backend/proto/authn/v1"
)

type Authn struct {
	gr *greeter.Logic
	authnpb.UnimplementedAuthnServiceServer
}

func NewAuthn() *Authn {
	return &Authn{
		gr: greeter.NewLogic(),
	}
}

func (g Authn) SendPing(ctx context.Context,
	_ *authnpb.SendPingRequest) (*authnpb.SendPingResponse, error) {
	msg, err := g.gr.Hello(ctx)
	if err != nil {
		return nil, err
	}

	return &authnpb.SendPingResponse{Pong: msg}, nil
}
