server:
  app: template
  server: template-go
  filter:
    - opentelemetry
    - debuglog
    - recovery
    - validation
  service:
    - name: backend.proto.authn.v1.AuthnService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 5000
client:
  network: tcp
  protocol: grpc
  filter:
    - opentelemetry
    - debuglog
  transport: grpc
  timeout: 5000
  service:
    - callee: greeter_cutomized_name
      target: ip://127.0.0.1:9090
      protocol: grpc
      network: tcp
      transport: grpc
    - callee: postgres.moego_todo
      target: dsn://postgresql://${secret.datasource.postgres.moego_todo.username}:${secret.datasource.postgres.moego_todo.password}@${secret.datasource.postgres.url}:${secret.datasource.postgres.port}/moego_todo
      protocol: gorm
      transport: gorm
plugins:
  database:
    gorm:
      max_idle: 20
      max_open: 100
      max_lifetime: 180000 # millisecond
      service:
        - name: mysql.xxxx
          max_idle: 10
          max_open: 50
          max_lifetime: 180000
        - name: postgres.moego_todo
          max_idle: 10
          max_open: 50
          max_lifetime: 180000
  telemetry:
    opentelemetry:
      traces:
        disable_trace_body: false
  auth:
    validation:
      enable_error_log: false
  config:
    nacos:
      providers:
        - name: nacos
          enabled: true
          server_addr: ${secret.nacos.server-addr}
          username: ${secret.nacos.username}
          password: ${secret.nacos.password}
          namespace: ${secret.nacos.namespace}

secrets:
  - name: 'moego/testing/datasource'
    prefix: 'secret.datasource.'
  - name: "moego/testing/nacos"
    prefix: "secret.nacos."