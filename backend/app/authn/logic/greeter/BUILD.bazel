load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "greeter",
    srcs = [
        "entity.go",
        "greeter_logic.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/authn/logic/greeter",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/authn/repo/gorm",
        "//backend/common/rpc/framework/log",
    ],
)

go_test(
    name = "greeter_test",
    srcs = ["greter_logic_test.go"],
    deps = [
        ":greeter",
        "@com_github_stretchr_testify//assert",
    ],
)
