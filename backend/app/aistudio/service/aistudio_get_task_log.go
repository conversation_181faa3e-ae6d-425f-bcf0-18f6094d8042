package service

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio"
	"github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio/entity"
	toolspb "github.com/MoeGolibrary/moego/backend/proto/aistudio/v1"
)

// GetAiStudioTaskLog retrieves an AI Studio task log.
func (s *AiStudioService) GetAiStudioTaskLog(ctx context.Context,
	req *toolspb.GetAiStudioTaskLogRequest) (*toolspb.AiStudioTaskLog, error) {
	repo := aistudio.New()
	taskLog, err := repo.GetTaskLog(ctx, req.Id)
	if err != nil {
		return nil, fmt.Errorf("failed to get task log: %w", err)
	}

	if taskLog == nil {
		return nil, fmt.Errorf("task log not found with id: %d", req.Id)
	}

	var dialogues entity.AiStudioTaskLogDialogues
	if err := json.Unmarshal(taskLog.Dialogues, &dialogues); err != nil {
		return nil, fmt.Errorf("failed to unmarshal dialogues: %w", err)
	}

	return &toolspb.AiStudioTaskLog{
		Id:        taskLog.ID,
		TaskId:    taskLog.TaskID,
		Prompt:    taskLog.Prompt,
		Envs:      taskLog.Envs,
		Dialogues: dialogues.Chats,
	}, nil
}
