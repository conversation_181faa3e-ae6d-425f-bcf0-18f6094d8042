//nolint:lll
package service

import (
	"context"
	"strings"
	"time"

	"github.com/slack-go/slack"

	"github.com/MoeGolibrary/moego/backend/app/aistudio/logic/aistudio"
	repoaistudio "github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio"
	"github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio/entity"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	toolspb "github.com/MoeGolibrary/moego/backend/proto/aistudio/v1"
)

type AiStudioService struct {
	toolspb.UnimplementedAIStudioServiceServer
	slackClient *slack.Client
	crontab     *aistudio.StringIDCron
}

func NewAiStudioService() *AiStudioService {
	slackBotToken := "*********************************************************"
	if slackBotToken == "" {
		panic("SLACK_BOT_TOKEN environment variable not set")
	}
	cron := aistudio.NewStringIDCron()
	cron.Start()

	s := &AiStudioService{
		slackClient: slack.New(slackBotToken),
		crontab:     cron,
	}
	s.syncDBTask(context.Background())

	return s
}

func (s *AiStudioService) syncDBTask(ctx context.Context) {

	sync := func() {
		repo := repoaistudio.New()
		tasks, err := repo.GetTasks(ctx, &entity.AiStudioTask{
			Spec: "all",
		})
		if err != nil {
			log.ErrorContextf(ctx, "syncDBTask err: %v", err)

			return
		}
		// 注册定时任务
		for _, task := range tasks {
			s.addCronJob(task.Spec, task)
		}
	}

	sync() // 初始化运行一次

	// 每分钟运行一次
	// For sync task, we use current time as version since it doesn't relate to a specific entity
	_ = s.crontab.AddFunc("sync_db_task", time.Now(), "* * * * *", sync)
}

func (s *AiStudioService) RequestAiStudioMcpClient(ctx context.Context,
	req *toolspb.RequestAiStudioMcpClientRequest) (*toolspb.RequestAiStudioMcpClientResponse, error) {
	geminiKey := req.GeminiKey
	prompt := req.Prompt
	mcps := strings.Split(req.Mcps, ",")
	envsString := req.Envs
	model := req.Model
	envs := aistudio.ParseEnvs(envsString)

	log.InfoContextf(ctx, "RequestAiStudioMcpClient geminiKey: %s, prompt: %s, mcps: %v, envs: %s, model: %s",
		geminiKey, prompt, mcps, envs, model)

	client := aistudio.GetClient(model, prompt, geminiKey, mcps, envs)
	resp, err := client.Send2LLM(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "RequestAiStudioMcpClient err: %v", err)

		return nil, err
	}

	response := &toolspb.RequestAiStudioMcpClientResponse{
		Status: 0,
		Msg:    "Success",
		Data: &toolspb.ResponseData{
			Dialogues: resp.Dialogues,
		},
	}

	return response, nil
}
