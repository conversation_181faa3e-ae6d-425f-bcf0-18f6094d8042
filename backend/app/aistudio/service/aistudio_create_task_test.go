package service

import (
	"testing"

	"github.com/stretchr/testify/assert"

	toolspb "github.com/MoeGolibrary/moego/backend/proto/aistudio/v1"
)

func TestCheckCreateTask(t *testing.T) {
	// Test case 1: envs match
	req1 := &toolspb.CreateAiStudioTaskRequest{
		Task:       "taskTest",
		TemplateId: 1,
		Ai<PERSON><PERSON>:      "aiKeyTest",
		Envs:       "key1=value1\nkey2=value2",
	}
	regEnvKeys1 := []string{"key1", "key2"}
	resp1, isReturn1 := checkCreateTask(req1, regEnvKeys1)
	assert.Nil(t, resp1)
	assert.False(t, isReturn1)

	// Test case 2: envs not match, different length
	req2 := &toolspb.CreateAiStudioTaskRequest{
		Task:       "taskTest",
		TemplateId: 1,
		Ai<PERSON>ey:      "aiKeyTest",
		Envs:       "key1=value1",
	}
	regEnvKeys2 := []string{"key1", "key2"}
	resp2, isReturn2 := checkCreateTask(req2, regEnvKeys2)
	assert.NotNil(t, resp2)
	assert.True(t, isReturn2)
	assert.Equal(t, "envs not match, need [key1 key2], got [key1]", resp2.Msg)

	// Test case 3: envs not match, different keys
	req3 := &toolspb.CreateAiStudioTaskRequest{
		Task:       "taskTest",
		TemplateId: 1,
		AiKey:      "aiKeyTest",
		Envs:       "key1=value1\nkey3=value3",
	}
	regEnvKeys3 := []string{"key1", "key2"}
	resp3, isReturn3 := checkCreateTask(req3, regEnvKeys3)
	assert.NotNil(t, resp3)
	assert.True(t, isReturn3)
	assert.Equal(t, "envs not match, need [key1 key2], got [key1 key3]", resp3.Msg)

	// Test case 4: empty envs
	req4 := &toolspb.CreateAiStudioTaskRequest{
		Task:       "taskTest",
		TemplateId: 1,
		AiKey:      "aiKeyTest",
		Envs:       "",
	}
	regEnvKeys4 := []string{}
	resp4, isReturn4 := checkCreateTask(req4, regEnvKeys4)
	assert.Nil(t, resp4)
	assert.False(t, isReturn4)

	// Test case 5: empty task
	req5 := &toolspb.CreateAiStudioTaskRequest{
		Task:       "",
		TemplateId: 1,
		AiKey:      "aiKeyTest",
		Envs:       "key1=value1\nkey2=value2",
	}
	regEnvKeys5 := []string{"key1", "key2"}
	resp5, isReturn5 := checkCreateTask(req5, regEnvKeys5)
	assert.NotNil(t, resp5)
	assert.True(t, isReturn5)
}
