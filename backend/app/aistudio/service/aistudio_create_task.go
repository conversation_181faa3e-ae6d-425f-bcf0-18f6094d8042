package service

import (
	"context"
	"fmt"
	"math/rand"
	"sort"
	"strings"
	"time"

	"github.com/samber/lo"

	aistudiologic "github.com/MoeGolibrary/moego/backend/app/aistudio/logic/aistudio"
	"github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio"
	"github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio/entity"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	toolspb "github.com/MoeGolibrary/moego/backend/proto/aistudio/v1"
)

func checkCreateTask(req *toolspb.CreateAiStudioTaskRequest,
	regEnvKeys []string) (resp *toolspb.AiStudioTask, isReturn bool) {

	if req.Task == "" {
		resp = &toolspb.AiStudioTask{
			Status: 1,
			Msg:    "failed to get task, task is empty",
		}

		return resp, true
	}

	if req.TemplateId == 0 {
		resp = &toolspb.AiStudioTask{
			Status: 1,
			Msg:    "failed to get template, template is empty",
		}

		return resp, true
	}

	if req.AiKey == "" {
		resp = &toolspb.AiStudioTask{
			Status: 1,
			Msg:    "failed to get ai key, ai key is empty",
		}

		return resp, true
	}

	// req envs: key=value\nkey=value
	reqEnvsMap := aistudiologic.ParseEnvs(req.Envs)

	reqEnvKeys := lo.Keys(reqEnvsMap)

	// 检查任务定义的环境变量和注册的环境变量是否一致
	reqEnvKeysSorted := sort.StringSlice(reqEnvKeys)
	regEnvKeysSorted := sort.StringSlice(regEnvKeys)

	if len(reqEnvKeysSorted) != len(regEnvKeysSorted) {
		resp = &toolspb.AiStudioTask{
			Status: 1,
			Msg:    fmt.Sprintf("envs not match, need %v, got %v", regEnvKeysSorted, reqEnvKeys),
		}

		return resp, true
	}

	for i := 0; i < len(reqEnvKeysSorted); i++ {
		if reqEnvKeysSorted[i] != regEnvKeysSorted[i] {
			resp = &toolspb.AiStudioTask{
				Status: 1,
				Msg:    fmt.Sprintf("envs not match, need %v, got %v", regEnvKeysSorted, reqEnvKeys),
			}

			return resp, true
		}
	}

	return nil, false
}

// CreateAiStudioTask creates a new AI Studio task.
func (s *AiStudioService) CreateAiStudioTask(ctx context.Context,
	req *toolspb.CreateAiStudioTaskRequest) (resp *toolspb.AiStudioTask, err error) {
	repo := aistudio.New()
	// 检查req的参数是否为空
	if req.TemplateId == 0 {
		resp = &toolspb.AiStudioTask{
			Status: 1,
			Msg:    "failed to get template, template is empty",
		}

		return resp, nil
	}

	targetTemplate, err := repo.GetTemplate(ctx, req.TemplateId)
	if err != nil {
		resp = &toolspb.AiStudioTask{
			Status: 1,
			Msg:    fmt.Sprintf("failed to get template: %v", err),
		}

		return resp, nil
	}

	// 查看这个模版预定要使用的regEnvKeys
	regEnvKeys := strings.Split(targetTemplate.EnvKeys, ",")
	if resp, isReturn := checkCreateTask(req, regEnvKeys); isReturn {
		return resp, nil
	}

	taskEntity := &entity.AiStudioTask{
		TaskName:   req.Task,
		TemplateID: req.TemplateId,
		Envs:       req.Envs,
		AIKey:      req.AiKey,
		IMChannel:  req.ImChannel,
		Spec:       req.Spec,
	}

	err = repo.CreateTask(ctx, taskEntity)
	if err != nil {
		resp = &toolspb.AiStudioTask{
			Status: 1,
			Msg:    fmt.Sprintf("failed to create task: %v", err),
		}

		return resp, nil
	}

	s.addCronJob(req.Spec, taskEntity)

	resp = &toolspb.AiStudioTask{
		Status: 0,
		Msg:    "Success",
		Data: &toolspb.AiStudioTaskData{
			Id:         taskEntity.ID,
			Task:       taskEntity.TaskName,
			TemplateId: taskEntity.TemplateID,
			Envs:       taskEntity.Envs,
			AiKey:      taskEntity.AIKey,
			ImChannel:  taskEntity.IMChannel,
			Spec:       taskEntity.Spec,
		},
	}

	return resp, nil
}

func (s *AiStudioService) convertSpec2CrontabSpec(userSpec string) string {
	var spec = ""
	switch userSpec {
	case "SpecDaily10AM":
		spec = aistudiologic.CronSpecDaily10AM
	case "SpecDaily6PM":
		spec = aistudiologic.CronSpecDaily6PM
	case "SpecEvery30Min":
		spec = aistudiologic.CronSpecEvery30Min
	case "SpecEvery5Min":
		spec = aistudiologic.CronSpecEvery5Min
	case "SpecDaily3PM":
		spec = aistudiologic.CronSpecDaily3PM
	case "SpecWeeklyFriday10AM":
		spec = aistudiologic.CronSpecWeeklyFriday10AM
	}

	return spec
}

func (s *AiStudioService) addCronJob(spec string, task *entity.AiStudioTask) {
	spec = s.convertSpec2CrontabSpec(spec)
	if spec == "" {
		// 删除任务
		ret := s.crontab.Remove(fmt.Sprint(task.ID))
		log.Infof("addCronJob, will remove cron:%v, ret:%v", task.ID, ret)
	} else {
		// 添加定时任务，使用task.UpdatedAt作为版本
		log.Infof("addCronJob, add task:%v, spec:%v", task.TaskName, spec)
		err := s.crontab.AddFunc(fmt.Sprint(task.ID), task.UpdatedAt, spec, func() {
			s.runAiStudioCronJob(context.Background(), task, spec)
		})
		if err != nil {
			log.Errorf("addCronJob, failed to add cron job, task:%v, spec:%v, err:%v",
				task.TaskName, spec, err)
		}
	}
}

// runAiStudioCronJob executes the logic for a scheduled AI Studio task.
func (s *AiStudioService) runAiStudioCronJob(ctx context.Context, taskEntity *entity.AiStudioTask, spec string) {
	log.Infof("runAiStudioCronJob runs, task:%v", taskEntity.TaskName)
	// 随机sleep 1-10秒，避免惊群效应
	sleepTime := time.Duration(rand.Intn(10)+1) * time.Second
	time.Sleep(sleepTime)

	now := time.Now()
	// 使用 YYYYMMDDHHMM 格式来确保jobID的每日唯一性，防止因分钟数重复导致第二天任务无法执行
	minuteFormat := now.Format("200601021504")
	jobID := fmt.Sprintf("%v_%v", taskEntity.TaskName, minuteFormat)

	// 在一分钟内，最多允许创建一个任务，这是为了防止并发执行定时任务
	// repo.CreateAiStudioCronJob 内部通过db uniq处理唯一性
	repo := aistudio.New()                                          // 每次执行时创建新的 repo 实例，确保线程安全
	err := repo.CreateAiStudioCronJob(ctx, &entity.AiStudioCronJob{ // 传递 ctx
		JobID:       jobID,
		TaskID:      taskEntity.ID,
		LastRunTime: time.Now(),
		CronSpec:    spec,
	})
	if err != nil {
		log.WarnContextf(ctx, "runAiStudioCronJob, failed to create cron job, jobID: %v, error: %v", jobID, err)

		return
	}

	taskRunner := aistudiologic.NewTask()                                                     // 每次执行时创建新的 TaskRunner 实例
	taskResp, err := taskRunner.RunAiStudioTask(ctx, taskEntity.TaskName, taskEntity.ID, nil) // 传递 ctx
	if err != nil {
		log.ErrorContextf(ctx, "runAiStudioCronJob, failed to run task: %v, taskName: %v, taskID: %v",
			err, taskEntity.TaskName, taskEntity.ID)
	}
	if taskResp.IMChannel != "" {
		err = s.slackMessage(taskResp.IMChannel, taskResp.Result)
		if err != nil {
			log.ErrorContextf(ctx, "send slackMessage error: %v", err)

			return
		}
	}
}
