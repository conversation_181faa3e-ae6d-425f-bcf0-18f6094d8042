package service

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio"
	"github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio/entity"
	toolspb "github.com/MoeGolibrary/moego/backend/proto/aistudio/v1"
)

// CreateAiStudioTaskLog creates a new AI Studio task log.
func (s *AiStudioService) CreateAiStudioTaskLog(ctx context.Context,
	req *toolspb.CreateAiStudioTaskLogRequest) (*toolspb.AiStudioTaskLog, error) {
	repo := aistudio.New()

	dialoguesJSON, err := json.Marshal(entity.AiStudioTaskLogDialogues{
		Chats: req.Dialogues,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to marshal dialogues: %w", err)
	}

	entity := &entity.AiStudioTaskLog{
		TaskID:    req.TaskId,
		Prompt:    req.Prompt,
		Envs:      req.Envs,
		Dialogues: dialoguesJSON,
	}

	err = repo.CreateTaskLog(ctx, entity)
	if err != nil {
		return nil, fmt.Errorf("failed to create task log: %w", err)
	}

	var dialogues []string
	if err := json.Unmarshal(entity.Dialogues, &dialogues); err != nil {
		return nil, fmt.Errorf("failed to unmarshal dialogues: %w", err)
	}

	return &toolspb.AiStudioTaskLog{
		Id:        entity.ID,
		TaskId:    entity.TaskID,
		Prompt:    entity.Prompt,
		Envs:      entity.Envs,
		Dialogues: dialogues,
	}, nil
}
