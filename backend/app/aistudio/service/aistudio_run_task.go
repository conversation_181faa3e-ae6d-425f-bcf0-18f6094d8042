package service

import (
	"context"
	"fmt"

	"github.com/slack-go/slack"

	aistudiologic "github.com/MoeGolibrary/moego/backend/app/aistudio/logic/aistudio"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	toolspb "github.com/MoeGolibrary/moego/backend/proto/aistudio/v1"
)

// RunAiStudioTask 运行模版
func (s *AiStudioService) RunAiStudioTask(ctx context.Context,
	req *toolspb.RunAiStudioTaskRequest) (resp *toolspb.RunAiStudioTaskResponse, err error) {
	resp = &toolspb.RunAiStudioTaskResponse{}

	reqEnvKV := aistudiologic.ParseEnvs(req.Envs)
	taskRunner := aistudiologic.NewTask()
	taskResp, err := taskRunner.RunAiStudioTask(ctx, req.Name, req.Id, reqEnvKV)
	if err != nil {
		resp.Status = 1
		resp.Msg = err.Error()

		return resp, nil
	}

	if taskResp.IMChannel != "" {
		err = s.slackMessage(taskResp.IMChannel, taskResp.Result)
		if err != nil {
			log.ErrorContextf(ctx, "send slackMessage error: %v", err)

			return resp, nil
		}
	}

	resp.Data = taskResp.Dialogues

	return resp, nil
}

func (s *AiStudioService) slackMessage(channelID, message string) (err error) {
	_, _, err = s.slackClient.PostMessage(
		channelID,
		slack.MsgOptionText(message, false),
	)

	// 4. Check for errors during the API call
	if err != nil {
		// Return a more informative error, including the original error
		return fmt.Errorf("failed to send message to channel %s: %w", channelID, err)
	}

	return err
}
