package service

import (
	"context"
	"encoding/json"
	"fmt"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio"
	"github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio/entity"
	toolspb "github.com/MoeGolibrary/moego/backend/proto/aistudio/v1"
)

// ListAiStudioTaskLog lists AI Studio task logs.
func (s *AiStudioService) ListAiStudioTaskLog(ctx context.Context,
	req *toolspb.ListAiStudioTaskLogRequest) (*toolspb.ListAiStudioTaskLogResponse, error) {
	repo := aistudio.New()
	taskLogs, total, err := repo.ListTaskLogs(ctx, req.PerPage, req.Page)
	if err != nil {
		return nil, fmt.Errorf("failed to list task logs: %w", err)
	}

	var pbTaskLogs []*toolspb.AiStudioTaskLog
	for _, taskLog := range taskLogs {
		var dialogues entity.AiStudioTaskLogDialogues
		if err := json.Unmarshal(taskLog.Dialogues, &dialogues); err != nil {
			return nil, fmt.Errorf("failed to unmarshal dialogues: %w", err)
		}

		pbTaskLogs = append(pbTaskLogs, &toolspb.AiStudioTaskLog{
			Id:         taskLog.ID,
			TaskId:     taskLog.TaskID,
			Prompt:     taskLog.Prompt,
			Envs:       taskLog.Envs,
			Dialogues:  dialogues.Chats,
			CreateTime: timestamppb.New(taskLog.CreateTime),
		})
	}

	return &toolspb.ListAiStudioTaskLogResponse{
		Status: 0,
		Msg:    "Success",
		Data: &toolspb.ListAiStudioTaskLogData{
			Items: pbTaskLogs,
			Total: total,
		},
	}, nil
}
