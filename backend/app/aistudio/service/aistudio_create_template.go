package service

import (
	"context"
	"fmt"
	"strings"

	"github.com/samber/lo"

	aistudiologic "github.com/MoeGolibrary/moego/backend/app/aistudio/logic/aistudio"
	"github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio"
	"github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio/entity"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	toolspb "github.com/MoeGolibrary/moego/backend/proto/aistudio/v1"
)

// CreateAistudioTemplate creates a new AI Studio template.
func (s *AiStudioService) CreateAiStudioTemplate(ctx context.Context,
	req *toolspb.CreateAiStudioTemplateRequest) (resp *toolspb.AiStudioTemplate, err error) {
	resp = &toolspb.AiStudioTemplate{
		Status: 0,
		Msg:    "Success",
	}

	repo := aistudio.New()
	reqEnvKeys := lo.Keys(aistudiologic.ParseEnvs(req.Envs))
	// 检查req的参数是否为空
	if req.Model == "" || req.Prompt == "" || len(req.Mcps) == 0 {
		resp.Status = 1
		resp.Msg = "Model, Prompt, and Mcps cannot be empty"

		return resp, nil
	}
	reqMCPs := strings.Split(req.Mcps, ",")

	mcpKeys := aistudiologic.LoadMCPEnvs()
	// 过滤出用户自定义的envs, 不在mcpKeys中的key
	userEnvKeys := lo.Filter(reqEnvKeys, func(key string, _ int) bool {
		return !lo.Contains(mcpKeys, key)
	})

	// 过滤出用户选择的mcp中包含的envs
	realMcpKeys := []string{}
	for _, mcpName := range reqMCPs {
		mcpKeys := aistudiologic.LoadMCPEnvsByMCPName(mcpName)
		for _, envKey := range reqEnvKeys {
			if lo.Contains(mcpKeys, envKey) {
				realMcpKeys = append(realMcpKeys, envKey)
			}
		}
	}
	log.InfoContextf(ctx, "CreateAiStudioTemplate mcpKeys: %v, userEnvKeys: %v, realMcpKeys: %v",
		mcpKeys, userEnvKeys, realMcpKeys)

	entity := &entity.AiStudioTemplate{
		Model:   req.Model, // gemini-2.0-flash-001
		Prompt:  req.Prompt,
		Mcps:    req.Mcps, // mcp-atlassian,datadog,mcp-slack,unix_timestamps_mcp
		EnvKeys: strings.Join(append(userEnvKeys, realMcpKeys...), ","),
		Name:    req.Name,
	}
	err = repo.CreateTemplate(ctx, entity)
	if err != nil {
		resp.Status = 1
		resp.Msg = fmt.Sprintf("failed to create template: %v", err)

		return resp, nil
	}
	resp.Data = &toolspb.AiStudioTemplateData{
		Id:      entity.ID,
		Model:   entity.Model,
		Prompt:  entity.Prompt,
		Mcps:    entity.Mcps,
		EnvKeys: strings.Join(reqEnvKeys, ","),
	}

	return resp, nil
}
