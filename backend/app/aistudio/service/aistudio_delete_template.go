package service

import (
	"context"

	"google.golang.org/protobuf/types/known/emptypb"

	"github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio"
	toolspb "github.com/MoeGolibrary/moego/backend/proto/aistudio/v1"
)

func (s *AiStudioService) DeleteAiStudioTemplate(ctx context.Context,
	req *toolspb.DeleteAiStudioTemplateRequest) (*emptypb.Empty, error) {
	repo := aistudio.New()
	err := repo.DeleteTemplate(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}
