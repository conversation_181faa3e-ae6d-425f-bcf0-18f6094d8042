package service

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio"
	toolspb "github.com/MoeGolibrary/moego/backend/proto/aistudio/v1"
)

func (s *AiStudioService) GetAiStudioTemplate(ctx context.Context,
	req *toolspb.GetAiStudioTemplateRequest) (*toolspb.AiStudioTemplate, error) {
	dbRepo := aistudio.New()
	dbEntity, err := dbRepo.GetTemplate(ctx, req.Id)
	if err != nil {
		return &toolspb.AiStudioTemplate{
			Status: 1,
			Msg:    err.Error(),
		}, nil
	}

	data := &toolspb.AiStudioTemplateData{
		Id:      dbEntity.ID,
		Model:   dbEntity.Model,
		Prompt:  dbEntity.Prompt,
		Mcps:    dbEntity.Mcps,
		EnvKeys: dbEntity.EnvKeys,
		Name:    dbEntity.Name,
	}

	return &toolspb.AiStudioTemplate{
		Status: 0,
		Msg:    "Success",
		Data:   data,
	}, nil
}
