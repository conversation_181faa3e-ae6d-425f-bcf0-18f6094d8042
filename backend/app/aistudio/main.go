package main

import (
	"time"
	_ "time/tzdata"

	"github.com/MoeGolibrary/moego/backend/app/aistudio/service"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/recovery"
	rpc "github.com/MoeGolibrary/moego/backend/common/rpc/framework"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	tools "github.com/MoeGolibrary/moego/backend/proto/aistudio/v1"
)

func main() {
	// 设置全局时区为 UTC
	time.Local = time.UTC
	s := rpc.NewServer()

	// 这里需要注册grpc服务
	grpc.Register(s, &tools.AIStudioService_ServiceDesc, service.NewAiStudioService())

	if err := s.Serve(); err != nil {
		log.Fatalf("failed to serve: %v", err)
	}

	log.Infof("server stopped")
}
