package entity

import (
	"time"

	"gorm.io/datatypes"
)

type AiStudioTemplate struct {
	ID      int64  `json:"id" gorm:"primaryKey"`
	Model   string `json:"model" gorm:"column:model"`
	Prompt  string `json:"prompt" gorm:"column:prompt"`
	Mcps    string `json:"mcps" gorm:"column:mcps"`
	EnvKeys string `json:"env_keys" gorm:"column:env_keys"`
	Name    string `json:"name" gorm:"column:name"`
}

func (a *AiStudioTemplate) TableName() string {
	return "aistudio_template"
}

type AiStudioTask struct {
	ID         int64            `json:"id" gorm:"primaryKey"`
	TaskName   string           `json:"task_name" gorm:"column:task_name"`
	TemplateID int64            `json:"template_id" gorm:"column:template_id"`
	Template   AiStudioTemplate `json:"template" gorm:"foreignKey:TemplateID"` // 关联 aistudio_template
	Envs       string           `json:"envs" gorm:"column:envs"`
	AIKey      string           `json:"ai_key" gorm:"column:ai_key"`
	IMChannel  string           `json:"im_channel" gorm:"column:im_channel"`
	Spec       string           `json:"spec" gorm:"column:spec"`
	UpdatedAt  time.Time        `json:"updated_at" gorm:"column:updated_at"`
}

func (a *AiStudioTask) TableName() string {
	return "aistudio_task"
}

type AiStudioTaskLog struct {
	ID         int64          `json:"id" gorm:"primaryKey"`
	TaskID     int64          `json:"task_id" gorm:"column:task_id"`
	Task       AiStudioTask   `json:"task" gorm:"foreignKey:TaskID"` // 关联 aistudio_task
	Prompt     string         `json:"prompt" gorm:"column:prompt"`
	Envs       string         `json:"envs" gorm:"column:envs"`
	Dialogues  datatypes.JSON `json:"dialogues" gorm:"column:dialogues"`
	CreateTime time.Time      `json:"create_time" gorm:"column:create_time"`
}

type AiStudioTaskLogDialogues struct {
	Chats []string `json:"chats"`
}

func (a *AiStudioTaskLog) TableName() string {
	return "aistudio_task_log"
}

type AiStudioCronJob struct {
	ID          int64        `json:"id" gorm:"primaryKey"`
	JobID       string       `json:"job_id" gorm:"column:job_id;uniqueIndex"`
	TaskID      int64        `json:"task_id" gorm:"column:task_id"`
	Task        AiStudioTask `json:"task" gorm:"foreignKey:TaskID"` // 关联 aistudio_task
	LastRunTime time.Time    `json:"last_run_time" gorm:"column:last_run_time"`
	CronSpec    string       `json:"cron_spec" gorm:"column:cron_spec"`
}

func (a *AiStudioCronJob) TableName() string {
	return "aistudio_cron_job"
}
