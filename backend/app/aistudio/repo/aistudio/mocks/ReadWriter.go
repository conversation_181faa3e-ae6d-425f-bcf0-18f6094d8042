// Code generated by mockery v2.52.3. DO NOT EDIT.

package mocks

import (
	context "context"

	entity "github.com/MoeGolibrary/moego/backend/app/aistudio/repo/aistudio/entity"
	mock "github.com/stretchr/testify/mock"
)

// ReadWriter is an autogenerated mock type for the ReadWriter type
type ReadWriter struct {
	mock.Mock
}

// CreateAiStudioCronJob provides a mock function with given fields: ctx, cronJob
func (_m *ReadWriter) CreateAiStudioCronJob(ctx context.Context, cronJob *entity.AiStudioCronJob) error {
	ret := _m.Called(ctx, cronJob)

	if len(ret) == 0 {
		panic("no return value specified for CreateAiStudioCronJob")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.AiStudioCronJob) error); ok {
		r0 = rf(ctx, cronJob)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CreateTask provides a mock function with given fields: ctx, task
func (_m *ReadWriter) CreateTask(ctx context.Context, task *entity.AiStudioTask) error {
	ret := _m.Called(ctx, task)

	if len(ret) == 0 {
		panic("no return value specified for CreateTask")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.AiStudioTask) error); ok {
		r0 = rf(ctx, task)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CreateTaskLog provides a mock function with given fields: ctx, taskLog
func (_m *ReadWriter) CreateTaskLog(ctx context.Context, taskLog *entity.AiStudioTaskLog) error {
	ret := _m.Called(ctx, taskLog)

	if len(ret) == 0 {
		panic("no return value specified for CreateTaskLog")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.AiStudioTaskLog) error); ok {
		r0 = rf(ctx, taskLog)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CreateTemplate provides a mock function with given fields: ctx, template
func (_m *ReadWriter) CreateTemplate(ctx context.Context, template *entity.AiStudioTemplate) error {
	ret := _m.Called(ctx, template)

	if len(ret) == 0 {
		panic("no return value specified for CreateTemplate")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.AiStudioTemplate) error); ok {
		r0 = rf(ctx, template)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteTask provides a mock function with given fields: ctx, id
func (_m *ReadWriter) DeleteTask(ctx context.Context, id int64) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteTask")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteTaskLog provides a mock function with given fields: ctx, id
func (_m *ReadWriter) DeleteTaskLog(ctx context.Context, id int64) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteTaskLog")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteTemplate provides a mock function with given fields: ctx, id
func (_m *ReadWriter) DeleteTemplate(ctx context.Context, id int64) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteTemplate")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetAiStudioCronJob provides a mock function with given fields: ctx, jobID
func (_m *ReadWriter) GetAiStudioCronJob(ctx context.Context, jobID string) (*entity.AiStudioCronJob, error) {
	ret := _m.Called(ctx, jobID)

	if len(ret) == 0 {
		panic("no return value specified for GetAiStudioCronJob")
	}

	var r0 *entity.AiStudioCronJob
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*entity.AiStudioCronJob, error)); ok {
		return rf(ctx, jobID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *entity.AiStudioCronJob); ok {
		r0 = rf(ctx, jobID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.AiStudioCronJob)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, jobID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetTask provides a mock function with given fields: ctx, id
func (_m *ReadWriter) GetTask(ctx context.Context, id int64) (*entity.AiStudioTask, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetTask")
	}

	var r0 *entity.AiStudioTask
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*entity.AiStudioTask, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *entity.AiStudioTask); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.AiStudioTask)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetTaskByName provides a mock function with given fields: ctx, name
func (_m *ReadWriter) GetTaskByName(ctx context.Context, name string) (*entity.AiStudioTask, error) {
	ret := _m.Called(ctx, name)

	if len(ret) == 0 {
		panic("no return value specified for GetTaskByName")
	}

	var r0 *entity.AiStudioTask
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*entity.AiStudioTask, error)); ok {
		return rf(ctx, name)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *entity.AiStudioTask); ok {
		r0 = rf(ctx, name)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.AiStudioTask)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, name)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetTaskLog provides a mock function with given fields: ctx, id
func (_m *ReadWriter) GetTaskLog(ctx context.Context, id int64) (*entity.AiStudioTaskLog, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetTaskLog")
	}

	var r0 *entity.AiStudioTaskLog
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*entity.AiStudioTaskLog, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *entity.AiStudioTaskLog); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.AiStudioTaskLog)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetTasks provides a mock function with given fields: ctx, searchBean
func (_m *ReadWriter) GetTasks(ctx context.Context, searchBean *entity.AiStudioTask) ([]*entity.AiStudioTask, error) {
	ret := _m.Called(ctx, searchBean)

	if len(ret) == 0 {
		panic("no return value specified for GetTasks")
	}

	var r0 []*entity.AiStudioTask
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.AiStudioTask) ([]*entity.AiStudioTask, error)); ok {
		return rf(ctx, searchBean)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entity.AiStudioTask) []*entity.AiStudioTask); ok {
		r0 = rf(ctx, searchBean)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entity.AiStudioTask)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entity.AiStudioTask) error); ok {
		r1 = rf(ctx, searchBean)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetTemplate provides a mock function with given fields: ctx, id
func (_m *ReadWriter) GetTemplate(ctx context.Context, id int64) (*entity.AiStudioTemplate, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetTemplate")
	}

	var r0 *entity.AiStudioTemplate
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*entity.AiStudioTemplate, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *entity.AiStudioTemplate); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.AiStudioTemplate)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListAiStudioCronJobs provides a mock function with given fields: ctx, pageSize, currentPage
func (_m *ReadWriter) ListAiStudioCronJobs(ctx context.Context, pageSize int32, currentPage int32) ([]*entity.AiStudioCronJob, int32, error) {
	ret := _m.Called(ctx, pageSize, currentPage)

	if len(ret) == 0 {
		panic("no return value specified for ListAiStudioCronJobs")
	}

	var r0 []*entity.AiStudioCronJob
	var r1 int32
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, int32, int32) ([]*entity.AiStudioCronJob, int32, error)); ok {
		return rf(ctx, pageSize, currentPage)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int32, int32) []*entity.AiStudioCronJob); ok {
		r0 = rf(ctx, pageSize, currentPage)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entity.AiStudioCronJob)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int32, int32) int32); ok {
		r1 = rf(ctx, pageSize, currentPage)
	} else {
		r1 = ret.Get(1).(int32)
	}

	if rf, ok := ret.Get(2).(func(context.Context, int32, int32) error); ok {
		r2 = rf(ctx, pageSize, currentPage)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// ListTaskLogs provides a mock function with given fields: ctx, pageSize, currentPage
func (_m *ReadWriter) ListTaskLogs(ctx context.Context, pageSize int32, currentPage int32) ([]*entity.AiStudioTaskLog, int32, error) {
	ret := _m.Called(ctx, pageSize, currentPage)

	if len(ret) == 0 {
		panic("no return value specified for ListTaskLogs")
	}

	var r0 []*entity.AiStudioTaskLog
	var r1 int32
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, int32, int32) ([]*entity.AiStudioTaskLog, int32, error)); ok {
		return rf(ctx, pageSize, currentPage)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int32, int32) []*entity.AiStudioTaskLog); ok {
		r0 = rf(ctx, pageSize, currentPage)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entity.AiStudioTaskLog)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int32, int32) int32); ok {
		r1 = rf(ctx, pageSize, currentPage)
	} else {
		r1 = ret.Get(1).(int32)
	}

	if rf, ok := ret.Get(2).(func(context.Context, int32, int32) error); ok {
		r2 = rf(ctx, pageSize, currentPage)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// ListTasks provides a mock function with given fields: ctx, pageSize, currentPage
func (_m *ReadWriter) ListTasks(ctx context.Context, pageSize int32, currentPage int32) ([]*entity.AiStudioTask, int32, error) {
	ret := _m.Called(ctx, pageSize, currentPage)

	if len(ret) == 0 {
		panic("no return value specified for ListTasks")
	}

	var r0 []*entity.AiStudioTask
	var r1 int32
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, int32, int32) ([]*entity.AiStudioTask, int32, error)); ok {
		return rf(ctx, pageSize, currentPage)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int32, int32) []*entity.AiStudioTask); ok {
		r0 = rf(ctx, pageSize, currentPage)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entity.AiStudioTask)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int32, int32) int32); ok {
		r1 = rf(ctx, pageSize, currentPage)
	} else {
		r1 = ret.Get(1).(int32)
	}

	if rf, ok := ret.Get(2).(func(context.Context, int32, int32) error); ok {
		r2 = rf(ctx, pageSize, currentPage)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// ListTemplate provides a mock function with given fields: ctx, pageSize, currentPage
func (_m *ReadWriter) ListTemplate(ctx context.Context, pageSize int32, currentPage int32) ([]*entity.AiStudioTemplate, int32, error) {
	ret := _m.Called(ctx, pageSize, currentPage)

	if len(ret) == 0 {
		panic("no return value specified for ListTemplate")
	}

	var r0 []*entity.AiStudioTemplate
	var r1 int32
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, int32, int32) ([]*entity.AiStudioTemplate, int32, error)); ok {
		return rf(ctx, pageSize, currentPage)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int32, int32) []*entity.AiStudioTemplate); ok {
		r0 = rf(ctx, pageSize, currentPage)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entity.AiStudioTemplate)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int32, int32) int32); ok {
		r1 = rf(ctx, pageSize, currentPage)
	} else {
		r1 = ret.Get(1).(int32)
	}

	if rf, ok := ret.Get(2).(func(context.Context, int32, int32) error); ok {
		r2 = rf(ctx, pageSize, currentPage)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// UpdateTask provides a mock function with given fields: ctx, task
func (_m *ReadWriter) UpdateTask(ctx context.Context, task *entity.AiStudioTask) error {
	ret := _m.Called(ctx, task)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTask")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.AiStudioTask) error); ok {
		r0 = rf(ctx, task)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateTemplate provides a mock function with given fields: ctx, template
func (_m *ReadWriter) UpdateTemplate(ctx context.Context, template *entity.AiStudioTemplate) error {
	ret := _m.Called(ctx, template)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTemplate")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.AiStudioTemplate) error); ok {
		r0 = rf(ctx, template)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewReadWriter creates a new instance of ReadWriter. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewReadWriter(t interface {
	mock.TestingT
	Cleanup(func())
}) *ReadWriter {
	mock := &ReadWriter{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
