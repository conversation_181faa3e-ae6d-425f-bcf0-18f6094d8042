package aistudio

import (
	"testing"
	"time"
)

// TestStringIDCron 测试使用字符串ID的cron任务
func TestStringIDCron(t *testing.T) {
	c := NewStringIDCron()

	// 启动调度器
	c.Start()
	defer c.Stop()

	// 添加一个带有字符串ID的任务
	version := time.Now()
	err := c.AddFunc("id_task_my_log", version, "@every 1s", func() {
		// 任务逻辑
	})

	if err != nil {
		t.Fatalf("添加任务失败: %v", err)
	}

	// 验证任务是否被添加
	if entry, exists := c.idMapper["id_task_my_log"]; !exists {
		t.Errorf("期望任务被添加，但未找到")
	} else if entry.Version != version {
		t.Errorf("期望版本为%v，但得到%v", version, entry.Version)
	}

	// 测试Entries方法
	entries := c.Entries()
	if len(entries) != 1 {
		t.<PERSON><PERSON><PERSON>("期望有1个任务条目，但得到%d个", len(entries))
	}

	// 测试移除任务
	removed := c.<PERSON>mo<PERSON>("id_task_my_log")
	if !removed {
		t.<PERSON><PERSON><PERSON>("期望任务被移除，但移除失败")
	}

	// 验证任务已被移除
	entries = c.Entries()
	if len(entries) != 0 {
		t.Errorf("期望任务被移除后没有条目，但得到%d个", len(entries))
	}

	// 测试移除不存在的任务
	removed = c.Remove("non_existent_id")
	if removed {
		t.Errorf("期望移除不存在的任务返回false，但返回了true")
	}
}

// TestStringIDCronVersioning 测试使用字符串ID的cron任务版本控制
func TestStringIDCronVersioning(t *testing.T) {
	c := NewStringIDCron()

	// 启动调度器
	c.Start()
	defer c.Stop()

	initialVersion := time.Now()
	newerVersion := initialVersion.Add(time.Hour)
	olderVersion := initialVersion.Add(-time.Hour)

	// 添加一个带有字符串ID的任务
	err := c.AddFunc("version_test_task", initialVersion, "@every 1s", func() {
		// 任务逻辑
	})

	if err != nil {
		t.Fatalf("添加任务失败: %v", err)
	}

	// 验证任务是否被添加
	if entry, exists := c.idMapper["version_test_task"]; !exists {
		t.Errorf("期望任务被添加，但未找到")
	} else if entry.Version != initialVersion {
		t.Errorf("期望版本为%v，但得到%v", initialVersion, entry.Version)
	}

	// 尝试用更旧的版本更新任务，应该不会更新
	err = c.AddFunc("version_test_task", olderVersion, "@every 1s", func() {
		// 新的任务逻辑
	})

	if err != nil {
		t.Fatalf("更新任务失败: %v", err)
	}

	// 验证任务版本没有改变
	if entry, exists := c.idMapper["version_test_task"]; !exists {
		t.Errorf("期望任务存在，但未找到")
	} else if entry.Version != initialVersion {
		t.Errorf("期望版本仍为%v，但得到%v", initialVersion, entry.Version)
	}

	// 用更新的版本更新任务，应该会更新
	err = c.AddFunc("version_test_task", newerVersion, "@every 1s", func() {
		// 更新的任务逻辑
	})

	if err != nil {
		t.Fatalf("更新任务失败: %v", err)
	}

	// 验证任务版本已更新
	if entry, exists := c.idMapper["version_test_task"]; !exists {
		t.Errorf("期望任务存在，但未找到")
	} else if entry.Version != newerVersion {
		t.Errorf("期望版本为%v，但得到%v", newerVersion, entry.Version)
	}
}
