package aistudio

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

// MCPClientInterface defines the interface for MCPClient to allow mocking
type MCPClientInterface interface {
	Send2LLM(ctx context.Context) (*GeminiResp, error)
}

var ModelsMapping = map[string]string{
	"gemini-2.0-flash":       "gemini-2.0-flash-001",
	"gemini-2.5-flash":       "gemini-2.5-flash",
	"gemini-2.5-pro":         "gemini-2.5-pro-preview-05-06",
	"moego-gemini-2.5-flash": "gemini-2.5-flash",
}

func NewMCPClient(modelType,
	initPrompt, apiKey string, mcpServersConfig map[string]*MCPConfig) MCPClientInterface {
	realModelType := ModelsMapping[modelType]
	ret := &MCPClient{
		ModelType:        realModelType,
		InitPrompt:       initPrompt,
		McpServersConfig: mcpServersConfig,
		APIKey:           apiKey,
		useOpenAI:        false,
	}
	if strings.HasPrefix(modelType, "moego-") {
		ret.BaseURL = "https://llmproxy-dev.devops.moego.pet/v1"
		ret.useOpenAI = true
	}

	return ret
}

type MCPClient struct {
	ModelType        string                `json:"model_type"`
	InitPrompt       string                `json:"init_prompt"`
	McpServersConfig map[string]*MCPConfig `json:"mcp_servers_config"`
	APIKey           string                `json:"api_key"`

	//     "base_url": "https://llmproxy-dev.devops.moego.pet/v1"
	BaseURL string `json:"base_url"`

	useOpenAI bool `json:"-"`
}

type GeminiResp struct {
	Result    string   `json:"result"`
	Dialogues []string `json:"dialogues"`
}

func (m *MCPClient) Send2LLM(ctx context.Context) (geminiResp *GeminiResp, err error) {
	url := "http://moego-gemini-mcp-client:8080/api/v1/gemini_mcp"

	if m.useOpenAI {
		url = "http://moego-gemini-mcp-client:8080/api/v1/openai_mcp"
	}

	jsonData, _ := json.Marshal(m)
	log.InfoContextf(ctx, "Send2LLM Req %s", jsonData)

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")

	client := http.DefaultClient
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	respBts, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	geminiResp = new(GeminiResp)
	if err := json.Unmarshal(respBts, geminiResp); err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	log.InfoContextf(ctx, "mcp client response: %s", geminiResp)

	return geminiResp, nil
}
