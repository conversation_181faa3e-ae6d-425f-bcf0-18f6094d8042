package watcher

import (
	"fmt"
	"sync/atomic"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/configloader"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/global"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/datadog"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/jira"
)

func TestJiraIncidentEvaluator_EvaluateBug_DedicateWorking(t *testing.T) {
	dedicateTime := time.Date(2025, time.July, 8, 10, 0, 0, 0, time.FixedZone("Asia/Shanghai", 8*60*60))
	oncallTime := time.Date(2025, time.July, 8, 15, 0, 0, 0, time.FixedZone("Asia/Shanghai", 8*60*60))

	mockConfig := &configloader.CSPageConfig{
		ComponentsSquadsMapping: map[string]string{
			"Workflow":      global.SquadCRM,
			"System access": "Infra",
			"Staff":         "HR",
		},
		RefUser: []string{"<EMAIL>"},
	}
	var mockCfg atomic.Pointer[configloader.CSPageConfig]
	mockCfg.Store(mockConfig)

	tests := []struct {
		name        string
		issue       *jira.Issue
		mockTime    time.Time
		expectedInc *datadog.Incident
		expectedErr error
	}{
		{
			name: "P0 T1 GoLive - Dedicate Time",
			issue: &jira.Issue{
				IssuePriority: global.PriorityP0,
				T1OrGeneral:   global.TierT1,
				CustomerStage: global.CustomerStageGoLive,
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			mockTime: dedicateTime,
			expectedInc: &datadog.Incident{
				OncallTeam:         global.TeamDedicate,
				IssuePriority:      global.PriorityP0,
				Tier:               global.TierT1,
				CustomerStage:      global.CustomerStageGoLive,
				NeedCreateIncident: true,
				NeedT1Slack:        true,
				NeedBuzzAssignee:   false,
				RefUsers:           []string{"<EMAIL>", "<EMAIL>", "<EMAIL>"},
			},
			expectedErr: nil,
		},
		{
			name: "P0 T1 GoLive - Oncall Time",
			issue: &jira.Issue{
				IssuePriority: global.PriorityP0,
				T1OrGeneral:   global.TierT1,
				CustomerStage: global.CustomerStageGoLive,
				Components:    []string{"Workflow"},
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			mockTime: oncallTime,
			expectedInc: &datadog.Incident{
				OncallTeam:         global.SquadCRM,
				IssuePriority:      global.PriorityP0,
				Tier:               global.TierT1,
				CustomerStage:      global.CustomerStageGoLive,
				NeedCreateIncident: true,
				NeedT1Slack:        true,
				NeedBuzzAssignee:   false,
				RefUsers:           []string{"<EMAIL>", "<EMAIL>", "<EMAIL>"},
			},
			expectedErr: nil,
		},
		{
			name: "P0 T1 PostLive without component - Oncall Time (Error Expected)",
			issue: &jira.Issue{
				IssuePriority: global.PriorityP0,
				T1OrGeneral:   global.TierT1,
				CustomerStage: global.CustomerStagePostLive,
				Components:    []string{},
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			mockTime:    oncallTime,
			expectedInc: nil,
			expectedErr: fmt.Errorf("no team found for issue []"),
		},
		{
			name: "P1 T1 GoLive with component - Dedicate Time",
			issue: &jira.Issue{
				IssuePriority: global.PriorityP1,
				T1OrGeneral:   global.TierT1,
				CustomerStage: global.CustomerStageGoLive,
				Components:    []string{"System access"},
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			mockTime: dedicateTime,
			expectedInc: &datadog.Incident{
				OncallTeam:         global.TeamDedicate,
				IssuePriority:      global.PriorityP1,
				Tier:               global.TierT1,
				CustomerStage:      global.CustomerStageGoLive,
				NeedCreateIncident: false,
				NeedT1Slack:        true,
				NeedBuzzAssignee:   true,
				RefUsers:           []string{"<EMAIL>", "<EMAIL>", "<EMAIL>"},
			},
			expectedErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dedicateOnVacation = false
			evaluator := &JiraIncidentEvaluator{
				timeGetter:   func() time.Time { return tt.mockTime },
				csPageConfig: &mockCfg,
			}
			retIncident, err := evaluator.EvaluateBug(tt.issue)

			assert.Equal(t, tt.expectedErr, err)
			if tt.expectedInc == nil {
				assert.Nil(t, retIncident)
			} else {
				tt.expectedInc.Issue = tt.issue
				assert.ElementsMatch(t, tt.expectedInc.RefUsers, retIncident.RefUsers)
				retIncident.RefUsers = nil
				tt.expectedInc.RefUsers = nil
				assert.Equal(t, tt.expectedInc, retIncident)
			}
		})
	}
}

func TestJiraIncidentEvaluator_EvaluateBug_DedicateOnVacation(t *testing.T) {
	dedicateTime := time.Date(2025, time.July, 8, 10, 0, 0, 0, time.FixedZone("Asia/Shanghai", 8*60*60))
	oncallTime := time.Date(2025, time.July, 8, 15, 0, 0, 0, time.FixedZone("Asia/Shanghai", 8*60*60))

	mockConfig := &configloader.CSPageConfig{
		ComponentsSquadsMapping: map[string]string{
			"Workflow":      global.SquadCRM,
			"System access": "Infra",
			"Staff":         "HR",
		},
		RefUser: []string{"<EMAIL>"},
	}
	var mockCfg atomic.Pointer[configloader.CSPageConfig]
	mockCfg.Store(mockConfig)

	tests := []struct {
		name        string
		issue       *jira.Issue
		mockTime    time.Time
		expectedInc *datadog.Incident
		expectedErr error
	}{
		{
			name: "P0 T1 GoLive - Dedicate Time but on vacation",
			issue: &jira.Issue{
				IssuePriority: global.PriorityP0,
				T1OrGeneral:   global.TierT1,
				CustomerStage: global.CustomerStageGoLive,
				Components:    []string{"Workflow"},
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			mockTime: dedicateTime,
			expectedInc: &datadog.Incident{
				OncallTeam:         global.SquadCRM,
				IssuePriority:      global.PriorityP0,
				Tier:               global.TierT1,
				CustomerStage:      global.CustomerStageGoLive,
				NeedCreateIncident: true,
				NeedT1Slack:        true,
				NeedBuzzAssignee:   false,
				RefUsers:           []string{"<EMAIL>", "<EMAIL>", "<EMAIL>"},
			},
			expectedErr: nil,
		},
		{
			name: "P0 T1 GoLive - Oncall Time",
			issue: &jira.Issue{
				IssuePriority: global.PriorityP0,
				T1OrGeneral:   global.TierT1,
				CustomerStage: global.CustomerStageGoLive,
				Components:    []string{"Workflow"},
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			mockTime: oncallTime,
			expectedInc: &datadog.Incident{
				OncallTeam:         global.SquadCRM,
				IssuePriority:      global.PriorityP0,
				Tier:               global.TierT1,
				CustomerStage:      global.CustomerStageGoLive,
				NeedCreateIncident: true,
				NeedT1Slack:        true,
				NeedBuzzAssignee:   false,
				RefUsers:           []string{"<EMAIL>", "<EMAIL>", "<EMAIL>"},
			},
			expectedErr: nil,
		},
		{
			name: "P0 T1 PostLive without component - Oncall Time (Error Expected)",
			issue: &jira.Issue{
				IssuePriority: global.PriorityP0,
				T1OrGeneral:   global.TierT1,
				CustomerStage: global.CustomerStagePostLive,
				Components:    []string{},
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			mockTime:    oncallTime,
			expectedInc: nil,
			expectedErr: fmt.Errorf("no team found for issue []"),
		},
		{
			name: "P0 T1 PostLive without component - Dedicate Time but on vacation (Error Expected)",
			issue: &jira.Issue{
				IssuePriority: global.PriorityP0,
				T1OrGeneral:   global.TierT1,
				CustomerStage: global.CustomerStagePostLive,
				Components:    []string{},
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			mockTime:    dedicateTime,
			expectedInc: nil,
			expectedErr: fmt.Errorf("no team found for issue []"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dedicateOnVacation = true
			evaluator := &JiraIncidentEvaluator{
				timeGetter:   func() time.Time { return tt.mockTime },
				csPageConfig: &mockCfg,
			}
			retIncident, err := evaluator.EvaluateBug(tt.issue)

			assert.Equal(t, tt.expectedErr, err)
			if tt.expectedInc == nil {
				assert.Nil(t, retIncident)
			} else {
				tt.expectedInc.Issue = tt.issue
				assert.ElementsMatch(t, tt.expectedInc.RefUsers, retIncident.RefUsers)
				retIncident.RefUsers = nil
				tt.expectedInc.RefUsers = nil
				assert.Equal(t, tt.expectedInc, retIncident)
			}
		})
	}
}

func TestJiraIncidentEvaluator_evaluateDedicateOrOncall_Working(t *testing.T) {
	mockConfig := &configloader.CSPageConfig{
		ComponentsSquadsMapping: map[string]string{
			"Workflow":      global.SquadCRM,
			"System access": "Infra",
			"Staff":         "HR",
		},
		RefUser: []string{"<EMAIL>"},
	}
	var mockCfg atomic.Pointer[configloader.CSPageConfig]
	mockCfg.Store(mockConfig)

	evaluator := &JiraIncidentEvaluator{
		csPageConfig: &mockCfg,
	}

	tests := []struct {
		name          string
		issue         *jira.Issue
		mockTime      time.Time
		expectedTeam  string
		expectedError bool
	}{
		{
			name:         "Dedicate Time - 10 AM Beijing",
			issue:        &jira.Issue{Components: []string{"Workflow"}},
			mockTime:     time.Date(2025, time.July, 8, 10, 0, 0, 0, time.FixedZone("Asia/Shanghai", 8*60*60)),
			expectedTeam: global.TeamDedicate,
		},
		{
			name:         "Oncall Time - 3 PM Beijing",
			issue:        &jira.Issue{Components: []string{"Workflow"}},
			mockTime:     time.Date(2025, time.July, 8, 15, 0, 0, 0, time.FixedZone("Asia/Shanghai", 8*60*60)),
			expectedTeam: global.SquadCRM,
		},
		{
			name:          "Oncall Time - No Component Match",
			issue:         &jira.Issue{Components: []string{"Unknown Component"}},
			mockTime:      time.Date(2025, time.July, 8, 15, 0, 0, 0, time.FixedZone("Asia/Shanghai", 8*60*60)),
			expectedTeam:  "",
			expectedError: true,
		},
		{
			name:         "Dedicate Time - 12 AM Beijing",
			issue:        &jira.Issue{Components: []string{"Workflow"}},
			mockTime:     time.Date(2025, time.July, 8, 0, 0, 0, 0, time.FixedZone("Asia/Shanghai", 8*60*60)),
			expectedTeam: global.TeamDedicate,
		},
		{
			name:         "Oncall Time - 2 PM Beijing",
			issue:        &jira.Issue{Components: []string{"Workflow"}},
			mockTime:     time.Date(2025, time.July, 8, 14, 0, 0, 0, time.FixedZone("Asia/Shanghai", 8*60*60)),
			expectedTeam: global.SquadCRM,
		},
		{
			name:         "Oncall Time - 1:59 PM Beijing",
			issue:        &jira.Issue{Components: []string{"Workflow"}},
			mockTime:     time.Date(2025, time.July, 8, 13, 59, 0, 0, time.FixedZone("Asia/Shanghai", 8*60*60)),
			expectedTeam: global.TeamDedicate,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dedicateOnVacation = false
			team, err := evaluator.evaluateDedicateOrOncall(tt.issue, tt.mockTime)
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedTeam, team)
			}
		})
	}
}

func TestJiraIncidentEvaluator_evaluateDedicateOrOncall_OnVacation(t *testing.T) {
	mockConfig := &configloader.CSPageConfig{
		ComponentsSquadsMapping: map[string]string{
			"Workflow":      global.SquadCRM,
			"System access": "Infra",
			"Staff":         "HR",
		},
		RefUser: []string{"<EMAIL>"},
	}
	var mockCfg atomic.Pointer[configloader.CSPageConfig]
	mockCfg.Store(mockConfig)

	evaluator := &JiraIncidentEvaluator{
		csPageConfig: &mockCfg,
	}

	tests := []struct {
		name          string
		issue         *jira.Issue
		mockTime      time.Time
		expectedTeam  string
		expectedError bool
	}{
		{
			name:         "Dedicate Time - 10 AM Beijing - On Vacation",
			issue:        &jira.Issue{Components: []string{"Workflow"}},
			mockTime:     time.Date(2025, time.July, 8, 10, 0, 0, 0, time.FixedZone("Asia/Shanghai", 8*60*60)),
			expectedTeam: global.SquadCRM,
		},
		{
			name:         "Oncall Time - 3 PM Beijing - On Vacation",
			issue:        &jira.Issue{Components: []string{"Workflow"}},
			mockTime:     time.Date(2025, time.July, 8, 15, 0, 0, 0, time.FixedZone("Asia/Shanghai", 8*60*60)),
			expectedTeam: global.SquadCRM,
		},
		{
			name:          "Oncall Time - No Component Match - On Vacation",
			issue:         &jira.Issue{Components: []string{"Unknown Component"}},
			mockTime:      time.Date(2025, time.July, 8, 15, 0, 0, 0, time.FixedZone("Asia/Shanghai", 8*60*60)),
			expectedTeam:  "",
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dedicateOnVacation = true
			team, err := evaluator.evaluateDedicateOrOncall(tt.issue, tt.mockTime)
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedTeam, team)
			}
		})
	}
}

// 新增 RefUser 为空 slice 且 Components 不为空的场景
func TestJiraIncidentEvaluator_EvaluateBug_RefUserNil(t *testing.T) {
	dedicateTime := time.Date(2025, time.July, 8, 10, 0, 0, 0, time.FixedZone("Asia/Shanghai", 8*60*60))
	mockConfig := &configloader.CSPageConfig{
		ComponentsSquadsMapping: map[string]string{
			"Workflow": global.SquadCRM,
		},
		RefUser: []string{}, // 关键：RefUser为空slice
	}
	var mockCfg atomic.Pointer[configloader.CSPageConfig]
	mockCfg.Store(mockConfig)

	issue := &jira.Issue{
		IssuePriority: global.PriorityP0,
		T1OrGeneral:   global.TierT1,
		CustomerStage: global.CustomerStageGoLive,
		Components:    []string{"Workflow"},
		Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
		Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
	}
	evaluator := &JiraIncidentEvaluator{
		timeGetter:   func() time.Time { return dedicateTime },
		csPageConfig: &mockCfg,
	}
	retIncident, err := evaluator.EvaluateBug(issue)
	assert.NoError(t, err)
	assert.NotNil(t, retIncident)
	assert.ElementsMatch(t, []string{"<EMAIL>", "<EMAIL>"}, retIncident.RefUsers)
}
