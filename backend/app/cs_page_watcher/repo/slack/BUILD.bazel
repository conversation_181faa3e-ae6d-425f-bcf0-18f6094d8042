load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "slack",
    srcs = ["slack.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/slack",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/log",
        "@com_github_slack_go_slack//:slack",
    ],
)

go_test(
    name = "slack_test",
    srcs = ["slack_test.go"],
    embed = [":slack"],
    deps = [
        "//backend/app/cs_page_watcher/configloader",
        "//backend/app/cs_page_watcher/global",
    ],
)
