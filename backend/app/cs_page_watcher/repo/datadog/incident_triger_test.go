package datadog

import (
	"context"
	"fmt"
	"os"
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/configloader"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/global"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/entity"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/jira"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/slack"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/config/nacos"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/recovery"
	rpc "github.com/MoeGolibrary/moego/backend/common/rpc/framework"
)

var setupTestSvc sync.Once

func setUp() {
	setupTestSvc.Do(func() {
		currentDir, _ := os.Getwd()
		fmt.Printf("currentDir:%v\n", currentDir)
		currentDir = currentDir[:len(currentDir)-len("repo/datadog")]
		rpc.ServerConfigPath = currentDir + "config/local/config.yaml"
		_ = rpc.NewServer()
	})
}

func TestPageATeam(t *testing.T) {
	t.Skip("manual test")
	setUp()
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	d := &IncidentGatewayImpl{
		slackClient:        slack.NewClient(cfg.CsPageWatcher.SlackToken),
		cfg:                cfg,
		csPageReaderWriter: entity.NewCsPageReaderWriter(),
	}
	err := d.pageATeam(context.Background(),
		cfg.CsPageWatcher.DatadogAPIKey, cfg.CsPageWatcher.DatadogAPPKey, "test page", "oncall-test")
	assert.Nil(t, err)
}

func TestTriggerIncident(t *testing.T) {
	t.Skip("manual test")
	setUp()
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	d := NewIncidentGateway(cfg,
		slack.NewClient(cfg.CsPageWatcher.SlackToken),
		entity.NewCsPageReaderWriter(),
	)
	jiraClient, err := jira.NewIssueRepository(cfg.CsPageWatcher.JiraEmail, cfg.CsPageWatcher.JiraToken)
	assert.Nil(t, err)

	issue, err := jiraClient.GetIssueDetails("IFRBE-2054")
	assert.Nil(t, err)

	err = d.TriggerIncident(context.Background(), &Incident{
		IssuePriority:      "P0-Block",
		Tier:               "T1 Ticket",
		CustomerStage:      "Go-live Day",
		NeedCreateIncident: true,
		OncallTeam:         "ZihaoTest",
		NeedT1Slack:        true,
		RefUsers: []string{
			issue.Assignee.EmailAddress,
			issue.Reporter.EmailAddress,
			"<EMAIL>",
		},
		Issue: issue,
	})
	assert.Nil(t, err)
}

func TestDatadogIncidentGatewayImpl_oncallTeam2Commander(t *testing.T) {
	t.Skip("manual test")
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	_ = cfg // Silence unused variable warning
	d := &IncidentGatewayImpl{}
	for team := range global.TeamSchedules {
		responder, err := d.getCommanderByTaskType(
			cfg.CsPageWatcher.DatadogAPIKey, cfg.CsPageWatcher.DatadogAPPKey, team, global.OncallTaskType)
		assert.Nil(t, err)
		assert.NotEqual(t, "", responder.commanderID)
		if team == global.SquadCRM {
			assert.Equal(t, "be-crm", responder.teamHandle)
		}
		t.Logf("team:%v gotCommanderID: %v, gotCommanderEmail:%v, teamHandle:%v",
			team, responder.commanderID, responder.commanderEmail, responder.teamHandle)
	}
}

func TestDatadogIncidentGatewayImpl_admintaskTeam2Commander(t *testing.T) {
	t.Skip("manual test")
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	_ = cfg // Silence unused variable warning
	d := &IncidentGatewayImpl{}
	for team := range global.TeamSchedules {
		responder, err := d.getCommanderByTaskType(
			cfg.CsPageWatcher.DatadogAPIKey, cfg.CsPageWatcher.DatadogAPPKey, team, global.AdminTaskType)
		assert.Nil(t, err)
		assert.NotEqual(t, "", responder.commanderID)
		if team == global.SquadCRM {
			assert.Equal(t, "be-crm", responder.teamHandle)
		}
		t.Logf("team:%v gotCommanderID: %v, gotCommanderEmail:%v, teamHandle:%v",
			team, responder.commanderID, responder.commanderEmail, responder.teamHandle)
	}
}

func TestDatadogIncidentGatewayImpl_GetCommander(t *testing.T) {
	t.Skip("manual test")
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	d := &IncidentGatewayImpl{cfg: cfg}

	// Test oncall type
	for team := range global.TeamSchedules {
		commanderEmail, err := d.GetCommander(team, global.OncallTaskType)
		assert.Nil(t, err)
		assert.NotEqual(t, "", commanderEmail)
		t.Logf("team:%v commanderEmail:%v", team, commanderEmail)
	}

	// Test adminTask type
	for team := range global.TeamSchedules {
		commanderEmail, err := d.GetCommander(team, global.AdminTaskType)
		assert.Nil(t, err)
		assert.NotEqual(t, "", commanderEmail)
		t.Logf("team:%v commanderEmail:%v", team, commanderEmail)
	}
}

func TestIncidentGatewayImpl_createIncident(t *testing.T) {
	t.Skip("manual test")
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	userID := "c65695fb-b21b-11ef-91b2-dee2953e9407" // zihao
	gotPublicID, err := createIncident(cfg.CsPageWatcher.DatadogAPIKey, cfg.CsPageWatcher.DatadogAPPKey,
		userID, "be-platform", "page-test-incident", "this is a test incident")
	if err != nil {
		t.Fatalf("TestIncidentGatewayImpl_createIncident err:%v", err)
		return
	}
	t.Logf("gotPublicID:%v", gotPublicID)
}

func TestIncidentGatewayImpl_GetRootCause(t *testing.T) {
	t.Skip("manual test")
	setUp()
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	d := &IncidentGatewayImpl{
		slackClient:        slack.NewClient(cfg.CsPageWatcher.SlackToken),
		cfg:                cfg,
		csPageReaderWriter: entity.NewCsPageReaderWriter(),
	}

	// Using a real incident ID for testing
	incidentID := "110"
	rootCause, err := d.GetRootCause(context.Background(), incidentID)
	if err != nil {
		t.Fatalf("TestIncidentGatewayImpl_GetRootCause err:%v", err)
		return
	}

	t.Logf("Incident ID: %s, Root Cause: %s", incidentID, rootCause)
	// Assert that we get a non-empty root cause
	assert.NotEmpty(t, rootCause, "Root cause should not be empty")
	// For incident 110, we expect a specific root cause
	assert.Equal(t, "代码中有一段逻辑没有兼容这个 book by pet family 白名单，导致不符合预期。", rootCause)
}

func Test_getInitSlackMsg(t *testing.T) {
	type args struct {
		incident *Incident
		devEmail string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "test with a P1 incident",
			args: args{
				incident: &Incident{
					IssuePriority: "P1-High",
					Issue: &jira.Issue{
						Key:              "TEST-123",
						Summary:          "Database connection issues",
						LogoName:         "TestLogo",
						IssueDescription: "Database is not responding",
						CreatedByCustom:  "<EMAIL>",
						Reporter: jira.UserInfo{
							EmailAddress: "<EMAIL>",
						},
						Assignee: jira.UserInfo{
							EmailAddress: "<EMAIL>",
						},
					},
				},
				devEmail: "<EMAIL>",
			},
			want: "Ticket: https://moego.atlassian.net/browse/TEST-123\nSummary: Database connection issues\nLogo: TestLogo\nIssue Description: Database is not responding\nCreated By: <EMAIL>\nSubmitted by(Jira Reporter):<EMAIL>\nPriority: P1-High\nQA Owner(Jira Assignee): <EMAIL>\nEng Owner: <EMAIL>",
		},
		{
			name: "test with empty logo name",
			args: args{
				incident: &Incident{
					IssuePriority: "P2-Medium",
					Issue: &jira.Issue{
						Key:              "TEST-456",
						Summary:          "UI rendering issue",
						LogoName:         "", // Empty logo name
						IssueDescription: "UI elements are not rendering correctly",
						CreatedByCustom:  "<EMAIL>",
						Reporter: jira.UserInfo{
							EmailAddress: "<EMAIL>",
						},
						Assignee: jira.UserInfo{
							EmailAddress: "<EMAIL>",
						},
					},
				},
				devEmail: "<EMAIL>",
			},
			want: "Ticket: https://moego.atlassian.net/browse/TEST-456\nSummary: UI rendering issue\nIssue Description: UI elements are not rendering correctly\nCreated By: <EMAIL>\nSubmitted by(Jira Reporter):<EMAIL>\nPriority: P2-Medium\nQA Owner(Jira Assignee): <EMAIL>\nEng Owner: <EMAIL>",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := getInitSlackMsg(tt.args.incident, tt.args.devEmail)
			if got != tt.want {
				t.Errorf("getInitSlackMsg() = %v, want %v", got, tt.want)
			}
			t.Log(got)
		})
	}
}
