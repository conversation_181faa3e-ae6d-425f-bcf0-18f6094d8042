package service

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/sales/logic/sales"
	salesutils "github.com/MoeGolibrary/moego/backend/app/sales/utils"
	salespb "github.com/MoeGolibrary/moego/backend/proto/sales/v1"
)

type SalesService struct {
	salespb.UnimplementedSalesServiceServer
	l *sales.Logic
}

func NewSalesService() *SalesService {
	return &SalesService{
		l: sales.NewLogic(),
	}
}

func (s *SalesService) SyncOpportunity(ctx context.Context, req *salespb.SyncOpportunityRequest) (
	*salespb.SyncOpportunityResponse, error) {
	err := s.l.SyncOpportunity(ctx, &sales.OpportunitySyncParams{
		ID:                    req.OpportunityId,
		Email:                 req.Email,
		Tier:                  req.Tier,
		TerminalPercentage:    salesutils.DecimalToString(req.TerminalPercentage),
		TerminalFixed:         salesutils.DecimalToString(req.TerminalFixed),
		NonTerminalPercentage: salesutils.DecimalToString(req.NonTerminalPercentage),
		NonTerminalFixed:      salesutils.DecimalToString(req.NonTerminalFixed),
		MinVolume:             salesutils.DecimalToString(req.MinVolume),
		SPIF:                  salesutils.DecimalToString(req.Spif),
	})
	if err != nil {
		return nil, err
	}

	return &salespb.SyncOpportunityResponse{}, nil
}

func (s *SalesService) SyncSalesSubscription(ctx context.Context, req *salespb.SyncSalesSubscriptionRequest) (
	*salespb.SyncSalesSubscriptionResponse, error) {
	err := s.l.SyncSalesSubscription(ctx, req)
	if err != nil {
		return nil, err
	}

	return &salespb.SyncSalesSubscriptionResponse{}, nil
}

func (s *SalesService) SyncSalesHardware(ctx context.Context, req *salespb.SyncSalesHardwareRequest) (
	*salespb.SyncSalesHardwareResponse, error) {
	err := s.l.SyncSalesHardware(ctx, req)
	if err != nil {
		return nil, err
	}

	return &salespb.SyncSalesHardwareResponse{}, nil
}

func (s *SalesService) CheckOpportunityExists(ctx context.Context, req *salespb.CheckOpportunityExistsRequest) (
	*salespb.CheckOpportunityExistsResponse, error) {
	exists := s.l.CheckOpportunityExists(ctx, req.OpportunityId)

	return &salespb.CheckOpportunityExistsResponse{
		Exists: exists,
	}, nil
}
