package service

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/sales/logic/sales"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	salespb "github.com/MoeGolibrary/moego/backend/proto/sales/v1"
)

type PlatformSalesService struct {
	salespb.UnimplementedPlatformSalesServiceServer
	l *sales.PlatformSalesLogic
}

func NewPlatformSalesService() *PlatformSalesService {
	return &PlatformSalesService{
		l: sales.NewPlatformSalesLogic(),
	}
}

func (s *PlatformSalesService) SyncPlatformSales(ctx context.Context, req *salespb.SyncPlatformSalesRequest) (
	*salespb.SyncPlatformSalesResponse, error) {
	go func() {
		salesCode := req.GetSalesCode()
		if err := s.l.SyncPlatformSales(context.Background(), salesCode); err != nil {
			log.ErrorContextf(ctx, "failed to sync platform sales, sales_code: %s, err: %v", salesCode, err)
		}
	}()

	return &salespb.SyncPlatformSalesResponse{}, nil
}
