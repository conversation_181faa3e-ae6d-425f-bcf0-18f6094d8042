package service

import (
	"context"

	"google.golang.org/protobuf/types/known/emptypb"

	"github.com/MoeGolibrary/moego/backend/app/sales/logic/sales"
	salespb "github.com/MoeGolibrary/moego/backend/proto/sales/v1"
)

type MoegoPayContractService struct {
	salespb.UnimplementedMoegoPayContractServiceServer
	c *sales.MoegoPayContractLogic
	a *sales.MoegoPayCustomFeeApprovalLogic
	s *sales.Logic
}

func NewMoegoPayContractService() *MoegoPayContractService {
	return &MoegoPayContractService{
		c: sales.NewMoegoPayContractLogic(),
		a: sales.NewMoegoPayCustomFeeApprovalLogic(),
		s: sales.NewLogic(),
	}
}

func (s *MoegoPayContractService) CreateMoegoPayContract(ctx context.Context,
	req *salespb.CreateMoegoPayContractRequest) (*salespb.MoegoPayContract, error) {
	return s.c.CreateMoegoPayContract(ctx, req)
}

func (s *MoegoPayContractService) GetMoegoPayContract(ctx context.Context, req *salespb.GetMoegoPayContractRequest) (
	*salespb.MoegoPayContract, error) {
	return s.c.GetMoegoPayContract(ctx, req.Id)
}

func (s *MoegoPayContractService) ListMoegoPayContracts(ctx context.Context,
	req *salespb.ListMoegoPayContractsRequest) (*salespb.ListMoegoPayContractsResponse, error) {
	return s.c.ListMoegoPayContracts(ctx, req)
}

func (s *MoegoPayContractService) CountMoegoPayContracts(ctx context.Context,
	req *salespb.CountMoegoPayContractsRequest) (*salespb.CountMoegoPayContractsResponse, error) {
	count, err := s.c.CountMoegoPayContracts(ctx, req)
	if err != nil {
		return nil, err
	}

	return &salespb.CountMoegoPayContractsResponse{
		Count: count,
	}, nil
}

func (s *MoegoPayContractService) SignMoegoPayContract(ctx context.Context, req *salespb.SignMoegoPayContractRequest) (
	*salespb.SignMoegoPayContractResponse, error) {
	// sign contract
	contract, err := s.c.SignMoegoPayContract(ctx, req)
	if err != nil {
		return nil, err
	}

	//// sync to salesforce
	//account, err := s.s.GetSalesforceAccountByEmail(ctx, contract.Parameters.OwnerEmail)
	//if err != nil {
	//	log.ErrorContextf(ctx, "failed to get salesforce account by email: %v", err)
	//} else {
	//	err = s.s.SyncContract(ctx, &sales.SalesforceContractSyncParams{
	//		SalesforceAccountID: *account.ID,
	//		SignTime:            contract.SignTime.AsTime(),
	//		Term:                pointer.Get(int32(12)),
	//		Type:                pointer.Get(sales.MoegoPayContractName),
	//		Link:                pointer.Get(s.c.GetMoegoPayContractLink(contract.GetId())),
	//	})
	//	if err != nil {
	//		// log and ignore error
	//		log.ErrorContextf(ctx, "failed to sync contract to salesforce: %v", err)
	//	}
	//}

	// create custom fee approval
	_, err = s.a.CreateMoegoPayCustomFeeApproval(ctx, &sales.MoegoPayCustomFeeApprovalCreateParams{
		AccountID:             contract.GetMetadata().GetAccountId(),
		CompanyID:             contract.GetMetadata().GetCompanyId(),
		OwnerEmail:            contract.GetParameters().GetOwnerEmail(),
		Creator:               contract.GetCreator(),
		TerminalPercentage:    contract.GetParameters().GetTerminalPercentage(),
		TerminalFixed:         contract.GetParameters().GetTerminalFixed(),
		NonTerminalPercentage: contract.GetParameters().GetNonTerminalPercentage(),
		NonTerminalFixed:      contract.GetParameters().GetNonTerminalFixed(),
		MinVolume:             contract.GetParameters().GetMinVolume(),
		ContractID:            &contract.Id,
	})
	if err != nil {
		return nil, err
	}

	return &salespb.SignMoegoPayContractResponse{
		Contract: contract,
	}, nil
}

func (s *MoegoPayContractService) DeleteMoegoPayContract(ctx context.Context,
	req *salespb.DeleteMoegoPayContractRequest) (*emptypb.Empty, error) {
	err := s.c.DeleteMoegoPayContract(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}
