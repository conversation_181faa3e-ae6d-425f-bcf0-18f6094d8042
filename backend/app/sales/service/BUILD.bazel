load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "service",
    srcs = [
        "annual_contract_service.go",
        "moego_pay_contract_service.go",
        "moego_pay_custom_fee_approval_service.go",
        "platform_sales_service.go",
        "sales_service.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/sales/service",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/sales/logic/sales",
        "//backend/app/sales/utils",
        "//backend/common/rpc/framework/log",
        "//backend/proto/sales/v1:sales",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/emptypb",
    ],
)
