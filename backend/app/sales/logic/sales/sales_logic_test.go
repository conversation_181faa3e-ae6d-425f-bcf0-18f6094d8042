package sales_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"github.com/MoeGolibrary/moego/backend/app/sales/logic/sales"
	salesmock "github.com/MoeGolibrary/moego/backend/app/sales/repo/mock/sales"
	salesforcemock "github.com/MoeGolibrary/moego/backend/app/sales/repo/mock/salesforce"
	salesrepo "github.com/MoeGolibrary/moego/backend/app/sales/repo/sales"
	"github.com/MoeGolibrary/moego/backend/app/sales/repo/salesforce"
	"github.com/MoeGolibrary/moego/backend/common/utils/pointer"
)

func TestCheckOpportunityExists(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockSalesforceClient := salesforcemock.NewMockClient(ctrl)
	logic := sales.NewByParams(nil, nil, nil, mockSalesforceClient)

	t.Run("exists", func(t *testing.T) {
		mockSalesforceClient.EXPECT().GetOpportunity(gomock.Any(), "exists_id").Return(&salesforce.Opportunity{}, nil)
		exists := logic.CheckOpportunityExists(context.Background(), "exists_id")
		assert.True(t, exists)
	})

	t.Run("not exists", func(t *testing.T) {
		mockSalesforceClient.EXPECT().GetOpportunity(gomock.Any(), "not_exists_id").Return(nil, errors.New("not found"))
		exists := logic.CheckOpportunityExists(context.Background(), "not_exists_id")
		assert.False(t, exists)
	})
}

func TestSyncOpportunity(t *testing.T) {
	ctrl := gomock.NewController(t)

	// 创建 mock 实现
	mockOpportunityRW := salesmock.NewMockOpportunityReadWriter(ctrl)
	mockSalesforceClient := salesforcemock.NewMockClient(ctrl)

	// 创建 Logic 实例
	logic := sales.NewByParams(mockOpportunityRW, nil, nil, mockSalesforceClient)

	t.Run("Test SyncOpportunity", func(t *testing.T) {

		opportunity := &salesrepo.Opportunity{
			ID: "id",
		}

		req := &sales.OpportunitySyncParams{
			ID:                    "id",
			Email:                 pointer.Get("<EMAIL>"),
			Tier:                  pointer.Get("T2"),
			TerminalPercentage:    pointer.Get("2.3"),
			TerminalFixed:         pointer.Get("0.5"),
			NonTerminalPercentage: pointer.Get("1.5"),
			NonTerminalFixed:      pointer.Get("0.1"),
			MinVolume:             pointer.Get("80000"),
			SPIF:                  pointer.Get("200"),
		}

		// 设置期望的调用
		mockOpportunityRW.EXPECT().Get(gomock.Any(), req.ID).Return(opportunity, nil)
		mockOpportunityRW.EXPECT().Save(gomock.Any(), gomock.Any()).Return(nil)
		mockSalesforceClient.EXPECT().GetOpportunity(gomock.Any(), req.ID).Return(opportunity.ToSalesforceEntity(), nil)
		mockSalesforceClient.EXPECT().UpdateOpportunity(gomock.Any(), gomock.Any()).Return(nil)

		err := logic.SyncOpportunity(context.Background(), req)
		assert.NoError(t, err)
	})
}

func TestSyncContract(t *testing.T) {
	ctrl := gomock.NewController(t)

	// 创建 mock 实现
	mockContractRW := salesmock.NewMockSalesforceContractReadWriter(ctrl)
	mockSalesforceClient := salesforcemock.NewMockClient(ctrl)

	// 创建 Logic 实例
	logic := sales.NewByParams(nil, nil, mockContractRW, mockSalesforceClient)

	t.Run("Test SyncContract", func(t *testing.T) {
		req := &sales.SalesforceContractSyncParams{
			SalesforceAccountID: "aaaa",
			SignTime:            time.Now(),
		}

		contract := &salesforce.Contract{
			ID: pointer.Get("contract_id"),
		}

		// 设置期望的调用
		mockSalesforceClient.EXPECT().CreateContract(gomock.Any(), gomock.Any()).Return(nil).SetArg(1, *contract)
		mockContractRW.EXPECT().Save(gomock.Any(), gomock.Any()).Return(nil)

		err := logic.SyncContract(context.Background(), req)
		assert.NoError(t, err)
	})
}
