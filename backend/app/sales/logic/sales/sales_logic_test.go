package sales_test

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"google.golang.org/genproto/googleapis/type/money"

	"github.com/MoeGolibrary/moego/backend/app/sales/logic/sales"
	salesmock "github.com/MoeGolibrary/moego/backend/app/sales/repo/mock/sales"
	salesforcemock "github.com/MoeGolibrary/moego/backend/app/sales/repo/mock/salesforce"
	salesrepo "github.com/MoeGolibrary/moego/backend/app/sales/repo/sales"
	"github.com/MoeGolibrary/moego/backend/app/sales/repo/salesforce"
	"github.com/MoeGolibrary/moego/backend/common/utils/pointer"
	salespb "github.com/MoeGolibrary/moego/backend/proto/sales/v1"
)

func TestCheckOpportunityExists(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockSalesforceClient := salesforcemock.NewMockClient(ctrl)
	logic := sales.NewByParams(nil, nil, mockSalesforceClient)

	t.Run("exists", func(t *testing.T) {
		mockSalesforceClient.EXPECT().GetOpportunity(gomock.Any(), "exists_id").Return(&salesforce.Opportunity{}, nil)
		exists := logic.CheckOpportunityExists(context.Background(), "exists_id")
		assert.True(t, exists)
	})

	t.Run("not exists", func(t *testing.T) {
		mockSalesforceClient.EXPECT().GetOpportunity(gomock.Any(), "not_exists_id").Return(nil, errors.New("not found"))
		exists := logic.CheckOpportunityExists(context.Background(), "not_exists_id")
		assert.False(t, exists)
	})
}

func TestSyncOpportunity(t *testing.T) {
	ctrl := gomock.NewController(t)

	// 创建 mock 实现
	mockOpportunityRW := salesmock.NewMockOpportunityReadWriter(ctrl)
	mockSalesforceClient := salesforcemock.NewMockClient(ctrl)

	// 创建 Logic 实例
	logic := sales.NewByParams(mockOpportunityRW, nil, mockSalesforceClient)

	t.Run("Test SyncOpportunity", func(t *testing.T) {

		opportunity := &salesrepo.Opportunity{
			ID: "id",
		}

		req := &sales.OpportunitySyncParams{
			ID:                    "id",
			Email:                 pointer.Get("<EMAIL>"),
			Tier:                  pointer.Get("T2"),
			TerminalPercentage:    pointer.Get("2.3"),
			TerminalFixed:         pointer.Get("0.5"),
			NonTerminalPercentage: pointer.Get("1.5"),
			NonTerminalFixed:      pointer.Get("0.1"),
			MinVolume:             pointer.Get("80000"),
			SPIF:                  pointer.Get("200"),
		}

		// 设置期望的调用
		mockOpportunityRW.EXPECT().Get(gomock.Any(), req.ID).Return(opportunity, nil)
		mockOpportunityRW.EXPECT().Save(gomock.Any(), gomock.Any()).Return(nil)
		mockSalesforceClient.EXPECT().GetOpportunity(gomock.Any(), req.ID).Return(opportunity.ToSalesforceEntity(), nil)
		mockSalesforceClient.EXPECT().UpdateOpportunity(gomock.Any(), gomock.Any()).Return(nil)

		err := logic.SyncOpportunity(context.Background(), req)
		assert.NoError(t, err)
	})
}

func TestSyncSalesSubscription(t *testing.T) {
	ctrl := gomock.NewController(t)

	// 创建 mock 实现
	mockOpportunityLineItemRW := salesmock.NewMockOpportunityLineItemReadWriter(ctrl)
	mockSalesforceClient := salesforcemock.NewMockClient(ctrl)

	// 创建 Logic 实例
	logic := sales.NewByParams(nil, mockOpportunityLineItemRW, mockSalesforceClient)

	t.Run("Test SyncSalesSubscription GROWTH_GROOMING", func(t *testing.T) {
		req := &salespb.SyncSalesSubscriptionRequest{
			OpportunityId:    "id",
			SubscriptionPlan: salespb.SubscriptionPlan_GROWTH_GROOMING,
			Salon: &salespb.LineItem{
				Quantity:  1,
				UnitPrice: &money.Money{Units: 100},
			},
			Van: &salespb.LineItem{
				Quantity:  1,
				UnitPrice: &money.Money{Units: 100},
			},
		}

		product := &salesforce.Product{
			ID:        "product_id",
			ListPrice: pointer.Get(100.0),
		}

		mockOpportunityLineItemRW.EXPECT().DeleteItems(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mockOpportunityLineItemRW.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil)
		mockSalesforceClient.EXPECT().ListOpportunityLineItems(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mockSalesforceClient.EXPECT().CreateOpportunityLineItems(gomock.Any(), gomock.Any()).Return(nil)
		mockSalesforceClient.EXPECT().GetProduct(gomock.Any(), gomock.Any()).Return(product).AnyTimes()

		err := logic.SyncSalesSubscription(context.Background(), req)
		assert.NoError(t, err)
	})

	t.Run("Test SyncSalesSubscription ULTIMATE_GROOMING", func(t *testing.T) {
		req := &salespb.SyncSalesSubscriptionRequest{
			OpportunityId:    "id",
			SubscriptionPlan: salespb.SubscriptionPlan_ULTIMATE_GROOMING,
			Salon: &salespb.LineItem{
				Quantity:  1,
				UnitPrice: &money.Money{Units: 100},
			},
			Van: &salespb.LineItem{
				Quantity:  1,
				UnitPrice: &money.Money{Units: 100},
			},
		}

		product := &salesforce.Product{
			ID:        "product_id",
			ListPrice: pointer.Get(100.0),
		}

		mockOpportunityLineItemRW.EXPECT().DeleteItems(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mockOpportunityLineItemRW.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil)
		mockSalesforceClient.EXPECT().ListOpportunityLineItems(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mockSalesforceClient.EXPECT().CreateOpportunityLineItems(gomock.Any(), gomock.Any()).Return(nil)
		mockSalesforceClient.EXPECT().GetProduct(gomock.Any(), gomock.Any()).Return(product).AnyTimes()

		err := logic.SyncSalesSubscription(context.Background(), req)
		assert.NoError(t, err)
	})

	t.Run("Test SyncSalesSubscription GROWTH_BOARDING_DAYCARE", func(t *testing.T) {
		req := &salespb.SyncSalesSubscriptionRequest{
			OpportunityId:    "id",
			SubscriptionPlan: salespb.SubscriptionPlan_GROWTH_BOARDING_DAYCARE,
			Salon: &salespb.LineItem{
				Quantity:  1,
				UnitPrice: &money.Money{Units: 100},
			},
		}

		product := &salesforce.Product{
			ID:        "product_id",
			ListPrice: pointer.Get(100.0),
		}

		mockOpportunityLineItemRW.EXPECT().DeleteItems(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mockOpportunityLineItemRW.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil)
		mockSalesforceClient.EXPECT().ListOpportunityLineItems(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mockSalesforceClient.EXPECT().CreateOpportunityLineItems(gomock.Any(), gomock.Any()).Return(nil)
		mockSalesforceClient.EXPECT().GetProduct(gomock.Any(), gomock.Any()).Return(product).AnyTimes()

		err := logic.SyncSalesSubscription(context.Background(), req)
		assert.NoError(t, err)
	})

	t.Run("Test SyncSalesSubscription ULTIMATE_BOARDING_DAYCARE", func(t *testing.T) {
		req := &salespb.SyncSalesSubscriptionRequest{
			OpportunityId:    "id",
			SubscriptionPlan: salespb.SubscriptionPlan_ULTIMATE_BOARDING_DAYCARE,
			Salon: &salespb.LineItem{
				Quantity:  1,
				UnitPrice: &money.Money{Units: 100},
			},
		}

		product := &salesforce.Product{
			ID:        "product_id",
			ListPrice: pointer.Get(100.0),
		}

		mockOpportunityLineItemRW.EXPECT().DeleteItems(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mockOpportunityLineItemRW.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil)
		mockSalesforceClient.EXPECT().ListOpportunityLineItems(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mockSalesforceClient.EXPECT().CreateOpportunityLineItems(gomock.Any(), gomock.Any()).Return(nil)
		mockSalesforceClient.EXPECT().GetProduct(gomock.Any(), gomock.Any()).Return(product).AnyTimes()

		err := logic.SyncSalesSubscription(context.Background(), req)
		assert.NoError(t, err)
	})

	t.Run("Test SyncSalesSubscription ENTERPRISE_BOARDING_DAYCARE", func(t *testing.T) {
		req := &salespb.SyncSalesSubscriptionRequest{
			OpportunityId:    "id",
			SubscriptionPlan: salespb.SubscriptionPlan_ENTERPRISE_BOARDING_DAYCARE,
			Salon: &salespb.LineItem{
				Quantity:  1,
				UnitPrice: &money.Money{Units: 100},
			},
		}

		product := &salesforce.Product{
			ID:        "product_id",
			ListPrice: pointer.Get(100.0),
		}

		mockOpportunityLineItemRW.EXPECT().DeleteItems(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mockOpportunityLineItemRW.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil)
		mockSalesforceClient.EXPECT().ListOpportunityLineItems(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mockSalesforceClient.EXPECT().CreateOpportunityLineItems(gomock.Any(), gomock.Any()).Return(nil)
		mockSalesforceClient.EXPECT().GetProduct(gomock.Any(), gomock.Any()).Return(product).AnyTimes()

		err := logic.SyncSalesSubscription(context.Background(), req)
		assert.NoError(t, err)
	})
}

func TestSyncSalesHardware(t *testing.T) {
	ctrl := gomock.NewController(t)

	// 创建 mock 实现
	mockOpportunityLineItemRW := salesmock.NewMockOpportunityLineItemReadWriter(ctrl)
	mockSalesforceClient := salesforcemock.NewMockClient(ctrl)

	// 创建 Logic 实例
	logic := sales.NewByParams(nil, mockOpportunityLineItemRW, mockSalesforceClient)

	t.Run("Test SyncSalesHardware", func(t *testing.T) {
		req := &salespb.SyncSalesHardwareRequest{
			OpportunityId: "id",
			ReaderM2: &salespb.LineItem{
				Quantity:  1,
				UnitPrice: &money.Money{Units: 100},
			},
			Bbpos: &salespb.LineItem{
				Quantity:  1,
				UnitPrice: &money.Money{Units: 100},
			},
		}

		product := &salesforce.Product{
			ID:        "product_id",
			ListPrice: pointer.Get(100.0),
		}

		mockOpportunityLineItemRW.EXPECT().DeleteItems(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mockOpportunityLineItemRW.EXPECT().BatchCreate(gomock.Any(), gomock.Any()).Return(nil)
		mockSalesforceClient.EXPECT().ListOpportunityLineItems(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mockSalesforceClient.EXPECT().CreateOpportunityLineItems(gomock.Any(), gomock.Any()).Return(nil)
		mockSalesforceClient.EXPECT().GetProduct(gomock.Any(), gomock.Any()).Return(product).AnyTimes()

		err := logic.SyncSalesHardware(context.Background(), req)
		assert.NoError(t, err)
	})
}
