package sales

import (
	"context"
	"errors"
	"time"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/sales/config"
	"github.com/MoeGolibrary/moego/backend/app/sales/repo/sales"
	"github.com/MoeGolibrary/moego/backend/app/sales/repo/salesforce"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type Logic struct {
	opportunity sales.OpportunityReadWriter
	lineItem    sales.OpportunityLineItemReadWriter
	contract    sales.SalesforceContractReadWriter
	sf          salesforce.Client
}

func NewLogic() *Logic {
	return &Logic{
		opportunity: sales.NewOpportunityRW(),
		lineItem:    sales.NewOpportunityLineItemRW(),
		contract:    sales.NewSalesforceContractRW(),
		sf:          salesforce.New(config.GetCfg().Salesforce),
	}
}

func NewByParams(
	opportunity sales.OpportunityReadWriter,
	lineItem sales.OpportunityLineItemReadWriter,
	contract sales.SalesforceContractReadWriter,
	sf salesforce.Client,
) *Logic {
	return &Logic{
		opportunity: opportunity,
		lineItem:    lineItem,
		contract:    contract,
		sf:          sf,
	}
}

func (l *Logic) CheckOpportunityExists(ctx context.Context, id string) bool {
	_, err := l.sf.GetOpportunity(ctx, id)

	return err == nil
}

func (l *Logic) GetSalesforceAccountByEmail(ctx context.Context, email string) (*salesforce.Account, error) {
	return l.sf.GetAccountByEmail(ctx, email)
}

func (l *Logic) SyncOpportunity(ctx context.Context, params *OpportunitySyncParams) error {
	// 先检查这个 opportunity 是否在 salesforce 上存在
	sfop, err := l.sf.GetOpportunity(ctx, params.ID)
	if err != nil {
		return err
	}

	// 查询  opportunity, 如果数据库中不存在则创建一个
	op, err := l.opportunity.Get(ctx, params.ID)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		log.InfoContextf(ctx, "First sync of opportunity %s", params.ID)
		op = &sales.Opportunity{
			ID: params.ID,
		}
	} else if err != nil {
		return err
	}
	params.ApplyTo(op)
	// account id 从 sfop 取
	op.SalesforceAccountID = sfop.AccountID

	// 先保存到数据库
	if err := l.opportunity.Save(ctx, op); err != nil {
		return err
	}

	// 更新到 salesforce
	return l.sf.UpdateOpportunity(ctx, op.ToSalesforceEntity())
}

func (l *Logic) SyncContract(ctx context.Context, params *SalesforceContractSyncParams) error {
	// TODO: check salesforce account exist
	startDate := params.SignTime.Format(time.DateOnly)

	contract := &salesforce.Contract{
		AccountID:   &params.SalesforceAccountID,
		StartDate:   &startDate,
		Link:        params.Link,
		Term:        params.Term,
		Type:        params.Type,
		Description: params.Description,
	}

	if err := l.sf.CreateContract(ctx, contract); err != nil {
		return err
	}

	return l.contract.Save(ctx, &sales.SalesforceContract{
		ID:                  *contract.ID,
		SalesforceAccountID: contract.AccountID,
		StartDate:           contract.StartDate,
		Term:                contract.Term,
		Link:                contract.Link,
		Type:                contract.Type,
		Description:         contract.Description,
	})
}

type OpportunitySyncParams struct {
	ID                    string
	Email                 *string
	Tier                  *string
	TerminalPercentage    *string
	TerminalFixed         *string
	NonTerminalPercentage *string
	NonTerminalFixed      *string
	MinVolume             *string
	SPIF                  *string
}

func (p *OpportunitySyncParams) ApplyTo(opportunity *sales.Opportunity) {
	setters := []struct {
		cond func() bool
		set  func(*sales.Opportunity)
	}{
		{
			cond: func() bool { return p.Email != nil },
			set:  func(o *sales.Opportunity) { o.Email = p.Email },
		},
		{
			cond: func() bool { return p.Tier != nil },
			set:  func(o *sales.Opportunity) { o.Tier = p.Tier },
		},
		{
			cond: func() bool { return p.TerminalPercentage != nil },
			set:  func(o *sales.Opportunity) { o.TerminalPercentage = p.TerminalPercentage },
		},
		{
			cond: func() bool { return p.TerminalFixed != nil },
			set:  func(o *sales.Opportunity) { o.TerminalFixed = p.TerminalFixed },
		},
		{
			cond: func() bool { return p.NonTerminalPercentage != nil },
			set:  func(o *sales.Opportunity) { o.NonTerminalPercentage = p.NonTerminalPercentage },
		},
		{
			cond: func() bool { return p.NonTerminalFixed != nil },
			set:  func(o *sales.Opportunity) { o.NonTerminalFixed = p.NonTerminalFixed },
		},
		{
			cond: func() bool { return p.MinVolume != nil },
			set:  func(o *sales.Opportunity) { o.MinVolume = p.MinVolume },
		},
		{
			cond: func() bool { return p.SPIF != nil },
			set:  func(o *sales.Opportunity) { o.SPIF = p.SPIF },
		},
	}

	for _, s := range setters {
		if s.cond() {
			s.set(opportunity)
		}
	}
}

type SalesforceContractSyncParams struct {
	SalesforceAccountID string
	SignTime            time.Time
	Description         *string
	Term                *int32
	Link                *string
	Type                *string
}
