package sales

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/shopspring/decimal"
	"github.com/slack-go/slack"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/sales/config"
	"github.com/MoeGolibrary/moego/backend/app/sales/repo/payment"
	"github.com/MoeGolibrary/moego/backend/app/sales/repo/sales"
	"github.com/MoeGolibrary/moego/backend/app/sales/repo/salesforce"
	slackclient "github.com/MoeGolibrary/moego/backend/app/sales/repo/slack"
	salesutils "github.com/MoeGolibrary/moego/backend/app/sales/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/utils/env"
	salespb "github.com/MoeGolibrary/moego/backend/proto/sales/v1"
)

type PlatformSalesLogic struct {
	salesLinkClient payment.SalesLinkClient
	priceClient     payment.PriceConfigClient
	slackClient     slackclient.Client
	slackPath       string
	opportunity     sales.OpportunityReadWriter
	lineItem        sales.OpportunityLineItemReadWriter
	contract        sales.SalesforceContractReadWriter
	sf              salesforce.Client
	approvalLogic   *MoegoPayCustomFeeApprovalLogic
}

func NewPlatformSalesLogic() *PlatformSalesLogic {
	return &PlatformSalesLogic{
		salesLinkClient: payment.NewSalesLinkClient(),
		priceClient:     payment.NewPriceConfigClient(),
		slackClient:     slackclient.New(),
		slackPath:       config.GetCfg().Slack.PlatformSalesSignUpPath,
		opportunity:     sales.NewOpportunityRW(),
		lineItem:        sales.NewOpportunityLineItemRW(),
		contract:        sales.NewSalesforceContractRW(),
		sf:              salesforce.New(config.GetCfg().Salesforce),
		approvalLogic:   NewMoegoPayCustomFeeApprovalLogic(),
	}
}

func NewPlatformSalesLogicByParams(
	salesLinkClient payment.SalesLinkClient,
	priceClient payment.PriceConfigClient,
	slackClient slackclient.Client,
	slackPath string,
	opportunity sales.OpportunityReadWriter,
	lineItem sales.OpportunityLineItemReadWriter,
	contract sales.SalesforceContractReadWriter,
	sf salesforce.Client,
	approvalLogic *MoegoPayCustomFeeApprovalLogic,
) *PlatformSalesLogic {
	return &PlatformSalesLogic{
		salesLinkClient: salesLinkClient,
		priceClient:     priceClient,
		slackClient:     slackClient,
		slackPath:       slackPath,
		opportunity:     opportunity,
		lineItem:        lineItem,
		contract:        contract,
		sf:              sf,
		approvalLogic:   approvalLogic,
	}
}

func (l *PlatformSalesLogic) GetSignUpContractLink(salesCode string) string {
	return fmt.Sprintf("https://%s/sales/contract?salesCode=%s", env.GetDomainHost(env.GO), salesCode)
}

// SyncPlatformSales 同步 sales link 注册数据并发送消息
func (l *PlatformSalesLogic) SyncPlatformSales(ctx context.Context, req *salespb.SyncPlatformSalesRequest) error {
	code := req.GetSalesCode()
	// 查询 platform sales link 记录
	record, err := l.salesLinkClient.GetSalesLink(ctx, code)
	if err != nil {
		return err
	}
	if record == nil {
		return fmt.Errorf("SalesLink code not exist, code=%s", code)
	}

	// 如果没有关联 opportunity id，则不需要同步数据
	if len(record.OpportunityID) <= 0 {
		log.InfoContextf(ctx, "opportunity id is empty, skip sales code: %s", code)

		return nil
	}

	// 查询 opportunity
	op, err := l.getOpportunity(ctx, record.OpportunityID)
	if err != nil {
		return err
	}

	// 同步 opportunity，如果失败打印日志，不阻塞后面的流程
	if err := l.syncOpportunity(ctx, op, record); err != nil {
		log.ErrorContextf(ctx, "failed to sync opportunity for sales code %s: %v", code, err)
	}

	// 同步 opportunity items，如果失败打印日志，不阻塞后面的流程
	if err := l.syncOpportunityItems(ctx, req, record); err != nil {
		log.ErrorContextf(ctx, "failed to sync opportunity items for sales code %s: %v", code, err)
	}

	// TODO: check salesforce account exist
	sfAccountID := op.SalesforceAccountID
	if sfAccountID == nil {
		log.ErrorContextf(ctx, "salesforce account id is empty for opportunity: %s, skip sync account", op.ID)
	} else {
		// 同步 salesforce account email，如果失败打印日志，不阻塞后面的流程
		if err := l.updateSalesforceAccountEmail(ctx, *sfAccountID, record.Email); err != nil {
			log.ErrorContextf(ctx, "failed to update email of salesforce account %s for sales code %s: %v",
				sfAccountID, code, err)
		}

		// 把 sign up contract 同步到 salesforce，如果失败打印日志，不阻塞后面的流程
		if err := l.syncSignUpContract(ctx, *sfAccountID, record); err != nil {
			log.ErrorContextf(ctx, "failed to sync sign up contract for sales code %s: %v", code, err)
		}
	}

	// 发起 custom rate 审批（如果有的话），发起失败打印日志，不阻塞后面的流程
	if err := l.createCustomFeeApproval(ctx, record); err != nil {
		log.ErrorContextf(ctx, "failed to create custom fee approval for sales code %s: %v", code, err)
	}

	// 发送 slack 消息
	return l.sendSignUpMessage(ctx, record)
}

func (l *PlatformSalesLogic) getOpportunity(ctx context.Context, id string) (*sales.Opportunity, error) {
	// 先检查这个 opportunity 是否在 salesforce 上存在
	sfop, err := l.sf.GetOpportunity(ctx, id)
	if err != nil {
		return nil, err
	}

	// 查询  opportunity, 如果数据库中不存在则创建一个
	op, err := l.opportunity.Get(ctx, id)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		log.InfoContextf(ctx, "First sync of opportunity %s", id)
		op = &sales.Opportunity{
			ID:                  id,
			SalesforceAccountID: sfop.AccountID,
		}
	} else if err != nil {
		return nil, err
	}

	return op, nil
}

func (l *PlatformSalesLogic) updateSalesforceAccountEmail(ctx context.Context,
	salesforceAccountID, email string) error {
	return l.sf.UpdateAccountEmail(ctx, salesforceAccountID, email)
}

func (l *PlatformSalesLogic) syncOpportunity(ctx context.Context,
	op *sales.Opportunity, record *payment.PlatformSalesRecord) error {
	// 暂时只更新这两个字段
	op.Email = &record.Email
	op.Tier = &record.Tier

	// 先保存到数据库
	if err := l.opportunity.Save(ctx, op); err != nil {
		return err
	}

	// 更新到 salesforce
	return l.sf.UpdateOpportunity(ctx, op.ToSalesforceEntity())
}

func (l *PlatformSalesLogic) syncOpportunityItems(ctx context.Context,
	req *salespb.SyncPlatformSalesRequest, record *payment.PlatformSalesRecord) error {

	var items []*sales.OpportunityLineItem

	subscriptionItems := l.buildSubscriptionItems(ctx, req, record)
	items = append(items, subscriptionItems...)

	hardwareItems := l.buildHardwareItems(ctx, req, record)
	items = append(items, hardwareItems...)

	opportunityID := record.OpportunityID
	productIDs := make([]string, 0, len(items))
	for _, item := range items {
		productIDs = append(productIDs, item.ProductID)
	}

	// 保存到数据库, 如果有相同的 productID, 则先删除再插入
	deleteItems, err := l.lineItem.DeleteItems(ctx, opportunityID, productIDs...)
	if err != nil {
		return err
	}
	if len(deleteItems) > 0 {
		log.WarnContextf(ctx, "delete items: %s", salesutils.ToJSON(deleteItems))
	}

	if err := l.lineItem.BatchCreate(ctx, items); err != nil {
		return err
	}

	// 保存到 salesforce, 同样如果有相同的 productID, 则先删除再插入
	sfOldItems, err := l.sf.ListOpportunityLineItems(ctx, opportunityID, productIDs...)
	if err != nil {
		return err
	}
	if len(sfOldItems) > 0 {
		itemIDs := make([]string, len(sfOldItems))
		for i := range sfOldItems {
			itemIDs[i] = *sfOldItems[i].ID
		}
		if err := l.sf.DeleteOpportunityLineItems(ctx, itemIDs); err != nil {
			log.ErrorContextf(ctx, "Failed to delete opportunity line items, opportunity ID: %s, err: %v",
				opportunityID, err)

			return err
		}
	}

	var sfItems []*salesforce.OpportunityLineItem
	for _, item := range items {
		sfItems = append(sfItems, item.ToSalesforceEntity())
	}

	return l.sf.CreateOpportunityLineItems(ctx, sfItems)
}

func (l *PlatformSalesLogic) buildSubscriptionItems(ctx context.Context,
	req *salespb.SyncPlatformSalesRequest, record *payment.PlatformSalesRecord) []*sales.OpportunityLineItem {
	subscriptionPlan := record.GetSubscriptionPlan()
	price, err := l.priceClient.GetPrice(ctx, subscriptionPlan)
	if err != nil {
		log.ErrorContextf(ctx, "failed to get price for subscription plan %s: %v", subscriptionPlan, err)

		return nil
	}

	// discount for annual terms
	var discountPercentage float64
	if req.GetTermType() == TermTypeAnnual {
		discountPercentage = decimal.NewFromInt32(record.SubscriptionDiscount).InexactFloat64()
	}

	var items []*sales.OpportunityLineItem

	// salon product item
	if req.GetLocationCount() > 0 {
		name := GetSalesforceSalonProductName(subscriptionPlan)
		product := l.sf.GetProduct(ctx, name)
		var unitPrice decimal.Decimal
		if record.IsBD.IsTrue() {
			unitPrice = price.BDLocation
		} else {
			unitPrice = price.GroomingLocation
		}
		if product != nil {
			item := &sales.OpportunityLineItem{
				OpportunityID:      record.OpportunityID,
				ProductID:          product.ID,
				Quantity:           req.GetLocationCount(),
				UnitPrice:          unitPrice.InexactFloat64(),
				DiscountPercentage: &discountPercentage,
			}
			items = append(items, item)
		} else {
			log.ErrorContextf(ctx, "salon product %s not found", name)
		}
	}

	// van product item
	if req.GetVanCount() > 0 {
		name := GetSalesforceMobileProductName(subscriptionPlan)
		product := l.sf.GetProduct(ctx, name)
		if product != nil {
			item := &sales.OpportunityLineItem{
				OpportunityID:      record.OpportunityID,
				ProductID:          product.ID,
				Quantity:           req.GetVanCount(),
				UnitPrice:          price.Van.InexactFloat64(),
				DiscountPercentage: &discountPercentage,
			}
			items = append(items, item)
		} else {
			log.ErrorContextf(ctx, "van product %s not found", name)
		}
	}

	return items
}

func (l *PlatformSalesLogic) buildHardwareItems(ctx context.Context,
	req *salespb.SyncPlatformSalesRequest, record *payment.PlatformSalesRecord) []*sales.OpportunityLineItem {

	discountPercentage := decimal.NewFromInt32(record.HardwareDiscount).InexactFloat64()
	var items []*sales.OpportunityLineItem

	// BBPOS
	if req.GetBbposCount() > 0 {
		product := l.sf.GetProduct(ctx, SalesforceProduct_BBPOSWisePOSE)
		if product != nil {
			item := &sales.OpportunityLineItem{
				OpportunityID:      record.OpportunityID,
				ProductID:          product.ID,
				Quantity:           req.GetBbposCount(),
				UnitPrice:          Price_Hardware_BBPOSWisePOSE,
				DiscountPercentage: &discountPercentage,
			}
			items = append(items, item)
		}
	}

	// Reader M2
	if req.GetReaderM2Count() > 0 {
		product := l.sf.GetProduct(ctx, SalesforceProduct_ReaderM2)
		if product != nil {
			item := &sales.OpportunityLineItem{
				OpportunityID:      record.OpportunityID,
				ProductID:          product.ID,
				Quantity:           req.GetReaderM2Count(),
				UnitPrice:          Price_Hardware_ReaderM2,
				DiscountPercentage: &discountPercentage,
			}
			items = append(items, item)
		}
	}

	return items
}

func (l *PlatformSalesLogic) syncSignUpContract(ctx context.Context,
	sfAccountID string, record *payment.PlatformSalesRecord) error {
	// 如果 sales link 没有 signed time，说明可能是 monthly 订阅的，没有签合同，则不需要同步合同到 salesforce
	if record.SignedTime == nil {
		log.WarnContextf(ctx, "skip syncSignUpContract because signed time is empty for sales code: %s", record.Code)

		return nil
	}

	startDate := record.SignedTime.Format(time.DateOnly)
	link := l.GetSignUpContractLink(record.Code)
	contractType := "Sign up contract"
	contract := &salesforce.Contract{
		AccountID: &sfAccountID,
		StartDate: &startDate,
		Link:      &link,
		Term:      &record.Terms,
		Type:      &contractType,
	}

	if err := l.sf.CreateContract(ctx, contract); err != nil {
		return err
	}

	return l.contract.Save(ctx, &sales.SalesforceContract{
		ID:                  *contract.ID,
		SalesforceAccountID: contract.AccountID,
		StartDate:           contract.StartDate,
		Term:                contract.Term,
		Link:                contract.Link,
		Type:                contract.Type,
		Description:         contract.Description,
	})
}

func (l *PlatformSalesLogic) createCustomFeeApproval(ctx context.Context,
	record *payment.PlatformSalesRecord) error {

	if !record.IsCustomRate.IsTrue() {
		return nil
	}

	terminalFix, _ := salesutils.GetFee(record.TerminalRate)
	terminalPercentage, _ := salesutils.GetPercentage(record.TerminalRate)
	nonTerminalFix, _ := salesutils.GetFee(record.NonTerminalRate)
	nonTerminalPercentage, _ := salesutils.GetPercentage(record.NonTerminalRate)
	minVolume, _ := salesutils.ParseMoney(record.MinVolume)

	var spif *string
	if record.Spif != nil {
		s := decimal.NewFromFloat(*record.Spif).String()
		spif = &s
	}

	params := &MoegoPayCustomFeeApprovalCreateParams{
		CompanyID:             record.CompanyID,
		AccountID:             record.AccountID,
		OwnerEmail:            record.Email,
		Creator:               record.Creator,
		MinVolume:             minVolume.String(),
		NonTerminalFixed:      nonTerminalFix.String(),
		NonTerminalPercentage: nonTerminalPercentage.String(),
		TerminalFixed:         terminalFix.String(),
		TerminalPercentage:    terminalPercentage.String(),
		Spif:                  spif,
		ContractID:            &record.Code,
		OpportunityID:         &record.OpportunityID,
	}

	_, err := l.approvalLogic.CreateMoegoPayCustomFeeApproval(ctx, params)

	return err
}

// buildSignUpMessage 构造 sign up 通知到 slack 消息
func (l *PlatformSalesLogic) buildSignUpMessage(ctx context.Context,
	record *payment.PlatformSalesRecord) *slack.Message {

	userID, err := l.slackClient.GetUserIDByEmail(ctx, record.Creator)
	if err != nil {
		log.ErrorContextf(ctx, "Get user id failed, email=%s, err=%v", record.Creator, err)
	}
	if userID == "" {
		userID = record.Creator
	} else {
		userID = fmt.Sprintf("<@%s>", userID)
	}

	host := "moego--partial.sandbox.lightning.force.com"
	if env.IsProduction() {
		host = "moego.lightning.force.com"
	}
	link := fmt.Sprintf("<https://%s/lightning/r/Opportunity/%s/view|link>", host, record.OpportunityID)

	descriptionSection := slack.NewSectionBlock(
		&slack.TextBlockObject{
			Type: slack.MarkdownType,
			Text: fmt.Sprintf(
				":confetti_ball: New sign up! MoeGo login email: *_%s_*\n%s Please close the opportunity: %s",
				record.Email, userID, link),
		},
		nil,
		nil,
	)

	msg := slack.NewBlockMessage(descriptionSection)

	return &msg
}

// sendSignUpMessage 发送 sign up 通知到 slack
func (l *PlatformSalesLogic) sendSignUpMessage(ctx context.Context, record *payment.PlatformSalesRecord) error {
	slackMsg := l.buildSignUpMessage(ctx, record)

	return l.slackClient.SendMessage(ctx, l.slackPath, slackMsg)
}
