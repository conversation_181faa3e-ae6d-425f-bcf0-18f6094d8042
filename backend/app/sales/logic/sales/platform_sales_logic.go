package sales

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/slack-go/slack"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/sales/config"
	"github.com/MoeGolibrary/moego/backend/app/sales/repo/payment"
	"github.com/MoeGolibrary/moego/backend/app/sales/repo/sales"
	"github.com/MoeGolibrary/moego/backend/app/sales/repo/salesforce"
	slackclient "github.com/MoeGolibrary/moego/backend/app/sales/repo/slack"
	salesutils "github.com/MoeGolibrary/moego/backend/app/sales/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type PlatformSalesLogic struct {
	salesLinkClient payment.SalesLinkClient
	slackClient     slackclient.Client
	slackPath       string
	opportunity     sales.OpportunityReadWriter
	contract        sales.SalesforceContractReadWriter
	sf              salesforce.Client
}

func NewPlatformSalesLogic() *PlatformSalesLogic {
	return &PlatformSalesLogic{
		salesLinkClient: payment.NewSalesLinkClient(),
		slackClient:     slackclient.New(),
		slackPath:       config.GetCfg().Slack.PlatformSalesSignUpPath,
		opportunity:     sales.NewOpportunityRW(),
		contract:        sales.NewSalesforceContractRW(),
		sf:              salesforce.New(config.GetCfg().Salesforce),
	}
}

func NewPlatformSalesLogicByParams(
	salesLinkClient payment.SalesLinkClient,
	slackClient slackclient.Client,
	slackPath string,
	opportunity sales.OpportunityReadWriter,
	contract sales.SalesforceContractReadWriter,
	sf salesforce.Client,
) *PlatformSalesLogic {
	return &PlatformSalesLogic{
		salesLinkClient: salesLinkClient,
		slackClient:     slackClient,
		slackPath:       slackPath,
		opportunity:     opportunity,
		contract:        contract,
		sf:              sf,
	}
}

func (l *PlatformSalesLogic) GetSignUpContractLink(salesCode string) string {
	return fmt.Sprintf("https://%s/sales/contract?salesCode=%s", salesutils.GetDomainHost(salesutils.GO), salesCode)
}

// SyncPlatformSales 同步 sales link 注册数据并发送消息
func (l *PlatformSalesLogic) SyncPlatformSales(ctx context.Context, code string) error {
	// 查询 platform sales link 记录
	record, err := l.salesLinkClient.GetSalesLink(ctx, code)
	if err != nil {
		return err
	}
	if record == nil {
		return fmt.Errorf("SalesLink code not exist, code=%s", code)
	}

	// 如果没有关联 opportunity id，则不需要同步数据
	if len(record.OpportunityID) <= 0 {
		log.InfoContextf(ctx, "opportunity id is empty, skip sales code: %s", code)

		return nil
	}

	// 查询 opportunity
	op, err := l.getOpportunity(ctx, record.OpportunityID)
	if err != nil {
		return err
	}

	// 同步 opportunity，如果失败打印日志，不阻塞后面的流程
	if err := l.syncOpportunity(ctx, op, record); err != nil {
		log.ErrorContextf(ctx, "failed to sync opportunity for sales code %s: %v", code, err)
	}

	// TODO: check salesforce account exist
	sfAccountID := *op.SalesforceAccountID

	// 同步 salesforce account email，如果失败打印日志，不阻塞后面的流程
	if err := l.updateSalesforceAccountEmail(ctx, sfAccountID, record.Email); err != nil {
		log.ErrorContextf(ctx, "failed to update email of salesforce account %s for sales code %s: %v",
			sfAccountID, code, err)
	}

	// 把 sign up contract 同步到 salesforce，如果失败打印日志，不阻塞后面的流程
	if err := l.syncSignUpContract(ctx, sfAccountID, record); err != nil {
		log.ErrorContextf(ctx, "failed to sync sign up contract for sales code %s: %v", code, err)
	}

	// 发送 slack 消息
	return l.sendSignUpMessage(ctx, record)
}

func (l *PlatformSalesLogic) getOpportunity(ctx context.Context, id string) (*sales.Opportunity, error) {
	// 先检查这个 opportunity 是否在 salesforce 上存在
	sfop, err := l.sf.GetOpportunity(ctx, id)
	if err != nil {
		return nil, err
	}

	// 查询  opportunity, 如果数据库中不存在则创建一个
	op, err := l.opportunity.Get(ctx, id)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		log.InfoContextf(ctx, "First sync of opportunity %s", id)
		op = &sales.Opportunity{
			ID:                  id,
			SalesforceAccountID: sfop.AccountID,
		}
	} else if err != nil {
		return nil, err
	}

	return op, nil
}

func (l *PlatformSalesLogic) updateSalesforceAccountEmail(ctx context.Context,
	salesforceAccountID, email string) error {
	return l.sf.UpdateAccountEmail(ctx, salesforceAccountID, email)
}

func (l *PlatformSalesLogic) syncOpportunity(ctx context.Context,
	op *sales.Opportunity, record *payment.PlatformSalesRecord) error {
	// 暂时只更新这两个字段
	op.Email = &record.Email
	op.Tier = &record.Tier

	// 先保存到数据库
	if err := l.opportunity.Save(ctx, op); err != nil {
		return err
	}

	// 更新到 salesforce
	return l.sf.UpdateOpportunity(ctx, op.ToSalesforceEntity())
}

func (l *PlatformSalesLogic) syncSignUpContract(ctx context.Context,
	sfAccountID string, record *payment.PlatformSalesRecord) error {
	// 如果 sales link 没有 signed time，说明可能是 monthly 订阅的，没有签合同，则不需要同步合同到 salesforce
	if record.SignedTime == nil {
		log.WarnContextf(ctx, "skip syncSignUpContract because signed time is empty for sales code: %s", record.Code)

		return nil
	}

	startDate := record.SignedTime.Format(time.DateOnly)
	link := l.GetSignUpContractLink(record.Code)
	contractType := "Sign up contract"
	contract := &salesforce.Contract{
		AccountID: &sfAccountID,
		StartDate: &startDate,
		Link:      &link,
		Term:      &record.Terms,
		Type:      &contractType,
	}

	if err := l.sf.CreateContract(ctx, contract); err != nil {
		return err
	}

	return l.contract.Save(ctx, &sales.SalesforceContract{
		ID:                  *contract.ID,
		SalesforceAccountID: contract.AccountID,
		StartDate:           contract.StartDate,
		Term:                contract.Term,
		Link:                contract.Link,
		Type:                contract.Type,
		Description:         contract.Description,
	})
}

// buildSignUpMessage 构造 sign up 通知到 slack 消息（TODO: 文案待修改）
func (l *PlatformSalesLogic) buildSignUpMessage(ctx context.Context,
	record *payment.PlatformSalesRecord) *slack.Message {

	userID, err := l.slackClient.GetUserIDByEmail(ctx, record.Creator)
	if err != nil {
		log.ErrorContextf(ctx, "Get user id failed, email=%s, err=%v", record.Creator, err)
	}
	if userID == "" {
		userID = record.Creator
	} else {
		userID = fmt.Sprintf("<@%s>", userID)
	}

	host := "moego--partial.sandbox.lightning.force.com"
	if salesutils.IsProduction() {
		host = "moego.lightning.force.com"
	}
	link := fmt.Sprintf("<https://%s/lightning/r/Opportunity/%s/view|link>", host, record.OpportunityID)

	descriptionSection := slack.NewSectionBlock(
		&slack.TextBlockObject{
			Type: slack.MarkdownType,
			Text: fmt.Sprintf(
				":confetti_ball: New sign up! MoeGo login email: *_%s_*\n%s Please close the opportunity: %s",
				record.Email, userID, link),
		},
		nil,
		nil,
	)

	msg := slack.NewBlockMessage(descriptionSection)

	return &msg
}

// sendSignUpMessage 发送 sign up 通知到 slack
func (l *PlatformSalesLogic) sendSignUpMessage(ctx context.Context, record *payment.PlatformSalesRecord) error {
	slackMsg := l.buildSignUpMessage(ctx, record)

	return l.slackClient.SendMessage(ctx, l.slackPath, slackMsg)
}
