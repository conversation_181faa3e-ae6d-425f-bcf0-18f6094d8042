package sales_test

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/sales/logic/sales"
	mockpayment "github.com/MoeGolibrary/moego/backend/app/sales/repo/mock/payment"
	mocksales "github.com/MoeGolibrary/moego/backend/app/sales/repo/mock/sales"
	mocksalesforce "github.com/MoeGolibrary/moego/backend/app/sales/repo/mock/salesforce"
	mockslack "github.com/MoeGolibrary/moego/backend/app/sales/repo/mock/slack"
	"github.com/MoeGolibrary/moego/backend/app/sales/repo/payment"
	salesrepo "github.com/MoeGolibrary/moego/backend/app/sales/repo/sales"
	"github.com/MoeGolibrary/moego/backend/app/sales/repo/salesforce"
	salesutils "github.com/MoeGolibrary/moego/backend/app/sales/utils"
)

// setup mocks and logic instance for tests
func setup(t *testing.T) (
	context.Context,
	*gomock.Controller,
	*mockpayment.MockSalesLinkClient,
	*mockslack.MockClient,
	*mocksales.MockOpportunityReadWriter,
	*mocksales.MockSalesforceContractReadWriter,
	*mocksalesforce.MockClient,
	*sales.PlatformSalesLogic,
) {
	ctrl := gomock.NewController(t)
	ctx := context.Background()
	mockSalesLinkClient := mockpayment.NewMockSalesLinkClient(ctrl)
	mockSlackClient := mockslack.NewMockClient(ctrl)
	mockOpportunityRW := mocksales.NewMockOpportunityReadWriter(ctrl)
	mockContractRW := mocksales.NewMockSalesforceContractReadWriter(ctrl)
	mockSFClient := mocksalesforce.NewMockClient(ctrl)
	logic := sales.NewPlatformSalesLogicByParams(
		mockSalesLinkClient,
		mockSlackClient,
		"/slack-path",
		mockOpportunityRW,
		mockContractRW,
		mockSFClient,
	)
	return ctx, ctrl, mockSalesLinkClient, mockSlackClient, mockOpportunityRW, mockContractRW, mockSFClient, logic
}

func TestPlatformSalesLogic_SyncPlatformSales(t *testing.T) {
	code := "testcode"
	oppID := "opp123"
	sfAccountID := "sf_account_123"
	creatorEmail := "<EMAIL>"
	customerEmail := "<EMAIL>"
	slackPath := "/slack-path"
	now := time.Now()

	salesLinkRecord := &payment.PlatformSalesRecord{
		Code:          code,
		Creator:       creatorEmail,
		Email:         customerEmail,
		OpportunityID: oppID,
		SignedTime:    &now,
		Terms:         12,
		Tier:          "Premium",
	}

	opportunity := &salesrepo.Opportunity{
		ID:                  oppID,
		SalesforceAccountID: &sfAccountID,
	}

	sfOpportunity := &salesforce.Opportunity{
		ID:        &oppID,
		AccountID: &sfAccountID,
	}

	t.Run("success", func(t *testing.T) {
		ctx, ctrl, mockSalesLinkClient, mockSlackClient, mockOpportunityRW, mockContractRW, mockSFClient, logic := setup(t)
		defer ctrl.Finish()

		contractID := "contract_id_123"
		gomock.InOrder(
			mockSalesLinkClient.EXPECT().GetSalesLink(ctx, code).Return(salesLinkRecord, nil),
			mockSFClient.EXPECT().GetOpportunity(ctx, oppID).Return(sfOpportunity, nil),
			mockOpportunityRW.EXPECT().Get(ctx, oppID).Return(opportunity, nil),
			mockOpportunityRW.EXPECT().Save(ctx, gomock.Any()).Return(nil),
			mockSFClient.EXPECT().UpdateOpportunity(ctx, gomock.Any()).Return(nil),
			mockSFClient.EXPECT().UpdateAccountEmail(ctx, sfAccountID, customerEmail).Return(nil),
			mockSFClient.EXPECT().CreateContract(ctx, gomock.Any()).DoAndReturn(func(_ context.Context, c *salesforce.Contract) error {
				c.ID = &contractID
				return nil
			}),
			mockContractRW.EXPECT().Save(ctx, gomock.Any()).Return(nil),
			mockSlackClient.EXPECT().GetUserIDByEmail(ctx, creatorEmail).Return("slack_user_id", nil),
			mockSlackClient.EXPECT().SendMessage(ctx, slackPath, gomock.Any()).Return(nil),
		)

		err := logic.SyncPlatformSales(ctx, code)
		assert.NoError(t, err)
	})

	t.Run("success when opportunity not in db", func(t *testing.T) {
		ctx, ctrl, mockSalesLinkClient, mockSlackClient, mockOpportunityRW, mockContractRW, mockSFClient, logic := setup(t)
		defer ctrl.Finish()

		contractID := "contract_id_123"
		gomock.InOrder(
			mockSalesLinkClient.EXPECT().GetSalesLink(ctx, code).Return(salesLinkRecord, nil),
			mockSFClient.EXPECT().GetOpportunity(ctx, oppID).Return(sfOpportunity, nil),
			mockOpportunityRW.EXPECT().Get(ctx, oppID).Return(nil, gorm.ErrRecordNotFound),
			mockOpportunityRW.EXPECT().Save(ctx, gomock.Any()).Return(nil),
			mockSFClient.EXPECT().UpdateOpportunity(ctx, gomock.Any()).Return(nil),
			mockSFClient.EXPECT().UpdateAccountEmail(ctx, sfAccountID, customerEmail).Return(nil),
			mockSFClient.EXPECT().CreateContract(ctx, gomock.Any()).DoAndReturn(func(_ context.Context, c *salesforce.Contract) error {
				c.ID = &contractID
				return nil
			}),
			mockContractRW.EXPECT().Save(ctx, gomock.Any()).Return(nil),
			mockSlackClient.EXPECT().GetUserIDByEmail(ctx, creatorEmail).Return("slack_user_id", nil),
			mockSlackClient.EXPECT().SendMessage(ctx, slackPath, gomock.Any()).Return(nil),
		)

		err := logic.SyncPlatformSales(ctx, code)
		assert.NoError(t, err)
	})

	t.Run("sales link not found", func(t *testing.T) {
		ctx, ctrl, mockSalesLinkClient, _, _, _, _, logic := setup(t)
		defer ctrl.Finish()

		mockSalesLinkClient.EXPECT().GetSalesLink(ctx, code).Return(nil, nil)
		err := logic.SyncPlatformSales(ctx, code)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "SalesLink code not exist")
	})

	t.Run("opportunity id is empty", func(t *testing.T) {
		ctx, ctrl, mockSalesLinkClient, _, _, _, _, logic := setup(t)
		defer ctrl.Finish()

		recordWithoutOpp := *salesLinkRecord
		recordWithoutOpp.OpportunityID = ""
		mockSalesLinkClient.EXPECT().GetSalesLink(ctx, code).Return(&recordWithoutOpp, nil)

		err := logic.SyncPlatformSales(ctx, code)
		assert.NoError(t, err)
	})

	t.Run("get sf opportunity error", func(t *testing.T) {
		ctx, ctrl, mockSalesLinkClient, _, _, _, mockSFClient, logic := setup(t)
		defer ctrl.Finish()

		dbErr := errors.New("salesforce error")
		gomock.InOrder(
			mockSalesLinkClient.EXPECT().GetSalesLink(ctx, code).Return(salesLinkRecord, nil),
			mockSFClient.EXPECT().GetOpportunity(ctx, oppID).Return(nil, dbErr),
		)

		err := logic.SyncPlatformSales(ctx, code)
		assert.Error(t, err)
		assert.EqualError(t, err, "salesforce error")
	})

	t.Run("get opportunity db error", func(t *testing.T) {
		ctx, ctrl, mockSalesLinkClient, _, mockOpportunityRW, _, mockSFClient, logic := setup(t)
		defer ctrl.Finish()

		dbErr := errors.New("db error")
		gomock.InOrder(
			mockSalesLinkClient.EXPECT().GetSalesLink(ctx, code).Return(salesLinkRecord, nil),
			mockSFClient.EXPECT().GetOpportunity(ctx, oppID).Return(sfOpportunity, nil),
			mockOpportunityRW.EXPECT().Get(ctx, oppID).Return(nil, dbErr),
		)

		err := logic.SyncPlatformSales(ctx, code)
		assert.Error(t, err)
		assert.EqualError(t, err, "db error")
	})

	t.Run("sync opportunity - save error, should continue", func(t *testing.T) {
		ctx, ctrl, mockSalesLinkClient, mockSlackClient, mockOpportunityRW, mockContractRW, mockSFClient, logic := setup(t)
		defer ctrl.Finish()

		contractID := "contract_id_123"
		gomock.InOrder(
			mockSalesLinkClient.EXPECT().GetSalesLink(ctx, code).Return(salesLinkRecord, nil),
			mockSFClient.EXPECT().GetOpportunity(ctx, oppID).Return(sfOpportunity, nil),
			mockOpportunityRW.EXPECT().Get(ctx, oppID).Return(opportunity, nil),
			mockOpportunityRW.EXPECT().Save(ctx, gomock.Any()).Return(errors.New("db save error")),
			// syncOpportunity fails, but process continues
			mockSFClient.EXPECT().UpdateAccountEmail(ctx, sfAccountID, customerEmail).Return(nil),
			mockSFClient.EXPECT().CreateContract(ctx, gomock.Any()).DoAndReturn(func(_ context.Context, c *salesforce.Contract) error {
				c.ID = &contractID
				return nil
			}),
			mockContractRW.EXPECT().Save(ctx, gomock.Any()).Return(nil),
			mockSlackClient.EXPECT().GetUserIDByEmail(ctx, creatorEmail).Return("slack_user_id", nil),
			mockSlackClient.EXPECT().SendMessage(ctx, slackPath, gomock.Any()).Return(nil),
		)

		err := logic.SyncPlatformSales(ctx, code)
		assert.NoError(t, err)
	})

	t.Run("sync opportunity - update sf error, should continue", func(t *testing.T) {
		ctx, ctrl, mockSalesLinkClient, mockSlackClient, mockOpportunityRW, mockContractRW, mockSFClient, logic := setup(t)
		defer ctrl.Finish()

		contractID := "contract_id_123"
		gomock.InOrder(
			mockSalesLinkClient.EXPECT().GetSalesLink(ctx, code).Return(salesLinkRecord, nil),
			mockSFClient.EXPECT().GetOpportunity(ctx, oppID).Return(sfOpportunity, nil),
			mockOpportunityRW.EXPECT().Get(ctx, oppID).Return(opportunity, nil),
			mockOpportunityRW.EXPECT().Save(ctx, gomock.Any()).Return(nil),
			mockSFClient.EXPECT().UpdateOpportunity(ctx, gomock.Any()).Return(errors.New("sf update error")),
			// syncOpportunity fails, but process continues
			mockSFClient.EXPECT().UpdateAccountEmail(ctx, sfAccountID, customerEmail).Return(nil),
			mockSFClient.EXPECT().CreateContract(ctx, gomock.Any()).DoAndReturn(func(_ context.Context, c *salesforce.Contract) error {
				c.ID = &contractID
				return nil
			}),
			mockContractRW.EXPECT().Save(ctx, gomock.Any()).Return(nil),
			mockSlackClient.EXPECT().GetUserIDByEmail(ctx, creatorEmail).Return("slack_user_id", nil),
			mockSlackClient.EXPECT().SendMessage(ctx, slackPath, gomock.Any()).Return(nil),
		)

		err := logic.SyncPlatformSales(ctx, code)
		assert.NoError(t, err)
	})

	t.Run("update sf account email error, should continue", func(t *testing.T) {
		ctx, ctrl, mockSalesLinkClient, mockSlackClient, mockOpportunityRW, mockContractRW, mockSFClient, logic := setup(t)
		defer ctrl.Finish()

		contractID := "contract_id_123"
		gomock.InOrder(
			mockSalesLinkClient.EXPECT().GetSalesLink(ctx, code).Return(salesLinkRecord, nil),
			mockSFClient.EXPECT().GetOpportunity(ctx, oppID).Return(sfOpportunity, nil),
			mockOpportunityRW.EXPECT().Get(ctx, oppID).Return(opportunity, nil),
			mockOpportunityRW.EXPECT().Save(ctx, gomock.Any()).Return(nil),
			mockSFClient.EXPECT().UpdateOpportunity(ctx, gomock.Any()).Return(nil),
			mockSFClient.EXPECT().UpdateAccountEmail(ctx, sfAccountID, customerEmail).Return(errors.New("sf update email error")),
			// update email fails, but process continues
			mockSFClient.EXPECT().CreateContract(ctx, gomock.Any()).DoAndReturn(func(_ context.Context, c *salesforce.Contract) error {
				c.ID = &contractID
				return nil
			}),
			mockContractRW.EXPECT().Save(ctx, gomock.Any()).Return(nil),
			mockSlackClient.EXPECT().GetUserIDByEmail(ctx, creatorEmail).Return("slack_user_id", nil),
			mockSlackClient.EXPECT().SendMessage(ctx, slackPath, gomock.Any()).Return(nil),
		)

		err := logic.SyncPlatformSales(ctx, code)
		assert.NoError(t, err)
	})

	t.Run("skip sync contract when signed time is nil", func(t *testing.T) {
		ctx, ctrl, mockSalesLinkClient, mockSlackClient, mockOpportunityRW, _, mockSFClient, logic := setup(t)
		defer ctrl.Finish()

		recordWithoutSignedTime := *salesLinkRecord
		recordWithoutSignedTime.SignedTime = nil

		gomock.InOrder(
			mockSalesLinkClient.EXPECT().GetSalesLink(ctx, code).Return(&recordWithoutSignedTime, nil),
			mockSFClient.EXPECT().GetOpportunity(ctx, oppID).Return(sfOpportunity, nil),
			mockOpportunityRW.EXPECT().Get(ctx, oppID).Return(opportunity, nil),
			mockOpportunityRW.EXPECT().Save(ctx, gomock.Any()).Return(nil),
			mockSFClient.EXPECT().UpdateOpportunity(ctx, gomock.Any()).Return(nil),
			mockSFClient.EXPECT().UpdateAccountEmail(ctx, sfAccountID, customerEmail).Return(nil),
			// No contract sync
			mockSlackClient.EXPECT().GetUserIDByEmail(ctx, creatorEmail).Return("slack_user_id", nil),
			mockSlackClient.EXPECT().SendMessage(ctx, slackPath, gomock.Any()).Return(nil),
		)

		err := logic.SyncPlatformSales(ctx, code)
		assert.NoError(t, err)
	})

	t.Run("create contract error, should continue", func(t *testing.T) {
		ctx, ctrl, mockSalesLinkClient, mockSlackClient, mockOpportunityRW, _, mockSFClient, logic := setup(t)
		defer ctrl.Finish()

		gomock.InOrder(
			mockSalesLinkClient.EXPECT().GetSalesLink(ctx, code).Return(salesLinkRecord, nil),
			mockSFClient.EXPECT().GetOpportunity(ctx, oppID).Return(sfOpportunity, nil),
			mockOpportunityRW.EXPECT().Get(ctx, oppID).Return(opportunity, nil),
			mockOpportunityRW.EXPECT().Save(ctx, gomock.Any()).Return(nil),
			mockSFClient.EXPECT().UpdateOpportunity(ctx, gomock.Any()).Return(nil),
			mockSFClient.EXPECT().UpdateAccountEmail(ctx, sfAccountID, customerEmail).Return(nil),
			mockSFClient.EXPECT().CreateContract(ctx, gomock.Any()).Return(errors.New("salesforce error")),
			// should continue to send message
			mockSlackClient.EXPECT().GetUserIDByEmail(ctx, creatorEmail).Return("slack_user_id", nil),
			mockSlackClient.EXPECT().SendMessage(ctx, slackPath, gomock.Any()).Return(nil),
		)

		err := logic.SyncPlatformSales(ctx, code)
		assert.NoError(t, err)
	})

	t.Run("save contract error, should continue", func(t *testing.T) {
		ctx, ctrl, mockSalesLinkClient, mockSlackClient, mockOpportunityRW, mockContractRW, mockSFClient, logic := setup(t)
		defer ctrl.Finish()

		contractID := "contract_id_123"
		gomock.InOrder(
			mockSalesLinkClient.EXPECT().GetSalesLink(ctx, code).Return(salesLinkRecord, nil),
			mockSFClient.EXPECT().GetOpportunity(ctx, oppID).Return(sfOpportunity, nil),
			mockOpportunityRW.EXPECT().Get(ctx, oppID).Return(opportunity, nil),
			mockOpportunityRW.EXPECT().Save(ctx, gomock.Any()).Return(nil),
			mockSFClient.EXPECT().UpdateOpportunity(ctx, gomock.Any()).Return(nil),
			mockSFClient.EXPECT().UpdateAccountEmail(ctx, sfAccountID, customerEmail).Return(nil),
			mockSFClient.EXPECT().CreateContract(ctx, gomock.Any()).DoAndReturn(func(_ context.Context, c *salesforce.Contract) error {
				c.ID = &contractID
				return nil
			}),
			mockContractRW.EXPECT().Save(ctx, gomock.Any()).Return(errors.New("db save error")),
			// should continue to send message
			mockSlackClient.EXPECT().GetUserIDByEmail(ctx, creatorEmail).Return("slack_user_id", nil),
			mockSlackClient.EXPECT().SendMessage(ctx, slackPath, gomock.Any()).Return(nil),
		)

		err := logic.SyncPlatformSales(ctx, code)
		assert.NoError(t, err)
	})

	t.Run("send slack message error", func(t *testing.T) {
		ctx, ctrl, mockSalesLinkClient, mockSlackClient, mockOpportunityRW, mockContractRW, mockSFClient, logic := setup(t)
		defer ctrl.Finish()

		contractID := "contract_id_123"
		gomock.InOrder(
			mockSalesLinkClient.EXPECT().GetSalesLink(ctx, code).Return(salesLinkRecord, nil),
			mockSFClient.EXPECT().GetOpportunity(ctx, oppID).Return(sfOpportunity, nil),
			mockOpportunityRW.EXPECT().Get(ctx, oppID).Return(opportunity, nil),
			mockOpportunityRW.EXPECT().Save(ctx, gomock.Any()).Return(nil),
			mockSFClient.EXPECT().UpdateOpportunity(ctx, gomock.Any()).Return(nil),
			mockSFClient.EXPECT().UpdateAccountEmail(ctx, sfAccountID, customerEmail).Return(nil),
			mockSFClient.EXPECT().CreateContract(ctx, gomock.Any()).DoAndReturn(func(_ context.Context, c *salesforce.Contract) error {
				c.ID = &contractID
				return nil
			}),
			mockContractRW.EXPECT().Save(ctx, gomock.Any()).Return(nil),
			mockSlackClient.EXPECT().GetUserIDByEmail(ctx, creatorEmail).Return("slack_user_id", nil),
			mockSlackClient.EXPECT().SendMessage(ctx, slackPath, gomock.Any()).Return(errors.New("slack error")),
		)

		err := logic.SyncPlatformSales(ctx, code)
		assert.Error(t, err)
		assert.EqualError(t, err, "slack error")
	})
}

func TestPlatformSalesLogic_GetSignUpContractLink(t *testing.T) {
	logic := &sales.PlatformSalesLogic{}
	salesCode := "test-code"
	expectedLink := fmt.Sprintf("https://%s/sales/contract?salesCode=%s", salesutils.GetDomainHost(salesutils.GO), salesCode)
	actualLink := logic.GetSignUpContractLink(salesCode)
	assert.Equal(t, expectedLink, actualLink)
}
