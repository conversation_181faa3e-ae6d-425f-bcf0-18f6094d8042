package sales

import (
	"context"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"github.com/MoeGolibrary/moego/backend/app/sales/repo/salesforce"
)

type OpportunityLineItem struct {
	ID                 int64      `gorm:"column:id"`
	OpportunityID      string     `gorm:"column:opportunity_id"`
	ProductID          string     `gorm:"column:product_id"`
	Quantity           int32      `gorm:"column:quantity"`
	UnitPrice          float64    `gorm:"column:unit_price"`
	DiscountPercentage *float64   `gorm:"column:discount_percentage"`
	ContractLink       *string    `gorm:"column:contract_link"`
	ContractType       *string    `gorm:"column:contract_type"`
	ContractSigned     *bool      `gorm:"column:contract_signed"`
	CreatedAt          *time.Time `gorm:"column:created_at"`
	UpdatedAt          *time.Time `gorm:"column:updated_at"`
}

func (*OpportunityLineItem) TableName() string {
	return "moego_sales.public.salesforce_opportunity_line_item"
}

func (i *OpportunityLineItem) ToSalesforceEntity() *salesforce.OpportunityLineItem {
	return &salesforce.OpportunityLineItem{
		OpportunityID:  i.OpportunityID,
		ProductID:      i.ProductID,
		Quantity:       i.Quantity,
		UnitPrice:      i.UnitPrice,
		Discount:       i.DiscountPercentage,
		ContractLink:   i.ContractLink,
		ContractType:   i.ContractType,
		ContractSigned: i.ContractSigned,
	}
}

type OpportunityLineItemReadWriter interface {
	BatchCreate(ctx context.Context, items []*OpportunityLineItem) error
	ListItems(ctx context.Context, opportunityID string) ([]*OpportunityLineItem, error)
	DeleteItems(ctx context.Context, opportunityID string, productIDs ...string) ([]*OpportunityLineItem, error)
}

type opportunityLineItemImpl struct {
	db *gorm.DB
}

func NewOpportunityLineItemRW() OpportunityLineItemReadWriter {
	return &opportunityLineItemImpl{
		db: NewDB(),
	}
}

func (i *opportunityLineItemImpl) ListItems(ctx context.Context, opportunityID string) ([]*OpportunityLineItem, error) {
	var items []*OpportunityLineItem
	if err := i.db.WithContext(ctx).
		Model(&OpportunityLineItem{}).
		Where("opportunity_id = ?", opportunityID).
		Find(&items).Error; err != nil {
		return nil, err
	}

	return items, nil
}

func (i *opportunityLineItemImpl) BatchCreate(ctx context.Context, items []*OpportunityLineItem) error {
	return i.db.WithContext(ctx).CreateInBatches(items, 100).Error
}

func (i *opportunityLineItemImpl) DeleteItems(ctx context.Context, opportunityID string, productIDs ...string) (
	[]*OpportunityLineItem, error) {
	var items []*OpportunityLineItem
	db := i.db.WithContext(ctx).Clauses(clause.Returning{}).Where("opportunity_id = ?", opportunityID)
	if len(productIDs) > 0 {
		db = db.Where("product_id IN ?", productIDs)
	}
	if err := db.Delete(&items).Error; err != nil {
		return nil, err
	}

	return items, nil
}
