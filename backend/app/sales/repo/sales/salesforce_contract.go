package sales

import (
	"context"
	"time"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/sales/repo/salesforce"
)

type SalesforceContract struct {
	ID                  string     `gorm:"column:id"`
	SalesforceAccountID *string    `gorm:"column:salesforce_account_id"`
	StartDate           *string    `gorm:"column:start_date"`
	Description         *string    `gorm:"column:description"`
	Term                *int32     `gorm:"column:term"`
	Link                *string    `gorm:"column:link"`
	Type                *string    `gorm:"column:type"`
	CreatedAt           *time.Time `gorm:"column:created_at"`
	UpdatedAt           *time.Time `gorm:"column:updated_at"`
}

func (*SalesforceContract) TableName() string {
	return "moego_sales.public.salesforce_contract"
}

func (c *SalesforceContract) ToSalesforceEntity() *salesforce.Contract {
	return &salesforce.Contract{
		ID:          &c.ID,
		AccountID:   c.SalesforceAccountID,
		StartDate:   c.StartDate,
		Description: c.Description,
		Term:        c.Term,
		Link:        c.<PERSON>,
		Type:        c.Type,
	}
}

type SalesforceContractReadWriter interface {
	Get(ctx context.Context, id string) (*SalesforceContract, error)
	Save(ctx context.Context, contract *SalesforceContract) error
}

type salesforceContractImpl struct {
	db *gorm.DB
}

func NewSalesforceContractRW() SalesforceContractReadWriter {
	return &salesforceContractImpl{
		db: NewDB(),
	}
}

func (i *salesforceContractImpl) Get(ctx context.Context, id string) (*SalesforceContract, error) {
	op := &SalesforceContract{}
	if err := i.db.WithContext(ctx).Where("id = ?", id).First(op).Error; err != nil {
		return nil, err
	}

	return op, nil
}

func (i *salesforceContractImpl) Save(ctx context.Context, contract *SalesforceContract) error {
	return i.db.WithContext(ctx).Create(contract).Error
}
