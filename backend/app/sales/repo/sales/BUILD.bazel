load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "sales",
    srcs = [
        "contract.go",
        "contract_template.go",
        "db.go",
        "moego_pay_custom_fee_approval.go",
        "opportunity.go",
        "opportunity_line_item.go",
        "page.go",
        "salesforce_contract.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/sales/repo/sales",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/sales/repo/salesforce",
        "//backend/app/sales/utils",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/framework/log",
        "@io_gorm_datatypes//:datatypes",
        "@io_gorm_gorm//:gorm",
        "@io_gorm_gorm//clause",
    ],
)
