// Code generated by MockGen. DO NOT EDIT.
// Source: ./salesforce/client.go
//
// Generated by this command:
//
//	mockgen -source=./salesforce/client.go -destination=mock/./salesforce/client_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	salesforce "github.com/MoeGolibrary/moego/backend/app/sales/repo/salesforce"
	gomock "go.uber.org/mock/gomock"
)

// MockClient is a mock of Client interface.
type MockClient struct {
	ctrl     *gomock.Controller
	recorder *MockClientMockRecorder
	isgomock struct{}
}

// MockClientMockRecorder is the mock recorder for MockClient.
type MockClientMockRecorder struct {
	mock *MockClient
}

// NewMockClient creates a new mock instance.
func NewMockClient(ctrl *gomock.Controller) *MockClient {
	mock := &MockClient{ctrl: ctrl}
	mock.recorder = &MockClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClient) EXPECT() *MockClientMockRecorder {
	return m.recorder
}

// CreateContract mocks base method.
func (m *MockClient) CreateContract(ctx context.Context, contract *salesforce.Contract) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateContract", ctx, contract)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateContract indicates an expected call of CreateContract.
func (mr *MockClientMockRecorder) CreateContract(ctx, contract any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateContract", reflect.TypeOf((*MockClient)(nil).CreateContract), ctx, contract)
}

// CreateOpportunityLineItems mocks base method.
func (m *MockClient) CreateOpportunityLineItems(ctx context.Context, items []*salesforce.OpportunityLineItem) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOpportunityLineItems", ctx, items)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateOpportunityLineItems indicates an expected call of CreateOpportunityLineItems.
func (mr *MockClientMockRecorder) CreateOpportunityLineItems(ctx, items any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOpportunityLineItems", reflect.TypeOf((*MockClient)(nil).CreateOpportunityLineItems), ctx, items)
}

// DeleteOpportunityLineItems mocks base method.
func (m *MockClient) DeleteOpportunityLineItems(ctx context.Context, ids []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteOpportunityLineItems", ctx, ids)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteOpportunityLineItems indicates an expected call of DeleteOpportunityLineItems.
func (mr *MockClientMockRecorder) DeleteOpportunityLineItems(ctx, ids any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteOpportunityLineItems", reflect.TypeOf((*MockClient)(nil).DeleteOpportunityLineItems), ctx, ids)
}

// GetAccountByEmail mocks base method.
func (m *MockClient) GetAccountByEmail(ctx context.Context, email string) (*salesforce.Account, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountByEmail", ctx, email)
	ret0, _ := ret[0].(*salesforce.Account)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountByEmail indicates an expected call of GetAccountByEmail.
func (mr *MockClientMockRecorder) GetAccountByEmail(ctx, email any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountByEmail", reflect.TypeOf((*MockClient)(nil).GetAccountByEmail), ctx, email)
}

// GetOpportunity mocks base method.
func (m *MockClient) GetOpportunity(ctx context.Context, id string) (*salesforce.Opportunity, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOpportunity", ctx, id)
	ret0, _ := ret[0].(*salesforce.Opportunity)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOpportunity indicates an expected call of GetOpportunity.
func (mr *MockClientMockRecorder) GetOpportunity(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOpportunity", reflect.TypeOf((*MockClient)(nil).GetOpportunity), ctx, id)
}

// GetProduct mocks base method.
func (m *MockClient) GetProduct(ctx context.Context, name string) *salesforce.Product {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProduct", ctx, name)
	ret0, _ := ret[0].(*salesforce.Product)
	return ret0
}

// GetProduct indicates an expected call of GetProduct.
func (mr *MockClientMockRecorder) GetProduct(ctx, name any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProduct", reflect.TypeOf((*MockClient)(nil).GetProduct), ctx, name)
}

// ListOpportunityLineItems mocks base method.
func (m *MockClient) ListOpportunityLineItems(ctx context.Context, opportunityID string, productIDs ...string) ([]*salesforce.OpportunityLineItem, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, opportunityID}
	for _, a := range productIDs {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListOpportunityLineItems", varargs...)
	ret0, _ := ret[0].([]*salesforce.OpportunityLineItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListOpportunityLineItems indicates an expected call of ListOpportunityLineItems.
func (mr *MockClientMockRecorder) ListOpportunityLineItems(ctx, opportunityID any, productIDs ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, opportunityID}, productIDs...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListOpportunityLineItems", reflect.TypeOf((*MockClient)(nil).ListOpportunityLineItems), varargs...)
}

// UpdateAccountEmail mocks base method.
func (m *MockClient) UpdateAccountEmail(ctx context.Context, id, email string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAccountEmail", ctx, id, email)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAccountEmail indicates an expected call of UpdateAccountEmail.
func (mr *MockClientMockRecorder) UpdateAccountEmail(ctx, id, email any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAccountEmail", reflect.TypeOf((*MockClient)(nil).UpdateAccountEmail), ctx, id, email)
}

// UpdateOpportunity mocks base method.
func (m *MockClient) UpdateOpportunity(ctx context.Context, op *salesforce.Opportunity) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOpportunity", ctx, op)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOpportunity indicates an expected call of UpdateOpportunity.
func (mr *MockClientMockRecorder) UpdateOpportunity(ctx, op any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOpportunity", reflect.TypeOf((*MockClient)(nil).UpdateOpportunity), ctx, op)
}
