// Code generated by MockGen. DO NOT EDIT.
// Source: ./sales/salesforce_contract.go
//
// Generated by this command:
//
//	mockgen -source=./sales/salesforce_contract.go -destination=mock/./sales/salesforce_contract_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	sales "github.com/MoeGolibrary/moego/backend/app/sales/repo/sales"
	gomock "go.uber.org/mock/gomock"
)

// MockSalesforceContractReadWriter is a mock of SalesforceContractReadWriter interface.
type MockSalesforceContractReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockSalesforceContractReadWriterMockRecorder
	isgomock struct{}
}

// MockSalesforceContractReadWriterMockRecorder is the mock recorder for MockSalesforceContractReadWriter.
type MockSalesforceContractReadWriterMockRecorder struct {
	mock *MockSalesforceContractReadWriter
}

// NewMockSalesforceContractReadWriter creates a new mock instance.
func NewMockSalesforceContractReadWriter(ctrl *gomock.Controller) *MockSalesforceContractReadWriter {
	mock := &MockSalesforceContractReadWriter{ctrl: ctrl}
	mock.recorder = &MockSalesforceContractReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSalesforceContractReadWriter) EXPECT() *MockSalesforceContractReadWriterMockRecorder {
	return m.recorder
}

// Get mocks base method.
func (m *MockSalesforceContractReadWriter) Get(ctx context.Context, id string) (*sales.SalesforceContract, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, id)
	ret0, _ := ret[0].(*sales.SalesforceContract)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockSalesforceContractReadWriterMockRecorder) Get(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockSalesforceContractReadWriter)(nil).Get), ctx, id)
}

// Save mocks base method.
func (m *MockSalesforceContractReadWriter) Save(ctx context.Context, contract *sales.SalesforceContract) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", ctx, contract)
	ret0, _ := ret[0].(error)
	return ret0
}

// Save indicates an expected call of Save.
func (mr *MockSalesforceContractReadWriterMockRecorder) Save(ctx, contract any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockSalesforceContractReadWriter)(nil).Save), ctx, contract)
}
