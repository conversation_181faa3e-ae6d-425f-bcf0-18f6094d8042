load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "sales",
    srcs = [
        "contract_mock.go",
        "contract_template_mock.go",
        "moego_pay_custom_fee_approval_mock.go",
        "opportunity_line_item_mock.go",
        "opportunity_mock.go",
        "page_mock.go",
        "salesforce_contract_mock.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/sales/repo/mock/sales",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/sales/repo/sales",
        "@io_gorm_gorm//:gorm",
        "@org_uber_go_mock//gomock",
    ],
)
