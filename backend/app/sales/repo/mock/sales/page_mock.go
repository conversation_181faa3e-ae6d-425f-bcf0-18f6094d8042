// Code generated by MockGen. DO NOT EDIT.
// Source: ./sales/page.go
//
// Generated by this command:
//
//	mockgen -source=./sales/page.go -destination=mock/./sales/page_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	sales "github.com/MoeGolibrary/moego/backend/app/sales/repo/sales"
	gomock "go.uber.org/mock/gomock"
	gorm "gorm.io/gorm"
)

// MockPage is a mock of Page interface.
type MockPage[T any] struct {
	ctrl     *gomock.Controller
	recorder *MockPageMockRecorder[T]
	isgomock struct{}
}

// MockPageMockRecorder is the mock recorder for MockPage.
type MockPageMockRecorder[T any] struct {
	mock *MockPage[T]
}

// NewMockPage creates a new mock instance.
func NewMockPage[T any](ctrl *gomock.Controller) *MockPage[T] {
	mock := &MockPage[T]{ctrl: ctrl}
	mock.recorder = &MockPageMockRecorder[T]{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPage[T]) EXPECT() *MockPageMockRecorder[T] {
	return m.recorder
}

// Apply mocks base method.
func (m *MockPage[T]) Apply(sql *gorm.DB) *gorm.DB {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Apply", sql)
	ret0, _ := ret[0].(*gorm.DB)
	return ret0
}

// Apply indicates an expected call of Apply.
func (mr *MockPageMockRecorder[T]) Apply(sql any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Apply", reflect.TypeOf((*MockPage[T])(nil).Apply), sql)
}

// NextPage mocks base method.
func (m *MockPage[T]) NextPage(items []T) sales.Page[T] {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NextPage", items)
	ret0, _ := ret[0].(sales.Page[T])
	return ret0
}

// NextPage indicates an expected call of NextPage.
func (mr *MockPageMockRecorder[T]) NextPage(items any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NextPage", reflect.TypeOf((*MockPage[T])(nil).NextPage), items)
}

// Token mocks base method.
func (m *MockPage[T]) Token() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Token")
	ret0, _ := ret[0].(string)
	return ret0
}

// Token indicates an expected call of Token.
func (mr *MockPageMockRecorder[T]) Token() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Token", reflect.TypeOf((*MockPage[T])(nil).Token))
}
