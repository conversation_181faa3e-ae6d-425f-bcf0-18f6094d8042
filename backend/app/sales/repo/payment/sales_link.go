package payment

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/http"
)

type PlatformSalesRecord struct {
	Code          string `json:"code"`
	AccountID     int64  `json:"accountId"`
	CompanyID     int64  `json:"companyId"`
	Email         string `json:"email"`
	Creator       string `json:"creator"`
	OpportunityID string `json:"opportunityId"`
}

type GetSalesLinkParams struct {
	Code string `json:"code"`
}

type GetSalesLinkResult struct {
	Records []PlatformSalesRecord `json:"view"`
	Total   int64                 `json:"total"`
}

type SalesLinkClient interface {
	GetSalesLink(ctx context.Context, code string) (*PlatformSalesRecord, error)
}

type salesLinkClientImpl struct {
	client http.Client
}

func (s salesLinkClientImpl) GetSalesLink(ctx context.Context, code string) (*PlatformSalesRecord, error) {
	params := GetSalesLinkParams{Code: code}
	var result GetSalesLinkResult
	err := s.client.Post(ctx, "/service/payment/platform/sales/list", params, &result)
	if err != nil {
		return nil, err
	}

	if len(result.Records) == 0 {
		return nil, nil
	}

	return &result.Records[0], nil
}

func NewSalesLinkClient() SalesLinkClient {
	return &salesLinkClientImpl{
		client: GetClient(),
	}
}
