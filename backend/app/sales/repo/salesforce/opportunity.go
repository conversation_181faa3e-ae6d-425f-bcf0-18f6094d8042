package salesforce

import (
	"context"
	"fmt"

	salesutils "github.com/MoeGolibrary/moego/backend/app/sales/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/client"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type Opportunity struct {
	ID                    *string  `json:"Id,omitempty"`
	AccountID             *string  `json:"AccountId,omitempty"`
	Email                 *string  `json:"MoeGo_Log_in_Email__c,omitempty"`
	Tier                  *string  `json:"Expected_Tier__c,omitempty"`
	TerminalPercentage    *float64 `json:"Terminal_Percentage__c,omitempty"`
	TerminalFixed         *float64 `json:"Terminal_Fixed__c,omitempty"`
	NonTerminalPercentage *float64 `json:"Non_terminal_Percentage__c,omitempty"`
	NonTerminalFixed      *float64 `json:"Non_terminal_Fixed__c,omitempty"`
	MinVolume             *float64 `json:"Minimum_Processing_Volume_Commitment__c,omitempty"`
	SPIF                  *float64 `json:"SPIF__c,omitempty"`
	PriceBookID           *string  `json:"Pricebook2Id,omitempty"`
}

const pathFmt = "/services/data/v59.0/sobjects/Opportunity/%s"

func (i *impl) GetOpportunity(ctx context.Context, id string) (*Opportunity, error) {
	headers, err := i.getHeaders(ctx)
	if err != nil {
		return nil, err
	}

	opportunity := &Opportunity{}
	path := fmt.Sprintf(pathFmt, id)
	if err := i.client.Get(ctx, path, opportunity, client.WithReqHead(headers)); err != nil {
		return nil, err
	}

	return opportunity, nil
}

func (i *impl) UpdateOpportunity(ctx context.Context, op *Opportunity) error {
	headers, err := i.getHeaders(ctx)
	if err != nil {
		return err
	}

	// pricebook id 是固定的
	op.PriceBookID = &pricebookID

	// id 不能被更新，提取出来
	id := *op.ID
	op.ID = nil
	// account id 也不应该更新
	op.AccountID = nil

	log.InfoContextf(ctx, "update opportunity body: %s", salesutils.ToJSON(op))

	path := fmt.Sprintf(pathFmt, id)

	return i.client.Patch(ctx, path, op, nil, client.WithReqHead(headers))
}
