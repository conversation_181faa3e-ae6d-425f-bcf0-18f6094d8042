package salesforce

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	salesutils "github.com/MoeGolibrary/moego/backend/app/sales/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type OpportunityLineItem struct {
	ID            *string `json:"Id,omitempty"`
	OpportunityID string  `json:"OpportunityId,omitempty"`
	ProductID     string  `json:"Product2Id,omitempty"`
	Quantity      int32   `json:"Quantity,omitempty"`
	// 单价（折前）
	UnitPrice float64 `json:"UnitPrice,omitempty"`
	// 单价（折前），等同于 UnitPrice，该字段仅做查询用，不应当更新
	ListPrice *float64 `json:"ListPrice,omitempty"`
	// 总价（折后）= UnitPrice * Quantity * (1 - Discount%)，该字段仅做查询用，不应当更新，salesforce 会自动计算
	TotalPrice *float64 `json:"TotalPrice,omitempty"`
	// 折扣（百分比），比如 5 表示 5%
	Discount *float64 `json:"Discount,omitempty"`
}

// 自定义结构体用于辅助解码
type opportunityLineItemAlias OpportunityLineItem

func (o *OpportunityLineItem) UnmarshalJSON(data []byte) error {
	// 创建一个别名结构体，用于解码 JSON
	aux := struct {
		Quantity interface{} `json:"Quantity"`
		*opportunityLineItemAlias
	}{
		opportunityLineItemAlias: (*opportunityLineItemAlias)(o),
	}

	// 解码到辅助结构体
	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}

	// 处理 Quantity 字段（允许 float64 或整型）
	switch v := aux.Quantity.(type) {
	case float64:
		o.Quantity = int32(v)
	case int32:
		o.Quantity = v
	case int64:
		o.Quantity = int32(v)
	case nil:
		// 留空即可，不设置
	default:
		return fmt.Errorf("invalid type for Quantity: %T", v)
	}

	return nil
}

func (i *impl) ListOpportunityLineItems(ctx context.Context, opportunityID string, productIDs ...string) (
	[]*OpportunityLineItem, error) {
	query := fmt.Sprintf(
		"q=SELECT+%s+FROM+OpportunityLineItem+WHERE+OpportunityId='%s'",
		strings.Join(salesutils.ParseStructJSONTagNames(OpportunityLineItem{}), ","),
		opportunityID)

	if len(productIDs) > 0 {
		query += fmt.Sprintf("+AND+Product2Id+IN+('%s')", strings.Join(productIDs, "','"))
	}

	result := &queryResult[*OpportunityLineItem]{}

	if err := i.Query(ctx, query, result); err != nil {
		return nil, err
	}

	return result.Records, nil
}

func (i *impl) DeleteOpportunityLineItems(ctx context.Context, ids []string) error {
	var subReqs []*subRequest
	for _, id := range ids {
		subReq := &subRequest{
			Method: "DELETE",
			URL:    "/services/data/v63.0/sobjects/OpportunityLineItem/" + id,
		}
		subReqs = append(subReqs, subReq)
	}

	respBody, err := i.batch(ctx, &batchRequestBody{
		Requests: subReqs,
	})

	log.InfoContextf(ctx, "DeleteOpportunityLineItems, respBody: %s", salesutils.ToJSON(respBody))

	return err
}

func (i *impl) CreateOpportunityLineItems(ctx context.Context, items []*OpportunityLineItem) error {
	var subReqs []*subRequest
	for _, item := range items {
		subReq := &subRequest{
			Method:    "POST",
			RichInput: item,
			URL:       "/services/data/v63.0/sobjects/OpportunityLineItem",
		}
		subReqs = append(subReqs, subReq)
	}

	respBody, err := i.batch(ctx, &batchRequestBody{
		Requests: subReqs,
	})

	log.InfoContextf(ctx, "CreateOpportunityLineItems, respBody: %s", salesutils.ToJSON(respBody))

	return err
}
