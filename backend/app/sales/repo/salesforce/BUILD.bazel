load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "salesforce",
    srcs = [
        "account.go",
        "batch.go",
        "client.go",
        "contract.go",
        "opportunity.go",
        "opportunity_line_item.go",
        "product.go",
        "query.go",
        "response.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/sales/repo/salesforce",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/sales/config",
        "//backend/app/sales/utils",
        "//backend/common/rpc/framework/client",
        "//backend/common/rpc/framework/http",
        "//backend/common/rpc/framework/log",
    ],
)

go_test(
    name = "salesforce_test",
    srcs = ["client_test.go"],
    embed = [":salesforce"],
    deps = [
        "//backend/app/sales/config",
        "//backend/app/sales/utils",
        "//backend/common/utils/pointer",
        "@com_github_shopspring_decimal//:decimal",
        "@com_github_stretchr_testify//suite",
    ],
)
