package salesforce

import (
	"context"
	"fmt"
	"strings"

	salesutils "github.com/MoeGolibrary/moego/backend/app/sales/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/client"
)

type Account struct {
	ID    *string `json:"Id,omitempty"`
	Email *string `json:"MoeGo_Log_in_Email__c,omitempty"`
	Name  *string `json:"Name,omitempty"`
}

const accountPathFmt = "/services/data/v60.0/sobjects/Account/%s"

func (i *impl) GetAccountByEmail(ctx context.Context, email string) (*Account, error) {
	query := fmt.Sprintf("q=SELECT+%s+FROM+Account+WHERE+MoeGo_Log_in_Email__c='%s'",
		strings.Join(salesutils.ParseStructJSONTagNames(Account{}), ","),
		email,
	)
	result := &queryResult[*Account]{}

	if err := i.Query(ctx, query, result); err != nil {
		return nil, err
	}

	accounts := result.Records
	if len(accounts) == 0 {
		return nil, nil
	}

	return accounts[0], nil
}

func (i *impl) UpdateAccountEmail(ctx context.Context, id, email string) error {
	headers, err := i.getHeaders(ctx)
	if err != nil {
		return err
	}

	body := &Account{
		Email: &email,
	}

	path := fmt.Sprintf(accountPathFmt, id)

	return i.client.Patch(ctx, path, body, nil, client.WithReqHead(headers))
}
