package salesforce

import (
	"context"
	"os"
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/suite"

	"github.com/MoeGolibrary/moego/backend/app/sales/config"
	salesutils "github.com/MoeGolibrary/moego/backend/app/sales/utils"
	"github.com/MoeGolibrary/moego/backend/common/utils/pointer"
)

type SalesforceTestSuite struct {
	suite.Suite
	client               Client
	opportunityID        string
	opportunityLineItems []*OpportunityLineItem
	ctx                  context.Context
}

func (s *SalesforceTestSuite) SetupSuite() {
	wd, _ := os.Getwd()
	s.T().Logf("wd: %s", wd)
	config.Init("../../config")
	s.client = New(config.GetCfg().Salesforce)
	s.opportunityID = "006WE00000NxdhdYAB"
	s.opportunityLineItems = []*OpportunityLineItem{
		{
			// Grooming Mobile - Ultimate
			ProductID:     "01tHs00000B3EmGIAV",
			OpportunityID: s.opportunityID,
			Quantity:      2,
			UnitPrice:     159.0,
			Discount:      pointer.Get(10.0),
		},
		{
			// Grooming Salon - Ultimate
			ProductID:     "01tHs00000B3EoHIAV",
			OpportunityID: s.opportunityID,
			Quantity:      3,
			UnitPrice:     239.0,
			Discount:      pointer.Get(10.0),
		},
		{
			// Reader M2
			ProductID:     "01tHs00000BwmrQIAR",
			OpportunityID: s.opportunityID,
			Quantity:      2,
			UnitPrice:     59.0,
			Discount:      pointer.Get(5.0),
		},
		{
			// BBPOS WisePOS E
			ProductID:     "01tHs00000BwmrBIAR",
			OpportunityID: s.opportunityID,
			Quantity:      3,
			UnitPrice:     249.0,
			Discount:      pointer.Get(5.0),
		},
	}
	s.ctx = context.Background()
}

func (s *SalesforceTestSuite) TestGetOpportunity() {
	// Test GetOpportunity with a real request
	// 使用一个已知存在的Opportunity ID进行测试
	opportunity, err := s.client.GetOpportunity(s.ctx, s.opportunityID)
	s.Require().NoError(err)

	// Log the opportunity details
	s.T().Logf("Opportunity: %v", salesutils.ToJSON(opportunity))
}

func (s *SalesforceTestSuite) TestCreateAndDeleteOpportunityLineItems() {
	require := s.Require()

	// 查询是否有存在的 item
	items, err := s.client.ListOpportunityLineItems(s.ctx, s.opportunityID)
	require.NoError(err)

	// 删除已存在的 item，避免干扰测试
	if len(items) > 0 {
		s.T().Logf("Deleting existing OpportunityLineItems: %+v", items)
		ids := make([]string, len(items))
		for i, item := range items {
			ids[i] = *item.ID
		}

		err = s.client.DeleteOpportunityLineItems(s.ctx, ids)
		require.NoError(err)
	}

	// 插入测试数据
	err = s.client.CreateOpportunityLineItems(s.ctx, s.opportunityLineItems)
	require.NoError(err)

	// 查询数据
	items, err = s.client.ListOpportunityLineItems(s.ctx, s.opportunityID)
	require.NoError(err)

	// 检查数据是否一致
	s.T().Logf("OpportunityLineItems: %v", salesutils.ToJSON(items))
	require.Len(items, len(s.opportunityLineItems), "item count mismatch")

	itemMap := map[string]*OpportunityLineItem{}
	for _, item := range items {
		itemMap[item.ProductID] = item
	}

	for _, expected := range s.opportunityLineItems {
		actual, ok := itemMap[expected.ProductID]
		require.True(ok, "ProductID not found: %s", expected.ProductID)

		require.Equal(expected.OpportunityID, actual.OpportunityID, "OpportunityID mismatch")
		require.Equal(expected.Quantity, actual.Quantity, "Quantity mismatch")
		require.Equal(expected.Discount, actual.Discount, "Discount mismatch")
		require.Equal(expected.UnitPrice, actual.UnitPrice, "UnitPrice mismatch")
		totalPrice, _ := decimal.NewFromFloat(expected.UnitPrice).
			Mul(decimal.NewFromInt32(expected.Quantity)).
			Mul(decimal.NewFromInt(100).
				Sub(decimal.NewFromFloat(*expected.Discount)).
				Div(decimal.NewFromInt(100))).
			RoundBank(2).Float64()
		require.Equal(totalPrice, *actual.TotalPrice, "TotalPrice mismatch")
	}

	// teardown
	ids := make([]string, len(items))
	for i, item := range items {
		ids[i] = *item.ID
	}
	err = s.client.DeleteOpportunityLineItems(s.ctx, ids)
	require.NoError(err)
}

func TestSalesforceTestSuite(t *testing.T) {
	suite.Run(t, new(SalesforceTestSuite))
}
