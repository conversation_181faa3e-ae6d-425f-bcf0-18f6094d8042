package salesforce

import (
	"context"
	"errors"

	salesutils "github.com/MoeGolibrary/moego/backend/app/sales/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/client"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type Contract struct {
	ID          *string `json:"Id,omitempty"`
	AccountID   *string `json:"AccountId,omitempty"`
	StartDate   *string `json:"StartDate,omitempty"`
	Description *string `json:"Description,omitempty"`
	Term        *int32  `json:"ContractTerm,omitempty"`
	Link        *string `json:"Contract_Link__c,omitempty"`
	Type        *string `json:"Contract_Type__c,omitempty"`
}

const createContractPath = "/services/data/v59.0/sobjects/Contract"

func (i *impl) CreateContract(ctx context.Context, contract *Contract) error {
	headers, err := i.getHeaders(ctx)
	if err != nil {
		return err
	}

	log.InfoContextf(ctx, "create contract body: %s", salesutils.ToJSON(contract))

	resp := &CreateResponse{}
	if err := i.client.Post(ctx, createContractPath, contract, resp, client.WithReqHead(headers)); err != nil {
		return err
	}

	if !resp.IsSuccess() {
		msg := resp.GetMergedErrorMessage()
		log.ErrorContextf(ctx, msg)

		return errors.New(msg)
	}

	contract.ID = resp.ID

	return nil
}
