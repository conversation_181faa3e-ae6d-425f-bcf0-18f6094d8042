package salesutils

import (
	"os"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

const (
	MIS = "mis"
	GO  = "go"
)

func GetDomainHost(domain string) string {
	env := os.Getenv("MOEGO_ENVIRONMENT")
	if len(env) == 0 {
		env = "local"
		log.Warnf("MOEGO_ENVIRONMENT is not set, using default value: %s", env)
	}

	switch env {
	case "production":
		return domain + ".moego.pet"
	case "staging":
		return domain + ".s1.moego.dev"
	default:
		return domain + ".t2.moego.dev"
	}
}
