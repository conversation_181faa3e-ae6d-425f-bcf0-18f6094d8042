package salesutils

import (
	"os"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

const (
	// domains
	MIS = "mis"
	GO  = "go"

	// environments
	Local      = "local"
	Staging    = "staging"
	Production = "production"
	Testing    = "testing"
)

func GetEnv() string {
	env := os.Getenv("MOEGO_ENVIRONMENT")
	if len(env) == 0 {
		env = Local
		log.Warnf("MOEGO_ENVIRONMENT is not set, using default value: %s", env)
	}

	return env
}

func GetDomainHost(domain string) string {
	switch GetEnv() {
	case Production:
		return domain + ".moego.pet"
	case Staging:
		return domain + ".s1.moego.dev"
	default:
		return domain + ".t2.moego.dev"
	}
}

func IsProduction() bool {
	return GetEnv() == Production
}
