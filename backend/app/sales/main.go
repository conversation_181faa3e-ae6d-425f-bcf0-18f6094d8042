package main

import (
	_ "time/tzdata"

	"github.com/MoeGolibrary/moego/backend/app/sales/config"
	"github.com/MoeGolibrary/moego/backend/app/sales/service"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/opentelemetry"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/recovery"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/validation"
	rpc "github.com/MoeGolibrary/moego/backend/common/rpc/framework"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	salespb "github.com/MoeGolibrary/moego/backend/proto/sales/v1"
)

func main() {
	s := rpc.NewServer()
	config.Init("./config")

	// 这里需要注册grpc服务
	grpc.Register(s, &salespb.SalesService_ServiceDesc, service.NewSalesService())
	grpc.Register(s, &salespb.MoegoPayContractService_ServiceDesc, service.NewMoegoPayContractService())
	grpc.Register(s, &salespb.MoegoPayCustomFeeApprovalService_ServiceDesc,
		service.NewMoegoPayCustomFeeApprovalService())
	grpc.Register(s, &salespb.AnnualContractService_ServiceDesc, service.NewAnnualContractService())
	grpc.Register(s, &salespb.PlatformSalesService_ServiceDesc, service.NewPlatformSalesService())

	if err := s.Serve(); err != nil {
		log.Fatalf("failed to serve: %v", err)
	}
}
