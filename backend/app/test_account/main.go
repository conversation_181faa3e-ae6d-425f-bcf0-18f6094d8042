package main

import (
	"time"

	"github.com/MoeGolibrary/moego/backend/app/test_account/service"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/opentelemetry"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/recovery"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/validation"
	rpc "github.com/MoeGolibrary/moego/backend/common/rpc/framework"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	testaccount "github.com/MoeGolibrary/moego/backend/proto/test_account/v1"
)

func main() {
	// 设置全局时区为 UTC
	time.Local = time.UTC

	s := rpc.NewServer()

	// 这里需要注册grpc 的service
	grpc.Register(s, &testaccount.TestAccountService_ServiceDesc, service.NewTestAccountService())

	if err := s.Serve(); err != nil {
		log.Fatalf("failed to serve: %v", err)
	}
}
