load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "entity",
    srcs = ["entity.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/test_account/logic/testaccount/entity",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/test_account/repo/testaccount/entity",
        "//backend/proto/test_account/v1:test_account",
    ],
)

go_test(
    name = "entity_test",
    srcs = ["entity_test.go"],
    embed = [":entity"],
    deps = [
        "//backend/app/test_account/repo/testaccount/entity",
        "//backend/common/utils/pointer",
        "//backend/proto/test_account/v1:test_account",
    ],
)
