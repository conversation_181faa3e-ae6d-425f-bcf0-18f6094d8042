load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "account",
    srcs = ["account.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/test_account/repo/account",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/test_account/repo/account/entity",
        "//backend/app/test_account/utils",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/framework/log",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/service/account/v1:account",
    ],
)
