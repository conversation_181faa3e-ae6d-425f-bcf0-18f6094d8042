package organization

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strconv"

	"github.com/bytedance/sonic"

	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	organizationsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1"
	"github.com/MoeGolibrary/moego/backend/app/test_account/repo/account/entity"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/utils/pointer"
)

const (
	MaxCredit            = *********
	MessageInPlanIsRight = 2
)

type ReadWriter interface {
	InitCompany(ctx context.Context, account *entity.Account, regionCode string) (int64, int64, error)
	UpdateSmsAndEmailCredit(ctx context.Context, companyID int64, hasSmsCredit, hasEmailCredit bool) error
}

type impl struct {
	company organizationsvcpb.CompanyServiceClient
}

func New() ReadWriter {
	return &impl{
		company: grpc.NewClient("moego-svc-organization", organizationsvcpb.NewCompanyServiceClient),
	}
}

func (i *impl) InitCompany(ctx context.Context, account *entity.Account, regionCode string) (int64, int64, error) {
	country, ok := countries[regionCode]
	if !ok {
		return 0, 0, fmt.Errorf("invalid region code")
	}

	// 1. create company
	companyID, businessID, err := i.createCompany(ctx, account, country)
	if err != nil {
		return 0, 0, err
	}

	// 2. update subscription
	err = i.updateSubscription(ctx, companyID)
	if err != nil {
		return 0, 0, err
	}

	return companyID, businessID, nil
}

func (i *impl) createCompany(ctx context.Context, account *entity.Account, country *Country) (int64, int64, error) {
	sourceType := organizationpb.SourceType_INTERNET_SEARCH
	resp, err := i.company.CreateCompany(ctx, &organizationsvcpb.CreateCompanyRequest{
		AccountId: account.ID,
		Location: &organizationpb.CreateLocationDef{
			Name:         fmt.Sprintf("%s %s", account.FirstName, account.LastName),
			BusinessType: organizationpb.BusinessType_HYBRID,
			ContactEmail: account.Email,
			Address: &organizationpb.AddressDef{
				Country: pointer.Get(country.Country.Code),
			},
			SourceFrom: organizationpb.BusinessSourceFromType_WEB_DESKTOP,
		},
		Country:        &country.Country,
		TimeZone:       &country.TimeZone,
		PhoneNumber:    "**********",
		CurrencyCode:   country.CurrencyCode,
		CurrencySymbol: country.CurrencySymbol,
		CompanyType:    pointer.Get(int32(organizationpb.CompanyModel_HYBRID)),
		Source:         &sourceType,
	})

	if err != nil {
		return 0, 0, err
	}

	return resp.GetCompanyId(), resp.GetBusinessId(), nil
}

// ignore magic number
//
//nolint:mnd
func (i *impl) updateSubscription(_ context.Context, companyID int64) error {
	// 设置订阅套餐
	subscription := map[string]interface{}{
		"companyId":             companyID,
		"stripeSubscriptionsId": "moego_test",
		"level":                 1201,
		"beginDate":             **********,
		"endDate":               **********,
		"currentPlanId":         103,
		"autoRenew":             1,
		"nextPlanId":            103,
		"chargeStatus":          0,
		"chargeMsg":             "",
		"packageMsgNum":         0,
		"packageMsgUsedNum":     0,
		"buyMsgRemainNum":       0,
		"businessNum":           1,
		"updateTime":            0,
		"createTime":            0,
		"chargeFailedTime":      0,
		"additionalStaffNum":    0,
	}

	// transfer to io.Reader
	payload, err := sonic.Marshal(subscription)
	if err != nil {
		return err
	}

	req, err := http.NewRequest(http.MethodPost,
		"http://moego-service-payment:9204/service/payment/subscription/createOrUpdateCompanyPermissionState",
		bytes.NewBuffer(payload))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")

	_, err = http.DefaultClient.Do(req)
	if err != nil {
		return err
	}

	// 更新 company 信息
	company := map[string]interface{}{
		"id":          companyID,
		"locationNum": 1,
		"vansNum":     2,
		"staffNum":    -1,
		"level":       1201,
	}

	payload, err = sonic.Marshal(company)
	if err != nil {
		return err

	}

	req, err = http.NewRequest(http.MethodPost,
		"http://moego-service-business:9203/service/business/business/updateCompanyBlindly",
		bytes.NewBuffer(payload))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")

	_, err = http.DefaultClient.Do(req)
	if err != nil {
		return err
	}

	return nil
}

func (i *impl) UpdateSmsAndEmailCredit(_ context.Context, companyID int64, hasSmsCredit, hasEmailCredit bool) error {
	smsCredit := 0
	emailCredit := 0
	if hasSmsCredit {
		smsCredit = MaxCredit
	}
	if hasEmailCredit {
		emailCredit = MaxCredit
	}

	// 更新订阅套餐上的 sms 数量
	subscription := map[string]interface{}{
		"companyId":         companyID,
		"packageMsgNum":     smsCredit,
		"packageMsgUsedNum": MessageInPlanIsRight,
	}

	payload, err := sonic.Marshal(subscription)
	if err != nil {
		return err
	}

	req, err := http.NewRequest(http.MethodPost,
		"http://moego-service-payment:9204/service/payment/subscription/createOrUpdateCompanyPermissionState",
		bytes.NewBuffer(payload))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")

	_, err = http.DefaultClient.Do(req)
	if err != nil {
		return err
	}

	// 更新 message billing
	query := &url.Values{
		// 参考 moego-server-payment 的代码, 这里 account id 传 1 即可
		"accountId": []string{"1"},
		"companyId": []string{strconv.FormatInt(companyID, 10)},
		// email 数量
		"subscriptionEmailAmount": []string{strconv.Itoa(emailCredit)},
		// SMS 数量
		"subscriptionAmount": []string{strconv.Itoa(smsCredit)},
		"subscriptionDate":   []string{"1"},
		"qualifyFlag":        []string{"false"},
	}

	req, err = http.NewRequest(http.MethodPost,
		"http://moego-service-message:9205/service/message/control/count?"+query.Encode(), nil)
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")

	_, err = http.DefaultClient.Do(req)
	if err != nil {
		return err
	}

	return nil
}
