package onboarding

import (
	"context"
	"errors"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"go.uber.org/mock/gomock"
	"gorm.io/gorm"

	onboardingrepo "github.com/MoeGolibrary/moego/backend/app/onboarding/repo/gorm"
	onboardingrepomock "github.com/MoeGolibrary/moego/backend/app/onboarding/repo/mock/gorm"
	pawpilotrepomock "github.com/MoeGolibrary/moego/backend/app/onboarding/repo/mock/pawpilot"
	onboardingpb "github.com/MoeGolibrary/moego/backend/proto/onboarding/v1"
)

var (
	companyID  = int64(1)
	businessID = int64(1)
	staffID    = int64(1)
	flowID     = "flow_id"
	stepID     = "test-step"
)

func TestLogic_GetOnboardingProgress(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	<PERSON>vey("Given a valid flow ID", t, func() {
		repo := onboardingrepomock.NewMockOnboardingRepo(ctl)
		pawpilotRepo := pawpilotrepomock.NewMockRepo(ctl)
		logic := NewLogic(repo, pawpilotRepo)
		req := &onboardingpb.GetOnboardingProgressRequest{
			CompanyId:  companyID,
			BusinessId: businessID,
			StaffId:    staffID,
			FlowId:     flowID,
		}

		flowRecord := &onboardingrepo.OnboardingFlow{
			ID:         1,
			CompanyID:  companyID,
			BusinessID: businessID,
			StaffID:    staffID,
			FlowID:     flowID,
		}

		steps := []*onboardingrepo.OnboardingStep{
			{
				StepID:   "step1",
				Status:   onboardingpb.StepState_STEP_STATE_COMPLETED,
				Metadata: []byte(`{}`),
			},
		}

		repo.EXPECT().GetOnboardingFlow(gomock.Any(), companyID, businessID, staffID, flowID).Return(flowRecord, nil)
		repo.EXPECT().GetOnboardingSteps(gomock.Any(), flowRecord.ID).Return(steps, nil)

		Convey("When GetOnboardingProgress is called", func() {
			resp, err := logic.GetOnboardingProgress(context.Background(), req)
			Convey("Then the response should be successful", func() {
				So(err, ShouldBeNil)
				So(resp, ShouldNotBeNil)
				So(resp.FlowId, ShouldEqual, flowID)
				So(len(resp.StepsProgress), ShouldEqual, 1)
				So(resp.StepsProgress[0].StepId, ShouldEqual, "step1")
			})
		})
	})

	Convey("Given an invalid flow ID", t, func() {
		repo := onboardingrepomock.NewMockOnboardingRepo(ctl)
		pawpilotRepo := pawpilotrepomock.NewMockRepo(ctl)
		logic := NewLogic(repo, pawpilotRepo)
		flowID := "invalid-flow"
		req := &onboardingpb.GetOnboardingProgressRequest{
			CompanyId:  companyID,
			BusinessId: businessID,
			StaffId:    staffID,
			FlowId:     flowID,
		}

		repo.EXPECT().GetOnboardingFlow(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), flowID).Return(nil, gorm.ErrRecordNotFound)

		Convey("When GetOnboardingProgress is called", func() {
			resp, err := logic.GetOnboardingProgress(context.Background(), req)

			Convey("Then the response should be an error", func() {
				So(err, ShouldNotBeNil)
				So(resp, ShouldBeNil)
			})
		})
	})

	Convey("Given GetOnboardingFlow returns a generic error", t, func() {
		repo := onboardingrepomock.NewMockOnboardingRepo(ctl)
		pawpilotRepo := pawpilotrepomock.NewMockRepo(ctl)
		logic := NewLogic(repo, pawpilotRepo)
		req := &onboardingpb.GetOnboardingProgressRequest{
			CompanyId:  companyID,
			BusinessId: businessID,
			StaffId:    staffID,
			FlowId:     flowID,
		}
		repo.EXPECT().GetOnboardingFlow(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("db error"))

		Convey("When GetOnboardingProgress is called", func() {
			resp, err := logic.GetOnboardingProgress(context.Background(), req)

			Convey("Then the response should be an error", func() {
				So(err, ShouldNotBeNil)
				So(resp, ShouldBeNil)
			})
		})
	})

	Convey("Given GetOnboardingSteps returns an error", t, func() {
		repo := onboardingrepomock.NewMockOnboardingRepo(ctl)
		pawpilotRepo := pawpilotrepomock.NewMockRepo(ctl)
		logic := NewLogic(repo, pawpilotRepo)
		req := &onboardingpb.GetOnboardingProgressRequest{
			CompanyId:  companyID,
			BusinessId: businessID,
			StaffId:    staffID,
			FlowId:     flowID,
		}
		flowRecord := &onboardingrepo.OnboardingFlow{
			ID:         1,
			CompanyID:  companyID,
			BusinessID: businessID,
			StaffID:    staffID,
			FlowID:     flowID,
		}
		repo.EXPECT().GetOnboardingFlow(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(flowRecord, nil)
		repo.EXPECT().GetOnboardingSteps(gomock.Any(), gomock.Any()).Return(nil, errors.New("db error"))

		Convey("When GetOnboardingProgress is called", func() {
			resp, err := logic.GetOnboardingProgress(context.Background(), req)

			Convey("Then the response should be an error", func() {
				So(err, ShouldNotBeNil)
				So(resp, ShouldBeNil)
			})
		})
	})

	Convey("Given UnmarshalJSON returns an error", t, func() {
		repo := onboardingrepomock.NewMockOnboardingRepo(ctl)
		pawpilotRepo := pawpilotrepomock.NewMockRepo(ctl)
		logic := NewLogic(repo, pawpilotRepo)
		req := &onboardingpb.GetOnboardingProgressRequest{
			CompanyId:  companyID,
			BusinessId: businessID,
			StaffId:    staffID,
			FlowId:     flowID,
		}
		flowRecord := &onboardingrepo.OnboardingFlow{
			ID:         1,
			CompanyID:  companyID,
			BusinessID: businessID,
			StaffID:    staffID,
			FlowID:     flowID,
		}
		steps := []*onboardingrepo.OnboardingStep{
			{
				StepID:   "step1",
				Status:   onboardingpb.StepState_STEP_STATE_COMPLETED,
				Metadata: []byte(`invalid json`),
			},
		}
		repo.EXPECT().GetOnboardingFlow(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(flowRecord, nil)
		repo.EXPECT().GetOnboardingSteps(gomock.Any(), gomock.Any()).Return(steps, nil)

		Convey("When GetOnboardingProgress is called", func() {
			resp, err := logic.GetOnboardingProgress(context.Background(), req)

			Convey("Then the response should be an error", func() {
				So(err, ShouldNotBeNil)
				So(resp, ShouldBeNil)
			})
		})
	})
}

// 添加一个简单的 txRepo mock 实现，用于模拟事务操作
type mockTxRepo struct {
	*onboardingrepomock.MockOnboardingRepo
	commitFunc   func() error
	rollbackFunc func() error
}

func (m *mockTxRepo) Commit() error {
	if m.commitFunc != nil {
		return m.commitFunc()
	}
	return nil
}

func (m *mockTxRepo) Rollback() error {
	if m.rollbackFunc != nil {
		return m.rollbackFunc()
	}
	return nil
}

func TestLogic_UpdateOnboardingProgress(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	Convey("When the flow does not exist", t, func() {
		repo := onboardingrepomock.NewMockOnboardingRepo(ctl)
		pawpilotRepo := pawpilotrepomock.NewMockRepo(ctl)
		logic := NewLogic(repo, pawpilotRepo)
		testFlowID := "test-flow-id"
		req := &onboardingpb.UpdateOnboardingProgressRequest{
			CompanyId:  companyID,
			BusinessId: businessID,
			StaffId:    staffID,
			FlowId:     testFlowID,
			StepId:     stepID,
			State:      onboardingpb.StepState_STEP_STATE_COMPLETED,
		}
		createdFlow := &onboardingrepo.OnboardingFlow{
			ID:         1,
			CompanyID:  companyID,
			BusinessID: businessID,
			StaffID:    staffID,
			FlowID:     testFlowID,
		}

		// 创建事务 repo mock
		txRepo := onboardingrepomock.NewMockOnboardingRepo(ctl)
		// 设置事务 repo 的行为
		txRepo.EXPECT().GetOnboardingFlow(gomock.Any(), companyID, businessID, staffID, testFlowID).Return(nil, gorm.ErrRecordNotFound)
		txRepo.EXPECT().CreateOnboardingFlow(gomock.Any(), gomock.Any()).Do(func(ctx context.Context, flow *onboardingrepo.OnboardingFlow) {
			*flow = *createdFlow
		}).Return(nil)
		txRepo.EXPECT().GetOnboardingSteps(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		txRepo.EXPECT().CreateOnboardingStep(gomock.Any(), gomock.Any()).Return(nil)

		// 主 repo 应该调用 WithTx 并返回事务 repo
		repo.EXPECT().WithTx(gomock.Any()).Return(txRepo, nil)

		// 最后获取进度时调用主 repo
		repo.EXPECT().GetOnboardingFlow(gomock.Any(), companyID, businessID, staffID, testFlowID).Return(createdFlow, nil)
		repo.EXPECT().GetOnboardingSteps(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

		Convey("When UpdateOnboardingProgress is called", func() {
			resp, err := logic.UpdateOnboardingProgress(context.Background(), req)

			Convey("Then a new flow and step should be created", func() {
				So(err, ShouldBeNil)
				So(resp, ShouldNotBeNil)
			})
		})
	})

	Convey("When the flow exists", t, func() {
		repo := onboardingrepomock.NewMockOnboardingRepo(ctl)
		pawpilotRepo := pawpilotrepomock.NewMockRepo(ctl)
		logic := NewLogic(repo, pawpilotRepo)
		req := &onboardingpb.UpdateOnboardingProgressRequest{
			CompanyId:  companyID,
			BusinessId: businessID,
			StaffId:    staffID,
			FlowId:     flowID,
			StepId:     stepID,
			State:      onboardingpb.StepState_STEP_STATE_COMPLETED,
		}
		flowRecord := &onboardingrepo.OnboardingFlow{
			ID:         1,
			CompanyID:  companyID,
			BusinessID: businessID,
			StaffID:    staffID,
			FlowID:     flowID,
		}

		steps := []*onboardingrepo.OnboardingStep{
			{
				StepID: stepID,
				Status: onboardingpb.StepState_STEP_STATE_IN_PROGRESS,
			},
		}

		// 创建事务 repo mock
		txRepo := onboardingrepomock.NewMockOnboardingRepo(ctl)
		// 设置事务 repo 的行为
		txRepo.EXPECT().GetOnboardingFlow(gomock.Any(), flowRecord.CompanyID, flowRecord.BusinessID, flowRecord.StaffID, flowID).Return(flowRecord, nil).AnyTimes()
		txRepo.EXPECT().GetOnboardingSteps(gomock.Any(), flowRecord.ID).Return(steps, nil).AnyTimes()
		txRepo.EXPECT().UpdateOnboardingStep(gomock.Any(), gomock.Any()).Return(nil)

		// 主 repo 应该调用 WithTx 并返回事务 repo
		repo.EXPECT().WithTx(gomock.Any()).Return(txRepo, nil)

		// 最后获取进度时调用主 repo
		repo.EXPECT().GetOnboardingFlow(gomock.Any(), flowRecord.CompanyID, flowRecord.BusinessID, flowRecord.StaffID, flowID).Return(flowRecord, nil).AnyTimes()
		repo.EXPECT().GetOnboardingSteps(gomock.Any(), flowRecord.ID).Return(steps, nil).AnyTimes()

		Convey("When UpdateOnboardingProgress is called", func() {
			resp, err := logic.UpdateOnboardingProgress(context.Background(), req)

			Convey("Then the step should be updated", func() {
				So(err, ShouldBeNil)
				So(resp, ShouldNotBeNil)
			})
		})
	})

	Convey("When a database error occurs", t, func() {
		dbErr := errors.New("database error")
		Convey("When GetOnboardingFlow fails", func() {
			repo := onboardingrepomock.NewMockOnboardingRepo(ctl)
			pawpilotRepo := pawpilotrepomock.NewMockRepo(ctl)
			logic := NewLogic(repo, pawpilotRepo)
			req := &onboardingpb.UpdateOnboardingProgressRequest{
				CompanyId:  companyID,
				BusinessId: businessID,
				StaffId:    staffID,
				FlowId:     flowID,
				StepId:     stepID,
				State:      onboardingpb.StepState_STEP_STATE_COMPLETED,
			}

			// 创建事务 repo mock
			txRepo := onboardingrepomock.NewMockOnboardingRepo(ctl)
			txRepo.EXPECT().GetOnboardingFlow(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), flowID).Return(nil, dbErr)

			// 主 repo 应该调用 WithTx 并返回事务 repo
			repo.EXPECT().WithTx(gomock.Any()).Return(txRepo, nil)

			Convey("When UpdateOnboardingProgress is called", func() {
				resp, err := logic.UpdateOnboardingProgress(context.Background(), req)

				Convey("Then the response should be an error", func() {
					So(err, ShouldNotBeNil)
					So(resp, ShouldBeNil)
				})
			})
		})

		Convey("When CreateOnboardingFlow fails", func() {
			repo := onboardingrepomock.NewMockOnboardingRepo(ctl)
			pawpilotRepo := pawpilotrepomock.NewMockRepo(ctl)
			logic := NewLogic(repo, pawpilotRepo)
			req := &onboardingpb.UpdateOnboardingProgressRequest{
				CompanyId:  companyID,
				BusinessId: businessID,
				StaffId:    staffID,
				FlowId:     flowID,
				StepId:     stepID,
				State:      onboardingpb.StepState_STEP_STATE_COMPLETED,
			}

			// 创建事务 repo mock
			txRepo := onboardingrepomock.NewMockOnboardingRepo(ctl)
			txRepo.EXPECT().GetOnboardingFlow(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), flowID).Return(nil, gorm.ErrRecordNotFound)
			txRepo.EXPECT().CreateOnboardingFlow(gomock.Any(), gomock.Any()).Return(dbErr)

			// 主 repo 应该调用 WithTx 并返回事务 repo
			repo.EXPECT().WithTx(gomock.Any()).Return(txRepo, nil)

			Convey("When UpdateOnboardingProgress is called", func() {
				resp, err := logic.UpdateOnboardingProgress(context.Background(), req)

				Convey("Then the response should be an error", func() {
					So(err, ShouldNotBeNil)
					So(resp, ShouldBeNil)
				})
			})
		})

		Convey("When CreateOnboardingStep fails", func() {
			repo := onboardingrepomock.NewMockOnboardingRepo(ctl)
			pawpilotRepo := pawpilotrepomock.NewMockRepo(ctl)
			logic := NewLogic(repo, pawpilotRepo)
			req := &onboardingpb.UpdateOnboardingProgressRequest{
				CompanyId:  companyID,
				BusinessId: businessID,
				StaffId:    staffID,
				FlowId:     flowID,
				StepId:     stepID,
				State:      onboardingpb.StepState_STEP_STATE_COMPLETED,
			}

			// 创建事务 repo mock
			txRepo := onboardingrepomock.NewMockOnboardingRepo(ctl)
			txRepo.EXPECT().GetOnboardingFlow(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), flowID).Return(nil, gorm.ErrRecordNotFound)
			txRepo.EXPECT().CreateOnboardingFlow(gomock.Any(), gomock.Any()).Return(nil)
			txRepo.EXPECT().GetOnboardingSteps(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			txRepo.EXPECT().CreateOnboardingStep(gomock.Any(), gomock.Any()).Return(dbErr)

			// 主 repo 应该调用 WithTx 并返回事务 repo
			repo.EXPECT().WithTx(gomock.Any()).Return(txRepo, nil)

			Convey("When UpdateOnboardingProgress is called", func() {
				resp, err := logic.UpdateOnboardingProgress(context.Background(), req)

				Convey("Then the response should be an error", func() {
					So(err, ShouldNotBeNil)
					So(resp, ShouldBeNil)
				})
			})
		})

		Convey("When UpdateOnboardingStep fails", func() {
			repo := onboardingrepomock.NewMockOnboardingRepo(ctl)
			pawpilotRepo := pawpilotrepomock.NewMockRepo(ctl)
			logic := NewLogic(repo, pawpilotRepo)
			req := &onboardingpb.UpdateOnboardingProgressRequest{
				CompanyId:  companyID,
				BusinessId: businessID,
				StaffId:    staffID,
				FlowId:     flowID,
				StepId:     stepID,
				State:      onboardingpb.StepState_STEP_STATE_COMPLETED,
			}
			flowRecord := &onboardingrepo.OnboardingFlow{
				ID:         2,
				CompanyID:  companyID,
				BusinessID: businessID,
				StaffID:    staffID,
				FlowID:     flowID,
			}

			steps := []*onboardingrepo.OnboardingStep{
				{
					StepID: stepID,
					Status: onboardingpb.StepState_STEP_STATE_IN_PROGRESS,
				},
			}

			// 创建事务 repo mock
			txRepo := onboardingrepomock.NewMockOnboardingRepo(ctl)
			txRepo.EXPECT().GetOnboardingFlow(gomock.Any(), flowRecord.CompanyID, flowRecord.BusinessID, flowRecord.StaffID, flowID).Return(flowRecord, nil)
			txRepo.EXPECT().GetOnboardingSteps(gomock.Any(), flowRecord.ID).Return(steps, nil)
			txRepo.EXPECT().UpdateOnboardingStep(gomock.Any(), gomock.Any()).Return(dbErr)

			// 主 repo 应该调用 WithTx 并返回事务 repo
			repo.EXPECT().WithTx(gomock.Any()).Return(txRepo, nil)

			Convey("When UpdateOnboardingProgress is called", func() {
				resp, err := logic.UpdateOnboardingProgress(context.Background(), req)

				Convey("Then the response should be an error", func() {
					So(err, ShouldNotBeNil)
					So(resp, ShouldBeNil)
				})
			})
		})

		Convey("When WithTx fails", func() {
			repo := onboardingrepomock.NewMockOnboardingRepo(ctl)
			pawpilotRepo := pawpilotrepomock.NewMockRepo(ctl)
			logic := NewLogic(repo, pawpilotRepo)
			req := &onboardingpb.UpdateOnboardingProgressRequest{
				CompanyId:  companyID,
				BusinessId: businessID,
				StaffId:    staffID,
				FlowId:     flowID,
				StepId:     stepID,
				State:      onboardingpb.StepState_STEP_STATE_COMPLETED,
			}

			// 主 repo 应该调用 WithTx 但返回错误
			repo.EXPECT().WithTx(gomock.Any()).Return(nil, dbErr)

			Convey("When UpdateOnboardingProgress is called", func() {
				resp, err := logic.UpdateOnboardingProgress(context.Background(), req)

				Convey("Then the response should be an error", func() {
					So(err, ShouldNotBeNil)
					So(resp, ShouldBeNil)
				})
			})
		})
	})
}

func TestLogic_GenerateBookingPage(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	Convey("Given a GenerateBookingPageRequest", t, func() {
		repo := onboardingrepomock.NewMockOnboardingRepo(ctl)
		pawpilotRepo := pawpilotrepomock.NewMockRepo(ctl)
		logic := NewLogic(repo, pawpilotRepo)
		req := &onboardingpb.GenerateBookingPageRequest{
			BusinessInfo: &onboardingpb.BusinessInfo{
				Name: "Test Business",
			},
		}

		Convey("When GenerateBookingPage is called", func() {
			resp, err := logic.GenerateBookingPage(context.Background(), req)

			Convey("Then the response should contain mock page config and no error", func() {
				So(err, ShouldBeNil)
				So(resp, ShouldNotBeNil)
				So(resp.PageConfig.ThemeColor, ShouldEqual, "#4A90E2")
				So(resp.PageConfig.HeaderText, ShouldEqual, "Welcome to Test Business")
			})
		})
	})
}
