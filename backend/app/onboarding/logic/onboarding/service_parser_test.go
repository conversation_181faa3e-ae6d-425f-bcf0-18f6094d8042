package onboarding

import (
	"context"
	"encoding/json"
	"errors"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"go.uber.org/mock/gomock"

	onboardingrepomock "github.com/MoeGolibrary/moego/backend/app/onboarding/repo/mock/gorm"
	pawpilotrepomock "github.com/MoeGolibrary/moego/backend/app/onboarding/repo/mock/pawpilot"
	"github.com/MoeGolibrary/moego/backend/app/onboarding/repo/pawpilot"
	onboardingpb "github.com/MoeGolibrary/moego/backend/proto/onboarding/v1"
)

func TestLogic_ParseServices(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	repo := onboardingrepomock.NewMockOnboardingRepo(ctl)
	pawpilotRepo := pawpilotrepomock.NewMockRepo(ctl)
	logic := NewLogic(repo, pawpilotRepo)

	Convey("Given a valid and complete ParseServicesRequest", t, func() {
		req := &onboardingpb.ParseServicesRequest{
			CompanyId:  companyID,
			BusinessId: businessID,
			Sources: []*onboardingpb.Source{
				{Type: onboardingpb.SourceType_STRING, Data: "full service data"},
			},
		}

		// Define a complete mock response from the Pawpilot service
		structuredOutput := map[string]interface{}{
			"services": []interface{}{
				map[string]interface{}{
					"id":          float64(1),
					"name":        "Full Grooming",
					"description": "A complete grooming service",
					"price":       float64(75),
					"duration":    float64(90),
					"categoryId":  float64(1),
					"taxId":       float64(1),
					"typeAndBreedList": []interface{}{
						map[string]interface{}{
							"petTypeId":   float64(1),
							"petBreedIds": []interface{}{float64(1), float64(2)},
						},
					},
				},
			},
			"categoryList": []interface{}{
				map[string]interface{}{"id": float64(1), "name": "Grooming"},
			},
			"priceTaxList": []interface{}{
				map[string]interface{}{"id": float64(1), "name": "Standard Tax", "rate": 0.08},
			},
			"petTypeList": []interface{}{
				map[string]interface{}{"id": float64(1), "name": "Dog"},
			},
			"petBreedList": []interface{}{
				map[string]interface{}{"id": float64(1), "petTypeId": float64(1), "name": "Golden Retriever"},
				map[string]interface{}{"id": float64(2), "petTypeId": float64(1), "name": "Poodle"},
			},
			"petSizeList":     []interface{}{},
			"petCoatTypeList": []interface{}{},
		}

		// 将 structuredOutput 转换为 JSON，再包装成 ContentParseResponse
		structuredOutputJSON, _ := json.Marshal(structuredOutput)
		mockPawpilotResponse := &pawpilot.ContentParseResponse{
			StructuredOutput: structuredOutputJSON,
			Errors:           []string{},
		}

		pawpilotRepo.EXPECT().ParseServices(gomock.Any(), gomock.Any()).Return(mockPawpilotResponse, nil)

		Convey("When ParseServices is called", func() {
			resp, err := logic.ParseServices(context.Background(), req)

			Convey("Then the response should contain all parsed data correctly", func() {
				So(err, ShouldBeNil)
				So(resp, ShouldNotBeNil)

				// Assert services
				So(len(resp.Services), ShouldEqual, 1)
				So(resp.Services[0].Name, ShouldEqual, "Full Grooming")
				So(resp.Services[0].CategoryId, ShouldEqual, 1)
				So(len(resp.Services[0].TypeAndBreedList), ShouldEqual, 1)
				So(resp.Services[0].TypeAndBreedList[0].PetTypeId, ShouldEqual, 1)
				So(resp.Services[0].TypeAndBreedList[0].PetBreedIds, ShouldResemble, []int64{1, 2})

				// Assert other lists
				So(len(resp.CategoryList), ShouldEqual, 1)
				So(resp.CategoryList[0].Name, ShouldEqual, "Grooming")

				So(len(resp.PriceTaxList), ShouldEqual, 1)
				So(resp.PriceTaxList[0].Rate, ShouldEqual, 0.08)

				So(len(resp.PetTypeList), ShouldEqual, 1)
				So(resp.PetTypeList[0].Name, ShouldEqual, "Dog")

				So(len(resp.PetBreedList), ShouldEqual, 2)
				So(resp.PetBreedList[1].Name, ShouldEqual, "Poodle")
			})
		})
	})

	Convey("Given the Pawpilot service returns an error", t, func() {
		req := &onboardingpb.ParseServicesRequest{ /* ... */ }
		expectedErr := errors.New("pawpilot is down")

		pawpilotRepo.EXPECT().ParseServices(gomock.Any(), gomock.Any()).Return(nil, expectedErr)

		Convey("When ParseServices is called", func() {
			resp, err := logic.ParseServices(context.Background(), req)

			Convey("Then the error should be propagated", func() {
				So(err, ShouldNotBeNil)
				So(err.Error(), ShouldContainSubstring, expectedErr.Error())
				So(resp, ShouldBeNil)
			})
		})
	})

	Convey("Given the Pawpilot response is empty", t, func() {
		req := &onboardingpb.ParseServicesRequest{ /* ... */ }

		// 创建空的结构化输出
		emptyOutput := map[string]interface{}{}
		emptyOutputJSON, _ := json.Marshal(emptyOutput)
		mockPawpilotResponse := &pawpilot.ContentParseResponse{
			StructuredOutput: emptyOutputJSON,
			Errors:           []string{},
		}

		pawpilotRepo.EXPECT().ParseServices(gomock.Any(), gomock.Any()).Return(mockPawpilotResponse, nil)

		Convey("When ParseServices is called", func() {
			resp, err := logic.ParseServices(context.Background(), req)

			Convey("Then the response should be an empty, non-nil object", func() {
				So(err, ShouldBeNil)
				So(resp, ShouldNotBeNil)
				So(len(resp.Services), ShouldEqual, 0)
				So(len(resp.CategoryList), ShouldEqual, 0)
			})
		})
	})

	Convey("Given the Pawpilot response contains malformed data", t, func() {
		req := &onboardingpb.ParseServicesRequest{ /* ... */ }
		// 创建包含错误数据的结构化输出
		malformedOutput := map[string]interface{}{
			"services": []interface{}{
				map[string]interface{}{
					"id":    float64(1),
					"name":  "Broken Service",
					"price": "fifty dollars", // This will cause Unmarshal to fail
				},
			},
		}

		malformedOutputJSON, _ := json.Marshal(malformedOutput)
		mockPawpilotResponse := &pawpilot.ContentParseResponse{
			StructuredOutput: malformedOutputJSON,
			Errors:           []string{},
		}

		pawpilotRepo.EXPECT().ParseServices(gomock.Any(), gomock.Any()).Return(mockPawpilotResponse, nil)

		Convey("When ParseServices is called", func() {
			resp, err := logic.ParseServices(context.Background(), req)

			Convey("Then an unmarshaling error should occur", func() {
				So(err, ShouldNotBeNil)
				So(err.Error(), ShouldContainSubstring, "unmarshal")
				So(resp, ShouldBeNil)
			})
		})
	})
}
