package gorm

import (
	"time"

	"gorm.io/datatypes"

	onboardingpb "github.com/MoeGolibrary/moego/backend/proto/onboarding/v1"
)

// OnboardingFlow represents the overall progress of an onboarding flow for a user.
type OnboardingFlow struct {
	ID         int64  `gorm:"primary_key;autoIncrement"`
	CompanyID  int64  `gorm:"index:idx_onboarding_flows_company_business_flow,unique"`
	BusinessID int64  `gorm:"index:idx_onboarding_flows_company_business_flow,unique"`
	StaffID    int64  `gorm:"index:idx_onboarding_flows_company_business_flow,unique"`
	FlowID     string `gorm:"index:idx_onboarding_flows_company_business_flow,unique"`
	FlowStatus onboardingpb.FlowState
	CreatedAt  time.Time
	UpdatedAt  time.Time
}

func (*OnboardingFlow) TableName() string {
	return "onboarding_flows"
}

// OnboardingStep represents the progress of a single step in the onboarding flow.
type OnboardingStep struct {
	ID           int64  `gorm:"primary_key;autoIncrement"`
	FlowRecordID int64  `gorm:"index:idx_onboarding_steps_flow_record_id_step_id,unique"`
	StepID       string `gorm:"index:idx_onboarding_steps_flow_record_id_step_id,unique"`
	Status       onboardingpb.StepState
	Metadata     datatypes.JSON
	StartedAt    time.Time
	CompletedAt  *time.Time
}

func (*OnboardingStep) TableName() string {
	return "onboarding_steps"
}
