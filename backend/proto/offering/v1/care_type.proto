syntax = "proto3";

package backend.proto.offering.v1;

import "backend/proto/organization/v1/organization.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option go_package="github.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.offering.v1";

// Defines a Care Type Config.
message CareType {
  // The unique ID of the care type.
  int64 id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The level of the organization (e.g., "enterprise", "company").
  backend.proto.organization.v1.OrganizationType organization_type = 2 [(google.api.field_behavior) = IMMUTABLE];

  // The ID of the organization.
  int64 organization_id = 3 [(google.api.field_behavior) = IMMUTABLE];

  // The name of the care type.
  string name = 4 [(google.api.field_behavior) = REQUIRED];

  // The system code of the care type (e.g. "grooming", "daycare", "custom").
  CareCategory care_category = 5 [(google.api.field_behavior) = REQUIRED];

  // An optional description.
  optional string description = 6 [(google.api.field_behavior) = OPTIONAL];

  // The time the care type was created.
  google.protobuf.Timestamp create_time = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
  // The time the care type was last updated.
  google.protobuf.Timestamp update_time = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
  // The time the care type was deleted.
  optional google.protobuf.Timestamp delete_time = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Defines an attribute for a care type.
message CareTypeAttribute {
  // The unique ID of the attribute.
  int64 id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The ID of the care type this attribute belongs to.
  int64 care_type_id = 2 [(google.api.field_behavior) = IMMUTABLE];

  // The unique key of the attribute.
  AttributeKey attribute_key = 3 [(google.api.field_behavior) = IMMUTABLE];

  // (-- api-linter: core::0122::name-suffix=disabled
  //     aip.dev/not-precedent: field_name 更适合表示字段名. --)
  // The field name of the attribute.
  string field_name = 4 [(google.api.field_behavior) = IMMUTABLE];

  // The display label for the UI (e.g., "Pet Size").
  optional string label = 5 [(google.api.field_behavior) = OPTIONAL];

  // The value type of the attribute (e.g., STRING, NUMBER).
  ValueType value_type = 6 [(google.api.field_behavior) = IMMUTABLE];

  // A list of options, used when value_type is ENUM.
  optional google.protobuf.Struct options = 7 [(google.api.field_behavior) = OPTIONAL];

  // An optional description.
  optional string description = 8 [(google.api.field_behavior) = OPTIONAL];

  // Whether this attribute is required.
  bool is_required = 9 [(google.api.field_behavior) = REQUIRED];

  // The default value for this attribute.
  optional google.protobuf.Value default_value = 10 [(google.api.field_behavior) = OPTIONAL];

  // The time the attribute was created.
  google.protobuf.Timestamp create_time = 11 [(google.api.field_behavior) = OUTPUT_ONLY];
  // The time the attribute was last updated.
  google.protobuf.Timestamp update_time = 12 [(google.api.field_behavior) = OUTPUT_ONLY];
  // The time the attribute was deleted.
  optional google.protobuf.Timestamp delete_time = 13 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// 护理类别，包含系统预设和自定义
enum CareCategory {
  // 未知护理类型
  CARE_CATEGORY_UNSPECIFIED = 0;
  // GROOMING类型
  GROOMING = 1;
  // BOARDING类型
  BOARDING = 2;
  // DAYCARE类型
  DAYCARE = 3;
  // EVALUATION类型
  EVALUATION = 4;
  // DOG_WALKING类型
  DOG_WALKING = 5;
  // GROUP_CLASS类型
  GROUP_CLASS = 6;
  // 自定义类型
  CUSTOM = 99;
}

// Defines the unique keys for all supported attributes in the system.
// This enum is the single source of truth for what attributes are available.
enum AttributeKey {
  // Unspecified attribute key.
  ATTRIBUTE_KEY_UNSPECIFIED = 0;
  // Duration in minutes. for grooming/dog_walking services
  DURATION = 1;
  // Max stay duration in minutes. for daycare services
  MAX_DURATION = 2;
  // A list of prerequisite service IDs. for boarding/daycare/group_class services
  PREREQUISITE_SERVICE = 3;
  // Indicates if staff can be auto-assigned.
  STAFF_AUTO_ASSIGN = 4;
  // Indicates if the service is resettable. for evaluation services
  RESULT_RESETTABLE = 5;
  // Indicates if staff binding is supported.
  AVAILABLE_STAFF = 6;
  // Indicates if lodging type binding is supported.
  AVAILABLE_LODGING_TYPE = 7;
  // Indicates if auto-rollover is supported.
  AUTO_ROLLOVER = 8;
  // Service name in online booking. for evaluation services
  ONLINE_BOOKING_ALIAS = 9;
  
  // Indicates if lodging is required. for boarding/daycare services
  // ATTRIBUTE_KEY_IS_LODGING_REQUIRED = 3;
  // Indicates if an evaluation is required. for boarding/daycare services
  // ATTRIBUTE_KEY_IS_EVALUATION_REQUIRED = 4;
  // The ID of the required evaluation. for boarding/daycare services
  // ATTRIBUTE_KEY_REQUIRED_EVALUATION_ID = 6;
  // The number of sessions. 
  // ATTRIBUTE_KEY_SESSION_COUNT = 7;
  // The minimum duration of a session in minutes.
  // ATTRIBUTE_KEY_SESSION_DURATION_MIN = 8;
  // The capacity of the class.
  // ATTRIBUTE_KEY_CLASS_CAPACITY = 9;
  // Indicates if a prerequisite class is required.
  // ATTRIBUTE_KEY_IS_PREREQUISITE_REQUIRED = 10;
  // Indicates if pet type binding is supported.
  // ATTRIBUTE_KEY_IS_SUPPORT_PET_TYPE_BINDING = 15;
  // Indicates if coat type binding is supported.
  // ATTRIBUTE_KEY_IS_SUPPORT_COAT_TYPE_BINDING = 16;
  // Indicates if pet size binding is supported.
  // ATTRIBUTE_KEY_IS_SUPPORT_PET_SIZE_BINDING = 17;
  // Indicates if pet code binding is supported.
  // ATTRIBUTE_KEY_IS_SUPPORT_PET_CODE_BINDING = 18;
}

// ValueType defines the data type of an attribute's value, for frontend rendering.
enum ValueType {
  // Unspecified value type.
  VALUE_TYPE_UNSPECIFIED = 0;
  // A single-line string.
  STRING = 1;
  // A multi-line text.
  TEXT = 2;
  // A numerical value (integer or decimal).
  NUMBER = 3;
  // A boolean value (true or false).
  BOOLEAN = 4;
  // A single selection from a list of options.
  SINGLE_SELECT = 5;
  // Multiple selections from a list of options.
  MULTI_SELECT = 6;
  // A date value.
  DATE = 7;
  // A date and time value.
  DATETIME = 8;
  // A JSON object.
  JSON = 9;
  // A table configuration that requires special UI components and database tables.
  TABLE = 10;
}