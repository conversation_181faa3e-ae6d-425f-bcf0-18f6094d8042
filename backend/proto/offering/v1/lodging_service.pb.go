// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/offering/v1/lodging_service.proto

package offeringpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GetLodgingTypeListByCompanyIDRequest 获取住宿类型列表请求
type GetLodgingTypeListByCompanyIDRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司ID
	CompanyId     int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLodgingTypeListByCompanyIDRequest) Reset() {
	*x = GetLodgingTypeListByCompanyIDRequest{}
	mi := &file_backend_proto_offering_v1_lodging_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLodgingTypeListByCompanyIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLodgingTypeListByCompanyIDRequest) ProtoMessage() {}

func (x *GetLodgingTypeListByCompanyIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_lodging_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLodgingTypeListByCompanyIDRequest.ProtoReflect.Descriptor instead.
func (*GetLodgingTypeListByCompanyIDRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_lodging_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetLodgingTypeListByCompanyIDRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// GetLodgingTypeListByCompanyIDResponse 获取住宿类型列表响应
type GetLodgingTypeListByCompanyIDResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 住宿类型列表
	LodgingTypes  []*LodgingType `protobuf:"bytes,1,rep,name=lodging_types,json=lodgingTypes,proto3" json:"lodging_types,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLodgingTypeListByCompanyIDResponse) Reset() {
	*x = GetLodgingTypeListByCompanyIDResponse{}
	mi := &file_backend_proto_offering_v1_lodging_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLodgingTypeListByCompanyIDResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLodgingTypeListByCompanyIDResponse) ProtoMessage() {}

func (x *GetLodgingTypeListByCompanyIDResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_lodging_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLodgingTypeListByCompanyIDResponse.ProtoReflect.Descriptor instead.
func (*GetLodgingTypeListByCompanyIDResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_lodging_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetLodgingTypeListByCompanyIDResponse) GetLodgingTypes() []*LodgingType {
	if x != nil {
		return x.LodgingTypes
	}
	return nil
}

// ListLodgingUnitRequest 获取住宿单元列表请求
// (-- api-linter: core::0132::request-parent-required=disabled
//
//	aip.dev/not-precedent: parent field is not needed for this use case --)
//
// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: page_size field is not needed for this use case --)
//
// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: page_token field is not needed for this use case --)
type ListLodgingUnitRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 商家ID
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 过滤条件
	Filter        *LodgingUnitFilter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListLodgingUnitRequest) Reset() {
	*x = ListLodgingUnitRequest{}
	mi := &file_backend_proto_offering_v1_lodging_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListLodgingUnitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLodgingUnitRequest) ProtoMessage() {}

func (x *ListLodgingUnitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_lodging_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLodgingUnitRequest.ProtoReflect.Descriptor instead.
func (*ListLodgingUnitRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_lodging_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListLodgingUnitRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListLodgingUnitRequest) GetFilter() *LodgingUnitFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListLodgingUnitResponse 获取住宿单元列表响应
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//
//	aip.dev/not-precedent: next_page_token field is not needed for this use case --)
type ListLodgingUnitResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 住宿单元列表
	// (-- api-linter: core::0132::response-unknown-fields=disabled
	//
	//	aip.dev/not-precedent: lodging_units field is necessary for this use case --)
	LodgingUnits  []*LodgingUnit `protobuf:"bytes,1,rep,name=lodging_units,json=lodgingUnits,proto3" json:"lodging_units,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListLodgingUnitResponse) Reset() {
	*x = ListLodgingUnitResponse{}
	mi := &file_backend_proto_offering_v1_lodging_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListLodgingUnitResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLodgingUnitResponse) ProtoMessage() {}

func (x *ListLodgingUnitResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_lodging_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLodgingUnitResponse.ProtoReflect.Descriptor instead.
func (*ListLodgingUnitResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_lodging_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListLodgingUnitResponse) GetLodgingUnits() []*LodgingUnit {
	if x != nil {
		return x.LodgingUnits
	}
	return nil
}

var File_backend_proto_offering_v1_lodging_service_proto protoreflect.FileDescriptor

const file_backend_proto_offering_v1_lodging_service_proto_rawDesc = "" +
	"\n" +
	"/backend/proto/offering/v1/lodging_service.proto\x12\x19backend.proto.offering.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bbuf/validate/validate.proto\x1a'backend/proto/offering/v1/lodging.proto\"N\n" +
	"$GetLodgingTypeListByCompanyIDRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tcompanyId\"t\n" +
	"%GetLodgingTypeListByCompanyIDResponse\x12K\n" +
	"\rlodging_types\x18\x01 \x03(\v2&.backend.proto.offering.v1.LodgingTypeR\flodgingTypes\"\x88\x01\n" +
	"\x16ListLodgingUnitRequest\x12(\n" +
	"\vbusiness_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"businessId\x12D\n" +
	"\x06filter\x18\x02 \x01(\v2,.backend.proto.offering.v1.LodgingUnitFilterR\x06filter\"f\n" +
	"\x17ListLodgingUnitResponse\x12K\n" +
	"\rlodging_units\x18\x01 \x03(\v2&.backend.proto.offering.v1.LodgingUnitR\flodgingUnits2\xaf\x02\n" +
	"\x0eLodgingService\x12\xa2\x01\n" +
	"\x1dGetLodgingTypeListByCompanyID\x12?.backend.proto.offering.v1.GetLodgingTypeListByCompanyIDRequest\<EMAIL>\x12x\n" +
	"\x0fListLodgingUnit\x121.backend.proto.offering.v1.ListLodgingUnitRequest\x1a2.backend.proto.offering.v1.ListLodgingUnitResponseBk\n" +
	"#com.moego.backend.proto.offering.v1P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpbb\x06proto3"

var (
	file_backend_proto_offering_v1_lodging_service_proto_rawDescOnce sync.Once
	file_backend_proto_offering_v1_lodging_service_proto_rawDescData []byte
)

func file_backend_proto_offering_v1_lodging_service_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_v1_lodging_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_v1_lodging_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_lodging_service_proto_rawDesc), len(file_backend_proto_offering_v1_lodging_service_proto_rawDesc)))
	})
	return file_backend_proto_offering_v1_lodging_service_proto_rawDescData
}

var file_backend_proto_offering_v1_lodging_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_backend_proto_offering_v1_lodging_service_proto_goTypes = []any{
	(*GetLodgingTypeListByCompanyIDRequest)(nil),  // 0: backend.proto.offering.v1.GetLodgingTypeListByCompanyIDRequest
	(*GetLodgingTypeListByCompanyIDResponse)(nil), // 1: backend.proto.offering.v1.GetLodgingTypeListByCompanyIDResponse
	(*ListLodgingUnitRequest)(nil),                // 2: backend.proto.offering.v1.ListLodgingUnitRequest
	(*ListLodgingUnitResponse)(nil),               // 3: backend.proto.offering.v1.ListLodgingUnitResponse
	(*LodgingType)(nil),                           // 4: backend.proto.offering.v1.LodgingType
	(*LodgingUnitFilter)(nil),                     // 5: backend.proto.offering.v1.LodgingUnitFilter
	(*LodgingUnit)(nil),                           // 6: backend.proto.offering.v1.LodgingUnit
}
var file_backend_proto_offering_v1_lodging_service_proto_depIdxs = []int32{
	4, // 0: backend.proto.offering.v1.GetLodgingTypeListByCompanyIDResponse.lodging_types:type_name -> backend.proto.offering.v1.LodgingType
	5, // 1: backend.proto.offering.v1.ListLodgingUnitRequest.filter:type_name -> backend.proto.offering.v1.LodgingUnitFilter
	6, // 2: backend.proto.offering.v1.ListLodgingUnitResponse.lodging_units:type_name -> backend.proto.offering.v1.LodgingUnit
	0, // 3: backend.proto.offering.v1.LodgingService.GetLodgingTypeListByCompanyID:input_type -> backend.proto.offering.v1.GetLodgingTypeListByCompanyIDRequest
	2, // 4: backend.proto.offering.v1.LodgingService.ListLodgingUnit:input_type -> backend.proto.offering.v1.ListLodgingUnitRequest
	1, // 5: backend.proto.offering.v1.LodgingService.GetLodgingTypeListByCompanyID:output_type -> backend.proto.offering.v1.GetLodgingTypeListByCompanyIDResponse
	3, // 6: backend.proto.offering.v1.LodgingService.ListLodgingUnit:output_type -> backend.proto.offering.v1.ListLodgingUnitResponse
	5, // [5:7] is the sub-list for method output_type
	3, // [3:5] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_v1_lodging_service_proto_init() }
func file_backend_proto_offering_v1_lodging_service_proto_init() {
	if File_backend_proto_offering_v1_lodging_service_proto != nil {
		return
	}
	file_backend_proto_offering_v1_lodging_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_lodging_service_proto_rawDesc), len(file_backend_proto_offering_v1_lodging_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_offering_v1_lodging_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_v1_lodging_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_offering_v1_lodging_service_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_v1_lodging_service_proto = out.File
	file_backend_proto_offering_v1_lodging_service_proto_goTypes = nil
	file_backend_proto_offering_v1_lodging_service_proto_depIdxs = nil
}
