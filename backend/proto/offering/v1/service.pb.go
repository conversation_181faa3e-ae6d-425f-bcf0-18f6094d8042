// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.8
// 	protoc        (unknown)
// source: backend/proto/offering/v1/service.proto

package offeringpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	v1 "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// (-- api-linter: core::0216::synonyms=disabled
//
//	aip.dev/not-precedent: 保持 status 命名设计. --)
//
// The status of the service.
type Service_Status int32

const (
	// Unspecified
	Service_STATUS_UNSPECIFIED Service_Status = 0
	// Active
	Service_ACTIVE Service_Status = 1
	// Inactive
	Service_INACTIVE Service_Status = 2
)

// Enum value maps for Service_Status.
var (
	Service_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "ACTIVE",
		2: "INACTIVE",
	}
	Service_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"ACTIVE":             1,
		"INACTIVE":           2,
	}
)

func (x Service_Status) Enum() *Service_Status {
	p := new(Service_Status)
	*p = x
	return p
}

func (x Service_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Service_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_service_proto_enumTypes[0].Descriptor()
}

func (Service_Status) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_service_proto_enumTypes[0]
}

func (x Service_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Service_Status.Descriptor instead.
func (Service_Status) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{0, 0}
}

// Service type.
type Service_Type int32

const (
	// Unspecified.
	Service_TYPE_UNSPECIFIED Service_Type = 0
	// Service.
	Service_SERVICE Service_Type = 1
	// Add-On.
	Service_ADD_ON Service_Type = 2
)

// Enum value maps for Service_Type.
var (
	Service_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "SERVICE",
		2: "ADD_ON",
	}
	Service_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"SERVICE":          1,
		"ADD_ON":           2,
	}
)

func (x Service_Type) Enum() *Service_Type {
	p := new(Service_Type)
	*p = x
	return p
}

func (x Service_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Service_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_service_proto_enumTypes[1].Descriptor()
}

func (Service_Type) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_service_proto_enumTypes[1]
}

func (x Service_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Service_Type.Descriptor instead.
func (Service_Type) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{0, 1}
}

// Defines the structure for a service, which acts as a blueprint for creating specific service instances.
type Service struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Primary key ID of the service
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The type of the organization (e.g., "enterprise", "company").
	OrganizationType v1.OrganizationType `protobuf:"varint,2,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// The ID of the organization.
	OrganizationId int64 `protobuf:"varint,3,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// The ID of the care type associated with this service.
	CareTypeId int64 `protobuf:"varint,4,opt,name=care_type_id,json=careTypeId,proto3" json:"care_type_id,omitempty"`
	// The ID of the category this service.
	CategoryId *int64 `protobuf:"varint,5,opt,name=category_id,json=categoryId,proto3,oneof" json:"category_id,omitempty"`
	// Name of the service, unique within the same company
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	// Optional description of the service
	Description *string `protobuf:"bytes,7,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// A color code associated with the service for UI purposes.
	ColorCode string `protobuf:"bytes,8,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// The sorting order of the service.
	Sort *int64 `protobuf:"varint,9,opt,name=sort,proto3,oneof" json:"sort,omitempty"`
	// A list of image URLs for the service.
	Images []string `protobuf:"bytes,10,rep,name=images,proto3" json:"images,omitempty"`
	// The offering source of the service.
	Source OfferingSource `protobuf:"varint,11,opt,name=source,proto3,enum=backend.proto.offering.v1.OfferingSource" json:"source,omitempty"`
	// (-- api-linter: core::0216::synonyms=disabled
	//
	//	aip.dev/not-precedent: 保持 status 命名设计. --)
	//
	// The status of the service.
	Status Service_Status `protobuf:"varint,12,opt,name=status,proto3,enum=backend.proto.offering.v1.Service_Status" json:"status,omitempty"`
	// Is deleted
	IsDeleted bool `protobuf:"varint,13,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	// The timestamp when the service was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,20,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The timestamp when the service was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,21,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// The timestamp when the service was deleted.
	DeleteTime *timestamppb.Timestamp `protobuf:"bytes,22,opt,name=delete_time,json=deleteTime,proto3,oneof" json:"delete_time,omitempty"`
	// The available business scope for this service.
	AvailableBusiness *AvailableBusiness `protobuf:"bytes,23,opt,name=available_business,json=availableBusiness,proto3" json:"available_business,omitempty"`
	// Additional service/addon scope configuration
	AdditionalService *AdditionalService `protobuf:"bytes,24,opt,name=additional_service,json=additionalService,proto3,oneof" json:"additional_service,omitempty"`
	// Service attributes for better frontend type safety
	Attributes    *ServiceAttributes `protobuf:"bytes,99,opt,name=attributes,proto3,oneof" json:"attributes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Service) Reset() {
	*x = Service{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Service) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Service) ProtoMessage() {}

func (x *Service) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Service.ProtoReflect.Descriptor instead.
func (*Service) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{0}
}

func (x *Service) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Service) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *Service) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *Service) GetCareTypeId() int64 {
	if x != nil {
		return x.CareTypeId
	}
	return 0
}

func (x *Service) GetCategoryId() int64 {
	if x != nil && x.CategoryId != nil {
		return *x.CategoryId
	}
	return 0
}

func (x *Service) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Service) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *Service) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *Service) GetSort() int64 {
	if x != nil && x.Sort != nil {
		return *x.Sort
	}
	return 0
}

func (x *Service) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *Service) GetSource() OfferingSource {
	if x != nil {
		return x.Source
	}
	return OfferingSource_OFFERING_SOURCE_UNSPECIFIED
}

func (x *Service) GetStatus() Service_Status {
	if x != nil {
		return x.Status
	}
	return Service_STATUS_UNSPECIFIED
}

func (x *Service) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

func (x *Service) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Service) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Service) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

func (x *Service) GetAvailableBusiness() *AvailableBusiness {
	if x != nil {
		return x.AvailableBusiness
	}
	return nil
}

func (x *Service) GetAdditionalService() *AdditionalService {
	if x != nil {
		return x.AdditionalService
	}
	return nil
}

func (x *Service) GetAttributes() *ServiceAttributes {
	if x != nil {
		return x.Attributes
	}
	return nil
}

// Additional service/addon scope configuration
type AdditionalService struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// List of care type IDs to include all services under those care types
	AdditionalCareTypeIds []int64 `protobuf:"varint,1,rep,packed,name=additional_care_type_ids,json=additionalCareTypeIds,proto3" json:"additional_care_type_ids,omitempty"`
	// List of specific additional service/addon IDs when is_all is false
	AdditionalServiceIds []int64 `protobuf:"varint,2,rep,packed,name=additional_service_ids,json=additionalServiceIds,proto3" json:"additional_service_ids,omitempty"`
	// List of service types to include (SERVICE, ADD_ON, or both)
	AdditionalServiceTypes []Service_Type `protobuf:"varint,3,rep,packed,name=additional_service_types,json=additionalServiceTypes,proto3,enum=backend.proto.offering.v1.Service_Type" json:"additional_service_types,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *AdditionalService) Reset() {
	*x = AdditionalService{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdditionalService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdditionalService) ProtoMessage() {}

func (x *AdditionalService) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdditionalService.ProtoReflect.Descriptor instead.
func (*AdditionalService) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{1}
}

func (x *AdditionalService) GetAdditionalCareTypeIds() []int64 {
	if x != nil {
		return x.AdditionalCareTypeIds
	}
	return nil
}

func (x *AdditionalService) GetAdditionalServiceIds() []int64 {
	if x != nil {
		return x.AdditionalServiceIds
	}
	return nil
}

func (x *AdditionalService) GetAdditionalServiceTypes() []Service_Type {
	if x != nil {
		return x.AdditionalServiceTypes
	}
	return nil
}

// optional extra info to include in response
type ExtraInfoOptions struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// whether to include online booking setting
	IncludeObSetting *bool `protobuf:"varint,1,opt,name=include_ob_setting,json=includeObSetting,proto3,oneof" json:"include_ob_setting,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ExtraInfoOptions) Reset() {
	*x = ExtraInfoOptions{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExtraInfoOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtraInfoOptions) ProtoMessage() {}

func (x *ExtraInfoOptions) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtraInfoOptions.ProtoReflect.Descriptor instead.
func (*ExtraInfoOptions) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{2}
}

func (x *ExtraInfoOptions) GetIncludeObSetting() bool {
	if x != nil && x.IncludeObSetting != nil {
		return *x.IncludeObSetting
	}
	return false
}

// service and service extension
type ServiceWithExtraInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// service base
	Service *Service `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	// online booking setting
	ObSetting     *ServiceOBSetting `protobuf:"bytes,2,opt,name=ob_setting,json=obSetting,proto3" json:"ob_setting,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceWithExtraInfo) Reset() {
	*x = ServiceWithExtraInfo{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceWithExtraInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceWithExtraInfo) ProtoMessage() {}

func (x *ServiceWithExtraInfo) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceWithExtraInfo.ProtoReflect.Descriptor instead.
func (*ServiceWithExtraInfo) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{3}
}

func (x *ServiceWithExtraInfo) GetService() *Service {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *ServiceWithExtraInfo) GetObSetting() *ServiceOBSetting {
	if x != nil {
		return x.ObSetting
	}
	return nil
}

// Defines a service auto rollover.
type AutoRollover struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Enabled auto rollover
	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// The ID of the target service.
	TargetServiceId int64 `protobuf:"varint,2,opt,name=target_service_id,json=targetServiceId,proto3" json:"target_service_id,omitempty"`
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: after_minute 表示服务后多少分钟. --)
	//
	// The number of minutes after the max duration to trigger auto rollover.
	AfterMinute   int32 `protobuf:"varint,3,opt,name=after_minute,json=afterMinute,proto3" json:"after_minute,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AutoRollover) Reset() {
	*x = AutoRollover{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AutoRollover) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoRollover) ProtoMessage() {}

func (x *AutoRollover) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoRollover.ProtoReflect.Descriptor instead.
func (*AutoRollover) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{4}
}

func (x *AutoRollover) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *AutoRollover) GetTargetServiceId() int64 {
	if x != nil {
		return x.TargetServiceId
	}
	return 0
}

func (x *AutoRollover) GetAfterMinute() int32 {
	if x != nil {
		return x.AfterMinute
	}
	return 0
}

// Defines available business configuration for a service
type AvailableBusiness struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether the service is available for all businesses
	IsAll bool `protobuf:"varint,1,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	// List of specific business IDs when is_all is false
	BusinessIds   []int64 `protobuf:"varint,2,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AvailableBusiness) Reset() {
	*x = AvailableBusiness{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvailableBusiness) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailableBusiness) ProtoMessage() {}

func (x *AvailableBusiness) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailableBusiness.ProtoReflect.Descriptor instead.
func (*AvailableBusiness) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{5}
}

func (x *AvailableBusiness) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

func (x *AvailableBusiness) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

// Defines available staff configuration for a service
type AvailableStaff struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether the service is available for all staff
	IsAll bool `protobuf:"varint,1,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	// List of specific staff IDs when is_all is false
	StaffIds      []int64 `protobuf:"varint,2,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AvailableStaff) Reset() {
	*x = AvailableStaff{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvailableStaff) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailableStaff) ProtoMessage() {}

func (x *AvailableStaff) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailableStaff.ProtoReflect.Descriptor instead.
func (*AvailableStaff) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{6}
}

func (x *AvailableStaff) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

func (x *AvailableStaff) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

// Defines available lodging configuration for a service
type AvailableLodgingType struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether the service is available for all lodging
	IsAll bool `protobuf:"varint,1,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	// List of specific lodging IDs when is_all is false
	LodgingTypeIds []int64 `protobuf:"varint,2,rep,packed,name=lodging_type_ids,json=lodgingTypeIds,proto3" json:"lodging_type_ids,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AvailableLodgingType) Reset() {
	*x = AvailableLodgingType{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvailableLodgingType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailableLodgingType) ProtoMessage() {}

func (x *AvailableLodgingType) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailableLodgingType.ProtoReflect.Descriptor instead.
func (*AvailableLodgingType) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{7}
}

func (x *AvailableLodgingType) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

func (x *AvailableLodgingType) GetLodgingTypeIds() []int64 {
	if x != nil {
		return x.LodgingTypeIds
	}
	return nil
}

// Defines service attributes for better frontend type safety
type ServiceAttributes struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Duration of the service.
	Duration *int32 `protobuf:"varint,1,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	// Max duration of the service.
	MaxDuration *int32 `protobuf:"varint,2,opt,name=max_duration,json=maxDuration,proto3,oneof" json:"max_duration,omitempty"`
	// Auto rollover configuration. auto rollover will be triggered when the service duration is greater than the max duration.
	AutoRollover *AutoRollover `protobuf:"bytes,3,opt,name=auto_rollover,json=autoRollover,proto3,oneof" json:"auto_rollover,omitempty"`
	// Available staff configuration. available staff will be used to schedule the service.
	AvailableStaff *AvailableStaff `protobuf:"bytes,4,opt,name=available_staff,json=availableStaff,proto3,oneof" json:"available_staff,omitempty"`
	// Available lodging type configuration. available lodging type will be used to schedule the service.
	AvailableLodgingType *AvailableLodgingType `protobuf:"bytes,5,opt,name=available_lodging_type,json=availableLodgingType,proto3,oneof" json:"available_lodging_type,omitempty"`
	// Whether the addon requires staff (only for ADD_ON type services)
	IsRequiredStaff *bool `protobuf:"varint,6,opt,name=is_required_staff,json=isRequiredStaff,proto3,oneof" json:"is_required_staff,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ServiceAttributes) Reset() {
	*x = ServiceAttributes{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceAttributes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceAttributes) ProtoMessage() {}

func (x *ServiceAttributes) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceAttributes.ProtoReflect.Descriptor instead.
func (*ServiceAttributes) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{8}
}

func (x *ServiceAttributes) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

func (x *ServiceAttributes) GetMaxDuration() int32 {
	if x != nil && x.MaxDuration != nil {
		return *x.MaxDuration
	}
	return 0
}

func (x *ServiceAttributes) GetAutoRollover() *AutoRollover {
	if x != nil {
		return x.AutoRollover
	}
	return nil
}

func (x *ServiceAttributes) GetAvailableStaff() *AvailableStaff {
	if x != nil {
		return x.AvailableStaff
	}
	return nil
}

func (x *ServiceAttributes) GetAvailableLodgingType() *AvailableLodgingType {
	if x != nil {
		return x.AvailableLodgingType
	}
	return nil
}

func (x *ServiceAttributes) GetIsRequiredStaff() bool {
	if x != nil && x.IsRequiredStaff != nil {
		return *x.IsRequiredStaff
	}
	return false
}

var File_backend_proto_offering_v1_service_proto protoreflect.FileDescriptor

const file_backend_proto_offering_v1_service_proto_rawDesc = "" +
	"\n" +
	"'backend/proto/offering/v1/service.proto\x12\x19backend.proto.offering.v1\x1a&backend/proto/offering/v1/common.proto\x1a2backend/proto/offering/v1/service_ob_setting.proto\x1a0backend/proto/organization/v1/organization.proto\x1a\x1bbuf/validate/validate.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xb8\t\n" +
	"\aService\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\\\n" +
	"\x11organization_type\x18\x02 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeR\x10organizationType\x12'\n" +
	"\x0forganization_id\x18\x03 \x01(\x03R\x0eorganizationId\x12 \n" +
	"\fcare_type_id\x18\x04 \x01(\x03R\n" +
	"careTypeId\x12$\n" +
	"\vcategory_id\x18\x05 \x01(\x03H\x00R\n" +
	"categoryId\x88\x01\x01\x12\x12\n" +
	"\x04name\x18\x06 \x01(\tR\x04name\x12%\n" +
	"\vdescription\x18\a \x01(\tH\x01R\vdescription\x88\x01\x01\x12\x1d\n" +
	"\n" +
	"color_code\x18\b \x01(\tR\tcolorCode\x12\x17\n" +
	"\x04sort\x18\t \x01(\x03H\x02R\x04sort\x88\x01\x01\x12\x16\n" +
	"\x06images\x18\n" +
	" \x03(\tR\x06images\x12A\n" +
	"\x06source\x18\v \x01(\x0e2).backend.proto.offering.v1.OfferingSourceR\x06source\x12A\n" +
	"\x06status\x18\f \x01(\x0e2).backend.proto.offering.v1.Service.StatusR\x06status\x12\x1d\n" +
	"\n" +
	"is_deleted\x18\r \x01(\bR\tisDeleted\x12;\n" +
	"\vcreate_time\x18\x14 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x15 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12@\n" +
	"\vdelete_time\x18\x16 \x01(\v2\x1a.google.protobuf.TimestampH\x03R\n" +
	"deleteTime\x88\x01\x01\x12[\n" +
	"\x12available_business\x18\x17 \x01(\v2,.backend.proto.offering.v1.AvailableBusinessR\x11availableBusiness\x12`\n" +
	"\x12additional_service\x18\x18 \x01(\v2,.backend.proto.offering.v1.AdditionalServiceH\x04R\x11additionalService\x88\x01\x01\x12Q\n" +
	"\n" +
	"attributes\x18c \x01(\v2,.backend.proto.offering.v1.ServiceAttributesH\x05R\n" +
	"attributes\x88\x01\x01\":\n" +
	"\x06Status\x12\x16\n" +
	"\x12STATUS_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06ACTIVE\x10\x01\x12\f\n" +
	"\bINACTIVE\x10\x02\"5\n" +
	"\x04Type\x12\x14\n" +
	"\x10TYPE_UNSPECIFIED\x10\x00\x12\v\n" +
	"\aSERVICE\x10\x01\x12\n" +
	"\n" +
	"\x06ADD_ON\x10\x02B\x0e\n" +
	"\f_category_idB\x0e\n" +
	"\f_descriptionB\a\n" +
	"\x05_sortB\x0e\n" +
	"\f_delete_timeB\x15\n" +
	"\x13_additional_serviceB\r\n" +
	"\v_attributes\"\x9f\x02\n" +
	"\x11AdditionalService\x12I\n" +
	"\x18additional_care_type_ids\x18\x01 \x03(\x03B\x10\xbaH\r\x92\x01\n" +
	"\b\x00\x10d\"\x04\"\x02 \x00R\x15additionalCareTypeIds\x12G\n" +
	"\x16additional_service_ids\x18\x02 \x03(\x03B\x11\xbaH\x0e\x92\x01\v\b\x00\x10\xe8\a\"\x04\"\x02 \x00R\x14additionalServiceIds\x12v\n" +
	"\x18additional_service_types\x18\x03 \x03(\x0e2'.backend.proto.offering.v1.Service.TypeB\x13\xbaH\x10\x92\x01\r\b\x00\x10\n" +
	"\"\a\x82\x01\x04\x10\x01 \x00R\x16additionalServiceTypes\"\\\n" +
	"\x10ExtraInfoOptions\x121\n" +
	"\x12include_ob_setting\x18\x01 \x01(\bH\x00R\x10includeObSetting\x88\x01\x01B\x15\n" +
	"\x13_include_ob_setting\"\xa0\x01\n" +
	"\x14ServiceWithExtraInfo\x12<\n" +
	"\aservice\x18\x01 \x01(\v2\".backend.proto.offering.v1.ServiceR\aservice\x12J\n" +
	"\n" +
	"ob_setting\x18\x02 \x01(\v2+.backend.proto.offering.v1.ServiceOBSettingR\tobSetting\"w\n" +
	"\fAutoRollover\x12\x18\n" +
	"\aenabled\x18\x01 \x01(\bR\aenabled\x12*\n" +
	"\x11target_service_id\x18\x02 \x01(\x03R\x0ftargetServiceId\x12!\n" +
	"\fafter_minute\x18\x03 \x01(\x05R\vafterMinute\"M\n" +
	"\x11AvailableBusiness\x12\x15\n" +
	"\x06is_all\x18\x01 \x01(\bR\x05isAll\x12!\n" +
	"\fbusiness_ids\x18\x02 \x03(\x03R\vbusinessIds\"D\n" +
	"\x0eAvailableStaff\x12\x15\n" +
	"\x06is_all\x18\x01 \x01(\bR\x05isAll\x12\x1b\n" +
	"\tstaff_ids\x18\x02 \x03(\x03R\bstaffIds\"W\n" +
	"\x14AvailableLodgingType\x12\x15\n" +
	"\x06is_all\x18\x01 \x01(\bR\x05isAll\x12(\n" +
	"\x10lodging_type_ids\x18\x02 \x03(\x03R\x0elodgingTypeIds\"\x9a\x04\n" +
	"\x11ServiceAttributes\x12\x1f\n" +
	"\bduration\x18\x01 \x01(\x05H\x00R\bduration\x88\x01\x01\x12&\n" +
	"\fmax_duration\x18\x02 \x01(\x05H\x01R\vmaxDuration\x88\x01\x01\x12Q\n" +
	"\rauto_rollover\x18\x03 \x01(\v2'.backend.proto.offering.v1.AutoRolloverH\x02R\fautoRollover\x88\x01\x01\x12W\n" +
	"\x0favailable_staff\x18\x04 \x01(\v2).backend.proto.offering.v1.AvailableStaffH\x03R\x0eavailableStaff\x88\x01\x01\x12j\n" +
	"\x16available_lodging_type\x18\x05 \x01(\v2/.backend.proto.offering.v1.AvailableLodgingTypeH\x04R\x14availableLodgingType\x88\x01\x01\x12/\n" +
	"\x11is_required_staff\x18\x06 \x01(\bH\x05R\x0fisRequiredStaff\x88\x01\x01B\v\n" +
	"\t_durationB\x0f\n" +
	"\r_max_durationB\x10\n" +
	"\x0e_auto_rolloverB\x12\n" +
	"\x10_available_staffB\x19\n" +
	"\x17_available_lodging_typeB\x14\n" +
	"\x12_is_required_staffBk\n" +
	"#com.moego.backend.proto.offering.v1P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpbb\x06proto3"

var (
	file_backend_proto_offering_v1_service_proto_rawDescOnce sync.Once
	file_backend_proto_offering_v1_service_proto_rawDescData []byte
)

func file_backend_proto_offering_v1_service_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_v1_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_v1_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_proto_rawDesc), len(file_backend_proto_offering_v1_service_proto_rawDesc)))
	})
	return file_backend_proto_offering_v1_service_proto_rawDescData
}

var file_backend_proto_offering_v1_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_backend_proto_offering_v1_service_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_backend_proto_offering_v1_service_proto_goTypes = []any{
	(Service_Status)(0),           // 0: backend.proto.offering.v1.Service.Status
	(Service_Type)(0),             // 1: backend.proto.offering.v1.Service.Type
	(*Service)(nil),               // 2: backend.proto.offering.v1.Service
	(*AdditionalService)(nil),     // 3: backend.proto.offering.v1.AdditionalService
	(*ExtraInfoOptions)(nil),      // 4: backend.proto.offering.v1.ExtraInfoOptions
	(*ServiceWithExtraInfo)(nil),  // 5: backend.proto.offering.v1.ServiceWithExtraInfo
	(*AutoRollover)(nil),          // 6: backend.proto.offering.v1.AutoRollover
	(*AvailableBusiness)(nil),     // 7: backend.proto.offering.v1.AvailableBusiness
	(*AvailableStaff)(nil),        // 8: backend.proto.offering.v1.AvailableStaff
	(*AvailableLodgingType)(nil),  // 9: backend.proto.offering.v1.AvailableLodgingType
	(*ServiceAttributes)(nil),     // 10: backend.proto.offering.v1.ServiceAttributes
	(v1.OrganizationType)(0),      // 11: backend.proto.organization.v1.OrganizationType
	(OfferingSource)(0),           // 12: backend.proto.offering.v1.OfferingSource
	(*timestamppb.Timestamp)(nil), // 13: google.protobuf.Timestamp
	(*ServiceOBSetting)(nil),      // 14: backend.proto.offering.v1.ServiceOBSetting
}
var file_backend_proto_offering_v1_service_proto_depIdxs = []int32{
	11, // 0: backend.proto.offering.v1.Service.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	12, // 1: backend.proto.offering.v1.Service.source:type_name -> backend.proto.offering.v1.OfferingSource
	0,  // 2: backend.proto.offering.v1.Service.status:type_name -> backend.proto.offering.v1.Service.Status
	13, // 3: backend.proto.offering.v1.Service.create_time:type_name -> google.protobuf.Timestamp
	13, // 4: backend.proto.offering.v1.Service.update_time:type_name -> google.protobuf.Timestamp
	13, // 5: backend.proto.offering.v1.Service.delete_time:type_name -> google.protobuf.Timestamp
	7,  // 6: backend.proto.offering.v1.Service.available_business:type_name -> backend.proto.offering.v1.AvailableBusiness
	3,  // 7: backend.proto.offering.v1.Service.additional_service:type_name -> backend.proto.offering.v1.AdditionalService
	10, // 8: backend.proto.offering.v1.Service.attributes:type_name -> backend.proto.offering.v1.ServiceAttributes
	1,  // 9: backend.proto.offering.v1.AdditionalService.additional_service_types:type_name -> backend.proto.offering.v1.Service.Type
	2,  // 10: backend.proto.offering.v1.ServiceWithExtraInfo.service:type_name -> backend.proto.offering.v1.Service
	14, // 11: backend.proto.offering.v1.ServiceWithExtraInfo.ob_setting:type_name -> backend.proto.offering.v1.ServiceOBSetting
	6,  // 12: backend.proto.offering.v1.ServiceAttributes.auto_rollover:type_name -> backend.proto.offering.v1.AutoRollover
	8,  // 13: backend.proto.offering.v1.ServiceAttributes.available_staff:type_name -> backend.proto.offering.v1.AvailableStaff
	9,  // 14: backend.proto.offering.v1.ServiceAttributes.available_lodging_type:type_name -> backend.proto.offering.v1.AvailableLodgingType
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_v1_service_proto_init() }
func file_backend_proto_offering_v1_service_proto_init() {
	if File_backend_proto_offering_v1_service_proto != nil {
		return
	}
	file_backend_proto_offering_v1_common_proto_init()
	file_backend_proto_offering_v1_service_ob_setting_proto_init()
	file_backend_proto_offering_v1_service_proto_msgTypes[0].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_proto_msgTypes[2].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_proto_msgTypes[8].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_proto_rawDesc), len(file_backend_proto_offering_v1_service_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_offering_v1_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_v1_service_proto_depIdxs,
		EnumInfos:         file_backend_proto_offering_v1_service_proto_enumTypes,
		MessageInfos:      file_backend_proto_offering_v1_service_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_v1_service_proto = out.File
	file_backend_proto_offering_v1_service_proto_goTypes = nil
	file_backend_proto_offering_v1_service_proto_depIdxs = nil
}
