// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.8
// 	protoc        (unknown)
// source: backend/proto/offering/v1/service_category.proto

package offeringpb

import (
	v1 "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Defines the structure for a service category, used to organize services.
type ServiceCategory struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Primary key ID of the service category.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The type of the organization (e.g., company, enterprise).
	OrganizationType v1.OrganizationType `protobuf:"varint,2,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// The ID of the organization this category belongs to.
	OrganizationId int64 `protobuf:"varint,3,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// The ID of the care type associated with this category.
	CareTypeId int64 `protobuf:"varint,4,opt,name=care_type_id,json=careTypeId,proto3" json:"care_type_id,omitempty"`
	// The name of the service category, unique within the same organization and care type.
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	// The sorting order of the service category.
	Sort int32 `protobuf:"varint,6,opt,name=sort,proto3" json:"sort,omitempty"`
	// The timestamp when the category was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The timestamp when the category was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// The timestamp when the category was soft-deleted. Null if not deleted.
	IsDeleted     bool `protobuf:"varint,9,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceCategory) Reset() {
	*x = ServiceCategory{}
	mi := &file_backend_proto_offering_v1_service_category_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceCategory) ProtoMessage() {}

func (x *ServiceCategory) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_category_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceCategory.ProtoReflect.Descriptor instead.
func (*ServiceCategory) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_category_proto_rawDescGZIP(), []int{0}
}

func (x *ServiceCategory) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceCategory) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *ServiceCategory) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *ServiceCategory) GetCareTypeId() int64 {
	if x != nil {
		return x.CareTypeId
	}
	return 0
}

func (x *ServiceCategory) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceCategory) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *ServiceCategory) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *ServiceCategory) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *ServiceCategory) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

var File_backend_proto_offering_v1_service_category_proto protoreflect.FileDescriptor

const file_backend_proto_offering_v1_service_category_proto_rawDesc = "" +
	"\n" +
	"0backend/proto/offering/v1/service_category.proto\x12\x19backend.proto.offering.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a0backend/proto/organization/v1/organization.proto\"\x8b\x03\n" +
	"\x0fServiceCategory\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\\\n" +
	"\x11organization_type\x18\x02 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeR\x10organizationType\x12'\n" +
	"\x0forganization_id\x18\x03 \x01(\x03R\x0eorganizationId\x12 \n" +
	"\fcare_type_id\x18\x04 \x01(\x03R\n" +
	"careTypeId\x12\x12\n" +
	"\x04name\x18\x05 \x01(\tR\x04name\x12\x12\n" +
	"\x04sort\x18\x06 \x01(\x05R\x04sort\x12;\n" +
	"\vcreate_time\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12\x1d\n" +
	"\n" +
	"is_deleted\x18\t \x01(\bR\tisDeletedBk\n" +
	"#com.moego.backend.proto.offering.v1P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpbb\x06proto3"

var (
	file_backend_proto_offering_v1_service_category_proto_rawDescOnce sync.Once
	file_backend_proto_offering_v1_service_category_proto_rawDescData []byte
)

func file_backend_proto_offering_v1_service_category_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_v1_service_category_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_v1_service_category_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_category_proto_rawDesc), len(file_backend_proto_offering_v1_service_category_proto_rawDesc)))
	})
	return file_backend_proto_offering_v1_service_category_proto_rawDescData
}

var file_backend_proto_offering_v1_service_category_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_backend_proto_offering_v1_service_category_proto_goTypes = []any{
	(*ServiceCategory)(nil),       // 0: backend.proto.offering.v1.ServiceCategory
	(v1.OrganizationType)(0),      // 1: backend.proto.organization.v1.OrganizationType
	(*timestamppb.Timestamp)(nil), // 2: google.protobuf.Timestamp
}
var file_backend_proto_offering_v1_service_category_proto_depIdxs = []int32{
	1, // 0: backend.proto.offering.v1.ServiceCategory.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	2, // 1: backend.proto.offering.v1.ServiceCategory.create_time:type_name -> google.protobuf.Timestamp
	2, // 2: backend.proto.offering.v1.ServiceCategory.update_time:type_name -> google.protobuf.Timestamp
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_v1_service_category_proto_init() }
func file_backend_proto_offering_v1_service_category_proto_init() {
	if File_backend_proto_offering_v1_service_category_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_category_proto_rawDesc), len(file_backend_proto_offering_v1_service_category_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_offering_v1_service_category_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_v1_service_category_proto_depIdxs,
		MessageInfos:      file_backend_proto_offering_v1_service_category_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_v1_service_category_proto = out.File
	file_backend_proto_offering_v1_service_category_proto_goTypes = nil
	file_backend_proto_offering_v1_service_category_proto_depIdxs = nil
}
