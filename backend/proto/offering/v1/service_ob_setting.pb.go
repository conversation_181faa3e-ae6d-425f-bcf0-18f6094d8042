// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.8
// 	protoc        (unknown)
// source: backend/proto/offering/v1/service_ob_setting.proto

package offeringpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Display price mode for online booking
type ServiceOBSetting_ShowBasePriceMode int32

const (
	// Unspecified show base price mode
	ServiceOBSetting_SHOW_BASE_PRICE_MODE_UNSPECIFIED ServiceOBSetting_ShowBasePriceMode = 0
	// Do not show price
	ServiceOBSetting_SHOW_BASE_PRICE_MODE_DO_NOT_SHOW ServiceOBSetting_ShowBasePriceMode = 1
	// Show fixed price
	ServiceOBSetting_SHOW_BASE_PRICE_MODE_SHOW_FIXED_PRICE ServiceOBSetting_ShowBasePriceMode = 2
	// Show "starting at" price
	ServiceOBSetting_SHOW_BASE_PRICE_MODE_SHOW_STARTING_AT ServiceOBSetting_ShowBasePriceMode = 3
	// Show "varies" price
	ServiceOBSetting_SHOW_BASE_PRICE_MODE_SHOW_VARIES ServiceOBSetting_ShowBasePriceMode = 4
)

// Enum value maps for ServiceOBSetting_ShowBasePriceMode.
var (
	ServiceOBSetting_ShowBasePriceMode_name = map[int32]string{
		0: "SHOW_BASE_PRICE_MODE_UNSPECIFIED",
		1: "SHOW_BASE_PRICE_MODE_DO_NOT_SHOW",
		2: "SHOW_BASE_PRICE_MODE_SHOW_FIXED_PRICE",
		3: "SHOW_BASE_PRICE_MODE_SHOW_STARTING_AT",
		4: "SHOW_BASE_PRICE_MODE_SHOW_VARIES",
	}
	ServiceOBSetting_ShowBasePriceMode_value = map[string]int32{
		"SHOW_BASE_PRICE_MODE_UNSPECIFIED":      0,
		"SHOW_BASE_PRICE_MODE_DO_NOT_SHOW":      1,
		"SHOW_BASE_PRICE_MODE_SHOW_FIXED_PRICE": 2,
		"SHOW_BASE_PRICE_MODE_SHOW_STARTING_AT": 3,
		"SHOW_BASE_PRICE_MODE_SHOW_VARIES":      4,
	}
)

func (x ServiceOBSetting_ShowBasePriceMode) Enum() *ServiceOBSetting_ShowBasePriceMode {
	p := new(ServiceOBSetting_ShowBasePriceMode)
	*p = x
	return p
}

func (x ServiceOBSetting_ShowBasePriceMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceOBSetting_ShowBasePriceMode) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_service_ob_setting_proto_enumTypes[0].Descriptor()
}

func (ServiceOBSetting_ShowBasePriceMode) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_service_ob_setting_proto_enumTypes[0]
}

func (x ServiceOBSetting_ShowBasePriceMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceOBSetting_ShowBasePriceMode.Descriptor instead.
func (ServiceOBSetting_ShowBasePriceMode) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_ob_setting_proto_rawDescGZIP(), []int{0, 0}
}

// Defines the structure for a service online booking setting, which controls how a service appears and behaves in online booking.
type ServiceOBSetting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Reference to the service template
	ServiceId int64 `protobuf:"varint,4,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// Whether the service is available for online booking
	IsAvailable bool `protobuf:"varint,5,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`
	// Display price mode: 0 - Do not show, 1 - Show fixed price, 2 - Show "starting at", 3 - Show "varies"
	ShowBasePrice ServiceOBSetting_ShowBasePriceMode `protobuf:"varint,6,opt,name=show_base_price,json=showBasePrice,proto3,enum=backend.proto.offering.v1.ServiceOBSetting_ShowBasePriceMode" json:"show_base_price,omitempty"`
	// Whether all staff are available for this service when booking online
	IsAllStaff bool `protobuf:"varint,7,opt,name=is_all_staff,json=isAllStaff,proto3" json:"is_all_staff,omitempty"`
	// Whether all staff are available for this service when booking online
	StaffIds []int64 `protobuf:"varint,8,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
	// Optional display alias for the service in online booking
	Alias         string `protobuf:"bytes,9,opt,name=alias,proto3" json:"alias,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceOBSetting) Reset() {
	*x = ServiceOBSetting{}
	mi := &file_backend_proto_offering_v1_service_ob_setting_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceOBSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceOBSetting) ProtoMessage() {}

func (x *ServiceOBSetting) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_ob_setting_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceOBSetting.ProtoReflect.Descriptor instead.
func (*ServiceOBSetting) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_ob_setting_proto_rawDescGZIP(), []int{0}
}

func (x *ServiceOBSetting) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *ServiceOBSetting) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

func (x *ServiceOBSetting) GetShowBasePrice() ServiceOBSetting_ShowBasePriceMode {
	if x != nil {
		return x.ShowBasePrice
	}
	return ServiceOBSetting_SHOW_BASE_PRICE_MODE_UNSPECIFIED
}

func (x *ServiceOBSetting) GetIsAllStaff() bool {
	if x != nil {
		return x.IsAllStaff
	}
	return false
}

func (x *ServiceOBSetting) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

func (x *ServiceOBSetting) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

var File_backend_proto_offering_v1_service_ob_setting_proto protoreflect.FileDescriptor

const file_backend_proto_offering_v1_service_ob_setting_proto_rawDesc = "" +
	"\n" +
	"2backend/proto/offering/v1/service_ob_setting.proto\x12\x19backend.proto.offering.v1\"\xee\x03\n" +
	"\x10ServiceOBSetting\x12\x1d\n" +
	"\n" +
	"service_id\x18\x04 \x01(\x03R\tserviceId\x12!\n" +
	"\fis_available\x18\x05 \x01(\bR\visAvailable\x12e\n" +
	"\x0fshow_base_price\x18\x06 \x01(\x0e2=.backend.proto.offering.v1.ServiceOBSetting.ShowBasePriceModeR\rshowBasePrice\x12 \n" +
	"\fis_all_staff\x18\a \x01(\bR\n" +
	"isAllStaff\x12\x1b\n" +
	"\tstaff_ids\x18\b \x03(\x03R\bstaffIds\x12\x14\n" +
	"\x05alias\x18\t \x01(\tR\x05alias\"\xdb\x01\n" +
	"\x11ShowBasePriceMode\x12$\n" +
	" SHOW_BASE_PRICE_MODE_UNSPECIFIED\x10\x00\x12$\n" +
	" SHOW_BASE_PRICE_MODE_DO_NOT_SHOW\x10\x01\x12)\n" +
	"%SHOW_BASE_PRICE_MODE_SHOW_FIXED_PRICE\x10\x02\x12)\n" +
	"%SHOW_BASE_PRICE_MODE_SHOW_STARTING_AT\x10\x03\x12$\n" +
	" SHOW_BASE_PRICE_MODE_SHOW_VARIES\x10\x04B\x87\x01\n" +
	"#com.moego.backend.proto.offering.v1B\x1aServiceOBSettingOuterClassP\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpbb\x06proto3"

var (
	file_backend_proto_offering_v1_service_ob_setting_proto_rawDescOnce sync.Once
	file_backend_proto_offering_v1_service_ob_setting_proto_rawDescData []byte
)

func file_backend_proto_offering_v1_service_ob_setting_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_v1_service_ob_setting_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_v1_service_ob_setting_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_ob_setting_proto_rawDesc), len(file_backend_proto_offering_v1_service_ob_setting_proto_rawDesc)))
	})
	return file_backend_proto_offering_v1_service_ob_setting_proto_rawDescData
}

var file_backend_proto_offering_v1_service_ob_setting_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_backend_proto_offering_v1_service_ob_setting_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_backend_proto_offering_v1_service_ob_setting_proto_goTypes = []any{
	(ServiceOBSetting_ShowBasePriceMode)(0), // 0: backend.proto.offering.v1.ServiceOBSetting.ShowBasePriceMode
	(*ServiceOBSetting)(nil),                // 1: backend.proto.offering.v1.ServiceOBSetting
}
var file_backend_proto_offering_v1_service_ob_setting_proto_depIdxs = []int32{
	0, // 0: backend.proto.offering.v1.ServiceOBSetting.show_base_price:type_name -> backend.proto.offering.v1.ServiceOBSetting.ShowBasePriceMode
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_v1_service_ob_setting_proto_init() }
func file_backend_proto_offering_v1_service_ob_setting_proto_init() {
	if File_backend_proto_offering_v1_service_ob_setting_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_ob_setting_proto_rawDesc), len(file_backend_proto_offering_v1_service_ob_setting_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_offering_v1_service_ob_setting_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_v1_service_ob_setting_proto_depIdxs,
		EnumInfos:         file_backend_proto_offering_v1_service_ob_setting_proto_enumTypes,
		MessageInfos:      file_backend_proto_offering_v1_service_ob_setting_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_v1_service_ob_setting_proto = out.File
	file_backend_proto_offering_v1_service_ob_setting_proto_goTypes = nil
	file_backend_proto_offering_v1_service_ob_setting_proto_depIdxs = nil
}
