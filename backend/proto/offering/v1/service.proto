syntax = "proto3";

package backend.proto.offering.v1;

import "backend/proto/offering/v1/service_ob_setting.proto";
import "backend/proto/organization/v1/organization.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.offering.v1";

// Defines the structure for a service, which acts as a blueprint for creating specific service instances.
message Service {
  // Primary key ID of the service
  int64 id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The type of the organization (e.g., "enterprise", "company").
  backend.proto.organization.v1.OrganizationType organization_type = 2 [(google.api.field_behavior) = IMMUTABLE];

  // The ID of the organization.
  int64 organization_id = 3 [(google.api.field_behavior) = IMMUTABLE];

  // The ID of the care type associated with this service.
  int64 care_type_id = 4 [(google.api.field_behavior) = REQUIRED];

  // The ID of the category this service.
  optional int64 category_id = 5 [(google.api.field_behavior) = OPTIONAL];

  // Name of the service, unique within the same company
  string name = 6 [(google.api.field_behavior) = REQUIRED];

  // Optional description of the service
  optional string description = 7 [(google.api.field_behavior) = OPTIONAL];

  // A color code associated with the service for UI purposes.
  string color_code = 8 [(google.api.field_behavior) = REQUIRED];

  // The sorting order of the service.
  optional int64 sort = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // A list of image URLs for the service.
  repeated string images = 10 [(google.api.field_behavior) = OPTIONAL];

  // The source of the service.
  Source source = 11 [(google.api.field_behavior) = IMMUTABLE];

  // (-- api-linter: core::0216::synonyms=disabled
  //     aip.dev/not-precedent: 保持 status 命名设计. --)
  // The status of the service.
  Status status = 12 [(google.api.field_behavior) = REQUIRED];

  // Is deleted
  bool is_deleted = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The timestamp when the service was created.
  google.protobuf.Timestamp create_time = 20 [(google.api.field_behavior) = OUTPUT_ONLY];
  // The timestamp when the service was last updated.
  google.protobuf.Timestamp update_time = 21 [(google.api.field_behavior) = OUTPUT_ONLY];
  // The timestamp when the service was deleted.
  optional google.protobuf.Timestamp delete_time = 22 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The available business scope for this service.
  AvailableBusiness available_business = 23 [(google.api.field_behavior) = REQUIRED];

  // Service attributes for better frontend type safety
  optional ServiceAttributes attributes = 99 [(google.api.field_behavior) = OPTIONAL];

  // (-- api-linter: core::0216::synonyms=disabled
  //     aip.dev/not-precedent: 保持 status 命名设计. --)
  // The status of the service.
  enum Status {
    // Unspecified
    STATUS_UNSPECIFIED = 0;
    // Active
    ACTIVE = 1;
    // Inactive
    INACTIVE = 2;
  }

  // Service source.
  enum Source {
    // Unspecified.
    SOURCE_UNSPECIFIED = 0;
    // MoeGo platform.
    MOEGO = 1;
    // Enterprise Hub.
    ENTERPRISE_HUB = 2;
  }

  // Service type.
  enum Type {
    // Unspecified.
    TYPE_UNSPECIFIED = 0;
    // Service.
    SERVICE = 1;
    // Add-On.
    ADD_ON = 2;
  }
}

// optional extra info to include in response
message ExtraInfoOptions {
  // whether to include online booking setting
  optional bool include_ob_setting = 1;
}

//service and service extension
message ServiceWithExtraInfo {
    // service base
    Service service = 1  [(google.api.field_behavior) = REQUIRED];
    // online booking setting
    ServiceOBSetting ob_setting = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Defines a service auto rollover.
message AutoRollover {
  // Enabled auto rollover
  bool enabled = 1 [(google.api.field_behavior) = REQUIRED];
  
  // The ID of the target service.
  int64 target_service_id = 2 [(google.api.field_behavior) = REQUIRED];

  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: after_minute 表示服务后多少分钟. --)
  // The number of minutes after the max duration to trigger auto rollover.
  int32 after_minute = 3 [(google.api.field_behavior) = REQUIRED];
}

// Defines available business configuration for a service
message AvailableBusiness {
  // Whether the service is available for all businesses
  bool is_all = 1 [(google.api.field_behavior) = REQUIRED];
  
  // List of specific business IDs when is_all is false
  repeated int64 business_ids = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Defines available staff configuration for a service
message AvailableStaff {
  // Whether the service is available for all staff
  bool is_all = 1 [(google.api.field_behavior) = REQUIRED];
  
  // List of specific staff IDs when is_all is false
  repeated int64 staff_ids = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Defines available lodging configuration for a service
message AvailableLodgingType {
  // Whether the service is available for all lodging
  bool is_all = 1 [(google.api.field_behavior) = REQUIRED];
  
  // List of specific lodging IDs when is_all is false
  repeated int64 lodging_type_ids = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Defines service attributes for better frontend type safety
message ServiceAttributes {
  // Duration of the service.
  optional int32 duration = 1;

  // Max duration of the service.
  optional int32 max_duration = 2;

  // Auto rollover configuration. auto rollover will be triggered when the service duration is greater than the max duration.
  optional AutoRollover auto_rollover = 3;
  
  // Available staff configuration. available staff will be used to schedule the service.
  optional AvailableStaff available_staff = 4;
  
  // Available lodging type configuration. available lodging type will be used to schedule the service.
  optional AvailableLodgingType available_lodging_type = 5;
}

// Defines the structure for a add on, which acts as a blueprint for creating specific add on instances.
message AddOn {
  // Primary key ID of the add on
  int64 id = 1;

  // The type of the organization (e.g., "enterprise", "company").
  backend.proto.organization.v1.OrganizationType organization_type = 2;

  // The ID of the organization.
  int64 organization_id = 3;

  // The flag of required staff.
  bool is_required_staff = 4;

  // The ID of the category this add on.
  optional int64 category_id = 5;

  // Name of the add on, unique within the same company
  string name = 6;

  // Optional description of the add on
  optional string description = 7;

  // A color code associated with the add on for UI purposes.
  string color_code = 8;

  // The sorting order of the add on.
  optional int64 sort = 9;

  // A list of image URLs for the add on.
  repeated string images = 10;

  // The source of the add on.
  Source source = 11;

  // (-- api-linter: core::0216::synonyms=disabled
  //     aip.dev/not-precedent: 保持 status 命名设计. --)
  // The status of the add on.
  Status status = 12;

  // Is deleted
  bool is_deleted = 13;

  // The timestamp when the add on was created.
  google.protobuf.Timestamp create_time = 20;
  // The timestamp when the add on was last updated.
  google.protobuf.Timestamp update_time = 21;
  // The timestamp when the add on was deleted.
  optional google.protobuf.Timestamp delete_time = 22;

  // The available business scope for this add on.
  AvailableBusiness available_business = 23;

  // The available service scope for this add on.
  AvailableService available_service = 24;

  // (-- api-linter: core::0216::synonyms=disabled
  //     aip.dev/not-precedent: 保持 status 命名设计. --)
  // The status of the add on.
  enum Status {
    // Unspecified
    STATUS_UNSPECIFIED = 0;
    // Active
    ACTIVE = 1;
    // Inactive
    INACTIVE = 2;
  }

  // add on source.
  enum Source {
    // Unspecified.
    SOURCE_UNSPECIFIED = 0;
    // MoeGo platform.
    MOEGO = 1;
    // Enterprise Hub.
    ENTERPRISE_HUB = 2;
  }
}

// Available service for add on.
message AvailableService {

}