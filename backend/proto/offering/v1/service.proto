syntax = "proto3";

package backend.proto.offering.v1;

import "backend/proto/offering/v1/common.proto";
import "backend/proto/offering/v1/service_ob_setting.proto";
import "backend/proto/organization/v1/organization.proto";
import "buf/validate/validate.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.offering.v1";

// Defines the structure for a service, which acts as a blueprint for creating specific service instances.
message Service {
  // Primary key ID of the service
  int64 id = 1;

  // The type of the organization (e.g., "enterprise", "company").
  backend.proto.organization.v1.OrganizationType organization_type = 2;

  // The ID of the organization.
  int64 organization_id = 3;

  // The ID of the care type associated with this service.
  int64 care_type_id = 4;

  // The ID of the category this service.
  optional int64 category_id = 5;

  // Name of the service, unique within the same company
  string name = 6;

  // Optional description of the service
  optional string description = 7;

  // A color code associated with the service for UI purposes.
  string color_code = 8;

  // The sorting order of the service.
  optional int64 sort = 9;

  // A list of image URLs for the service.
  repeated string images = 10;

  // The offering source of the service.
  OfferingSource source = 11;

  // (-- api-linter: core::0216::synonyms=disabled
  //     aip.dev/not-precedent: 保持 status 命名设计. --)
  // The status of the service.
  Status status = 12;

  // Is deleted
  bool is_deleted = 13;

  // The timestamp when the service was created.
  google.protobuf.Timestamp create_time = 20;
  // The timestamp when the service was last updated.
  google.protobuf.Timestamp update_time = 21;
  // The timestamp when the service was deleted.
  optional google.protobuf.Timestamp delete_time = 22;

  // The available business scope for this service.
  AvailableBusiness available_business = 23;

  // Additional service/addon scope configuration
  optional AdditionalService additional_service = 24;

  // Service attributes for better frontend type safety
  optional ServiceAttributes attributes = 99;

  // (-- api-linter: core::0216::synonyms=disabled
  //     aip.dev/not-precedent: 保持 status 命名设计. --)
  // The status of the service.
  enum Status {
    // Unspecified
    STATUS_UNSPECIFIED = 0;
    // Active
    ACTIVE = 1;
    // Inactive
    INACTIVE = 2;
  }

  // Service type.
  enum Type {
    // Unspecified.
    TYPE_UNSPECIFIED = 0;
    // Service.
    SERVICE = 1;
    // Add-On.
    ADD_ON = 2;
  }
}

// Additional service/addon scope configuration
message AdditionalService {
  // List of care type IDs to include all services under those care types
  repeated int64 additional_care_type_ids = 1 [(buf.validate.field).repeated = {
    min_items: 0,
    max_items: 100,
    items: {
      int64: {gt: 0}
    }
  }];

  // List of specific additional service/addon IDs when is_all is false
  repeated int64 additional_service_ids = 2 [(buf.validate.field).repeated = {
    min_items: 0,
    max_items: 1000,
    items: {
      int64: {gt: 0}
    }
  }];
  
  // List of service types to include (SERVICE, ADD_ON, or both)
  repeated Service.Type additional_service_types = 3 [(buf.validate.field).repeated = {
    min_items: 0,
    max_items: 10,
    items: {
      enum: {
        not_in: [0],
        defined_only: true
      }
    }
  }];
}

// optional extra info to include in response
message ExtraInfoOptions {
  // whether to include online booking setting
  optional bool include_ob_setting = 1;
}

//service and service extension
message ServiceWithExtraInfo {
    // service base
    Service service = 1 ;
    // online booking setting
    ServiceOBSetting ob_setting = 2;
}

// Defines a service auto rollover.
message AutoRollover {
  // Enabled auto rollover
  bool enabled = 1;
  
  // The ID of the target service.
  int64 target_service_id = 2;

  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: after_minute 表示服务后多少分钟. --)
  // The number of minutes after the max duration to trigger auto rollover.
  int32 after_minute = 3;
}

// Defines available business configuration for a service
message AvailableBusiness {
  // Whether the service is available for all businesses
  bool is_all = 1;
  
  // List of specific business IDs when is_all is false
  repeated int64 business_ids = 2;
}

// Defines available staff configuration for a service
message AvailableStaff {
  // Whether the service is available for all staff
  bool is_all = 1;
  
  // List of specific staff IDs when is_all is false
  repeated int64 staff_ids = 2;
}

// Defines available lodging configuration for a service
message AvailableLodgingType {
  // Whether the service is available for all lodging
  bool is_all = 1;
  
  // List of specific lodging IDs when is_all is false
  repeated int64 lodging_type_ids = 2;
}

// Defines service attributes for better frontend type safety
message ServiceAttributes {
  // Duration of the service.
  optional int32 duration = 1;

  // Max duration of the service.
  optional int32 max_duration = 2;

  // Auto rollover configuration. auto rollover will be triggered when the service duration is greater than the max duration.
  optional AutoRollover auto_rollover = 3;
  
  // Available staff configuration. available staff will be used to schedule the service.
  optional AvailableStaff available_staff = 4;
  
  // Available lodging type configuration. available lodging type will be used to schedule the service.
  optional AvailableLodgingType available_lodging_type = 5;
  
  // Whether the addon requires staff (only for ADD_ON type services)
  optional bool is_required_staff = 6;
}