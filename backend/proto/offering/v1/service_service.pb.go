// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.8
// 	protoc        (unknown)
// source: backend/proto/offering/v1/service_service.proto

package offeringpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	v1 "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 删除服务请求
type DeleteServiceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务ID
	ServiceId     int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteServiceRequest) Reset() {
	*x = DeleteServiceRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteServiceRequest) ProtoMessage() {}

func (x *DeleteServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteServiceRequest.ProtoReflect.Descriptor instead.
func (*DeleteServiceRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{0}
}

func (x *DeleteServiceRequest) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

// 删除服务响应
type DeleteServiceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteServiceResponse) Reset() {
	*x = DeleteServiceResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteServiceResponse) ProtoMessage() {}

func (x *DeleteServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteServiceResponse.ProtoReflect.Descriptor instead.
func (*DeleteServiceResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{1}
}

// 更新服务请求
type UpdateServiceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务
	Service       *Service `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateServiceRequest) Reset() {
	*x = UpdateServiceRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceRequest) ProtoMessage() {}

func (x *UpdateServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceRequest.ProtoReflect.Descriptor instead.
func (*UpdateServiceRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateServiceRequest) GetService() *Service {
	if x != nil {
		return x.Service
	}
	return nil
}

// 更新服务响应
type UpdateServiceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateServiceResponse) Reset() {
	*x = UpdateServiceResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceResponse) ProtoMessage() {}

func (x *UpdateServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceResponse.ProtoReflect.Descriptor instead.
func (*UpdateServiceResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{3}
}

// 创建服务请求
type CreateServiceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务
	Service       *Service `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateServiceRequest) Reset() {
	*x = CreateServiceRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceRequest) ProtoMessage() {}

func (x *CreateServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceRequest.ProtoReflect.Descriptor instead.
func (*CreateServiceRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{4}
}

func (x *CreateServiceRequest) GetService() *Service {
	if x != nil {
		return x.Service
	}
	return nil
}

// 创建服务响应
type CreateServiceResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务 ID
	ServiceId     int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateServiceResponse) Reset() {
	*x = CreateServiceResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceResponse) ProtoMessage() {}

func (x *CreateServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceResponse.ProtoReflect.Descriptor instead.
func (*CreateServiceResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{5}
}

func (x *CreateServiceResponse) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

// 获取服务请求
type GetServiceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务ID
	ServiceId     int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceRequest) Reset() {
	*x = GetServiceRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceRequest) ProtoMessage() {}

func (x *GetServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceRequest.ProtoReflect.Descriptor instead.
func (*GetServiceRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetServiceRequest) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

// 获取服务响应
type GetServiceResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务
	Service       *Service `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceResponse) Reset() {
	*x = GetServiceResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceResponse) ProtoMessage() {}

func (x *GetServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceResponse.ProtoReflect.Descriptor instead.
func (*GetServiceResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetServiceResponse) GetService() *Service {
	if x != nil {
		return x.Service
	}
	return nil
}

// 分类服务
type CategoryService struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 分类ID
	CategoryId int64 `protobuf:"varint,1,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// 分类名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 服务模板列表
	Services      []*Service `protobuf:"bytes,3,rep,name=services,proto3" json:"services,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CategoryService) Reset() {
	*x = CategoryService{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CategoryService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CategoryService) ProtoMessage() {}

func (x *CategoryService) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CategoryService.ProtoReflect.Descriptor instead.
func (*CategoryService) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{8}
}

func (x *CategoryService) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *CategoryService) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CategoryService) GetServices() []*Service {
	if x != nil {
		return x.Services
	}
	return nil
}

// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0132::request-parent-required=disabled
//
//	aip.dev/not-precedent: 无 parent 语义 --)
//
// list services request
// TODO: CR List 和 BatchGet 分开
type ListServicesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The organization type
	OrganizationType v1.OrganizationType `protobuf:"varint,1,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// The organization ID.
	OrganizationId int64 `protobuf:"varint,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// filter
	Filter *ListServicesRequest_Filter `protobuf:"bytes,18,opt,name=filter,proto3" json:"filter,omitempty"`
	// options to include extra info in response
	ExtraInfoOptions *ExtraInfoOptions `protobuf:"bytes,19,opt,name=extra_info_options,json=extraInfoOptions,proto3" json:"extra_info_options,omitempty"`
	// 分页信息
	Pagination    *PaginationRef `protobuf:"bytes,20,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListServicesRequest) Reset() {
	*x = ListServicesRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServicesRequest) ProtoMessage() {}

func (x *ListServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServicesRequest.ProtoReflect.Descriptor instead.
func (*ListServicesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{9}
}

func (x *ListServicesRequest) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *ListServicesRequest) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *ListServicesRequest) GetFilter() *ListServicesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListServicesRequest) GetExtraInfoOptions() *ExtraInfoOptions {
	if x != nil {
		return x.ExtraInfoOptions
	}
	return nil
}

func (x *ListServicesRequest) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// (-- api-linter: core::0158::response-next-page-token-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0132::response-unknown-fields=disabled
//
//	aip.dev/not-precedent: 额外返回 total. --)
//
// list setting services response
type ListServicesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务模板列表
	Services []*ServiceWithExtraInfo `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"`
	// 分页信息
	Pagination *PaginationRef `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// 总数
	Total         int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListServicesResponse) Reset() {
	*x = ListServicesResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServicesResponse) ProtoMessage() {}

func (x *ListServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServicesResponse.ProtoReflect.Descriptor instead.
func (*ListServicesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{10}
}

func (x *ListServicesResponse) GetServices() []*ServiceWithExtraInfo {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *ListServicesResponse) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListServicesResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// (-- api-linter: core::0234::request-unknown-fields=disabled
//
//	aip.dev/not-precedent: 需要租户信息 --)
//
// (-- api-linter: core::0234::request-requests-field=disabled
//
//	aip.dev/not-precedent: 不复用 UpdateServiceRequest 结构 --)
//
// (-- api-linter: core::0234::request-parent-field=disabled
//
//	aip.dev/not-precedent: 无 parent 语义 --)
//
// 批量更新服务请求
type BatchUpdateServicesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 当前的租户类型
	OrganizationType v1.OrganizationType `protobuf:"varint,1,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// 当前的租户 ID
	OrganizationId int64 `protobuf:"varint,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// 服务更新配置列表
	UpdateServices []*ServiceUpdateDef `protobuf:"bytes,3,rep,name=update_services,json=updateServices,proto3" json:"update_services,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BatchUpdateServicesRequest) Reset() {
	*x = BatchUpdateServicesRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchUpdateServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateServicesRequest) ProtoMessage() {}

func (x *BatchUpdateServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateServicesRequest.ProtoReflect.Descriptor instead.
func (*BatchUpdateServicesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{11}
}

func (x *BatchUpdateServicesRequest) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *BatchUpdateServicesRequest) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *BatchUpdateServicesRequest) GetUpdateServices() []*ServiceUpdateDef {
	if x != nil {
		return x.UpdateServices
	}
	return nil
}

// 单个服务的更新配置
type ServiceUpdateDef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务 ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 分类 ID（可选）
	CategoryId *int64 `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3,oneof" json:"category_id,omitempty"`
	// 服务名称（可选）
	Name *string `protobuf:"bytes,3,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// 描述
	Description *string `protobuf:"bytes,4,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// 颜色
	ColorCode *string `protobuf:"bytes,5,opt,name=color_code,json=colorCode,proto3,oneof" json:"color_code,omitempty"`
	// 排序值（可选）
	Sort *int64 `protobuf:"varint,6,opt,name=sort,proto3,oneof" json:"sort,omitempty"`
	// 图片
	Images []string `protobuf:"bytes,7,rep,name=images,proto3" json:"images,omitempty"`
	// 状态
	Status *Service_Status `protobuf:"varint,8,opt,name=status,proto3,enum=backend.proto.offering.v1.Service_Status,oneof" json:"status,omitempty"`
	// 可用 business scope
	AvailableBusiness *AvailableBusiness `protobuf:"bytes,9,opt,name=available_business,json=availableBusiness,proto3,oneof" json:"available_business,omitempty"`
	// 关联的属性值
	Attributes    *ServiceAttributes `protobuf:"bytes,10,opt,name=attributes,proto3,oneof" json:"attributes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceUpdateDef) Reset() {
	*x = ServiceUpdateDef{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceUpdateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceUpdateDef) ProtoMessage() {}

func (x *ServiceUpdateDef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceUpdateDef.ProtoReflect.Descriptor instead.
func (*ServiceUpdateDef) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{12}
}

func (x *ServiceUpdateDef) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceUpdateDef) GetCategoryId() int64 {
	if x != nil && x.CategoryId != nil {
		return *x.CategoryId
	}
	return 0
}

func (x *ServiceUpdateDef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *ServiceUpdateDef) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *ServiceUpdateDef) GetColorCode() string {
	if x != nil && x.ColorCode != nil {
		return *x.ColorCode
	}
	return ""
}

func (x *ServiceUpdateDef) GetSort() int64 {
	if x != nil && x.Sort != nil {
		return *x.Sort
	}
	return 0
}

func (x *ServiceUpdateDef) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *ServiceUpdateDef) GetStatus() Service_Status {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return Service_STATUS_UNSPECIFIED
}

func (x *ServiceUpdateDef) GetAvailableBusiness() *AvailableBusiness {
	if x != nil {
		return x.AvailableBusiness
	}
	return nil
}

func (x *ServiceUpdateDef) GetAttributes() *ServiceAttributes {
	if x != nil {
		return x.Attributes
	}
	return nil
}

// (-- api-linter: core::0234::response-resource-field=disabled
//
//	aip.dev/not-precedent: 不返回 Service 类型 --)
//
// 批量更新服务响应
type BatchUpdateServicesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchUpdateServicesResponse) Reset() {
	*x = BatchUpdateServicesResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchUpdateServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateServicesResponse) ProtoMessage() {}

func (x *BatchUpdateServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateServicesResponse.ProtoReflect.Descriptor instead.
func (*BatchUpdateServicesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{13}
}

// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0132::request-parent-required=disabled
//
//	aip.dev/not-precedent: 无 parent 语义 --)
//
// 查询可用服务请求（用于预约场景）
type ListAvailableServicesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 当前的租户类型
	OrganizationType v1.OrganizationType `protobuf:"varint,1,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// 当前的租户 ID
	OrganizationId int64 `protobuf:"varint,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// 当前需要预约的 business_id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 是否启用 business scope 范围过滤，默认启用
	// 当为 true 时，会根据 ServiceBusinessScope 的 available_business_ids 配置过滤服务范围
	// 当为 false 时，不进行 business scope 范围过滤
	EnableBusinessScopeFilter bool `protobuf:"varint,4,opt,name=enable_business_scope_filter,json=enableBusinessScopeFilter,proto3" json:"enable_business_scope_filter,omitempty"`
	// 可选的过滤条件
	Filter *ListAvailableServicesRequest_Filter `protobuf:"bytes,5,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	// 分页信息
	Pagination    *PaginationRef `protobuf:"bytes,6,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAvailableServicesRequest) Reset() {
	*x = ListAvailableServicesRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAvailableServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAvailableServicesRequest) ProtoMessage() {}

func (x *ListAvailableServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAvailableServicesRequest.ProtoReflect.Descriptor instead.
func (*ListAvailableServicesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{14}
}

func (x *ListAvailableServicesRequest) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *ListAvailableServicesRequest) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *ListAvailableServicesRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListAvailableServicesRequest) GetEnableBusinessScopeFilter() bool {
	if x != nil {
		return x.EnableBusinessScopeFilter
	}
	return false
}

func (x *ListAvailableServicesRequest) GetFilter() *ListAvailableServicesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListAvailableServicesRequest) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// (-- api-linter: core::0158::response-next-page-token-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0132::response-unknown-fields=disabled
//
//	aip.dev/not-precedent: 额外返回 total. --)
//
// 查询可用服务响应
type ListAvailableServicesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 可用服务列表
	Services []*Service `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"`
	// 分页信息
	Pagination *PaginationRef `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// 总数
	Total         int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAvailableServicesResponse) Reset() {
	*x = ListAvailableServicesResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAvailableServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAvailableServicesResponse) ProtoMessage() {}

func (x *ListAvailableServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAvailableServicesResponse.ProtoReflect.Descriptor instead.
func (*ListAvailableServicesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{15}
}

func (x *ListAvailableServicesResponse) GetServices() []*Service {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *ListAvailableServicesResponse) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListAvailableServicesResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// UpdateOBServiceRequest
type UpdateOBServiceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// Whether the service is available for online booking
	IsAvailable *bool `protobuf:"varint,2,opt,name=is_available,json=isAvailable,proto3,oneof" json:"is_available,omitempty"`
	// Display price mode: 0 - Do not show, 1 - Show fixed price, 2 - Show "starting at", 3 - Show "varies"
	ShowBasePrice *ServiceOBSetting_ShowBasePriceMode `protobuf:"varint,3,opt,name=show_base_price,json=showBasePrice,proto3,enum=backend.proto.offering.v1.ServiceOBSetting_ShowBasePriceMode,oneof" json:"show_base_price,omitempty"`
	// Whether all staff are available for this service when booking online
	IsAllStaff *bool `protobuf:"varint,4,opt,name=is_all_staff,json=isAllStaff,proto3,oneof" json:"is_all_staff,omitempty"`
	// staff ids
	StaffIds []int64 `protobuf:"varint,5,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
	// Optional display alias for the service in online booking
	Alias         *string `protobuf:"bytes,6,opt,name=alias,proto3,oneof" json:"alias,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateOBServiceRequest) Reset() {
	*x = UpdateOBServiceRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateOBServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOBServiceRequest) ProtoMessage() {}

func (x *UpdateOBServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOBServiceRequest.ProtoReflect.Descriptor instead.
func (*UpdateOBServiceRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{16}
}

func (x *UpdateOBServiceRequest) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *UpdateOBServiceRequest) GetIsAvailable() bool {
	if x != nil && x.IsAvailable != nil {
		return *x.IsAvailable
	}
	return false
}

func (x *UpdateOBServiceRequest) GetShowBasePrice() ServiceOBSetting_ShowBasePriceMode {
	if x != nil && x.ShowBasePrice != nil {
		return *x.ShowBasePrice
	}
	return ServiceOBSetting_SHOW_BASE_PRICE_MODE_UNSPECIFIED
}

func (x *UpdateOBServiceRequest) GetIsAllStaff() bool {
	if x != nil && x.IsAllStaff != nil {
		return *x.IsAllStaff
	}
	return false
}

func (x *UpdateOBServiceRequest) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

func (x *UpdateOBServiceRequest) GetAlias() string {
	if x != nil && x.Alias != nil {
		return *x.Alias
	}
	return ""
}

// Update OB Service Response
type UpdateOBServiceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateOBServiceResponse) Reset() {
	*x = UpdateOBServiceResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateOBServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOBServiceResponse) ProtoMessage() {}

func (x *UpdateOBServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOBServiceResponse.ProtoReflect.Descriptor instead.
func (*UpdateOBServiceResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{17}
}

// list filter
type ListServicesRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// category ids
	CategoryIds []int64 `protobuf:"varint,4,rep,packed,name=category_ids,json=categoryIds,proto3" json:"category_ids,omitempty"`
	// care type ids
	CareTypeIds []int64 `protobuf:"varint,5,rep,packed,name=care_type_ids,json=careTypeIds,proto3" json:"care_type_ids,omitempty"`
	// statuses
	Statuses      []Service_Status `protobuf:"varint,6,rep,packed,name=statuses,proto3,enum=backend.proto.offering.v1.Service_Status" json:"statuses,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListServicesRequest_Filter) Reset() {
	*x = ListServicesRequest_Filter{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListServicesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServicesRequest_Filter) ProtoMessage() {}

func (x *ListServicesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServicesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListServicesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{9, 0}
}

func (x *ListServicesRequest_Filter) GetCategoryIds() []int64 {
	if x != nil {
		return x.CategoryIds
	}
	return nil
}

func (x *ListServicesRequest_Filter) GetCareTypeIds() []int64 {
	if x != nil {
		return x.CareTypeIds
	}
	return nil
}

func (x *ListServicesRequest_Filter) GetStatuses() []Service_Status {
	if x != nil {
		return x.Statuses
	}
	return nil
}

// 过滤条件
type ListAvailableServicesRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 当前选择的 Pet ID 列表
	// 如果传递了 pet_ids，将根据 pet 的 type、breed、code
	// 等配置信息进行服务匹配筛选 如果未传递 pet_ids，则返回该 business
	// 下所有可用的服务
	PetIds []int64 `protobuf:"varint,1,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
	// 当前选择的 care_type_id 列表
	CareTypeIds []int64 `protobuf:"varint,2,rep,packed,name=care_type_ids,json=careTypeIds,proto3" json:"care_type_ids,omitempty"`
	// 状态过滤（如：active/inactive 等）
	Statuses []Service_Status `protobuf:"varint,3,rep,packed,name=statuses,proto3,enum=backend.proto.offering.v1.Service_Status" json:"statuses,omitempty"`
	// 关键词搜索（如服务名称等）
	Keyword       *string `protobuf:"bytes,4,opt,name=keyword,proto3,oneof" json:"keyword,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAvailableServicesRequest_Filter) Reset() {
	*x = ListAvailableServicesRequest_Filter{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAvailableServicesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAvailableServicesRequest_Filter) ProtoMessage() {}

func (x *ListAvailableServicesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAvailableServicesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListAvailableServicesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{14, 0}
}

func (x *ListAvailableServicesRequest_Filter) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

func (x *ListAvailableServicesRequest_Filter) GetCareTypeIds() []int64 {
	if x != nil {
		return x.CareTypeIds
	}
	return nil
}

func (x *ListAvailableServicesRequest_Filter) GetStatuses() []Service_Status {
	if x != nil {
		return x.Statuses
	}
	return nil
}

func (x *ListAvailableServicesRequest_Filter) GetKeyword() string {
	if x != nil && x.Keyword != nil {
		return *x.Keyword
	}
	return ""
}

var File_backend_proto_offering_v1_service_service_proto protoreflect.FileDescriptor

const file_backend_proto_offering_v1_service_service_proto_rawDesc = "" +
	"\n" +
	"/backend/proto/offering/v1/service_service.proto\x12\x19backend.proto.offering.v1\x1a&backend/proto/offering/v1/common.proto\x1a'backend/proto/offering/v1/service.proto\x1a2backend/proto/offering/v1/service_ob_setting.proto\x1a0backend/proto/organization/v1/organization.proto\x1a\x1bbuf/validate/validate.proto\"5\n" +
	"\x14DeleteServiceRequest\x12\x1d\n" +
	"\n" +
	"service_id\x18\x01 \x01(\x03R\tserviceId\"\x17\n" +
	"\x15DeleteServiceResponse\"T\n" +
	"\x14UpdateServiceRequest\x12<\n" +
	"\aservice\x18\x01 \x01(\v2\".backend.proto.offering.v1.ServiceR\aservice\"\x17\n" +
	"\x15UpdateServiceResponse\"T\n" +
	"\x14CreateServiceRequest\x12<\n" +
	"\aservice\x18\x01 \x01(\v2\".backend.proto.offering.v1.ServiceR\aservice\"6\n" +
	"\x15CreateServiceResponse\x12\x1d\n" +
	"\n" +
	"service_id\x18\x01 \x01(\x03R\tserviceId\"2\n" +
	"\x11GetServiceRequest\x12\x1d\n" +
	"\n" +
	"service_id\x18\x01 \x01(\x03R\tserviceId\"R\n" +
	"\x12GetServiceResponse\x12<\n" +
	"\aservice\x18\x01 \x01(\v2\".backend.proto.offering.v1.ServiceR\aservice\"\x86\x01\n" +
	"\x0fCategoryService\x12\x1f\n" +
	"\vcategory_id\x18\x01 \x01(\x03R\n" +
	"categoryId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12>\n" +
	"\bservices\x18\x03 \x03(\v2\".backend.proto.offering.v1.ServiceR\bservices\"\xa9\x04\n" +
	"\x13ListServicesRequest\x12\\\n" +
	"\x11organization_type\x18\x01 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeR\x10organizationType\x12'\n" +
	"\x0forganization_id\x18\x02 \x01(\x03R\x0eorganizationId\x12M\n" +
	"\x06filter\x18\x12 \x01(\v25.backend.proto.offering.v1.ListServicesRequest.FilterR\x06filter\x12Y\n" +
	"\x12extra_info_options\x18\x13 \x01(\v2+.backend.proto.offering.v1.ExtraInfoOptionsR\x10extraInfoOptions\x12H\n" +
	"\n" +
	"pagination\x18\x14 \x01(\v2(.backend.proto.offering.v1.PaginationRefR\n" +
	"pagination\x1a\x96\x01\n" +
	"\x06Filter\x12!\n" +
	"\fcategory_ids\x18\x04 \x03(\x03R\vcategoryIds\x12\"\n" +
	"\rcare_type_ids\x18\x05 \x03(\x03R\vcareTypeIds\x12E\n" +
	"\bstatuses\x18\x06 \x03(\x0e2).backend.proto.offering.v1.Service.StatusR\bstatuses\"\xd7\x01\n" +
	"\x14ListServicesResponse\x12K\n" +
	"\bservices\x18\x01 \x03(\v2/.backend.proto.offering.v1.ServiceWithExtraInfoR\bservices\x12M\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2(.backend.proto.offering.v1.PaginationRefH\x00R\n" +
	"pagination\x88\x01\x01\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05totalB\r\n" +
	"\v_pagination\"\x9b\x02\n" +
	"\x1aBatchUpdateServicesRequest\x12h\n" +
	"\x11organization_type\x18\x01 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeB\n" +
	"\xbaH\a\x82\x01\x04\x10\x01 \x00R\x10organizationType\x120\n" +
	"\x0forganization_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x0eorganizationId\x12a\n" +
	"\x0fupdate_services\x18\x03 \x03(\v2+.backend.proto.offering.v1.ServiceUpdateDefB\v\xbaH\b\x92\x01\x05\b\x01\x10\xe8\aR\x0eupdateServices\"\xa5\x05\n" +
	"\x10ServiceUpdateDef\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\x12-\n" +
	"\vcategory_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x00R\n" +
	"categoryId\x88\x01\x01\x12 \n" +
	"\x04name\x18\x03 \x01(\tB\a\xbaH\x04r\x02\x18dH\x01R\x04name\x88\x01\x01\x12/\n" +
	"\vdescription\x18\x04 \x01(\tB\b\xbaH\x05r\x03\x18\xe8\aH\x02R\vdescription\x88\x01\x01\x12+\n" +
	"\n" +
	"color_code\x18\x05 \x01(\tB\a\xbaH\x04r\x02\x18\n" +
	"H\x03R\tcolorCode\x88\x01\x01\x12 \n" +
	"\x04sort\x18\x06 \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x04R\x04sort\x88\x01\x01\x12,\n" +
	"\x06images\x18\a \x03(\tB\x14\xbaH\x11\x92\x01\x0e\b\x00\x10\xe8\a\x18\x01\"\x05r\x03\x18\x80\bR\x06images\x12R\n" +
	"\x06status\x18\b \x01(\x0e2).backend.proto.offering.v1.Service.StatusB\n" +
	"\xbaH\a\x82\x01\x04\x10\x01 \x00H\x05R\x06status\x88\x01\x01\x12`\n" +
	"\x12available_business\x18\t \x01(\v2,.backend.proto.offering.v1.AvailableBusinessH\x06R\x11availableBusiness\x88\x01\x01\x12Q\n" +
	"\n" +
	"attributes\x18\n" +
	" \x01(\v2,.backend.proto.offering.v1.ServiceAttributesH\aR\n" +
	"attributes\x88\x01\x01B\x0e\n" +
	"\f_category_idB\a\n" +
	"\x05_nameB\x0e\n" +
	"\f_descriptionB\r\n" +
	"\v_color_codeB\a\n" +
	"\x05_sortB\t\n" +
	"\a_statusB\x15\n" +
	"\x13_available_businessB\r\n" +
	"\v_attributes\"\x1d\n" +
	"\x1bBatchUpdateServicesResponse\"\xed\x05\n" +
	"\x1cListAvailableServicesRequest\x12h\n" +
	"\x11organization_type\x18\x01 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeB\n" +
	"\xbaH\a\x82\x01\x04\x10\x01 \x00R\x10organizationType\x120\n" +
	"\x0forganization_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x0eorganizationId\x12(\n" +
	"\vbusiness_id\x18\x03 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"businessId\x12?\n" +
	"\x1cenable_business_scope_filter\x18\x04 \x01(\bR\x19enableBusinessScopeFilter\x12[\n" +
	"\x06filter\x18\x05 \x01(\v2>.backend.proto.offering.v1.ListAvailableServicesRequest.FilterH\x00R\x06filter\x88\x01\x01\x12M\n" +
	"\n" +
	"pagination\x18\x06 \x01(\v2(.backend.proto.offering.v1.PaginationRefH\x01R\n" +
	"pagination\x88\x01\x01\x1a\xff\x01\n" +
	"\x06Filter\x12+\n" +
	"\apet_ids\x18\x01 \x03(\x03B\x12\xbaH\x0f\x92\x01\f\b\x00\x10d\x18\x01\"\x04\"\x02 \x00R\x06petIds\x126\n" +
	"\rcare_type_ids\x18\x02 \x03(\x03B\x12\xbaH\x0f\x92\x01\f\b\x00\x10d\x18\x01\"\x04\"\x02 \x00R\vcareTypeIds\x12\\\n" +
	"\bstatuses\x18\x03 \x03(\x0e2).backend.proto.offering.v1.Service.StatusB\x15\xbaH\x12\x92\x01\x0f\b\x00\x10\n" +
	"\x18\x01\"\a\x82\x01\x04\x10\x01 \x00R\bstatuses\x12&\n" +
	"\akeyword\x18\x04 \x01(\tB\a\xbaH\x04r\x02\x18dH\x00R\akeyword\x88\x01\x01B\n" +
	"\n" +
	"\b_keywordB\t\n" +
	"\a_filterB\r\n" +
	"\v_pagination\"\xd3\x01\n" +
	"\x1dListAvailableServicesResponse\x12>\n" +
	"\bservices\x18\x01 \x03(\v2\".backend.proto.offering.v1.ServiceR\bservices\x12M\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2(.backend.proto.offering.v1.PaginationRefH\x00R\n" +
	"pagination\x88\x01\x01\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05totalB\r\n" +
	"\v_pagination\"\xea\x02\n" +
	"\x16UpdateOBServiceRequest\x12\x1d\n" +
	"\n" +
	"service_id\x18\x01 \x01(\x03R\tserviceId\x12&\n" +
	"\fis_available\x18\x02 \x01(\bH\x00R\visAvailable\x88\x01\x01\x12j\n" +
	"\x0fshow_base_price\x18\x03 \x01(\x0e2=.backend.proto.offering.v1.ServiceOBSetting.ShowBasePriceModeH\x01R\rshowBasePrice\x88\x01\x01\x12%\n" +
	"\fis_all_staff\x18\x04 \x01(\bH\x02R\n" +
	"isAllStaff\x88\x01\x01\x12\x1b\n" +
	"\tstaff_ids\x18\x05 \x03(\x03R\bstaffIds\x12\x19\n" +
	"\x05alias\x18\x06 \x01(\tH\x03R\x05alias\x88\x01\x01B\x0f\n" +
	"\r_is_availableB\x12\n" +
	"\x10_show_base_priceB\x0f\n" +
	"\r_is_all_staffB\b\n" +
	"\x06_alias\"\x19\n" +
	"\x17UpdateOBServiceResponse2\xd6\a\n" +
	"\x0eServiceService\x12r\n" +
	"\rCreateService\x12/.backend.proto.offering.v1.CreateServiceRequest\x1a0.backend.proto.offering.v1.CreateServiceResponse\x12i\n" +
	"\n" +
	"GetService\x12,.backend.proto.offering.v1.GetServiceRequest\x1a-.backend.proto.offering.v1.GetServiceResponse\x12r\n" +
	"\rUpdateService\x12/.backend.proto.offering.v1.UpdateServiceRequest\x1a0.backend.proto.offering.v1.UpdateServiceResponse\x12r\n" +
	"\rDeleteService\x12/.backend.proto.offering.v1.DeleteServiceRequest\x1a0.backend.proto.offering.v1.DeleteServiceResponse\x12o\n" +
	"\fListServices\x12..backend.proto.offering.v1.ListServicesRequest\x1a/.backend.proto.offering.v1.ListServicesResponse\x12\x8a\x01\n" +
	"\x15ListAvailableServices\x127.backend.proto.offering.v1.ListAvailableServicesRequest\x1a8.backend.proto.offering.v1.ListAvailableServicesResponse\x12\x84\x01\n" +
	"\x13BatchUpdateServices\x125.backend.proto.offering.v1.BatchUpdateServicesRequest\x1a6.backend.proto.offering.v1.BatchUpdateServicesResponse\x12x\n" +
	"\x0fUpdateOBService\x121.backend.proto.offering.v1.UpdateOBServiceRequest\x1a2.backend.proto.offering.v1.UpdateOBServiceResponseBk\n" +
	"#com.moego.backend.proto.offering.v1P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpbb\x06proto3"

var (
	file_backend_proto_offering_v1_service_service_proto_rawDescOnce sync.Once
	file_backend_proto_offering_v1_service_service_proto_rawDescData []byte
)

func file_backend_proto_offering_v1_service_service_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_v1_service_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_v1_service_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_service_proto_rawDesc), len(file_backend_proto_offering_v1_service_service_proto_rawDesc)))
	})
	return file_backend_proto_offering_v1_service_service_proto_rawDescData
}

var file_backend_proto_offering_v1_service_service_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_backend_proto_offering_v1_service_service_proto_goTypes = []any{
	(*DeleteServiceRequest)(nil),                // 0: backend.proto.offering.v1.DeleteServiceRequest
	(*DeleteServiceResponse)(nil),               // 1: backend.proto.offering.v1.DeleteServiceResponse
	(*UpdateServiceRequest)(nil),                // 2: backend.proto.offering.v1.UpdateServiceRequest
	(*UpdateServiceResponse)(nil),               // 3: backend.proto.offering.v1.UpdateServiceResponse
	(*CreateServiceRequest)(nil),                // 4: backend.proto.offering.v1.CreateServiceRequest
	(*CreateServiceResponse)(nil),               // 5: backend.proto.offering.v1.CreateServiceResponse
	(*GetServiceRequest)(nil),                   // 6: backend.proto.offering.v1.GetServiceRequest
	(*GetServiceResponse)(nil),                  // 7: backend.proto.offering.v1.GetServiceResponse
	(*CategoryService)(nil),                     // 8: backend.proto.offering.v1.CategoryService
	(*ListServicesRequest)(nil),                 // 9: backend.proto.offering.v1.ListServicesRequest
	(*ListServicesResponse)(nil),                // 10: backend.proto.offering.v1.ListServicesResponse
	(*BatchUpdateServicesRequest)(nil),          // 11: backend.proto.offering.v1.BatchUpdateServicesRequest
	(*ServiceUpdateDef)(nil),                    // 12: backend.proto.offering.v1.ServiceUpdateDef
	(*BatchUpdateServicesResponse)(nil),         // 13: backend.proto.offering.v1.BatchUpdateServicesResponse
	(*ListAvailableServicesRequest)(nil),        // 14: backend.proto.offering.v1.ListAvailableServicesRequest
	(*ListAvailableServicesResponse)(nil),       // 15: backend.proto.offering.v1.ListAvailableServicesResponse
	(*UpdateOBServiceRequest)(nil),              // 16: backend.proto.offering.v1.UpdateOBServiceRequest
	(*UpdateOBServiceResponse)(nil),             // 17: backend.proto.offering.v1.UpdateOBServiceResponse
	(*ListServicesRequest_Filter)(nil),          // 18: backend.proto.offering.v1.ListServicesRequest.Filter
	(*ListAvailableServicesRequest_Filter)(nil), // 19: backend.proto.offering.v1.ListAvailableServicesRequest.Filter
	(*Service)(nil),                             // 20: backend.proto.offering.v1.Service
	(v1.OrganizationType)(0),                    // 21: backend.proto.organization.v1.OrganizationType
	(*ExtraInfoOptions)(nil),                    // 22: backend.proto.offering.v1.ExtraInfoOptions
	(*PaginationRef)(nil),                       // 23: backend.proto.offering.v1.PaginationRef
	(*ServiceWithExtraInfo)(nil),                // 24: backend.proto.offering.v1.ServiceWithExtraInfo
	(Service_Status)(0),                         // 25: backend.proto.offering.v1.Service.Status
	(*AvailableBusiness)(nil),                   // 26: backend.proto.offering.v1.AvailableBusiness
	(*ServiceAttributes)(nil),                   // 27: backend.proto.offering.v1.ServiceAttributes
	(ServiceOBSetting_ShowBasePriceMode)(0),     // 28: backend.proto.offering.v1.ServiceOBSetting.ShowBasePriceMode
}
var file_backend_proto_offering_v1_service_service_proto_depIdxs = []int32{
	20, // 0: backend.proto.offering.v1.UpdateServiceRequest.service:type_name -> backend.proto.offering.v1.Service
	20, // 1: backend.proto.offering.v1.CreateServiceRequest.service:type_name -> backend.proto.offering.v1.Service
	20, // 2: backend.proto.offering.v1.GetServiceResponse.service:type_name -> backend.proto.offering.v1.Service
	20, // 3: backend.proto.offering.v1.CategoryService.services:type_name -> backend.proto.offering.v1.Service
	21, // 4: backend.proto.offering.v1.ListServicesRequest.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	18, // 5: backend.proto.offering.v1.ListServicesRequest.filter:type_name -> backend.proto.offering.v1.ListServicesRequest.Filter
	22, // 6: backend.proto.offering.v1.ListServicesRequest.extra_info_options:type_name -> backend.proto.offering.v1.ExtraInfoOptions
	23, // 7: backend.proto.offering.v1.ListServicesRequest.pagination:type_name -> backend.proto.offering.v1.PaginationRef
	24, // 8: backend.proto.offering.v1.ListServicesResponse.services:type_name -> backend.proto.offering.v1.ServiceWithExtraInfo
	23, // 9: backend.proto.offering.v1.ListServicesResponse.pagination:type_name -> backend.proto.offering.v1.PaginationRef
	21, // 10: backend.proto.offering.v1.BatchUpdateServicesRequest.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	12, // 11: backend.proto.offering.v1.BatchUpdateServicesRequest.update_services:type_name -> backend.proto.offering.v1.ServiceUpdateDef
	25, // 12: backend.proto.offering.v1.ServiceUpdateDef.status:type_name -> backend.proto.offering.v1.Service.Status
	26, // 13: backend.proto.offering.v1.ServiceUpdateDef.available_business:type_name -> backend.proto.offering.v1.AvailableBusiness
	27, // 14: backend.proto.offering.v1.ServiceUpdateDef.attributes:type_name -> backend.proto.offering.v1.ServiceAttributes
	21, // 15: backend.proto.offering.v1.ListAvailableServicesRequest.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	19, // 16: backend.proto.offering.v1.ListAvailableServicesRequest.filter:type_name -> backend.proto.offering.v1.ListAvailableServicesRequest.Filter
	23, // 17: backend.proto.offering.v1.ListAvailableServicesRequest.pagination:type_name -> backend.proto.offering.v1.PaginationRef
	20, // 18: backend.proto.offering.v1.ListAvailableServicesResponse.services:type_name -> backend.proto.offering.v1.Service
	23, // 19: backend.proto.offering.v1.ListAvailableServicesResponse.pagination:type_name -> backend.proto.offering.v1.PaginationRef
	28, // 20: backend.proto.offering.v1.UpdateOBServiceRequest.show_base_price:type_name -> backend.proto.offering.v1.ServiceOBSetting.ShowBasePriceMode
	25, // 21: backend.proto.offering.v1.ListServicesRequest.Filter.statuses:type_name -> backend.proto.offering.v1.Service.Status
	25, // 22: backend.proto.offering.v1.ListAvailableServicesRequest.Filter.statuses:type_name -> backend.proto.offering.v1.Service.Status
	4,  // 23: backend.proto.offering.v1.ServiceService.CreateService:input_type -> backend.proto.offering.v1.CreateServiceRequest
	6,  // 24: backend.proto.offering.v1.ServiceService.GetService:input_type -> backend.proto.offering.v1.GetServiceRequest
	2,  // 25: backend.proto.offering.v1.ServiceService.UpdateService:input_type -> backend.proto.offering.v1.UpdateServiceRequest
	0,  // 26: backend.proto.offering.v1.ServiceService.DeleteService:input_type -> backend.proto.offering.v1.DeleteServiceRequest
	9,  // 27: backend.proto.offering.v1.ServiceService.ListServices:input_type -> backend.proto.offering.v1.ListServicesRequest
	14, // 28: backend.proto.offering.v1.ServiceService.ListAvailableServices:input_type -> backend.proto.offering.v1.ListAvailableServicesRequest
	11, // 29: backend.proto.offering.v1.ServiceService.BatchUpdateServices:input_type -> backend.proto.offering.v1.BatchUpdateServicesRequest
	16, // 30: backend.proto.offering.v1.ServiceService.UpdateOBService:input_type -> backend.proto.offering.v1.UpdateOBServiceRequest
	5,  // 31: backend.proto.offering.v1.ServiceService.CreateService:output_type -> backend.proto.offering.v1.CreateServiceResponse
	7,  // 32: backend.proto.offering.v1.ServiceService.GetService:output_type -> backend.proto.offering.v1.GetServiceResponse
	3,  // 33: backend.proto.offering.v1.ServiceService.UpdateService:output_type -> backend.proto.offering.v1.UpdateServiceResponse
	1,  // 34: backend.proto.offering.v1.ServiceService.DeleteService:output_type -> backend.proto.offering.v1.DeleteServiceResponse
	10, // 35: backend.proto.offering.v1.ServiceService.ListServices:output_type -> backend.proto.offering.v1.ListServicesResponse
	15, // 36: backend.proto.offering.v1.ServiceService.ListAvailableServices:output_type -> backend.proto.offering.v1.ListAvailableServicesResponse
	13, // 37: backend.proto.offering.v1.ServiceService.BatchUpdateServices:output_type -> backend.proto.offering.v1.BatchUpdateServicesResponse
	17, // 38: backend.proto.offering.v1.ServiceService.UpdateOBService:output_type -> backend.proto.offering.v1.UpdateOBServiceResponse
	31, // [31:39] is the sub-list for method output_type
	23, // [23:31] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_v1_service_service_proto_init() }
func file_backend_proto_offering_v1_service_service_proto_init() {
	if File_backend_proto_offering_v1_service_service_proto != nil {
		return
	}
	file_backend_proto_offering_v1_common_proto_init()
	file_backend_proto_offering_v1_service_proto_init()
	file_backend_proto_offering_v1_service_ob_setting_proto_init()
	file_backend_proto_offering_v1_service_service_proto_msgTypes[10].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_service_proto_msgTypes[12].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_service_proto_msgTypes[14].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_service_proto_msgTypes[15].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_service_proto_msgTypes[16].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_service_proto_msgTypes[19].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_service_proto_rawDesc), len(file_backend_proto_offering_v1_service_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_offering_v1_service_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_v1_service_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_offering_v1_service_service_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_v1_service_service_proto = out.File
	file_backend_proto_offering_v1_service_service_proto_goTypes = nil
	file_backend_proto_offering_v1_service_service_proto_depIdxs = nil
}
