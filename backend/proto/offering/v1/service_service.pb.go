// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/offering/v1/service_service.proto

package offeringpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	v1 "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 删除服务请求
type DeleteServiceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务ID
	ServiceId     int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteServiceRequest) Reset() {
	*x = DeleteServiceRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteServiceRequest) ProtoMessage() {}

func (x *DeleteServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteServiceRequest.ProtoReflect.Descriptor instead.
func (*DeleteServiceRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{0}
}

func (x *DeleteServiceRequest) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

// 删除服务响应
type DeleteServiceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteServiceResponse) Reset() {
	*x = DeleteServiceResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteServiceResponse) ProtoMessage() {}

func (x *DeleteServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteServiceResponse.ProtoReflect.Descriptor instead.
func (*DeleteServiceResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{1}
}

// 更新服务请求
type UpdateServiceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务更新配置
	Service       *ServiceUpdateDef `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateServiceRequest) Reset() {
	*x = UpdateServiceRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceRequest) ProtoMessage() {}

func (x *UpdateServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceRequest.ProtoReflect.Descriptor instead.
func (*UpdateServiceRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateServiceRequest) GetService() *ServiceUpdateDef {
	if x != nil {
		return x.Service
	}
	return nil
}

// 更新服务响应
type UpdateServiceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateServiceResponse) Reset() {
	*x = UpdateServiceResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceResponse) ProtoMessage() {}

func (x *UpdateServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceResponse.ProtoReflect.Descriptor instead.
func (*UpdateServiceResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{3}
}

// (-- api-linter: core::0133::request-resource-field=disabled
//
//	aip.dev/not-precedent: 不复用 model 结构，单独定义 ServiceCreateDef 结构 --)
//
// 创建服务请求
type CreateServiceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务创建配置
	Service       *ServiceCreateDef `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateServiceRequest) Reset() {
	*x = CreateServiceRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceRequest) ProtoMessage() {}

func (x *CreateServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceRequest.ProtoReflect.Descriptor instead.
func (*CreateServiceRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{4}
}

func (x *CreateServiceRequest) GetService() *ServiceCreateDef {
	if x != nil {
		return x.Service
	}
	return nil
}

// 创建服务响应
type CreateServiceResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务 ID
	ServiceId     int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateServiceResponse) Reset() {
	*x = CreateServiceResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceResponse) ProtoMessage() {}

func (x *CreateServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceResponse.ProtoReflect.Descriptor instead.
func (*CreateServiceResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{5}
}

func (x *CreateServiceResponse) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

// 获取服务请求
type GetServiceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务ID
	ServiceId     int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceRequest) Reset() {
	*x = GetServiceRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceRequest) ProtoMessage() {}

func (x *GetServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceRequest.ProtoReflect.Descriptor instead.
func (*GetServiceRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetServiceRequest) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

// 获取服务响应
type GetServiceResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务
	Service       *Service `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceResponse) Reset() {
	*x = GetServiceResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceResponse) ProtoMessage() {}

func (x *GetServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceResponse.ProtoReflect.Descriptor instead.
func (*GetServiceResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetServiceResponse) GetService() *Service {
	if x != nil {
		return x.Service
	}
	return nil
}

// 分类服务
type CategoryService struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 分类ID
	CategoryId int64 `protobuf:"varint,1,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// 分类名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 服务模板列表
	Services      []*Service `protobuf:"bytes,3,rep,name=services,proto3" json:"services,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CategoryService) Reset() {
	*x = CategoryService{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CategoryService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CategoryService) ProtoMessage() {}

func (x *CategoryService) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CategoryService.ProtoReflect.Descriptor instead.
func (*CategoryService) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{8}
}

func (x *CategoryService) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *CategoryService) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CategoryService) GetServices() []*Service {
	if x != nil {
		return x.Services
	}
	return nil
}

// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0132::request-parent-required=disabled
//
//	aip.dev/not-precedent: 无 parent 语义 --)
//
// list services request
type ListServicesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The organization type
	OrganizationType v1.OrganizationType `protobuf:"varint,1,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// The organization ID.
	OrganizationId int64 `protobuf:"varint,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// filter
	Filter *ListServicesRequest_Filter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
	// options to include extra info in response
	ExtraInfoOptions *ExtraInfoOptions `protobuf:"bytes,4,opt,name=extra_info_options,json=extraInfoOptions,proto3" json:"extra_info_options,omitempty"`
	// 分页信息
	Pagination    *PaginationRef `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListServicesRequest) Reset() {
	*x = ListServicesRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServicesRequest) ProtoMessage() {}

func (x *ListServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServicesRequest.ProtoReflect.Descriptor instead.
func (*ListServicesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{9}
}

func (x *ListServicesRequest) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *ListServicesRequest) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *ListServicesRequest) GetFilter() *ListServicesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListServicesRequest) GetExtraInfoOptions() *ExtraInfoOptions {
	if x != nil {
		return x.ExtraInfoOptions
	}
	return nil
}

func (x *ListServicesRequest) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// (-- api-linter: core::0158::response-next-page-token-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0132::response-unknown-fields=disabled
//
//	aip.dev/not-precedent: 额外返回 total. --)
//
// list setting services response
type ListServicesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务模板列表
	Services []*ServiceWithExtraInfo `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"`
	// 分页信息
	Pagination *PaginationRef `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// 总数
	Total         int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListServicesResponse) Reset() {
	*x = ListServicesResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServicesResponse) ProtoMessage() {}

func (x *ListServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServicesResponse.ProtoReflect.Descriptor instead.
func (*ListServicesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{10}
}

func (x *ListServicesResponse) GetServices() []*ServiceWithExtraInfo {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *ListServicesResponse) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListServicesResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// (-- api-linter: core::0234::request-unknown-fields=disabled
//
//	aip.dev/not-precedent: 需要租户信息 --)
//
// (-- api-linter: core::0234::request-requests-field=disabled
//
//	aip.dev/not-precedent: 不复用 UpdateServiceRequest 结构 --)
//
// (-- api-linter: core::0234::request-parent-field=disabled
//
//	aip.dev/not-precedent: 无 parent 语义 --)
//
// 批量更新服务请求
type BatchUpdateServicesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 当前的租户类型
	OrganizationType v1.OrganizationType `protobuf:"varint,1,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// 当前的租户 ID
	OrganizationId int64 `protobuf:"varint,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// 服务更新配置列表
	UpdateServices []*ServiceUpdateDef `protobuf:"bytes,3,rep,name=update_services,json=updateServices,proto3" json:"update_services,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BatchUpdateServicesRequest) Reset() {
	*x = BatchUpdateServicesRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchUpdateServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateServicesRequest) ProtoMessage() {}

func (x *BatchUpdateServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateServicesRequest.ProtoReflect.Descriptor instead.
func (*BatchUpdateServicesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{11}
}

func (x *BatchUpdateServicesRequest) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *BatchUpdateServicesRequest) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *BatchUpdateServicesRequest) GetUpdateServices() []*ServiceUpdateDef {
	if x != nil {
		return x.UpdateServices
	}
	return nil
}

// 单个服务的更新配置
type ServiceUpdateDef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务 ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 分类 ID（可选）
	CategoryId *int64 `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3,oneof" json:"category_id,omitempty"`
	// 服务名称（可选）
	Name *string `protobuf:"bytes,3,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// 描述
	Description *string `protobuf:"bytes,4,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// 颜色
	ColorCode *string `protobuf:"bytes,5,opt,name=color_code,json=colorCode,proto3,oneof" json:"color_code,omitempty"`
	// 排序值（可选）
	Sort *int64 `protobuf:"varint,6,opt,name=sort,proto3,oneof" json:"sort,omitempty"`
	// 图片
	Images []string `protobuf:"bytes,7,rep,name=images,proto3" json:"images,omitempty"`
	// 状态
	Status *Service_Status `protobuf:"varint,8,opt,name=status,proto3,enum=backend.proto.offering.v1.Service_Status,oneof" json:"status,omitempty"`
	// 可用 business scope
	AvailableBusiness *AvailableBusiness `protobuf:"bytes,9,opt,name=available_business,json=availableBusiness,proto3,oneof" json:"available_business,omitempty"`
	// 关联的属性值
	Attributes *ServiceAttributes `protobuf:"bytes,10,opt,name=attributes,proto3,oneof" json:"attributes,omitempty"`
	// 附加服务配置（可选）
	AdditionalService *AdditionalService `protobuf:"bytes,11,opt,name=additional_service,json=additionalService,proto3,oneof" json:"additional_service,omitempty"`
	// 可用宠物类型和品种配置（可选）
	AvailableTypeBreed *AvailablePetTypeBreed `protobuf:"bytes,12,opt,name=available_type_breed,json=availableTypeBreed,proto3,oneof" json:"available_type_breed,omitempty"`
	// 可用宠物尺寸配置（可选）
	AvailablePetSize *AvailablePetSize `protobuf:"bytes,13,opt,name=available_pet_size,json=availablePetSize,proto3,oneof" json:"available_pet_size,omitempty"`
	// 可用宠物毛类型配置（可选）
	AvailableCoatType *AvailableCoatType `protobuf:"bytes,14,opt,name=available_coat_type,json=availableCoatType,proto3,oneof" json:"available_coat_type,omitempty"`
	// 可用宠物代码配置（可选）
	AvailablePetCode *AvailablePetCode `protobuf:"bytes,15,opt,name=available_pet_code,json=availablePetCode,proto3,oneof" json:"available_pet_code,omitempty"`
	// 可用宠物体重配置（可选）
	AvailablePetWeight *AvailablePetWeight `protobuf:"bytes,16,opt,name=available_pet_weight,json=availablePetWeight,proto3,oneof" json:"available_pet_weight,omitempty"`
	// 价格配置
	Price *money.Money `protobuf:"bytes,17,opt,name=price,proto3,oneof" json:"price,omitempty"`
	// 税收 ID
	TaxId *int64 `protobuf:"varint,19,opt,name=tax_id,json=taxId,proto3,oneof" json:"tax_id,omitempty"`
	// Business and staff override configuration (price, duration, tax_id overrides for specific businesses and staff)
	BusinessStaffOverrides []*BusinessStaffOverride `protobuf:"bytes,20,rep,name=business_staff_overrides,json=businessStaffOverrides,proto3" json:"business_staff_overrides,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *ServiceUpdateDef) Reset() {
	*x = ServiceUpdateDef{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceUpdateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceUpdateDef) ProtoMessage() {}

func (x *ServiceUpdateDef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceUpdateDef.ProtoReflect.Descriptor instead.
func (*ServiceUpdateDef) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{12}
}

func (x *ServiceUpdateDef) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceUpdateDef) GetCategoryId() int64 {
	if x != nil && x.CategoryId != nil {
		return *x.CategoryId
	}
	return 0
}

func (x *ServiceUpdateDef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *ServiceUpdateDef) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *ServiceUpdateDef) GetColorCode() string {
	if x != nil && x.ColorCode != nil {
		return *x.ColorCode
	}
	return ""
}

func (x *ServiceUpdateDef) GetSort() int64 {
	if x != nil && x.Sort != nil {
		return *x.Sort
	}
	return 0
}

func (x *ServiceUpdateDef) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *ServiceUpdateDef) GetStatus() Service_Status {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return Service_STATUS_UNSPECIFIED
}

func (x *ServiceUpdateDef) GetAvailableBusiness() *AvailableBusiness {
	if x != nil {
		return x.AvailableBusiness
	}
	return nil
}

func (x *ServiceUpdateDef) GetAttributes() *ServiceAttributes {
	if x != nil {
		return x.Attributes
	}
	return nil
}

func (x *ServiceUpdateDef) GetAdditionalService() *AdditionalService {
	if x != nil {
		return x.AdditionalService
	}
	return nil
}

func (x *ServiceUpdateDef) GetAvailableTypeBreed() *AvailablePetTypeBreed {
	if x != nil {
		return x.AvailableTypeBreed
	}
	return nil
}

func (x *ServiceUpdateDef) GetAvailablePetSize() *AvailablePetSize {
	if x != nil {
		return x.AvailablePetSize
	}
	return nil
}

func (x *ServiceUpdateDef) GetAvailableCoatType() *AvailableCoatType {
	if x != nil {
		return x.AvailableCoatType
	}
	return nil
}

func (x *ServiceUpdateDef) GetAvailablePetCode() *AvailablePetCode {
	if x != nil {
		return x.AvailablePetCode
	}
	return nil
}

func (x *ServiceUpdateDef) GetAvailablePetWeight() *AvailablePetWeight {
	if x != nil {
		return x.AvailablePetWeight
	}
	return nil
}

func (x *ServiceUpdateDef) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *ServiceUpdateDef) GetTaxId() int64 {
	if x != nil && x.TaxId != nil {
		return *x.TaxId
	}
	return 0
}

func (x *ServiceUpdateDef) GetBusinessStaffOverrides() []*BusinessStaffOverride {
	if x != nil {
		return x.BusinessStaffOverrides
	}
	return nil
}

// (-- api-linter: core::0234::response-resource-field=disabled
//
//	aip.dev/not-precedent: 不返回 Service 类型 --)
//
// 批量更新服务响应
type BatchUpdateServicesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchUpdateServicesResponse) Reset() {
	*x = BatchUpdateServicesResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchUpdateServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateServicesResponse) ProtoMessage() {}

func (x *BatchUpdateServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateServicesResponse.ProtoReflect.Descriptor instead.
func (*BatchUpdateServicesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{13}
}

// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0132::request-parent-required=disabled
//
//	aip.dev/not-precedent: 无 parent 语义 --)
//
// 查询可用服务请求（用于预约场景）
type ListAvailableServicesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 当前的租户类型
	OrganizationType v1.OrganizationType `protobuf:"varint,1,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// 当前的租户 ID
	OrganizationId int64 `protobuf:"varint,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// 当前需要预约的 business_id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 是否启用 business scope 范围过滤，默认启用
	// 当为 true 时，会根据 ServiceBusinessScope 的 available_business_ids 配置过滤服务范围
	// 当为 false 时，不进行 business scope 范围过滤
	EnableBusinessScopeFilter bool `protobuf:"varint,4,opt,name=enable_business_scope_filter,json=enableBusinessScopeFilter,proto3" json:"enable_business_scope_filter,omitempty"`
	// 可选的过滤条件
	Filter *ListAvailableServicesRequest_Filter `protobuf:"bytes,5,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	// 分页信息
	Pagination    *PaginationRef `protobuf:"bytes,6,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAvailableServicesRequest) Reset() {
	*x = ListAvailableServicesRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAvailableServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAvailableServicesRequest) ProtoMessage() {}

func (x *ListAvailableServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAvailableServicesRequest.ProtoReflect.Descriptor instead.
func (*ListAvailableServicesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{14}
}

func (x *ListAvailableServicesRequest) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *ListAvailableServicesRequest) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *ListAvailableServicesRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListAvailableServicesRequest) GetEnableBusinessScopeFilter() bool {
	if x != nil {
		return x.EnableBusinessScopeFilter
	}
	return false
}

func (x *ListAvailableServicesRequest) GetFilter() *ListAvailableServicesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListAvailableServicesRequest) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// (-- api-linter: core::0158::response-next-page-token-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0132::response-unknown-fields=disabled
//
//	aip.dev/not-precedent: 额外返回 total. --)
//
// 查询可用服务响应
type ListAvailableServicesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 可用服务列表
	Services []*Service `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"`
	// 分页信息
	Pagination *PaginationRef `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// 总数
	Total         int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAvailableServicesResponse) Reset() {
	*x = ListAvailableServicesResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAvailableServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAvailableServicesResponse) ProtoMessage() {}

func (x *ListAvailableServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAvailableServicesResponse.ProtoReflect.Descriptor instead.
func (*ListAvailableServicesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{15}
}

func (x *ListAvailableServicesResponse) GetServices() []*Service {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *ListAvailableServicesResponse) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListAvailableServicesResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// UpdateOBServiceRequest
type UpdateOBServiceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The organization type
	OrganizationType v1.OrganizationType `protobuf:"varint,1,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// The organization ID.
	OrganizationId int64 `protobuf:"varint,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// 服务
	ServiceId int64 `protobuf:"varint,3,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// Whether the service is available for online booking
	IsAvailable *bool `protobuf:"varint,11,opt,name=is_available,json=isAvailable,proto3,oneof" json:"is_available,omitempty"`
	// Display price mode: 0 - Do not show, 1 - Show fixed price, 2 - Show "starting at", 3 - Show "varies"
	ShowBasePrice *ServiceOBSetting_ShowBasePriceMode `protobuf:"varint,12,opt,name=show_base_price,json=showBasePrice,proto3,enum=backend.proto.offering.v1.ServiceOBSetting_ShowBasePriceMode,oneof" json:"show_base_price,omitempty"`
	// Whether all staff are available for this service when booking online
	IsAllStaff *bool `protobuf:"varint,13,opt,name=is_all_staff,json=isAllStaff,proto3,oneof" json:"is_all_staff,omitempty"`
	// staff ids
	StaffIds []int64 `protobuf:"varint,14,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
	// Optional display alias for the service in online booking
	Alias         *string `protobuf:"bytes,15,opt,name=alias,proto3,oneof" json:"alias,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateOBServiceRequest) Reset() {
	*x = UpdateOBServiceRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateOBServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOBServiceRequest) ProtoMessage() {}

func (x *UpdateOBServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOBServiceRequest.ProtoReflect.Descriptor instead.
func (*UpdateOBServiceRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{16}
}

func (x *UpdateOBServiceRequest) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *UpdateOBServiceRequest) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *UpdateOBServiceRequest) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *UpdateOBServiceRequest) GetIsAvailable() bool {
	if x != nil && x.IsAvailable != nil {
		return *x.IsAvailable
	}
	return false
}

func (x *UpdateOBServiceRequest) GetShowBasePrice() ServiceOBSetting_ShowBasePriceMode {
	if x != nil && x.ShowBasePrice != nil {
		return *x.ShowBasePrice
	}
	return ServiceOBSetting_SHOW_BASE_PRICE_MODE_UNSPECIFIED
}

func (x *UpdateOBServiceRequest) GetIsAllStaff() bool {
	if x != nil && x.IsAllStaff != nil {
		return *x.IsAllStaff
	}
	return false
}

func (x *UpdateOBServiceRequest) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

func (x *UpdateOBServiceRequest) GetAlias() string {
	if x != nil && x.Alias != nil {
		return *x.Alias
	}
	return ""
}

// Update OB Service Response
type UpdateOBServiceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateOBServiceResponse) Reset() {
	*x = UpdateOBServiceResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateOBServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOBServiceResponse) ProtoMessage() {}

func (x *UpdateOBServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOBServiceResponse.ProtoReflect.Descriptor instead.
func (*UpdateOBServiceResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{17}
}

// (-- api-linter: core::0231::request-unknown-fields=disabled
//
//	aip.dev/not-precedent: 直接根据 ID 批量获取服务 --)
//
// (-- api-linter: core::0231::request-names-field=disabled
//
//	aip.dev/not-precedent: 直接根据 ID 批量获取服务 --)
//
// 批量获取服务请求
type BatchGetServicesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 当前的租户类型
	OrganizationType v1.OrganizationType `protobuf:"varint,1,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// 当前的租户 ID
	OrganizationId int64 `protobuf:"varint,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// 服务ID列表
	Ids           []int64 `protobuf:"varint,3,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchGetServicesRequest) Reset() {
	*x = BatchGetServicesRequest{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchGetServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetServicesRequest) ProtoMessage() {}

func (x *BatchGetServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetServicesRequest.ProtoReflect.Descriptor instead.
func (*BatchGetServicesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{18}
}

func (x *BatchGetServicesRequest) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *BatchGetServicesRequest) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *BatchGetServicesRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// 批量获取服务响应
type BatchGetServicesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务列表
	Services      []*Service `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchGetServicesResponse) Reset() {
	*x = BatchGetServicesResponse{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchGetServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetServicesResponse) ProtoMessage() {}

func (x *BatchGetServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetServicesResponse.ProtoReflect.Descriptor instead.
func (*BatchGetServicesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{19}
}

func (x *BatchGetServicesResponse) GetServices() []*Service {
	if x != nil {
		return x.Services
	}
	return nil
}

// Service 创建配置定义
type ServiceCreateDef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 组织类型
	OrganizationType v1.OrganizationType `protobuf:"varint,1,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// 组织 ID
	OrganizationId int64 `protobuf:"varint,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// 护理类型 ID
	CareTypeId int64 `protobuf:"varint,3,opt,name=care_type_id,json=careTypeId,proto3" json:"care_type_id,omitempty"`
	// 分类 ID（可选）
	CategoryId *int64 `protobuf:"varint,4,opt,name=category_id,json=categoryId,proto3,oneof" json:"category_id,omitempty"`
	// 服务名称
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	// 描述（可选）
	Description *string `protobuf:"bytes,6,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// 颜色代码
	ColorCode string `protobuf:"bytes,7,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// 图片列表（可选）
	Images []string `protobuf:"bytes,9,rep,name=images,proto3" json:"images,omitempty"`
	// 来源
	Source OfferingSource `protobuf:"varint,10,opt,name=source,proto3,enum=backend.proto.offering.v1.OfferingSource" json:"source,omitempty"`
	// 状态
	Status Service_Status `protobuf:"varint,11,opt,name=status,proto3,enum=backend.proto.offering.v1.Service_Status" json:"status,omitempty"`
	// 可用业务范围
	AvailableBusiness *AvailableBusiness `protobuf:"bytes,13,opt,name=available_business,json=availableBusiness,proto3" json:"available_business,omitempty"`
	// 附加服务配置（可选）
	AdditionalService *AdditionalService `protobuf:"bytes,14,opt,name=additional_service,json=additionalService,proto3,oneof" json:"additional_service,omitempty"`
	// 可用宠物类型和品种配置（可选）
	AvailableTypeBreed *AvailablePetTypeBreed `protobuf:"bytes,15,opt,name=available_type_breed,json=availableTypeBreed,proto3,oneof" json:"available_type_breed,omitempty"`
	// 可用宠物尺寸配置（可选）
	AvailablePetSize *AvailablePetSize `protobuf:"bytes,16,opt,name=available_pet_size,json=availablePetSize,proto3,oneof" json:"available_pet_size,omitempty"`
	// 可用宠物毛类型配置（可选）
	AvailableCoatType *AvailableCoatType `protobuf:"bytes,17,opt,name=available_coat_type,json=availableCoatType,proto3,oneof" json:"available_coat_type,omitempty"`
	// 可用宠物代码配置（可选）
	AvailablePetCode *AvailablePetCode `protobuf:"bytes,18,opt,name=available_pet_code,json=availablePetCode,proto3,oneof" json:"available_pet_code,omitempty"`
	// 服务属性（可选）
	Attributes *ServiceAttributes `protobuf:"bytes,19,opt,name=attributes,proto3,oneof" json:"attributes,omitempty"`
	// 可用宠物体重配置（可选）
	AvailablePetWeight *AvailablePetWeight `protobuf:"bytes,20,opt,name=available_pet_weight,json=availablePetWeight,proto3,oneof" json:"available_pet_weight,omitempty"`
	// 价格配置
	Price *money.Money `protobuf:"bytes,21,opt,name=price,proto3" json:"price,omitempty"`
	// 税收 ID
	TaxId int64 `protobuf:"varint,23,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// Business and staff override configuration (price, duration, tax_id overrides for specific businesses and staff)
	BusinessStaffOverrides []*BusinessStaffOverride `protobuf:"bytes,25,rep,name=business_staff_overrides,json=businessStaffOverrides,proto3" json:"business_staff_overrides,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *ServiceCreateDef) Reset() {
	*x = ServiceCreateDef{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceCreateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceCreateDef) ProtoMessage() {}

func (x *ServiceCreateDef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceCreateDef.ProtoReflect.Descriptor instead.
func (*ServiceCreateDef) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{20}
}

func (x *ServiceCreateDef) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *ServiceCreateDef) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *ServiceCreateDef) GetCareTypeId() int64 {
	if x != nil {
		return x.CareTypeId
	}
	return 0
}

func (x *ServiceCreateDef) GetCategoryId() int64 {
	if x != nil && x.CategoryId != nil {
		return *x.CategoryId
	}
	return 0
}

func (x *ServiceCreateDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceCreateDef) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *ServiceCreateDef) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *ServiceCreateDef) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *ServiceCreateDef) GetSource() OfferingSource {
	if x != nil {
		return x.Source
	}
	return OfferingSource_OFFERING_SOURCE_UNSPECIFIED
}

func (x *ServiceCreateDef) GetStatus() Service_Status {
	if x != nil {
		return x.Status
	}
	return Service_STATUS_UNSPECIFIED
}

func (x *ServiceCreateDef) GetAvailableBusiness() *AvailableBusiness {
	if x != nil {
		return x.AvailableBusiness
	}
	return nil
}

func (x *ServiceCreateDef) GetAdditionalService() *AdditionalService {
	if x != nil {
		return x.AdditionalService
	}
	return nil
}

func (x *ServiceCreateDef) GetAvailableTypeBreed() *AvailablePetTypeBreed {
	if x != nil {
		return x.AvailableTypeBreed
	}
	return nil
}

func (x *ServiceCreateDef) GetAvailablePetSize() *AvailablePetSize {
	if x != nil {
		return x.AvailablePetSize
	}
	return nil
}

func (x *ServiceCreateDef) GetAvailableCoatType() *AvailableCoatType {
	if x != nil {
		return x.AvailableCoatType
	}
	return nil
}

func (x *ServiceCreateDef) GetAvailablePetCode() *AvailablePetCode {
	if x != nil {
		return x.AvailablePetCode
	}
	return nil
}

func (x *ServiceCreateDef) GetAttributes() *ServiceAttributes {
	if x != nil {
		return x.Attributes
	}
	return nil
}

func (x *ServiceCreateDef) GetAvailablePetWeight() *AvailablePetWeight {
	if x != nil {
		return x.AvailablePetWeight
	}
	return nil
}

func (x *ServiceCreateDef) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *ServiceCreateDef) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *ServiceCreateDef) GetBusinessStaffOverrides() []*BusinessStaffOverride {
	if x != nil {
		return x.BusinessStaffOverrides
	}
	return nil
}

// list filter
type ListServicesRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// category ids
	CategoryIds []int64 `protobuf:"varint,1,rep,packed,name=category_ids,json=categoryIds,proto3" json:"category_ids,omitempty"`
	// care type ids
	CareTypeIds []int64 `protobuf:"varint,2,rep,packed,name=care_type_ids,json=careTypeIds,proto3" json:"care_type_ids,omitempty"`
	// statuses
	Statuses      []Service_Status `protobuf:"varint,3,rep,packed,name=statuses,proto3,enum=backend.proto.offering.v1.Service_Status" json:"statuses,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListServicesRequest_Filter) Reset() {
	*x = ListServicesRequest_Filter{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListServicesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServicesRequest_Filter) ProtoMessage() {}

func (x *ListServicesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServicesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListServicesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{9, 0}
}

func (x *ListServicesRequest_Filter) GetCategoryIds() []int64 {
	if x != nil {
		return x.CategoryIds
	}
	return nil
}

func (x *ListServicesRequest_Filter) GetCareTypeIds() []int64 {
	if x != nil {
		return x.CareTypeIds
	}
	return nil
}

func (x *ListServicesRequest_Filter) GetStatuses() []Service_Status {
	if x != nil {
		return x.Statuses
	}
	return nil
}

// 过滤条件
type ListAvailableServicesRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 当前选择的 Pet ID 列表
	// 如果传递了 pet_ids，将根据 pet 的 type、breed、code
	// 等配置信息进行服务匹配筛选 如果未传递 pet_ids，则返回该 business
	// 下所有可用的服务
	PetIds []int64 `protobuf:"varint,1,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
	// 当前选择的 care_type_id 列表
	CareTypeIds []int64 `protobuf:"varint,2,rep,packed,name=care_type_ids,json=careTypeIds,proto3" json:"care_type_ids,omitempty"`
	// 状态过滤（如：active/inactive 等）
	Statuses []Service_Status `protobuf:"varint,3,rep,packed,name=statuses,proto3,enum=backend.proto.offering.v1.Service_Status" json:"statuses,omitempty"`
	// 关键词搜索（如服务名称等）
	Keyword       *string `protobuf:"bytes,4,opt,name=keyword,proto3,oneof" json:"keyword,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAvailableServicesRequest_Filter) Reset() {
	*x = ListAvailableServicesRequest_Filter{}
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAvailableServicesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAvailableServicesRequest_Filter) ProtoMessage() {}

func (x *ListAvailableServicesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_service_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAvailableServicesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListAvailableServicesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_service_proto_rawDescGZIP(), []int{14, 0}
}

func (x *ListAvailableServicesRequest_Filter) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

func (x *ListAvailableServicesRequest_Filter) GetCareTypeIds() []int64 {
	if x != nil {
		return x.CareTypeIds
	}
	return nil
}

func (x *ListAvailableServicesRequest_Filter) GetStatuses() []Service_Status {
	if x != nil {
		return x.Statuses
	}
	return nil
}

func (x *ListAvailableServicesRequest_Filter) GetKeyword() string {
	if x != nil && x.Keyword != nil {
		return *x.Keyword
	}
	return ""
}

var File_backend_proto_offering_v1_service_service_proto protoreflect.FileDescriptor

const file_backend_proto_offering_v1_service_service_proto_rawDesc = "" +
	"\n" +
	"/backend/proto/offering/v1/service_service.proto\x12\x19backend.proto.offering.v1\x1a&backend/proto/offering/v1/common.proto\x1a'backend/proto/offering/v1/service.proto\x1a2backend/proto/offering/v1/service_ob_setting.proto\x1a0backend/proto/organization/v1/organization.proto\x1a\x1bbuf/validate/validate.proto\x1a\x17google/type/money.proto\">\n" +
	"\x14DeleteServiceRequest\x12&\n" +
	"\n" +
	"service_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tserviceId\"\x17\n" +
	"\x15DeleteServiceResponse\"e\n" +
	"\x14UpdateServiceRequest\x12M\n" +
	"\aservice\x18\x01 \x01(\v2+.backend.proto.offering.v1.ServiceUpdateDefB\x06\xbaH\x03\xc8\x01\x01R\aservice\"\x17\n" +
	"\x15UpdateServiceResponse\"e\n" +
	"\x14CreateServiceRequest\x12M\n" +
	"\aservice\x18\x01 \x01(\v2+.backend.proto.offering.v1.ServiceCreateDefB\x06\xbaH\x03\xc8\x01\x01R\aservice\"6\n" +
	"\x15CreateServiceResponse\x12\x1d\n" +
	"\n" +
	"service_id\x18\x01 \x01(\x03R\tserviceId\";\n" +
	"\x11GetServiceRequest\x12&\n" +
	"\n" +
	"service_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tserviceId\"R\n" +
	"\x12GetServiceResponse\x12<\n" +
	"\aservice\x18\x01 \x01(\v2\".backend.proto.offering.v1.ServiceR\aservice\"\x86\x01\n" +
	"\x0fCategoryService\x12\x1f\n" +
	"\vcategory_id\x18\x01 \x01(\x03R\n" +
	"categoryId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12>\n" +
	"\bservices\x18\x03 \x03(\v2\".backend.proto.offering.v1.ServiceR\bservices\"\xe8\x04\n" +
	"\x13ListServicesRequest\x12k\n" +
	"\x11organization_type\x18\x01 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeB\r\xbaH\n" +
	"\xc8\x01\x01\x82\x01\x04\x10\x01 \x00R\x10organizationType\x123\n" +
	"\x0forganization_id\x18\x02 \x01(\x03B\n" +
	"\xbaH\a\xc8\x01\x01\"\x02 \x00R\x0eorganizationId\x12M\n" +
	"\x06filter\x18\x03 \x01(\v25.backend.proto.offering.v1.ListServicesRequest.FilterR\x06filter\x12Y\n" +
	"\x12extra_info_options\x18\x04 \x01(\v2+.backend.proto.offering.v1.ExtraInfoOptionsR\x10extraInfoOptions\x12H\n" +
	"\n" +
	"pagination\x18\x05 \x01(\v2(.backend.proto.offering.v1.PaginationRefR\n" +
	"pagination\x1a\xba\x01\n" +
	"\x06Filter\x12-\n" +
	"\fcategory_ids\x18\x01 \x03(\x03B\n" +
	"\xbaH\a\x92\x01\x04\b\x00\x10dR\vcategoryIds\x12.\n" +
	"\rcare_type_ids\x18\x02 \x03(\x03B\n" +
	"\xbaH\a\x92\x01\x04\b\x00\x10dR\vcareTypeIds\x12Q\n" +
	"\bstatuses\x18\x03 \x03(\x0e2).backend.proto.offering.v1.Service.StatusB\n" +
	"\xbaH\a\x92\x01\x04\b\x00\x10\n" +
	"R\bstatuses\"\xd7\x01\n" +
	"\x14ListServicesResponse\x12K\n" +
	"\bservices\x18\x01 \x03(\v2/.backend.proto.offering.v1.ServiceWithExtraInfoR\bservices\x12M\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2(.backend.proto.offering.v1.PaginationRefH\x00R\n" +
	"pagination\x88\x01\x01\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05totalB\r\n" +
	"\v_pagination\"\x9b\x02\n" +
	"\x1aBatchUpdateServicesRequest\x12h\n" +
	"\x11organization_type\x18\x01 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeB\n" +
	"\xbaH\a\x82\x01\x04\x10\x01 \x00R\x10organizationType\x120\n" +
	"\x0forganization_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x0eorganizationId\x12a\n" +
	"\x0fupdate_services\x18\x03 \x03(\v2+.backend.proto.offering.v1.ServiceUpdateDefB\v\xbaH\b\x92\x01\x05\b\x01\x10\xe8\aR\x0eupdateServices\"\xd4\f\n" +
	"\x10ServiceUpdateDef\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\x12-\n" +
	"\vcategory_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x00R\n" +
	"categoryId\x88\x01\x01\x12 \n" +
	"\x04name\x18\x03 \x01(\tB\a\xbaH\x04r\x02\x18dH\x01R\x04name\x88\x01\x01\x12/\n" +
	"\vdescription\x18\x04 \x01(\tB\b\xbaH\x05r\x03\x18\xe8\aH\x02R\vdescription\x88\x01\x01\x12+\n" +
	"\n" +
	"color_code\x18\x05 \x01(\tB\a\xbaH\x04r\x02\x18\n" +
	"H\x03R\tcolorCode\x88\x01\x01\x12 \n" +
	"\x04sort\x18\x06 \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x04R\x04sort\x88\x01\x01\x12,\n" +
	"\x06images\x18\a \x03(\tB\x14\xbaH\x11\x92\x01\x0e\b\x00\x10\xe8\a\x18\x01\"\x05r\x03\x18\x80\bR\x06images\x12R\n" +
	"\x06status\x18\b \x01(\x0e2).backend.proto.offering.v1.Service.StatusB\n" +
	"\xbaH\a\x82\x01\x04\x10\x01 \x00H\x05R\x06status\x88\x01\x01\x12`\n" +
	"\x12available_business\x18\t \x01(\v2,.backend.proto.offering.v1.AvailableBusinessH\x06R\x11availableBusiness\x88\x01\x01\x12Q\n" +
	"\n" +
	"attributes\x18\n" +
	" \x01(\v2,.backend.proto.offering.v1.ServiceAttributesH\aR\n" +
	"attributes\x88\x01\x01\x12`\n" +
	"\x12additional_service\x18\v \x01(\v2,.backend.proto.offering.v1.AdditionalServiceH\bR\x11additionalService\x88\x01\x01\x12g\n" +
	"\x14available_type_breed\x18\f \x01(\v20.backend.proto.offering.v1.AvailablePetTypeBreedH\tR\x12availableTypeBreed\x88\x01\x01\x12^\n" +
	"\x12available_pet_size\x18\r \x01(\v2+.backend.proto.offering.v1.AvailablePetSizeH\n" +
	"R\x10availablePetSize\x88\x01\x01\x12a\n" +
	"\x13available_coat_type\x18\x0e \x01(\v2,.backend.proto.offering.v1.AvailableCoatTypeH\vR\x11availableCoatType\x88\x01\x01\x12^\n" +
	"\x12available_pet_code\x18\x0f \x01(\v2+.backend.proto.offering.v1.AvailablePetCodeH\fR\x10availablePetCode\x88\x01\x01\x12d\n" +
	"\x14available_pet_weight\x18\x10 \x01(\v2-.backend.proto.offering.v1.AvailablePetWeightH\rR\x12availablePetWeight\x88\x01\x01\x12-\n" +
	"\x05price\x18\x11 \x01(\v2\x12.google.type.MoneyH\x0eR\x05price\x88\x01\x01\x12\x1a\n" +
	"\x06tax_id\x18\x13 \x01(\x03H\x0fR\x05taxId\x88\x01\x01\x12j\n" +
	"\x18business_staff_overrides\x18\x14 \x03(\v20.backend.proto.offering.v1.BusinessStaffOverrideR\x16businessStaffOverridesB\x0e\n" +
	"\f_category_idB\a\n" +
	"\x05_nameB\x0e\n" +
	"\f_descriptionB\r\n" +
	"\v_color_codeB\a\n" +
	"\x05_sortB\t\n" +
	"\a_statusB\x15\n" +
	"\x13_available_businessB\r\n" +
	"\v_attributesB\x15\n" +
	"\x13_additional_serviceB\x17\n" +
	"\x15_available_type_breedB\x15\n" +
	"\x13_available_pet_sizeB\x16\n" +
	"\x14_available_coat_typeB\x15\n" +
	"\x13_available_pet_codeB\x17\n" +
	"\x15_available_pet_weightB\b\n" +
	"\x06_priceB\t\n" +
	"\a_tax_id\"\x1d\n" +
	"\x1bBatchUpdateServicesResponse\"\xed\x05\n" +
	"\x1cListAvailableServicesRequest\x12h\n" +
	"\x11organization_type\x18\x01 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeB\n" +
	"\xbaH\a\x82\x01\x04\x10\x01 \x00R\x10organizationType\x120\n" +
	"\x0forganization_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x0eorganizationId\x12(\n" +
	"\vbusiness_id\x18\x03 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"businessId\x12?\n" +
	"\x1cenable_business_scope_filter\x18\x04 \x01(\bR\x19enableBusinessScopeFilter\x12[\n" +
	"\x06filter\x18\x05 \x01(\v2>.backend.proto.offering.v1.ListAvailableServicesRequest.FilterH\x00R\x06filter\x88\x01\x01\x12M\n" +
	"\n" +
	"pagination\x18\x06 \x01(\v2(.backend.proto.offering.v1.PaginationRefH\x01R\n" +
	"pagination\x88\x01\x01\x1a\xff\x01\n" +
	"\x06Filter\x12+\n" +
	"\apet_ids\x18\x01 \x03(\x03B\x12\xbaH\x0f\x92\x01\f\b\x00\x10d\x18\x01\"\x04\"\x02 \x00R\x06petIds\x126\n" +
	"\rcare_type_ids\x18\x02 \x03(\x03B\x12\xbaH\x0f\x92\x01\f\b\x00\x10d\x18\x01\"\x04\"\x02 \x00R\vcareTypeIds\x12\\\n" +
	"\bstatuses\x18\x03 \x03(\x0e2).backend.proto.offering.v1.Service.StatusB\x15\xbaH\x12\x92\x01\x0f\b\x00\x10\n" +
	"\x18\x01\"\a\x82\x01\x04\x10\x01 \x00R\bstatuses\x12&\n" +
	"\akeyword\x18\x04 \x01(\tB\a\xbaH\x04r\x02\x18dH\x00R\akeyword\x88\x01\x01B\n" +
	"\n" +
	"\b_keywordB\t\n" +
	"\a_filterB\r\n" +
	"\v_pagination\"\xd3\x01\n" +
	"\x1dListAvailableServicesResponse\x12>\n" +
	"\bservices\x18\x01 \x03(\v2\".backend.proto.offering.v1.ServiceR\bservices\x12M\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2(.backend.proto.offering.v1.PaginationRefH\x00R\n" +
	"pagination\x88\x01\x01\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05totalB\r\n" +
	"\v_pagination\"\xfa\x03\n" +
	"\x16UpdateOBServiceRequest\x12\\\n" +
	"\x11organization_type\x18\x01 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeR\x10organizationType\x12'\n" +
	"\x0forganization_id\x18\x02 \x01(\x03R\x0eorganizationId\x12&\n" +
	"\n" +
	"service_id\x18\x03 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tserviceId\x12&\n" +
	"\fis_available\x18\v \x01(\bH\x00R\visAvailable\x88\x01\x01\x12j\n" +
	"\x0fshow_base_price\x18\f \x01(\x0e2=.backend.proto.offering.v1.ServiceOBSetting.ShowBasePriceModeH\x01R\rshowBasePrice\x88\x01\x01\x12%\n" +
	"\fis_all_staff\x18\r \x01(\bH\x02R\n" +
	"isAllStaff\x88\x01\x01\x12\x1b\n" +
	"\tstaff_ids\x18\x0e \x03(\x03R\bstaffIds\x12\x19\n" +
	"\x05alias\x18\x0f \x01(\tH\x03R\x05alias\x88\x01\x01B\x0f\n" +
	"\r_is_availableB\x12\n" +
	"\x10_show_base_priceB\x0f\n" +
	"\r_is_all_staffB\b\n" +
	"\x06_alias\"\x19\n" +
	"\x17UpdateOBServiceResponse\"\xdb\x01\n" +
	"\x17BatchGetServicesRequest\x12h\n" +
	"\x11organization_type\x18\x01 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeB\n" +
	"\xbaH\a\x82\x01\x04\x10\x01 \x00R\x10organizationType\x120\n" +
	"\x0forganization_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x0eorganizationId\x12$\n" +
	"\x03ids\x18\x03 \x03(\x03B\x12\xbaH\x0f\x92\x01\f\b\x01\x10d\x18\x01\"\x04\"\x02 \x00R\x03ids\"Z\n" +
	"\x18BatchGetServicesResponse\x12>\n" +
	"\bservices\x18\x01 \x03(\v2\".backend.proto.offering.v1.ServiceR\bservices\"\xeb\r\n" +
	"\x10ServiceCreateDef\x12k\n" +
	"\x11organization_type\x18\x01 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeB\r\xbaH\n" +
	"\xc8\x01\x01\x82\x01\x04\x10\x01 \x00R\x10organizationType\x123\n" +
	"\x0forganization_id\x18\x02 \x01(\x03B\n" +
	"\xbaH\a\xc8\x01\x01\"\x02 \x00R\x0eorganizationId\x12,\n" +
	"\fcare_type_id\x18\x03 \x01(\x03B\n" +
	"\xbaH\a\xc8\x01\x01\"\x02 \x00R\n" +
	"careTypeId\x12-\n" +
	"\vcategory_id\x18\x04 \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x00R\n" +
	"categoryId\x88\x01\x01\x12 \n" +
	"\x04name\x18\x05 \x01(\tB\f\xbaH\t\xc8\x01\x01r\x04\x10\x01\x18dR\x04name\x12/\n" +
	"\vdescription\x18\x06 \x01(\tB\b\xbaH\x05r\x03\x18\xe8\aH\x01R\vdescription\x88\x01\x01\x12+\n" +
	"\n" +
	"color_code\x18\a \x01(\tB\f\xbaH\t\xc8\x01\x01r\x04\x10\x01\x18\n" +
	"R\tcolorCode\x12,\n" +
	"\x06images\x18\t \x03(\tB\x14\xbaH\x11\x92\x01\x0e\b\x00\x10\xe8\a\x18\x01\"\x05r\x03\x18\x80\bR\x06images\x12P\n" +
	"\x06source\x18\n" +
	" \x01(\x0e2).backend.proto.offering.v1.OfferingSourceB\r\xbaH\n" +
	"\xc8\x01\x01\x82\x01\x04\x10\x01 \x00R\x06source\x12P\n" +
	"\x06status\x18\v \x01(\x0e2).backend.proto.offering.v1.Service.StatusB\r\xbaH\n" +
	"\xc8\x01\x01\x82\x01\x04\x10\x01 \x00R\x06status\x12c\n" +
	"\x12available_business\x18\r \x01(\v2,.backend.proto.offering.v1.AvailableBusinessB\x06\xbaH\x03\xc8\x01\x01R\x11availableBusiness\x12`\n" +
	"\x12additional_service\x18\x0e \x01(\v2,.backend.proto.offering.v1.AdditionalServiceH\x02R\x11additionalService\x88\x01\x01\x12g\n" +
	"\x14available_type_breed\x18\x0f \x01(\v20.backend.proto.offering.v1.AvailablePetTypeBreedH\x03R\x12availableTypeBreed\x88\x01\x01\x12^\n" +
	"\x12available_pet_size\x18\x10 \x01(\v2+.backend.proto.offering.v1.AvailablePetSizeH\x04R\x10availablePetSize\x88\x01\x01\x12a\n" +
	"\x13available_coat_type\x18\x11 \x01(\v2,.backend.proto.offering.v1.AvailableCoatTypeH\x05R\x11availableCoatType\x88\x01\x01\x12^\n" +
	"\x12available_pet_code\x18\x12 \x01(\v2+.backend.proto.offering.v1.AvailablePetCodeH\x06R\x10availablePetCode\x88\x01\x01\x12Q\n" +
	"\n" +
	"attributes\x18\x13 \x01(\v2,.backend.proto.offering.v1.ServiceAttributesH\aR\n" +
	"attributes\x88\x01\x01\x12d\n" +
	"\x14available_pet_weight\x18\x14 \x01(\v2-.backend.proto.offering.v1.AvailablePetWeightH\bR\x12availablePetWeight\x88\x01\x01\x120\n" +
	"\x05price\x18\x15 \x01(\v2\x12.google.type.MoneyB\x06\xbaH\x03\xc8\x01\x01R\x05price\x12\x1e\n" +
	"\x06tax_id\x18\x17 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x05taxId\x12j\n" +
	"\x18business_staff_overrides\x18\x19 \x03(\v20.backend.proto.offering.v1.BusinessStaffOverrideR\x16businessStaffOverridesB\x0e\n" +
	"\f_category_idB\x0e\n" +
	"\f_descriptionB\x15\n" +
	"\x13_additional_serviceB\x17\n" +
	"\x15_available_type_breedB\x15\n" +
	"\x13_available_pet_sizeB\x16\n" +
	"\x14_available_coat_typeB\x15\n" +
	"\x13_available_pet_codeB\r\n" +
	"\v_attributesB\x17\n" +
	"\x15_available_pet_weight2\xd3\b\n" +
	"\x0eServiceService\x12r\n" +
	"\rCreateService\x12/.backend.proto.offering.v1.CreateServiceRequest\x1a0.backend.proto.offering.v1.CreateServiceResponse\x12i\n" +
	"\n" +
	"GetService\x12,.backend.proto.offering.v1.GetServiceRequest\x1a-.backend.proto.offering.v1.GetServiceResponse\x12r\n" +
	"\rUpdateService\x12/.backend.proto.offering.v1.UpdateServiceRequest\x1a0.backend.proto.offering.v1.UpdateServiceResponse\x12r\n" +
	"\rDeleteService\x12/.backend.proto.offering.v1.DeleteServiceRequest\x1a0.backend.proto.offering.v1.DeleteServiceResponse\x12o\n" +
	"\fListServices\x12..backend.proto.offering.v1.ListServicesRequest\x1a/.backend.proto.offering.v1.ListServicesResponse\x12\x8a\x01\n" +
	"\x15ListAvailableServices\x127.backend.proto.offering.v1.ListAvailableServicesRequest\x1a8.backend.proto.offering.v1.ListAvailableServicesResponse\x12\x84\x01\n" +
	"\x13BatchUpdateServices\x125.backend.proto.offering.v1.BatchUpdateServicesRequest\x1a6.backend.proto.offering.v1.BatchUpdateServicesResponse\x12x\n" +
	"\x0fUpdateOBService\x121.backend.proto.offering.v1.UpdateOBServiceRequest\x1a2.backend.proto.offering.v1.UpdateOBServiceResponse\x12{\n" +
	"\x10BatchGetServices\x122.backend.proto.offering.v1.BatchGetServicesRequest\x1a3.backend.proto.offering.v1.BatchGetServicesResponseBk\n" +
	"#com.moego.backend.proto.offering.v1P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpbb\x06proto3"

var (
	file_backend_proto_offering_v1_service_service_proto_rawDescOnce sync.Once
	file_backend_proto_offering_v1_service_service_proto_rawDescData []byte
)

func file_backend_proto_offering_v1_service_service_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_v1_service_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_v1_service_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_service_proto_rawDesc), len(file_backend_proto_offering_v1_service_service_proto_rawDesc)))
	})
	return file_backend_proto_offering_v1_service_service_proto_rawDescData
}

var file_backend_proto_offering_v1_service_service_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_backend_proto_offering_v1_service_service_proto_goTypes = []any{
	(*DeleteServiceRequest)(nil),                // 0: backend.proto.offering.v1.DeleteServiceRequest
	(*DeleteServiceResponse)(nil),               // 1: backend.proto.offering.v1.DeleteServiceResponse
	(*UpdateServiceRequest)(nil),                // 2: backend.proto.offering.v1.UpdateServiceRequest
	(*UpdateServiceResponse)(nil),               // 3: backend.proto.offering.v1.UpdateServiceResponse
	(*CreateServiceRequest)(nil),                // 4: backend.proto.offering.v1.CreateServiceRequest
	(*CreateServiceResponse)(nil),               // 5: backend.proto.offering.v1.CreateServiceResponse
	(*GetServiceRequest)(nil),                   // 6: backend.proto.offering.v1.GetServiceRequest
	(*GetServiceResponse)(nil),                  // 7: backend.proto.offering.v1.GetServiceResponse
	(*CategoryService)(nil),                     // 8: backend.proto.offering.v1.CategoryService
	(*ListServicesRequest)(nil),                 // 9: backend.proto.offering.v1.ListServicesRequest
	(*ListServicesResponse)(nil),                // 10: backend.proto.offering.v1.ListServicesResponse
	(*BatchUpdateServicesRequest)(nil),          // 11: backend.proto.offering.v1.BatchUpdateServicesRequest
	(*ServiceUpdateDef)(nil),                    // 12: backend.proto.offering.v1.ServiceUpdateDef
	(*BatchUpdateServicesResponse)(nil),         // 13: backend.proto.offering.v1.BatchUpdateServicesResponse
	(*ListAvailableServicesRequest)(nil),        // 14: backend.proto.offering.v1.ListAvailableServicesRequest
	(*ListAvailableServicesResponse)(nil),       // 15: backend.proto.offering.v1.ListAvailableServicesResponse
	(*UpdateOBServiceRequest)(nil),              // 16: backend.proto.offering.v1.UpdateOBServiceRequest
	(*UpdateOBServiceResponse)(nil),             // 17: backend.proto.offering.v1.UpdateOBServiceResponse
	(*BatchGetServicesRequest)(nil),             // 18: backend.proto.offering.v1.BatchGetServicesRequest
	(*BatchGetServicesResponse)(nil),            // 19: backend.proto.offering.v1.BatchGetServicesResponse
	(*ServiceCreateDef)(nil),                    // 20: backend.proto.offering.v1.ServiceCreateDef
	(*ListServicesRequest_Filter)(nil),          // 21: backend.proto.offering.v1.ListServicesRequest.Filter
	(*ListAvailableServicesRequest_Filter)(nil), // 22: backend.proto.offering.v1.ListAvailableServicesRequest.Filter
	(*Service)(nil),                             // 23: backend.proto.offering.v1.Service
	(v1.OrganizationType)(0),                    // 24: backend.proto.organization.v1.OrganizationType
	(*ExtraInfoOptions)(nil),                    // 25: backend.proto.offering.v1.ExtraInfoOptions
	(*PaginationRef)(nil),                       // 26: backend.proto.offering.v1.PaginationRef
	(*ServiceWithExtraInfo)(nil),                // 27: backend.proto.offering.v1.ServiceWithExtraInfo
	(Service_Status)(0),                         // 28: backend.proto.offering.v1.Service.Status
	(*AvailableBusiness)(nil),                   // 29: backend.proto.offering.v1.AvailableBusiness
	(*ServiceAttributes)(nil),                   // 30: backend.proto.offering.v1.ServiceAttributes
	(*AdditionalService)(nil),                   // 31: backend.proto.offering.v1.AdditionalService
	(*AvailablePetTypeBreed)(nil),               // 32: backend.proto.offering.v1.AvailablePetTypeBreed
	(*AvailablePetSize)(nil),                    // 33: backend.proto.offering.v1.AvailablePetSize
	(*AvailableCoatType)(nil),                   // 34: backend.proto.offering.v1.AvailableCoatType
	(*AvailablePetCode)(nil),                    // 35: backend.proto.offering.v1.AvailablePetCode
	(*AvailablePetWeight)(nil),                  // 36: backend.proto.offering.v1.AvailablePetWeight
	(*money.Money)(nil),                         // 37: google.type.Money
	(*BusinessStaffOverride)(nil),               // 38: backend.proto.offering.v1.BusinessStaffOverride
	(ServiceOBSetting_ShowBasePriceMode)(0),     // 39: backend.proto.offering.v1.ServiceOBSetting.ShowBasePriceMode
	(OfferingSource)(0),                         // 40: backend.proto.offering.v1.OfferingSource
}
var file_backend_proto_offering_v1_service_service_proto_depIdxs = []int32{
	12, // 0: backend.proto.offering.v1.UpdateServiceRequest.service:type_name -> backend.proto.offering.v1.ServiceUpdateDef
	20, // 1: backend.proto.offering.v1.CreateServiceRequest.service:type_name -> backend.proto.offering.v1.ServiceCreateDef
	23, // 2: backend.proto.offering.v1.GetServiceResponse.service:type_name -> backend.proto.offering.v1.Service
	23, // 3: backend.proto.offering.v1.CategoryService.services:type_name -> backend.proto.offering.v1.Service
	24, // 4: backend.proto.offering.v1.ListServicesRequest.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	21, // 5: backend.proto.offering.v1.ListServicesRequest.filter:type_name -> backend.proto.offering.v1.ListServicesRequest.Filter
	25, // 6: backend.proto.offering.v1.ListServicesRequest.extra_info_options:type_name -> backend.proto.offering.v1.ExtraInfoOptions
	26, // 7: backend.proto.offering.v1.ListServicesRequest.pagination:type_name -> backend.proto.offering.v1.PaginationRef
	27, // 8: backend.proto.offering.v1.ListServicesResponse.services:type_name -> backend.proto.offering.v1.ServiceWithExtraInfo
	26, // 9: backend.proto.offering.v1.ListServicesResponse.pagination:type_name -> backend.proto.offering.v1.PaginationRef
	24, // 10: backend.proto.offering.v1.BatchUpdateServicesRequest.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	12, // 11: backend.proto.offering.v1.BatchUpdateServicesRequest.update_services:type_name -> backend.proto.offering.v1.ServiceUpdateDef
	28, // 12: backend.proto.offering.v1.ServiceUpdateDef.status:type_name -> backend.proto.offering.v1.Service.Status
	29, // 13: backend.proto.offering.v1.ServiceUpdateDef.available_business:type_name -> backend.proto.offering.v1.AvailableBusiness
	30, // 14: backend.proto.offering.v1.ServiceUpdateDef.attributes:type_name -> backend.proto.offering.v1.ServiceAttributes
	31, // 15: backend.proto.offering.v1.ServiceUpdateDef.additional_service:type_name -> backend.proto.offering.v1.AdditionalService
	32, // 16: backend.proto.offering.v1.ServiceUpdateDef.available_type_breed:type_name -> backend.proto.offering.v1.AvailablePetTypeBreed
	33, // 17: backend.proto.offering.v1.ServiceUpdateDef.available_pet_size:type_name -> backend.proto.offering.v1.AvailablePetSize
	34, // 18: backend.proto.offering.v1.ServiceUpdateDef.available_coat_type:type_name -> backend.proto.offering.v1.AvailableCoatType
	35, // 19: backend.proto.offering.v1.ServiceUpdateDef.available_pet_code:type_name -> backend.proto.offering.v1.AvailablePetCode
	36, // 20: backend.proto.offering.v1.ServiceUpdateDef.available_pet_weight:type_name -> backend.proto.offering.v1.AvailablePetWeight
	37, // 21: backend.proto.offering.v1.ServiceUpdateDef.price:type_name -> google.type.Money
	38, // 22: backend.proto.offering.v1.ServiceUpdateDef.business_staff_overrides:type_name -> backend.proto.offering.v1.BusinessStaffOverride
	24, // 23: backend.proto.offering.v1.ListAvailableServicesRequest.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	22, // 24: backend.proto.offering.v1.ListAvailableServicesRequest.filter:type_name -> backend.proto.offering.v1.ListAvailableServicesRequest.Filter
	26, // 25: backend.proto.offering.v1.ListAvailableServicesRequest.pagination:type_name -> backend.proto.offering.v1.PaginationRef
	23, // 26: backend.proto.offering.v1.ListAvailableServicesResponse.services:type_name -> backend.proto.offering.v1.Service
	26, // 27: backend.proto.offering.v1.ListAvailableServicesResponse.pagination:type_name -> backend.proto.offering.v1.PaginationRef
	24, // 28: backend.proto.offering.v1.UpdateOBServiceRequest.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	39, // 29: backend.proto.offering.v1.UpdateOBServiceRequest.show_base_price:type_name -> backend.proto.offering.v1.ServiceOBSetting.ShowBasePriceMode
	24, // 30: backend.proto.offering.v1.BatchGetServicesRequest.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	23, // 31: backend.proto.offering.v1.BatchGetServicesResponse.services:type_name -> backend.proto.offering.v1.Service
	24, // 32: backend.proto.offering.v1.ServiceCreateDef.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	40, // 33: backend.proto.offering.v1.ServiceCreateDef.source:type_name -> backend.proto.offering.v1.OfferingSource
	28, // 34: backend.proto.offering.v1.ServiceCreateDef.status:type_name -> backend.proto.offering.v1.Service.Status
	29, // 35: backend.proto.offering.v1.ServiceCreateDef.available_business:type_name -> backend.proto.offering.v1.AvailableBusiness
	31, // 36: backend.proto.offering.v1.ServiceCreateDef.additional_service:type_name -> backend.proto.offering.v1.AdditionalService
	32, // 37: backend.proto.offering.v1.ServiceCreateDef.available_type_breed:type_name -> backend.proto.offering.v1.AvailablePetTypeBreed
	33, // 38: backend.proto.offering.v1.ServiceCreateDef.available_pet_size:type_name -> backend.proto.offering.v1.AvailablePetSize
	34, // 39: backend.proto.offering.v1.ServiceCreateDef.available_coat_type:type_name -> backend.proto.offering.v1.AvailableCoatType
	35, // 40: backend.proto.offering.v1.ServiceCreateDef.available_pet_code:type_name -> backend.proto.offering.v1.AvailablePetCode
	30, // 41: backend.proto.offering.v1.ServiceCreateDef.attributes:type_name -> backend.proto.offering.v1.ServiceAttributes
	36, // 42: backend.proto.offering.v1.ServiceCreateDef.available_pet_weight:type_name -> backend.proto.offering.v1.AvailablePetWeight
	37, // 43: backend.proto.offering.v1.ServiceCreateDef.price:type_name -> google.type.Money
	38, // 44: backend.proto.offering.v1.ServiceCreateDef.business_staff_overrides:type_name -> backend.proto.offering.v1.BusinessStaffOverride
	28, // 45: backend.proto.offering.v1.ListServicesRequest.Filter.statuses:type_name -> backend.proto.offering.v1.Service.Status
	28, // 46: backend.proto.offering.v1.ListAvailableServicesRequest.Filter.statuses:type_name -> backend.proto.offering.v1.Service.Status
	4,  // 47: backend.proto.offering.v1.ServiceService.CreateService:input_type -> backend.proto.offering.v1.CreateServiceRequest
	6,  // 48: backend.proto.offering.v1.ServiceService.GetService:input_type -> backend.proto.offering.v1.GetServiceRequest
	2,  // 49: backend.proto.offering.v1.ServiceService.UpdateService:input_type -> backend.proto.offering.v1.UpdateServiceRequest
	0,  // 50: backend.proto.offering.v1.ServiceService.DeleteService:input_type -> backend.proto.offering.v1.DeleteServiceRequest
	9,  // 51: backend.proto.offering.v1.ServiceService.ListServices:input_type -> backend.proto.offering.v1.ListServicesRequest
	14, // 52: backend.proto.offering.v1.ServiceService.ListAvailableServices:input_type -> backend.proto.offering.v1.ListAvailableServicesRequest
	11, // 53: backend.proto.offering.v1.ServiceService.BatchUpdateServices:input_type -> backend.proto.offering.v1.BatchUpdateServicesRequest
	16, // 54: backend.proto.offering.v1.ServiceService.UpdateOBService:input_type -> backend.proto.offering.v1.UpdateOBServiceRequest
	18, // 55: backend.proto.offering.v1.ServiceService.BatchGetServices:input_type -> backend.proto.offering.v1.BatchGetServicesRequest
	5,  // 56: backend.proto.offering.v1.ServiceService.CreateService:output_type -> backend.proto.offering.v1.CreateServiceResponse
	7,  // 57: backend.proto.offering.v1.ServiceService.GetService:output_type -> backend.proto.offering.v1.GetServiceResponse
	3,  // 58: backend.proto.offering.v1.ServiceService.UpdateService:output_type -> backend.proto.offering.v1.UpdateServiceResponse
	1,  // 59: backend.proto.offering.v1.ServiceService.DeleteService:output_type -> backend.proto.offering.v1.DeleteServiceResponse
	10, // 60: backend.proto.offering.v1.ServiceService.ListServices:output_type -> backend.proto.offering.v1.ListServicesResponse
	15, // 61: backend.proto.offering.v1.ServiceService.ListAvailableServices:output_type -> backend.proto.offering.v1.ListAvailableServicesResponse
	13, // 62: backend.proto.offering.v1.ServiceService.BatchUpdateServices:output_type -> backend.proto.offering.v1.BatchUpdateServicesResponse
	17, // 63: backend.proto.offering.v1.ServiceService.UpdateOBService:output_type -> backend.proto.offering.v1.UpdateOBServiceResponse
	19, // 64: backend.proto.offering.v1.ServiceService.BatchGetServices:output_type -> backend.proto.offering.v1.BatchGetServicesResponse
	56, // [56:65] is the sub-list for method output_type
	47, // [47:56] is the sub-list for method input_type
	47, // [47:47] is the sub-list for extension type_name
	47, // [47:47] is the sub-list for extension extendee
	0,  // [0:47] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_v1_service_service_proto_init() }
func file_backend_proto_offering_v1_service_service_proto_init() {
	if File_backend_proto_offering_v1_service_service_proto != nil {
		return
	}
	file_backend_proto_offering_v1_common_proto_init()
	file_backend_proto_offering_v1_service_proto_init()
	file_backend_proto_offering_v1_service_ob_setting_proto_init()
	file_backend_proto_offering_v1_service_service_proto_msgTypes[10].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_service_proto_msgTypes[12].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_service_proto_msgTypes[14].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_service_proto_msgTypes[15].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_service_proto_msgTypes[16].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_service_proto_msgTypes[20].OneofWrappers = []any{}
	file_backend_proto_offering_v1_service_service_proto_msgTypes[22].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_service_proto_rawDesc), len(file_backend_proto_offering_v1_service_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_offering_v1_service_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_v1_service_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_offering_v1_service_service_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_v1_service_service_proto = out.File
	file_backend_proto_offering_v1_service_service_proto_goTypes = nil
	file_backend_proto_offering_v1_service_service_proto_depIdxs = nil
}
