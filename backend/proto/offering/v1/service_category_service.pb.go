// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/offering/v1/service_category_service.proto

package offeringpb

import (
	v1 "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: no need page --)
//
// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: no need page --)
//
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//
//	aip.dev/not-precedent: no need page --)
//
// list category request
type ListCategoriesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The organization type
	OrganizationType v1.OrganizationType `protobuf:"varint,1,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// The organization ID.
	OrganizationId int64 `protobuf:"varint,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// filter
	Filter        *ListCategoriesRequest_Filter `protobuf:"bytes,18,opt,name=filter,proto3" json:"filter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCategoriesRequest) Reset() {
	*x = ListCategoriesRequest{}
	mi := &file_backend_proto_offering_v1_service_category_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCategoriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCategoriesRequest) ProtoMessage() {}

func (x *ListCategoriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_category_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCategoriesRequest.ProtoReflect.Descriptor instead.
func (*ListCategoriesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_category_service_proto_rawDescGZIP(), []int{0}
}

func (x *ListCategoriesRequest) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *ListCategoriesRequest) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *ListCategoriesRequest) GetFilter() *ListCategoriesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// (-- api-linter: core::0158::response-next-page-token-field=disabled
//
//	aip.dev/not-precedent: no need page --)
//
// list setting services response
type ListCategoriesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// category list
	Categories    []*ServiceCategory `protobuf:"bytes,1,rep,name=categories,proto3" json:"categories,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCategoriesResponse) Reset() {
	*x = ListCategoriesResponse{}
	mi := &file_backend_proto_offering_v1_service_category_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCategoriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCategoriesResponse) ProtoMessage() {}

func (x *ListCategoriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_category_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCategoriesResponse.ProtoReflect.Descriptor instead.
func (*ListCategoriesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_category_service_proto_rawDescGZIP(), []int{1}
}

func (x *ListCategoriesResponse) GetCategories() []*ServiceCategory {
	if x != nil {
		return x.Categories
	}
	return nil
}

// save categories request
type SaveCategoriesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The type of the organization (e.g., company, enterprise).
	OrganizationType v1.OrganizationType `protobuf:"varint,1,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// The ID of the organization this category belongs to.
	OrganizationId int64 `protobuf:"varint,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// create category list
	CreateCategories []*SaveCategoriesRequest_CategoryCreateDef `protobuf:"bytes,3,rep,name=create_categories,json=createCategories,proto3" json:"create_categories,omitempty"`
	// update category list
	UpdateCategories []*SaveCategoriesRequest_CategoryUpdateDef `protobuf:"bytes,4,rep,name=update_categories,json=updateCategories,proto3" json:"update_categories,omitempty"`
	// delete ids category
	DeleteIds     []int64 `protobuf:"varint,5,rep,packed,name=delete_ids,json=deleteIds,proto3" json:"delete_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaveCategoriesRequest) Reset() {
	*x = SaveCategoriesRequest{}
	mi := &file_backend_proto_offering_v1_service_category_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveCategoriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveCategoriesRequest) ProtoMessage() {}

func (x *SaveCategoriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_category_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveCategoriesRequest.ProtoReflect.Descriptor instead.
func (*SaveCategoriesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_category_service_proto_rawDescGZIP(), []int{2}
}

func (x *SaveCategoriesRequest) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *SaveCategoriesRequest) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *SaveCategoriesRequest) GetCreateCategories() []*SaveCategoriesRequest_CategoryCreateDef {
	if x != nil {
		return x.CreateCategories
	}
	return nil
}

func (x *SaveCategoriesRequest) GetUpdateCategories() []*SaveCategoriesRequest_CategoryUpdateDef {
	if x != nil {
		return x.UpdateCategories
	}
	return nil
}

func (x *SaveCategoriesRequest) GetDeleteIds() []int64 {
	if x != nil {
		return x.DeleteIds
	}
	return nil
}

// save categories response
type SaveCategoriesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaveCategoriesResponse) Reset() {
	*x = SaveCategoriesResponse{}
	mi := &file_backend_proto_offering_v1_service_category_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveCategoriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveCategoriesResponse) ProtoMessage() {}

func (x *SaveCategoriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_category_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveCategoriesResponse.ProtoReflect.Descriptor instead.
func (*SaveCategoriesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_category_service_proto_rawDescGZIP(), []int{3}
}

// list filter
type ListCategoriesRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// category ids
	CategoryIds []int64 `protobuf:"varint,2,rep,packed,name=category_ids,json=categoryIds,proto3" json:"category_ids,omitempty"`
	// care type ids
	CareTypeIds   []int64 `protobuf:"varint,3,rep,packed,name=care_type_ids,json=careTypeIds,proto3" json:"care_type_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCategoriesRequest_Filter) Reset() {
	*x = ListCategoriesRequest_Filter{}
	mi := &file_backend_proto_offering_v1_service_category_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCategoriesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCategoriesRequest_Filter) ProtoMessage() {}

func (x *ListCategoriesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_category_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCategoriesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListCategoriesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_category_service_proto_rawDescGZIP(), []int{0, 0}
}

func (x *ListCategoriesRequest_Filter) GetCategoryIds() []int64 {
	if x != nil {
		return x.CategoryIds
	}
	return nil
}

func (x *ListCategoriesRequest_Filter) GetCareTypeIds() []int64 {
	if x != nil {
		return x.CareTypeIds
	}
	return nil
}

// Defines the structure for a service category, used to organize services.
type SaveCategoriesRequest_CategoryUpdateDef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Primary key ID of the service category.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The ID of the care type associated with this category.
	CareTypeId int64 `protobuf:"varint,2,opt,name=care_type_id,json=careTypeId,proto3" json:"care_type_id,omitempty"`
	// The name of the service category, unique within the same organization and care type.
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// sort order of the category
	Sort          int64 `protobuf:"varint,4,opt,name=sort,proto3" json:"sort,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaveCategoriesRequest_CategoryUpdateDef) Reset() {
	*x = SaveCategoriesRequest_CategoryUpdateDef{}
	mi := &file_backend_proto_offering_v1_service_category_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveCategoriesRequest_CategoryUpdateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveCategoriesRequest_CategoryUpdateDef) ProtoMessage() {}

func (x *SaveCategoriesRequest_CategoryUpdateDef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_category_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveCategoriesRequest_CategoryUpdateDef.ProtoReflect.Descriptor instead.
func (*SaveCategoriesRequest_CategoryUpdateDef) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_category_service_proto_rawDescGZIP(), []int{2, 0}
}

func (x *SaveCategoriesRequest_CategoryUpdateDef) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SaveCategoriesRequest_CategoryUpdateDef) GetCareTypeId() int64 {
	if x != nil {
		return x.CareTypeId
	}
	return 0
}

func (x *SaveCategoriesRequest_CategoryUpdateDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SaveCategoriesRequest_CategoryUpdateDef) GetSort() int64 {
	if x != nil {
		return x.Sort
	}
	return 0
}

// Defines the structure for a service category, used to organize services.
type SaveCategoriesRequest_CategoryCreateDef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The ID of the care type associated with this category.
	CareTypeId int64 `protobuf:"varint,2,opt,name=care_type_id,json=careTypeId,proto3" json:"care_type_id,omitempty"`
	// The name of the service category, unique within the same organization and care type.
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// sort order of the category
	Sort          int64 `protobuf:"varint,4,opt,name=sort,proto3" json:"sort,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaveCategoriesRequest_CategoryCreateDef) Reset() {
	*x = SaveCategoriesRequest_CategoryCreateDef{}
	mi := &file_backend_proto_offering_v1_service_category_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveCategoriesRequest_CategoryCreateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveCategoriesRequest_CategoryCreateDef) ProtoMessage() {}

func (x *SaveCategoriesRequest_CategoryCreateDef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_category_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveCategoriesRequest_CategoryCreateDef.ProtoReflect.Descriptor instead.
func (*SaveCategoriesRequest_CategoryCreateDef) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_category_service_proto_rawDescGZIP(), []int{2, 1}
}

func (x *SaveCategoriesRequest_CategoryCreateDef) GetCareTypeId() int64 {
	if x != nil {
		return x.CareTypeId
	}
	return 0
}

func (x *SaveCategoriesRequest_CategoryCreateDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SaveCategoriesRequest_CategoryCreateDef) GetSort() int64 {
	if x != nil {
		return x.Sort
	}
	return 0
}

var File_backend_proto_offering_v1_service_category_service_proto protoreflect.FileDescriptor

const file_backend_proto_offering_v1_service_category_service_proto_rawDesc = "" +
	"\n" +
	"8backend/proto/offering/v1/service_category_service.proto\x12\x19backend.proto.offering.v1\x1a0backend/proto/offering/v1/service_category.proto\x1a0backend/proto/organization/v1/organization.proto\"\xc0\x02\n" +
	"\x15ListCategoriesRequest\x12\\\n" +
	"\x11organization_type\x18\x01 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeR\x10organizationType\x12'\n" +
	"\x0forganization_id\x18\x02 \x01(\x03R\x0eorganizationId\x12O\n" +
	"\x06filter\x18\x12 \x01(\v27.backend.proto.offering.v1.ListCategoriesRequest.FilterR\x06filter\x1aO\n" +
	"\x06Filter\x12!\n" +
	"\fcategory_ids\x18\x02 \x03(\x03R\vcategoryIds\x12\"\n" +
	"\rcare_type_ids\x18\x03 \x03(\x03R\vcareTypeIds\"d\n" +
	"\x16ListCategoriesResponse\x12J\n" +
	"\n" +
	"categories\x18\x01 \x03(\v2*.backend.proto.offering.v1.ServiceCategoryR\n" +
	"categories\"\xed\x04\n" +
	"\x15SaveCategoriesRequest\x12\\\n" +
	"\x11organization_type\x18\x01 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeR\x10organizationType\x12'\n" +
	"\x0forganization_id\x18\x02 \x01(\x03R\x0eorganizationId\x12o\n" +
	"\x11create_categories\x18\x03 \x03(\v2B.backend.proto.offering.v1.SaveCategoriesRequest.CategoryCreateDefR\x10createCategories\x12o\n" +
	"\x11update_categories\x18\x04 \x03(\v2B.backend.proto.offering.v1.SaveCategoriesRequest.CategoryUpdateDefR\x10updateCategories\x12\x1d\n" +
	"\n" +
	"delete_ids\x18\x05 \x03(\x03R\tdeleteIds\x1am\n" +
	"\x11CategoryUpdateDef\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12 \n" +
	"\fcare_type_id\x18\x02 \x01(\x03R\n" +
	"careTypeId\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x12\n" +
	"\x04sort\x18\x04 \x01(\x03R\x04sort\x1a]\n" +
	"\x11CategoryCreateDef\x12 \n" +
	"\fcare_type_id\x18\x02 \x01(\x03R\n" +
	"careTypeId\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x12\n" +
	"\x04sort\x18\x04 \x01(\x03R\x04sort\"\x18\n" +
	"\x16SaveCategoriesResponse2\x86\x02\n" +
	"\x16ServiceCategoryService\x12u\n" +
	"\x0eListCategories\x120.backend.proto.offering.v1.ListCategoriesRequest\x1a1.backend.proto.offering.v1.ListCategoriesResponse\x12u\n" +
	"\x0eSaveCategories\x120.backend.proto.offering.v1.SaveCategoriesRequest\x1a1.backend.proto.offering.v1.SaveCategoriesResponseBk\n" +
	"#com.moego.backend.proto.offering.v1P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpbb\x06proto3"

var (
	file_backend_proto_offering_v1_service_category_service_proto_rawDescOnce sync.Once
	file_backend_proto_offering_v1_service_category_service_proto_rawDescData []byte
)

func file_backend_proto_offering_v1_service_category_service_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_v1_service_category_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_v1_service_category_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_category_service_proto_rawDesc), len(file_backend_proto_offering_v1_service_category_service_proto_rawDesc)))
	})
	return file_backend_proto_offering_v1_service_category_service_proto_rawDescData
}

var file_backend_proto_offering_v1_service_category_service_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_backend_proto_offering_v1_service_category_service_proto_goTypes = []any{
	(*ListCategoriesRequest)(nil),                   // 0: backend.proto.offering.v1.ListCategoriesRequest
	(*ListCategoriesResponse)(nil),                  // 1: backend.proto.offering.v1.ListCategoriesResponse
	(*SaveCategoriesRequest)(nil),                   // 2: backend.proto.offering.v1.SaveCategoriesRequest
	(*SaveCategoriesResponse)(nil),                  // 3: backend.proto.offering.v1.SaveCategoriesResponse
	(*ListCategoriesRequest_Filter)(nil),            // 4: backend.proto.offering.v1.ListCategoriesRequest.Filter
	(*SaveCategoriesRequest_CategoryUpdateDef)(nil), // 5: backend.proto.offering.v1.SaveCategoriesRequest.CategoryUpdateDef
	(*SaveCategoriesRequest_CategoryCreateDef)(nil), // 6: backend.proto.offering.v1.SaveCategoriesRequest.CategoryCreateDef
	(v1.OrganizationType)(0),                        // 7: backend.proto.organization.v1.OrganizationType
	(*ServiceCategory)(nil),                         // 8: backend.proto.offering.v1.ServiceCategory
}
var file_backend_proto_offering_v1_service_category_service_proto_depIdxs = []int32{
	7, // 0: backend.proto.offering.v1.ListCategoriesRequest.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	4, // 1: backend.proto.offering.v1.ListCategoriesRequest.filter:type_name -> backend.proto.offering.v1.ListCategoriesRequest.Filter
	8, // 2: backend.proto.offering.v1.ListCategoriesResponse.categories:type_name -> backend.proto.offering.v1.ServiceCategory
	7, // 3: backend.proto.offering.v1.SaveCategoriesRequest.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	6, // 4: backend.proto.offering.v1.SaveCategoriesRequest.create_categories:type_name -> backend.proto.offering.v1.SaveCategoriesRequest.CategoryCreateDef
	5, // 5: backend.proto.offering.v1.SaveCategoriesRequest.update_categories:type_name -> backend.proto.offering.v1.SaveCategoriesRequest.CategoryUpdateDef
	0, // 6: backend.proto.offering.v1.ServiceCategoryService.ListCategories:input_type -> backend.proto.offering.v1.ListCategoriesRequest
	2, // 7: backend.proto.offering.v1.ServiceCategoryService.SaveCategories:input_type -> backend.proto.offering.v1.SaveCategoriesRequest
	1, // 8: backend.proto.offering.v1.ServiceCategoryService.ListCategories:output_type -> backend.proto.offering.v1.ListCategoriesResponse
	3, // 9: backend.proto.offering.v1.ServiceCategoryService.SaveCategories:output_type -> backend.proto.offering.v1.SaveCategoriesResponse
	8, // [8:10] is the sub-list for method output_type
	6, // [6:8] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_v1_service_category_service_proto_init() }
func file_backend_proto_offering_v1_service_category_service_proto_init() {
	if File_backend_proto_offering_v1_service_category_service_proto != nil {
		return
	}
	file_backend_proto_offering_v1_service_category_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_category_service_proto_rawDesc), len(file_backend_proto_offering_v1_service_category_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_offering_v1_service_category_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_v1_service_category_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_offering_v1_service_category_service_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_v1_service_category_service_proto = out.File
	file_backend_proto_offering_v1_service_category_service_proto_goTypes = nil
	file_backend_proto_offering_v1_service_category_service_proto_depIdxs = nil
}
