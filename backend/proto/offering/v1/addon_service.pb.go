// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.8
// 	protoc        (unknown)
// source: backend/proto/offering/v1/addon_service.proto

package offeringpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	v1 "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// (-- api-linter: core::0133::request-resource-field=disabled
//
//	aip.dev/not-precedent: 不复用 model 结构，单独定义 AddOnCreateDef 结构 --)
//
// 创建 AddOn 请求
type CreateAddOnRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// AddOn 创建配置
	AddOn         *AddOnCreateDef `protobuf:"bytes,1,opt,name=add_on,json=addOn,proto3" json:"add_on,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAddOnRequest) Reset() {
	*x = CreateAddOnRequest{}
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAddOnRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAddOnRequest) ProtoMessage() {}

func (x *CreateAddOnRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAddOnRequest.ProtoReflect.Descriptor instead.
func (*CreateAddOnRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_addon_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateAddOnRequest) GetAddOn() *AddOnCreateDef {
	if x != nil {
		return x.AddOn
	}
	return nil
}

// 创建 AddOn 响应
type CreateAddOnResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// AddOn ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAddOnResponse) Reset() {
	*x = CreateAddOnResponse{}
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAddOnResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAddOnResponse) ProtoMessage() {}

func (x *CreateAddOnResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAddOnResponse.ProtoReflect.Descriptor instead.
func (*CreateAddOnResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_addon_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateAddOnResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 获取 AddOn 请求
type GetAddOnRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// AddOn ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAddOnRequest) Reset() {
	*x = GetAddOnRequest{}
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAddOnRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAddOnRequest) ProtoMessage() {}

func (x *GetAddOnRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAddOnRequest.ProtoReflect.Descriptor instead.
func (*GetAddOnRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_addon_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetAddOnRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 获取 AddOn 响应
type GetAddOnResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// AddOn 信息
	AddOn         *AddOn `protobuf:"bytes,1,opt,name=add_on,json=addOn,proto3" json:"add_on,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAddOnResponse) Reset() {
	*x = GetAddOnResponse{}
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAddOnResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAddOnResponse) ProtoMessage() {}

func (x *GetAddOnResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAddOnResponse.ProtoReflect.Descriptor instead.
func (*GetAddOnResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_addon_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetAddOnResponse) GetAddOn() *AddOn {
	if x != nil {
		return x.AddOn
	}
	return nil
}

// 更新 AddOn 请求
type UpdateAddOnRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// AddOn 更新配置
	AddOn         *AddOnUpdateDef `protobuf:"bytes,1,opt,name=add_on,json=addOn,proto3" json:"add_on,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAddOnRequest) Reset() {
	*x = UpdateAddOnRequest{}
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAddOnRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAddOnRequest) ProtoMessage() {}

func (x *UpdateAddOnRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAddOnRequest.ProtoReflect.Descriptor instead.
func (*UpdateAddOnRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_addon_service_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateAddOnRequest) GetAddOn() *AddOnUpdateDef {
	if x != nil {
		return x.AddOn
	}
	return nil
}

// 更新 AddOn 响应
type UpdateAddOnResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAddOnResponse) Reset() {
	*x = UpdateAddOnResponse{}
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAddOnResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAddOnResponse) ProtoMessage() {}

func (x *UpdateAddOnResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAddOnResponse.ProtoReflect.Descriptor instead.
func (*UpdateAddOnResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_addon_service_proto_rawDescGZIP(), []int{5}
}

// 删除 AddOn 请求
type DeleteAddOnRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// AddOn ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteAddOnRequest) Reset() {
	*x = DeleteAddOnRequest{}
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAddOnRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAddOnRequest) ProtoMessage() {}

func (x *DeleteAddOnRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAddOnRequest.ProtoReflect.Descriptor instead.
func (*DeleteAddOnRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_addon_service_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteAddOnRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 删除 AddOn 响应
type DeleteAddOnResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteAddOnResponse) Reset() {
	*x = DeleteAddOnResponse{}
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAddOnResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAddOnResponse) ProtoMessage() {}

func (x *DeleteAddOnResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAddOnResponse.ProtoReflect.Descriptor instead.
func (*DeleteAddOnResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_addon_service_proto_rawDescGZIP(), []int{7}
}

// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0132::request-parent-required=disabled
//
//	aip.dev/not-precedent: 无 parent 语义 --)
//
// 列表查询 AddOn 请求
type ListAddOnsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 组织类型
	OrganizationType v1.OrganizationType `protobuf:"varint,1,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// 组织 ID
	OrganizationId int64 `protobuf:"varint,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// 过滤条件
	Filter *ListAddOnsRequest_Filter `protobuf:"bytes,3,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	// 分页信息
	Pagination    *PaginationRef `protobuf:"bytes,4,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAddOnsRequest) Reset() {
	*x = ListAddOnsRequest{}
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAddOnsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAddOnsRequest) ProtoMessage() {}

func (x *ListAddOnsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAddOnsRequest.ProtoReflect.Descriptor instead.
func (*ListAddOnsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_addon_service_proto_rawDescGZIP(), []int{8}
}

func (x *ListAddOnsRequest) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *ListAddOnsRequest) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *ListAddOnsRequest) GetFilter() *ListAddOnsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListAddOnsRequest) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// (-- api-linter: core::0158::response-next-page-token-field=disabled
//
//	aip.dev/not-precedent: 使用 PaginationRef 替代 --)
//
// (-- api-linter: core::0132::response-unknown-fields=disabled
//
//	aip.dev/not-precedent: 额外返回 total. --)
//
// 列表查询 AddOn 响应
type ListAddOnsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// AddOn 列表
	AddOns []*AddOn `protobuf:"bytes,1,rep,name=add_ons,json=addOns,proto3" json:"add_ons,omitempty"`
	// 分页信息
	Pagination *PaginationRef `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// 总数
	Total         int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAddOnsResponse) Reset() {
	*x = ListAddOnsResponse{}
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAddOnsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAddOnsResponse) ProtoMessage() {}

func (x *ListAddOnsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAddOnsResponse.ProtoReflect.Descriptor instead.
func (*ListAddOnsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_addon_service_proto_rawDescGZIP(), []int{9}
}

func (x *ListAddOnsResponse) GetAddOns() []*AddOn {
	if x != nil {
		return x.AddOns
	}
	return nil
}

func (x *ListAddOnsResponse) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListAddOnsResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// (-- api-linter: core::0234::request-unknown-fields=disabled
//
//	aip.dev/not-precedent: 需要租户信息 --)
//
// (-- api-linter: core::0234::request-requests-field=disabled
//
//	aip.dev/not-precedent: 不复用 UpdateRequest 结构 --)
//
// (-- api-linter: core::0234::request-parent-field=disabled
//
//	aip.dev/not-precedent: 无 parent 语义 --)
//
// 批量更新 AddOn 请求
type BatchUpdateAddOnsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 当前租户类型
	OrganizationType v1.OrganizationType `protobuf:"varint,1,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// 当前租户 ID
	OrganizationId int64 `protobuf:"varint,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// AddOn 更新配置列表
	UpdateAddOns  []*AddOnUpdateDef `protobuf:"bytes,3,rep,name=update_add_ons,json=updateAddOns,proto3" json:"update_add_ons,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchUpdateAddOnsRequest) Reset() {
	*x = BatchUpdateAddOnsRequest{}
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchUpdateAddOnsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateAddOnsRequest) ProtoMessage() {}

func (x *BatchUpdateAddOnsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateAddOnsRequest.ProtoReflect.Descriptor instead.
func (*BatchUpdateAddOnsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_addon_service_proto_rawDescGZIP(), []int{10}
}

func (x *BatchUpdateAddOnsRequest) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *BatchUpdateAddOnsRequest) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *BatchUpdateAddOnsRequest) GetUpdateAddOns() []*AddOnUpdateDef {
	if x != nil {
		return x.UpdateAddOns
	}
	return nil
}

// AddOn 创建配置定义
type AddOnCreateDef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 组织类型
	OrganizationType v1.OrganizationType `protobuf:"varint,1,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// 组织 ID
	OrganizationId int64 `protobuf:"varint,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// 是否必需员工
	IsRequiredStaff bool `protobuf:"varint,3,opt,name=is_required_staff,json=isRequiredStaff,proto3" json:"is_required_staff,omitempty"`
	// 分类 ID（可选）
	CategoryId *int64 `protobuf:"varint,4,opt,name=category_id,json=categoryId,proto3,oneof" json:"category_id,omitempty"`
	// AddOn 名称
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	// 描述（可选）
	Description *string `protobuf:"bytes,6,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// 颜色代码
	ColorCode string `protobuf:"bytes,7,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// 图片列表（可选）
	Images []string `protobuf:"bytes,8,rep,name=images,proto3" json:"images,omitempty"`
	// 状态
	Status AddOn_Status `protobuf:"varint,9,opt,name=status,proto3,enum=backend.proto.offering.v1.AddOn_Status" json:"status,omitempty"`
	// 来源
	Source OfferingSource `protobuf:"varint,10,opt,name=source,proto3,enum=backend.proto.offering.v1.OfferingSource" json:"source,omitempty"`
	// 可用业务范围
	AvailableBusiness *AvailableBusiness `protobuf:"bytes,11,opt,name=available_business,json=availableBusiness,proto3" json:"available_business,omitempty"`
	// 适用服务配置
	ApplicableService *ApplicableService `protobuf:"bytes,12,opt,name=applicable_service,json=applicableService,proto3" json:"applicable_service,omitempty"`
	// 时长。单位：分钟
	Duration      int32 `protobuf:"varint,13,opt,name=duration,proto3" json:"duration,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddOnCreateDef) Reset() {
	*x = AddOnCreateDef{}
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddOnCreateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddOnCreateDef) ProtoMessage() {}

func (x *AddOnCreateDef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddOnCreateDef.ProtoReflect.Descriptor instead.
func (*AddOnCreateDef) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_addon_service_proto_rawDescGZIP(), []int{11}
}

func (x *AddOnCreateDef) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *AddOnCreateDef) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *AddOnCreateDef) GetIsRequiredStaff() bool {
	if x != nil {
		return x.IsRequiredStaff
	}
	return false
}

func (x *AddOnCreateDef) GetCategoryId() int64 {
	if x != nil && x.CategoryId != nil {
		return *x.CategoryId
	}
	return 0
}

func (x *AddOnCreateDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AddOnCreateDef) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *AddOnCreateDef) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *AddOnCreateDef) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *AddOnCreateDef) GetStatus() AddOn_Status {
	if x != nil {
		return x.Status
	}
	return AddOn_STATUS_UNSPECIFIED
}

func (x *AddOnCreateDef) GetSource() OfferingSource {
	if x != nil {
		return x.Source
	}
	return OfferingSource_OFFERING_SOURCE_UNSPECIFIED
}

func (x *AddOnCreateDef) GetAvailableBusiness() *AvailableBusiness {
	if x != nil {
		return x.AvailableBusiness
	}
	return nil
}

func (x *AddOnCreateDef) GetApplicableService() *ApplicableService {
	if x != nil {
		return x.ApplicableService
	}
	return nil
}

func (x *AddOnCreateDef) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

// AddOn 更新配置定义
type AddOnUpdateDef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// AddOn ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 是否必需员工（可选）
	IsRequiredStaff *bool `protobuf:"varint,2,opt,name=is_required_staff,json=isRequiredStaff,proto3,oneof" json:"is_required_staff,omitempty"`
	// 分类 ID（可选）
	CategoryId *int64 `protobuf:"varint,3,opt,name=category_id,json=categoryId,proto3,oneof" json:"category_id,omitempty"`
	// AddOn 名称（可选）
	Name *string `protobuf:"bytes,4,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// 描述（可选）
	Description *string `protobuf:"bytes,5,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// 颜色代码（可选）
	ColorCode *string `protobuf:"bytes,6,opt,name=color_code,json=colorCode,proto3,oneof" json:"color_code,omitempty"`
	// 排序值（可选）
	Sort *int64 `protobuf:"varint,7,opt,name=sort,proto3,oneof" json:"sort,omitempty"`
	// 图片列表（可选）
	Images []string `protobuf:"bytes,8,rep,name=images,proto3" json:"images,omitempty"`
	// 状态（可选）
	Status *AddOn_Status `protobuf:"varint,9,opt,name=status,proto3,enum=backend.proto.offering.v1.AddOn_Status,oneof" json:"status,omitempty"`
	// 可用业务范围（可选）
	AvailableBusiness *AvailableBusiness `protobuf:"bytes,10,opt,name=available_business,json=availableBusiness,proto3,oneof" json:"available_business,omitempty"`
	// 适用服务配置（可选）
	ApplicableService *ApplicableService `protobuf:"bytes,11,opt,name=applicable_service,json=applicableService,proto3,oneof" json:"applicable_service,omitempty"`
	// 时长（可选）单位：分钟
	Duration      *int32 `protobuf:"varint,12,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddOnUpdateDef) Reset() {
	*x = AddOnUpdateDef{}
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddOnUpdateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddOnUpdateDef) ProtoMessage() {}

func (x *AddOnUpdateDef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddOnUpdateDef.ProtoReflect.Descriptor instead.
func (*AddOnUpdateDef) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_addon_service_proto_rawDescGZIP(), []int{12}
}

func (x *AddOnUpdateDef) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AddOnUpdateDef) GetIsRequiredStaff() bool {
	if x != nil && x.IsRequiredStaff != nil {
		return *x.IsRequiredStaff
	}
	return false
}

func (x *AddOnUpdateDef) GetCategoryId() int64 {
	if x != nil && x.CategoryId != nil {
		return *x.CategoryId
	}
	return 0
}

func (x *AddOnUpdateDef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *AddOnUpdateDef) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *AddOnUpdateDef) GetColorCode() string {
	if x != nil && x.ColorCode != nil {
		return *x.ColorCode
	}
	return ""
}

func (x *AddOnUpdateDef) GetSort() int64 {
	if x != nil && x.Sort != nil {
		return *x.Sort
	}
	return 0
}

func (x *AddOnUpdateDef) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *AddOnUpdateDef) GetStatus() AddOn_Status {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return AddOn_STATUS_UNSPECIFIED
}

func (x *AddOnUpdateDef) GetAvailableBusiness() *AvailableBusiness {
	if x != nil {
		return x.AvailableBusiness
	}
	return nil
}

func (x *AddOnUpdateDef) GetApplicableService() *ApplicableService {
	if x != nil {
		return x.ApplicableService
	}
	return nil
}

func (x *AddOnUpdateDef) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

// (-- api-linter: core::0234::response-resource-field=disabled
//
//	aip.dev/not-precedent: 不返回 AddOn 类型 --)
//
// 批量更新 AddOn 响应
type BatchUpdateAddOnsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchUpdateAddOnsResponse) Reset() {
	*x = BatchUpdateAddOnsResponse{}
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchUpdateAddOnsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateAddOnsResponse) ProtoMessage() {}

func (x *BatchUpdateAddOnsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateAddOnsResponse.ProtoReflect.Descriptor instead.
func (*BatchUpdateAddOnsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_addon_service_proto_rawDescGZIP(), []int{13}
}

// (-- api-linter: core::0132::request-parent-required=disabled
//
//	aip.dev/not-precedent: no need parent --)
//
// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: no need page --)
//
// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: no need page --)
//
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//
//	aip.dev/not-precedent: no need page --)
//
// 获取 AddOn 分类列表请求
type ListAddOnCategoriesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 租户类型
	OrganizationType v1.OrganizationType `protobuf:"varint,1,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// 租户 ID
	OrganizationId int64 `protobuf:"varint,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// 过滤条件
	Filter        *ListAddOnCategoriesRequest_Filter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAddOnCategoriesRequest) Reset() {
	*x = ListAddOnCategoriesRequest{}
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAddOnCategoriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAddOnCategoriesRequest) ProtoMessage() {}

func (x *ListAddOnCategoriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAddOnCategoriesRequest.ProtoReflect.Descriptor instead.
func (*ListAddOnCategoriesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_addon_service_proto_rawDescGZIP(), []int{14}
}

func (x *ListAddOnCategoriesRequest) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *ListAddOnCategoriesRequest) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *ListAddOnCategoriesRequest) GetFilter() *ListAddOnCategoriesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// (-- api-linter: core::0132::response-unknown-fields=disabled
//
//	aip.dev/not-precedent: 命名简化 --)
//
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//
//	aip.dev/not-precedent: no need page --)
//
// 获取 AddOn 分类列表响应
type ListAddOnCategoriesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 分类列表
	Categories    []*AddOnCategory `protobuf:"bytes,1,rep,name=categories,proto3" json:"categories,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAddOnCategoriesResponse) Reset() {
	*x = ListAddOnCategoriesResponse{}
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAddOnCategoriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAddOnCategoriesResponse) ProtoMessage() {}

func (x *ListAddOnCategoriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAddOnCategoriesResponse.ProtoReflect.Descriptor instead.
func (*ListAddOnCategoriesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_addon_service_proto_rawDescGZIP(), []int{15}
}

func (x *ListAddOnCategoriesResponse) GetCategories() []*AddOnCategory {
	if x != nil {
		return x.Categories
	}
	return nil
}

// 保存 AddOn 分类请求
type SaveAddOnCategoriesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 租户类型
	OrganizationType v1.OrganizationType `protobuf:"varint,1,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// 租户 ID
	OrganizationId int64 `protobuf:"varint,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// 新增的分类列表
	CreateCategories []*SaveAddOnCategoriesRequest_CategoryCreateDef `protobuf:"bytes,3,rep,name=create_categories,json=createCategories,proto3" json:"create_categories,omitempty"`
	// 更新的分类列表
	UpdateCategories []*SaveAddOnCategoriesRequest_CategoryUpdateDef `protobuf:"bytes,4,rep,name=update_categories,json=updateCategories,proto3" json:"update_categories,omitempty"`
	// 删除的分类 ID 列表
	DeleteIds     []int64 `protobuf:"varint,5,rep,packed,name=delete_ids,json=deleteIds,proto3" json:"delete_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaveAddOnCategoriesRequest) Reset() {
	*x = SaveAddOnCategoriesRequest{}
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveAddOnCategoriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAddOnCategoriesRequest) ProtoMessage() {}

func (x *SaveAddOnCategoriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAddOnCategoriesRequest.ProtoReflect.Descriptor instead.
func (*SaveAddOnCategoriesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_addon_service_proto_rawDescGZIP(), []int{16}
}

func (x *SaveAddOnCategoriesRequest) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *SaveAddOnCategoriesRequest) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *SaveAddOnCategoriesRequest) GetCreateCategories() []*SaveAddOnCategoriesRequest_CategoryCreateDef {
	if x != nil {
		return x.CreateCategories
	}
	return nil
}

func (x *SaveAddOnCategoriesRequest) GetUpdateCategories() []*SaveAddOnCategoriesRequest_CategoryUpdateDef {
	if x != nil {
		return x.UpdateCategories
	}
	return nil
}

func (x *SaveAddOnCategoriesRequest) GetDeleteIds() []int64 {
	if x != nil {
		return x.DeleteIds
	}
	return nil
}

// 保存 AddOn 分类响应
type SaveAddOnCategoriesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaveAddOnCategoriesResponse) Reset() {
	*x = SaveAddOnCategoriesResponse{}
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveAddOnCategoriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAddOnCategoriesResponse) ProtoMessage() {}

func (x *SaveAddOnCategoriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAddOnCategoriesResponse.ProtoReflect.Descriptor instead.
func (*SaveAddOnCategoriesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_addon_service_proto_rawDescGZIP(), []int{17}
}

// 过滤条件
type ListAddOnsRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 分类 ID 列表
	CategoryIds []int64 `protobuf:"varint,1,rep,packed,name=category_ids,json=categoryIds,proto3" json:"category_ids,omitempty"`
	// 状态过滤
	Statuses []AddOn_Status `protobuf:"varint,3,rep,packed,name=statuses,proto3,enum=backend.proto.offering.v1.AddOn_Status" json:"statuses,omitempty"`
	// 来源过滤
	Sources []OfferingSource `protobuf:"varint,4,rep,packed,name=sources,proto3,enum=backend.proto.offering.v1.OfferingSource" json:"sources,omitempty"`
	// 关键词搜索（名称）
	Keyword       *string `protobuf:"bytes,6,opt,name=keyword,proto3,oneof" json:"keyword,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAddOnsRequest_Filter) Reset() {
	*x = ListAddOnsRequest_Filter{}
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAddOnsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAddOnsRequest_Filter) ProtoMessage() {}

func (x *ListAddOnsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAddOnsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListAddOnsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_addon_service_proto_rawDescGZIP(), []int{8, 0}
}

func (x *ListAddOnsRequest_Filter) GetCategoryIds() []int64 {
	if x != nil {
		return x.CategoryIds
	}
	return nil
}

func (x *ListAddOnsRequest_Filter) GetStatuses() []AddOn_Status {
	if x != nil {
		return x.Statuses
	}
	return nil
}

func (x *ListAddOnsRequest_Filter) GetSources() []OfferingSource {
	if x != nil {
		return x.Sources
	}
	return nil
}

func (x *ListAddOnsRequest_Filter) GetKeyword() string {
	if x != nil && x.Keyword != nil {
		return *x.Keyword
	}
	return ""
}

// 过滤条件
type ListAddOnCategoriesRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 分类 ID 列表
	CategoryIds   []int64 `protobuf:"varint,1,rep,packed,name=category_ids,json=categoryIds,proto3" json:"category_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAddOnCategoriesRequest_Filter) Reset() {
	*x = ListAddOnCategoriesRequest_Filter{}
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAddOnCategoriesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAddOnCategoriesRequest_Filter) ProtoMessage() {}

func (x *ListAddOnCategoriesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAddOnCategoriesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListAddOnCategoriesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_addon_service_proto_rawDescGZIP(), []int{14, 0}
}

func (x *ListAddOnCategoriesRequest_Filter) GetCategoryIds() []int64 {
	if x != nil {
		return x.CategoryIds
	}
	return nil
}

// 更新分类请求
type SaveAddOnCategoriesRequest_CategoryUpdateDef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 分类 ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 分类名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 排序值
	Sort          int64 `protobuf:"varint,3,opt,name=sort,proto3" json:"sort,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaveAddOnCategoriesRequest_CategoryUpdateDef) Reset() {
	*x = SaveAddOnCategoriesRequest_CategoryUpdateDef{}
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveAddOnCategoriesRequest_CategoryUpdateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAddOnCategoriesRequest_CategoryUpdateDef) ProtoMessage() {}

func (x *SaveAddOnCategoriesRequest_CategoryUpdateDef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAddOnCategoriesRequest_CategoryUpdateDef.ProtoReflect.Descriptor instead.
func (*SaveAddOnCategoriesRequest_CategoryUpdateDef) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_addon_service_proto_rawDescGZIP(), []int{16, 0}
}

func (x *SaveAddOnCategoriesRequest_CategoryUpdateDef) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SaveAddOnCategoriesRequest_CategoryUpdateDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SaveAddOnCategoriesRequest_CategoryUpdateDef) GetSort() int64 {
	if x != nil {
		return x.Sort
	}
	return 0
}

// 创建分类请求
type SaveAddOnCategoriesRequest_CategoryCreateDef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 分类名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 排序值
	Sort          int64 `protobuf:"varint,2,opt,name=sort,proto3" json:"sort,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaveAddOnCategoriesRequest_CategoryCreateDef) Reset() {
	*x = SaveAddOnCategoriesRequest_CategoryCreateDef{}
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveAddOnCategoriesRequest_CategoryCreateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAddOnCategoriesRequest_CategoryCreateDef) ProtoMessage() {}

func (x *SaveAddOnCategoriesRequest_CategoryCreateDef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_addon_service_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAddOnCategoriesRequest_CategoryCreateDef.ProtoReflect.Descriptor instead.
func (*SaveAddOnCategoriesRequest_CategoryCreateDef) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_addon_service_proto_rawDescGZIP(), []int{16, 1}
}

func (x *SaveAddOnCategoriesRequest_CategoryCreateDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SaveAddOnCategoriesRequest_CategoryCreateDef) GetSort() int64 {
	if x != nil {
		return x.Sort
	}
	return 0
}

var File_backend_proto_offering_v1_addon_service_proto protoreflect.FileDescriptor

const file_backend_proto_offering_v1_addon_service_proto_rawDesc = "" +
	"\n" +
	"-backend/proto/offering/v1/addon_service.proto\x12\x19backend.proto.offering.v1\x1a&backend/proto/offering/v1/common.proto\x1a%backend/proto/offering/v1/addon.proto\x1a'backend/proto/offering/v1/service.proto\x1a0backend/proto/organization/v1/organization.proto\x1a\x1bbuf/validate/validate.proto\"^\n" +
	"\x12CreateAddOnRequest\x12H\n" +
	"\x06add_on\x18\x01 \x01(\v2).backend.proto.offering.v1.AddOnCreateDefB\x06\xbaH\x03\xc8\x01\x01R\x05addOn\"%\n" +
	"\x13CreateAddOnResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"*\n" +
	"\x0fGetAddOnRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"K\n" +
	"\x10GetAddOnResponse\x127\n" +
	"\x06add_on\x18\x01 \x01(\v2 .backend.proto.offering.v1.AddOnR\x05addOn\"^\n" +
	"\x12UpdateAddOnRequest\x12H\n" +
	"\x06add_on\x18\x01 \x01(\v2).backend.proto.offering.v1.AddOnUpdateDefB\x06\xbaH\x03\xc8\x01\x01R\x05addOn\"\x15\n" +
	"\x13UpdateAddOnResponse\"-\n" +
	"\x12DeleteAddOnRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"\x15\n" +
	"\x13DeleteAddOnResponse\"\x84\x05\n" +
	"\x11ListAddOnsRequest\x12h\n" +
	"\x11organization_type\x18\x01 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeB\n" +
	"\xbaH\a\x82\x01\x04\x10\x01 \x00R\x10organizationType\x120\n" +
	"\x0forganization_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x0eorganizationId\x12P\n" +
	"\x06filter\x18\x03 \x01(\v23.backend.proto.offering.v1.ListAddOnsRequest.FilterH\x00R\x06filter\x88\x01\x01\x12H\n" +
	"\n" +
	"pagination\x18\x04 \x01(\v2(.backend.proto.offering.v1.PaginationRefR\n" +
	"pagination\x1a\xab\x02\n" +
	"\x06Filter\x125\n" +
	"\fcategory_ids\x18\x01 \x03(\x03B\x12\xbaH\x0f\x92\x01\f\b\x00\x10d\x18\x01\"\x04\"\x02 \x00R\vcategoryIds\x12Z\n" +
	"\bstatuses\x18\x03 \x03(\x0e2'.backend.proto.offering.v1.AddOn.StatusB\x15\xbaH\x12\x92\x01\x0f\b\x00\x10\n" +
	"\x18\x01\"\a\x82\x01\x04\x10\x01 \x00R\bstatuses\x12Z\n" +
	"\asources\x18\x04 \x03(\x0e2).backend.proto.offering.v1.OfferingSourceB\x15\xbaH\x12\x92\x01\x0f\b\x00\x10\n" +
	"\x18\x01\"\a\x82\x01\x04\x10\x01 \x00R\asources\x12&\n" +
	"\akeyword\x18\x06 \x01(\tB\a\xbaH\x04r\x02\x18dH\x00R\akeyword\x88\x01\x01B\n" +
	"\n" +
	"\b_keywordB\t\n" +
	"\a_filter\"\xc3\x01\n" +
	"\x12ListAddOnsResponse\x129\n" +
	"\aadd_ons\x18\x01 \x03(\v2 .backend.proto.offering.v1.AddOnR\x06addOns\x12M\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2(.backend.proto.offering.v1.PaginationRefH\x00R\n" +
	"pagination\x88\x01\x01\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05totalB\r\n" +
	"\v_pagination\"\x94\x02\n" +
	"\x18BatchUpdateAddOnsRequest\x12h\n" +
	"\x11organization_type\x18\x01 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeB\n" +
	"\xbaH\a\x82\x01\x04\x10\x01 \x00R\x10organizationType\x120\n" +
	"\x0forganization_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x0eorganizationId\x12\\\n" +
	"\x0eupdate_add_ons\x18\x03 \x03(\v2).backend.proto.offering.v1.AddOnUpdateDefB\v\xbaH\b\x92\x01\x05\b\x01\x10\xe8\aR\fupdateAddOns\"\xec\x06\n" +
	"\x0eAddOnCreateDef\x12k\n" +
	"\x11organization_type\x18\x01 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeB\r\xbaH\n" +
	"\xc8\x01\x01\x82\x01\x04\x10\x01 \x00R\x10organizationType\x123\n" +
	"\x0forganization_id\x18\x02 \x01(\x03B\n" +
	"\xbaH\a\xc8\x01\x01\"\x02 \x00R\x0eorganizationId\x12*\n" +
	"\x11is_required_staff\x18\x03 \x01(\bR\x0fisRequiredStaff\x12-\n" +
	"\vcategory_id\x18\x04 \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x00R\n" +
	"categoryId\x88\x01\x01\x12 \n" +
	"\x04name\x18\x05 \x01(\tB\f\xbaH\t\xc8\x01\x01r\x04\x10\x01\x18dR\x04name\x12/\n" +
	"\vdescription\x18\x06 \x01(\tB\b\xbaH\x05r\x03\x18\xe8\aH\x01R\vdescription\x88\x01\x01\x12+\n" +
	"\n" +
	"color_code\x18\a \x01(\tB\f\xbaH\t\xc8\x01\x01r\x04\x10\x01\x18\n" +
	"R\tcolorCode\x12,\n" +
	"\x06images\x18\b \x03(\tB\x14\xbaH\x11\x92\x01\x0e\b\x00\x10\xe8\a\x18\x01\"\x05r\x03\x18\x80\bR\x06images\x12N\n" +
	"\x06status\x18\t \x01(\x0e2'.backend.proto.offering.v1.AddOn.StatusB\r\xbaH\n" +
	"\xc8\x01\x01\x82\x01\x04\x10\x01 \x00R\x06status\x12P\n" +
	"\x06source\x18\n" +
	" \x01(\x0e2).backend.proto.offering.v1.OfferingSourceB\r\xbaH\n" +
	"\xc8\x01\x01\x82\x01\x04\x10\x01 \x00R\x06source\x12c\n" +
	"\x12available_business\x18\v \x01(\v2,.backend.proto.offering.v1.AvailableBusinessB\x06\xbaH\x03\xc8\x01\x01R\x11availableBusiness\x12c\n" +
	"\x12applicable_service\x18\f \x01(\v2,.backend.proto.offering.v1.ApplicableServiceB\x06\xbaH\x03\xc8\x01\x01R\x11applicableService\x12#\n" +
	"\bduration\x18\r \x01(\x05B\a\xbaH\x04\x1a\x02 \x00R\bdurationB\x0e\n" +
	"\f_category_idB\x0e\n" +
	"\f_description\"\xba\x06\n" +
	"\x0eAddOnUpdateDef\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\x12/\n" +
	"\x11is_required_staff\x18\x02 \x01(\bH\x00R\x0fisRequiredStaff\x88\x01\x01\x12-\n" +
	"\vcategory_id\x18\x03 \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x01R\n" +
	"categoryId\x88\x01\x01\x12\"\n" +
	"\x04name\x18\x04 \x01(\tB\t\xbaH\x06r\x04\x10\x01\x18dH\x02R\x04name\x88\x01\x01\x12/\n" +
	"\vdescription\x18\x05 \x01(\tB\b\xbaH\x05r\x03\x18\xe8\aH\x03R\vdescription\x88\x01\x01\x12-\n" +
	"\n" +
	"color_code\x18\x06 \x01(\tB\t\xbaH\x06r\x04\x10\x01\x18\n" +
	"H\x04R\tcolorCode\x88\x01\x01\x12 \n" +
	"\x04sort\x18\a \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x05R\x04sort\x88\x01\x01\x12,\n" +
	"\x06images\x18\b \x03(\tB\x14\xbaH\x11\x92\x01\x0e\b\x00\x10\xe8\a\x18\x01\"\x05r\x03\x18\x80\bR\x06images\x12P\n" +
	"\x06status\x18\t \x01(\x0e2'.backend.proto.offering.v1.AddOn.StatusB\n" +
	"\xbaH\a\x82\x01\x04\x10\x01 \x00H\x06R\x06status\x88\x01\x01\x12`\n" +
	"\x12available_business\x18\n" +
	" \x01(\v2,.backend.proto.offering.v1.AvailableBusinessH\aR\x11availableBusiness\x88\x01\x01\x12`\n" +
	"\x12applicable_service\x18\v \x01(\v2,.backend.proto.offering.v1.ApplicableServiceH\bR\x11applicableService\x88\x01\x01\x12(\n" +
	"\bduration\x18\f \x01(\x05B\a\xbaH\x04\x1a\x02 \x00H\tR\bduration\x88\x01\x01B\x14\n" +
	"\x12_is_required_staffB\x0e\n" +
	"\f_category_idB\a\n" +
	"\x05_nameB\x0e\n" +
	"\f_descriptionB\r\n" +
	"\v_color_codeB\a\n" +
	"\x05_sortB\t\n" +
	"\a_statusB\x15\n" +
	"\x13_available_businessB\x15\n" +
	"\x13_applicable_serviceB\v\n" +
	"\t_duration\"\x1b\n" +
	"\x19BatchUpdateAddOnsResponse\"\xa6\x02\n" +
	"\x1aListAddOnCategoriesRequest\x12\\\n" +
	"\x11organization_type\x18\x01 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeR\x10organizationType\x12'\n" +
	"\x0forganization_id\x18\x02 \x01(\x03R\x0eorganizationId\x12T\n" +
	"\x06filter\x18\x03 \x01(\v2<.backend.proto.offering.v1.ListAddOnCategoriesRequest.FilterR\x06filter\x1a+\n" +
	"\x06Filter\x12!\n" +
	"\fcategory_ids\x18\x01 \x03(\x03R\vcategoryIds\"g\n" +
	"\x1bListAddOnCategoriesResponse\x12H\n" +
	"\n" +
	"categories\x18\x01 \x03(\v2(.backend.proto.offering.v1.AddOnCategoryR\n" +
	"categories\"\xb8\x04\n" +
	"\x1aSaveAddOnCategoriesRequest\x12\\\n" +
	"\x11organization_type\x18\x01 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeR\x10organizationType\x12'\n" +
	"\x0forganization_id\x18\x02 \x01(\x03R\x0eorganizationId\x12t\n" +
	"\x11create_categories\x18\x03 \x03(\v2G.backend.proto.offering.v1.SaveAddOnCategoriesRequest.CategoryCreateDefR\x10createCategories\x12t\n" +
	"\x11update_categories\x18\x04 \x03(\v2G.backend.proto.offering.v1.SaveAddOnCategoriesRequest.CategoryUpdateDefR\x10updateCategories\x12\x1d\n" +
	"\n" +
	"delete_ids\x18\x05 \x03(\x03R\tdeleteIds\x1aK\n" +
	"\x11CategoryUpdateDef\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x12\n" +
	"\x04sort\x18\x03 \x01(\x03R\x04sort\x1a;\n" +
	"\x11CategoryCreateDef\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x12\n" +
	"\x04sort\x18\x02 \x01(\x03R\x04sort\"\x1d\n" +
	"\x1bSaveAddOnCategoriesResponse2\xb6\a\n" +
	"\fAddOnService\x12l\n" +
	"\vCreateAddOn\x12-.backend.proto.offering.v1.CreateAddOnRequest\x1a..backend.proto.offering.v1.CreateAddOnResponse\x12c\n" +
	"\bGetAddOn\x12*.backend.proto.offering.v1.GetAddOnRequest\x1a+.backend.proto.offering.v1.GetAddOnResponse\x12l\n" +
	"\vUpdateAddOn\x12-.backend.proto.offering.v1.UpdateAddOnRequest\x1a..backend.proto.offering.v1.UpdateAddOnResponse\x12l\n" +
	"\vDeleteAddOn\x12-.backend.proto.offering.v1.DeleteAddOnRequest\x1a..backend.proto.offering.v1.DeleteAddOnResponse\x12i\n" +
	"\n" +
	"ListAddOns\x12,.backend.proto.offering.v1.ListAddOnsRequest\x1a-.backend.proto.offering.v1.ListAddOnsResponse\x12~\n" +
	"\x11BatchUpdateAddOns\x123.backend.proto.offering.v1.BatchUpdateAddOnsRequest\x1a4.backend.proto.offering.v1.BatchUpdateAddOnsResponse\x12\x84\x01\n" +
	"\x13ListAddOnCategories\x125.backend.proto.offering.v1.ListAddOnCategoriesRequest\x1a6.backend.proto.offering.v1.ListAddOnCategoriesResponse\x12\x84\x01\n" +
	"\x13SaveAddOnCategories\x125.backend.proto.offering.v1.SaveAddOnCategoriesRequest\x1a6.backend.proto.offering.v1.SaveAddOnCategoriesResponseBk\n" +
	"#com.moego.backend.proto.offering.v1P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpbb\x06proto3"

var (
	file_backend_proto_offering_v1_addon_service_proto_rawDescOnce sync.Once
	file_backend_proto_offering_v1_addon_service_proto_rawDescData []byte
)

func file_backend_proto_offering_v1_addon_service_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_v1_addon_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_v1_addon_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_addon_service_proto_rawDesc), len(file_backend_proto_offering_v1_addon_service_proto_rawDesc)))
	})
	return file_backend_proto_offering_v1_addon_service_proto_rawDescData
}

var file_backend_proto_offering_v1_addon_service_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_backend_proto_offering_v1_addon_service_proto_goTypes = []any{
	(*CreateAddOnRequest)(nil),                           // 0: backend.proto.offering.v1.CreateAddOnRequest
	(*CreateAddOnResponse)(nil),                          // 1: backend.proto.offering.v1.CreateAddOnResponse
	(*GetAddOnRequest)(nil),                              // 2: backend.proto.offering.v1.GetAddOnRequest
	(*GetAddOnResponse)(nil),                             // 3: backend.proto.offering.v1.GetAddOnResponse
	(*UpdateAddOnRequest)(nil),                           // 4: backend.proto.offering.v1.UpdateAddOnRequest
	(*UpdateAddOnResponse)(nil),                          // 5: backend.proto.offering.v1.UpdateAddOnResponse
	(*DeleteAddOnRequest)(nil),                           // 6: backend.proto.offering.v1.DeleteAddOnRequest
	(*DeleteAddOnResponse)(nil),                          // 7: backend.proto.offering.v1.DeleteAddOnResponse
	(*ListAddOnsRequest)(nil),                            // 8: backend.proto.offering.v1.ListAddOnsRequest
	(*ListAddOnsResponse)(nil),                           // 9: backend.proto.offering.v1.ListAddOnsResponse
	(*BatchUpdateAddOnsRequest)(nil),                     // 10: backend.proto.offering.v1.BatchUpdateAddOnsRequest
	(*AddOnCreateDef)(nil),                               // 11: backend.proto.offering.v1.AddOnCreateDef
	(*AddOnUpdateDef)(nil),                               // 12: backend.proto.offering.v1.AddOnUpdateDef
	(*BatchUpdateAddOnsResponse)(nil),                    // 13: backend.proto.offering.v1.BatchUpdateAddOnsResponse
	(*ListAddOnCategoriesRequest)(nil),                   // 14: backend.proto.offering.v1.ListAddOnCategoriesRequest
	(*ListAddOnCategoriesResponse)(nil),                  // 15: backend.proto.offering.v1.ListAddOnCategoriesResponse
	(*SaveAddOnCategoriesRequest)(nil),                   // 16: backend.proto.offering.v1.SaveAddOnCategoriesRequest
	(*SaveAddOnCategoriesResponse)(nil),                  // 17: backend.proto.offering.v1.SaveAddOnCategoriesResponse
	(*ListAddOnsRequest_Filter)(nil),                     // 18: backend.proto.offering.v1.ListAddOnsRequest.Filter
	(*ListAddOnCategoriesRequest_Filter)(nil),            // 19: backend.proto.offering.v1.ListAddOnCategoriesRequest.Filter
	(*SaveAddOnCategoriesRequest_CategoryUpdateDef)(nil), // 20: backend.proto.offering.v1.SaveAddOnCategoriesRequest.CategoryUpdateDef
	(*SaveAddOnCategoriesRequest_CategoryCreateDef)(nil), // 21: backend.proto.offering.v1.SaveAddOnCategoriesRequest.CategoryCreateDef
	(*AddOn)(nil),                                        // 22: backend.proto.offering.v1.AddOn
	(v1.OrganizationType)(0),                             // 23: backend.proto.organization.v1.OrganizationType
	(*PaginationRef)(nil),                                // 24: backend.proto.offering.v1.PaginationRef
	(AddOn_Status)(0),                                    // 25: backend.proto.offering.v1.AddOn.Status
	(OfferingSource)(0),                                  // 26: backend.proto.offering.v1.OfferingSource
	(*AvailableBusiness)(nil),                            // 27: backend.proto.offering.v1.AvailableBusiness
	(*ApplicableService)(nil),                            // 28: backend.proto.offering.v1.ApplicableService
	(*AddOnCategory)(nil),                                // 29: backend.proto.offering.v1.AddOnCategory
}
var file_backend_proto_offering_v1_addon_service_proto_depIdxs = []int32{
	11, // 0: backend.proto.offering.v1.CreateAddOnRequest.add_on:type_name -> backend.proto.offering.v1.AddOnCreateDef
	22, // 1: backend.proto.offering.v1.GetAddOnResponse.add_on:type_name -> backend.proto.offering.v1.AddOn
	12, // 2: backend.proto.offering.v1.UpdateAddOnRequest.add_on:type_name -> backend.proto.offering.v1.AddOnUpdateDef
	23, // 3: backend.proto.offering.v1.ListAddOnsRequest.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	18, // 4: backend.proto.offering.v1.ListAddOnsRequest.filter:type_name -> backend.proto.offering.v1.ListAddOnsRequest.Filter
	24, // 5: backend.proto.offering.v1.ListAddOnsRequest.pagination:type_name -> backend.proto.offering.v1.PaginationRef
	22, // 6: backend.proto.offering.v1.ListAddOnsResponse.add_ons:type_name -> backend.proto.offering.v1.AddOn
	24, // 7: backend.proto.offering.v1.ListAddOnsResponse.pagination:type_name -> backend.proto.offering.v1.PaginationRef
	23, // 8: backend.proto.offering.v1.BatchUpdateAddOnsRequest.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	12, // 9: backend.proto.offering.v1.BatchUpdateAddOnsRequest.update_add_ons:type_name -> backend.proto.offering.v1.AddOnUpdateDef
	23, // 10: backend.proto.offering.v1.AddOnCreateDef.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	25, // 11: backend.proto.offering.v1.AddOnCreateDef.status:type_name -> backend.proto.offering.v1.AddOn.Status
	26, // 12: backend.proto.offering.v1.AddOnCreateDef.source:type_name -> backend.proto.offering.v1.OfferingSource
	27, // 13: backend.proto.offering.v1.AddOnCreateDef.available_business:type_name -> backend.proto.offering.v1.AvailableBusiness
	28, // 14: backend.proto.offering.v1.AddOnCreateDef.applicable_service:type_name -> backend.proto.offering.v1.ApplicableService
	25, // 15: backend.proto.offering.v1.AddOnUpdateDef.status:type_name -> backend.proto.offering.v1.AddOn.Status
	27, // 16: backend.proto.offering.v1.AddOnUpdateDef.available_business:type_name -> backend.proto.offering.v1.AvailableBusiness
	28, // 17: backend.proto.offering.v1.AddOnUpdateDef.applicable_service:type_name -> backend.proto.offering.v1.ApplicableService
	23, // 18: backend.proto.offering.v1.ListAddOnCategoriesRequest.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	19, // 19: backend.proto.offering.v1.ListAddOnCategoriesRequest.filter:type_name -> backend.proto.offering.v1.ListAddOnCategoriesRequest.Filter
	29, // 20: backend.proto.offering.v1.ListAddOnCategoriesResponse.categories:type_name -> backend.proto.offering.v1.AddOnCategory
	23, // 21: backend.proto.offering.v1.SaveAddOnCategoriesRequest.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	21, // 22: backend.proto.offering.v1.SaveAddOnCategoriesRequest.create_categories:type_name -> backend.proto.offering.v1.SaveAddOnCategoriesRequest.CategoryCreateDef
	20, // 23: backend.proto.offering.v1.SaveAddOnCategoriesRequest.update_categories:type_name -> backend.proto.offering.v1.SaveAddOnCategoriesRequest.CategoryUpdateDef
	25, // 24: backend.proto.offering.v1.ListAddOnsRequest.Filter.statuses:type_name -> backend.proto.offering.v1.AddOn.Status
	26, // 25: backend.proto.offering.v1.ListAddOnsRequest.Filter.sources:type_name -> backend.proto.offering.v1.OfferingSource
	0,  // 26: backend.proto.offering.v1.AddOnService.CreateAddOn:input_type -> backend.proto.offering.v1.CreateAddOnRequest
	2,  // 27: backend.proto.offering.v1.AddOnService.GetAddOn:input_type -> backend.proto.offering.v1.GetAddOnRequest
	4,  // 28: backend.proto.offering.v1.AddOnService.UpdateAddOn:input_type -> backend.proto.offering.v1.UpdateAddOnRequest
	6,  // 29: backend.proto.offering.v1.AddOnService.DeleteAddOn:input_type -> backend.proto.offering.v1.DeleteAddOnRequest
	8,  // 30: backend.proto.offering.v1.AddOnService.ListAddOns:input_type -> backend.proto.offering.v1.ListAddOnsRequest
	10, // 31: backend.proto.offering.v1.AddOnService.BatchUpdateAddOns:input_type -> backend.proto.offering.v1.BatchUpdateAddOnsRequest
	14, // 32: backend.proto.offering.v1.AddOnService.ListAddOnCategories:input_type -> backend.proto.offering.v1.ListAddOnCategoriesRequest
	16, // 33: backend.proto.offering.v1.AddOnService.SaveAddOnCategories:input_type -> backend.proto.offering.v1.SaveAddOnCategoriesRequest
	1,  // 34: backend.proto.offering.v1.AddOnService.CreateAddOn:output_type -> backend.proto.offering.v1.CreateAddOnResponse
	3,  // 35: backend.proto.offering.v1.AddOnService.GetAddOn:output_type -> backend.proto.offering.v1.GetAddOnResponse
	5,  // 36: backend.proto.offering.v1.AddOnService.UpdateAddOn:output_type -> backend.proto.offering.v1.UpdateAddOnResponse
	7,  // 37: backend.proto.offering.v1.AddOnService.DeleteAddOn:output_type -> backend.proto.offering.v1.DeleteAddOnResponse
	9,  // 38: backend.proto.offering.v1.AddOnService.ListAddOns:output_type -> backend.proto.offering.v1.ListAddOnsResponse
	13, // 39: backend.proto.offering.v1.AddOnService.BatchUpdateAddOns:output_type -> backend.proto.offering.v1.BatchUpdateAddOnsResponse
	15, // 40: backend.proto.offering.v1.AddOnService.ListAddOnCategories:output_type -> backend.proto.offering.v1.ListAddOnCategoriesResponse
	17, // 41: backend.proto.offering.v1.AddOnService.SaveAddOnCategories:output_type -> backend.proto.offering.v1.SaveAddOnCategoriesResponse
	34, // [34:42] is the sub-list for method output_type
	26, // [26:34] is the sub-list for method input_type
	26, // [26:26] is the sub-list for extension type_name
	26, // [26:26] is the sub-list for extension extendee
	0,  // [0:26] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_v1_addon_service_proto_init() }
func file_backend_proto_offering_v1_addon_service_proto_init() {
	if File_backend_proto_offering_v1_addon_service_proto != nil {
		return
	}
	file_backend_proto_offering_v1_common_proto_init()
	file_backend_proto_offering_v1_addon_proto_init()
	file_backend_proto_offering_v1_service_proto_init()
	file_backend_proto_offering_v1_addon_service_proto_msgTypes[8].OneofWrappers = []any{}
	file_backend_proto_offering_v1_addon_service_proto_msgTypes[9].OneofWrappers = []any{}
	file_backend_proto_offering_v1_addon_service_proto_msgTypes[11].OneofWrappers = []any{}
	file_backend_proto_offering_v1_addon_service_proto_msgTypes[12].OneofWrappers = []any{}
	file_backend_proto_offering_v1_addon_service_proto_msgTypes[18].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_addon_service_proto_rawDesc), len(file_backend_proto_offering_v1_addon_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_offering_v1_addon_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_v1_addon_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_offering_v1_addon_service_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_v1_addon_service_proto = out.File
	file_backend_proto_offering_v1_addon_service_proto_goTypes = nil
	file_backend_proto_offering_v1_addon_service_proto_depIdxs = nil
}
