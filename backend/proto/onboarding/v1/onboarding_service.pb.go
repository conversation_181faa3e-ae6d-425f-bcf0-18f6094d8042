// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/onboarding/v1/onboarding_service.proto

package onboardingpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Task state enum
type TaskState int32

const (
	// Unspecified task state
	TaskState_TASK_STATE_UNSPECIFIED TaskState = 0
	// Task is queued
	TaskState_TASK_STATE_QUEUED TaskState = 1
	// Task is in progress
	TaskState_TASK_STATE_IN_PROGRESS TaskState = 2
	// Task is completed
	TaskState_TASK_STATE_COMPLETED TaskState = 3
	// Task failed
	TaskState_TASK_STATE_FAILED TaskState = 4
)

// Enum value maps for TaskState.
var (
	TaskState_name = map[int32]string{
		0: "TASK_STATE_UNSPECIFIED",
		1: "TASK_STATE_QUEUED",
		2: "TASK_STATE_IN_PROGRESS",
		3: "TASK_STATE_COMPLETED",
		4: "TASK_STATE_FAILED",
	}
	TaskState_value = map[string]int32{
		"TASK_STATE_UNSPECIFIED": 0,
		"TASK_STATE_QUEUED":      1,
		"TASK_STATE_IN_PROGRESS": 2,
		"TASK_STATE_COMPLETED":   3,
		"TASK_STATE_FAILED":      4,
	}
)

func (x TaskState) Enum() *TaskState {
	p := new(TaskState)
	*p = x
	return p
}

func (x TaskState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskState) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_enumTypes[0].Descriptor()
}

func (TaskState) Type() protoreflect.EnumType {
	return &file_backend_proto_onboarding_v1_onboarding_service_proto_enumTypes[0]
}

func (x TaskState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskState.Descriptor instead.
func (TaskState) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP(), []int{0}
}

// FlowState represents the status of an onboarding flow.
type FlowState int32

const (
	// Unspecified state.
	FlowState_FLOW_STATE_UNSPECIFIED FlowState = 0
	// The flow is in progress.
	FlowState_FLOW_STATE_IN_PROGRESS FlowState = 1
	// The flow is completed.
	FlowState_FLOW_STATE_COMPLETED FlowState = 2
)

// Enum value maps for FlowState.
var (
	FlowState_name = map[int32]string{
		0: "FLOW_STATE_UNSPECIFIED",
		1: "FLOW_STATE_IN_PROGRESS",
		2: "FLOW_STATE_COMPLETED",
	}
	FlowState_value = map[string]int32{
		"FLOW_STATE_UNSPECIFIED": 0,
		"FLOW_STATE_IN_PROGRESS": 1,
		"FLOW_STATE_COMPLETED":   2,
	}
)

func (x FlowState) Enum() *FlowState {
	p := new(FlowState)
	*p = x
	return p
}

func (x FlowState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FlowState) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_enumTypes[1].Descriptor()
}

func (FlowState) Type() protoreflect.EnumType {
	return &file_backend_proto_onboarding_v1_onboarding_service_proto_enumTypes[1]
}

func (x FlowState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FlowState.Descriptor instead.
func (FlowState) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP(), []int{1}
}

// Enum for step state.
type StepState int32

const (
	// Unspecified step state.
	StepState_STEP_STATE_UNSPECIFIED StepState = 0
	// The step is in progress.
	StepState_STEP_STATE_IN_PROGRESS StepState = 1
	// The step is completed.
	StepState_STEP_STATE_COMPLETED StepState = 2
	// The step is skipped.
	StepState_STEP_STATE_SKIPPED StepState = 3
)

// Enum value maps for StepState.
var (
	StepState_name = map[int32]string{
		0: "STEP_STATE_UNSPECIFIED",
		1: "STEP_STATE_IN_PROGRESS",
		2: "STEP_STATE_COMPLETED",
		3: "STEP_STATE_SKIPPED",
	}
	StepState_value = map[string]int32{
		"STEP_STATE_UNSPECIFIED": 0,
		"STEP_STATE_IN_PROGRESS": 1,
		"STEP_STATE_COMPLETED":   2,
		"STEP_STATE_SKIPPED":     3,
	}
)

func (x StepState) Enum() *StepState {
	p := new(StepState)
	*p = x
	return p
}

func (x StepState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StepState) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_enumTypes[2].Descriptor()
}

func (StepState) Type() protoreflect.EnumType {
	return &file_backend_proto_onboarding_v1_onboarding_service_proto_enumTypes[2]
}

func (x StepState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StepState.Descriptor instead.
func (StepState) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP(), []int{2}
}

// Enum for source type
type SourceType int32

const (
	// Unspecified source type.
	SourceType_SOURCE_TYPE_UNSPECIFIED SourceType = 0
	// The source is a CSV file.
	SourceType_CSV SourceType = 1
	// The source is an image file.
	SourceType_IMAGE SourceType = 2
	// The source is a URL.
	SourceType_URL SourceType = 3
	// The source is a string.
	SourceType_STRING SourceType = 4
	// The source is a PDF file.
	SourceType_PDF SourceType = 5
	// The source is a DOCX file.
	SourceType_DOCX SourceType = 6
	// The source is an Excel file.
	SourceType_EXCEL SourceType = 7
)

// Enum value maps for SourceType.
var (
	SourceType_name = map[int32]string{
		0: "SOURCE_TYPE_UNSPECIFIED",
		1: "CSV",
		2: "IMAGE",
		3: "URL",
		4: "STRING",
		5: "PDF",
		6: "DOCX",
		7: "EXCEL",
	}
	SourceType_value = map[string]int32{
		"SOURCE_TYPE_UNSPECIFIED": 0,
		"CSV":                     1,
		"IMAGE":                   2,
		"URL":                     3,
		"STRING":                  4,
		"PDF":                     5,
		"DOCX":                    6,
		"EXCEL":                   7,
	}
)

func (x SourceType) Enum() *SourceType {
	p := new(SourceType)
	*p = x
	return p
}

func (x SourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_enumTypes[3].Descriptor()
}

func (SourceType) Type() protoreflect.EnumType {
	return &file_backend_proto_onboarding_v1_onboarding_service_proto_enumTypes[3]
}

func (x SourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SourceType.Descriptor instead.
func (SourceType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP(), []int{3}
}

// Enum for service item type
type ServiceItemType int32

const (
	// Unspecified service item type.
	ServiceItemType_SERVICE_ITEM_TYPE_UNSPECIFIED ServiceItemType = 0
	// Grooming care type
	ServiceItemType_GROOMING ServiceItemType = 1
	// Boarding care type
	ServiceItemType_BOARDING ServiceItemType = 2
	// Daycare care type
	ServiceItemType_DAYCARE ServiceItemType = 3
	// Evaluation care type
	ServiceItemType_EVALUATION ServiceItemType = 4
	// Dog walking care type
	ServiceItemType_DOG_WALKING ServiceItemType = 5
	// Training group class
	ServiceItemType_GROUP_CLASS ServiceItemType = 6
)

// Enum value maps for ServiceItemType.
var (
	ServiceItemType_name = map[int32]string{
		0: "SERVICE_ITEM_TYPE_UNSPECIFIED",
		1: "GROOMING",
		2: "BOARDING",
		3: "DAYCARE",
		4: "EVALUATION",
		5: "DOG_WALKING",
		6: "GROUP_CLASS",
	}
	ServiceItemType_value = map[string]int32{
		"SERVICE_ITEM_TYPE_UNSPECIFIED": 0,
		"GROOMING":                      1,
		"BOARDING":                      2,
		"DAYCARE":                       3,
		"EVALUATION":                    4,
		"DOG_WALKING":                   5,
		"GROUP_CLASS":                   6,
	}
)

func (x ServiceItemType) Enum() *ServiceItemType {
	p := new(ServiceItemType)
	*p = x
	return p
}

func (x ServiceItemType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceItemType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_enumTypes[4].Descriptor()
}

func (ServiceItemType) Type() protoreflect.EnumType {
	return &file_backend_proto_onboarding_v1_onboarding_service_proto_enumTypes[4]
}

func (x ServiceItemType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceItemType.Descriptor instead.
func (ServiceItemType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP(), []int{4}
}

// ErrCode 定义错误码枚举
//
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
type ErrCode int32

const (
	// (-- api-linter: core::0126::unspecified=disabled
	//
	//	aip.dev/not-precedent: We need to do this because
	//	the content of the error code is automatically generated by
	//	the script and is exclusive to each service.
	//	Please do not turn off this linter for the rest of the enum --)
	//
	// 成功
	ErrCode_ERR_CODE_OK ErrCode = 0
	// 本服务自动分配的全局唯一的起始错误码
	ErrCode_ERR_CODE_UNSPECIFIED ErrCode = 130600
	// Task not found
	ErrCode_ERR_CODE_TASK_NOT_FOUND ErrCode = 130601
	// Task already exists
	ErrCode_ERR_CODE_TASK_ALREADY_EXISTS ErrCode = 130602
	// Task is not in progress
	ErrCode_ERR_CODE_TASK_NOT_IN_PROGRESS ErrCode = 130603
	// Task is not completed
	ErrCode_ERR_CODE_TASK_NOT_COMPLETED ErrCode = 130604
	// Task is not queued
	ErrCode_ERR_CODE_TASK_NOT_QUEUED ErrCode = 130605
	// Task is already queued
	ErrCode_ERR_CODE_TASK_ALREADY_QUEUED ErrCode = 130606
	// Task status is invalid
	ErrCode_ERR_CODE_TASK_STATUS_INVALID ErrCode = 130607
)

// Enum value maps for ErrCode.
var (
	ErrCode_name = map[int32]string{
		0:      "ERR_CODE_OK",
		130600: "ERR_CODE_UNSPECIFIED",
		130601: "ERR_CODE_TASK_NOT_FOUND",
		130602: "ERR_CODE_TASK_ALREADY_EXISTS",
		130603: "ERR_CODE_TASK_NOT_IN_PROGRESS",
		130604: "ERR_CODE_TASK_NOT_COMPLETED",
		130605: "ERR_CODE_TASK_NOT_QUEUED",
		130606: "ERR_CODE_TASK_ALREADY_QUEUED",
		130607: "ERR_CODE_TASK_STATUS_INVALID",
	}
	ErrCode_value = map[string]int32{
		"ERR_CODE_OK":                   0,
		"ERR_CODE_UNSPECIFIED":          130600,
		"ERR_CODE_TASK_NOT_FOUND":       130601,
		"ERR_CODE_TASK_ALREADY_EXISTS":  130602,
		"ERR_CODE_TASK_NOT_IN_PROGRESS": 130603,
		"ERR_CODE_TASK_NOT_COMPLETED":   130604,
		"ERR_CODE_TASK_NOT_QUEUED":      130605,
		"ERR_CODE_TASK_ALREADY_QUEUED":  130606,
		"ERR_CODE_TASK_STATUS_INVALID":  130607,
	}
)

func (x ErrCode) Enum() *ErrCode {
	p := new(ErrCode)
	*p = x
	return p
}

func (x ErrCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrCode) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_enumTypes[5].Descriptor()
}

func (ErrCode) Type() protoreflect.EnumType {
	return &file_backend_proto_onboarding_v1_onboarding_service_proto_enumTypes[5]
}

func (x ErrCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrCode.Descriptor instead.
func (ErrCode) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP(), []int{5}
}

// Represents the overall progress of an onboarding flow.
type OnboardingProgress struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// The ID of the flow.
	FlowId string `protobuf:"bytes,4,opt,name=flow_id,json=flowId,proto3" json:"flow_id,omitempty"`
	// The progress of each step.
	Steps []*StepProgress `protobuf:"bytes,5,rep,name=steps,proto3" json:"steps,omitempty"`
	// The timestamp of the last update.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// The timestamp of the created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The state of the flow.
	State FlowState `protobuf:"varint,8,opt,name=state,proto3,enum=backend.proto.onboarding.v1.FlowState" json:"state,omitempty"`
	// The metadata of the flow.
	Metadata      *structpb.Struct `protobuf:"bytes,9,opt,name=metadata,proto3" json:"metadata,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OnboardingProgress) Reset() {
	*x = OnboardingProgress{}
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OnboardingProgress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnboardingProgress) ProtoMessage() {}

func (x *OnboardingProgress) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnboardingProgress.ProtoReflect.Descriptor instead.
func (*OnboardingProgress) Descriptor() ([]byte, []int) {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP(), []int{0}
}

func (x *OnboardingProgress) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *OnboardingProgress) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *OnboardingProgress) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *OnboardingProgress) GetFlowId() string {
	if x != nil {
		return x.FlowId
	}
	return ""
}

func (x *OnboardingProgress) GetSteps() []*StepProgress {
	if x != nil {
		return x.Steps
	}
	return nil
}

func (x *OnboardingProgress) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *OnboardingProgress) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *OnboardingProgress) GetState() FlowState {
	if x != nil {
		return x.State
	}
	return FlowState_FLOW_STATE_UNSPECIFIED
}

func (x *OnboardingProgress) GetMetadata() *structpb.Struct {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// Represents the progress of a single step in the onboarding flow.
type StepProgress struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The ID of the step.
	StepId string `protobuf:"bytes,1,opt,name=step_id,json=stepId,proto3" json:"step_id,omitempty"`
	// The state of the step.
	State StepState `protobuf:"varint,2,opt,name=state,proto3,enum=backend.proto.onboarding.v1.StepState" json:"state,omitempty"`
	// The metadata of the step.
	Metadata *structpb.Struct `protobuf:"bytes,3,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// The timestamp of the start.
	StartTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// The timestamp of the completion.
	CompleteTime  *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=complete_time,json=completeTime,proto3" json:"complete_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StepProgress) Reset() {
	*x = StepProgress{}
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StepProgress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StepProgress) ProtoMessage() {}

func (x *StepProgress) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StepProgress.ProtoReflect.Descriptor instead.
func (*StepProgress) Descriptor() ([]byte, []int) {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP(), []int{1}
}

func (x *StepProgress) GetStepId() string {
	if x != nil {
		return x.StepId
	}
	return ""
}

func (x *StepProgress) GetState() StepState {
	if x != nil {
		return x.State
	}
	return StepState_STEP_STATE_UNSPECIFIED
}

func (x *StepProgress) GetMetadata() *structpb.Struct {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *StepProgress) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *StepProgress) GetCompleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CompleteTime
	}
	return nil
}

// Represents a data source for parsing services.
type Source struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The type of the source.
	Type SourceType `protobuf:"varint,1,opt,name=type,proto3,enum=backend.proto.onboarding.v1.SourceType" json:"type,omitempty"`
	// The data of the source.
	Data          string `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Source) Reset() {
	*x = Source{}
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Source) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Source) ProtoMessage() {}

func (x *Source) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Source.ProtoReflect.Descriptor instead.
func (*Source) Descriptor() ([]byte, []int) {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP(), []int{2}
}

func (x *Source) GetType() SourceType {
	if x != nil {
		return x.Type
	}
	return SourceType_SOURCE_TYPE_UNSPECIFIED
}

func (x *Source) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

// Request message for GetOnboardingProgress RPC.
type GetOnboardingProgressRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// The ID of the flow.
	FlowId        string `protobuf:"bytes,4,opt,name=flow_id,json=flowId,proto3" json:"flow_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOnboardingProgressRequest) Reset() {
	*x = GetOnboardingProgressRequest{}
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOnboardingProgressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnboardingProgressRequest) ProtoMessage() {}

func (x *GetOnboardingProgressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnboardingProgressRequest.ProtoReflect.Descriptor instead.
func (*GetOnboardingProgressRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetOnboardingProgressRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetOnboardingProgressRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetOnboardingProgressRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *GetOnboardingProgressRequest) GetFlowId() string {
	if x != nil {
		return x.FlowId
	}
	return ""
}

// Request message for UpdateOnboardingProgress RPC.
type UpdateOnboardingProgressRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// The ID of the flow.
	FlowId string `protobuf:"bytes,4,opt,name=flow_id,json=flowId,proto3" json:"flow_id,omitempty"`
	// The list of step updates.
	Steps []*UpdateOnboardingProgressRequest_StepUpdate `protobuf:"bytes,5,rep,name=steps,proto3" json:"steps,omitempty"`
	// The state of the flow.
	FlowState *FlowState `protobuf:"varint,6,opt,name=flow_state,json=flowState,proto3,enum=backend.proto.onboarding.v1.FlowState,oneof" json:"flow_state,omitempty"`
	// The metadata of the flow.
	Metadata      *structpb.Struct `protobuf:"bytes,7,opt,name=metadata,proto3" json:"metadata,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateOnboardingProgressRequest) Reset() {
	*x = UpdateOnboardingProgressRequest{}
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateOnboardingProgressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOnboardingProgressRequest) ProtoMessage() {}

func (x *UpdateOnboardingProgressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOnboardingProgressRequest.ProtoReflect.Descriptor instead.
func (*UpdateOnboardingProgressRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateOnboardingProgressRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpdateOnboardingProgressRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *UpdateOnboardingProgressRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *UpdateOnboardingProgressRequest) GetFlowId() string {
	if x != nil {
		return x.FlowId
	}
	return ""
}

func (x *UpdateOnboardingProgressRequest) GetSteps() []*UpdateOnboardingProgressRequest_StepUpdate {
	if x != nil {
		return x.Steps
	}
	return nil
}

func (x *UpdateOnboardingProgressRequest) GetFlowState() FlowState {
	if x != nil && x.FlowState != nil {
		return *x.FlowState
	}
	return FlowState_FLOW_STATE_UNSPECIFIED
}

func (x *UpdateOnboardingProgressRequest) GetMetadata() *structpb.Struct {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// Request message for ParseServices RPC.
type ParseServicesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// The list of sources.
	Sources       []*Source `protobuf:"bytes,4,rep,name=sources,proto3" json:"sources,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ParseServicesRequest) Reset() {
	*x = ParseServicesRequest{}
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParseServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseServicesRequest) ProtoMessage() {}

func (x *ParseServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseServicesRequest.ProtoReflect.Descriptor instead.
func (*ParseServicesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP(), []int{5}
}

func (x *ParseServicesRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ParseServicesRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ParseServicesRequest) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *ParseServicesRequest) GetSources() []*Source {
	if x != nil {
		return x.Sources
	}
	return nil
}

// Response message for ParseServices RPC.
type ParseServicesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The list of services.
	Services []*ParseServicesResponse_Service `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"`
	// The list of categories.
	CategoryList []*ParseServicesResponse_Category `protobuf:"bytes,2,rep,name=category_list,json=categoryList,proto3" json:"category_list,omitempty"`
	// The list of price taxes.
	PriceTaxList []*ParseServicesResponse_PriceTax `protobuf:"bytes,3,rep,name=price_tax_list,json=priceTaxList,proto3" json:"price_tax_list,omitempty"`
	// The list of pet types.
	PetTypeList []*ParseServicesResponse_PetType `protobuf:"bytes,4,rep,name=pet_type_list,json=petTypeList,proto3" json:"pet_type_list,omitempty"`
	// The list of pet breeds.
	PetBreedList []*ParseServicesResponse_PetBreed `protobuf:"bytes,5,rep,name=pet_breed_list,json=petBreedList,proto3" json:"pet_breed_list,omitempty"`
	// The list of pet sizes.
	PetSizeList []*ParseServicesResponse_PetSize `protobuf:"bytes,6,rep,name=pet_size_list,json=petSizeList,proto3" json:"pet_size_list,omitempty"`
	// The list of pet coat types.
	PetCoatTypeList []*ParseServicesResponse_PetCoatType `protobuf:"bytes,7,rep,name=pet_coat_type_list,json=petCoatTypeList,proto3" json:"pet_coat_type_list,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ParseServicesResponse) Reset() {
	*x = ParseServicesResponse{}
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParseServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseServicesResponse) ProtoMessage() {}

func (x *ParseServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseServicesResponse.ProtoReflect.Descriptor instead.
func (*ParseServicesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP(), []int{6}
}

func (x *ParseServicesResponse) GetServices() []*ParseServicesResponse_Service {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *ParseServicesResponse) GetCategoryList() []*ParseServicesResponse_Category {
	if x != nil {
		return x.CategoryList
	}
	return nil
}

func (x *ParseServicesResponse) GetPriceTaxList() []*ParseServicesResponse_PriceTax {
	if x != nil {
		return x.PriceTaxList
	}
	return nil
}

func (x *ParseServicesResponse) GetPetTypeList() []*ParseServicesResponse_PetType {
	if x != nil {
		return x.PetTypeList
	}
	return nil
}

func (x *ParseServicesResponse) GetPetBreedList() []*ParseServicesResponse_PetBreed {
	if x != nil {
		return x.PetBreedList
	}
	return nil
}

func (x *ParseServicesResponse) GetPetSizeList() []*ParseServicesResponse_PetSize {
	if x != nil {
		return x.PetSizeList
	}
	return nil
}

func (x *ParseServicesResponse) GetPetCoatTypeList() []*ParseServicesResponse_PetCoatType {
	if x != nil {
		return x.PetCoatTypeList
	}
	return nil
}

// Request message for triggering GenerateBookingPage RPC.
type GenerateBookingPageRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id
	StaffId       *int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenerateBookingPageRequest) Reset() {
	*x = GenerateBookingPageRequest{}
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateBookingPageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateBookingPageRequest) ProtoMessage() {}

func (x *GenerateBookingPageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateBookingPageRequest.ProtoReflect.Descriptor instead.
func (*GenerateBookingPageRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP(), []int{7}
}

func (x *GenerateBookingPageRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GenerateBookingPageRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GenerateBookingPageRequest) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

// Response message for GenerateBookingPage RPC.
type GenerateBookingPageResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// task id
	TaskId string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	// task state
	State         TaskState `protobuf:"varint,2,opt,name=state,proto3,enum=backend.proto.onboarding.v1.TaskState" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenerateBookingPageResponse) Reset() {
	*x = GenerateBookingPageResponse{}
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateBookingPageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateBookingPageResponse) ProtoMessage() {}

func (x *GenerateBookingPageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateBookingPageResponse.ProtoReflect.Descriptor instead.
func (*GenerateBookingPageResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP(), []int{8}
}

func (x *GenerateBookingPageResponse) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *GenerateBookingPageResponse) GetState() TaskState {
	if x != nil {
		return x.State
	}
	return TaskState_TASK_STATE_UNSPECIFIED
}

// Request message for GetGenerateBookingPageTaskState RPC.
type GetGenerateBookingPageTaskStateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// task id, optional, if not provided, use the latest task
	TaskId        *string `protobuf:"bytes,4,opt,name=task_id,json=taskId,proto3,oneof" json:"task_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGenerateBookingPageTaskStateRequest) Reset() {
	*x = GetGenerateBookingPageTaskStateRequest{}
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGenerateBookingPageTaskStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGenerateBookingPageTaskStateRequest) ProtoMessage() {}

func (x *GetGenerateBookingPageTaskStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGenerateBookingPageTaskStateRequest.ProtoReflect.Descriptor instead.
func (*GetGenerateBookingPageTaskStateRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetGenerateBookingPageTaskStateRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetGenerateBookingPageTaskStateRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetGenerateBookingPageTaskStateRequest) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *GetGenerateBookingPageTaskStateRequest) GetTaskId() string {
	if x != nil && x.TaskId != nil {
		return *x.TaskId
	}
	return ""
}

// Response message for GetGenerateBookingPageTaskState RPC.
type GenerateBookingPageTaskState struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// task id
	TaskId string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	// task state
	State TaskState `protobuf:"varint,2,opt,name=state,proto3,enum=backend.proto.onboarding.v1.TaskState" json:"state,omitempty"`
	// Error message. Only available when state is TASK_STATE_FAILED
	ErrorMessage  *string `protobuf:"bytes,3,opt,name=error_message,json=errorMessage,proto3,oneof" json:"error_message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenerateBookingPageTaskState) Reset() {
	*x = GenerateBookingPageTaskState{}
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateBookingPageTaskState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateBookingPageTaskState) ProtoMessage() {}

func (x *GenerateBookingPageTaskState) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateBookingPageTaskState.ProtoReflect.Descriptor instead.
func (*GenerateBookingPageTaskState) Descriptor() ([]byte, []int) {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP(), []int{10}
}

func (x *GenerateBookingPageTaskState) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *GenerateBookingPageTaskState) GetState() TaskState {
	if x != nil {
		return x.State
	}
	return TaskState_TASK_STATE_UNSPECIFIED
}

func (x *GenerateBookingPageTaskState) GetErrorMessage() string {
	if x != nil && x.ErrorMessage != nil {
		return *x.ErrorMessage
	}
	return ""
}

// Request message for updating task state
type UpdateGenerateBookingPageTaskStateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// task id
	TaskId string `protobuf:"bytes,4,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	// task state
	State TaskState `protobuf:"varint,5,opt,name=state,proto3,enum=backend.proto.onboarding.v1.TaskState" json:"state,omitempty"`
	// Error message. Only required when state is TASK_STATE_FAILED
	ErrorMessage  *string `protobuf:"bytes,6,opt,name=error_message,json=errorMessage,proto3,oneof" json:"error_message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateGenerateBookingPageTaskStateRequest) Reset() {
	*x = UpdateGenerateBookingPageTaskStateRequest{}
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateGenerateBookingPageTaskStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateGenerateBookingPageTaskStateRequest) ProtoMessage() {}

func (x *UpdateGenerateBookingPageTaskStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateGenerateBookingPageTaskStateRequest.ProtoReflect.Descriptor instead.
func (*UpdateGenerateBookingPageTaskStateRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateGenerateBookingPageTaskStateRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpdateGenerateBookingPageTaskStateRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *UpdateGenerateBookingPageTaskStateRequest) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *UpdateGenerateBookingPageTaskStateRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *UpdateGenerateBookingPageTaskStateRequest) GetState() TaskState {
	if x != nil {
		return x.State
	}
	return TaskState_TASK_STATE_UNSPECIFIED
}

func (x *UpdateGenerateBookingPageTaskStateRequest) GetErrorMessage() string {
	if x != nil && x.ErrorMessage != nil {
		return *x.ErrorMessage
	}
	return ""
}

// Represents a single step update.
type UpdateOnboardingProgressRequest_StepUpdate struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The ID of the step.
	StepId string `protobuf:"bytes,1,opt,name=step_id,json=stepId,proto3" json:"step_id,omitempty"`
	// The state of the step.
	State StepState `protobuf:"varint,2,opt,name=state,proto3,enum=backend.proto.onboarding.v1.StepState" json:"state,omitempty"`
	// The metadata of the step.
	Metadata      *structpb.Struct `protobuf:"bytes,3,opt,name=metadata,proto3" json:"metadata,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateOnboardingProgressRequest_StepUpdate) Reset() {
	*x = UpdateOnboardingProgressRequest_StepUpdate{}
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateOnboardingProgressRequest_StepUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOnboardingProgressRequest_StepUpdate) ProtoMessage() {}

func (x *UpdateOnboardingProgressRequest_StepUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOnboardingProgressRequest_StepUpdate.ProtoReflect.Descriptor instead.
func (*UpdateOnboardingProgressRequest_StepUpdate) Descriptor() ([]byte, []int) {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP(), []int{4, 0}
}

func (x *UpdateOnboardingProgressRequest_StepUpdate) GetStepId() string {
	if x != nil {
		return x.StepId
	}
	return ""
}

func (x *UpdateOnboardingProgressRequest_StepUpdate) GetState() StepState {
	if x != nil {
		return x.State
	}
	return StepState_STEP_STATE_UNSPECIFIED
}

func (x *UpdateOnboardingProgressRequest_StepUpdate) GetMetadata() *structpb.Struct {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// Represents a service category.
type ParseServicesResponse_Category struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The unique virtual identifier for the category.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The name of the category.
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ParseServicesResponse_Category) Reset() {
	*x = ParseServicesResponse_Category{}
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParseServicesResponse_Category) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseServicesResponse_Category) ProtoMessage() {}

func (x *ParseServicesResponse_Category) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseServicesResponse_Category.ProtoReflect.Descriptor instead.
func (*ParseServicesResponse_Category) Descriptor() ([]byte, []int) {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP(), []int{6, 0}
}

func (x *ParseServicesResponse_Category) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ParseServicesResponse_Category) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// Represents a price tax.
type ParseServicesResponse_PriceTax struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The unique virtual identifier for the tax.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The name of the tax.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// The tax rate.
	Rate          float64 `protobuf:"fixed64,3,opt,name=rate,proto3" json:"rate,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ParseServicesResponse_PriceTax) Reset() {
	*x = ParseServicesResponse_PriceTax{}
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParseServicesResponse_PriceTax) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseServicesResponse_PriceTax) ProtoMessage() {}

func (x *ParseServicesResponse_PriceTax) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseServicesResponse_PriceTax.ProtoReflect.Descriptor instead.
func (*ParseServicesResponse_PriceTax) Descriptor() ([]byte, []int) {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP(), []int{6, 1}
}

func (x *ParseServicesResponse_PriceTax) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ParseServicesResponse_PriceTax) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ParseServicesResponse_PriceTax) GetRate() float64 {
	if x != nil {
		return x.Rate
	}
	return 0
}

// Represents a pet type.
type ParseServicesResponse_PetType struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The unique virtual identifier for the pet type.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The unique real identifier for the pet type. The same value as id.
	PetTypeId int64 `protobuf:"varint,2,opt,name=pet_type_id,json=petTypeId,proto3" json:"pet_type_id,omitempty"`
	// The name of the pet type.
	Name          string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ParseServicesResponse_PetType) Reset() {
	*x = ParseServicesResponse_PetType{}
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParseServicesResponse_PetType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseServicesResponse_PetType) ProtoMessage() {}

func (x *ParseServicesResponse_PetType) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseServicesResponse_PetType.ProtoReflect.Descriptor instead.
func (*ParseServicesResponse_PetType) Descriptor() ([]byte, []int) {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP(), []int{6, 2}
}

func (x *ParseServicesResponse_PetType) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ParseServicesResponse_PetType) GetPetTypeId() int64 {
	if x != nil {
		return x.PetTypeId
	}
	return 0
}

func (x *ParseServicesResponse_PetType) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// Represents a pet breed.
type ParseServicesResponse_PetBreed struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The unique virtual identifier for the pet breed.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The ID of the pet type this breed belongs to.
	PetTypeId int64 `protobuf:"varint,2,opt,name=pet_type_id,json=petTypeId,proto3" json:"pet_type_id,omitempty"`
	// The name of the breed.
	Name          string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ParseServicesResponse_PetBreed) Reset() {
	*x = ParseServicesResponse_PetBreed{}
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParseServicesResponse_PetBreed) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseServicesResponse_PetBreed) ProtoMessage() {}

func (x *ParseServicesResponse_PetBreed) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseServicesResponse_PetBreed.ProtoReflect.Descriptor instead.
func (*ParseServicesResponse_PetBreed) Descriptor() ([]byte, []int) {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP(), []int{6, 3}
}

func (x *ParseServicesResponse_PetBreed) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ParseServicesResponse_PetBreed) GetPetTypeId() int64 {
	if x != nil {
		return x.PetTypeId
	}
	return 0
}

func (x *ParseServicesResponse_PetBreed) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// Represents a pet size.
type ParseServicesResponse_PetSize struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The unique virtual identifier for the pet size.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The name of the size.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// The lower bound of the weight range.
	WeightLow int32 `protobuf:"varint,3,opt,name=weight_low,json=weightLow,proto3" json:"weight_low,omitempty"`
	// The upper bound of the weight range.
	WeightHigh    int32 `protobuf:"varint,4,opt,name=weight_high,json=weightHigh,proto3" json:"weight_high,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ParseServicesResponse_PetSize) Reset() {
	*x = ParseServicesResponse_PetSize{}
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParseServicesResponse_PetSize) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseServicesResponse_PetSize) ProtoMessage() {}

func (x *ParseServicesResponse_PetSize) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseServicesResponse_PetSize.ProtoReflect.Descriptor instead.
func (*ParseServicesResponse_PetSize) Descriptor() ([]byte, []int) {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP(), []int{6, 4}
}

func (x *ParseServicesResponse_PetSize) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ParseServicesResponse_PetSize) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ParseServicesResponse_PetSize) GetWeightLow() int32 {
	if x != nil {
		return x.WeightLow
	}
	return 0
}

func (x *ParseServicesResponse_PetSize) GetWeightHigh() int32 {
	if x != nil {
		return x.WeightHigh
	}
	return 0
}

// Represents a pet coat type.
type ParseServicesResponse_PetCoatType struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The unique virtual identifier for the coat type.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The name of the coat type.
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ParseServicesResponse_PetCoatType) Reset() {
	*x = ParseServicesResponse_PetCoatType{}
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParseServicesResponse_PetCoatType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseServicesResponse_PetCoatType) ProtoMessage() {}

func (x *ParseServicesResponse_PetCoatType) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseServicesResponse_PetCoatType.ProtoReflect.Descriptor instead.
func (*ParseServicesResponse_PetCoatType) Descriptor() ([]byte, []int) {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP(), []int{6, 5}
}

func (x *ParseServicesResponse_PetCoatType) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ParseServicesResponse_PetCoatType) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// Represents a pet type and breed combination for a service.
type ParseServicesResponse_ServicePetTypeAndBreed struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The ID of the pet type.
	PetTypeId int64 `protobuf:"varint,1,opt,name=pet_type_id,json=petTypeId,proto3" json:"pet_type_id,omitempty"`
	// The ID of the pet breed.
	PetBreedIds   []int64 `protobuf:"varint,2,rep,packed,name=pet_breed_ids,json=petBreedIds,proto3" json:"pet_breed_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ParseServicesResponse_ServicePetTypeAndBreed) Reset() {
	*x = ParseServicesResponse_ServicePetTypeAndBreed{}
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParseServicesResponse_ServicePetTypeAndBreed) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseServicesResponse_ServicePetTypeAndBreed) ProtoMessage() {}

func (x *ParseServicesResponse_ServicePetTypeAndBreed) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseServicesResponse_ServicePetTypeAndBreed.ProtoReflect.Descriptor instead.
func (*ParseServicesResponse_ServicePetTypeAndBreed) Descriptor() ([]byte, []int) {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP(), []int{6, 6}
}

func (x *ParseServicesResponse_ServicePetTypeAndBreed) GetPetTypeId() int64 {
	if x != nil {
		return x.PetTypeId
	}
	return 0
}

func (x *ParseServicesResponse_ServicePetTypeAndBreed) GetPetBreedIds() []int64 {
	if x != nil {
		return x.PetBreedIds
	}
	return nil
}

// Represents a single service.
type ParseServicesResponse_Service struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// description
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// inactive
	Inactive bool `protobuf:"varint,4,opt,name=inactive,proto3" json:"inactive,omitempty"`
	// category id
	CategoryId int64 `protobuf:"varint,6,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// service type
	ServiceItemType ServiceItemType `protobuf:"varint,10,opt,name=service_item_type,json=serviceItemType,proto3,enum=backend.proto.onboarding.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// price
	Price float64 `protobuf:"fixed64,11,opt,name=price,proto3" json:"price,omitempty"`
	// tax id
	TaxId int64 `protobuf:"varint,13,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// duration
	Duration int32 `protobuf:"varint,14,opt,name=duration,proto3" json:"duration,omitempty"`
	// type and breed list
	TypeAndBreedList []*ParseServicesResponse_ServicePetTypeAndBreed `protobuf:"bytes,15,rep,name=type_and_breed_list,json=typeAndBreedList,proto3" json:"type_and_breed_list,omitempty"`
	// pet size id list
	PetSizeIdList []int64 `protobuf:"varint,16,rep,packed,name=pet_size_id_list,json=petSizeIdList,proto3" json:"pet_size_id_list,omitempty"`
	// pet coat type list
	PetCoatTypeList []int64 `protobuf:"varint,17,rep,packed,name=pet_coat_type_list,json=petCoatTypeList,proto3" json:"pet_coat_type_list,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ParseServicesResponse_Service) Reset() {
	*x = ParseServicesResponse_Service{}
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParseServicesResponse_Service) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseServicesResponse_Service) ProtoMessage() {}

func (x *ParseServicesResponse_Service) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseServicesResponse_Service.ProtoReflect.Descriptor instead.
func (*ParseServicesResponse_Service) Descriptor() ([]byte, []int) {
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP(), []int{6, 7}
}

func (x *ParseServicesResponse_Service) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ParseServicesResponse_Service) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ParseServicesResponse_Service) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ParseServicesResponse_Service) GetInactive() bool {
	if x != nil {
		return x.Inactive
	}
	return false
}

func (x *ParseServicesResponse_Service) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *ParseServicesResponse_Service) GetServiceItemType() ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return ServiceItemType_SERVICE_ITEM_TYPE_UNSPECIFIED
}

func (x *ParseServicesResponse_Service) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *ParseServicesResponse_Service) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *ParseServicesResponse_Service) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *ParseServicesResponse_Service) GetTypeAndBreedList() []*ParseServicesResponse_ServicePetTypeAndBreed {
	if x != nil {
		return x.TypeAndBreedList
	}
	return nil
}

func (x *ParseServicesResponse_Service) GetPetSizeIdList() []int64 {
	if x != nil {
		return x.PetSizeIdList
	}
	return nil
}

func (x *ParseServicesResponse_Service) GetPetCoatTypeList() []int64 {
	if x != nil {
		return x.PetCoatTypeList
	}
	return nil
}

var File_backend_proto_onboarding_v1_onboarding_service_proto protoreflect.FileDescriptor

const file_backend_proto_onboarding_v1_onboarding_service_proto_rawDesc = "" +
	"\n" +
	"4backend/proto/onboarding/v1/onboarding_service.proto\x12\x1bbackend.proto.onboarding.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bbuf/validate/validate.proto\"\xd6\x03\n" +
	"\x12OnboardingProgress\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tcompanyId\x12(\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"businessId\x12\"\n" +
	"\bstaff_id\x18\x03 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\astaffId\x12\x17\n" +
	"\aflow_id\x18\x04 \x01(\tR\x06flowId\x12?\n" +
	"\x05steps\x18\x05 \x03(\v2).backend.proto.onboarding.v1.StepProgressR\x05steps\x12;\n" +
	"\vupdate_time\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12;\n" +
	"\vcreate_time\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12A\n" +
	"\x05state\x18\b \x01(\x0e2&.backend.proto.onboarding.v1.FlowStateB\x03\xe0A\x03R\x05state\x123\n" +
	"\bmetadata\x18\t \x01(\v2\x17.google.protobuf.StructR\bmetadata\"\x9b\x02\n" +
	"\fStepProgress\x12\x17\n" +
	"\astep_id\x18\x01 \x01(\tR\x06stepId\x12A\n" +
	"\x05state\x18\x02 \x01(\x0e2&.backend.proto.onboarding.v1.StepStateB\x03\xe0A\x03R\x05state\x123\n" +
	"\bmetadata\x18\x03 \x01(\v2\x17.google.protobuf.StructR\bmetadata\x129\n" +
	"\n" +
	"start_time\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x12?\n" +
	"\rcomplete_time\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\fcompleteTime\"Y\n" +
	"\x06Source\x12;\n" +
	"\x04type\x18\x01 \x01(\x0e2'.backend.proto.onboarding.v1.SourceTypeR\x04type\x12\x12\n" +
	"\x04data\x18\x02 \x01(\tR\x04data\"\xb6\x01\n" +
	"\x1cGetOnboardingProgressRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tcompanyId\x12(\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"businessId\x12\"\n" +
	"\bstaff_id\x18\x03 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\astaffId\x12 \n" +
	"\aflow_id\x18\x04 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x06flowId\"\xf0\x04\n" +
	"\x1fUpdateOnboardingProgressRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tcompanyId\x12(\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"businessId\x12\"\n" +
	"\bstaff_id\x18\x03 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\astaffId\x12 \n" +
	"\aflow_id\x18\x04 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x06flowId\x12g\n" +
	"\x05steps\x18\x05 \x03(\v2G.backend.proto.onboarding.v1.UpdateOnboardingProgressRequest.StepUpdateB\b\xbaH\x05\x92\x01\x02\b\x00R\x05steps\x12W\n" +
	"\n" +
	"flow_state\x18\x06 \x01(\x0e2&.backend.proto.onboarding.v1.FlowStateB\v\xe0A\x03\xbaH\x05\x82\x01\x02\x10\x01H\x00R\tflowState\x88\x01\x01\x123\n" +
	"\bmetadata\x18\a \x01(\v2\x17.google.protobuf.StructR\bmetadata\x1a\xae\x01\n" +
	"\n" +
	"StepUpdate\x12 \n" +
	"\astep_id\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x00R\x06stepId\x12I\n" +
	"\x05state\x18\x02 \x01(\x0e2&.backend.proto.onboarding.v1.StepStateB\v\xe0A\x03\xbaH\x05\x82\x01\x02\x10\x01R\x05state\x123\n" +
	"\bmetadata\x18\x03 \x01(\v2\x17.google.protobuf.StructR\bmetadataB\r\n" +
	"\v_flow_state\"\xe7\x01\n" +
	"\x14ParseServicesRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tcompanyId\x12(\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"businessId\x12'\n" +
	"\bstaff_id\x18\x03 \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x00R\astaffId\x88\x01\x01\x12G\n" +
	"\asources\x18\x04 \x03(\v2#.backend.proto.onboarding.v1.SourceB\b\xbaH\x05\x92\x01\x02\b\x01R\asourcesB\v\n" +
	"\t_staff_id\"\xd9\r\n" +
	"\x15ParseServicesResponse\x12V\n" +
	"\bservices\x18\x01 \x03(\v2:.backend.proto.onboarding.v1.ParseServicesResponse.ServiceR\bservices\x12`\n" +
	"\rcategory_list\x18\x02 \x03(\v2;.backend.proto.onboarding.v1.ParseServicesResponse.CategoryR\fcategoryList\x12a\n" +
	"\x0eprice_tax_list\x18\x03 \x03(\v2;.backend.proto.onboarding.v1.ParseServicesResponse.PriceTaxR\fpriceTaxList\x12^\n" +
	"\rpet_type_list\x18\x04 \x03(\v2:.backend.proto.onboarding.v1.ParseServicesResponse.PetTypeR\vpetTypeList\x12a\n" +
	"\x0epet_breed_list\x18\x05 \x03(\v2;.backend.proto.onboarding.v1.ParseServicesResponse.PetBreedR\fpetBreedList\x12^\n" +
	"\rpet_size_list\x18\x06 \x03(\v2:.backend.proto.onboarding.v1.ParseServicesResponse.PetSizeR\vpetSizeList\x12k\n" +
	"\x12pet_coat_type_list\x18\a \x03(\v2>.backend.proto.onboarding.v1.ParseServicesResponse.PetCoatTypeR\x0fpetCoatTypeList\x1a.\n" +
	"\bCategory\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x1aB\n" +
	"\bPriceTax\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x12\n" +
	"\x04rate\x18\x03 \x01(\x01R\x04rate\x1aM\n" +
	"\aPetType\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1e\n" +
	"\vpet_type_id\x18\x02 \x01(\x03R\tpetTypeId\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x1aN\n" +
	"\bPetBreed\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1e\n" +
	"\vpet_type_id\x18\x02 \x01(\x03R\tpetTypeId\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x1am\n" +
	"\aPetSize\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1d\n" +
	"\n" +
	"weight_low\x18\x03 \x01(\x05R\tweightLow\x12\x1f\n" +
	"\vweight_high\x18\x04 \x01(\x05R\n" +
	"weightHigh\x1a1\n" +
	"\vPetCoatType\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x1a\\\n" +
	"\x16ServicePetTypeAndBreed\x12\x1e\n" +
	"\vpet_type_id\x18\x01 \x01(\x03R\tpetTypeId\x12\"\n" +
	"\rpet_breed_ids\x18\x02 \x03(\x03R\vpetBreedIds\x1a\xff\x03\n" +
	"\aService\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x1a\n" +
	"\binactive\x18\x04 \x01(\bR\binactive\x12\x1f\n" +
	"\vcategory_id\x18\x06 \x01(\x03R\n" +
	"categoryId\x12X\n" +
	"\x11service_item_type\x18\n" +
	" \x01(\x0e2,.backend.proto.onboarding.v1.ServiceItemTypeR\x0fserviceItemType\x12\x14\n" +
	"\x05price\x18\v \x01(\x01R\x05price\x12\x15\n" +
	"\x06tax_id\x18\r \x01(\x03R\x05taxId\x12\x1a\n" +
	"\bduration\x18\x0e \x01(\x05R\bduration\x12x\n" +
	"\x13type_and_breed_list\x18\x0f \x03(\v2I.backend.proto.onboarding.v1.ParseServicesResponse.ServicePetTypeAndBreedR\x10typeAndBreedList\x12'\n" +
	"\x10pet_size_id_list\x18\x10 \x03(\x03R\rpetSizeIdList\x12+\n" +
	"\x12pet_coat_type_list\x18\x11 \x03(\x03R\x0fpetCoatTypeList\"\xa4\x01\n" +
	"\x1aGenerateBookingPageRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tcompanyId\x12(\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"businessId\x12'\n" +
	"\bstaff_id\x18\x03 \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x00R\astaffId\x88\x01\x01B\v\n" +
	"\t_staff_id\"y\n" +
	"\x1bGenerateBookingPageResponse\x12\x17\n" +
	"\atask_id\x18\x01 \x01(\tR\x06taskId\x12A\n" +
	"\x05state\x18\x02 \x01(\x0e2&.backend.proto.onboarding.v1.TaskStateB\x03\xe0A\x03R\x05state\"\xe3\x01\n" +
	"&GetGenerateBookingPageTaskStateRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tcompanyId\x12(\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"businessId\x12'\n" +
	"\bstaff_id\x18\x03 \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x00R\astaffId\x88\x01\x01\x12%\n" +
	"\atask_id\x18\x04 \x01(\tB\a\xbaH\x04r\x02\x10\x01H\x01R\x06taskId\x88\x01\x01B\v\n" +
	"\t_staff_idB\n" +
	"\n" +
	"\b_task_id\"\xb6\x01\n" +
	"\x1cGenerateBookingPageTaskState\x12\x17\n" +
	"\atask_id\x18\x01 \x01(\tR\x06taskId\x12A\n" +
	"\x05state\x18\x02 \x01(\x0e2&.backend.proto.onboarding.v1.TaskStateB\x03\xe0A\x03R\x05state\x12(\n" +
	"\rerror_message\x18\x03 \x01(\tH\x00R\ferrorMessage\x88\x01\x01B\x10\n" +
	"\x0e_error_message\"\xdc\x02\n" +
	")UpdateGenerateBookingPageTaskStateRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tcompanyId\x12(\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"businessId\x12'\n" +
	"\bstaff_id\x18\x03 \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x00R\astaffId\x88\x01\x01\x12 \n" +
	"\atask_id\x18\x04 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x06taskId\x12I\n" +
	"\x05state\x18\x05 \x01(\x0e2&.backend.proto.onboarding.v1.TaskStateB\v\xe0A\x03\xbaH\x05\x82\x01\x02\x10\x01R\x05state\x12(\n" +
	"\rerror_message\x18\x06 \x01(\tH\x01R\ferrorMessage\x88\x01\x01B\v\n" +
	"\t_staff_idB\x10\n" +
	"\x0e_error_message*\x8b\x01\n" +
	"\tTaskState\x12\x1a\n" +
	"\x16TASK_STATE_UNSPECIFIED\x10\x00\x12\x15\n" +
	"\x11TASK_STATE_QUEUED\x10\x01\x12\x1a\n" +
	"\x16TASK_STATE_IN_PROGRESS\x10\x02\x12\x18\n" +
	"\x14TASK_STATE_COMPLETED\x10\x03\x12\x15\n" +
	"\x11TASK_STATE_FAILED\x10\x04*]\n" +
	"\tFlowState\x12\x1a\n" +
	"\x16FLOW_STATE_UNSPECIFIED\x10\x00\x12\x1a\n" +
	"\x16FLOW_STATE_IN_PROGRESS\x10\x01\x12\x18\n" +
	"\x14FLOW_STATE_COMPLETED\x10\x02*u\n" +
	"\tStepState\x12\x1a\n" +
	"\x16STEP_STATE_UNSPECIFIED\x10\x00\x12\x1a\n" +
	"\x16STEP_STATE_IN_PROGRESS\x10\x01\x12\x18\n" +
	"\x14STEP_STATE_COMPLETED\x10\x02\x12\x16\n" +
	"\x12STEP_STATE_SKIPPED\x10\x03*p\n" +
	"\n" +
	"SourceType\x12\x1b\n" +
	"\x17SOURCE_TYPE_UNSPECIFIED\x10\x00\x12\a\n" +
	"\x03CSV\x10\x01\x12\t\n" +
	"\x05IMAGE\x10\x02\x12\a\n" +
	"\x03URL\x10\x03\x12\n" +
	"\n" +
	"\x06STRING\x10\x04\x12\a\n" +
	"\x03PDF\x10\x05\x12\b\n" +
	"\x04DOCX\x10\x06\x12\t\n" +
	"\x05EXCEL\x10\a*\x8f\x01\n" +
	"\x0fServiceItemType\x12!\n" +
	"\x1dSERVICE_ITEM_TYPE_UNSPECIFIED\x10\x00\x12\f\n" +
	"\bGROOMING\x10\x01\x12\f\n" +
	"\bBOARDING\x10\x02\x12\v\n" +
	"\aDAYCARE\x10\x03\x12\x0e\n" +
	"\n" +
	"EVALUATION\x10\x04\x12\x0f\n" +
	"\vDOG_WALKING\x10\x05\x12\x0f\n" +
	"\vGROUP_CLASS\x10\x06*\xa9\x02\n" +
	"\aErrCode\x12\x0f\n" +
	"\vERR_CODE_OK\x10\x00\x12\x1a\n" +
	"\x14ERR_CODE_UNSPECIFIED\x10\xa8\xfc\a\x12\x1d\n" +
	"\x17ERR_CODE_TASK_NOT_FOUND\x10\xa9\xfc\a\x12\"\n" +
	"\x1cERR_CODE_TASK_ALREADY_EXISTS\x10\xaa\xfc\a\x12#\n" +
	"\x1dERR_CODE_TASK_NOT_IN_PROGRESS\x10\xab\xfc\a\x12!\n" +
	"\x1bERR_CODE_TASK_NOT_COMPLETED\x10\xac\xfc\a\x12\x1e\n" +
	"\x18ERR_CODE_TASK_NOT_QUEUED\x10\xad\xfc\a\x12\"\n" +
	"\x1cERR_CODE_TASK_ALREADY_QUEUED\x10\xae\xfc\a\x12\"\n" +
	"\x1cERR_CODE_TASK_STATUS_INVALID\x10\xaf\xfc\a2\xf6\x06\n" +
	"\x11OnboardingService\x12\x83\x01\n" +
	"\x15GetOnboardingProgress\x129.backend.proto.onboarding.v1.GetOnboardingProgressRequest\x1a/.backend.proto.onboarding.v1.OnboardingProgress\x12\x89\x01\n" +
	"\x18UpdateOnboardingProgress\x12<.backend.proto.onboarding.v1.UpdateOnboardingProgressRequest\x1a/.backend.proto.onboarding.v1.OnboardingProgress\x12v\n" +
	"\rParseServices\x121.backend.proto.onboarding.v1.ParseServicesRequest\x1a2.backend.proto.onboarding.v1.ParseServicesResponse\x12\x88\x01\n" +
	"\x13GenerateBookingPage\x127.backend.proto.onboarding.v1.GenerateBookingPageRequest\x1a8.backend.proto.onboarding.v1.GenerateBookingPageResponse\x12\xa1\x01\n" +
	"\x1fGetGenerateBookingPageTaskState\x12C.backend.proto.onboarding.v1.GetGenerateBookingPageTaskStateRequest\x1a9.backend.proto.onboarding.v1.GenerateBookingPageTaskState\x12\xa7\x01\n" +
	"\"UpdateGenerateBookingPageTaskState\x12F.backend.proto.onboarding.v1.UpdateGenerateBookingPageTaskStateRequest\x1a9.backend.proto.onboarding.v1.GenerateBookingPageTaskStateBq\n" +
	"%com.moego.backend.proto.onboarding.v1P\x01ZFgithub.com/MoeGolibrary/moego/backend/proto/onboarding/v1;onboardingpbb\x06proto3"

var (
	file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescOnce sync.Once
	file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescData []byte
)

func file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescGZIP() []byte {
	file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_onboarding_v1_onboarding_service_proto_rawDesc), len(file_backend_proto_onboarding_v1_onboarding_service_proto_rawDesc)))
	})
	return file_backend_proto_onboarding_v1_onboarding_service_proto_rawDescData
}

var file_backend_proto_onboarding_v1_onboarding_service_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_backend_proto_onboarding_v1_onboarding_service_proto_goTypes = []any{
	(TaskState)(0),                                       // 0: backend.proto.onboarding.v1.TaskState
	(FlowState)(0),                                       // 1: backend.proto.onboarding.v1.FlowState
	(StepState)(0),                                       // 2: backend.proto.onboarding.v1.StepState
	(SourceType)(0),                                      // 3: backend.proto.onboarding.v1.SourceType
	(ServiceItemType)(0),                                 // 4: backend.proto.onboarding.v1.ServiceItemType
	(ErrCode)(0),                                         // 5: backend.proto.onboarding.v1.ErrCode
	(*OnboardingProgress)(nil),                           // 6: backend.proto.onboarding.v1.OnboardingProgress
	(*StepProgress)(nil),                                 // 7: backend.proto.onboarding.v1.StepProgress
	(*Source)(nil),                                       // 8: backend.proto.onboarding.v1.Source
	(*GetOnboardingProgressRequest)(nil),                 // 9: backend.proto.onboarding.v1.GetOnboardingProgressRequest
	(*UpdateOnboardingProgressRequest)(nil),              // 10: backend.proto.onboarding.v1.UpdateOnboardingProgressRequest
	(*ParseServicesRequest)(nil),                         // 11: backend.proto.onboarding.v1.ParseServicesRequest
	(*ParseServicesResponse)(nil),                        // 12: backend.proto.onboarding.v1.ParseServicesResponse
	(*GenerateBookingPageRequest)(nil),                   // 13: backend.proto.onboarding.v1.GenerateBookingPageRequest
	(*GenerateBookingPageResponse)(nil),                  // 14: backend.proto.onboarding.v1.GenerateBookingPageResponse
	(*GetGenerateBookingPageTaskStateRequest)(nil),       // 15: backend.proto.onboarding.v1.GetGenerateBookingPageTaskStateRequest
	(*GenerateBookingPageTaskState)(nil),                 // 16: backend.proto.onboarding.v1.GenerateBookingPageTaskState
	(*UpdateGenerateBookingPageTaskStateRequest)(nil),    // 17: backend.proto.onboarding.v1.UpdateGenerateBookingPageTaskStateRequest
	(*UpdateOnboardingProgressRequest_StepUpdate)(nil),   // 18: backend.proto.onboarding.v1.UpdateOnboardingProgressRequest.StepUpdate
	(*ParseServicesResponse_Category)(nil),               // 19: backend.proto.onboarding.v1.ParseServicesResponse.Category
	(*ParseServicesResponse_PriceTax)(nil),               // 20: backend.proto.onboarding.v1.ParseServicesResponse.PriceTax
	(*ParseServicesResponse_PetType)(nil),                // 21: backend.proto.onboarding.v1.ParseServicesResponse.PetType
	(*ParseServicesResponse_PetBreed)(nil),               // 22: backend.proto.onboarding.v1.ParseServicesResponse.PetBreed
	(*ParseServicesResponse_PetSize)(nil),                // 23: backend.proto.onboarding.v1.ParseServicesResponse.PetSize
	(*ParseServicesResponse_PetCoatType)(nil),            // 24: backend.proto.onboarding.v1.ParseServicesResponse.PetCoatType
	(*ParseServicesResponse_ServicePetTypeAndBreed)(nil), // 25: backend.proto.onboarding.v1.ParseServicesResponse.ServicePetTypeAndBreed
	(*ParseServicesResponse_Service)(nil),                // 26: backend.proto.onboarding.v1.ParseServicesResponse.Service
	(*timestamppb.Timestamp)(nil),                        // 27: google.protobuf.Timestamp
	(*structpb.Struct)(nil),                              // 28: google.protobuf.Struct
}
var file_backend_proto_onboarding_v1_onboarding_service_proto_depIdxs = []int32{
	7,  // 0: backend.proto.onboarding.v1.OnboardingProgress.steps:type_name -> backend.proto.onboarding.v1.StepProgress
	27, // 1: backend.proto.onboarding.v1.OnboardingProgress.update_time:type_name -> google.protobuf.Timestamp
	27, // 2: backend.proto.onboarding.v1.OnboardingProgress.create_time:type_name -> google.protobuf.Timestamp
	1,  // 3: backend.proto.onboarding.v1.OnboardingProgress.state:type_name -> backend.proto.onboarding.v1.FlowState
	28, // 4: backend.proto.onboarding.v1.OnboardingProgress.metadata:type_name -> google.protobuf.Struct
	2,  // 5: backend.proto.onboarding.v1.StepProgress.state:type_name -> backend.proto.onboarding.v1.StepState
	28, // 6: backend.proto.onboarding.v1.StepProgress.metadata:type_name -> google.protobuf.Struct
	27, // 7: backend.proto.onboarding.v1.StepProgress.start_time:type_name -> google.protobuf.Timestamp
	27, // 8: backend.proto.onboarding.v1.StepProgress.complete_time:type_name -> google.protobuf.Timestamp
	3,  // 9: backend.proto.onboarding.v1.Source.type:type_name -> backend.proto.onboarding.v1.SourceType
	18, // 10: backend.proto.onboarding.v1.UpdateOnboardingProgressRequest.steps:type_name -> backend.proto.onboarding.v1.UpdateOnboardingProgressRequest.StepUpdate
	1,  // 11: backend.proto.onboarding.v1.UpdateOnboardingProgressRequest.flow_state:type_name -> backend.proto.onboarding.v1.FlowState
	28, // 12: backend.proto.onboarding.v1.UpdateOnboardingProgressRequest.metadata:type_name -> google.protobuf.Struct
	8,  // 13: backend.proto.onboarding.v1.ParseServicesRequest.sources:type_name -> backend.proto.onboarding.v1.Source
	26, // 14: backend.proto.onboarding.v1.ParseServicesResponse.services:type_name -> backend.proto.onboarding.v1.ParseServicesResponse.Service
	19, // 15: backend.proto.onboarding.v1.ParseServicesResponse.category_list:type_name -> backend.proto.onboarding.v1.ParseServicesResponse.Category
	20, // 16: backend.proto.onboarding.v1.ParseServicesResponse.price_tax_list:type_name -> backend.proto.onboarding.v1.ParseServicesResponse.PriceTax
	21, // 17: backend.proto.onboarding.v1.ParseServicesResponse.pet_type_list:type_name -> backend.proto.onboarding.v1.ParseServicesResponse.PetType
	22, // 18: backend.proto.onboarding.v1.ParseServicesResponse.pet_breed_list:type_name -> backend.proto.onboarding.v1.ParseServicesResponse.PetBreed
	23, // 19: backend.proto.onboarding.v1.ParseServicesResponse.pet_size_list:type_name -> backend.proto.onboarding.v1.ParseServicesResponse.PetSize
	24, // 20: backend.proto.onboarding.v1.ParseServicesResponse.pet_coat_type_list:type_name -> backend.proto.onboarding.v1.ParseServicesResponse.PetCoatType
	0,  // 21: backend.proto.onboarding.v1.GenerateBookingPageResponse.state:type_name -> backend.proto.onboarding.v1.TaskState
	0,  // 22: backend.proto.onboarding.v1.GenerateBookingPageTaskState.state:type_name -> backend.proto.onboarding.v1.TaskState
	0,  // 23: backend.proto.onboarding.v1.UpdateGenerateBookingPageTaskStateRequest.state:type_name -> backend.proto.onboarding.v1.TaskState
	2,  // 24: backend.proto.onboarding.v1.UpdateOnboardingProgressRequest.StepUpdate.state:type_name -> backend.proto.onboarding.v1.StepState
	28, // 25: backend.proto.onboarding.v1.UpdateOnboardingProgressRequest.StepUpdate.metadata:type_name -> google.protobuf.Struct
	4,  // 26: backend.proto.onboarding.v1.ParseServicesResponse.Service.service_item_type:type_name -> backend.proto.onboarding.v1.ServiceItemType
	25, // 27: backend.proto.onboarding.v1.ParseServicesResponse.Service.type_and_breed_list:type_name -> backend.proto.onboarding.v1.ParseServicesResponse.ServicePetTypeAndBreed
	9,  // 28: backend.proto.onboarding.v1.OnboardingService.GetOnboardingProgress:input_type -> backend.proto.onboarding.v1.GetOnboardingProgressRequest
	10, // 29: backend.proto.onboarding.v1.OnboardingService.UpdateOnboardingProgress:input_type -> backend.proto.onboarding.v1.UpdateOnboardingProgressRequest
	11, // 30: backend.proto.onboarding.v1.OnboardingService.ParseServices:input_type -> backend.proto.onboarding.v1.ParseServicesRequest
	13, // 31: backend.proto.onboarding.v1.OnboardingService.GenerateBookingPage:input_type -> backend.proto.onboarding.v1.GenerateBookingPageRequest
	15, // 32: backend.proto.onboarding.v1.OnboardingService.GetGenerateBookingPageTaskState:input_type -> backend.proto.onboarding.v1.GetGenerateBookingPageTaskStateRequest
	17, // 33: backend.proto.onboarding.v1.OnboardingService.UpdateGenerateBookingPageTaskState:input_type -> backend.proto.onboarding.v1.UpdateGenerateBookingPageTaskStateRequest
	6,  // 34: backend.proto.onboarding.v1.OnboardingService.GetOnboardingProgress:output_type -> backend.proto.onboarding.v1.OnboardingProgress
	6,  // 35: backend.proto.onboarding.v1.OnboardingService.UpdateOnboardingProgress:output_type -> backend.proto.onboarding.v1.OnboardingProgress
	12, // 36: backend.proto.onboarding.v1.OnboardingService.ParseServices:output_type -> backend.proto.onboarding.v1.ParseServicesResponse
	14, // 37: backend.proto.onboarding.v1.OnboardingService.GenerateBookingPage:output_type -> backend.proto.onboarding.v1.GenerateBookingPageResponse
	16, // 38: backend.proto.onboarding.v1.OnboardingService.GetGenerateBookingPageTaskState:output_type -> backend.proto.onboarding.v1.GenerateBookingPageTaskState
	16, // 39: backend.proto.onboarding.v1.OnboardingService.UpdateGenerateBookingPageTaskState:output_type -> backend.proto.onboarding.v1.GenerateBookingPageTaskState
	34, // [34:40] is the sub-list for method output_type
	28, // [28:34] is the sub-list for method input_type
	28, // [28:28] is the sub-list for extension type_name
	28, // [28:28] is the sub-list for extension extendee
	0,  // [0:28] is the sub-list for field type_name
}

func init() { file_backend_proto_onboarding_v1_onboarding_service_proto_init() }
func file_backend_proto_onboarding_v1_onboarding_service_proto_init() {
	if File_backend_proto_onboarding_v1_onboarding_service_proto != nil {
		return
	}
	file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[4].OneofWrappers = []any{}
	file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[5].OneofWrappers = []any{}
	file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[7].OneofWrappers = []any{}
	file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[9].OneofWrappers = []any{}
	file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[10].OneofWrappers = []any{}
	file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes[11].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_onboarding_v1_onboarding_service_proto_rawDesc), len(file_backend_proto_onboarding_v1_onboarding_service_proto_rawDesc)),
			NumEnums:      6,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_onboarding_v1_onboarding_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_onboarding_v1_onboarding_service_proto_depIdxs,
		EnumInfos:         file_backend_proto_onboarding_v1_onboarding_service_proto_enumTypes,
		MessageInfos:      file_backend_proto_onboarding_v1_onboarding_service_proto_msgTypes,
	}.Build()
	File_backend_proto_onboarding_v1_onboarding_service_proto = out.File
	file_backend_proto_onboarding_v1_onboarding_service_proto_goTypes = nil
	file_backend_proto_onboarding_v1_onboarding_service_proto_depIdxs = nil
}
