// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/onboarding/v1/onboarding_service.proto

package onboardingpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	OnboardingService_GetOnboardingProgress_FullMethodName    = "/backend.proto.onboarding.v1.OnboardingService/GetOnboardingProgress"
	OnboardingService_UpdateOnboardingProgress_FullMethodName = "/backend.proto.onboarding.v1.OnboardingService/UpdateOnboardingProgress"
	OnboardingService_ParseServices_FullMethodName            = "/backend.proto.onboarding.v1.OnboardingService/ParseServices"
	OnboardingService_GenerateBookingPage_FullMethodName      = "/backend.proto.onboarding.v1.OnboardingService/GenerateBookingPage"
)

// OnboardingServiceClient is the client API for OnboardingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// OnboardingService defines the gRPC service for managing the onboarding process.
type OnboardingServiceClient interface {
	// GetOnboardingProgress retrieves the current progress of an onboarding flow.
	GetOnboardingProgress(ctx context.Context, in *GetOnboardingProgressRequest, opts ...grpc.CallOption) (*OnboardingProgress, error)
	// UpdateOnboardingProgress updates the status of a step in an onboarding flow.
	UpdateOnboardingProgress(ctx context.Context, in *UpdateOnboardingProgressRequest, opts ...grpc.CallOption) (*OnboardingProgress, error)
	// ParseServices parses a list of services from various sources.
	// This RPC will proxy the request to the AI Service.
	ParseServices(ctx context.Context, in *ParseServicesRequest, opts ...grpc.CallOption) (*ParseServicesResponse, error)
	// GenerateBookingPage generates a configuration for an online booking page.
	// This RPC will proxy the aistudio to the AI Service.
	GenerateBookingPage(ctx context.Context, in *GenerateBookingPageRequest, opts ...grpc.CallOption) (*GenerateBookingPageResponse, error)
}

type onboardingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOnboardingServiceClient(cc grpc.ClientConnInterface) OnboardingServiceClient {
	return &onboardingServiceClient{cc}
}

func (c *onboardingServiceClient) GetOnboardingProgress(ctx context.Context, in *GetOnboardingProgressRequest, opts ...grpc.CallOption) (*OnboardingProgress, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OnboardingProgress)
	err := c.cc.Invoke(ctx, OnboardingService_GetOnboardingProgress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingServiceClient) UpdateOnboardingProgress(ctx context.Context, in *UpdateOnboardingProgressRequest, opts ...grpc.CallOption) (*OnboardingProgress, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OnboardingProgress)
	err := c.cc.Invoke(ctx, OnboardingService_UpdateOnboardingProgress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingServiceClient) ParseServices(ctx context.Context, in *ParseServicesRequest, opts ...grpc.CallOption) (*ParseServicesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ParseServicesResponse)
	err := c.cc.Invoke(ctx, OnboardingService_ParseServices_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingServiceClient) GenerateBookingPage(ctx context.Context, in *GenerateBookingPageRequest, opts ...grpc.CallOption) (*GenerateBookingPageResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GenerateBookingPageResponse)
	err := c.cc.Invoke(ctx, OnboardingService_GenerateBookingPage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OnboardingServiceServer is the server API for OnboardingService service.
// All implementations must embed UnimplementedOnboardingServiceServer
// for forward compatibility.
//
// OnboardingService defines the gRPC service for managing the onboarding process.
type OnboardingServiceServer interface {
	// GetOnboardingProgress retrieves the current progress of an onboarding flow.
	GetOnboardingProgress(context.Context, *GetOnboardingProgressRequest) (*OnboardingProgress, error)
	// UpdateOnboardingProgress updates the status of a step in an onboarding flow.
	UpdateOnboardingProgress(context.Context, *UpdateOnboardingProgressRequest) (*OnboardingProgress, error)
	// ParseServices parses a list of services from various sources.
	// This RPC will proxy the request to the AI Service.
	ParseServices(context.Context, *ParseServicesRequest) (*ParseServicesResponse, error)
	// GenerateBookingPage generates a configuration for an online booking page.
	// This RPC will proxy the aistudio to the AI Service.
	GenerateBookingPage(context.Context, *GenerateBookingPageRequest) (*GenerateBookingPageResponse, error)
	mustEmbedUnimplementedOnboardingServiceServer()
}

// UnimplementedOnboardingServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedOnboardingServiceServer struct{}

func (UnimplementedOnboardingServiceServer) GetOnboardingProgress(context.Context, *GetOnboardingProgressRequest) (*OnboardingProgress, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOnboardingProgress not implemented")
}
func (UnimplementedOnboardingServiceServer) UpdateOnboardingProgress(context.Context, *UpdateOnboardingProgressRequest) (*OnboardingProgress, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateOnboardingProgress not implemented")
}
func (UnimplementedOnboardingServiceServer) ParseServices(context.Context, *ParseServicesRequest) (*ParseServicesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ParseServices not implemented")
}
func (UnimplementedOnboardingServiceServer) GenerateBookingPage(context.Context, *GenerateBookingPageRequest) (*GenerateBookingPageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateBookingPage not implemented")
}
func (UnimplementedOnboardingServiceServer) mustEmbedUnimplementedOnboardingServiceServer() {}
func (UnimplementedOnboardingServiceServer) testEmbeddedByValue()                           {}

// UnsafeOnboardingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OnboardingServiceServer will
// result in compilation errors.
type UnsafeOnboardingServiceServer interface {
	mustEmbedUnimplementedOnboardingServiceServer()
}

func RegisterOnboardingServiceServer(s grpc.ServiceRegistrar, srv OnboardingServiceServer) {
	// If the following call pancis, it indicates UnimplementedOnboardingServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&OnboardingService_ServiceDesc, srv)
}

func _OnboardingService_GetOnboardingProgress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOnboardingProgressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServiceServer).GetOnboardingProgress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OnboardingService_GetOnboardingProgress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServiceServer).GetOnboardingProgress(ctx, req.(*GetOnboardingProgressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OnboardingService_UpdateOnboardingProgress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateOnboardingProgressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServiceServer).UpdateOnboardingProgress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OnboardingService_UpdateOnboardingProgress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServiceServer).UpdateOnboardingProgress(ctx, req.(*UpdateOnboardingProgressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OnboardingService_ParseServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ParseServicesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServiceServer).ParseServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OnboardingService_ParseServices_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServiceServer).ParseServices(ctx, req.(*ParseServicesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OnboardingService_GenerateBookingPage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateBookingPageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServiceServer).GenerateBookingPage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OnboardingService_GenerateBookingPage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServiceServer).GenerateBookingPage(ctx, req.(*GenerateBookingPageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// OnboardingService_ServiceDesc is the grpc.ServiceDesc for OnboardingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OnboardingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.onboarding.v1.OnboardingService",
	HandlerType: (*OnboardingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetOnboardingProgress",
			Handler:    _OnboardingService_GetOnboardingProgress_Handler,
		},
		{
			MethodName: "UpdateOnboardingProgress",
			Handler:    _OnboardingService_UpdateOnboardingProgress_Handler,
		},
		{
			MethodName: "ParseServices",
			Handler:    _OnboardingService_ParseServices_Handler,
		},
		{
			MethodName: "GenerateBookingPage",
			Handler:    _OnboardingService_GenerateBookingPage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/onboarding/v1/onboarding_service.proto",
}
