// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.8
// 	protoc        (unknown)
// source: backend/proto/tools/v1/staging_service.proto

package toolspb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// RestoreDatabasesRequest
type RestoreDatabasesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RestoreDatabasesRequest) Reset() {
	*x = RestoreDatabasesRequest{}
	mi := &file_backend_proto_tools_v1_staging_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RestoreDatabasesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RestoreDatabasesRequest) ProtoMessage() {}

func (x *RestoreDatabasesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_staging_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RestoreDatabasesRequest.ProtoReflect.Descriptor instead.
func (*RestoreDatabasesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_staging_service_proto_rawDescGZIP(), []int{0}
}

// RestoreDatabasesResponse
type RestoreDatabasesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// restored databases
	Databases     []*Database `protobuf:"bytes,1,rep,name=databases,proto3" json:"databases,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RestoreDatabasesResponse) Reset() {
	*x = RestoreDatabasesResponse{}
	mi := &file_backend_proto_tools_v1_staging_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RestoreDatabasesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RestoreDatabasesResponse) ProtoMessage() {}

func (x *RestoreDatabasesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_staging_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RestoreDatabasesResponse.ProtoReflect.Descriptor instead.
func (*RestoreDatabasesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_staging_service_proto_rawDescGZIP(), []int{1}
}

func (x *RestoreDatabasesResponse) GetDatabases() []*Database {
	if x != nil {
		return x.Databases
	}
	return nil
}

// ResetDatabaseRequest
type ResetDatabaseRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// database identifier
	Identifier *PlatformIdentifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// database username
	Username *string `protobuf:"bytes,2,opt,name=username,proto3,oneof" json:"username,omitempty"`
	// database username
	Password *string `protobuf:"bytes,3,opt,name=password,proto3,oneof" json:"password,omitempty"`
	// list of sql requests
	SqlRequests   []*ResetDatabaseRequest_SqlRequest `protobuf:"bytes,8,rep,name=sql_requests,json=sqlRequests,proto3" json:"sql_requests,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResetDatabaseRequest) Reset() {
	*x = ResetDatabaseRequest{}
	mi := &file_backend_proto_tools_v1_staging_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResetDatabaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetDatabaseRequest) ProtoMessage() {}

func (x *ResetDatabaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_staging_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetDatabaseRequest.ProtoReflect.Descriptor instead.
func (*ResetDatabaseRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_staging_service_proto_rawDescGZIP(), []int{2}
}

func (x *ResetDatabaseRequest) GetIdentifier() *PlatformIdentifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

func (x *ResetDatabaseRequest) GetUsername() string {
	if x != nil && x.Username != nil {
		return *x.Username
	}
	return ""
}

func (x *ResetDatabaseRequest) GetPassword() string {
	if x != nil && x.Password != nil {
		return *x.Password
	}
	return ""
}

func (x *ResetDatabaseRequest) GetSqlRequests() []*ResetDatabaseRequest_SqlRequest {
	if x != nil {
		return x.SqlRequests
	}
	return nil
}

// ResetDatabaseResponse
type ResetDatabaseResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// executed sql
	Sql           []string `protobuf:"bytes,1,rep,name=sql,proto3" json:"sql,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResetDatabaseResponse) Reset() {
	*x = ResetDatabaseResponse{}
	mi := &file_backend_proto_tools_v1_staging_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResetDatabaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetDatabaseResponse) ProtoMessage() {}

func (x *ResetDatabaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_staging_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetDatabaseResponse.ProtoReflect.Descriptor instead.
func (*ResetDatabaseResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_staging_service_proto_rawDescGZIP(), []int{3}
}

func (x *ResetDatabaseResponse) GetSql() []string {
	if x != nil {
		return x.Sql
	}
	return nil
}

// FlushSecretsRequest
type FlushSecretsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// secrets
	Secrets       []*FlushSecretsRequest_Secret `protobuf:"bytes,1,rep,name=secrets,proto3" json:"secrets,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FlushSecretsRequest) Reset() {
	*x = FlushSecretsRequest{}
	mi := &file_backend_proto_tools_v1_staging_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FlushSecretsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlushSecretsRequest) ProtoMessage() {}

func (x *FlushSecretsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_staging_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlushSecretsRequest.ProtoReflect.Descriptor instead.
func (*FlushSecretsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_staging_service_proto_rawDescGZIP(), []int{4}
}

func (x *FlushSecretsRequest) GetSecrets() []*FlushSecretsRequest_Secret {
	if x != nil {
		return x.Secrets
	}
	return nil
}

// FlushSecretsResponse
type FlushSecretsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// changed secrets key count
	Changed       map[string]int32 `protobuf:"bytes,1,rep,name=changed,proto3" json:"changed,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FlushSecretsResponse) Reset() {
	*x = FlushSecretsResponse{}
	mi := &file_backend_proto_tools_v1_staging_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FlushSecretsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlushSecretsResponse) ProtoMessage() {}

func (x *FlushSecretsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_staging_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlushSecretsResponse.ProtoReflect.Descriptor instead.
func (*FlushSecretsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_staging_service_proto_rawDescGZIP(), []int{5}
}

func (x *FlushSecretsResponse) GetChanged() map[string]int32 {
	if x != nil {
		return x.Changed
	}
	return nil
}

// FlushRoutesRequest
type FlushRoutesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FlushRoutesRequest) Reset() {
	*x = FlushRoutesRequest{}
	mi := &file_backend_proto_tools_v1_staging_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FlushRoutesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlushRoutesRequest) ProtoMessage() {}

func (x *FlushRoutesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_staging_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlushRoutesRequest.ProtoReflect.Descriptor instead.
func (*FlushRoutesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_staging_service_proto_rawDescGZIP(), []int{6}
}

// FlushRoutesResponse
type FlushRoutesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// flushed route names
	Names         []string `protobuf:"bytes,1,rep,name=names,proto3" json:"names,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FlushRoutesResponse) Reset() {
	*x = FlushRoutesResponse{}
	mi := &file_backend_proto_tools_v1_staging_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FlushRoutesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlushRoutesResponse) ProtoMessage() {}

func (x *FlushRoutesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_staging_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlushRoutesResponse.ProtoReflect.Descriptor instead.
func (*FlushRoutesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_staging_service_proto_rawDescGZIP(), []int{7}
}

func (x *FlushRoutesResponse) GetNames() []string {
	if x != nil {
		return x.Names
	}
	return nil
}

// FlushDatabasesRequest
type FlushDatabasesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// database identifiers
	Databases     []*PlatformIdentifier `protobuf:"bytes,1,rep,name=databases,proto3" json:"databases,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FlushDatabasesRequest) Reset() {
	*x = FlushDatabasesRequest{}
	mi := &file_backend_proto_tools_v1_staging_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FlushDatabasesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlushDatabasesRequest) ProtoMessage() {}

func (x *FlushDatabasesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_staging_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlushDatabasesRequest.ProtoReflect.Descriptor instead.
func (*FlushDatabasesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_staging_service_proto_rawDescGZIP(), []int{8}
}

func (x *FlushDatabasesRequest) GetDatabases() []*PlatformIdentifier {
	if x != nil {
		return x.Databases
	}
	return nil
}

// FlushDatabasesResponse
type FlushDatabasesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// deleted database count
	Deleted int32 `protobuf:"varint,1,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// inserted database count
	Inserted      int32 `protobuf:"varint,2,opt,name=inserted,proto3" json:"inserted,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FlushDatabasesResponse) Reset() {
	*x = FlushDatabasesResponse{}
	mi := &file_backend_proto_tools_v1_staging_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FlushDatabasesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlushDatabasesResponse) ProtoMessage() {}

func (x *FlushDatabasesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_staging_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlushDatabasesResponse.ProtoReflect.Descriptor instead.
func (*FlushDatabasesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_staging_service_proto_rawDescGZIP(), []int{9}
}

func (x *FlushDatabasesResponse) GetDeleted() int32 {
	if x != nil {
		return x.Deleted
	}
	return 0
}

func (x *FlushDatabasesResponse) GetInserted() int32 {
	if x != nil {
		return x.Inserted
	}
	return 0
}

// SqlRequest
type ResetDatabaseRequest_SqlRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// database to execute sql
	Database string `protobuf:"bytes,1,opt,name=database,proto3" json:"database,omitempty"`
	// list of sql statements
	Sql           []string `protobuf:"bytes,2,rep,name=sql,proto3" json:"sql,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResetDatabaseRequest_SqlRequest) Reset() {
	*x = ResetDatabaseRequest_SqlRequest{}
	mi := &file_backend_proto_tools_v1_staging_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResetDatabaseRequest_SqlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetDatabaseRequest_SqlRequest) ProtoMessage() {}

func (x *ResetDatabaseRequest_SqlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_staging_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetDatabaseRequest_SqlRequest.ProtoReflect.Descriptor instead.
func (*ResetDatabaseRequest_SqlRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_staging_service_proto_rawDescGZIP(), []int{2, 0}
}

func (x *ResetDatabaseRequest_SqlRequest) GetDatabase() string {
	if x != nil {
		return x.Database
	}
	return ""
}

func (x *ResetDatabaseRequest_SqlRequest) GetSql() []string {
	if x != nil {
		return x.Sql
	}
	return nil
}

// Secret
type FlushSecretsRequest_Secret struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// secret name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// secret values
	Secrets       map[string]string `protobuf:"bytes,2,rep,name=secrets,proto3" json:"secrets,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FlushSecretsRequest_Secret) Reset() {
	*x = FlushSecretsRequest_Secret{}
	mi := &file_backend_proto_tools_v1_staging_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FlushSecretsRequest_Secret) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlushSecretsRequest_Secret) ProtoMessage() {}

func (x *FlushSecretsRequest_Secret) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_staging_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlushSecretsRequest_Secret.ProtoReflect.Descriptor instead.
func (*FlushSecretsRequest_Secret) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_staging_service_proto_rawDescGZIP(), []int{4, 0}
}

func (x *FlushSecretsRequest_Secret) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FlushSecretsRequest_Secret) GetSecrets() map[string]string {
	if x != nil {
		return x.Secrets
	}
	return nil
}

var File_backend_proto_tools_v1_staging_service_proto protoreflect.FileDescriptor

const file_backend_proto_tools_v1_staging_service_proto_rawDesc = "" +
	"\n" +
	",backend/proto/tools/v1/staging_service.proto\x12\x16backend.proto.tools.v1\x1a,backend/proto/tools/v1/resource_models.proto\"\x19\n" +
	"\x17RestoreDatabasesRequest\"Z\n" +
	"\x18RestoreDatabasesResponse\x12>\n" +
	"\tdatabases\x18\x01 \x03(\v2 .backend.proto.tools.v1.DatabaseR\tdatabases\"\xd6\x02\n" +
	"\x14ResetDatabaseRequest\x12J\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2*.backend.proto.tools.v1.PlatformIdentifierR\n" +
	"identifier\x12\x1f\n" +
	"\busername\x18\x02 \x01(\tH\x00R\busername\x88\x01\x01\x12\x1f\n" +
	"\bpassword\x18\x03 \x01(\tH\x01R\bpassword\x88\x01\x01\x12Z\n" +
	"\fsql_requests\x18\b \x03(\v27.backend.proto.tools.v1.ResetDatabaseRequest.SqlRequestR\vsqlRequests\x1a:\n" +
	"\n" +
	"SqlRequest\x12\x1a\n" +
	"\bdatabase\x18\x01 \x01(\tR\bdatabase\x12\x10\n" +
	"\x03sql\x18\x02 \x03(\tR\x03sqlB\v\n" +
	"\t_usernameB\v\n" +
	"\t_password\")\n" +
	"\x15ResetDatabaseResponse\x12\x10\n" +
	"\x03sql\x18\x01 \x03(\tR\x03sql\"\x99\x02\n" +
	"\x13FlushSecretsRequest\x12L\n" +
	"\asecrets\x18\x01 \x03(\v22.backend.proto.tools.v1.FlushSecretsRequest.SecretR\asecrets\x1a\xb3\x01\n" +
	"\x06Secret\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12Y\n" +
	"\asecrets\x18\x02 \x03(\v2?.backend.proto.tools.v1.FlushSecretsRequest.Secret.SecretsEntryR\asecrets\x1a:\n" +
	"\fSecretsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xa7\x01\n" +
	"\x14FlushSecretsResponse\x12S\n" +
	"\achanged\x18\x01 \x03(\v29.backend.proto.tools.v1.FlushSecretsResponse.ChangedEntryR\achanged\x1a:\n" +
	"\fChangedEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x05R\x05value:\x028\x01\"\x14\n" +
	"\x12FlushRoutesRequest\"+\n" +
	"\x13FlushRoutesResponse\x12\x14\n" +
	"\x05names\x18\x01 \x03(\tR\x05names\"a\n" +
	"\x15FlushDatabasesRequest\x12H\n" +
	"\tdatabases\x18\x01 \x03(\v2*.backend.proto.tools.v1.PlatformIdentifierR\tdatabases\"N\n" +
	"\x16FlushDatabasesResponse\x12\x18\n" +
	"\adeleted\x18\x01 \x01(\x05R\adeleted\x12\x1a\n" +
	"\binserted\x18\x02 \x01(\x05R\binserted2\xb9\x04\n" +
	"\x0eStagingService\x12u\n" +
	"\x10RestoreDatabases\x12/.backend.proto.tools.v1.RestoreDatabasesRequest\x1a0.backend.proto.tools.v1.RestoreDatabasesResponse\x12l\n" +
	"\rResetDatabase\x12,.backend.proto.tools.v1.ResetDatabaseRequest\x1a-.backend.proto.tools.v1.ResetDatabaseResponse\x12i\n" +
	"\fFlushSecrets\x12+.backend.proto.tools.v1.FlushSecretsRequest\x1a,.backend.proto.tools.v1.FlushSecretsResponse\x12f\n" +
	"\vFlushRoutes\x12*.backend.proto.tools.v1.FlushRoutesRequest\x1a+.backend.proto.tools.v1.FlushRoutesResponse\x12o\n" +
	"\x0eFlushDatabases\x12-.backend.proto.tools.v1.FlushDatabasesRequest\x1a..backend.proto.tools.v1.FlushDatabasesResponseBb\n" +
	" com.moego.backend.proto.tools.v1P\x01Z<github.com/MoeGolibrary/moego/backend/proto/tools/v1;toolspbb\x06proto3"

var (
	file_backend_proto_tools_v1_staging_service_proto_rawDescOnce sync.Once
	file_backend_proto_tools_v1_staging_service_proto_rawDescData []byte
)

func file_backend_proto_tools_v1_staging_service_proto_rawDescGZIP() []byte {
	file_backend_proto_tools_v1_staging_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_tools_v1_staging_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_tools_v1_staging_service_proto_rawDesc), len(file_backend_proto_tools_v1_staging_service_proto_rawDesc)))
	})
	return file_backend_proto_tools_v1_staging_service_proto_rawDescData
}

var file_backend_proto_tools_v1_staging_service_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_backend_proto_tools_v1_staging_service_proto_goTypes = []any{
	(*RestoreDatabasesRequest)(nil),         // 0: backend.proto.tools.v1.RestoreDatabasesRequest
	(*RestoreDatabasesResponse)(nil),        // 1: backend.proto.tools.v1.RestoreDatabasesResponse
	(*ResetDatabaseRequest)(nil),            // 2: backend.proto.tools.v1.ResetDatabaseRequest
	(*ResetDatabaseResponse)(nil),           // 3: backend.proto.tools.v1.ResetDatabaseResponse
	(*FlushSecretsRequest)(nil),             // 4: backend.proto.tools.v1.FlushSecretsRequest
	(*FlushSecretsResponse)(nil),            // 5: backend.proto.tools.v1.FlushSecretsResponse
	(*FlushRoutesRequest)(nil),              // 6: backend.proto.tools.v1.FlushRoutesRequest
	(*FlushRoutesResponse)(nil),             // 7: backend.proto.tools.v1.FlushRoutesResponse
	(*FlushDatabasesRequest)(nil),           // 8: backend.proto.tools.v1.FlushDatabasesRequest
	(*FlushDatabasesResponse)(nil),          // 9: backend.proto.tools.v1.FlushDatabasesResponse
	(*ResetDatabaseRequest_SqlRequest)(nil), // 10: backend.proto.tools.v1.ResetDatabaseRequest.SqlRequest
	(*FlushSecretsRequest_Secret)(nil),      // 11: backend.proto.tools.v1.FlushSecretsRequest.Secret
	nil,                                     // 12: backend.proto.tools.v1.FlushSecretsRequest.Secret.SecretsEntry
	nil,                                     // 13: backend.proto.tools.v1.FlushSecretsResponse.ChangedEntry
	(*Database)(nil),                        // 14: backend.proto.tools.v1.Database
	(*PlatformIdentifier)(nil),              // 15: backend.proto.tools.v1.PlatformIdentifier
}
var file_backend_proto_tools_v1_staging_service_proto_depIdxs = []int32{
	14, // 0: backend.proto.tools.v1.RestoreDatabasesResponse.databases:type_name -> backend.proto.tools.v1.Database
	15, // 1: backend.proto.tools.v1.ResetDatabaseRequest.identifier:type_name -> backend.proto.tools.v1.PlatformIdentifier
	10, // 2: backend.proto.tools.v1.ResetDatabaseRequest.sql_requests:type_name -> backend.proto.tools.v1.ResetDatabaseRequest.SqlRequest
	11, // 3: backend.proto.tools.v1.FlushSecretsRequest.secrets:type_name -> backend.proto.tools.v1.FlushSecretsRequest.Secret
	13, // 4: backend.proto.tools.v1.FlushSecretsResponse.changed:type_name -> backend.proto.tools.v1.FlushSecretsResponse.ChangedEntry
	15, // 5: backend.proto.tools.v1.FlushDatabasesRequest.databases:type_name -> backend.proto.tools.v1.PlatformIdentifier
	12, // 6: backend.proto.tools.v1.FlushSecretsRequest.Secret.secrets:type_name -> backend.proto.tools.v1.FlushSecretsRequest.Secret.SecretsEntry
	0,  // 7: backend.proto.tools.v1.StagingService.RestoreDatabases:input_type -> backend.proto.tools.v1.RestoreDatabasesRequest
	2,  // 8: backend.proto.tools.v1.StagingService.ResetDatabase:input_type -> backend.proto.tools.v1.ResetDatabaseRequest
	4,  // 9: backend.proto.tools.v1.StagingService.FlushSecrets:input_type -> backend.proto.tools.v1.FlushSecretsRequest
	6,  // 10: backend.proto.tools.v1.StagingService.FlushRoutes:input_type -> backend.proto.tools.v1.FlushRoutesRequest
	8,  // 11: backend.proto.tools.v1.StagingService.FlushDatabases:input_type -> backend.proto.tools.v1.FlushDatabasesRequest
	1,  // 12: backend.proto.tools.v1.StagingService.RestoreDatabases:output_type -> backend.proto.tools.v1.RestoreDatabasesResponse
	3,  // 13: backend.proto.tools.v1.StagingService.ResetDatabase:output_type -> backend.proto.tools.v1.ResetDatabaseResponse
	5,  // 14: backend.proto.tools.v1.StagingService.FlushSecrets:output_type -> backend.proto.tools.v1.FlushSecretsResponse
	7,  // 15: backend.proto.tools.v1.StagingService.FlushRoutes:output_type -> backend.proto.tools.v1.FlushRoutesResponse
	9,  // 16: backend.proto.tools.v1.StagingService.FlushDatabases:output_type -> backend.proto.tools.v1.FlushDatabasesResponse
	12, // [12:17] is the sub-list for method output_type
	7,  // [7:12] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_backend_proto_tools_v1_staging_service_proto_init() }
func file_backend_proto_tools_v1_staging_service_proto_init() {
	if File_backend_proto_tools_v1_staging_service_proto != nil {
		return
	}
	file_backend_proto_tools_v1_resource_models_proto_init()
	file_backend_proto_tools_v1_staging_service_proto_msgTypes[2].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_tools_v1_staging_service_proto_rawDesc), len(file_backend_proto_tools_v1_staging_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_tools_v1_staging_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_tools_v1_staging_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_tools_v1_staging_service_proto_msgTypes,
	}.Build()
	File_backend_proto_tools_v1_staging_service_proto = out.File
	file_backend_proto_tools_v1_staging_service_proto_goTypes = nil
	file_backend_proto_tools_v1_staging_service_proto_depIdxs = nil
}
