// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/fulfillment/inner/inner.proto

// (-- api-linter: core::0215::versioned-packages=disabled
//     aip.dev/not-precedent: This is a legacy inner service package that doesn't follow versioned package naming convention. --)

package fulfillmentinnerpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	v1 "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 美容宠物详情DTO - 对应Java的GroomingPetDetailDTO
// (-- api-linter: core::0142::time-field-type=disabled
//
//	aip.dev/not-precedent: We need to do this because reasons. --)
type GroomingPetDetailDTO struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 详情ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 履约ID
	GroomingId int64 `protobuf:"varint,2,opt,name=grooming_id,json=groomingId,proto3" json:"grooming_id,omitempty"`
	// 宠物ID
	PetId int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// 员工ID
	StaffId int64 `protobuf:"varint,4,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 服务实例ID
	ServiceId int64 `protobuf:"varint,5,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// 服务时长(分钟)
	ServiceTime int32 `protobuf:"varint,6,opt,name=service_time,json=serviceTime,proto3" json:"service_time,omitempty"`
	// 服务价格
	ServicePrice float64 `protobuf:"fixed64,7,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
	// 开始时间
	// (-- api-linter: core::0142::time-field-type=disabled
	//
	//	aip.dev/not-precedent: Legacy field using int64 timestamp for backward compatibility. --)
	StartTime int64 `protobuf:"varint,8,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 结束时间
	// (-- api-linter: core::0142::time-field-type=disabled
	//
	//	aip.dev/not-precedent: Legacy field using int64 timestamp for backward compatibility. --)
	EndTime int64 `protobuf:"varint,9,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 范围类型价格
	ScopeTypePrice int32 `protobuf:"varint,10,opt,name=scope_type_price,json=scopeTypePrice,proto3" json:"scope_type_price,omitempty"`
	// 范围类型时长
	ScopeTypeTime int32 `protobuf:"varint,11,opt,name=scope_type_time,json=scopeTypeTime,proto3" json:"scope_type_time,omitempty"`
	// 标星员工ID
	StarStaffId int64 `protobuf:"varint,12,opt,name=star_staff_id,json=starStaffId,proto3" json:"star_staff_id,omitempty"`
	// 服务名称
	// (-- api-linter: core::0122::name-suffix=disabled
	//
	//	aip.dev/not-precedent: Legacy field naming convention maintained for backward compatibility. --)
	ServiceName string `protobuf:"bytes,13,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// 服务类型
	ServiceType int32 `protobuf:"varint,14,opt,name=service_type,json=serviceType,proto3" json:"service_type,omitempty"`
	// 服务状态
	ServiceStatus int32 `protobuf:"varint,15,opt,name=service_status,json=serviceStatus,proto3" json:"service_status,omitempty"`
	// 员工名字
	// (-- api-linter: core::0122::name-suffix=disabled
	//
	//	aip.dev/not-precedent: Legacy field naming convention maintained for backward compatibility. --)
	StaffFirstName string `protobuf:"bytes,16,opt,name=staff_first_name,json=staffFirstName,proto3" json:"staff_first_name,omitempty"`
	// 员工姓氏
	// (-- api-linter: core::0122::name-suffix=disabled
	//
	//	aip.dev/not-precedent: Legacy field naming convention maintained for backward compatibility. --)
	StaffLastName string `protobuf:"bytes,17,opt,name=staff_last_name,json=staffLastName,proto3" json:"staff_last_name,omitempty"`
	// 宠物名字
	// (-- api-linter: core::0122::name-suffix=disabled
	//
	//	aip.dev/not-precedent: Legacy field naming convention maintained for backward compatibility. --)
	PetName string `protobuf:"bytes,18,opt,name=pet_name,json=petName,proto3" json:"pet_name,omitempty"`
	// 宠物生命状态
	PetLifeStatus int32 `protobuf:"varint,19,opt,name=pet_life_status,json=petLifeStatus,proto3" json:"pet_life_status,omitempty"`
	// 是否启用操作
	EnableOperation bool `protobuf:"varint,20,opt,name=enable_operation,json=enableOperation,proto3" json:"enable_operation,omitempty"`
	// 颜色代码
	ColorCode string `protobuf:"bytes,21,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// 工作模式 (0 - parallel, 1 - sequence)
	WorkMode int32 `protobuf:"varint,22,opt,name=work_mode,json=workMode,proto3" json:"work_mode,omitempty"`
	// 操作记录列表
	OperationList []*GroomingServiceOperationDTO `protobuf:"bytes,23,rep,name=operation_list,json=operationList,proto3" json:"operation_list,omitempty"`
	// 服务项目类型
	ServiceItemType int32 `protobuf:"varint,24,opt,name=service_item_type,json=serviceItemType,proto3" json:"service_item_type,omitempty"`
	// 住宿ID
	LodgingId int64 `protobuf:"varint,25,opt,name=lodging_id,json=lodgingId,proto3" json:"lodging_id,omitempty"`
	// 开始日期
	// (-- api-linter: core::0142::time-field-type=disabled
	//
	//	aip.dev/not-precedent: Legacy field using string date for backward compatibility. --)
	StartDate string `protobuf:"bytes,26,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// 结束日期
	// (-- api-linter: core::0142::time-field-type=disabled
	//
	//	aip.dev/not-precedent: Legacy field using string date for backward compatibility. --)
	EndDate string `protobuf:"bytes,27,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// 价格单位 (内部计算用，暂时不用对外暴露)
	PriceUnit int32 `protobuf:"varint,28,opt,name=price_unit,json=priceUnit,proto3" json:"price_unit,omitempty"`
	// 特定日期
	SpecificDates string `protobuf:"bytes,29,opt,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
	// 关联服务ID
	AssociatedServiceId int64 `protobuf:"varint,30,opt,name=associated_service_id,json=associatedServiceId,proto3" json:"associated_service_id,omitempty"`
	// 本条pet detail等价的order line item中quantity数量
	Quantity int32 `protobuf:"varint,31,opt,name=quantity,proto3" json:"quantity,omitempty"`
	// 价格覆盖类型
	PriceOverrideType v1.OverrideType `protobuf:"varint,32,opt,name=price_override_type,json=priceOverrideType,proto3,enum=backend.proto.fulfillment.v1.OverrideType" json:"price_override_type,omitempty"`
	// 时长覆盖类型
	DurationOverrideType v1.OverrideType `protobuf:"varint,33,opt,name=duration_override_type,json=durationOverrideType,proto3,enum=backend.proto.fulfillment.v1.OverrideType" json:"duration_override_type,omitempty"`
	// 更新时间
	// (-- api-linter: core::0142::time-field-names=disabled
	//
	//	api-linter: core::0140::prepositions=disabled
	//	aip.dev/not-precedent: Legacy field naming convention using 'at' suffix maintained for backward compatibility. --)
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,34,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// 每日数量
	QuantityPerDay int32 `protobuf:"varint,35,opt,name=quantity_per_day,json=quantityPerDay,proto3" json:"quantity_per_day,omitempty"`
	// 是否需要专用员工
	RequireDedicatedStaff bool `protobuf:"varint,36,opt,name=require_dedicated_staff,json=requireDedicatedStaff,proto3" json:"require_dedicated_staff,omitempty"`
	// 日期类型 (1-every day except checkout day, 2-specific date, 3-date point, 4-every day include checkout day)
	DateType int32 `protobuf:"varint,37,opt,name=date_type,json=dateType,proto3" json:"date_type,omitempty"`
	// 订单行项目ID
	OrderLineItemId int64 `protobuf:"varint,38,opt,name=order_line_item_id,json=orderLineItemId,proto3" json:"order_line_item_id,omitempty"`
	// 外部ID (petdetail_xxx, evaluation_xxx)
	ExternalId    string `protobuf:"bytes,39,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GroomingPetDetailDTO) Reset() {
	*x = GroomingPetDetailDTO{}
	mi := &file_backend_proto_fulfillment_inner_inner_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GroomingPetDetailDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroomingPetDetailDTO) ProtoMessage() {}

func (x *GroomingPetDetailDTO) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_inner_inner_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroomingPetDetailDTO.ProtoReflect.Descriptor instead.
func (*GroomingPetDetailDTO) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_inner_inner_proto_rawDescGZIP(), []int{0}
}

func (x *GroomingPetDetailDTO) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GroomingPetDetailDTO) GetGroomingId() int64 {
	if x != nil {
		return x.GroomingId
	}
	return 0
}

func (x *GroomingPetDetailDTO) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *GroomingPetDetailDTO) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *GroomingPetDetailDTO) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *GroomingPetDetailDTO) GetServiceTime() int32 {
	if x != nil {
		return x.ServiceTime
	}
	return 0
}

func (x *GroomingPetDetailDTO) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

func (x *GroomingPetDetailDTO) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *GroomingPetDetailDTO) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *GroomingPetDetailDTO) GetScopeTypePrice() int32 {
	if x != nil {
		return x.ScopeTypePrice
	}
	return 0
}

func (x *GroomingPetDetailDTO) GetScopeTypeTime() int32 {
	if x != nil {
		return x.ScopeTypeTime
	}
	return 0
}

func (x *GroomingPetDetailDTO) GetStarStaffId() int64 {
	if x != nil {
		return x.StarStaffId
	}
	return 0
}

func (x *GroomingPetDetailDTO) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *GroomingPetDetailDTO) GetServiceType() int32 {
	if x != nil {
		return x.ServiceType
	}
	return 0
}

func (x *GroomingPetDetailDTO) GetServiceStatus() int32 {
	if x != nil {
		return x.ServiceStatus
	}
	return 0
}

func (x *GroomingPetDetailDTO) GetStaffFirstName() string {
	if x != nil {
		return x.StaffFirstName
	}
	return ""
}

func (x *GroomingPetDetailDTO) GetStaffLastName() string {
	if x != nil {
		return x.StaffLastName
	}
	return ""
}

func (x *GroomingPetDetailDTO) GetPetName() string {
	if x != nil {
		return x.PetName
	}
	return ""
}

func (x *GroomingPetDetailDTO) GetPetLifeStatus() int32 {
	if x != nil {
		return x.PetLifeStatus
	}
	return 0
}

func (x *GroomingPetDetailDTO) GetEnableOperation() bool {
	if x != nil {
		return x.EnableOperation
	}
	return false
}

func (x *GroomingPetDetailDTO) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *GroomingPetDetailDTO) GetWorkMode() int32 {
	if x != nil {
		return x.WorkMode
	}
	return 0
}

func (x *GroomingPetDetailDTO) GetOperationList() []*GroomingServiceOperationDTO {
	if x != nil {
		return x.OperationList
	}
	return nil
}

func (x *GroomingPetDetailDTO) GetServiceItemType() int32 {
	if x != nil {
		return x.ServiceItemType
	}
	return 0
}

func (x *GroomingPetDetailDTO) GetLodgingId() int64 {
	if x != nil {
		return x.LodgingId
	}
	return 0
}

func (x *GroomingPetDetailDTO) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *GroomingPetDetailDTO) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *GroomingPetDetailDTO) GetPriceUnit() int32 {
	if x != nil {
		return x.PriceUnit
	}
	return 0
}

func (x *GroomingPetDetailDTO) GetSpecificDates() string {
	if x != nil {
		return x.SpecificDates
	}
	return ""
}

func (x *GroomingPetDetailDTO) GetAssociatedServiceId() int64 {
	if x != nil {
		return x.AssociatedServiceId
	}
	return 0
}

func (x *GroomingPetDetailDTO) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *GroomingPetDetailDTO) GetPriceOverrideType() v1.OverrideType {
	if x != nil {
		return x.PriceOverrideType
	}
	return v1.OverrideType(0)
}

func (x *GroomingPetDetailDTO) GetDurationOverrideType() v1.OverrideType {
	if x != nil {
		return x.DurationOverrideType
	}
	return v1.OverrideType(0)
}

func (x *GroomingPetDetailDTO) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *GroomingPetDetailDTO) GetQuantityPerDay() int32 {
	if x != nil {
		return x.QuantityPerDay
	}
	return 0
}

func (x *GroomingPetDetailDTO) GetRequireDedicatedStaff() bool {
	if x != nil {
		return x.RequireDedicatedStaff
	}
	return false
}

func (x *GroomingPetDetailDTO) GetDateType() int32 {
	if x != nil {
		return x.DateType
	}
	return 0
}

func (x *GroomingPetDetailDTO) GetOrderLineItemId() int64 {
	if x != nil {
		return x.OrderLineItemId
	}
	return 0
}

func (x *GroomingPetDetailDTO) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

// 美容服务操作DTO - 对应Java的GroomingServiceOperationDTO
type GroomingServiceOperationDTO struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 操作ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 商家ID
	BusinessId int32 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 履约ID
	GroomingId int32 `protobuf:"varint,3,opt,name=grooming_id,json=groomingId,proto3" json:"grooming_id,omitempty"`
	// 美容服务ID
	GroomingServiceId int32 `protobuf:"varint,4,opt,name=grooming_service_id,json=groomingServiceId,proto3" json:"grooming_service_id,omitempty"`
	// 宠物ID
	PetId int32 `protobuf:"varint,5,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// 员工ID (应该大于0)
	StaffId int32 `protobuf:"varint,6,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 操作名称 (最大150字符)
	// (-- api-linter: core::0122::name-suffix=disabled
	//
	//	aip.dev/not-precedent: Legacy field naming convention maintained for backward compatibility. --)
	OperationName string `protobuf:"bytes,7,opt,name=operation_name,json=operationName,proto3" json:"operation_name,omitempty"`
	// 开始时间 (0-1440分钟)
	// (-- api-linter: core::0142::time-field-type=disabled
	//
	//	aip.dev/not-precedent: Legacy field using int32 time for backward compatibility. --)
	StartTime int32 `protobuf:"varint,8,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 服务时长 (0-1440分钟)
	// (-- api-linter: core::0142::time-field-type=disabled
	//
	//	aip.dev/not-precedent: Legacy field using int32 duration for backward compatibility. --)
	Duration int32 `protobuf:"varint,9,opt,name=duration,proto3" json:"duration,omitempty"`
	// 备注 (最大200字符)
	Comment string `protobuf:"bytes,10,opt,name=comment,proto3" json:"comment,omitempty"`
	// 操作价格 (最多18位整数，2位小数)
	Price float64 `protobuf:"fixed64,11,opt,name=price,proto3" json:"price,omitempty"`
	// 价格比例 (最多1位整数，2位小数)
	PriceRatio    float64 `protobuf:"fixed64,12,opt,name=price_ratio,json=priceRatio,proto3" json:"price_ratio,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GroomingServiceOperationDTO) Reset() {
	*x = GroomingServiceOperationDTO{}
	mi := &file_backend_proto_fulfillment_inner_inner_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GroomingServiceOperationDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroomingServiceOperationDTO) ProtoMessage() {}

func (x *GroomingServiceOperationDTO) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_inner_inner_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroomingServiceOperationDTO.ProtoReflect.Descriptor instead.
func (*GroomingServiceOperationDTO) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_inner_inner_proto_rawDescGZIP(), []int{1}
}

func (x *GroomingServiceOperationDTO) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GroomingServiceOperationDTO) GetBusinessId() int32 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GroomingServiceOperationDTO) GetGroomingId() int32 {
	if x != nil {
		return x.GroomingId
	}
	return 0
}

func (x *GroomingServiceOperationDTO) GetGroomingServiceId() int32 {
	if x != nil {
		return x.GroomingServiceId
	}
	return 0
}

func (x *GroomingServiceOperationDTO) GetPetId() int32 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *GroomingServiceOperationDTO) GetStaffId() int32 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *GroomingServiceOperationDTO) GetOperationName() string {
	if x != nil {
		return x.OperationName
	}
	return ""
}

func (x *GroomingServiceOperationDTO) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *GroomingServiceOperationDTO) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *GroomingServiceOperationDTO) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *GroomingServiceOperationDTO) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *GroomingServiceOperationDTO) GetPriceRatio() float64 {
	if x != nil {
		return x.PriceRatio
	}
	return 0
}

var File_backend_proto_fulfillment_inner_inner_proto protoreflect.FileDescriptor

const file_backend_proto_fulfillment_inner_inner_proto_rawDesc = "" +
	"\n" +
	"+backend/proto/fulfillment/inner/inner.proto\x12\x1fbackend.proto.fulfillment.inner\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bbuf/validate/validate.proto\x1a)backend/proto/fulfillment/v1/common.proto\x1a.backend/proto/fulfillment/v1/appointment.proto\"\xbf\f\n" +
	"\x14GroomingPetDetailDTO\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vgrooming_id\x18\x02 \x01(\x03R\n" +
	"groomingId\x12\x15\n" +
	"\x06pet_id\x18\x03 \x01(\x03R\x05petId\x12\x19\n" +
	"\bstaff_id\x18\x04 \x01(\x03R\astaffId\x12\x1d\n" +
	"\n" +
	"service_id\x18\x05 \x01(\x03R\tserviceId\x12!\n" +
	"\fservice_time\x18\x06 \x01(\x05R\vserviceTime\x12#\n" +
	"\rservice_price\x18\a \x01(\x01R\fservicePrice\x12\x1d\n" +
	"\n" +
	"start_time\x18\b \x01(\x03R\tstartTime\x12\x19\n" +
	"\bend_time\x18\t \x01(\x03R\aendTime\x12(\n" +
	"\x10scope_type_price\x18\n" +
	" \x01(\x05R\x0escopeTypePrice\x12&\n" +
	"\x0fscope_type_time\x18\v \x01(\x05R\rscopeTypeTime\x12\"\n" +
	"\rstar_staff_id\x18\f \x01(\x03R\vstarStaffId\x12!\n" +
	"\fservice_name\x18\r \x01(\tR\vserviceName\x12!\n" +
	"\fservice_type\x18\x0e \x01(\x05R\vserviceType\x12%\n" +
	"\x0eservice_status\x18\x0f \x01(\x05R\rserviceStatus\x12(\n" +
	"\x10staff_first_name\x18\x10 \x01(\tR\x0estaffFirstName\x12&\n" +
	"\x0fstaff_last_name\x18\x11 \x01(\tR\rstaffLastName\x12\x19\n" +
	"\bpet_name\x18\x12 \x01(\tR\apetName\x12&\n" +
	"\x0fpet_life_status\x18\x13 \x01(\x05R\rpetLifeStatus\x12)\n" +
	"\x10enable_operation\x18\x14 \x01(\bR\x0fenableOperation\x12\x1d\n" +
	"\n" +
	"color_code\x18\x15 \x01(\tR\tcolorCode\x12\x1b\n" +
	"\twork_mode\x18\x16 \x01(\x05R\bworkMode\x12c\n" +
	"\x0eoperation_list\x18\x17 \x03(\v2<.backend.proto.fulfillment.inner.GroomingServiceOperationDTOR\roperationList\x12*\n" +
	"\x11service_item_type\x18\x18 \x01(\x05R\x0fserviceItemType\x12\x1d\n" +
	"\n" +
	"lodging_id\x18\x19 \x01(\x03R\tlodgingId\x12\x1d\n" +
	"\n" +
	"start_date\x18\x1a \x01(\tR\tstartDate\x12\x19\n" +
	"\bend_date\x18\x1b \x01(\tR\aendDate\x12\x1d\n" +
	"\n" +
	"price_unit\x18\x1c \x01(\x05R\tpriceUnit\x12%\n" +
	"\x0especific_dates\x18\x1d \x01(\tR\rspecificDates\x122\n" +
	"\x15associated_service_id\x18\x1e \x01(\x03R\x13associatedServiceId\x12\x1a\n" +
	"\bquantity\x18\x1f \x01(\x05R\bquantity\x12Z\n" +
	"\x13price_override_type\x18  \x01(\x0e2*.backend.proto.fulfillment.v1.OverrideTypeR\x11priceOverrideType\x12`\n" +
	"\x16duration_override_type\x18! \x01(\x0e2*.backend.proto.fulfillment.v1.OverrideTypeR\x14durationOverrideType\x129\n" +
	"\n" +
	"updated_at\x18\" \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x12(\n" +
	"\x10quantity_per_day\x18# \x01(\x05R\x0equantityPerDay\x126\n" +
	"\x17require_dedicated_staff\x18$ \x01(\bR\x15requireDedicatedStaff\x12\x1b\n" +
	"\tdate_type\x18% \x01(\x05R\bdateType\x12+\n" +
	"\x12order_line_item_id\x18& \x01(\x03R\x0forderLineItemId\x12\x1f\n" +
	"\vexternal_id\x18' \x01(\tR\n" +
	"externalId\"\x84\x03\n" +
	"\x1bGroomingServiceOperationDTO\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vbusiness_id\x18\x02 \x01(\x05R\n" +
	"businessId\x12\x1f\n" +
	"\vgrooming_id\x18\x03 \x01(\x05R\n" +
	"groomingId\x12.\n" +
	"\x13grooming_service_id\x18\x04 \x01(\x05R\x11groomingServiceId\x12\x15\n" +
	"\x06pet_id\x18\x05 \x01(\x05R\x05petId\x12\x19\n" +
	"\bstaff_id\x18\x06 \x01(\x05R\astaffId\x12%\n" +
	"\x0eoperation_name\x18\a \x01(\tR\roperationName\x12\x1d\n" +
	"\n" +
	"start_time\x18\b \x01(\x05R\tstartTime\x12\x1a\n" +
	"\bduration\x18\t \x01(\x05R\bduration\x12\x18\n" +
	"\acomment\x18\n" +
	" \x01(\tR\acomment\x12\x14\n" +
	"\x05price\x18\v \x01(\x01R\x05price\x12\x1f\n" +
	"\vprice_ratio\x18\f \x01(\x01R\n" +
	"priceRatioB\x7f\n" +
	")com.moego.backend.proto.fulfillment.innerP\x01ZPgithub.com/MoeGolibrary/moego/backend/proto/fulfillment/inner;fulfillmentinnerpbb\x06proto3"

var (
	file_backend_proto_fulfillment_inner_inner_proto_rawDescOnce sync.Once
	file_backend_proto_fulfillment_inner_inner_proto_rawDescData []byte
)

func file_backend_proto_fulfillment_inner_inner_proto_rawDescGZIP() []byte {
	file_backend_proto_fulfillment_inner_inner_proto_rawDescOnce.Do(func() {
		file_backend_proto_fulfillment_inner_inner_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_inner_inner_proto_rawDesc), len(file_backend_proto_fulfillment_inner_inner_proto_rawDesc)))
	})
	return file_backend_proto_fulfillment_inner_inner_proto_rawDescData
}

var file_backend_proto_fulfillment_inner_inner_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_backend_proto_fulfillment_inner_inner_proto_goTypes = []any{
	(*GroomingPetDetailDTO)(nil),        // 0: backend.proto.fulfillment.inner.GroomingPetDetailDTO
	(*GroomingServiceOperationDTO)(nil), // 1: backend.proto.fulfillment.inner.GroomingServiceOperationDTO
	(v1.OverrideType)(0),                // 2: backend.proto.fulfillment.v1.OverrideType
	(*timestamppb.Timestamp)(nil),       // 3: google.protobuf.Timestamp
}
var file_backend_proto_fulfillment_inner_inner_proto_depIdxs = []int32{
	1, // 0: backend.proto.fulfillment.inner.GroomingPetDetailDTO.operation_list:type_name -> backend.proto.fulfillment.inner.GroomingServiceOperationDTO
	2, // 1: backend.proto.fulfillment.inner.GroomingPetDetailDTO.price_override_type:type_name -> backend.proto.fulfillment.v1.OverrideType
	2, // 2: backend.proto.fulfillment.inner.GroomingPetDetailDTO.duration_override_type:type_name -> backend.proto.fulfillment.v1.OverrideType
	3, // 3: backend.proto.fulfillment.inner.GroomingPetDetailDTO.updated_at:type_name -> google.protobuf.Timestamp
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_backend_proto_fulfillment_inner_inner_proto_init() }
func file_backend_proto_fulfillment_inner_inner_proto_init() {
	if File_backend_proto_fulfillment_inner_inner_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_inner_inner_proto_rawDesc), len(file_backend_proto_fulfillment_inner_inner_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_fulfillment_inner_inner_proto_goTypes,
		DependencyIndexes: file_backend_proto_fulfillment_inner_inner_proto_depIdxs,
		MessageInfos:      file_backend_proto_fulfillment_inner_inner_proto_msgTypes,
	}.Build()
	File_backend_proto_fulfillment_inner_inner_proto = out.File
	file_backend_proto_fulfillment_inner_inner_proto_goTypes = nil
	file_backend_proto_fulfillment_inner_inner_proto_depIdxs = nil
}
