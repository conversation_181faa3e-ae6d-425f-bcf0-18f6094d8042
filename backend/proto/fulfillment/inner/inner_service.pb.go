// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/fulfillment/inner/inner_service.proto

// (-- api-linter: core::0215::versioned-packages=disabled
//     aip.dev/not-precedent: This is a legacy inner service package that doesn't follow versioned package naming convention. --)

package fulfillmentinnerpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	v1 "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 通过预约ID获取美容详情请求
type GetGroomingDetailByAppointmentIdRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 预约ID
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// 服务项目类型列表 (可选，对应原SQL中的serviceItems参数)
	ServiceItemTypes []v1.ServiceType `protobuf:"varint,2,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=backend.proto.fulfillment.v1.ServiceType" json:"service_item_types,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *GetGroomingDetailByAppointmentIdRequest) Reset() {
	*x = GetGroomingDetailByAppointmentIdRequest{}
	mi := &file_backend_proto_fulfillment_inner_inner_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGroomingDetailByAppointmentIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGroomingDetailByAppointmentIdRequest) ProtoMessage() {}

func (x *GetGroomingDetailByAppointmentIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_inner_inner_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGroomingDetailByAppointmentIdRequest.ProtoReflect.Descriptor instead.
func (*GetGroomingDetailByAppointmentIdRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_inner_inner_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetGroomingDetailByAppointmentIdRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *GetGroomingDetailByAppointmentIdRequest) GetServiceItemTypes() []v1.ServiceType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

// 通过预约ID获取美容详情响应
type GetGroomingDetailByAppointmentIdResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 美容详情列表
	GroomingDetails []*GroomingPetDetailDTO `protobuf:"bytes,1,rep,name=grooming_details,json=groomingDetails,proto3" json:"grooming_details,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetGroomingDetailByAppointmentIdResponse) Reset() {
	*x = GetGroomingDetailByAppointmentIdResponse{}
	mi := &file_backend_proto_fulfillment_inner_inner_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGroomingDetailByAppointmentIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGroomingDetailByAppointmentIdResponse) ProtoMessage() {}

func (x *GetGroomingDetailByAppointmentIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_inner_inner_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGroomingDetailByAppointmentIdResponse.ProtoReflect.Descriptor instead.
func (*GetGroomingDetailByAppointmentIdResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_inner_inner_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetGroomingDetailByAppointmentIdResponse) GetGroomingDetails() []*GroomingPetDetailDTO {
	if x != nil {
		return x.GroomingDetails
	}
	return nil
}

var File_backend_proto_fulfillment_inner_inner_service_proto protoreflect.FileDescriptor

const file_backend_proto_fulfillment_inner_inner_service_proto_rawDesc = "" +
	"\n" +
	"3backend/proto/fulfillment/inner/inner_service.proto\x12\x1fbackend.proto.fulfillment.inner\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bbuf/validate/validate.proto\x1a)backend/proto/fulfillment/v1/common.proto\x1a+backend/proto/fulfillment/inner/inner.proto\"\xa9\x01\n" +
	"'GetGroomingDetailByAppointmentIdRequest\x12%\n" +
	"\x0eappointment_id\x18\x01 \x01(\x03R\rappointmentId\x12W\n" +
	"\x12service_item_types\x18\x02 \x03(\x0e2).backend.proto.fulfillment.v1.ServiceTypeR\x10serviceItemTypes\"\x8c\x01\n" +
	"(GetGroomingDetailByAppointmentIdResponse\x12`\n" +
	"\x10grooming_details\x18\x01 \x03(\v25.backend.proto.fulfillment.inner.GroomingPetDetailDTOR\x0fgroomingDetails2\xc8\x01\n" +
	"\fInnerService\x12\xb7\x01\n" +
	" GetGroomingDetailByAppointmentId\x12H.backend.proto.fulfillment.inner.GetGroomingDetailByAppointmentIdRequest\x1aI.backend.proto.fulfillment.inner.GetGroomingDetailByAppointmentIdResponseB\x7f\n" +
	")com.moego.backend.proto.fulfillment.innerP\x01ZPgithub.com/MoeGolibrary/moego/backend/proto/fulfillment/inner;fulfillmentinnerpbb\x06proto3"

var (
	file_backend_proto_fulfillment_inner_inner_service_proto_rawDescOnce sync.Once
	file_backend_proto_fulfillment_inner_inner_service_proto_rawDescData []byte
)

func file_backend_proto_fulfillment_inner_inner_service_proto_rawDescGZIP() []byte {
	file_backend_proto_fulfillment_inner_inner_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_fulfillment_inner_inner_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_inner_inner_service_proto_rawDesc), len(file_backend_proto_fulfillment_inner_inner_service_proto_rawDesc)))
	})
	return file_backend_proto_fulfillment_inner_inner_service_proto_rawDescData
}

var file_backend_proto_fulfillment_inner_inner_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_backend_proto_fulfillment_inner_inner_service_proto_goTypes = []any{
	(*GetGroomingDetailByAppointmentIdRequest)(nil),  // 0: backend.proto.fulfillment.inner.GetGroomingDetailByAppointmentIdRequest
	(*GetGroomingDetailByAppointmentIdResponse)(nil), // 1: backend.proto.fulfillment.inner.GetGroomingDetailByAppointmentIdResponse
	(v1.ServiceType)(0),                              // 2: backend.proto.fulfillment.v1.ServiceType
	(*GroomingPetDetailDTO)(nil),                     // 3: backend.proto.fulfillment.inner.GroomingPetDetailDTO
}
var file_backend_proto_fulfillment_inner_inner_service_proto_depIdxs = []int32{
	2, // 0: backend.proto.fulfillment.inner.GetGroomingDetailByAppointmentIdRequest.service_item_types:type_name -> backend.proto.fulfillment.v1.ServiceType
	3, // 1: backend.proto.fulfillment.inner.GetGroomingDetailByAppointmentIdResponse.grooming_details:type_name -> backend.proto.fulfillment.inner.GroomingPetDetailDTO
	0, // 2: backend.proto.fulfillment.inner.InnerService.GetGroomingDetailByAppointmentId:input_type -> backend.proto.fulfillment.inner.GetGroomingDetailByAppointmentIdRequest
	1, // 3: backend.proto.fulfillment.inner.InnerService.GetGroomingDetailByAppointmentId:output_type -> backend.proto.fulfillment.inner.GetGroomingDetailByAppointmentIdResponse
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_backend_proto_fulfillment_inner_inner_service_proto_init() }
func file_backend_proto_fulfillment_inner_inner_service_proto_init() {
	if File_backend_proto_fulfillment_inner_inner_service_proto != nil {
		return
	}
	file_backend_proto_fulfillment_inner_inner_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_inner_inner_service_proto_rawDesc), len(file_backend_proto_fulfillment_inner_inner_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_fulfillment_inner_inner_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_fulfillment_inner_inner_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_fulfillment_inner_inner_service_proto_msgTypes,
	}.Build()
	File_backend_proto_fulfillment_inner_inner_service_proto = out.File
	file_backend_proto_fulfillment_inner_inner_service_proto_goTypes = nil
	file_backend_proto_fulfillment_inner_inner_service_proto_depIdxs = nil
}
