// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/fulfillment/v1/appointment.proto

package fulfillmentpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	timeofday "google.golang.org/genproto/googleapis/type/timeofday"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 重复类型
type RepeatType int32

const (
	// 未指定重复类型
	RepeatType_REPEAT_TYPE_UNSPECIFIED RepeatType = 0
	// 每日重复
	RepeatType_REPEAT_TYPE_DAILY RepeatType = 1
	// 每周重复
	RepeatType_REPEAT_TYPE_WEEKLY RepeatType = 2
	// 每月重复
	RepeatType_REPEAT_TYPE_MONTHLY RepeatType = 3
)

// Enum value maps for RepeatType.
var (
	RepeatType_name = map[int32]string{
		0: "REPEAT_TYPE_UNSPECIFIED",
		1: "REPEAT_TYPE_DAILY",
		2: "REPEAT_TYPE_WEEKLY",
		3: "REPEAT_TYPE_MONTHLY",
	}
	RepeatType_value = map[string]int32{
		"REPEAT_TYPE_UNSPECIFIED": 0,
		"REPEAT_TYPE_DAILY":       1,
		"REPEAT_TYPE_WEEKLY":      2,
		"REPEAT_TYPE_MONTHLY":     3,
	}
)

func (x RepeatType) Enum() *RepeatType {
	p := new(RepeatType)
	*p = x
	return p
}

func (x RepeatType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RepeatType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_appointment_proto_enumTypes[0].Descriptor()
}

func (RepeatType) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_appointment_proto_enumTypes[0]
}

func (x RepeatType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RepeatType.Descriptor instead.
func (RepeatType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{0}
}

// 预约单状态枚举
// (-- api-linter: core::0216::nesting=disabled
//
//	aip.dev/not-precedent: AppointmentState is appropriate as top-level enum --)
type AppointmentState int32

const (
	// 未指定状态
	AppointmentState_APPOINTMENT_STATE_UNSPECIFIED AppointmentState = 0
	// 未确认
	AppointmentState_APPOINTMENT_STATE_UNCONFIRMED AppointmentState = 1
	// 已确认
	AppointmentState_APPOINTMENT_STATE_CONFIRMED AppointmentState = 2
	// 已完成(check out)
	AppointmentState_APPOINTMENT_STATE_FINISHED AppointmentState = 3
	// 已取消
	AppointmentState_APPOINTMENT_STATE_CANCELED AppointmentState = 4
	// 准备就绪
	AppointmentState_APPOINTMENT_STATE_READY AppointmentState = 5
	// 已入住
	AppointmentState_APPOINTMENT_STATE_CHECKED_IN AppointmentState = 6
)

// Enum value maps for AppointmentState.
var (
	AppointmentState_name = map[int32]string{
		0: "APPOINTMENT_STATE_UNSPECIFIED",
		1: "APPOINTMENT_STATE_UNCONFIRMED",
		2: "APPOINTMENT_STATE_CONFIRMED",
		3: "APPOINTMENT_STATE_FINISHED",
		4: "APPOINTMENT_STATE_CANCELED",
		5: "APPOINTMENT_STATE_READY",
		6: "APPOINTMENT_STATE_CHECKED_IN",
	}
	AppointmentState_value = map[string]int32{
		"APPOINTMENT_STATE_UNSPECIFIED": 0,
		"APPOINTMENT_STATE_UNCONFIRMED": 1,
		"APPOINTMENT_STATE_CONFIRMED":   2,
		"APPOINTMENT_STATE_FINISHED":    3,
		"APPOINTMENT_STATE_CANCELED":    4,
		"APPOINTMENT_STATE_READY":       5,
		"APPOINTMENT_STATE_CHECKED_IN":  6,
	}
)

func (x AppointmentState) Enum() *AppointmentState {
	p := new(AppointmentState)
	*p = x
	return p
}

func (x AppointmentState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AppointmentState) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_appointment_proto_enumTypes[1].Descriptor()
}

func (AppointmentState) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_appointment_proto_enumTypes[1]
}

func (x AppointmentState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AppointmentState.Descriptor instead.
func (AppointmentState) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{1}
}

// 操作模式枚举
type OperationMode int32

const (
	// 未指定操作模式
	OperationMode_OPERATION_MODE_UNSPECIFIED OperationMode = 0
	// 创建
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	OperationMode_OPERATION_MODE_CREATE OperationMode = 1
	// 更新
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	OperationMode_OPERATION_MODE_UPDATE OperationMode = 2
	// 删除
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	OperationMode_OPERATION_MODE_DELETE OperationMode = 3
)

// Enum value maps for OperationMode.
var (
	OperationMode_name = map[int32]string{
		0: "OPERATION_MODE_UNSPECIFIED",
		1: "OPERATION_MODE_CREATE",
		2: "OPERATION_MODE_UPDATE",
		3: "OPERATION_MODE_DELETE",
	}
	OperationMode_value = map[string]int32{
		"OPERATION_MODE_UNSPECIFIED": 0,
		"OPERATION_MODE_CREATE":      1,
		"OPERATION_MODE_UPDATE":      2,
		"OPERATION_MODE_DELETE":      3,
	}
)

func (x OperationMode) Enum() *OperationMode {
	p := new(OperationMode)
	*p = x
	return p
}

func (x OperationMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OperationMode) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_appointment_proto_enumTypes[2].Descriptor()
}

func (OperationMode) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_appointment_proto_enumTypes[2]
}

func (x OperationMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OperationMode.Descriptor instead.
func (OperationMode) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{2}
}

// appointment note type
type AppointmentNoteType int32

const (
	// unspecified
	AppointmentNoteType_APPOINTMENT_NOTE_TYPE_UNSPECIFIED AppointmentNoteType = 0
	// Alert notes
	AppointmentNoteType_ALERT_NOTES AppointmentNoteType = 1
	// Ticket comments, Block description
	AppointmentNoteType_COMMENT AppointmentNoteType = 2
	// Cancel reason
	AppointmentNoteType_CANCEL AppointmentNoteType = 3
	// Booking request additional note
	AppointmentNoteType_ADDITIONAL AppointmentNoteType = 4
)

// Enum value maps for AppointmentNoteType.
var (
	AppointmentNoteType_name = map[int32]string{
		0: "APPOINTMENT_NOTE_TYPE_UNSPECIFIED",
		1: "ALERT_NOTES",
		2: "COMMENT",
		3: "CANCEL",
		4: "ADDITIONAL",
	}
	AppointmentNoteType_value = map[string]int32{
		"APPOINTMENT_NOTE_TYPE_UNSPECIFIED": 0,
		"ALERT_NOTES":                       1,
		"COMMENT":                           2,
		"CANCEL":                            3,
		"ADDITIONAL":                        4,
	}
)

func (x AppointmentNoteType) Enum() *AppointmentNoteType {
	p := new(AppointmentNoteType)
	*p = x
	return p
}

func (x AppointmentNoteType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AppointmentNoteType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_appointment_proto_enumTypes[3].Descriptor()
}

func (AppointmentNoteType) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_appointment_proto_enumTypes[3]
}

func (x AppointmentNoteType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AppointmentNoteType.Descriptor instead.
func (AppointmentNoteType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{3}
}

// AppointmentSource source
type AppointmentSource int32

const (
	// Unspecified
	AppointmentSource_APPOINTMENT_SOURCE_UNSPECIFIED AppointmentSource = 0
	// web
	AppointmentSource_WEB AppointmentSource = 22018
	// online booking
	AppointmentSource_OB AppointmentSource = 22168
	// android
	AppointmentSource_ANDROID AppointmentSource = 17216
	// ios
	AppointmentSource_IOS AppointmentSource = 17802
	// auto dm
	AppointmentSource_AUTO_DM AppointmentSource = 23426
	// google calendar
	AppointmentSource_GOOGLE_CALENDAR AppointmentSource = 19826
	// open api
	AppointmentSource_OPEN_API AppointmentSource = 23333
)

// Enum value maps for AppointmentSource.
var (
	AppointmentSource_name = map[int32]string{
		0:     "APPOINTMENT_SOURCE_UNSPECIFIED",
		22018: "WEB",
		22168: "OB",
		17216: "ANDROID",
		17802: "IOS",
		23426: "AUTO_DM",
		19826: "GOOGLE_CALENDAR",
		23333: "OPEN_API",
	}
	AppointmentSource_value = map[string]int32{
		"APPOINTMENT_SOURCE_UNSPECIFIED": 0,
		"WEB":                            22018,
		"OB":                             22168,
		"ANDROID":                        17216,
		"IOS":                            17802,
		"AUTO_DM":                        23426,
		"GOOGLE_CALENDAR":                19826,
		"OPEN_API":                       23333,
	}
)

func (x AppointmentSource) Enum() *AppointmentSource {
	p := new(AppointmentSource)
	*p = x
	return p
}

func (x AppointmentSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AppointmentSource) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_appointment_proto_enumTypes[4].Descriptor()
}

func (AppointmentSource) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_appointment_proto_enumTypes[4]
}

func (x AppointmentSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AppointmentSource.Descriptor instead.
func (AppointmentSource) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{4}
}

// 工作模式枚举
type WorkMode int32

const (
	// 未指定工作模式
	WorkMode_WORK_MODE_UNSPECIFIED WorkMode = 0
	// parallel
	WorkMode_PARALLEL WorkMode = 1
	// sequence
	WorkMode_SEQUENCE WorkMode = 2
)

// Enum value maps for WorkMode.
var (
	WorkMode_name = map[int32]string{
		0: "WORK_MODE_UNSPECIFIED",
		1: "PARALLEL",
		2: "SEQUENCE",
	}
	WorkMode_value = map[string]int32{
		"WORK_MODE_UNSPECIFIED": 0,
		"PARALLEL":              1,
		"SEQUENCE":              2,
	}
)

func (x WorkMode) Enum() *WorkMode {
	p := new(WorkMode)
	*p = x
	return p
}

func (x WorkMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WorkMode) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_appointment_proto_enumTypes[5].Descriptor()
}

func (WorkMode) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_appointment_proto_enumTypes[5]
}

func (x WorkMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WorkMode.Descriptor instead.
func (WorkMode) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{5}
}

// feeding medication schedule date type
type FeedingMedicationScheduleDateType int32

const (
	// unspecified
	FeedingMedicationScheduleDateType_FEEDING_MEDICATION_SCHEDULE_DATE_TYPE_UNSPECIFIED FeedingMedicationScheduleDateType = 0
	// Every day except for checkout day
	FeedingMedicationScheduleDateType_EVERYDAY_EXCEPT_CHECKOUT_DATE FeedingMedicationScheduleDateType = 1
	// Every day include checkout day
	FeedingMedicationScheduleDateType_EVERYDAY_INCLUDE_CHECKOUT_DATE FeedingMedicationScheduleDateType = 2
	// Specific date
	FeedingMedicationScheduleDateType_SPECIFIC_DATE FeedingMedicationScheduleDateType = 3
)

// Enum value maps for FeedingMedicationScheduleDateType.
var (
	FeedingMedicationScheduleDateType_name = map[int32]string{
		0: "FEEDING_MEDICATION_SCHEDULE_DATE_TYPE_UNSPECIFIED",
		1: "EVERYDAY_EXCEPT_CHECKOUT_DATE",
		2: "EVERYDAY_INCLUDE_CHECKOUT_DATE",
		3: "SPECIFIC_DATE",
	}
	FeedingMedicationScheduleDateType_value = map[string]int32{
		"FEEDING_MEDICATION_SCHEDULE_DATE_TYPE_UNSPECIFIED": 0,
		"EVERYDAY_EXCEPT_CHECKOUT_DATE":                     1,
		"EVERYDAY_INCLUDE_CHECKOUT_DATE":                    2,
		"SPECIFIC_DATE":                                     3,
	}
)

func (x FeedingMedicationScheduleDateType) Enum() *FeedingMedicationScheduleDateType {
	p := new(FeedingMedicationScheduleDateType)
	*p = x
	return p
}

func (x FeedingMedicationScheduleDateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FeedingMedicationScheduleDateType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_appointment_proto_enumTypes[6].Descriptor()
}

func (FeedingMedicationScheduleDateType) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_appointment_proto_enumTypes[6]
}

func (x FeedingMedicationScheduleDateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FeedingMedicationScheduleDateType.Descriptor instead.
func (FeedingMedicationScheduleDateType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{6}
}

// 特定日期调度类型
type SpecificDateScheduleType int32

const (
	// 未指定类型
	SpecificDateScheduleType_SPECIFIC_DATE_SCHEDULE_TYPE_UNSPECIFIED SpecificDateScheduleType = 0
	// 每天固定时间：所有日期使用相同的固定时间
	SpecificDateScheduleType_SPECIFIC_DATE_SCHEDULE_TYPE_REGULAR_TIME SpecificDateScheduleType = 1
	// 用户自定义时间：每个日期可以有不同的时间
	SpecificDateScheduleType_SPECIFIC_DATE_SCHEDULE_TYPE_CUSTOM_TIME SpecificDateScheduleType = 2
)

// Enum value maps for SpecificDateScheduleType.
var (
	SpecificDateScheduleType_name = map[int32]string{
		0: "SPECIFIC_DATE_SCHEDULE_TYPE_UNSPECIFIED",
		1: "SPECIFIC_DATE_SCHEDULE_TYPE_REGULAR_TIME",
		2: "SPECIFIC_DATE_SCHEDULE_TYPE_CUSTOM_TIME",
	}
	SpecificDateScheduleType_value = map[string]int32{
		"SPECIFIC_DATE_SCHEDULE_TYPE_UNSPECIFIED":  0,
		"SPECIFIC_DATE_SCHEDULE_TYPE_REGULAR_TIME": 1,
		"SPECIFIC_DATE_SCHEDULE_TYPE_CUSTOM_TIME":  2,
	}
)

func (x SpecificDateScheduleType) Enum() *SpecificDateScheduleType {
	p := new(SpecificDateScheduleType)
	*p = x
	return p
}

func (x SpecificDateScheduleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SpecificDateScheduleType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_fulfillment_v1_appointment_proto_enumTypes[7].Descriptor()
}

func (SpecificDateScheduleType) Type() protoreflect.EnumType {
	return &file_backend_proto_fulfillment_v1_appointment_proto_enumTypes[7]
}

func (x SpecificDateScheduleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SpecificDateScheduleType.Descriptor instead.
func (SpecificDateScheduleType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{7}
}

// 预约过滤器
// (-- api-linter: core::0216::state-field-output-only=disabled
//
//	aip.dev/not-precedent: We need to do this because reasons. --)
type AppointmentFilter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 状态列表
	Statuses      []AppointmentState `protobuf:"varint,1,rep,packed,name=statuses,proto3,enum=backend.proto.fulfillment.v1.AppointmentState" json:"statuses,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AppointmentFilter) Reset() {
	*x = AppointmentFilter{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AppointmentFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentFilter) ProtoMessage() {}

func (x *AppointmentFilter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentFilter.ProtoReflect.Descriptor instead.
func (*AppointmentFilter) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{0}
}

func (x *AppointmentFilter) GetStatuses() []AppointmentState {
	if x != nil {
		return x.Statuses
	}
	return nil
}

// 创建预约定义
type CreateAppointmentDef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 商家ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 客户ID
	CustomerId int64 `protobuf:"varint,4,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 预约状态+1
	// (-- api-linter: core::0216::state-field-output-only=disabled
	//
	//	aip.dev/not-precedent: 状态字段需要可写 --)
	Status AppointmentState `protobuf:"varint,5,opt,name=status,proto3,enum=backend.proto.fulfillment.v1.AppointmentState" json:"status,omitempty"`
	// 颜色代码
	ColorCode string `protobuf:"bytes,7,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// 开始时间
	StartTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 结束时间
	EndTime *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 宠物详情列表
	Pets          []*CreatePetDetailDef `protobuf:"bytes,10,rep,name=pets,proto3" json:"pets,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAppointmentDef) Reset() {
	*x = CreateAppointmentDef{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAppointmentDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAppointmentDef) ProtoMessage() {}

func (x *CreateAppointmentDef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAppointmentDef.ProtoReflect.Descriptor instead.
func (*CreateAppointmentDef) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{1}
}

func (x *CreateAppointmentDef) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateAppointmentDef) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateAppointmentDef) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CreateAppointmentDef) GetStatus() AppointmentState {
	if x != nil {
		return x.Status
	}
	return AppointmentState_APPOINTMENT_STATE_UNSPECIFIED
}

func (x *CreateAppointmentDef) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *CreateAppointmentDef) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *CreateAppointmentDef) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *CreateAppointmentDef) GetPets() []*CreatePetDetailDef {
	if x != nil {
		return x.Pets
	}
	return nil
}

// 创建宠物详情定义
type CreatePetDetailDef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 宠物ID
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// 服务实例列表
	Services      []*CreateServiceInstanceDef `protobuf:"bytes,2,rep,name=services,proto3" json:"services,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePetDetailDef) Reset() {
	*x = CreatePetDetailDef{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePetDetailDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetDetailDef) ProtoMessage() {}

func (x *CreatePetDetailDef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetDetailDef.ProtoReflect.Descriptor instead.
func (*CreatePetDetailDef) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{2}
}

func (x *CreatePetDetailDef) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *CreatePetDetailDef) GetServices() []*CreateServiceInstanceDef {
	if x != nil {
		return x.Services
	}
	return nil
}

// 创建服务实例定义
type CreateServiceInstanceDef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务模板ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 时间配置，像service这类明确知道起始时间的，用time_config给时间戳
	// 如果是addon类需要跟随service的，给date_schedule_config
	// 两个参数oneof,2选1
	//
	// Types that are valid to be assigned to Time:
	//
	//	*CreateServiceInstanceDef_TimeConfig
	//	*CreateServiceInstanceDef_DateScheduleConfig
	Time isCreateServiceInstanceDef_Time `protobuf_oneof:"time"`
	// 员工ID
	StaffId *int64 `protobuf:"varint,4,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// 工作模式 (0 - parallel, 1 - sequence)
	WorkMode *WorkMode `protobuf:"varint,5,opt,name=work_mode,json=workMode,proto3,enum=backend.proto.fulfillment.v1.WorkMode,oneof" json:"work_mode,omitempty"`
	// 子服务实例列表
	SubServiceInstances []*CreateServiceInstanceDef `protobuf:"bytes,6,rep,name=sub_service_instances,json=subServiceInstances,proto3" json:"sub_service_instances,omitempty"`
	// 服务费用列表
	Charges []*ServiceCharge `protobuf:"bytes,7,rep,name=charges,proto3" json:"charges,omitempty"`
	// 价格
	Price *int64 `protobuf:"varint,8,opt,name=price,proto3,oneof" json:"price,omitempty"`
	// 时长
	Duration *durationpb.Duration `protobuf:"bytes,9,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	// 用药计划
	Medications []*AppointmentPetMedicationScheduleDef `protobuf:"bytes,10,rep,name=medications,proto3" json:"medications,omitempty"`
	// 喂养计划
	Feedings []*AppointmentPetFeedingScheduleDef `protobuf:"bytes,11,rep,name=feedings,proto3" json:"feedings,omitempty"`
	// 服务细则
	ServiceDetail *ServiceDetail `protobuf:"bytes,12,opt,name=service_detail,json=serviceDetail,proto3,oneof" json:"service_detail,omitempty"`
	// 住宿ID
	LodgingId     *int64 `protobuf:"varint,13,opt,name=lodging_id,json=lodgingId,proto3,oneof" json:"lodging_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateServiceInstanceDef) Reset() {
	*x = CreateServiceInstanceDef{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceInstanceDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceInstanceDef) ProtoMessage() {}

func (x *CreateServiceInstanceDef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceInstanceDef.ProtoReflect.Descriptor instead.
func (*CreateServiceInstanceDef) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{3}
}

func (x *CreateServiceInstanceDef) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CreateServiceInstanceDef) GetTime() isCreateServiceInstanceDef_Time {
	if x != nil {
		return x.Time
	}
	return nil
}

func (x *CreateServiceInstanceDef) GetTimeConfig() *TimeConfig {
	if x != nil {
		if x, ok := x.Time.(*CreateServiceInstanceDef_TimeConfig); ok {
			return x.TimeConfig
		}
	}
	return nil
}

func (x *CreateServiceInstanceDef) GetDateScheduleConfig() *DateScheduleConfig {
	if x != nil {
		if x, ok := x.Time.(*CreateServiceInstanceDef_DateScheduleConfig); ok {
			return x.DateScheduleConfig
		}
	}
	return nil
}

func (x *CreateServiceInstanceDef) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *CreateServiceInstanceDef) GetWorkMode() WorkMode {
	if x != nil && x.WorkMode != nil {
		return *x.WorkMode
	}
	return WorkMode_WORK_MODE_UNSPECIFIED
}

func (x *CreateServiceInstanceDef) GetSubServiceInstances() []*CreateServiceInstanceDef {
	if x != nil {
		return x.SubServiceInstances
	}
	return nil
}

func (x *CreateServiceInstanceDef) GetCharges() []*ServiceCharge {
	if x != nil {
		return x.Charges
	}
	return nil
}

func (x *CreateServiceInstanceDef) GetPrice() int64 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *CreateServiceInstanceDef) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

func (x *CreateServiceInstanceDef) GetMedications() []*AppointmentPetMedicationScheduleDef {
	if x != nil {
		return x.Medications
	}
	return nil
}

func (x *CreateServiceInstanceDef) GetFeedings() []*AppointmentPetFeedingScheduleDef {
	if x != nil {
		return x.Feedings
	}
	return nil
}

func (x *CreateServiceInstanceDef) GetServiceDetail() *ServiceDetail {
	if x != nil {
		return x.ServiceDetail
	}
	return nil
}

func (x *CreateServiceInstanceDef) GetLodgingId() int64 {
	if x != nil && x.LodgingId != nil {
		return *x.LodgingId
	}
	return 0
}

type isCreateServiceInstanceDef_Time interface {
	isCreateServiceInstanceDef_Time()
}

type CreateServiceInstanceDef_TimeConfig struct {
	// 时间配置
	TimeConfig *TimeConfig `protobuf:"bytes,2,opt,name=time_config,json=timeConfig,proto3,oneof"`
}

type CreateServiceInstanceDef_DateScheduleConfig struct {
	// 日期调度配置
	DateScheduleConfig *DateScheduleConfig `protobuf:"bytes,3,opt,name=date_schedule_config,json=dateScheduleConfig,proto3,oneof"`
}

func (*CreateServiceInstanceDef_TimeConfig) isCreateServiceInstanceDef_Time() {}

func (*CreateServiceInstanceDef_DateScheduleConfig) isCreateServiceInstanceDef_Time() {}

// 时间配置
type TimeConfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务开始时间
	StartTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
	// 服务结束时间
	EndTime       *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TimeConfig) Reset() {
	*x = TimeConfig{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TimeConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeConfig) ProtoMessage() {}

func (x *TimeConfig) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeConfig.ProtoReflect.Descriptor instead.
func (*TimeConfig) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{4}
}

func (x *TimeConfig) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *TimeConfig) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

// 日期调度配置
type DateScheduleConfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 日期类型
	DateType DateType `protobuf:"varint,1,opt,name=date_type,json=dateType,proto3,enum=backend.proto.fulfillment.v1.DateType" json:"date_type,omitempty"`
	// 格式: "15:30:15"
	// (-- api-linter: core::0142::time-field-type=disabled
	//
	//	aip.dev/not-precedent: start_minute represents minute offset, not timestamp --)
	RegularTime *timeofday.TimeOfDay `protobuf:"bytes,2,opt,name=regular_time,json=regularTime,proto3,oneof" json:"regular_time,omitempty"`
	// 特定日期配置列表（当 date_type 为 DATE_TYPE_SPECIFIC_DATE 时使用）
	// (-- api-linter: core::0142::time-field-names=disabled
	//
	//	aip.dev/not-precedent: We need to do this because reasons. --)
	SpecificDates []*timestamppb.Timestamp `protobuf:"bytes,4,rep,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DateScheduleConfig) Reset() {
	*x = DateScheduleConfig{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DateScheduleConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DateScheduleConfig) ProtoMessage() {}

func (x *DateScheduleConfig) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DateScheduleConfig.ProtoReflect.Descriptor instead.
func (*DateScheduleConfig) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{5}
}

func (x *DateScheduleConfig) GetDateType() DateType {
	if x != nil {
		return x.DateType
	}
	return DateType_DATE_TYPE_UNSPECIFIED
}

func (x *DateScheduleConfig) GetRegularTime() *timeofday.TimeOfDay {
	if x != nil {
		return x.RegularTime
	}
	return nil
}

func (x *DateScheduleConfig) GetSpecificDates() []*timestamppb.Timestamp {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

// 预约单核心数据
type Appointment struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 预约ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 商家ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 客户ID
	CustomerId int64 `protobuf:"varint,4,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 预约状态
	// (-- api-linter: core::0216::state-field-output-only=disabled
	//
	//	aip.dev/not-precedent: 状态字段需要可写 --)
	Status AppointmentState `protobuf:"varint,5,opt,name=status,proto3,enum=backend.proto.fulfillment.v1.AppointmentState" json:"status,omitempty"`
	// 服务位图
	ServiceItemType int32 `protobuf:"varint,6,opt,name=service_item_type,json=serviceItemType,proto3" json:"service_item_type,omitempty"`
	// 颜色代码
	ColorCode string `protobuf:"bytes,7,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// 开始时间
	StartTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 结束时间
	EndTime *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 宠物详情列表
	Pets []*PetDetail `protobuf:"bytes,10,rep,name=pets,proto3" json:"pets,omitempty"`
	// 来源
	Source AppointmentSource `protobuf:"varint,11,opt,name=source,proto3,enum=backend.proto.fulfillment.v1.AppointmentSource" json:"source,omitempty"`
	// 所有宠物开始时间是否相同
	// (-- api-linter: core::0142::time-field-type=disabled
	//
	//	aip.dev/not-precedent: This is a boolean field, not a timestamp field --)
	AllPetsStartSameTime bool `protobuf:"varint,12,opt,name=all_pets_start_same_time,json=allPetsStartSameTime,proto3" json:"all_pets_start_same_time,omitempty"`
	// 备注列表
	Notes         []*Note `protobuf:"bytes,13,rep,name=notes,proto3" json:"notes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Appointment) Reset() {
	*x = Appointment{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Appointment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Appointment) ProtoMessage() {}

func (x *Appointment) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Appointment.ProtoReflect.Descriptor instead.
func (*Appointment) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{6}
}

func (x *Appointment) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Appointment) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *Appointment) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *Appointment) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *Appointment) GetStatus() AppointmentState {
	if x != nil {
		return x.Status
	}
	return AppointmentState_APPOINTMENT_STATE_UNSPECIFIED
}

func (x *Appointment) GetServiceItemType() int32 {
	if x != nil {
		return x.ServiceItemType
	}
	return 0
}

func (x *Appointment) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *Appointment) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *Appointment) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *Appointment) GetPets() []*PetDetail {
	if x != nil {
		return x.Pets
	}
	return nil
}

func (x *Appointment) GetSource() AppointmentSource {
	if x != nil {
		return x.Source
	}
	return AppointmentSource_APPOINTMENT_SOURCE_UNSPECIFIED
}

func (x *Appointment) GetAllPetsStartSameTime() bool {
	if x != nil {
		return x.AllPetsStartSameTime
	}
	return false
}

func (x *Appointment) GetNotes() []*Note {
	if x != nil {
		return x.Notes
	}
	return nil
}

// Pre-auth enable definition
// Can pass the association when creating an appointment,
// or can send a request cof link to associate at the time of callback.
type PreAuthEnableDef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Enable pre-auth
	Enable bool `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
	// Stripe payment method id, such as pm_1ObH0nIZwcIFVLGrR5SnOkLU
	PaymentMethodId *string `protobuf:"bytes,2,opt,name=payment_method_id,json=paymentMethodId,proto3,oneof" json:"payment_method_id,omitempty"`
	// Card brand and last 4 digits, such as Visa(1111)
	CardBrandLast4 *string `protobuf:"bytes,3,opt,name=card_brand_last4,json=cardBrandLast4,proto3,oneof" json:"card_brand_last4,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PreAuthEnableDef) Reset() {
	*x = PreAuthEnableDef{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PreAuthEnableDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreAuthEnableDef) ProtoMessage() {}

func (x *PreAuthEnableDef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreAuthEnableDef.ProtoReflect.Descriptor instead.
func (*PreAuthEnableDef) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{7}
}

func (x *PreAuthEnableDef) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *PreAuthEnableDef) GetPaymentMethodId() string {
	if x != nil && x.PaymentMethodId != nil {
		return *x.PaymentMethodId
	}
	return ""
}

func (x *PreAuthEnableDef) GetCardBrandLast4() string {
	if x != nil && x.CardBrandLast4 != nil {
		return *x.CardBrandLast4
	}
	return ""
}

// Pet belonging
type PetBelonging struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// pet area
	Area *string `protobuf:"bytes,3,opt,name=area,proto3,oneof" json:"area,omitempty"`
	// pet photo uri
	PhotoUri      *string `protobuf:"bytes,4,opt,name=photo_uri,json=photoUri,proto3,oneof" json:"photo_uri,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PetBelonging) Reset() {
	*x = PetBelonging{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetBelonging) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetBelonging) ProtoMessage() {}

func (x *PetBelonging) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetBelonging.ProtoReflect.Descriptor instead.
func (*PetBelonging) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{8}
}

func (x *PetBelonging) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PetBelonging) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PetBelonging) GetArea() string {
	if x != nil && x.Area != nil {
		return *x.Area
	}
	return ""
}

func (x *PetBelonging) GetPhotoUri() string {
	if x != nil && x.PhotoUri != nil {
		return *x.PhotoUri
	}
	return ""
}

// 宠物信息
type PetDetail struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 宠物ID
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// 服务实例列表
	Services      []*ServiceInstanceImpl `protobuf:"bytes,2,rep,name=services,proto3" json:"services,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PetDetail) Reset() {
	*x = PetDetail{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetDetail) ProtoMessage() {}

func (x *PetDetail) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetDetail.ProtoReflect.Descriptor instead.
func (*PetDetail) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{9}
}

func (x *PetDetail) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PetDetail) GetServices() []*ServiceInstanceImpl {
	if x != nil {
		return x.Services
	}
	return nil
}

// 服务实例
type ServiceInstanceImpl struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务实例ID
	ServiceInstanceId int64 `protobuf:"varint,1,opt,name=service_instance_id,json=serviceInstanceId,proto3" json:"service_instance_id,omitempty"`
	// 服务模板ID
	Id int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	// 服务开始时间
	StartTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 服务结束时间
	EndTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 员工ID
	StaffId *int64 `protobuf:"varint,5,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// 工作模式 (0 - parallel, 1 - sequence)
	WorkMode *WorkMode `protobuf:"varint,6,opt,name=work_mode,json=workMode,proto3,enum=backend.proto.fulfillment.v1.WorkMode,oneof" json:"work_mode,omitempty"`
	// 子服务实例列表
	SubServiceInstances []*ServiceInstanceImpl `protobuf:"bytes,7,rep,name=sub_service_instances,json=subServiceInstances,proto3" json:"sub_service_instances,omitempty"`
	// 服务费用列表
	Charges []*ServiceCharge `protobuf:"bytes,8,rep,name=charges,proto3" json:"charges,omitempty"`
	// 喂养用药列表
	FeedingMedications []*FeedingMedication `protobuf:"bytes,9,rep,name=feeding_medications,json=feedingMedications,proto3" json:"feeding_medications,omitempty"`
	// 服务细则（包含所有执行细节）
	ServiceDetail *ServiceDetail `protobuf:"bytes,10,opt,name=service_detail,json=serviceDetail,proto3,oneof" json:"service_detail,omitempty"`
	// 价格
	Price *int64 `protobuf:"varint,11,opt,name=price,proto3,oneof" json:"price,omitempty"`
	// 时长
	Duration      *int64 `protobuf:"varint,12,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceInstanceImpl) Reset() {
	*x = ServiceInstanceImpl{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceInstanceImpl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceInstanceImpl) ProtoMessage() {}

func (x *ServiceInstanceImpl) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceInstanceImpl.ProtoReflect.Descriptor instead.
func (*ServiceInstanceImpl) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{10}
}

func (x *ServiceInstanceImpl) GetServiceInstanceId() int64 {
	if x != nil {
		return x.ServiceInstanceId
	}
	return 0
}

func (x *ServiceInstanceImpl) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceInstanceImpl) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *ServiceInstanceImpl) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *ServiceInstanceImpl) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *ServiceInstanceImpl) GetWorkMode() WorkMode {
	if x != nil && x.WorkMode != nil {
		return *x.WorkMode
	}
	return WorkMode_WORK_MODE_UNSPECIFIED
}

func (x *ServiceInstanceImpl) GetSubServiceInstances() []*ServiceInstanceImpl {
	if x != nil {
		return x.SubServiceInstances
	}
	return nil
}

func (x *ServiceInstanceImpl) GetCharges() []*ServiceCharge {
	if x != nil {
		return x.Charges
	}
	return nil
}

func (x *ServiceInstanceImpl) GetFeedingMedications() []*FeedingMedication {
	if x != nil {
		return x.FeedingMedications
	}
	return nil
}

func (x *ServiceInstanceImpl) GetServiceDetail() *ServiceDetail {
	if x != nil {
		return x.ServiceDetail
	}
	return nil
}

func (x *ServiceInstanceImpl) GetPrice() int64 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *ServiceInstanceImpl) GetDuration() int64 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

// 服务细则
type ServiceDetail struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 员工细则列表
	StaffDetails  []*StaffDetail `protobuf:"bytes,1,rep,name=staff_details,json=staffDetails,proto3" json:"staff_details,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceDetail) Reset() {
	*x = ServiceDetail{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceDetail) ProtoMessage() {}

func (x *ServiceDetail) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceDetail.ProtoReflect.Descriptor instead.
func (*ServiceDetail) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{11}
}

func (x *ServiceDetail) GetStaffDetails() []*StaffDetail {
	if x != nil {
		return x.StaffDetails
	}
	return nil
}

// 员工细则
type StaffDetail struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// operation name
	OperationName *string `protobuf:"bytes,2,opt,name=operation_name,json=operationName,proto3,oneof" json:"operation_name,omitempty"`
	// start time, 格式: "15:30:15"
	StartTime *timeofday.TimeOfDay `protobuf:"bytes,3,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
	// duration
	Duration *durationpb.Duration `protobuf:"bytes,4,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	// price ratio
	PriceRatio *float64 `protobuf:"fixed64,5,opt,name=price_ratio,json=priceRatio,proto3,oneof" json:"price_ratio,omitempty"`
	// exact price
	Price         *float64 `protobuf:"fixed64,6,opt,name=price,proto3,oneof" json:"price,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StaffDetail) Reset() {
	*x = StaffDetail{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StaffDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffDetail) ProtoMessage() {}

func (x *StaffDetail) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffDetail.ProtoReflect.Descriptor instead.
func (*StaffDetail) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{12}
}

func (x *StaffDetail) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *StaffDetail) GetOperationName() string {
	if x != nil && x.OperationName != nil {
		return *x.OperationName
	}
	return ""
}

func (x *StaffDetail) GetStartTime() *timeofday.TimeOfDay {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *StaffDetail) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

func (x *StaffDetail) GetPriceRatio() float64 {
	if x != nil && x.PriceRatio != nil {
		return *x.PriceRatio
	}
	return 0
}

func (x *StaffDetail) GetPrice() float64 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

// 服务费用
type ServiceCharge struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 费用名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 费用金额
	Amount float64 `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	// 费用描述
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// 费用类型
	ChargeType    int32 `protobuf:"varint,4,opt,name=charge_type,json=chargeType,proto3" json:"charge_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceCharge) Reset() {
	*x = ServiceCharge{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceCharge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceCharge) ProtoMessage() {}

func (x *ServiceCharge) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceCharge.ProtoReflect.Descriptor instead.
func (*ServiceCharge) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{13}
}

func (x *ServiceCharge) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceCharge) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *ServiceCharge) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ServiceCharge) GetChargeType() int32 {
	if x != nil {
		return x.ChargeType
	}
	return 0
}

// 备注
type Note struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 备注类型
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	Type AppointmentNoteType `protobuf:"varint,1,opt,name=type,proto3,enum=backend.proto.fulfillment.v1.AppointmentNoteType" json:"type,omitempty"`
	// 备注内容
	Content       string `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Note) Reset() {
	*x = Note{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Note) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Note) ProtoMessage() {}

func (x *Note) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Note.ProtoReflect.Descriptor instead.
func (*Note) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{14}
}

func (x *Note) GetType() AppointmentNoteType {
	if x != nil {
		return x.Type
	}
	return AppointmentNoteType_APPOINTMENT_NOTE_TYPE_UNSPECIFIED
}

func (x *Note) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

// 喂养用药
type FeedingMedication struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 喂养规则
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	FeedingRule string `protobuf:"bytes,1,opt,name=feeding_rule,json=feedingRule,proto3" json:"feeding_rule,omitempty"`
	// 用药规则
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	MedicationRule string `protobuf:"bytes,2,opt,name=medication_rule,json=medicationRule,proto3" json:"medication_rule,omitempty"`
	// 用药计划列表
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	Medications   []*MedicationSchedule `protobuf:"bytes,3,rep,name=medications,proto3" json:"medications,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FeedingMedication) Reset() {
	*x = FeedingMedication{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FeedingMedication) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeedingMedication) ProtoMessage() {}

func (x *FeedingMedication) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeedingMedication.ProtoReflect.Descriptor instead.
func (*FeedingMedication) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{15}
}

func (x *FeedingMedication) GetFeedingRule() string {
	if x != nil {
		return x.FeedingRule
	}
	return ""
}

func (x *FeedingMedication) GetMedicationRule() string {
	if x != nil {
		return x.MedicationRule
	}
	return ""
}

func (x *FeedingMedication) GetMedications() []*MedicationSchedule {
	if x != nil {
		return x.Medications
	}
	return nil
}

// 用药计划
type MedicationSchedule struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 药品名称
	// (-- api-linter: core::0122::name-suffix=disabled
	//
	//	aip.dev/not-precedent: medication is clear and appropriate --)
	Medication string `protobuf:"bytes,1,opt,name=medication,proto3" json:"medication,omitempty"`
	// 剂量
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	Dosage string `protobuf:"bytes,2,opt,name=dosage,proto3" json:"dosage,omitempty"`
	// 频率(如"每日两次")
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	Frequency string `protobuf:"bytes,3,opt,name=frequency,proto3" json:"frequency,omitempty"`
	// 给药方式(如"口服")
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	AdministrationMethod string `protobuf:"bytes,4,opt,name=administration_method,json=administrationMethod,proto3" json:"administration_method,omitempty"`
	// 注意事项
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	Notes         string `protobuf:"bytes,5,opt,name=notes,proto3" json:"notes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MedicationSchedule) Reset() {
	*x = MedicationSchedule{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MedicationSchedule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MedicationSchedule) ProtoMessage() {}

func (x *MedicationSchedule) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MedicationSchedule.ProtoReflect.Descriptor instead.
func (*MedicationSchedule) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{16}
}

func (x *MedicationSchedule) GetMedication() string {
	if x != nil {
		return x.Medication
	}
	return ""
}

func (x *MedicationSchedule) GetDosage() string {
	if x != nil {
		return x.Dosage
	}
	return ""
}

func (x *MedicationSchedule) GetFrequency() string {
	if x != nil {
		return x.Frequency
	}
	return ""
}

func (x *MedicationSchedule) GetAdministrationMethod() string {
	if x != nil {
		return x.AdministrationMethod
	}
	return ""
}

func (x *MedicationSchedule) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

// 服务选项
type ServiceOption struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务选项模板ID
	ServiceOptionTemplateId int64 `protobuf:"varint,1,opt,name=service_option_template_id,json=serviceOptionTemplateId,proto3" json:"service_option_template_id,omitempty"`
	// 开始时间
	StartTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 结束时间
	EndTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 选项名称
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// 数量
	Quantity int32 `protobuf:"varint,5,opt,name=quantity,proto3" json:"quantity,omitempty"`
	// 价格
	Price float64 `protobuf:"fixed64,6,opt,name=price,proto3" json:"price,omitempty"`
	// 税费
	Tax float64 `protobuf:"fixed64,7,opt,name=tax,proto3" json:"tax,omitempty"`
	// 每天执行数量
	QuantityPerDay int64 `protobuf:"varint,8,opt,name=quantity_per_day,json=quantityPerDay,proto3" json:"quantity_per_day,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ServiceOption) Reset() {
	*x = ServiceOption{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceOption) ProtoMessage() {}

func (x *ServiceOption) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceOption.ProtoReflect.Descriptor instead.
func (*ServiceOption) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{17}
}

func (x *ServiceOption) GetServiceOptionTemplateId() int64 {
	if x != nil {
		return x.ServiceOptionTemplateId
	}
	return 0
}

func (x *ServiceOption) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *ServiceOption) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *ServiceOption) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceOption) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *ServiceOption) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *ServiceOption) GetTax() float64 {
	if x != nil {
		return x.Tax
	}
	return 0
}

func (x *ServiceOption) GetQuantityPerDay() int64 {
	if x != nil {
		return x.QuantityPerDay
	}
	return 0
}

// 元数据
type Metadata struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 标签映射
	Tags          map[string]string `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Metadata) Reset() {
	*x = Metadata{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Metadata) ProtoMessage() {}

func (x *Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Metadata.ProtoReflect.Descriptor instead.
func (*Metadata) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{18}
}

func (x *Metadata) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

// 预约单操作结果
type AppointmentOperationResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 操作类型
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	OperationType string `protobuf:"bytes,1,opt,name=operation_type,json=operationType,proto3" json:"operation_type,omitempty"` // "start_time", "end_time", "color_code", "status"
	// 操作是否成功
	Success bool `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	// 错误消息
	ErrorMessage string `protobuf:"bytes,3,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	// 操作前的值（如果更新失败，用于回滚）
	OldStartTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=old_start_time,json=oldStartTime,proto3,oneof" json:"old_start_time,omitempty"`
	// 操作前的结束时间
	OldEndTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=old_end_time,json=oldEndTime,proto3,oneof" json:"old_end_time,omitempty"`
	// 操作前的颜色代码
	OldColorCode *string `protobuf:"bytes,6,opt,name=old_color_code,json=oldColorCode,proto3,oneof" json:"old_color_code,omitempty"`
	// 操作前的状态
	// (-- api-linter: core::0216::state-field-output-only=disabled
	//
	//	aip.dev/not-precedent: 状态字段需要可写 --)
	OldStatus *AppointmentState `protobuf:"varint,7,opt,name=old_status,json=oldStatus,proto3,enum=backend.proto.fulfillment.v1.AppointmentState,oneof" json:"old_status,omitempty"`
	// 操作后的值
	NewStartTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=new_start_time,json=newStartTime,proto3,oneof" json:"new_start_time,omitempty"`
	// 操作后的结束时间
	NewEndTime *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=new_end_time,json=newEndTime,proto3,oneof" json:"new_end_time,omitempty"`
	// 操作后的颜色代码
	NewColorCode *string `protobuf:"bytes,10,opt,name=new_color_code,json=newColorCode,proto3,oneof" json:"new_color_code,omitempty"`
	// 操作后的状态
	// (-- api-linter: core::0216::state-field-output-only=disabled
	//
	//	aip.dev/not-precedent: 状态字段需要可写 --)
	NewStatus     *AppointmentState `protobuf:"varint,11,opt,name=new_status,json=newStatus,proto3,enum=backend.proto.fulfillment.v1.AppointmentState,oneof" json:"new_status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AppointmentOperationResult) Reset() {
	*x = AppointmentOperationResult{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AppointmentOperationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentOperationResult) ProtoMessage() {}

func (x *AppointmentOperationResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentOperationResult.ProtoReflect.Descriptor instead.
func (*AppointmentOperationResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{19}
}

func (x *AppointmentOperationResult) GetOperationType() string {
	if x != nil {
		return x.OperationType
	}
	return ""
}

func (x *AppointmentOperationResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *AppointmentOperationResult) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *AppointmentOperationResult) GetOldStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.OldStartTime
	}
	return nil
}

func (x *AppointmentOperationResult) GetOldEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.OldEndTime
	}
	return nil
}

func (x *AppointmentOperationResult) GetOldColorCode() string {
	if x != nil && x.OldColorCode != nil {
		return *x.OldColorCode
	}
	return ""
}

func (x *AppointmentOperationResult) GetOldStatus() AppointmentState {
	if x != nil && x.OldStatus != nil {
		return *x.OldStatus
	}
	return AppointmentState_APPOINTMENT_STATE_UNSPECIFIED
}

func (x *AppointmentOperationResult) GetNewStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.NewStartTime
	}
	return nil
}

func (x *AppointmentOperationResult) GetNewEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.NewEndTime
	}
	return nil
}

func (x *AppointmentOperationResult) GetNewColorCode() string {
	if x != nil && x.NewColorCode != nil {
		return *x.NewColorCode
	}
	return ""
}

func (x *AppointmentOperationResult) GetNewStatus() AppointmentState {
	if x != nil && x.NewStatus != nil {
		return *x.NewStatus
	}
	return AppointmentState_APPOINTMENT_STATE_UNSPECIFIED
}

// 宠物操作结果
type PetOperationResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 宠物ID
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// 操作模式
	OperationMode OperationMode `protobuf:"varint,2,opt,name=operation_mode,json=operationMode,proto3,enum=backend.proto.fulfillment.v1.OperationMode" json:"operation_mode,omitempty"`
	// 操作是否成功
	Success bool `protobuf:"varint,3,opt,name=success,proto3" json:"success,omitempty"`
	// 错误消息
	ErrorMessage string `protobuf:"bytes,4,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	// 操作详情
	//
	// Types that are valid to be assigned to OperationDetail:
	//
	//	*PetOperationResult_DeleteResult
	//	*PetOperationResult_CreateResult
	//	*PetOperationResult_UpdateResult
	OperationDetail isPetOperationResult_OperationDetail `protobuf_oneof:"operation_detail"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *PetOperationResult) Reset() {
	*x = PetOperationResult{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetOperationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetOperationResult) ProtoMessage() {}

func (x *PetOperationResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetOperationResult.ProtoReflect.Descriptor instead.
func (*PetOperationResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{20}
}

func (x *PetOperationResult) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PetOperationResult) GetOperationMode() OperationMode {
	if x != nil {
		return x.OperationMode
	}
	return OperationMode_OPERATION_MODE_UNSPECIFIED
}

func (x *PetOperationResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *PetOperationResult) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *PetOperationResult) GetOperationDetail() isPetOperationResult_OperationDetail {
	if x != nil {
		return x.OperationDetail
	}
	return nil
}

func (x *PetOperationResult) GetDeleteResult() *PetDeleteResult {
	if x != nil {
		if x, ok := x.OperationDetail.(*PetOperationResult_DeleteResult); ok {
			return x.DeleteResult
		}
	}
	return nil
}

func (x *PetOperationResult) GetCreateResult() *PetCreateResult {
	if x != nil {
		if x, ok := x.OperationDetail.(*PetOperationResult_CreateResult); ok {
			return x.CreateResult
		}
	}
	return nil
}

func (x *PetOperationResult) GetUpdateResult() *PetUpdateResult {
	if x != nil {
		if x, ok := x.OperationDetail.(*PetOperationResult_UpdateResult); ok {
			return x.UpdateResult
		}
	}
	return nil
}

type isPetOperationResult_OperationDetail interface {
	isPetOperationResult_OperationDetail()
}

type PetOperationResult_DeleteResult struct {
	// 删除结果
	DeleteResult *PetDeleteResult `protobuf:"bytes,5,opt,name=delete_result,json=deleteResult,proto3,oneof"`
}

type PetOperationResult_CreateResult struct {
	// 创建结果
	CreateResult *PetCreateResult `protobuf:"bytes,6,opt,name=create_result,json=createResult,proto3,oneof"`
}

type PetOperationResult_UpdateResult struct {
	// 更新结果
	UpdateResult *PetUpdateResult `protobuf:"bytes,7,opt,name=update_result,json=updateResult,proto3,oneof"`
}

func (*PetOperationResult_DeleteResult) isPetOperationResult_OperationDetail() {}

func (*PetOperationResult_CreateResult) isPetOperationResult_OperationDetail() {}

func (*PetOperationResult_UpdateResult) isPetOperationResult_OperationDetail() {}

// 宠物删除结果
type PetDeleteResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 是否删除成功
	Deleted bool `protobuf:"varint,1,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// 删除的服务数量
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	DeletedServiceCount int32 `protobuf:"varint,2,opt,name=deleted_service_count,json=deletedServiceCount,proto3" json:"deleted_service_count,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *PetDeleteResult) Reset() {
	*x = PetDeleteResult{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetDeleteResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetDeleteResult) ProtoMessage() {}

func (x *PetDeleteResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetDeleteResult.ProtoReflect.Descriptor instead.
func (*PetDeleteResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{21}
}

func (x *PetDeleteResult) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *PetDeleteResult) GetDeletedServiceCount() int32 {
	if x != nil {
		return x.DeletedServiceCount
	}
	return 0
}

// 宠物创建结果
type PetCreateResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 新宠物ID
	NewPetId int64 `protobuf:"varint,1,opt,name=new_pet_id,json=newPetId,proto3" json:"new_pet_id,omitempty"`
	// 创建的服务数量
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	CreatedServiceCount int32 `protobuf:"varint,2,opt,name=created_service_count,json=createdServiceCount,proto3" json:"created_service_count,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *PetCreateResult) Reset() {
	*x = PetCreateResult{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetCreateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetCreateResult) ProtoMessage() {}

func (x *PetCreateResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetCreateResult.ProtoReflect.Descriptor instead.
func (*PetCreateResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{22}
}

func (x *PetCreateResult) GetNewPetId() int64 {
	if x != nil {
		return x.NewPetId
	}
	return 0
}

func (x *PetCreateResult) GetCreatedServiceCount() int32 {
	if x != nil {
		return x.CreatedServiceCount
	}
	return 0
}

// 宠物更新结果
type PetUpdateResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 更新后的宠物
	UpdatedPet *PetDetail `protobuf:"bytes,1,opt,name=updated_pet,json=updatedPet,proto3" json:"updated_pet,omitempty"`
	// 更新的服务数量
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	UpdatedServiceCount int32 `protobuf:"varint,2,opt,name=updated_service_count,json=updatedServiceCount,proto3" json:"updated_service_count,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *PetUpdateResult) Reset() {
	*x = PetUpdateResult{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetUpdateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetUpdateResult) ProtoMessage() {}

func (x *PetUpdateResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetUpdateResult.ProtoReflect.Descriptor instead.
func (*PetUpdateResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{23}
}

func (x *PetUpdateResult) GetUpdatedPet() *PetDetail {
	if x != nil {
		return x.UpdatedPet
	}
	return nil
}

func (x *PetUpdateResult) GetUpdatedServiceCount() int32 {
	if x != nil {
		return x.UpdatedServiceCount
	}
	return 0
}

// 服务操作结果
type ServiceOperationResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务实例ID
	ServiceInstanceId int64 `protobuf:"varint,1,opt,name=service_instance_id,json=serviceInstanceId,proto3" json:"service_instance_id,omitempty"`
	// 宠物ID
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// 操作模式
	OperationMode OperationMode `protobuf:"varint,3,opt,name=operation_mode,json=operationMode,proto3,enum=backend.proto.fulfillment.v1.OperationMode" json:"operation_mode,omitempty"`
	// 操作是否成功
	Success bool `protobuf:"varint,4,opt,name=success,proto3" json:"success,omitempty"`
	// 错误消息
	ErrorMessage string `protobuf:"bytes,5,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	// 操作详情
	//
	// Types that are valid to be assigned to OperationDetail:
	//
	//	*ServiceOperationResult_DeleteResult
	//	*ServiceOperationResult_CreateResult
	//	*ServiceOperationResult_UpdateResult
	OperationDetail isServiceOperationResult_OperationDetail `protobuf_oneof:"operation_detail"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ServiceOperationResult) Reset() {
	*x = ServiceOperationResult{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceOperationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceOperationResult) ProtoMessage() {}

func (x *ServiceOperationResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceOperationResult.ProtoReflect.Descriptor instead.
func (*ServiceOperationResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{24}
}

func (x *ServiceOperationResult) GetServiceInstanceId() int64 {
	if x != nil {
		return x.ServiceInstanceId
	}
	return 0
}

func (x *ServiceOperationResult) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ServiceOperationResult) GetOperationMode() OperationMode {
	if x != nil {
		return x.OperationMode
	}
	return OperationMode_OPERATION_MODE_UNSPECIFIED
}

func (x *ServiceOperationResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ServiceOperationResult) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *ServiceOperationResult) GetOperationDetail() isServiceOperationResult_OperationDetail {
	if x != nil {
		return x.OperationDetail
	}
	return nil
}

func (x *ServiceOperationResult) GetDeleteResult() *ServiceDeleteResult {
	if x != nil {
		if x, ok := x.OperationDetail.(*ServiceOperationResult_DeleteResult); ok {
			return x.DeleteResult
		}
	}
	return nil
}

func (x *ServiceOperationResult) GetCreateResult() *ServiceCreateResult {
	if x != nil {
		if x, ok := x.OperationDetail.(*ServiceOperationResult_CreateResult); ok {
			return x.CreateResult
		}
	}
	return nil
}

func (x *ServiceOperationResult) GetUpdateResult() *ServiceUpdateResult {
	if x != nil {
		if x, ok := x.OperationDetail.(*ServiceOperationResult_UpdateResult); ok {
			return x.UpdateResult
		}
	}
	return nil
}

type isServiceOperationResult_OperationDetail interface {
	isServiceOperationResult_OperationDetail()
}

type ServiceOperationResult_DeleteResult struct {
	// 删除结果
	DeleteResult *ServiceDeleteResult `protobuf:"bytes,6,opt,name=delete_result,json=deleteResult,proto3,oneof"`
}

type ServiceOperationResult_CreateResult struct {
	// 创建结果
	CreateResult *ServiceCreateResult `protobuf:"bytes,7,opt,name=create_result,json=createResult,proto3,oneof"`
}

type ServiceOperationResult_UpdateResult struct {
	// 更新结果
	UpdateResult *ServiceUpdateResult `protobuf:"bytes,8,opt,name=update_result,json=updateResult,proto3,oneof"`
}

func (*ServiceOperationResult_DeleteResult) isServiceOperationResult_OperationDetail() {}

func (*ServiceOperationResult_CreateResult) isServiceOperationResult_OperationDetail() {}

func (*ServiceOperationResult_UpdateResult) isServiceOperationResult_OperationDetail() {}

// 服务删除结果
type ServiceDeleteResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 是否删除成功
	Deleted bool `protobuf:"varint,1,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// 删除的附加服务数量
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	DeletedOptionCount int32 `protobuf:"varint,2,opt,name=deleted_option_count,json=deletedOptionCount,proto3" json:"deleted_option_count,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ServiceDeleteResult) Reset() {
	*x = ServiceDeleteResult{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceDeleteResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceDeleteResult) ProtoMessage() {}

func (x *ServiceDeleteResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceDeleteResult.ProtoReflect.Descriptor instead.
func (*ServiceDeleteResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{25}
}

func (x *ServiceDeleteResult) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *ServiceDeleteResult) GetDeletedOptionCount() int32 {
	if x != nil {
		return x.DeletedOptionCount
	}
	return 0
}

// 服务创建结果
type ServiceCreateResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 新服务实例ID
	NewServiceInstanceId int64 `protobuf:"varint,1,opt,name=new_service_instance_id,json=newServiceInstanceId,proto3" json:"new_service_instance_id,omitempty"`
	// 创建的附加服务数量
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	CreatedOptionCount int32 `protobuf:"varint,2,opt,name=created_option_count,json=createdOptionCount,proto3" json:"created_option_count,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ServiceCreateResult) Reset() {
	*x = ServiceCreateResult{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceCreateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceCreateResult) ProtoMessage() {}

func (x *ServiceCreateResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceCreateResult.ProtoReflect.Descriptor instead.
func (*ServiceCreateResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{26}
}

func (x *ServiceCreateResult) GetNewServiceInstanceId() int64 {
	if x != nil {
		return x.NewServiceInstanceId
	}
	return 0
}

func (x *ServiceCreateResult) GetCreatedOptionCount() int32 {
	if x != nil {
		return x.CreatedOptionCount
	}
	return 0
}

// 服务更新结果
type ServiceUpdateResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 更新后的服务
	UpdatedService *ServiceInstanceImpl `protobuf:"bytes,1,opt,name=updated_service,json=updatedService,proto3" json:"updated_service,omitempty"`
	// 更新的附加服务数量
	// (-- api-linter: core::0192::only-leading-comments=disabled
	//
	//	aip.dev/not-precedent: inline comment is clear and helpful --)
	UpdatedOptionCount int32 `protobuf:"varint,2,opt,name=updated_option_count,json=updatedOptionCount,proto3" json:"updated_option_count,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ServiceUpdateResult) Reset() {
	*x = ServiceUpdateResult{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceUpdateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceUpdateResult) ProtoMessage() {}

func (x *ServiceUpdateResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceUpdateResult.ProtoReflect.Descriptor instead.
func (*ServiceUpdateResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{27}
}

func (x *ServiceUpdateResult) GetUpdatedService() *ServiceInstanceImpl {
	if x != nil {
		return x.UpdatedService
	}
	return nil
}

func (x *ServiceUpdateResult) GetUpdatedOptionCount() int32 {
	if x != nil {
		return x.UpdatedOptionCount
	}
	return 0
}

// 附加服务操作结果
type OptionOperationResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 服务选项ID
	ServiceOptionId int64 `protobuf:"varint,1,opt,name=service_option_id,json=serviceOptionId,proto3" json:"service_option_id,omitempty"`
	// 服务实例ID
	ServiceInstanceId int64 `protobuf:"varint,2,opt,name=service_instance_id,json=serviceInstanceId,proto3" json:"service_instance_id,omitempty"`
	// 操作模式
	OperationMode OperationMode `protobuf:"varint,3,opt,name=operation_mode,json=operationMode,proto3,enum=backend.proto.fulfillment.v1.OperationMode" json:"operation_mode,omitempty"`
	// 操作是否成功
	Success bool `protobuf:"varint,4,opt,name=success,proto3" json:"success,omitempty"`
	// 错误消息
	ErrorMessage string `protobuf:"bytes,5,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	// 操作详情
	//
	// Types that are valid to be assigned to OperationDetail:
	//
	//	*OptionOperationResult_DeleteResult
	//	*OptionOperationResult_CreateResult
	//	*OptionOperationResult_UpdateResult
	OperationDetail isOptionOperationResult_OperationDetail `protobuf_oneof:"operation_detail"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *OptionOperationResult) Reset() {
	*x = OptionOperationResult{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OptionOperationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OptionOperationResult) ProtoMessage() {}

func (x *OptionOperationResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OptionOperationResult.ProtoReflect.Descriptor instead.
func (*OptionOperationResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{28}
}

func (x *OptionOperationResult) GetServiceOptionId() int64 {
	if x != nil {
		return x.ServiceOptionId
	}
	return 0
}

func (x *OptionOperationResult) GetServiceInstanceId() int64 {
	if x != nil {
		return x.ServiceInstanceId
	}
	return 0
}

func (x *OptionOperationResult) GetOperationMode() OperationMode {
	if x != nil {
		return x.OperationMode
	}
	return OperationMode_OPERATION_MODE_UNSPECIFIED
}

func (x *OptionOperationResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *OptionOperationResult) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *OptionOperationResult) GetOperationDetail() isOptionOperationResult_OperationDetail {
	if x != nil {
		return x.OperationDetail
	}
	return nil
}

func (x *OptionOperationResult) GetDeleteResult() *OptionDeleteResult {
	if x != nil {
		if x, ok := x.OperationDetail.(*OptionOperationResult_DeleteResult); ok {
			return x.DeleteResult
		}
	}
	return nil
}

func (x *OptionOperationResult) GetCreateResult() *OptionCreateResult {
	if x != nil {
		if x, ok := x.OperationDetail.(*OptionOperationResult_CreateResult); ok {
			return x.CreateResult
		}
	}
	return nil
}

func (x *OptionOperationResult) GetUpdateResult() *OptionUpdateResult {
	if x != nil {
		if x, ok := x.OperationDetail.(*OptionOperationResult_UpdateResult); ok {
			return x.UpdateResult
		}
	}
	return nil
}

type isOptionOperationResult_OperationDetail interface {
	isOptionOperationResult_OperationDetail()
}

type OptionOperationResult_DeleteResult struct {
	// 删除结果
	DeleteResult *OptionDeleteResult `protobuf:"bytes,6,opt,name=delete_result,json=deleteResult,proto3,oneof"`
}

type OptionOperationResult_CreateResult struct {
	// 创建结果
	CreateResult *OptionCreateResult `protobuf:"bytes,7,opt,name=create_result,json=createResult,proto3,oneof"`
}

type OptionOperationResult_UpdateResult struct {
	// 更新结果
	UpdateResult *OptionUpdateResult `protobuf:"bytes,8,opt,name=update_result,json=updateResult,proto3,oneof"`
}

func (*OptionOperationResult_DeleteResult) isOptionOperationResult_OperationDetail() {}

func (*OptionOperationResult_CreateResult) isOptionOperationResult_OperationDetail() {}

func (*OptionOperationResult_UpdateResult) isOptionOperationResult_OperationDetail() {}

// 选项删除结果
type OptionDeleteResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 是否删除成功
	Deleted       bool `protobuf:"varint,1,opt,name=deleted,proto3" json:"deleted,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OptionDeleteResult) Reset() {
	*x = OptionDeleteResult{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OptionDeleteResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OptionDeleteResult) ProtoMessage() {}

func (x *OptionDeleteResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OptionDeleteResult.ProtoReflect.Descriptor instead.
func (*OptionDeleteResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{29}
}

func (x *OptionDeleteResult) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

// 选项创建结果
type OptionCreateResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 新服务选项ID
	NewServiceOptionId int64 `protobuf:"varint,1,opt,name=new_service_option_id,json=newServiceOptionId,proto3" json:"new_service_option_id,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *OptionCreateResult) Reset() {
	*x = OptionCreateResult{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OptionCreateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OptionCreateResult) ProtoMessage() {}

func (x *OptionCreateResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OptionCreateResult.ProtoReflect.Descriptor instead.
func (*OptionCreateResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{30}
}

func (x *OptionCreateResult) GetNewServiceOptionId() int64 {
	if x != nil {
		return x.NewServiceOptionId
	}
	return 0
}

// 选项更新结果
type OptionUpdateResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 更新后的选项
	UpdatedOption *ServiceOption `protobuf:"bytes,1,opt,name=updated_option,json=updatedOption,proto3" json:"updated_option,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OptionUpdateResult) Reset() {
	*x = OptionUpdateResult{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OptionUpdateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OptionUpdateResult) ProtoMessage() {}

func (x *OptionUpdateResult) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OptionUpdateResult.ProtoReflect.Descriptor instead.
func (*OptionUpdateResult) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{31}
}

func (x *OptionUpdateResult) GetUpdatedOption() *ServiceOption {
	if x != nil {
		return x.UpdatedOption
	}
	return nil
}

// Appointment pet medication schedule definition
type AppointmentPetMedicationScheduleDef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// medication amount, such as 1.2, 1/2, 1 etc.
	MedicationAmount string `protobuf:"bytes,1,opt,name=medication_amount,json=medicationAmount,proto3" json:"medication_amount,omitempty"`
	// medication unit, pet_metadata.metadata_value, metadata_name = 7
	MedicationUnit string `protobuf:"bytes,2,opt,name=medication_unit,json=medicationUnit,proto3" json:"medication_unit,omitempty"`
	// medication name, user input
	MedicationName string `protobuf:"bytes,3,opt,name=medication_name,json=medicationName,proto3" json:"medication_name,omitempty"`
	// medication source, user input
	MedicationNote *string `protobuf:"bytes,4,opt,name=medication_note,json=medicationNote,proto3,oneof" json:"medication_note,omitempty"`
	// medication time, medication time in minutes, 09:00 AM = 540, 09:30 AM = 570 etc.
	MedicationTimes []*BusinessPetScheduleTimeDef `protobuf:"bytes,6,rep,name=medication_times,json=medicationTimes,proto3" json:"medication_times,omitempty"`
	// feeding medication schedule selected date
	SelectedDate  *AppointmentPetMedicationScheduleDef_SelectedDateDef `protobuf:"bytes,7,opt,name=selected_date,json=selectedDate,proto3,oneof" json:"selected_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AppointmentPetMedicationScheduleDef) Reset() {
	*x = AppointmentPetMedicationScheduleDef{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AppointmentPetMedicationScheduleDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentPetMedicationScheduleDef) ProtoMessage() {}

func (x *AppointmentPetMedicationScheduleDef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentPetMedicationScheduleDef.ProtoReflect.Descriptor instead.
func (*AppointmentPetMedicationScheduleDef) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{32}
}

func (x *AppointmentPetMedicationScheduleDef) GetMedicationAmount() string {
	if x != nil {
		return x.MedicationAmount
	}
	return ""
}

func (x *AppointmentPetMedicationScheduleDef) GetMedicationUnit() string {
	if x != nil {
		return x.MedicationUnit
	}
	return ""
}

func (x *AppointmentPetMedicationScheduleDef) GetMedicationName() string {
	if x != nil {
		return x.MedicationName
	}
	return ""
}

func (x *AppointmentPetMedicationScheduleDef) GetMedicationNote() string {
	if x != nil && x.MedicationNote != nil {
		return *x.MedicationNote
	}
	return ""
}

func (x *AppointmentPetMedicationScheduleDef) GetMedicationTimes() []*BusinessPetScheduleTimeDef {
	if x != nil {
		return x.MedicationTimes
	}
	return nil
}

func (x *AppointmentPetMedicationScheduleDef) GetSelectedDate() *AppointmentPetMedicationScheduleDef_SelectedDateDef {
	if x != nil {
		return x.SelectedDate
	}
	return nil
}

// Appointment pet feeding schedule definition
type AppointmentPetFeedingScheduleDef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// feeding amount, such as 1.2, 1/2, 1 etc.
	FeedingAmount string `protobuf:"bytes,1,opt,name=feeding_amount,json=feedingAmount,proto3" json:"feeding_amount,omitempty"`
	// feeding unit, pet_metadata.metadata_value, metadata_name = 2
	FeedingUnit string `protobuf:"bytes,2,opt,name=feeding_unit,json=feedingUnit,proto3" json:"feeding_unit,omitempty"`
	// feeding type, pet_metadata.metadata_value, metadata_name = 3
	FeedingType string `protobuf:"bytes,3,opt,name=feeding_type,json=feedingType,proto3" json:"feeding_type,omitempty"`
	// feeding source, pet_metadata.metadata_value, metadata_name = 4
	FeedingSource string `protobuf:"bytes,4,opt,name=feeding_source,json=feedingSource,proto3" json:"feeding_source,omitempty"`
	// feeding instruction, pet_metadata.metadata_value, metadata_name = 5
	FeedingInstruction *string `protobuf:"bytes,5,opt,name=feeding_instruction,json=feedingInstruction,proto3,oneof" json:"feeding_instruction,omitempty"`
	// feeding time, feeding time in minutes, 09:00 AM = 540, 09:30 AM = 570 etc.
	FeedingTimes []*BusinessPetScheduleTimeDef `protobuf:"bytes,6,rep,name=feeding_times,json=feedingTimes,proto3" json:"feeding_times,omitempty"`
	// feeding note, user input
	FeedingNote   *string `protobuf:"bytes,7,opt,name=feeding_note,json=feedingNote,proto3,oneof" json:"feeding_note,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AppointmentPetFeedingScheduleDef) Reset() {
	*x = AppointmentPetFeedingScheduleDef{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AppointmentPetFeedingScheduleDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentPetFeedingScheduleDef) ProtoMessage() {}

func (x *AppointmentPetFeedingScheduleDef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentPetFeedingScheduleDef.ProtoReflect.Descriptor instead.
func (*AppointmentPetFeedingScheduleDef) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{33}
}

func (x *AppointmentPetFeedingScheduleDef) GetFeedingAmount() string {
	if x != nil {
		return x.FeedingAmount
	}
	return ""
}

func (x *AppointmentPetFeedingScheduleDef) GetFeedingUnit() string {
	if x != nil {
		return x.FeedingUnit
	}
	return ""
}

func (x *AppointmentPetFeedingScheduleDef) GetFeedingType() string {
	if x != nil {
		return x.FeedingType
	}
	return ""
}

func (x *AppointmentPetFeedingScheduleDef) GetFeedingSource() string {
	if x != nil {
		return x.FeedingSource
	}
	return ""
}

func (x *AppointmentPetFeedingScheduleDef) GetFeedingInstruction() string {
	if x != nil && x.FeedingInstruction != nil {
		return *x.FeedingInstruction
	}
	return ""
}

func (x *AppointmentPetFeedingScheduleDef) GetFeedingTimes() []*BusinessPetScheduleTimeDef {
	if x != nil {
		return x.FeedingTimes
	}
	return nil
}

func (x *AppointmentPetFeedingScheduleDef) GetFeedingNote() string {
	if x != nil && x.FeedingNote != nil {
		return *x.FeedingNote
	}
	return ""
}

// Business pet schedule time definition
type BusinessPetScheduleTimeDef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// schedule time, schedule time in minutes, 09:00 AM = 540, 09:30 AM = 570 etc.
	ScheduleTime int32 `protobuf:"varint,1,opt,name=schedule_time,json=scheduleTime,proto3" json:"schedule_time,omitempty"`
	// schedule extra json, such as schedule time label etc.
	ExtraJson     map[string]string `protobuf:"bytes,2,rep,name=extra_json,json=extraJson,proto3" json:"extra_json,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BusinessPetScheduleTimeDef) Reset() {
	*x = BusinessPetScheduleTimeDef{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BusinessPetScheduleTimeDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessPetScheduleTimeDef) ProtoMessage() {}

func (x *BusinessPetScheduleTimeDef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessPetScheduleTimeDef.ProtoReflect.Descriptor instead.
func (*BusinessPetScheduleTimeDef) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{34}
}

func (x *BusinessPetScheduleTimeDef) GetScheduleTime() int32 {
	if x != nil {
		return x.ScheduleTime
	}
	return 0
}

func (x *BusinessPetScheduleTimeDef) GetExtraJson() map[string]string {
	if x != nil {
		return x.ExtraJson
	}
	return nil
}

// selected date
type AppointmentPetMedicationScheduleDef_SelectedDateDef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// feeding medication schedule date type
	// default for PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY
	DateType FeedingMedicationScheduleDateType `protobuf:"varint,1,opt,name=date_type,json=dateType,proto3,enum=backend.proto.fulfillment.v1.FeedingMedicationScheduleDateType" json:"date_type,omitempty"`
	// specific date
	SpecificDates []string `protobuf:"bytes,2,rep,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AppointmentPetMedicationScheduleDef_SelectedDateDef) Reset() {
	*x = AppointmentPetMedicationScheduleDef_SelectedDateDef{}
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AppointmentPetMedicationScheduleDef_SelectedDateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentPetMedicationScheduleDef_SelectedDateDef) ProtoMessage() {}

func (x *AppointmentPetMedicationScheduleDef_SelectedDateDef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentPetMedicationScheduleDef_SelectedDateDef.ProtoReflect.Descriptor instead.
func (*AppointmentPetMedicationScheduleDef_SelectedDateDef) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP(), []int{32, 0}
}

func (x *AppointmentPetMedicationScheduleDef_SelectedDateDef) GetDateType() FeedingMedicationScheduleDateType {
	if x != nil {
		return x.DateType
	}
	return FeedingMedicationScheduleDateType_FEEDING_MEDICATION_SCHEDULE_DATE_TYPE_UNSPECIFIED
}

func (x *AppointmentPetMedicationScheduleDef_SelectedDateDef) GetSpecificDates() []string {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

var File_backend_proto_fulfillment_v1_appointment_proto protoreflect.FileDescriptor

const file_backend_proto_fulfillment_v1_appointment_proto_rawDesc = "" +
	"\n" +
	".backend/proto/fulfillment/v1/appointment.proto\x12\x1cbackend.proto.fulfillment.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bbuf/validate/validate.proto\x1a)backend/proto/fulfillment/v1/common.proto\x1a\x1bgoogle/type/timeofday.proto\x1a\x1egoogle/protobuf/duration.proto\"_\n" +
	"\x11AppointmentFilter\x12J\n" +
	"\bstatuses\x18\x01 \x03(\x0e2..backend.proto.fulfillment.v1.AppointmentStateR\bstatuses\"\xa8\x03\n" +
	"\x14CreateAppointmentDef\x12(\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"businessId\x12&\n" +
	"\n" +
	"company_id\x18\x03 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tcompanyId\x12\x1f\n" +
	"\vcustomer_id\x18\x04 \x01(\x03R\n" +
	"customerId\x12F\n" +
	"\x06status\x18\x05 \x01(\x0e2..backend.proto.fulfillment.v1.AppointmentStateR\x06status\x12\x1d\n" +
	"\n" +
	"color_code\x18\a \x01(\tR\tcolorCode\x129\n" +
	"\n" +
	"start_time\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12D\n" +
	"\x04pets\x18\n" +
	" \x03(\v20.backend.proto.fulfillment.v1.CreatePetDetailDefR\x04pets\"\x7f\n" +
	"\x12CreatePetDetailDef\x12\x15\n" +
	"\x06pet_id\x18\x01 \x01(\x03R\x05petId\x12R\n" +
	"\bservices\x18\x02 \x03(\v26.backend.proto.fulfillment.v1.CreateServiceInstanceDefR\bservices\"\xeb\a\n" +
	"\x18CreateServiceInstanceDef\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12K\n" +
	"\vtime_config\x18\x02 \x01(\v2(.backend.proto.fulfillment.v1.TimeConfigH\x00R\n" +
	"timeConfig\x12d\n" +
	"\x14date_schedule_config\x18\x03 \x01(\v20.backend.proto.fulfillment.v1.DateScheduleConfigH\x00R\x12dateScheduleConfig\x12\x1e\n" +
	"\bstaff_id\x18\x04 \x01(\x03H\x01R\astaffId\x88\x01\x01\x12H\n" +
	"\twork_mode\x18\x05 \x01(\x0e2&.backend.proto.fulfillment.v1.WorkModeH\x02R\bworkMode\x88\x01\x01\x12j\n" +
	"\x15sub_service_instances\x18\x06 \x03(\v26.backend.proto.fulfillment.v1.CreateServiceInstanceDefR\x13subServiceInstances\x12E\n" +
	"\acharges\x18\a \x03(\v2+.backend.proto.fulfillment.v1.ServiceChargeR\acharges\x12\x19\n" +
	"\x05price\x18\b \x01(\x03H\x03R\x05price\x88\x01\x01\x12:\n" +
	"\bduration\x18\t \x01(\v2\x19.google.protobuf.DurationH\x04R\bduration\x88\x01\x01\x12c\n" +
	"\vmedications\x18\n" +
	" \x03(\v2A.backend.proto.fulfillment.v1.AppointmentPetMedicationScheduleDefR\vmedications\x12Z\n" +
	"\bfeedings\x18\v \x03(\v2>.backend.proto.fulfillment.v1.AppointmentPetFeedingScheduleDefR\bfeedings\x12W\n" +
	"\x0eservice_detail\x18\f \x01(\v2+.backend.proto.fulfillment.v1.ServiceDetailH\x05R\rserviceDetail\x88\x01\x01\x12\"\n" +
	"\n" +
	"lodging_id\x18\r \x01(\x03H\x06R\tlodgingId\x88\x01\x01B\x06\n" +
	"\x04timeB\v\n" +
	"\t_staff_idB\f\n" +
	"\n" +
	"_work_modeB\b\n" +
	"\x06_priceB\v\n" +
	"\t_durationB\x11\n" +
	"\x0f_service_detailB\r\n" +
	"\v_lodging_id\"\xa4\x01\n" +
	"\n" +
	"TimeConfig\x12>\n" +
	"\n" +
	"start_time\x18\x01 \x01(\v2\x1a.google.protobuf.TimestampH\x00R\tstartTime\x88\x01\x01\x12:\n" +
	"\bend_time\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampH\x01R\aendTime\x88\x01\x01B\r\n" +
	"\v_start_timeB\v\n" +
	"\t_end_time\"\xed\x01\n" +
	"\x12DateScheduleConfig\x12C\n" +
	"\tdate_type\x18\x01 \x01(\x0e2&.backend.proto.fulfillment.v1.DateTypeR\bdateType\x12>\n" +
	"\fregular_time\x18\x02 \x01(\v2\x16.google.type.TimeOfDayH\x00R\vregularTime\x88\x01\x01\x12A\n" +
	"\x0especific_dates\x18\x04 \x03(\v2\x1a.google.protobuf.TimestampR\rspecificDatesB\x0f\n" +
	"\r_regular_time\"\x8d\x05\n" +
	"\vAppointment\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12(\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"businessId\x12&\n" +
	"\n" +
	"company_id\x18\x03 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tcompanyId\x12\x1f\n" +
	"\vcustomer_id\x18\x04 \x01(\x03R\n" +
	"customerId\x12F\n" +
	"\x06status\x18\x05 \x01(\x0e2..backend.proto.fulfillment.v1.AppointmentStateR\x06status\x12*\n" +
	"\x11service_item_type\x18\x06 \x01(\x05R\x0fserviceItemType\x12\x1d\n" +
	"\n" +
	"color_code\x18\a \x01(\tR\tcolorCode\x129\n" +
	"\n" +
	"start_time\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12;\n" +
	"\x04pets\x18\n" +
	" \x03(\v2'.backend.proto.fulfillment.v1.PetDetailR\x04pets\x12G\n" +
	"\x06source\x18\v \x01(\x0e2/.backend.proto.fulfillment.v1.AppointmentSourceR\x06source\x126\n" +
	"\x18all_pets_start_same_time\x18\f \x01(\bR\x14allPetsStartSameTime\x128\n" +
	"\x05notes\x18\r \x03(\v2\".backend.proto.fulfillment.v1.NoteR\x05notes\"\xdb\x01\n" +
	"\x10PreAuthEnableDef\x12\x1f\n" +
	"\x06enable\x18\x01 \x01(\bB\a\xbaH\x04j\x02\b\x01R\x06enable\x12B\n" +
	"\x11payment_method_id\x18\x02 \x01(\tB\x11\xbaH\x0er\f\x18\xff\x012\a^pm_.*$H\x00R\x0fpaymentMethodId\x88\x01\x01\x127\n" +
	"\x10card_brand_last4\x18\x03 \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01H\x01R\x0ecardBrandLast4\x88\x01\x01B\x14\n" +
	"\x12_payment_method_idB\x13\n" +
	"\x11_card_brand_last4\"\xb6\x01\n" +
	"\fPetBelonging\x12\x1e\n" +
	"\x06pet_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x05petId\x12\x1e\n" +
	"\x04name\x18\x02 \x01(\tB\n" +
	"\xbaH\ar\x05\x10\x01\x18\x80\bR\x04name\x12 \n" +
	"\x04area\x18\x03 \x01(\tB\a\xbaH\x04r\x02\x18dH\x00R\x04area\x88\x01\x01\x12-\n" +
	"\tphoto_uri\x18\x04 \x01(\tB\v\xbaH\br\x06\x18\x80\b\x88\x01\x01H\x01R\bphotoUri\x88\x01\x01B\a\n" +
	"\x05_areaB\f\n" +
	"\n" +
	"_photo_uri\"q\n" +
	"\tPetDetail\x12\x15\n" +
	"\x06pet_id\x18\x01 \x01(\x03R\x05petId\x12M\n" +
	"\bservices\x18\x02 \x03(\v21.backend.proto.fulfillment.v1.ServiceInstanceImplR\bservices\"\x9b\x06\n" +
	"\x13ServiceInstanceImpl\x12.\n" +
	"\x13service_instance_id\x18\x01 \x01(\x03R\x11serviceInstanceId\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\x03R\x02id\x129\n" +
	"\n" +
	"start_time\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12\x1e\n" +
	"\bstaff_id\x18\x05 \x01(\x03H\x00R\astaffId\x88\x01\x01\x12H\n" +
	"\twork_mode\x18\x06 \x01(\x0e2&.backend.proto.fulfillment.v1.WorkModeH\x01R\bworkMode\x88\x01\x01\x12e\n" +
	"\x15sub_service_instances\x18\a \x03(\v21.backend.proto.fulfillment.v1.ServiceInstanceImplR\x13subServiceInstances\x12E\n" +
	"\acharges\x18\b \x03(\v2+.backend.proto.fulfillment.v1.ServiceChargeR\acharges\x12`\n" +
	"\x13feeding_medications\x18\t \x03(\v2/.backend.proto.fulfillment.v1.FeedingMedicationR\x12feedingMedications\x12W\n" +
	"\x0eservice_detail\x18\n" +
	" \x01(\v2+.backend.proto.fulfillment.v1.ServiceDetailH\x02R\rserviceDetail\x88\x01\x01\x12\x19\n" +
	"\x05price\x18\v \x01(\x03H\x03R\x05price\x88\x01\x01\x12\x1f\n" +
	"\bduration\x18\f \x01(\x03H\x04R\bduration\x88\x01\x01B\v\n" +
	"\t_staff_idB\f\n" +
	"\n" +
	"_work_modeB\x11\n" +
	"\x0f_service_detailB\b\n" +
	"\x06_priceB\v\n" +
	"\t_duration\"_\n" +
	"\rServiceDetail\x12N\n" +
	"\rstaff_details\x18\x01 \x03(\v2).backend.proto.fulfillment.v1.StaffDetailR\fstaffDetails\"\x92\x03\n" +
	"\vStaffDetail\x12\"\n" +
	"\bstaff_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\astaffId\x124\n" +
	"\x0eoperation_name\x18\x02 \x01(\tB\b\xbaH\x05r\x03\x18\x96\x01H\x00R\roperationName\x88\x01\x01\x12:\n" +
	"\n" +
	"start_time\x18\x03 \x01(\v2\x16.google.type.TimeOfDayH\x01R\tstartTime\x88\x01\x01\x12:\n" +
	"\bduration\x18\x04 \x01(\v2\x19.google.protobuf.DurationH\x02R\bduration\x88\x01\x01\x12=\n" +
	"\vprice_ratio\x18\x05 \x01(\x01B\x17\xbaH\x14\x12\x12\x19\x00\x00\x00\x00\x00\x00\xf0?)\x00\x00\x00\x00\x00\x00\x00\x00H\x03R\n" +
	"priceRatio\x88\x01\x01\x12)\n" +
	"\x05price\x18\x06 \x01(\x01B\x0e\xbaH\v\x12\t)\x00\x00\x00\x00\x00\x00\x00\x00H\x04R\x05price\x88\x01\x01B\x11\n" +
	"\x0f_operation_nameB\r\n" +
	"\v_start_timeB\v\n" +
	"\t_durationB\x0e\n" +
	"\f_price_ratioB\b\n" +
	"\x06_price\"~\n" +
	"\rServiceCharge\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x01R\x06amount\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x1f\n" +
	"\vcharge_type\x18\x04 \x01(\x05R\n" +
	"chargeType\"g\n" +
	"\x04Note\x12E\n" +
	"\x04type\x18\x01 \x01(\x0e21.backend.proto.fulfillment.v1.AppointmentNoteTypeR\x04type\x12\x18\n" +
	"\acontent\x18\x02 \x01(\tR\acontent\"\xb3\x01\n" +
	"\x11FeedingMedication\x12!\n" +
	"\ffeeding_rule\x18\x01 \x01(\tR\vfeedingRule\x12'\n" +
	"\x0fmedication_rule\x18\x02 \x01(\tR\x0emedicationRule\x12R\n" +
	"\vmedications\x18\x03 \x03(\v20.backend.proto.fulfillment.v1.MedicationScheduleR\vmedications\"\xb5\x01\n" +
	"\x12MedicationSchedule\x12\x1e\n" +
	"\n" +
	"medication\x18\x01 \x01(\tR\n" +
	"medication\x12\x16\n" +
	"\x06dosage\x18\x02 \x01(\tR\x06dosage\x12\x1c\n" +
	"\tfrequency\x18\x03 \x01(\tR\tfrequency\x123\n" +
	"\x15administration_method\x18\x04 \x01(\tR\x14administrationMethod\x12\x14\n" +
	"\x05notes\x18\x05 \x01(\tR\x05notes\"\xc0\x02\n" +
	"\rServiceOption\x12;\n" +
	"\x1aservice_option_template_id\x18\x01 \x01(\x03R\x17serviceOptionTemplateId\x129\n" +
	"\n" +
	"start_time\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12\x1a\n" +
	"\bquantity\x18\x05 \x01(\x05R\bquantity\x12\x14\n" +
	"\x05price\x18\x06 \x01(\x01R\x05price\x12\x10\n" +
	"\x03tax\x18\a \x01(\x01R\x03tax\x12(\n" +
	"\x10quantity_per_day\x18\b \x01(\x03R\x0equantityPerDay\"\x89\x01\n" +
	"\bMetadata\x12D\n" +
	"\x04tags\x18\x01 \x03(\v20.backend.proto.fulfillment.v1.Metadata.TagsEntryR\x04tags\x1a7\n" +
	"\tTagsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xa0\x06\n" +
	"\x1aAppointmentOperationResult\x12%\n" +
	"\x0eoperation_type\x18\x01 \x01(\tR\roperationType\x12\x18\n" +
	"\asuccess\x18\x02 \x01(\bR\asuccess\x12#\n" +
	"\rerror_message\x18\x03 \x01(\tR\ferrorMessage\x12E\n" +
	"\x0eold_start_time\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampH\x00R\foldStartTime\x88\x01\x01\x12A\n" +
	"\fold_end_time\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampH\x01R\n" +
	"oldEndTime\x88\x01\x01\x12)\n" +
	"\x0eold_color_code\x18\x06 \x01(\tH\x02R\foldColorCode\x88\x01\x01\x12R\n" +
	"\n" +
	"old_status\x18\a \x01(\x0e2..backend.proto.fulfillment.v1.AppointmentStateH\x03R\toldStatus\x88\x01\x01\x12E\n" +
	"\x0enew_start_time\x18\b \x01(\v2\x1a.google.protobuf.TimestampH\x04R\fnewStartTime\x88\x01\x01\x12A\n" +
	"\fnew_end_time\x18\t \x01(\v2\x1a.google.protobuf.TimestampH\x05R\n" +
	"newEndTime\x88\x01\x01\x12)\n" +
	"\x0enew_color_code\x18\n" +
	" \x01(\tH\x06R\fnewColorCode\x88\x01\x01\x12R\n" +
	"\n" +
	"new_status\x18\v \x01(\x0e2..backend.proto.fulfillment.v1.AppointmentStateH\aR\tnewStatus\x88\x01\x01B\x11\n" +
	"\x0f_old_start_timeB\x0f\n" +
	"\r_old_end_timeB\x11\n" +
	"\x0f_old_color_codeB\r\n" +
	"\v_old_statusB\x11\n" +
	"\x0f_new_start_timeB\x0f\n" +
	"\r_new_end_timeB\x11\n" +
	"\x0f_new_color_codeB\r\n" +
	"\v_new_status\"\xd4\x03\n" +
	"\x12PetOperationResult\x12\x15\n" +
	"\x06pet_id\x18\x01 \x01(\x03R\x05petId\x12R\n" +
	"\x0eoperation_mode\x18\x02 \x01(\x0e2+.backend.proto.fulfillment.v1.OperationModeR\roperationMode\x12\x18\n" +
	"\asuccess\x18\x03 \x01(\bR\asuccess\x12#\n" +
	"\rerror_message\x18\x04 \x01(\tR\ferrorMessage\x12T\n" +
	"\rdelete_result\x18\x05 \x01(\v2-.backend.proto.fulfillment.v1.PetDeleteResultH\x00R\fdeleteResult\x12T\n" +
	"\rcreate_result\x18\x06 \x01(\v2-.backend.proto.fulfillment.v1.PetCreateResultH\x00R\fcreateResult\x12T\n" +
	"\rupdate_result\x18\a \x01(\v2-.backend.proto.fulfillment.v1.PetUpdateResultH\x00R\fupdateResultB\x12\n" +
	"\x10operation_detail\"_\n" +
	"\x0fPetDeleteResult\x12\x18\n" +
	"\adeleted\x18\x01 \x01(\bR\adeleted\x122\n" +
	"\x15deleted_service_count\x18\x02 \x01(\x05R\x13deletedServiceCount\"c\n" +
	"\x0fPetCreateResult\x12\x1c\n" +
	"\n" +
	"new_pet_id\x18\x01 \x01(\x03R\bnewPetId\x122\n" +
	"\x15created_service_count\x18\x02 \x01(\x05R\x13createdServiceCount\"\x8f\x01\n" +
	"\x0fPetUpdateResult\x12H\n" +
	"\vupdated_pet\x18\x01 \x01(\v2'.backend.proto.fulfillment.v1.PetDetailR\n" +
	"updatedPet\x122\n" +
	"\x15updated_service_count\x18\x02 \x01(\x05R\x13updatedServiceCount\"\x94\x04\n" +
	"\x16ServiceOperationResult\x12.\n" +
	"\x13service_instance_id\x18\x01 \x01(\x03R\x11serviceInstanceId\x12\x15\n" +
	"\x06pet_id\x18\x02 \x01(\x03R\x05petId\x12R\n" +
	"\x0eoperation_mode\x18\x03 \x01(\x0e2+.backend.proto.fulfillment.v1.OperationModeR\roperationMode\x12\x18\n" +
	"\asuccess\x18\x04 \x01(\bR\asuccess\x12#\n" +
	"\rerror_message\x18\x05 \x01(\tR\ferrorMessage\x12X\n" +
	"\rdelete_result\x18\x06 \x01(\v21.backend.proto.fulfillment.v1.ServiceDeleteResultH\x00R\fdeleteResult\x12X\n" +
	"\rcreate_result\x18\a \x01(\v21.backend.proto.fulfillment.v1.ServiceCreateResultH\x00R\fcreateResult\x12X\n" +
	"\rupdate_result\x18\b \x01(\v21.backend.proto.fulfillment.v1.ServiceUpdateResultH\x00R\fupdateResultB\x12\n" +
	"\x10operation_detail\"a\n" +
	"\x13ServiceDeleteResult\x12\x18\n" +
	"\adeleted\x18\x01 \x01(\bR\adeleted\x120\n" +
	"\x14deleted_option_count\x18\x02 \x01(\x05R\x12deletedOptionCount\"~\n" +
	"\x13ServiceCreateResult\x125\n" +
	"\x17new_service_instance_id\x18\x01 \x01(\x03R\x14newServiceInstanceId\x120\n" +
	"\x14created_option_count\x18\x02 \x01(\x05R\x12createdOptionCount\"\xa3\x01\n" +
	"\x13ServiceUpdateResult\x12Z\n" +
	"\x0fupdated_service\x18\x01 \x01(\v21.backend.proto.fulfillment.v1.ServiceInstanceImplR\x0eupdatedService\x120\n" +
	"\x14updated_option_count\x18\x02 \x01(\x05R\x12updatedOptionCount\"\xa5\x04\n" +
	"\x15OptionOperationResult\x12*\n" +
	"\x11service_option_id\x18\x01 \x01(\x03R\x0fserviceOptionId\x12.\n" +
	"\x13service_instance_id\x18\x02 \x01(\x03R\x11serviceInstanceId\x12R\n" +
	"\x0eoperation_mode\x18\x03 \x01(\x0e2+.backend.proto.fulfillment.v1.OperationModeR\roperationMode\x12\x18\n" +
	"\asuccess\x18\x04 \x01(\bR\asuccess\x12#\n" +
	"\rerror_message\x18\x05 \x01(\tR\ferrorMessage\x12W\n" +
	"\rdelete_result\x18\x06 \x01(\v20.backend.proto.fulfillment.v1.OptionDeleteResultH\x00R\fdeleteResult\x12W\n" +
	"\rcreate_result\x18\a \x01(\v20.backend.proto.fulfillment.v1.OptionCreateResultH\x00R\fcreateResult\x12W\n" +
	"\rupdate_result\x18\b \x01(\v20.backend.proto.fulfillment.v1.OptionUpdateResultH\x00R\fupdateResultB\x12\n" +
	"\x10operation_detail\".\n" +
	"\x12OptionDeleteResult\x12\x18\n" +
	"\adeleted\x18\x01 \x01(\bR\adeleted\"G\n" +
	"\x12OptionCreateResult\x121\n" +
	"\x15new_service_option_id\x18\x01 \x01(\x03R\x12newServiceOptionId\"h\n" +
	"\x12OptionUpdateResult\x12R\n" +
	"\x0eupdated_option\x18\x01 \x01(\v2+.backend.proto.fulfillment.v1.ServiceOptionR\rupdatedOption\"\xd6\x05\n" +
	"#AppointmentPetMedicationScheduleDef\x125\n" +
	"\x11medication_amount\x18\x01 \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01R\x10medicationAmount\x121\n" +
	"\x0fmedication_unit\x18\x02 \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01R\x0emedicationUnit\x121\n" +
	"\x0fmedication_name\x18\x03 \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01R\x0emedicationName\x126\n" +
	"\x0fmedication_note\x18\x04 \x01(\tB\b\xbaH\x05r\x03\x18\x80PH\x00R\x0emedicationNote\x88\x01\x01\x12o\n" +
	"\x10medication_times\x18\x06 \x03(\v28.backend.proto.fulfillment.v1.BusinessPetScheduleTimeDefB\n" +
	"\xbaH\a\x92\x01\x04\b\x01\x10cR\x0fmedicationTimes\x12{\n" +
	"\rselected_date\x18\a \x01(\v2Q.backend.proto.fulfillment.v1.AppointmentPetMedicationScheduleDef.SelectedDateDefH\x01R\fselectedDate\x88\x01\x01\x1a\xc5\x01\n" +
	"\x0fSelectedDateDef\x12h\n" +
	"\tdate_type\x18\x01 \x01(\x0e2?.backend.proto.fulfillment.v1.FeedingMedicationScheduleDateTypeB\n" +
	"\xbaH\a\x82\x01\x04\x10\x01 \x00R\bdateType\x12H\n" +
	"\x0especific_dates\x18\x02 \x03(\tB!\xbaH\x1e\x92\x01\x1b\x10d\"\x17r\x152\x13^\\d{4}-\\d{2}-\\d{2}$R\rspecificDatesB\x12\n" +
	"\x10_medication_noteB\x10\n" +
	"\x0e_selected_date\"\xe4\x03\n" +
	" AppointmentPetFeedingScheduleDef\x12/\n" +
	"\x0efeeding_amount\x18\x01 \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01R\rfeedingAmount\x12+\n" +
	"\ffeeding_unit\x18\x02 \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01R\vfeedingUnit\x12+\n" +
	"\ffeeding_type\x18\x03 \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01R\vfeedingType\x12/\n" +
	"\x0efeeding_source\x18\x04 \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01R\rfeedingSource\x12>\n" +
	"\x13feeding_instruction\x18\x05 \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01H\x00R\x12feedingInstruction\x88\x01\x01\x12i\n" +
	"\rfeeding_times\x18\x06 \x03(\v28.backend.proto.fulfillment.v1.BusinessPetScheduleTimeDefB\n" +
	"\xbaH\a\x92\x01\x04\b\x01\x10cR\ffeedingTimes\x120\n" +
	"\ffeeding_note\x18\a \x01(\tB\b\xbaH\x05r\x03\x18\x80PH\x01R\vfeedingNote\x88\x01\x01B\x16\n" +
	"\x14_feeding_instructionB\x0f\n" +
	"\r_feeding_note\"\x80\x02\n" +
	"\x1aBusinessPetScheduleTimeDef\x12/\n" +
	"\rschedule_time\x18\x01 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xa0\v(\x00R\fscheduleTime\x12s\n" +
	"\n" +
	"extra_json\x18\x02 \x03(\v2G.backend.proto.fulfillment.v1.BusinessPetScheduleTimeDef.ExtraJsonEntryB\v\xbaH\b\x9a\x01\x05\b\x00\x10\xe8\aR\textraJson\x1a<\n" +
	"\x0eExtraJsonEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01*q\n" +
	"\n" +
	"RepeatType\x12\x1b\n" +
	"\x17REPEAT_TYPE_UNSPECIFIED\x10\x00\x12\x15\n" +
	"\x11REPEAT_TYPE_DAILY\x10\x01\x12\x16\n" +
	"\x12REPEAT_TYPE_WEEKLY\x10\x02\x12\x17\n" +
	"\x13REPEAT_TYPE_MONTHLY\x10\x03*\xf8\x01\n" +
	"\x10AppointmentState\x12!\n" +
	"\x1dAPPOINTMENT_STATE_UNSPECIFIED\x10\x00\x12!\n" +
	"\x1dAPPOINTMENT_STATE_UNCONFIRMED\x10\x01\x12\x1f\n" +
	"\x1bAPPOINTMENT_STATE_CONFIRMED\x10\x02\x12\x1e\n" +
	"\x1aAPPOINTMENT_STATE_FINISHED\x10\x03\x12\x1e\n" +
	"\x1aAPPOINTMENT_STATE_CANCELED\x10\x04\x12\x1b\n" +
	"\x17APPOINTMENT_STATE_READY\x10\x05\x12 \n" +
	"\x1cAPPOINTMENT_STATE_CHECKED_IN\x10\x06*\x80\x01\n" +
	"\rOperationMode\x12\x1e\n" +
	"\x1aOPERATION_MODE_UNSPECIFIED\x10\x00\x12\x19\n" +
	"\x15OPERATION_MODE_CREATE\x10\x01\x12\x19\n" +
	"\x15OPERATION_MODE_UPDATE\x10\x02\x12\x19\n" +
	"\x15OPERATION_MODE_DELETE\x10\x03*v\n" +
	"\x13AppointmentNoteType\x12%\n" +
	"!APPOINTMENT_NOTE_TYPE_UNSPECIFIED\x10\x00\x12\x0f\n" +
	"\vALERT_NOTES\x10\x01\x12\v\n" +
	"\aCOMMENT\x10\x02\x12\n" +
	"\n" +
	"\x06CANCEL\x10\x03\x12\x0e\n" +
	"\n" +
	"ADDITIONAL\x10\x04*\x9c\x01\n" +
	"\x11AppointmentSource\x12\"\n" +
	"\x1eAPPOINTMENT_SOURCE_UNSPECIFIED\x10\x00\x12\t\n" +
	"\x03WEB\x10\x82\xac\x01\x12\b\n" +
	"\x02OB\x10\x98\xad\x01\x12\r\n" +
	"\aANDROID\x10\xc0\x86\x01\x12\t\n" +
	"\x03IOS\x10\x8a\x8b\x01\x12\r\n" +
	"\aAUTO_DM\x10\x82\xb7\x01\x12\x15\n" +
	"\x0fGOOGLE_CALENDAR\x10\xf2\x9a\x01\x12\x0e\n" +
	"\bOPEN_API\x10\xa5\xb6\x01*A\n" +
	"\bWorkMode\x12\x19\n" +
	"\x15WORK_MODE_UNSPECIFIED\x10\x00\x12\f\n" +
	"\bPARALLEL\x10\x01\x12\f\n" +
	"\bSEQUENCE\x10\x02*\xb4\x01\n" +
	"!FeedingMedicationScheduleDateType\x125\n" +
	"1FEEDING_MEDICATION_SCHEDULE_DATE_TYPE_UNSPECIFIED\x10\x00\x12!\n" +
	"\x1dEVERYDAY_EXCEPT_CHECKOUT_DATE\x10\x01\x12\"\n" +
	"\x1eEVERYDAY_INCLUDE_CHECKOUT_DATE\x10\x02\x12\x11\n" +
	"\rSPECIFIC_DATE\x10\x03*\xa2\x01\n" +
	"\x18SpecificDateScheduleType\x12+\n" +
	"'SPECIFIC_DATE_SCHEDULE_TYPE_UNSPECIFIED\x10\x00\x12,\n" +
	"(SPECIFIC_DATE_SCHEDULE_TYPE_REGULAR_TIME\x10\x01\x12+\n" +
	"'SPECIFIC_DATE_SCHEDULE_TYPE_CUSTOM_TIME\x10\x02Bt\n" +
	"&com.moego.backend.proto.fulfillment.v1P\x01ZHgithub.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpbb\x06proto3"

var (
	file_backend_proto_fulfillment_v1_appointment_proto_rawDescOnce sync.Once
	file_backend_proto_fulfillment_v1_appointment_proto_rawDescData []byte
)

func file_backend_proto_fulfillment_v1_appointment_proto_rawDescGZIP() []byte {
	file_backend_proto_fulfillment_v1_appointment_proto_rawDescOnce.Do(func() {
		file_backend_proto_fulfillment_v1_appointment_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_appointment_proto_rawDesc), len(file_backend_proto_fulfillment_v1_appointment_proto_rawDesc)))
	})
	return file_backend_proto_fulfillment_v1_appointment_proto_rawDescData
}

var file_backend_proto_fulfillment_v1_appointment_proto_enumTypes = make([]protoimpl.EnumInfo, 8)
var file_backend_proto_fulfillment_v1_appointment_proto_msgTypes = make([]protoimpl.MessageInfo, 38)
var file_backend_proto_fulfillment_v1_appointment_proto_goTypes = []any{
	(RepeatType)(0),                             // 0: backend.proto.fulfillment.v1.RepeatType
	(AppointmentState)(0),                       // 1: backend.proto.fulfillment.v1.AppointmentState
	(OperationMode)(0),                          // 2: backend.proto.fulfillment.v1.OperationMode
	(AppointmentNoteType)(0),                    // 3: backend.proto.fulfillment.v1.AppointmentNoteType
	(AppointmentSource)(0),                      // 4: backend.proto.fulfillment.v1.AppointmentSource
	(WorkMode)(0),                               // 5: backend.proto.fulfillment.v1.WorkMode
	(FeedingMedicationScheduleDateType)(0),      // 6: backend.proto.fulfillment.v1.FeedingMedicationScheduleDateType
	(SpecificDateScheduleType)(0),               // 7: backend.proto.fulfillment.v1.SpecificDateScheduleType
	(*AppointmentFilter)(nil),                   // 8: backend.proto.fulfillment.v1.AppointmentFilter
	(*CreateAppointmentDef)(nil),                // 9: backend.proto.fulfillment.v1.CreateAppointmentDef
	(*CreatePetDetailDef)(nil),                  // 10: backend.proto.fulfillment.v1.CreatePetDetailDef
	(*CreateServiceInstanceDef)(nil),            // 11: backend.proto.fulfillment.v1.CreateServiceInstanceDef
	(*TimeConfig)(nil),                          // 12: backend.proto.fulfillment.v1.TimeConfig
	(*DateScheduleConfig)(nil),                  // 13: backend.proto.fulfillment.v1.DateScheduleConfig
	(*Appointment)(nil),                         // 14: backend.proto.fulfillment.v1.Appointment
	(*PreAuthEnableDef)(nil),                    // 15: backend.proto.fulfillment.v1.PreAuthEnableDef
	(*PetBelonging)(nil),                        // 16: backend.proto.fulfillment.v1.PetBelonging
	(*PetDetail)(nil),                           // 17: backend.proto.fulfillment.v1.PetDetail
	(*ServiceInstanceImpl)(nil),                 // 18: backend.proto.fulfillment.v1.ServiceInstanceImpl
	(*ServiceDetail)(nil),                       // 19: backend.proto.fulfillment.v1.ServiceDetail
	(*StaffDetail)(nil),                         // 20: backend.proto.fulfillment.v1.StaffDetail
	(*ServiceCharge)(nil),                       // 21: backend.proto.fulfillment.v1.ServiceCharge
	(*Note)(nil),                                // 22: backend.proto.fulfillment.v1.Note
	(*FeedingMedication)(nil),                   // 23: backend.proto.fulfillment.v1.FeedingMedication
	(*MedicationSchedule)(nil),                  // 24: backend.proto.fulfillment.v1.MedicationSchedule
	(*ServiceOption)(nil),                       // 25: backend.proto.fulfillment.v1.ServiceOption
	(*Metadata)(nil),                            // 26: backend.proto.fulfillment.v1.Metadata
	(*AppointmentOperationResult)(nil),          // 27: backend.proto.fulfillment.v1.AppointmentOperationResult
	(*PetOperationResult)(nil),                  // 28: backend.proto.fulfillment.v1.PetOperationResult
	(*PetDeleteResult)(nil),                     // 29: backend.proto.fulfillment.v1.PetDeleteResult
	(*PetCreateResult)(nil),                     // 30: backend.proto.fulfillment.v1.PetCreateResult
	(*PetUpdateResult)(nil),                     // 31: backend.proto.fulfillment.v1.PetUpdateResult
	(*ServiceOperationResult)(nil),              // 32: backend.proto.fulfillment.v1.ServiceOperationResult
	(*ServiceDeleteResult)(nil),                 // 33: backend.proto.fulfillment.v1.ServiceDeleteResult
	(*ServiceCreateResult)(nil),                 // 34: backend.proto.fulfillment.v1.ServiceCreateResult
	(*ServiceUpdateResult)(nil),                 // 35: backend.proto.fulfillment.v1.ServiceUpdateResult
	(*OptionOperationResult)(nil),               // 36: backend.proto.fulfillment.v1.OptionOperationResult
	(*OptionDeleteResult)(nil),                  // 37: backend.proto.fulfillment.v1.OptionDeleteResult
	(*OptionCreateResult)(nil),                  // 38: backend.proto.fulfillment.v1.OptionCreateResult
	(*OptionUpdateResult)(nil),                  // 39: backend.proto.fulfillment.v1.OptionUpdateResult
	(*AppointmentPetMedicationScheduleDef)(nil), // 40: backend.proto.fulfillment.v1.AppointmentPetMedicationScheduleDef
	(*AppointmentPetFeedingScheduleDef)(nil),    // 41: backend.proto.fulfillment.v1.AppointmentPetFeedingScheduleDef
	(*BusinessPetScheduleTimeDef)(nil),          // 42: backend.proto.fulfillment.v1.BusinessPetScheduleTimeDef
	nil,                                         // 43: backend.proto.fulfillment.v1.Metadata.TagsEntry
	(*AppointmentPetMedicationScheduleDef_SelectedDateDef)(nil), // 44: backend.proto.fulfillment.v1.AppointmentPetMedicationScheduleDef.SelectedDateDef
	nil,                           // 45: backend.proto.fulfillment.v1.BusinessPetScheduleTimeDef.ExtraJsonEntry
	(*timestamppb.Timestamp)(nil), // 46: google.protobuf.Timestamp
	(*durationpb.Duration)(nil),   // 47: google.protobuf.Duration
	(DateType)(0),                 // 48: backend.proto.fulfillment.v1.DateType
	(*timeofday.TimeOfDay)(nil),   // 49: google.type.TimeOfDay
}
var file_backend_proto_fulfillment_v1_appointment_proto_depIdxs = []int32{
	1,  // 0: backend.proto.fulfillment.v1.AppointmentFilter.statuses:type_name -> backend.proto.fulfillment.v1.AppointmentState
	1,  // 1: backend.proto.fulfillment.v1.CreateAppointmentDef.status:type_name -> backend.proto.fulfillment.v1.AppointmentState
	46, // 2: backend.proto.fulfillment.v1.CreateAppointmentDef.start_time:type_name -> google.protobuf.Timestamp
	46, // 3: backend.proto.fulfillment.v1.CreateAppointmentDef.end_time:type_name -> google.protobuf.Timestamp
	10, // 4: backend.proto.fulfillment.v1.CreateAppointmentDef.pets:type_name -> backend.proto.fulfillment.v1.CreatePetDetailDef
	11, // 5: backend.proto.fulfillment.v1.CreatePetDetailDef.services:type_name -> backend.proto.fulfillment.v1.CreateServiceInstanceDef
	12, // 6: backend.proto.fulfillment.v1.CreateServiceInstanceDef.time_config:type_name -> backend.proto.fulfillment.v1.TimeConfig
	13, // 7: backend.proto.fulfillment.v1.CreateServiceInstanceDef.date_schedule_config:type_name -> backend.proto.fulfillment.v1.DateScheduleConfig
	5,  // 8: backend.proto.fulfillment.v1.CreateServiceInstanceDef.work_mode:type_name -> backend.proto.fulfillment.v1.WorkMode
	11, // 9: backend.proto.fulfillment.v1.CreateServiceInstanceDef.sub_service_instances:type_name -> backend.proto.fulfillment.v1.CreateServiceInstanceDef
	21, // 10: backend.proto.fulfillment.v1.CreateServiceInstanceDef.charges:type_name -> backend.proto.fulfillment.v1.ServiceCharge
	47, // 11: backend.proto.fulfillment.v1.CreateServiceInstanceDef.duration:type_name -> google.protobuf.Duration
	40, // 12: backend.proto.fulfillment.v1.CreateServiceInstanceDef.medications:type_name -> backend.proto.fulfillment.v1.AppointmentPetMedicationScheduleDef
	41, // 13: backend.proto.fulfillment.v1.CreateServiceInstanceDef.feedings:type_name -> backend.proto.fulfillment.v1.AppointmentPetFeedingScheduleDef
	19, // 14: backend.proto.fulfillment.v1.CreateServiceInstanceDef.service_detail:type_name -> backend.proto.fulfillment.v1.ServiceDetail
	46, // 15: backend.proto.fulfillment.v1.TimeConfig.start_time:type_name -> google.protobuf.Timestamp
	46, // 16: backend.proto.fulfillment.v1.TimeConfig.end_time:type_name -> google.protobuf.Timestamp
	48, // 17: backend.proto.fulfillment.v1.DateScheduleConfig.date_type:type_name -> backend.proto.fulfillment.v1.DateType
	49, // 18: backend.proto.fulfillment.v1.DateScheduleConfig.regular_time:type_name -> google.type.TimeOfDay
	46, // 19: backend.proto.fulfillment.v1.DateScheduleConfig.specific_dates:type_name -> google.protobuf.Timestamp
	1,  // 20: backend.proto.fulfillment.v1.Appointment.status:type_name -> backend.proto.fulfillment.v1.AppointmentState
	46, // 21: backend.proto.fulfillment.v1.Appointment.start_time:type_name -> google.protobuf.Timestamp
	46, // 22: backend.proto.fulfillment.v1.Appointment.end_time:type_name -> google.protobuf.Timestamp
	17, // 23: backend.proto.fulfillment.v1.Appointment.pets:type_name -> backend.proto.fulfillment.v1.PetDetail
	4,  // 24: backend.proto.fulfillment.v1.Appointment.source:type_name -> backend.proto.fulfillment.v1.AppointmentSource
	22, // 25: backend.proto.fulfillment.v1.Appointment.notes:type_name -> backend.proto.fulfillment.v1.Note
	18, // 26: backend.proto.fulfillment.v1.PetDetail.services:type_name -> backend.proto.fulfillment.v1.ServiceInstanceImpl
	46, // 27: backend.proto.fulfillment.v1.ServiceInstanceImpl.start_time:type_name -> google.protobuf.Timestamp
	46, // 28: backend.proto.fulfillment.v1.ServiceInstanceImpl.end_time:type_name -> google.protobuf.Timestamp
	5,  // 29: backend.proto.fulfillment.v1.ServiceInstanceImpl.work_mode:type_name -> backend.proto.fulfillment.v1.WorkMode
	18, // 30: backend.proto.fulfillment.v1.ServiceInstanceImpl.sub_service_instances:type_name -> backend.proto.fulfillment.v1.ServiceInstanceImpl
	21, // 31: backend.proto.fulfillment.v1.ServiceInstanceImpl.charges:type_name -> backend.proto.fulfillment.v1.ServiceCharge
	23, // 32: backend.proto.fulfillment.v1.ServiceInstanceImpl.feeding_medications:type_name -> backend.proto.fulfillment.v1.FeedingMedication
	19, // 33: backend.proto.fulfillment.v1.ServiceInstanceImpl.service_detail:type_name -> backend.proto.fulfillment.v1.ServiceDetail
	20, // 34: backend.proto.fulfillment.v1.ServiceDetail.staff_details:type_name -> backend.proto.fulfillment.v1.StaffDetail
	49, // 35: backend.proto.fulfillment.v1.StaffDetail.start_time:type_name -> google.type.TimeOfDay
	47, // 36: backend.proto.fulfillment.v1.StaffDetail.duration:type_name -> google.protobuf.Duration
	3,  // 37: backend.proto.fulfillment.v1.Note.type:type_name -> backend.proto.fulfillment.v1.AppointmentNoteType
	24, // 38: backend.proto.fulfillment.v1.FeedingMedication.medications:type_name -> backend.proto.fulfillment.v1.MedicationSchedule
	46, // 39: backend.proto.fulfillment.v1.ServiceOption.start_time:type_name -> google.protobuf.Timestamp
	46, // 40: backend.proto.fulfillment.v1.ServiceOption.end_time:type_name -> google.protobuf.Timestamp
	43, // 41: backend.proto.fulfillment.v1.Metadata.tags:type_name -> backend.proto.fulfillment.v1.Metadata.TagsEntry
	46, // 42: backend.proto.fulfillment.v1.AppointmentOperationResult.old_start_time:type_name -> google.protobuf.Timestamp
	46, // 43: backend.proto.fulfillment.v1.AppointmentOperationResult.old_end_time:type_name -> google.protobuf.Timestamp
	1,  // 44: backend.proto.fulfillment.v1.AppointmentOperationResult.old_status:type_name -> backend.proto.fulfillment.v1.AppointmentState
	46, // 45: backend.proto.fulfillment.v1.AppointmentOperationResult.new_start_time:type_name -> google.protobuf.Timestamp
	46, // 46: backend.proto.fulfillment.v1.AppointmentOperationResult.new_end_time:type_name -> google.protobuf.Timestamp
	1,  // 47: backend.proto.fulfillment.v1.AppointmentOperationResult.new_status:type_name -> backend.proto.fulfillment.v1.AppointmentState
	2,  // 48: backend.proto.fulfillment.v1.PetOperationResult.operation_mode:type_name -> backend.proto.fulfillment.v1.OperationMode
	29, // 49: backend.proto.fulfillment.v1.PetOperationResult.delete_result:type_name -> backend.proto.fulfillment.v1.PetDeleteResult
	30, // 50: backend.proto.fulfillment.v1.PetOperationResult.create_result:type_name -> backend.proto.fulfillment.v1.PetCreateResult
	31, // 51: backend.proto.fulfillment.v1.PetOperationResult.update_result:type_name -> backend.proto.fulfillment.v1.PetUpdateResult
	17, // 52: backend.proto.fulfillment.v1.PetUpdateResult.updated_pet:type_name -> backend.proto.fulfillment.v1.PetDetail
	2,  // 53: backend.proto.fulfillment.v1.ServiceOperationResult.operation_mode:type_name -> backend.proto.fulfillment.v1.OperationMode
	33, // 54: backend.proto.fulfillment.v1.ServiceOperationResult.delete_result:type_name -> backend.proto.fulfillment.v1.ServiceDeleteResult
	34, // 55: backend.proto.fulfillment.v1.ServiceOperationResult.create_result:type_name -> backend.proto.fulfillment.v1.ServiceCreateResult
	35, // 56: backend.proto.fulfillment.v1.ServiceOperationResult.update_result:type_name -> backend.proto.fulfillment.v1.ServiceUpdateResult
	18, // 57: backend.proto.fulfillment.v1.ServiceUpdateResult.updated_service:type_name -> backend.proto.fulfillment.v1.ServiceInstanceImpl
	2,  // 58: backend.proto.fulfillment.v1.OptionOperationResult.operation_mode:type_name -> backend.proto.fulfillment.v1.OperationMode
	37, // 59: backend.proto.fulfillment.v1.OptionOperationResult.delete_result:type_name -> backend.proto.fulfillment.v1.OptionDeleteResult
	38, // 60: backend.proto.fulfillment.v1.OptionOperationResult.create_result:type_name -> backend.proto.fulfillment.v1.OptionCreateResult
	39, // 61: backend.proto.fulfillment.v1.OptionOperationResult.update_result:type_name -> backend.proto.fulfillment.v1.OptionUpdateResult
	25, // 62: backend.proto.fulfillment.v1.OptionUpdateResult.updated_option:type_name -> backend.proto.fulfillment.v1.ServiceOption
	42, // 63: backend.proto.fulfillment.v1.AppointmentPetMedicationScheduleDef.medication_times:type_name -> backend.proto.fulfillment.v1.BusinessPetScheduleTimeDef
	44, // 64: backend.proto.fulfillment.v1.AppointmentPetMedicationScheduleDef.selected_date:type_name -> backend.proto.fulfillment.v1.AppointmentPetMedicationScheduleDef.SelectedDateDef
	42, // 65: backend.proto.fulfillment.v1.AppointmentPetFeedingScheduleDef.feeding_times:type_name -> backend.proto.fulfillment.v1.BusinessPetScheduleTimeDef
	45, // 66: backend.proto.fulfillment.v1.BusinessPetScheduleTimeDef.extra_json:type_name -> backend.proto.fulfillment.v1.BusinessPetScheduleTimeDef.ExtraJsonEntry
	6,  // 67: backend.proto.fulfillment.v1.AppointmentPetMedicationScheduleDef.SelectedDateDef.date_type:type_name -> backend.proto.fulfillment.v1.FeedingMedicationScheduleDateType
	68, // [68:68] is the sub-list for method output_type
	68, // [68:68] is the sub-list for method input_type
	68, // [68:68] is the sub-list for extension type_name
	68, // [68:68] is the sub-list for extension extendee
	0,  // [0:68] is the sub-list for field type_name
}

func init() { file_backend_proto_fulfillment_v1_appointment_proto_init() }
func file_backend_proto_fulfillment_v1_appointment_proto_init() {
	if File_backend_proto_fulfillment_v1_appointment_proto != nil {
		return
	}
	file_backend_proto_fulfillment_v1_common_proto_init()
	file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[3].OneofWrappers = []any{
		(*CreateServiceInstanceDef_TimeConfig)(nil),
		(*CreateServiceInstanceDef_DateScheduleConfig)(nil),
	}
	file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[4].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[5].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[7].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[8].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[10].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[12].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[19].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[20].OneofWrappers = []any{
		(*PetOperationResult_DeleteResult)(nil),
		(*PetOperationResult_CreateResult)(nil),
		(*PetOperationResult_UpdateResult)(nil),
	}
	file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[24].OneofWrappers = []any{
		(*ServiceOperationResult_DeleteResult)(nil),
		(*ServiceOperationResult_CreateResult)(nil),
		(*ServiceOperationResult_UpdateResult)(nil),
	}
	file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[28].OneofWrappers = []any{
		(*OptionOperationResult_DeleteResult)(nil),
		(*OptionOperationResult_CreateResult)(nil),
		(*OptionOperationResult_UpdateResult)(nil),
	}
	file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[32].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_appointment_proto_msgTypes[33].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_appointment_proto_rawDesc), len(file_backend_proto_fulfillment_v1_appointment_proto_rawDesc)),
			NumEnums:      8,
			NumMessages:   38,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_fulfillment_v1_appointment_proto_goTypes,
		DependencyIndexes: file_backend_proto_fulfillment_v1_appointment_proto_depIdxs,
		EnumInfos:         file_backend_proto_fulfillment_v1_appointment_proto_enumTypes,
		MessageInfos:      file_backend_proto_fulfillment_v1_appointment_proto_msgTypes,
	}.Build()
	File_backend_proto_fulfillment_v1_appointment_proto = out.File
	file_backend_proto_fulfillment_v1_appointment_proto_goTypes = nil
	file_backend_proto_fulfillment_v1_appointment_proto_depIdxs = nil
}
