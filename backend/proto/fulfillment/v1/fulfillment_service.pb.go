// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/fulfillment/v1/fulfillment_service.proto

package fulfillmentpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GetPetsByTimeRangeRequest 根据时间区间获取宠物信息请求
type GetPetsByTimeRangeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司ID
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: company_id is clear and descriptive --)
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 商家ID
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: business_id is clear and descriptive --)
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 查询开始时间
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: start_time is clear and descriptive --)
	StartTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 查询结束时间
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: end_time is clear and descriptive --)
	EndTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 过滤条件（可选）
	Filter        *FulfillmentFilter `protobuf:"bytes,5,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPetsByTimeRangeRequest) Reset() {
	*x = GetPetsByTimeRangeRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPetsByTimeRangeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetsByTimeRangeRequest) ProtoMessage() {}

func (x *GetPetsByTimeRangeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetsByTimeRangeRequest.ProtoReflect.Descriptor instead.
func (*GetPetsByTimeRangeRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetPetsByTimeRangeRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetPetsByTimeRangeRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetPetsByTimeRangeRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *GetPetsByTimeRangeRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *GetPetsByTimeRangeRequest) GetFilter() *FulfillmentFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// GetPetsByTimeRangeResponse 根据时间区间获取宠物信息响应
type GetPetsByTimeRangeResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 宠物信息列表
	Pets []*PetInfo `protobuf:"bytes,1,rep,name=pets,proto3" json:"pets,omitempty"`
	// 总数量
	Total         int32 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPetsByTimeRangeResponse) Reset() {
	*x = GetPetsByTimeRangeResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPetsByTimeRangeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetsByTimeRangeResponse) ProtoMessage() {}

func (x *GetPetsByTimeRangeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetsByTimeRangeResponse.ProtoReflect.Descriptor instead.
func (*GetPetsByTimeRangeResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetPetsByTimeRangeResponse) GetPets() []*PetInfo {
	if x != nil {
		return x.Pets
	}
	return nil
}

func (x *GetPetsByTimeRangeResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// PetInfo 宠物信息
type PetInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 宠物ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 宠物头像路径
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: avatar_path is clear and descriptive --)
	AvatarPath string `protobuf:"bytes,2,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// 宠物名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 宠物类型
	Type          int32 `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PetInfo) Reset() {
	*x = PetInfo{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetInfo) ProtoMessage() {}

func (x *PetInfo) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetInfo.ProtoReflect.Descriptor instead.
func (*PetInfo) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{2}
}

func (x *PetInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PetInfo) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *PetInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PetInfo) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

// GetPetCountByTimeRangeRequest 根据时间区间统计不同careType的宠物数量请求
type GetPetCountByTimeRangeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司ID
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: company_id is clear and descriptive --)
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 商家ID
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: business_id is clear and descriptive --)
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 查询开始时间
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: start_time is clear and descriptive --)
	StartTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 查询结束时间
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: end_time is clear and descriptive --)
	EndTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 过滤条件（可选）
	Filter        *FulfillmentFilter `protobuf:"bytes,5,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPetCountByTimeRangeRequest) Reset() {
	*x = GetPetCountByTimeRangeRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPetCountByTimeRangeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetCountByTimeRangeRequest) ProtoMessage() {}

func (x *GetPetCountByTimeRangeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetCountByTimeRangeRequest.ProtoReflect.Descriptor instead.
func (*GetPetCountByTimeRangeRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetPetCountByTimeRangeRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetPetCountByTimeRangeRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetPetCountByTimeRangeRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *GetPetCountByTimeRangeRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *GetPetCountByTimeRangeRequest) GetFilter() *FulfillmentFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// GetPetCountByTimeRangeResponse 根据时间区间统计不同careType的宠物数量响应
type GetPetCountByTimeRangeResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 按日期分组的统计数据
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: daily_pet_counts is clear and descriptive --)
	DailyPetCounts []*DailyPetCount `protobuf:"bytes,1,rep,name=daily_pet_counts,json=dailyPetCounts,proto3" json:"daily_pet_counts,omitempty"`
	// 总宠物数量（所有日期的去重总数）
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: total_pet_count is clear and descriptive --)
	TotalPetCount int32 `protobuf:"varint,2,opt,name=total_pet_count,json=totalPetCount,proto3" json:"total_pet_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPetCountByTimeRangeResponse) Reset() {
	*x = GetPetCountByTimeRangeResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPetCountByTimeRangeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetCountByTimeRangeResponse) ProtoMessage() {}

func (x *GetPetCountByTimeRangeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetCountByTimeRangeResponse.ProtoReflect.Descriptor instead.
func (*GetPetCountByTimeRangeResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetPetCountByTimeRangeResponse) GetDailyPetCounts() []*DailyPetCount {
	if x != nil {
		return x.DailyPetCounts
	}
	return nil
}

func (x *GetPetCountByTimeRangeResponse) GetTotalPetCount() int32 {
	if x != nil {
		return x.TotalPetCount
	}
	return 0
}

// DailyPetCount 每日宠物数量统计
type DailyPetCount struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 日期（YYYY-MM-DD格式）
	// (-- api-linter: core::0142::time-field-type=disabled
	//
	//	aip.dev/not-precedent: string date format is appropriate for daily statistics --)
	Date string `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	// 按care type分组的宠物数量统计
	// key: care_type_id (int64)
	// value: 该care type的宠物数量 (int32)
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: We need to do this because reasons. --)
	PetCareTypeCount map[int64]int32 `protobuf:"bytes,2,rep,name=pet_care_type_count,json=petCareTypeCount,proto3" json:"pet_care_type_count,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	// 需要住宿的宠物数量
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: pet_count_with_lodging is clear and descriptive --)
	PetCountWithLodging int32 `protobuf:"varint,3,opt,name=pet_count_with_lodging,json=petCountWithLodging,proto3" json:"pet_count_with_lodging,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *DailyPetCount) Reset() {
	*x = DailyPetCount{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DailyPetCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DailyPetCount) ProtoMessage() {}

func (x *DailyPetCount) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DailyPetCount.ProtoReflect.Descriptor instead.
func (*DailyPetCount) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{5}
}

func (x *DailyPetCount) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *DailyPetCount) GetPetCareTypeCount() map[int64]int32 {
	if x != nil {
		return x.PetCareTypeCount
	}
	return nil
}

func (x *DailyPetCount) GetPetCountWithLodging() int32 {
	if x != nil {
		return x.PetCountWithLodging
	}
	return 0
}

// ListFulfillmentRequest
// (-- api-linter: core::0132::request-parent-required=disabled
//
//	aip.dev/not-precedent: 无效的parent定义 --)
//
// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: 不需要page-size --)
//
// (-- api-linter: core::0158::request-page_token-field=disabled
//
//	aip.dev/not-precedent: 不需要page_token --)
//
// (-- api-linter: core::0158::request-next_page_token-field=disabled
//
//	aip.dev/not-precedent: 不需要next_page_token --)
//
// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: 不需要page-token --)
type ListFulfillmentRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司ID
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: company_id is clear and descriptive --)
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 商家ID
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: business_id is clear and descriptive --)
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 查询开始时间
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: start_time is clear and descriptive --)
	StartTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 查询结束时间
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: end_time is clear and descriptive --)
	EndTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 过滤条件
	// (-- api-linter: core::0132::request-field-types=disabled
	//
	//	aip.dev/not-precedent: 打平成string不利用协议理解 --)
	Filter *FulfillmentFilter `protobuf:"bytes,5,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序方式
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: sort_type is clear and descriptive --)
	SortType SortType `protobuf:"varint,6,opt,name=sort_type,json=sortType,proto3,enum=backend.proto.fulfillment.v1.SortType" json:"sort_type,omitempty"`
	// 分页信息
	Pagination    *PaginationRef `protobuf:"bytes,7,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListFulfillmentRequest) Reset() {
	*x = ListFulfillmentRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListFulfillmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFulfillmentRequest) ProtoMessage() {}

func (x *ListFulfillmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFulfillmentRequest.ProtoReflect.Descriptor instead.
func (*ListFulfillmentRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{6}
}

func (x *ListFulfillmentRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListFulfillmentRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListFulfillmentRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *ListFulfillmentRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *ListFulfillmentRequest) GetFilter() *FulfillmentFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListFulfillmentRequest) GetSortType() SortType {
	if x != nil {
		return x.SortType
	}
	return SortType_SORT_TYPE_UNSPECIFIED
}

func (x *ListFulfillmentRequest) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// ListFulfillmentResponse
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//
//	aip.dev/not-precedent: 不需要next-page-token --)
type ListFulfillmentResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 履约列表
	// (-- api-linter: core::0132::response-unknown-fields=disabled
	//
	//	aip.dev/not-precedent: 必要的参数 --)
	Fulfillments []*Fulfillment `protobuf:"bytes,1,rep,name=fulfillments,proto3" json:"fulfillments,omitempty"`
	// 分页信息
	// (-- api-linter: core::0132::response-unknown-fields=disabled
	//
	//	aip.dev/not-precedent: 必要的参数 --)
	Pagination *PaginationRef `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// 是否最后一页
	// (-- api-linter: core::0132::response-unknown-fields=disabled
	//
	//	aip.dev/not-precedent: 必要的参数 --)
	//
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: is_end is clear and descriptive --)
	IsEnd bool `protobuf:"varint,3,opt,name=is_end,json=isEnd,proto3" json:"is_end,omitempty"`
	// 总条数
	// (-- api-linter: core::0132::response-unknown-fields=disabled
	//
	//	aip.dev/not-precedent: 必要的参数 --)
	Total         int32 `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListFulfillmentResponse) Reset() {
	*x = ListFulfillmentResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListFulfillmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFulfillmentResponse) ProtoMessage() {}

func (x *ListFulfillmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFulfillmentResponse.ProtoReflect.Descriptor instead.
func (*ListFulfillmentResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{7}
}

func (x *ListFulfillmentResponse) GetFulfillments() []*Fulfillment {
	if x != nil {
		return x.Fulfillments
	}
	return nil
}

func (x *ListFulfillmentResponse) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListFulfillmentResponse) GetIsEnd() bool {
	if x != nil {
		return x.IsEnd
	}
	return false
}

func (x *ListFulfillmentResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

var File_backend_proto_fulfillment_v1_fulfillment_service_proto protoreflect.FileDescriptor

const file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDesc = "" +
	"\n" +
	"6backend/proto/fulfillment/v1/fulfillment_service.proto\x12\x1cbackend.proto.fulfillment.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a.backend/proto/fulfillment/v1/fulfillment.proto\x1a)backend/proto/fulfillment/v1/common.proto\x1a\x1bbuf/validate/validate.proto\"\xc8\x02\n" +
	"\x19GetPetsByTimeRangeRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tcompanyId\x12(\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"businessId\x12A\n" +
	"\n" +
	"start_time\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampB\x06\xbaH\x03\xc8\x01\x01R\tstartTime\x12=\n" +
	"\bend_time\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampB\x06\xbaH\x03\xc8\x01\x01R\aendTime\x12L\n" +
	"\x06filter\x18\x05 \x01(\v2/.backend.proto.fulfillment.v1.FulfillmentFilterH\x00R\x06filter\x88\x01\x01B\t\n" +
	"\a_filter\"m\n" +
	"\x1aGetPetsByTimeRangeResponse\x129\n" +
	"\x04pets\x18\x01 \x03(\v2%.backend.proto.fulfillment.v1.PetInfoR\x04pets\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x05R\x05total\"b\n" +
	"\aPetInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vavatar_path\x18\x02 \x01(\tR\n" +
	"avatarPath\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x12\n" +
	"\x04type\x18\x04 \x01(\x05R\x04type\"\xcc\x02\n" +
	"\x1dGetPetCountByTimeRangeRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tcompanyId\x12(\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"businessId\x12A\n" +
	"\n" +
	"start_time\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampB\x06\xbaH\x03\xc8\x01\x01R\tstartTime\x12=\n" +
	"\bend_time\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampB\x06\xbaH\x03\xc8\x01\x01R\aendTime\x12L\n" +
	"\x06filter\x18\x05 \x01(\v2/.backend.proto.fulfillment.v1.FulfillmentFilterH\x00R\x06filter\x88\x01\x01B\t\n" +
	"\a_filter\"\x9f\x01\n" +
	"\x1eGetPetCountByTimeRangeResponse\x12U\n" +
	"\x10daily_pet_counts\x18\x01 \x03(\v2+.backend.proto.fulfillment.v1.DailyPetCountR\x0edailyPetCounts\x12&\n" +
	"\x0ftotal_pet_count\x18\x02 \x01(\x05R\rtotalPetCount\"\x8f\x02\n" +
	"\rDailyPetCount\x12\x12\n" +
	"\x04date\x18\x01 \x01(\tR\x04date\x12p\n" +
	"\x13pet_care_type_count\x18\x02 \x03(\v2A.backend.proto.fulfillment.v1.DailyPetCount.PetCareTypeCountEntryR\x10petCareTypeCount\x123\n" +
	"\x16pet_count_with_lodging\x18\x03 \x01(\x05R\x13petCountWithLodging\x1aC\n" +
	"\x15PetCareTypeCountEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x03R\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x05R\x05value:\x028\x01\"\xb7\x03\n" +
	"\x16ListFulfillmentRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tcompanyId\x12(\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"businessId\x129\n" +
	"\n" +
	"start_time\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12G\n" +
	"\x06filter\x18\x05 \x01(\v2/.backend.proto.fulfillment.v1.FulfillmentFilterR\x06filter\x12C\n" +
	"\tsort_type\x18\x06 \x01(\x0e2&.backend.proto.fulfillment.v1.SortTypeR\bsortType\x12K\n" +
	"\n" +
	"pagination\x18\a \x01(\v2+.backend.proto.fulfillment.v1.PaginationRefR\n" +
	"pagination\"\xe2\x01\n" +
	"\x17ListFulfillmentResponse\x12M\n" +
	"\ffulfillments\x18\x01 \x03(\v2).backend.proto.fulfillment.v1.FulfillmentR\ffulfillments\x12K\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2+.backend.proto.fulfillment.v1.PaginationRefR\n" +
	"pagination\x12\x15\n" +
	"\x06is_end\x18\x03 \x01(\bR\x05isEnd\x12\x14\n" +
	"\x05total\x18\x04 \x01(\x05R\x05total2\xb4\x03\n" +
	"\x12FulfillmentService\x12~\n" +
	"\x0fListFulfillment\x124.backend.proto.fulfillment.v1.ListFulfillmentRequest\x1a5.backend.proto.fulfillment.v1.ListFulfillmentResponse\x12\x87\x01\n" +
	"\x12GetPetsByTimeRange\x127.backend.proto.fulfillment.v1.GetPetsByTimeRangeRequest\x1a8.backend.proto.fulfillment.v1.GetPetsByTimeRangeResponse\x12\x93\x01\n" +
	"\x16GetPetCountByTimeRange\x12;.backend.proto.fulfillment.v1.GetPetCountByTimeRangeRequest\x1a<.backend.proto.fulfillment.v1.GetPetCountByTimeRangeResponseBt\n" +
	"&com.moego.backend.proto.fulfillment.v1P\x01ZHgithub.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpbb\x06proto3"

var (
	file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescOnce sync.Once
	file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescData []byte
)

func file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescGZIP() []byte {
	file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDesc), len(file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDesc)))
	})
	return file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescData
}

var file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_backend_proto_fulfillment_v1_fulfillment_service_proto_goTypes = []any{
	(*GetPetsByTimeRangeRequest)(nil),      // 0: backend.proto.fulfillment.v1.GetPetsByTimeRangeRequest
	(*GetPetsByTimeRangeResponse)(nil),     // 1: backend.proto.fulfillment.v1.GetPetsByTimeRangeResponse
	(*PetInfo)(nil),                        // 2: backend.proto.fulfillment.v1.PetInfo
	(*GetPetCountByTimeRangeRequest)(nil),  // 3: backend.proto.fulfillment.v1.GetPetCountByTimeRangeRequest
	(*GetPetCountByTimeRangeResponse)(nil), // 4: backend.proto.fulfillment.v1.GetPetCountByTimeRangeResponse
	(*DailyPetCount)(nil),                  // 5: backend.proto.fulfillment.v1.DailyPetCount
	(*ListFulfillmentRequest)(nil),         // 6: backend.proto.fulfillment.v1.ListFulfillmentRequest
	(*ListFulfillmentResponse)(nil),        // 7: backend.proto.fulfillment.v1.ListFulfillmentResponse
	nil,                                    // 8: backend.proto.fulfillment.v1.DailyPetCount.PetCareTypeCountEntry
	(*timestamppb.Timestamp)(nil),          // 9: google.protobuf.Timestamp
	(*FulfillmentFilter)(nil),              // 10: backend.proto.fulfillment.v1.FulfillmentFilter
	(SortType)(0),                          // 11: backend.proto.fulfillment.v1.SortType
	(*PaginationRef)(nil),                  // 12: backend.proto.fulfillment.v1.PaginationRef
	(*Fulfillment)(nil),                    // 13: backend.proto.fulfillment.v1.Fulfillment
}
var file_backend_proto_fulfillment_v1_fulfillment_service_proto_depIdxs = []int32{
	9,  // 0: backend.proto.fulfillment.v1.GetPetsByTimeRangeRequest.start_time:type_name -> google.protobuf.Timestamp
	9,  // 1: backend.proto.fulfillment.v1.GetPetsByTimeRangeRequest.end_time:type_name -> google.protobuf.Timestamp
	10, // 2: backend.proto.fulfillment.v1.GetPetsByTimeRangeRequest.filter:type_name -> backend.proto.fulfillment.v1.FulfillmentFilter
	2,  // 3: backend.proto.fulfillment.v1.GetPetsByTimeRangeResponse.pets:type_name -> backend.proto.fulfillment.v1.PetInfo
	9,  // 4: backend.proto.fulfillment.v1.GetPetCountByTimeRangeRequest.start_time:type_name -> google.protobuf.Timestamp
	9,  // 5: backend.proto.fulfillment.v1.GetPetCountByTimeRangeRequest.end_time:type_name -> google.protobuf.Timestamp
	10, // 6: backend.proto.fulfillment.v1.GetPetCountByTimeRangeRequest.filter:type_name -> backend.proto.fulfillment.v1.FulfillmentFilter
	5,  // 7: backend.proto.fulfillment.v1.GetPetCountByTimeRangeResponse.daily_pet_counts:type_name -> backend.proto.fulfillment.v1.DailyPetCount
	8,  // 8: backend.proto.fulfillment.v1.DailyPetCount.pet_care_type_count:type_name -> backend.proto.fulfillment.v1.DailyPetCount.PetCareTypeCountEntry
	9,  // 9: backend.proto.fulfillment.v1.ListFulfillmentRequest.start_time:type_name -> google.protobuf.Timestamp
	9,  // 10: backend.proto.fulfillment.v1.ListFulfillmentRequest.end_time:type_name -> google.protobuf.Timestamp
	10, // 11: backend.proto.fulfillment.v1.ListFulfillmentRequest.filter:type_name -> backend.proto.fulfillment.v1.FulfillmentFilter
	11, // 12: backend.proto.fulfillment.v1.ListFulfillmentRequest.sort_type:type_name -> backend.proto.fulfillment.v1.SortType
	12, // 13: backend.proto.fulfillment.v1.ListFulfillmentRequest.pagination:type_name -> backend.proto.fulfillment.v1.PaginationRef
	13, // 14: backend.proto.fulfillment.v1.ListFulfillmentResponse.fulfillments:type_name -> backend.proto.fulfillment.v1.Fulfillment
	12, // 15: backend.proto.fulfillment.v1.ListFulfillmentResponse.pagination:type_name -> backend.proto.fulfillment.v1.PaginationRef
	6,  // 16: backend.proto.fulfillment.v1.FulfillmentService.ListFulfillment:input_type -> backend.proto.fulfillment.v1.ListFulfillmentRequest
	0,  // 17: backend.proto.fulfillment.v1.FulfillmentService.GetPetsByTimeRange:input_type -> backend.proto.fulfillment.v1.GetPetsByTimeRangeRequest
	3,  // 18: backend.proto.fulfillment.v1.FulfillmentService.GetPetCountByTimeRange:input_type -> backend.proto.fulfillment.v1.GetPetCountByTimeRangeRequest
	7,  // 19: backend.proto.fulfillment.v1.FulfillmentService.ListFulfillment:output_type -> backend.proto.fulfillment.v1.ListFulfillmentResponse
	1,  // 20: backend.proto.fulfillment.v1.FulfillmentService.GetPetsByTimeRange:output_type -> backend.proto.fulfillment.v1.GetPetsByTimeRangeResponse
	4,  // 21: backend.proto.fulfillment.v1.FulfillmentService.GetPetCountByTimeRange:output_type -> backend.proto.fulfillment.v1.GetPetCountByTimeRangeResponse
	19, // [19:22] is the sub-list for method output_type
	16, // [16:19] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_backend_proto_fulfillment_v1_fulfillment_service_proto_init() }
func file_backend_proto_fulfillment_v1_fulfillment_service_proto_init() {
	if File_backend_proto_fulfillment_v1_fulfillment_service_proto != nil {
		return
	}
	file_backend_proto_fulfillment_v1_fulfillment_proto_init()
	file_backend_proto_fulfillment_v1_common_proto_init()
	file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes[0].OneofWrappers = []any{}
	file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes[3].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDesc), len(file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_fulfillment_v1_fulfillment_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_fulfillment_v1_fulfillment_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes,
	}.Build()
	File_backend_proto_fulfillment_v1_fulfillment_service_proto = out.File
	file_backend_proto_fulfillment_v1_fulfillment_service_proto_goTypes = nil
	file_backend_proto_fulfillment_v1_fulfillment_service_proto_depIdxs = nil
}
