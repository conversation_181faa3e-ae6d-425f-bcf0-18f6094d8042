load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "fulfillmentpb_proto",
    srcs = [
        "appointment.proto",
        "appointment_service.proto",
        "common.proto",
        "fulfillment.proto",
        "fulfillment_report.proto",
        "fulfillment_report_service.proto",
        "fulfillment_service.proto",
        "instance.proto",
        "instance_service.proto",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "//backend/proto/offering/v1:offeringpb_proto",
        "//backend/proto/pet/v1:petpb_proto",
        "@com_github_bufbuild_protovalidate//proto/protovalidate/buf/validate:validate_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@googleapis//google/type:latlng_proto",
    ],
)

go_proto_library(
    name = "fulfillmentpb_go_proto",
    compilers = [
        "@io_bazel_rules_go//proto:go_grpc_v2",
        "@io_bazel_rules_go//proto:go_proto",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1",
    proto = ":fulfillmentpb_proto",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/proto/offering/v1:offering",
        "//backend/proto/pet/v1:pet",
        "@build_buf_gen_go_bufbuild_protovalidate_protocolbuffers_go//buf/validate:go_default_library",
        "@org_golang_google_genproto//googleapis/type/latlng",
    ],
)

go_library(
    name = "fulfillment",
    embed = [":fulfillmentpb_go_proto"],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1",
    visibility = ["//visibility:public"],
)
