syntax = "proto3";

package backend.proto.fulfillment.v1;

import "google/protobuf/timestamp.proto";
import "buf/validate/validate.proto";
import "backend/proto/fulfillment/v1/common.proto";
import "google/type/timeofday.proto";
import "google/protobuf/duration.proto";

option go_package="github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.fulfillment.v1";

// 预约过滤器
// (-- api-linter: core::0216::state-field-output-only=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
message AppointmentFilter {
  // 状态列表
  repeated AppointmentState statuses = 1;
}

// 创建预约定义
message CreateAppointmentDef {
  // 商家ID
  int64 business_id = 2 [(buf.validate.field).int64.gt = 0];
  // 公司ID
  int64 company_id = 3 [(buf.validate.field).int64.gt = 0];
  // 客户ID
  int64 customer_id = 4;
  // 预约状态+1
  // (-- api-linter: core::0216::state-field-output-only=disabled
  //     aip.dev/not-precedent: 状态字段需要可写 --)
  AppointmentState status = 5;
  // 颜色代码
  string color_code = 7;
  // 开始时间
  google.protobuf.Timestamp start_time = 8;
  // 结束时间
  google.protobuf.Timestamp end_time = 9;
  // 宠物详情列表
  repeated CreatePetDetailDef pets = 10;
}

// 创建宠物详情定义
message CreatePetDetailDef {
  // 宠物ID
  int64 pet_id = 1;
  // 服务实例列表
  repeated CreateServiceInstanceDef services = 2;
}

// 创建服务实例定义
message CreateServiceInstanceDef {
  // 服务模板ID
  int64 id = 1;
  // 时间配置，像service这类明确知道起始时间的，用time_config给时间戳
  // 如果是addon类需要跟随service的，给date_schedule_config
  // 两个参数oneof,2选1
  oneof time {
    // 时间配置
    TimeConfig time_config = 2;
    // 日期调度配置
    DateScheduleConfig date_schedule_config = 3;
  }
  // 员工ID
  optional int64 staff_id = 4;
  // 工作模式 (0 - parallel, 1 - sequence)
  optional WorkMode work_mode = 5;
  // 子服务实例列表
  repeated CreateServiceInstanceDef sub_service_instances = 6;
  // 服务费用列表
  repeated ServiceCharge charges = 7;
  // 价格
  optional int64 price = 8;
  // 时长
  optional google.protobuf.Duration duration = 9;
  // 用药计划
  repeated AppointmentPetMedicationScheduleDef medications = 10;
  // 喂养计划
  repeated AppointmentPetFeedingScheduleDef feedings = 11;
  // 服务细则
  optional ServiceDetail service_detail = 12;
  // 住宿ID
  optional int64 lodging_id = 13;
}

// 时间配置
message TimeConfig {
  // 服务开始时间
  optional google.protobuf.Timestamp start_time = 1;
  // 服务结束时间
  optional google.protobuf.Timestamp end_time = 2;
}

// 日期调度配置
message DateScheduleConfig {
  // 日期类型
  DateType date_type = 1;
  // 格式: "15:30:15"
  // (-- api-linter: core::0142::time-field-type=disabled
  //     aip.dev/not-precedent: start_minute represents minute offset, not timestamp --)
  optional google.type.TimeOfDay regular_time = 2;
  // 特定日期配置列表（当 date_type 为 DATE_TYPE_SPECIFIC_DATE 时使用）
  // (-- api-linter: core::0142::time-field-names=disabled
  //     aip.dev/not-precedent: We need to do this because reasons. --)
  repeated google.protobuf.Timestamp specific_dates = 4;
}

// 预约单核心数据
message Appointment {
  // 预约ID
  int64 id = 1;
  // 商家ID
  int64 business_id = 2 [(buf.validate.field).int64.gt = 0];
  // 公司ID
  int64 company_id = 3 [(buf.validate.field).int64.gt = 0];
  // 客户ID
  int64 customer_id = 4;
  // 预约状态
  // (-- api-linter: core::0216::state-field-output-only=disabled
  //     aip.dev/not-precedent: 状态字段需要可写 --)
  AppointmentState status = 5;
  // 服务位图
  int32 service_item_type = 6;
  // 颜色代码
  string color_code = 7;
  // 开始时间
  google.protobuf.Timestamp start_time = 8;
  // 结束时间
  google.protobuf.Timestamp end_time = 9;
  // 宠物详情列表
  repeated PetDetail pets = 10;
  // 来源
  AppointmentSource source = 11;
  // 所有宠物开始时间是否相同
  // (-- api-linter: core::0142::time-field-type=disabled
  //     aip.dev/not-precedent: This is a boolean field, not a timestamp field --)
  bool all_pets_start_same_time = 12;
  // 备注列表
  repeated Note notes = 13;
}

// Pre-auth enable definition
// Can pass the association when creating an appointment,
// or can send a request cof link to associate at the time of callback.
message PreAuthEnableDef {
  // Enable pre-auth
  bool enable = 1 [(buf.validate.field).bool = {const: true}];
  // Stripe payment method id, such as pm_1ObH0nIZwcIFVLGrR5SnOkLU
  optional string payment_method_id = 2 [(buf.validate.field).string = {
    pattern: "^pm_.*$"
    max_len: 255
  }];
  // Card brand and last 4 digits, such as Visa(1111)
  optional string card_brand_last4 = 3 [(buf.validate.field).string = {max_len: 255}];
}

// Pet belonging
message PetBelonging {
  // pet id
  int64 pet_id = 1 [(buf.validate.field).int64.gt = 0];
  // pet name
  string name = 2 [(buf.validate.field).string = {
    min_len: 1
    max_len: 1024
  }];
  // pet area
  optional string area = 3 [(buf.validate.field).string = {max_len: 100}];
  // pet photo uri
  optional string photo_uri = 4 [(buf.validate.field).string = {
    max_len: 1024
    uri: true
  }];
}

// 宠物信息
message PetDetail {
  // 宠物ID
  int64 pet_id = 1;
  // 服务实例列表
  repeated ServiceInstanceImpl services = 2;
}

// 服务实例
message ServiceInstanceImpl {
  // 服务实例ID
  int64 service_instance_id = 1;
  // 服务模板ID
  int64 id = 2;
  // 服务开始时间
  google.protobuf.Timestamp start_time = 3;
  // 服务结束时间
  google.protobuf.Timestamp end_time = 4;
  // 员工ID
  optional int64 staff_id = 5;
  // 工作模式 (0 - parallel, 1 - sequence)
  optional WorkMode work_mode = 6;
  // 子服务实例列表
  repeated ServiceInstanceImpl sub_service_instances = 7;
  // 服务费用列表
  repeated ServiceCharge charges = 8;
  // 喂养用药列表
  repeated FeedingMedication feeding_medications = 9;
  // 服务细则（包含所有执行细节）
  optional ServiceDetail service_detail = 10;
  // 价格
  optional int64 price = 11;
  // 时长
  optional int64 duration = 12;
}

// 服务细则
message ServiceDetail{
  // 员工细则列表
  repeated StaffDetail staff_details = 1;
}

// 员工细则 
message StaffDetail {
   // staff id
  int64 staff_id = 1 [(buf.validate.field).int64.gt = 0];
  // operation name
  optional string operation_name = 2 [(buf.validate.field).string = {max_len: 150}];
  // start time, 格式: "15:30:15"
  optional google.type.TimeOfDay start_time = 3;
  // duration
  optional google.protobuf.Duration duration = 4;
  // price ratio
  optional double price_ratio = 5 [(buf.validate.field).double = {
    gte: 0
    lte: 1
  }];
  // exact price
  optional double price = 6 [(buf.validate.field).double = {gte: 0}];
}

// 服务费用
message ServiceCharge {
  // 费用名称
  string name = 1;
  // 费用金额
  double amount = 2;
  // 费用描述
  string description = 3;
  // 费用类型
  int32 charge_type = 4;
}

// 备注
message Note {
  // 备注类型
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  AppointmentNoteType type = 1;
  // 备注内容
  string content = 2;
}

// 喂养用药
message FeedingMedication {
  // 喂养规则
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  string feeding_rule = 1;
  // 用药规则
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  string medication_rule = 2;
  // 用药计划列表
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  repeated MedicationSchedule medications = 3;
}

// 用药计划
message MedicationSchedule {
  // 药品名称
  // (-- api-linter: core::0122::name-suffix=disabled
  //     aip.dev/not-precedent: medication is clear and appropriate --)
  string medication = 1;
  // 剂量
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  string dosage = 2;
  // 频率(如"每日两次")
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  string frequency = 3;
  // 给药方式(如"口服")
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  string administration_method = 4;
  // 注意事项
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  string notes = 5;
}

// 服务选项
message ServiceOption {
  // 服务选项模板ID
  int64 service_option_template_id = 1;
  // 开始时间
  google.protobuf.Timestamp start_time = 2;
  // 结束时间
  google.protobuf.Timestamp end_time = 3;
  // 选项名称
  string name = 4;
  // 数量
  int32 quantity = 5;
  // 价格
  double price = 6;
  // 税费
  double tax = 7;
  // 每天执行数量
  int64 quantity_per_day = 8;
}

// 元数据
message Metadata{
  // 标签映射
  map<string, string> tags = 1;
}

// 预约单操作结果
message AppointmentOperationResult {
  // 操作类型
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  string operation_type = 1; // "start_time", "end_time", "color_code", "status"
  // 操作是否成功
  bool success = 2;
  // 错误消息
  string error_message = 3;

  // 操作前的值（如果更新失败，用于回滚）
  optional google.protobuf.Timestamp old_start_time = 4;
  // 操作前的结束时间
  optional google.protobuf.Timestamp old_end_time = 5;
  // 操作前的颜色代码
  optional string old_color_code = 6;
  // 操作前的状态
  // (-- api-linter: core::0216::state-field-output-only=disabled
  //     aip.dev/not-precedent: 状态字段需要可写 --)
  optional AppointmentState old_status = 7;

  // 操作后的值
  optional google.protobuf.Timestamp new_start_time = 8;
  // 操作后的结束时间
  optional google.protobuf.Timestamp new_end_time = 9;
  // 操作后的颜色代码
  optional string new_color_code = 10;
  // 操作后的状态
  // (-- api-linter: core::0216::state-field-output-only=disabled
  //     aip.dev/not-precedent: 状态字段需要可写 --)
  optional AppointmentState new_status = 11;
}

// 宠物操作结果
message PetOperationResult {
  // 宠物ID
  int64 pet_id = 1;
  // 操作模式
  OperationMode operation_mode = 2;
  // 操作是否成功
  bool success = 3;
  // 错误消息
  string error_message = 4;

  // 操作详情
  oneof operation_detail {
    // 删除结果
    PetDeleteResult delete_result = 5;
    // 创建结果
    PetCreateResult create_result = 6;
    // 更新结果
    PetUpdateResult update_result = 7;
  }
}

// 宠物删除结果
message PetDeleteResult {
  // 是否删除成功
  bool deleted = 1;
  // 删除的服务数量
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  int32 deleted_service_count = 2;
}

// 宠物创建结果
message PetCreateResult {
  // 新宠物ID
  int64 new_pet_id = 1;
  // 创建的服务数量
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  int32 created_service_count = 2;
}

// 宠物更新结果
message PetUpdateResult {
  // 更新后的宠物
  PetDetail updated_pet = 1;
  // 更新的服务数量
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  int32 updated_service_count = 2;
}

// 服务操作结果
message ServiceOperationResult {
  // 服务实例ID
  int64 service_instance_id = 1;
  // 宠物ID
  int64 pet_id = 2;
  // 操作模式
  OperationMode operation_mode = 3;
  // 操作是否成功
  bool success = 4;
  // 错误消息
  string error_message = 5;

  // 操作详情
  oneof operation_detail {
    // 删除结果
    ServiceDeleteResult delete_result = 6;
    // 创建结果
    ServiceCreateResult create_result = 7;
    // 更新结果
    ServiceUpdateResult update_result = 8;
  }
}

// 服务删除结果
message ServiceDeleteResult {
  // 是否删除成功
  bool deleted = 1;
  // 删除的附加服务数量
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  int32 deleted_option_count = 2;
}

// 服务创建结果
message ServiceCreateResult {
  // 新服务实例ID
  int64 new_service_instance_id = 1;
  // 创建的附加服务数量
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  int32 created_option_count = 2;
}

// 服务更新结果
message ServiceUpdateResult {
  // 更新后的服务
  ServiceInstanceImpl updated_service = 1;
  // 更新的附加服务数量
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  int32 updated_option_count = 2;
}

// 附加服务操作结果
message OptionOperationResult {
  // 服务选项ID
  int64 service_option_id = 1;
  // 服务实例ID
  int64 service_instance_id = 2;
  // 操作模式
  OperationMode operation_mode = 3;
  // 操作是否成功
  bool success = 4;
  // 错误消息
  string error_message = 5;

  // 操作详情
  oneof operation_detail {
    // 删除结果
    OptionDeleteResult delete_result = 6;
    // 创建结果
    OptionCreateResult create_result = 7;
    // 更新结果
    OptionUpdateResult update_result = 8;
  }
}

// 选项删除结果
message OptionDeleteResult {
  // 是否删除成功
  bool deleted = 1;
}

// 选项创建结果
message OptionCreateResult {
  // 新服务选项ID
  int64 new_service_option_id = 1;
}

// 选项更新结果
message OptionUpdateResult {
  // 更新后的选项
  ServiceOption updated_option = 1;
}

// Appointment pet medication schedule definition
message AppointmentPetMedicationScheduleDef {
  // medication amount, such as 1.2, 1/2, 1 etc.
  string medication_amount = 1 [(buf.validate.field).string = {max_len: 255}];

  // medication unit, pet_metadata.metadata_value, metadata_name = 7
  string medication_unit = 2 [(buf.validate.field).string = {max_len: 255}];

  // medication name, user input
  string medication_name = 3 [(buf.validate.field).string = {max_len: 255}];

  // medication source, user input
  optional string medication_note = 4 [(buf.validate.field).string = {max_len: 10240}];

  // medication time, medication time in minutes, 09:00 AM = 540, 09:30 AM = 570 etc.
  repeated BusinessPetScheduleTimeDef medication_times = 6 [(buf.validate.field).repeated = { 
    min_items: 1, max_items: 99 
  }];

  // feeding medication schedule selected date
  optional SelectedDateDef selected_date = 7;

  // selected date
  message SelectedDateDef {
    // feeding medication schedule date type
    // default for PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY
    FeedingMedicationScheduleDateType date_type = 1 [(buf.validate.field).enum = {
      defined_only: true
      not_in: [0]
    }];
    // specific date
    repeated string specific_dates = 2 [(buf.validate.field).repeated = {
      max_items: 100
      items: {
        string: {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}
      }
    }];
  }
}

// Appointment pet feeding schedule definition
message AppointmentPetFeedingScheduleDef {
  // feeding amount, such as 1.2, 1/2, 1 etc.
  string feeding_amount = 1 [(buf.validate.field).string = {max_len: 255}];

  // feeding unit, pet_metadata.metadata_value, metadata_name = 2
  string feeding_unit = 2 [(buf.validate.field).string = {max_len: 255}];

  // feeding type, pet_metadata.metadata_value, metadata_name = 3
  string feeding_type = 3 [(buf.validate.field).string = {max_len: 255}];

  // feeding source, pet_metadata.metadata_value, metadata_name = 4
  string feeding_source = 4 [(buf.validate.field).string = {max_len: 255}];

  // feeding instruction, pet_metadata.metadata_value, metadata_name = 5
  optional string feeding_instruction = 5 [(buf.validate.field).string = {max_len: 255}];

  // feeding time, feeding time in minutes, 09:00 AM = 540, 09:30 AM = 570 etc.
  repeated BusinessPetScheduleTimeDef feeding_times = 6 [(buf.validate.field).repeated = {
    min_items: 1, max_items: 99 
  }];

  // feeding note, user input
  optional string feeding_note = 7 [(buf.validate.field).string = {max_len: 10240}];
}

// Business pet schedule time definition
message BusinessPetScheduleTimeDef {
  // schedule time, schedule time in minutes, 09:00 AM = 540, 09:30 AM = 570 etc.
  int32 schedule_time = 1 [(buf.validate.field).int32 = {
    gte: 0, lte: 1440
  }];
  // schedule extra json, such as schedule time label etc.
  map<string, string> extra_json = 2 [(buf.validate.field).map = {
    min_pairs: 0, max_pairs: 1000
  }];
}

// 重复类型
enum RepeatType {
  // 未指定重复类型
  REPEAT_TYPE_UNSPECIFIED = 0;
  // 每日重复
  REPEAT_TYPE_DAILY = 1;
  // 每周重复
  REPEAT_TYPE_WEEKLY = 2;
  // 每月重复
  REPEAT_TYPE_MONTHLY = 3;
}
// 预约单状态枚举
// (-- api-linter: core::0216::nesting=disabled
//     aip.dev/not-precedent: AppointmentState is appropriate as top-level enum --)
enum AppointmentState {
  // 未指定状态
  APPOINTMENT_STATE_UNSPECIFIED = 0;
  // 未确认
  APPOINTMENT_STATE_UNCONFIRMED = 1;
  // 已确认
  APPOINTMENT_STATE_CONFIRMED = 2;
  // 已完成(check out)
  APPOINTMENT_STATE_FINISHED = 3;
  // 已取消
  APPOINTMENT_STATE_CANCELED = 4;
  // 准备就绪
  APPOINTMENT_STATE_READY = 5;
  // 已入住
  APPOINTMENT_STATE_CHECKED_IN = 6;
}

// 操作模式枚举
enum OperationMode {
  // 未指定操作模式
  OPERATION_MODE_UNSPECIFIED = 0;
  // 创建
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  OPERATION_MODE_CREATE = 1;
  // 更新
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  OPERATION_MODE_UPDATE = 2;
  // 删除
  // (-- api-linter: core::0192::only-leading-comments=disabled
  //     aip.dev/not-precedent: inline comment is clear and helpful --)
  OPERATION_MODE_DELETE = 3;
}

// appointment note type
enum AppointmentNoteType {
  // unspecified
  APPOINTMENT_NOTE_TYPE_UNSPECIFIED = 0;
  // Alert notes
  ALERT_NOTES = 1;
  // Ticket comments, Block description
  COMMENT = 2;
  // Cancel reason
  CANCEL = 3;
  // Booking request additional note
  ADDITIONAL = 4;
}

// AppointmentSource source
enum AppointmentSource {
  // Unspecified
  APPOINTMENT_SOURCE_UNSPECIFIED = 0;
  // web
  WEB = 22018;
  // online booking
  OB = 22168;
  // android
  ANDROID = 17216;
  // ios
  IOS = 17802;
  // auto dm
  AUTO_DM = 23426;
  // google calendar
  GOOGLE_CALENDAR = 19826;
  // open api
  OPEN_API = 23333;
}

// 工作模式枚举
enum WorkMode {
  // 未指定工作模式
  WORK_MODE_UNSPECIFIED = 0;
  // parallel
  PARALLEL = 1;
  // sequence
  SEQUENCE = 2;
}

// feeding medication schedule date type
enum FeedingMedicationScheduleDateType {
  // unspecified
  FEEDING_MEDICATION_SCHEDULE_DATE_TYPE_UNSPECIFIED = 0;
  // Every day except for checkout day
  EVERYDAY_EXCEPT_CHECKOUT_DATE = 1;
  // Every day include checkout day
  EVERYDAY_INCLUDE_CHECKOUT_DATE = 2;
  // Specific date
  SPECIFIC_DATE = 3;
}

// 特定日期调度类型
enum SpecificDateScheduleType {
  // 未指定类型
  SPECIFIC_DATE_SCHEDULE_TYPE_UNSPECIFIED = 0;
  // 每天固定时间：所有日期使用相同的固定时间
  SPECIFIC_DATE_SCHEDULE_TYPE_REGULAR_TIME = 1;
  // 用户自定义时间：每个日期可以有不同的时间
  SPECIFIC_DATE_SCHEDULE_TYPE_CUSTOM_TIME = 2;
}
