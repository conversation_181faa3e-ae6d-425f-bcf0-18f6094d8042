// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/authn/v1/authn_service.proto

package authnpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ErrCode 定义错误码枚举
//
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
type ErrCode int32

const (
	// (-- api-linter: core::0126::unspecified=disabled
	//
	//	aip.dev/not-precedent: We need to do this because
	//	the content of the error code is automatically generated by
	//	the script and is exclusive to each service.
	//	Please do not turn off this linter for the rest of the enum --)
	//
	// 成功
	ErrCode_ERR_CODE_OK ErrCode = 0
	// 本服务自动分配的全局唯一的起始错误码
	ErrCode_ERR_CODE_UNSPECIFIED ErrCode = 997100
)

// Enum value maps for ErrCode.
var (
	ErrCode_name = map[int32]string{
		0:      "ERR_CODE_OK",
		997100: "ERR_CODE_UNSPECIFIED",
	}
	ErrCode_value = map[string]int32{
		"ERR_CODE_OK":          0,
		"ERR_CODE_UNSPECIFIED": 997100,
	}
)

func (x ErrCode) Enum() *ErrCode {
	p := new(ErrCode)
	*p = x
	return p
}

func (x ErrCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrCode) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_authn_v1_authn_service_proto_enumTypes[0].Descriptor()
}

func (ErrCode) Type() protoreflect.EnumType {
	return &file_backend_proto_authn_v1_authn_service_proto_enumTypes[0]
}

func (x ErrCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrCode.Descriptor instead.
func (ErrCode) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_authn_v1_authn_service_proto_rawDescGZIP(), []int{0}
}

// SendPingRequest
type SendPingRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 测试用的字段
	Ping          string `protobuf:"bytes,1,opt,name=ping,proto3" json:"ping,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendPingRequest) Reset() {
	*x = SendPingRequest{}
	mi := &file_backend_proto_authn_v1_authn_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendPingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendPingRequest) ProtoMessage() {}

func (x *SendPingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_authn_v1_authn_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendPingRequest.ProtoReflect.Descriptor instead.
func (*SendPingRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_authn_v1_authn_service_proto_rawDescGZIP(), []int{0}
}

func (x *SendPingRequest) GetPing() string {
	if x != nil {
		return x.Ping
	}
	return ""
}

// SendPingResponse
type SendPingResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// pong
	Pong          string `protobuf:"bytes,1,opt,name=pong,proto3" json:"pong,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendPingResponse) Reset() {
	*x = SendPingResponse{}
	mi := &file_backend_proto_authn_v1_authn_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendPingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendPingResponse) ProtoMessage() {}

func (x *SendPingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_authn_v1_authn_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendPingResponse.ProtoReflect.Descriptor instead.
func (*SendPingResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_authn_v1_authn_service_proto_rawDescGZIP(), []int{1}
}

func (x *SendPingResponse) GetPong() string {
	if x != nil {
		return x.Pong
	}
	return ""
}

var File_backend_proto_authn_v1_authn_service_proto protoreflect.FileDescriptor

const file_backend_proto_authn_v1_authn_service_proto_rawDesc = "" +
	"\n" +
	"*backend/proto/authn/v1/authn_service.proto\x12\x16backend.proto.authn.v1\x1a\x1bbuf/validate/validate.proto\".\n" +
	"\x0fSendPingRequest\x12\x1b\n" +
	"\x04ping\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x04ping\"&\n" +
	"\x10SendPingResponse\x12\x12\n" +
	"\x04pong\x18\x01 \x01(\tR\x04pong*6\n" +
	"\aErrCode\x12\x0f\n" +
	"\vERR_CODE_OK\x10\x00\x12\x1a\n" +
	"\x14ERR_CODE_UNSPECIFIED\x10\xec\xed<2m\n" +
	"\fAuthnService\x12]\n" +
	"\bSendPing\x12'.backend.proto.authn.v1.SendPingRequest\x1a(.backend.proto.authn.v1.SendPingResponseBb\n" +
	" com.moego.backend.proto.authn.v1P\x01Z<github.com/MoeGolibrary/moego/backend/proto/authn/v1;authnpbb\x06proto3"

var (
	file_backend_proto_authn_v1_authn_service_proto_rawDescOnce sync.Once
	file_backend_proto_authn_v1_authn_service_proto_rawDescData []byte
)

func file_backend_proto_authn_v1_authn_service_proto_rawDescGZIP() []byte {
	file_backend_proto_authn_v1_authn_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_authn_v1_authn_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_authn_v1_authn_service_proto_rawDesc), len(file_backend_proto_authn_v1_authn_service_proto_rawDesc)))
	})
	return file_backend_proto_authn_v1_authn_service_proto_rawDescData
}

var file_backend_proto_authn_v1_authn_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_backend_proto_authn_v1_authn_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_backend_proto_authn_v1_authn_service_proto_goTypes = []any{
	(ErrCode)(0),             // 0: backend.proto.authn.v1.ErrCode
	(*SendPingRequest)(nil),  // 1: backend.proto.authn.v1.SendPingRequest
	(*SendPingResponse)(nil), // 2: backend.proto.authn.v1.SendPingResponse
}
var file_backend_proto_authn_v1_authn_service_proto_depIdxs = []int32{
	1, // 0: backend.proto.authn.v1.AuthnService.SendPing:input_type -> backend.proto.authn.v1.SendPingRequest
	2, // 1: backend.proto.authn.v1.AuthnService.SendPing:output_type -> backend.proto.authn.v1.SendPingResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_backend_proto_authn_v1_authn_service_proto_init() }
func file_backend_proto_authn_v1_authn_service_proto_init() {
	if File_backend_proto_authn_v1_authn_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_authn_v1_authn_service_proto_rawDesc), len(file_backend_proto_authn_v1_authn_service_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_authn_v1_authn_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_authn_v1_authn_service_proto_depIdxs,
		EnumInfos:         file_backend_proto_authn_v1_authn_service_proto_enumTypes,
		MessageInfos:      file_backend_proto_authn_v1_authn_service_proto_msgTypes,
	}.Build()
	File_backend_proto_authn_v1_authn_service_proto = out.File
	file_backend_proto_authn_v1_authn_service_proto_goTypes = nil
	file_backend_proto_authn_v1_authn_service_proto_depIdxs = nil
}
