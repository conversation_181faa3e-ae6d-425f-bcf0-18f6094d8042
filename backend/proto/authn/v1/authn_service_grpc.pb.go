// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/authn/v1/authn_service.proto

package authnpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AuthnService_SendPing_FullMethodName = "/backend.proto.authn.v1.AuthnService/SendPing"
)

// AuthnServiceClient is the client API for AuthnService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// AuthnService
type AuthnServiceClient interface {
	// SendPing sends a ping request and returns a pong response
	SendPing(ctx context.Context, in *SendPingRequest, opts ...grpc.CallOption) (*SendPingResponse, error)
}

type authnServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAuthnServiceClient(cc grpc.ClientConnInterface) AuthnServiceClient {
	return &authnServiceClient{cc}
}

func (c *authnServiceClient) SendPing(ctx context.Context, in *SendPingRequest, opts ...grpc.CallOption) (*SendPingResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendPingResponse)
	err := c.cc.Invoke(ctx, AuthnService_SendPing_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AuthnServiceServer is the server API for AuthnService service.
// All implementations must embed UnimplementedAuthnServiceServer
// for forward compatibility.
//
// AuthnService
type AuthnServiceServer interface {
	// SendPing sends a ping request and returns a pong response
	SendPing(context.Context, *SendPingRequest) (*SendPingResponse, error)
	mustEmbedUnimplementedAuthnServiceServer()
}

// UnimplementedAuthnServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAuthnServiceServer struct{}

func (UnimplementedAuthnServiceServer) SendPing(context.Context, *SendPingRequest) (*SendPingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendPing not implemented")
}
func (UnimplementedAuthnServiceServer) mustEmbedUnimplementedAuthnServiceServer() {}
func (UnimplementedAuthnServiceServer) testEmbeddedByValue()                      {}

// UnsafeAuthnServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AuthnServiceServer will
// result in compilation errors.
type UnsafeAuthnServiceServer interface {
	mustEmbedUnimplementedAuthnServiceServer()
}

func RegisterAuthnServiceServer(s grpc.ServiceRegistrar, srv AuthnServiceServer) {
	// If the following call pancis, it indicates UnimplementedAuthnServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AuthnService_ServiceDesc, srv)
}

func _AuthnService_SendPing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendPingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthnServiceServer).SendPing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthnService_SendPing_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthnServiceServer).SendPing(ctx, req.(*SendPingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AuthnService_ServiceDesc is the grpc.ServiceDesc for AuthnService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AuthnService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.authn.v1.AuthnService",
	HandlerType: (*AuthnServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendPing",
			Handler:    _AuthnService_SendPing_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/authn/v1/authn_service.proto",
}
