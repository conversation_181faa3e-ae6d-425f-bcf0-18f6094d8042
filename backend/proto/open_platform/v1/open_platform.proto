// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: 使用company_id作为标识符 --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 没有分页的需求, 所以没有必要设置page_token --)
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 没有分页的需求, 所以没有必要设置page_size --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 使用自定义分页方式 --)
// (-- api-linter: core::0131::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0122::name-suffix=disabled
//     aip.dev/not-precedent: 使用_name后缀作为字段名 --)
// (-- api-linter: core::0136::response-message-name=disabled
//     aip.dev/not-precedent: 使用empty返回 --)
// (-- api-linter: core::0140::uri=disabled
//     aip.dev/not-precedent: 使用url --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: 使用customers --)

syntax = "proto3";

package backend.proto.open_platform.v1;

option go_package = "github.com/MoeGolibrary/moego/backend/proto/open_platform/v1;open_platformpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.open_platform.v1";

// Userinfo represents the structure of user information returned by the OAuth2 API.
message GoogleOAuthUserInfo {
  // The user's email address.
  string email = 1;
  // The user's last name.
  string family_name = 2;
  // The user's gender.
  string gender = 3;
  // The user's first name.
  string given_name = 4;
  // The hosted domain e.g. example.com if the user is Google apps user.
  string hd = 5;
  // The obfuscated ID of the user.
  string id = 6;
  // URL of the profile page.
  string link = 7;
  // The user's preferred locale.
  string locale = 8;
  // The user's full name.
  string name = 9;
  // URL of the user's picture image.
  string picture = 10;
  // Boolean flag which is true if the email address is verified.
  // Always verified because we only return the user's primary email address.
  optional bool verified_email = 11;
}

// moego google ads setting
message GoogleAdsSetting {
  // link google ads customer ids
  repeated int64 link_google_ads_customer_ids = 1;
}

// https://github.com/googleapis/googleapis/blob/master/google/ads/googleads/v20/resources/customer.proto
// A customer.
message GoogleAdsCustomer {
  // Immutable. The resource name of the customer.
  // Customer resource names have the form:
  //
  // `customers/{customer_id}`
  string resource_name = 1;

  // Output only. The ID of the customer.
  optional int64 id = 19;

  // Optional, non-unique descriptive name of the customer.
  optional string descriptive_name = 20;

  // Immutable. The currency in which the account operates.
  // A subset of the currency codes from the ISO 4217 standard is
  // supported.
  optional string currency_code = 21;

  // Immutable. The local timezone ID of the customer.
  optional string time_zone = 22 ;

  // The URL template for constructing a tracking URL out of parameters.
  // Only mutable in an `update` operation.
  optional string tracking_url_template = 23;

  // The URL template for appending params to the final URL.
  // Only mutable in an `update` operation.
  optional string final_url_suffix = 24;

  // Whether auto-tagging is enabled for the customer.
  // Only mutable in an `update` operation.
  optional bool auto_tagging_enabled = 25;

  // Output only. Whether the Customer has a Partners program badge. If the
  // Customer is not associated with the Partners program, this will be false.
  // For more information, see
  // https://support.google.com/partners/answer/3125774.
  optional bool has_partners_badge = 26 ;

  // Output only. Whether the customer is a manager.
  optional bool manager = 27 ;

  // Output only. Whether the customer is a test account.
  optional bool test_account = 28;

  // Output only. Optimization score of the customer.
  //
  // Optimization score is an estimate of how well a customer's campaigns are
  // set to perform. It ranges from 0% (0.0) to 100% (1.0). This field is null
  // for all manager customers, and for unscored non-manager customers.
  //
  // See "About optimization score" at
  // https://support.google.com/google-ads/answer/9061546.
  //
  // This field is read-only.
  optional double optimization_score = 29 ;
}

// Userinfo represents the structure of user information returned by the OAuth2 API.
message MetaOAuthUserInfo {
  // The user's email address.
  string email = 1;
  // The user's name.
  string name = 2;
  // The obfuscated ID of the user.
  string id = 3;
}

// https://developers.facebook.com/docs/marketing-api/reference/ad-account/
message MetaAdsAccount {
  // The resource name, e.g. "act_***************"
  string id = 1;
  // The numeric account id, e.g. "***************"
  string account_id = 2;
  // The account name
  string name = 3;
  // The account status
  int32 account_status = 4;
  // The currency code, e.g. "USD"
  string currency_code = 5;
  // The amount spent, as a string (Facebook返回的是字符串)
  string amount_spent = 6;
  // The business name
  string business_name = 7;
}

// moego meta ads setting
message MetaAdsSetting {
  // link meta ads count ids
  repeated int64 link_meta_ads_account_ids = 1;
}