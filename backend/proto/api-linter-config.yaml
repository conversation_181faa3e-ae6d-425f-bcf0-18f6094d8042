---
- included_paths:
      - "backend/proto/**/*.proto"
  disabled_rules:
      # we don't need java outer classname, we use java_multiple_files
      - "core::0191::java-outer-classname"
      # this should not be mandatory in Create method
      - "core::0123::resource-annotation"
      - "core::0127::http-annotation"
      # This rule requires us to add a signature to methods that start with "Get", but this signature is of no use to us.
      # https://linter.aip.dev/131/method-signature
      - "core::0131::method-signature"
      # This rule requires us to add a signature to methods that start with "List", but this signature is of no use to us.
      # https://linter.aip.dev/132/method-signature
      - "core::0132::method-signature"
      # This rule requires us to add a signature to methods that start with "Create", but this signature is of no use to us.
      # https://linter.aip.dev/133/method-signature
      - "core::0133::method-signature"
      # This rule requires us to add a signature to methods that start with "Update", but this signature is of no use to us.
      # https://linter.aip.dev/134/method-signature
      - "core::0134::method-signature"
      # This rule requires us to add a signature to methods that start with "Delete", but this signature is of no use to us.
      # https://linter.aip.dev/135/method-signature
      - "core::0135::method-signature"
      # This rule requires us to add `google.api.field_behavior` annotation to `Get` request message, but this annotation has no meaning to us.
      # https://linter.aip.dev/131/request-name-behavior
      - "core::0131::request-name-behavior"
      # This rule requires us to add `google.api.resource_reference` annotation to `Get` request message, but this annotation has no meaning to us.
      # https://linter.aip.dev/131/request-name-reference
      - "core::0131::request-name-reference"
      # The following two rules enforces that all Get standard methods must have only a string name field in the request message, but our resource `Get` request does not use name to query.
      # https://linter.aip.dev/131/request-name-required
      # https://linter.aip.dev/131/request-unknown-fields
      - "core::0131::request-name-required"
      - "core::0131::request-unknown-fields"
      # This rule enforces that `List*Request` messages to contain only specific fields, but our list request is usually a batch query with arbitrary conditions.
      # https://linter.aip.dev/132/request-unknown-fields
      - "core::0132::request-unknown-fields"
      # This rule enforces that `Create*Request` messages to contain only specific fields, but our create request is usually a few fields describing the resource.
      # https://linter.aip.dev/133/request-unknown-fields
      - "core::0133::request-unknown-fields"
      # This rule requires us to add `google.api.field_behavior` annotation to `Create` request message, but this annotation has no meaning to us.
      # https://linter.aip.dev/133/request-name-behavior
      - "core::0133::request-resource-behavior"
      # This rule requires us to add field `parent` to `Create` request message, but this field is not needed for us.
      # https://linter.aip.dev/133/request-parent-required
      - "core::0133::request-parent-required"
      # This rule requires that an ID must be specified when creating a resource, but we cannot specify an id.
      # https://linter.aip.dev/133/request-id-field
      - "core::0133::request-id-field"
      # The following three rules enforces that all Update standard methods have a field in the request message for the resource itself, but our update request is usually based on the update behavior of the condition.
      # https://linter.aip.dev/134/request-unknown-fields
      # https://linter.aip.dev/134/request-resource-required
      # https://linter.aip.dev/134/request-mask-field
      - "core::0134::request-unknown-fields"
      - "core::0134::request-resource-required"
      - "core::0134::request-mask-required"
      # The following two rules enforces that all Delete standard methods must have only a string name field in the request message, but our resource `Delete` request does not use name to delete.
      # https://linter.aip.dev/135/request-name-required
      # https://linter.aip.dev/135/request-unknown-fields
      - "core::0135::request-name-required"
      - "core::0135::request-unknown-fields"
      # This rule enforces that field names use common abbreviations, but abbreviations are not suitable for all situations, and sometimes the full spelling is more semantically appropriate.
      # https://linter.aip.dev/140/abbreviations
      - "core::0140::abbreviations"
      # This rule enforces that each field in the resource message must be annotated, but it doesn't make any sense to us.
      # https://linter.aip.dev/203/field-behavior-required
      - "core::0203::field-behavior-required"
      # This rule enforces that the `parent` field should include a `google.api.resource_reference` annotation, but it doesn't make any sense to us.
      # https://linter.aip.dev/132/request-parent-reference
      - "core::0132::request-parent-reference"
      # This rule enforces that the `filter` field should be a singular string, but it doesn't make any sense to us.
      # https://linter.aip.dev/132/request-field-types
      - "core::0132::request-field-types"
      # This rule enforces that time-related fields use google.protobuf.Timestamp, but some fields like start_minute represent minute offsets, not timestamps.
      # https://linter.aip.dev/142/time-field-type
      - "core::0142::time-field-type"
      # This rule scans all fields complains if it sees the suffix _name on a field.
      # https://linter.aip.dev/122/name-suffix
      - "core::0122::name-suffix"