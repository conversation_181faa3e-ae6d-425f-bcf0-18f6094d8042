// (-- api-linter: core::0133::response-message-name=disabled
//     aip.dev/not-precedent: 平铺使用 --)
// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: 平铺使用 --)
// (-- api-linter: core::0131::response-message-name=disabled
//     aip.dev/not-precedent: 平铺使用 --)
// (-- api-linter:  core::0134::response-message-name=disabled
//     aip.dev/not-precedent: 平铺使用 --)
// (-- api-linter:  core::0134::synonyms=disabled
//     aip.dev/not-precedent: 使用Set表达Upsert语义 --)

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/customer_portal/v1/customer_portal_service.proto

package customerportalpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ErrCode 定义错误码枚举
//
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
type ErrCode int32

const (
	// (-- api-linter: core::0126::unspecified=disabled
	//
	//	aip.dev/not-precedent: We need to do this because
	//	the content of the error code is automatically generated by
	//	the script and is exclusive to each service.
	//	Please do not turn off this linter for the rest of the enum --)
	//
	// 成功
	ErrCode_ERR_CODE_OK ErrCode = 0
	// 本服务自动分配的全局唯一的起始错误码
	ErrCode_ERR_CODE_UNSPECIFIED ErrCode = 113000
)

// Enum value maps for ErrCode.
var (
	ErrCode_name = map[int32]string{
		0:      "ERR_CODE_OK",
		113000: "ERR_CODE_UNSPECIFIED",
	}
	ErrCode_value = map[string]int32{
		"ERR_CODE_OK":          0,
		"ERR_CODE_UNSPECIFIED": 113000,
	}
)

func (x ErrCode) Enum() *ErrCode {
	p := new(ErrCode)
	*p = x
	return p
}

func (x ErrCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrCode) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_portal_v1_customer_portal_service_proto_enumTypes[0].Descriptor()
}

func (ErrCode) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_portal_v1_customer_portal_service_proto_enumTypes[0]
}

func (x ErrCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrCode.Descriptor instead.
func (ErrCode) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_portal_v1_customer_portal_service_proto_rawDescGZIP(), []int{0}
}

// SetOnlineBookingScriptRequest 设置OB CSS&JS 脚本请求
type SetOnlineBookingScriptRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// companyID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// businessID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staffID
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// css text
	Css *string `protobuf:"bytes,4,opt,name=css,proto3,oneof" json:"css,omitempty"`
	// js text
	Js            *string `protobuf:"bytes,5,opt,name=js,proto3,oneof" json:"js,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetOnlineBookingScriptRequest) Reset() {
	*x = SetOnlineBookingScriptRequest{}
	mi := &file_backend_proto_customer_portal_v1_customer_portal_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetOnlineBookingScriptRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetOnlineBookingScriptRequest) ProtoMessage() {}

func (x *SetOnlineBookingScriptRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_portal_v1_customer_portal_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetOnlineBookingScriptRequest.ProtoReflect.Descriptor instead.
func (*SetOnlineBookingScriptRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_portal_v1_customer_portal_service_proto_rawDescGZIP(), []int{0}
}

func (x *SetOnlineBookingScriptRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *SetOnlineBookingScriptRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *SetOnlineBookingScriptRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *SetOnlineBookingScriptRequest) GetCss() string {
	if x != nil && x.Css != nil {
		return *x.Css
	}
	return ""
}

func (x *SetOnlineBookingScriptRequest) GetJs() string {
	if x != nil && x.Js != nil {
		return *x.Js
	}
	return ""
}

// CreateOnlineBookingScriptResponse 设置OB CSS&JS 脚本响应
type SetOnlineBookingScriptResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetOnlineBookingScriptResponse) Reset() {
	*x = SetOnlineBookingScriptResponse{}
	mi := &file_backend_proto_customer_portal_v1_customer_portal_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetOnlineBookingScriptResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetOnlineBookingScriptResponse) ProtoMessage() {}

func (x *SetOnlineBookingScriptResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_portal_v1_customer_portal_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetOnlineBookingScriptResponse.ProtoReflect.Descriptor instead.
func (*SetOnlineBookingScriptResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_portal_v1_customer_portal_service_proto_rawDescGZIP(), []int{1}
}

// GetOnlineBookingScriptRequest 获取OB CSS&JS 脚本请求
type GetOnlineBookingScriptRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// businessID
	BusinessId    int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOnlineBookingScriptRequest) Reset() {
	*x = GetOnlineBookingScriptRequest{}
	mi := &file_backend_proto_customer_portal_v1_customer_portal_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOnlineBookingScriptRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnlineBookingScriptRequest) ProtoMessage() {}

func (x *GetOnlineBookingScriptRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_portal_v1_customer_portal_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnlineBookingScriptRequest.ProtoReflect.Descriptor instead.
func (*GetOnlineBookingScriptRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_portal_v1_customer_portal_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetOnlineBookingScriptRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// GetOnlineBookingScriptResponse 获取OB CSS&JS 脚本响应
type GetOnlineBookingScriptResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// css text
	Css string `protobuf:"bytes,1,opt,name=css,proto3" json:"css,omitempty"`
	// js text
	Js            string `protobuf:"bytes,2,opt,name=js,proto3" json:"js,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOnlineBookingScriptResponse) Reset() {
	*x = GetOnlineBookingScriptResponse{}
	mi := &file_backend_proto_customer_portal_v1_customer_portal_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOnlineBookingScriptResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnlineBookingScriptResponse) ProtoMessage() {}

func (x *GetOnlineBookingScriptResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_portal_v1_customer_portal_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnlineBookingScriptResponse.ProtoReflect.Descriptor instead.
func (*GetOnlineBookingScriptResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_portal_v1_customer_portal_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetOnlineBookingScriptResponse) GetCss() string {
	if x != nil {
		return x.Css
	}
	return ""
}

func (x *GetOnlineBookingScriptResponse) GetJs() string {
	if x != nil {
		return x.Js
	}
	return ""
}

var File_backend_proto_customer_portal_v1_customer_portal_service_proto protoreflect.FileDescriptor

const file_backend_proto_customer_portal_v1_customer_portal_service_proto_rawDesc = "" +
	"\n" +
	">backend/proto/customer_portal/v1/customer_portal_service.proto\x12 backend.proto.customer_portal.v1\"\xb5\x01\n" +
	"\x1dSetOnlineBookingScriptRequest\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\x12\x1f\n" +
	"\vbusiness_id\x18\x02 \x01(\x03R\n" +
	"businessId\x12\x19\n" +
	"\bstaff_id\x18\x03 \x01(\x03R\astaffId\x12\x15\n" +
	"\x03css\x18\x04 \x01(\tH\x00R\x03css\x88\x01\x01\x12\x13\n" +
	"\x02js\x18\x05 \x01(\tH\x01R\x02js\x88\x01\x01B\x06\n" +
	"\x04_cssB\x05\n" +
	"\x03_js\" \n" +
	"\x1eSetOnlineBookingScriptResponse\"@\n" +
	"\x1dGetOnlineBookingScriptRequest\x12\x1f\n" +
	"\vbusiness_id\x18\x01 \x01(\x03R\n" +
	"businessId\"B\n" +
	"\x1eGetOnlineBookingScriptResponse\x12\x10\n" +
	"\x03css\x18\x01 \x01(\tR\x03css\x12\x0e\n" +
	"\x02js\x18\x02 \x01(\tR\x02js*6\n" +
	"\aErrCode\x12\x0f\n" +
	"\vERR_CODE_OK\x10\x00\x12\x1a\n" +
	"\x14ERR_CODE_UNSPECIFIED\x10\xe8\xf2\x062\xd3\x02\n" +
	"\x15CustomerPortalService\x12\x9b\x01\n" +
	"\x16SetOnlineBookingScript\x12?.backend.proto.customer_portal.v1.SetOnlineBookingScriptRequest\<EMAIL>.customer_portal.v1.SetOnlineBookingScriptResponse\x12\x9b\x01\n" +
	"\x16GetOnlineBookingScript\x12?.backend.proto.customer_portal.v1.GetOnlineBookingScriptRequest\<EMAIL>.customer_portal.v1.GetOnlineBookingScriptResponseB\x7f\n" +
	"*com.moego.backend.proto.customer_portal.v1P\x01ZOgithub.com/MoeGolibrary/moego/backend/proto/customer_portal/v1;customerportalpbb\x06proto3"

var (
	file_backend_proto_customer_portal_v1_customer_portal_service_proto_rawDescOnce sync.Once
	file_backend_proto_customer_portal_v1_customer_portal_service_proto_rawDescData []byte
)

func file_backend_proto_customer_portal_v1_customer_portal_service_proto_rawDescGZIP() []byte {
	file_backend_proto_customer_portal_v1_customer_portal_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_customer_portal_v1_customer_portal_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_customer_portal_v1_customer_portal_service_proto_rawDesc), len(file_backend_proto_customer_portal_v1_customer_portal_service_proto_rawDesc)))
	})
	return file_backend_proto_customer_portal_v1_customer_portal_service_proto_rawDescData
}

var file_backend_proto_customer_portal_v1_customer_portal_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_backend_proto_customer_portal_v1_customer_portal_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_backend_proto_customer_portal_v1_customer_portal_service_proto_goTypes = []any{
	(ErrCode)(0),                           // 0: backend.proto.customer_portal.v1.ErrCode
	(*SetOnlineBookingScriptRequest)(nil),  // 1: backend.proto.customer_portal.v1.SetOnlineBookingScriptRequest
	(*SetOnlineBookingScriptResponse)(nil), // 2: backend.proto.customer_portal.v1.SetOnlineBookingScriptResponse
	(*GetOnlineBookingScriptRequest)(nil),  // 3: backend.proto.customer_portal.v1.GetOnlineBookingScriptRequest
	(*GetOnlineBookingScriptResponse)(nil), // 4: backend.proto.customer_portal.v1.GetOnlineBookingScriptResponse
}
var file_backend_proto_customer_portal_v1_customer_portal_service_proto_depIdxs = []int32{
	1, // 0: backend.proto.customer_portal.v1.CustomerPortalService.SetOnlineBookingScript:input_type -> backend.proto.customer_portal.v1.SetOnlineBookingScriptRequest
	3, // 1: backend.proto.customer_portal.v1.CustomerPortalService.GetOnlineBookingScript:input_type -> backend.proto.customer_portal.v1.GetOnlineBookingScriptRequest
	2, // 2: backend.proto.customer_portal.v1.CustomerPortalService.SetOnlineBookingScript:output_type -> backend.proto.customer_portal.v1.SetOnlineBookingScriptResponse
	4, // 3: backend.proto.customer_portal.v1.CustomerPortalService.GetOnlineBookingScript:output_type -> backend.proto.customer_portal.v1.GetOnlineBookingScriptResponse
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_backend_proto_customer_portal_v1_customer_portal_service_proto_init() }
func file_backend_proto_customer_portal_v1_customer_portal_service_proto_init() {
	if File_backend_proto_customer_portal_v1_customer_portal_service_proto != nil {
		return
	}
	file_backend_proto_customer_portal_v1_customer_portal_service_proto_msgTypes[0].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_customer_portal_v1_customer_portal_service_proto_rawDesc), len(file_backend_proto_customer_portal_v1_customer_portal_service_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_customer_portal_v1_customer_portal_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_customer_portal_v1_customer_portal_service_proto_depIdxs,
		EnumInfos:         file_backend_proto_customer_portal_v1_customer_portal_service_proto_enumTypes,
		MessageInfos:      file_backend_proto_customer_portal_v1_customer_portal_service_proto_msgTypes,
	}.Build()
	File_backend_proto_customer_portal_v1_customer_portal_service_proto = out.File
	file_backend_proto_customer_portal_v1_customer_portal_service_proto_goTypes = nil
	file_backend_proto_customer_portal_v1_customer_portal_service_proto_depIdxs = nil
}
