// (-- api-linter: core::0131::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0133::request-required-fields=disabled
//     aip.dev/not-precedent: 使用自定义请求消息 --)
// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: 使用自定义请求消息 --)
// (-- api-linter: core::0133::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0134::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0135::response-message-name=disabled
//     aip.dev/not-precedent: 使用空resp易于拓展 --);
// (-- api-linter: core::0216::state-field-output-only=disabled
//     aip.dev/not-precedent: 使用自定义状态字段 --)
// (-- api-linter: core::0216::synonyms=disabled
//     aip.dev/not-precedent: 对齐 moego 内部使用习惯 --)

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.8
// 	protoc        (unknown)
// source: backend/proto/customer/v2/metadata_service.proto

package customerpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	phone_number "google.golang.org/genproto/googleapis/type/phone_number"
	postaladdress "google.golang.org/genproto/googleapis/type/postaladdress"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/emptypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ErrCode 定义错误码枚举
//
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
type ErrCode int32

const (
	// (-- api-linter: core::0126::unspecified=disabled
	//
	//	aip.dev/not-precedent: We need to do this because
	//	the content of the error code is automatically generated by
	//	the script and is exclusive to each service.
	//	Please do not turn off this linter for the rest of the enum --)
	//
	// customer
	// 成功
	ErrCode_ERR_CODE_OK ErrCode = 0
	// 本服务自动分配的全局唯一的起始错误码
	ErrCode_ERR_CODE_UNSPECIFIED ErrCode = 119500
	// 客户不存在
	ErrCode_ERR_CODE_CUSTOMER_NOT_FOUND ErrCode = 119501
	// 客户已存在
	ErrCode_ERR_CODE_CUSTOMER_ALREADY_EXISTS ErrCode = 119502
	// 无效的客户ID
	ErrCode_ERR_CODE_INVALID_CUSTOMER_ID ErrCode = 119503
	// 无效的客户名称
	ErrCode_ERR_CODE_INVALID_CUSTOMER_NAME ErrCode = 119504
	// 客户已删除
	ErrCode_ERR_CODE_CUSTOMER_DELETED ErrCode = 119505
	// 创建客户失败
	ErrCode_ERR_CODE_CREATE_CUSTOMER_FAILED ErrCode = 119506
	// address
	// 地址不存在
	ErrCode_ERR_CODE_ADDRESS_NOT_FOUND ErrCode = 119510
	// 无效的地址信息
	ErrCode_ERR_CODE_INVALID_ADDRESS ErrCode = 119511
	// 超出地址数量限制
	ErrCode_ERR_CODE_ADDRESS_LIMIT_EXCEEDED ErrCode = 119512
	// 不允许重复设置主地址
	ErrCode_ERR_CODE_DUPLICATE_SET_PRIMARY_ADDRESS ErrCode = 119513
	// task
	// 任务不存在
	ErrCode_ERR_CODE_TASK_NOT_FOUND ErrCode = 119520
	// 任务已完成
	ErrCode_ERR_CODE_TASK_ALREADY_COMPLETED ErrCode = 119521
	// 无效的任务状态
	ErrCode_ERR_CODE_INVALID_TASK_STATUS ErrCode = 119522
	// source
	// Action State Name 已经存在
	ErrCode_ERR_CODE_ACTION_STATE_NAME_EXIST ErrCode = 119525
	// Life Cycle Name 已经存在
	ErrCode_ERR_CODE_LIFE_CYCLE_NAME_EXIST ErrCode = 119526
	// View Name 已经存在
	ErrCode_ERR_CODE_VIEW_NAME_EXIST ErrCode = 119527
	// Source Name 已经存在
	ErrCode_ERR_CODE_SOURCE_NAME_EXIST ErrCode = 119528
	// Tag Name 已经存在
	ErrCode_ERR_CODE_TAG_NAME_EXIST ErrCode = 119529
	// contact
	// 联系人不存在
	ErrCode_ERR_CODE_CONTACT_NOT_FOUND ErrCode = 119535
	// 联系人已存在
	ErrCode_ERR_CODE_CONTACT_ALREADY_EXISTS ErrCode = 119536
	// 无效的联系人信息
	ErrCode_ERR_CODE_INVALID_CONTACT ErrCode = 119537
	// 更新的内容与该联系方式记录的内容不符 (即contact记录email就只能更新email)
	ErrCode_ERR_CODE_UPDATE_CONTACT_CONTENT_NOT_MATCH ErrCode = 119538
	// lead
	// 线索不存在
	ErrCode_ERR_CODE_LEAD_NOT_FOUND ErrCode = 119540
	// 创建线索失败
	ErrCode_ERR_CODE_CREATE_LEAD_FAILED ErrCode = 119541
	// contact tag
	// 标签不存在
	ErrCode_ERR_CODE_CONTACT_TAG_NOT_FOUND ErrCode = 119545
	// 创建标签失败
	ErrCode_ERR_CODE_CREATE_CONTACT_TAG_FAILED ErrCode = 119546
	// custom field
	// 自定义字段不存在
	ErrCode_ERR_CODE_CUSTOM_FIELD_NOT_FOUND ErrCode = 119550
	// 创建自定义字段失败
	ErrCode_ERR_CODE_CREATE_CUSTOM_FIELD_FAILED ErrCode = 119551
	// 自定义字段已存在
	ErrCode_ERR_CODE_CUSTOM_FIELD_ALREADY_EXISTS ErrCode = 119552
	// 自定义字段已删除
	ErrCode_ERR_CODE_CUSTOM_FIELD_DELETED ErrCode = 119553
	// 自定义字段选项不存在
	ErrCode_ERR_CODE_CUSTOM_FIELD_OPTION_NOT_FOUND ErrCode = 119554
	// 自定义字段选项已删除
	ErrCode_ERR_CODE_CUSTOM_FIELD_OPTION_DELETED ErrCode = 119555
	// customer related data
	// 客户关联数据不存在
	ErrCode_ERR_CODE_CUSTOMER_RELATED_DATA_NOT_FOUND ErrCode = 119560
	// 聚合创建失败
	ErrCode_ERR_CODE_CREATE_CUSTOMER_AGGREGATE_FAILED ErrCode = 119561
)

// Enum value maps for ErrCode.
var (
	ErrCode_name = map[int32]string{
		0:      "ERR_CODE_OK",
		119500: "ERR_CODE_UNSPECIFIED",
		119501: "ERR_CODE_CUSTOMER_NOT_FOUND",
		119502: "ERR_CODE_CUSTOMER_ALREADY_EXISTS",
		119503: "ERR_CODE_INVALID_CUSTOMER_ID",
		119504: "ERR_CODE_INVALID_CUSTOMER_NAME",
		119505: "ERR_CODE_CUSTOMER_DELETED",
		119506: "ERR_CODE_CREATE_CUSTOMER_FAILED",
		119510: "ERR_CODE_ADDRESS_NOT_FOUND",
		119511: "ERR_CODE_INVALID_ADDRESS",
		119512: "ERR_CODE_ADDRESS_LIMIT_EXCEEDED",
		119513: "ERR_CODE_DUPLICATE_SET_PRIMARY_ADDRESS",
		119520: "ERR_CODE_TASK_NOT_FOUND",
		119521: "ERR_CODE_TASK_ALREADY_COMPLETED",
		119522: "ERR_CODE_INVALID_TASK_STATUS",
		119525: "ERR_CODE_ACTION_STATE_NAME_EXIST",
		119526: "ERR_CODE_LIFE_CYCLE_NAME_EXIST",
		119527: "ERR_CODE_VIEW_NAME_EXIST",
		119528: "ERR_CODE_SOURCE_NAME_EXIST",
		119529: "ERR_CODE_TAG_NAME_EXIST",
		119535: "ERR_CODE_CONTACT_NOT_FOUND",
		119536: "ERR_CODE_CONTACT_ALREADY_EXISTS",
		119537: "ERR_CODE_INVALID_CONTACT",
		119538: "ERR_CODE_UPDATE_CONTACT_CONTENT_NOT_MATCH",
		119540: "ERR_CODE_LEAD_NOT_FOUND",
		119541: "ERR_CODE_CREATE_LEAD_FAILED",
		119545: "ERR_CODE_CONTACT_TAG_NOT_FOUND",
		119546: "ERR_CODE_CREATE_CONTACT_TAG_FAILED",
		119550: "ERR_CODE_CUSTOM_FIELD_NOT_FOUND",
		119551: "ERR_CODE_CREATE_CUSTOM_FIELD_FAILED",
		119552: "ERR_CODE_CUSTOM_FIELD_ALREADY_EXISTS",
		119553: "ERR_CODE_CUSTOM_FIELD_DELETED",
		119554: "ERR_CODE_CUSTOM_FIELD_OPTION_NOT_FOUND",
		119555: "ERR_CODE_CUSTOM_FIELD_OPTION_DELETED",
		119560: "ERR_CODE_CUSTOMER_RELATED_DATA_NOT_FOUND",
		119561: "ERR_CODE_CREATE_CUSTOMER_AGGREGATE_FAILED",
	}
	ErrCode_value = map[string]int32{
		"ERR_CODE_OK":                               0,
		"ERR_CODE_UNSPECIFIED":                      119500,
		"ERR_CODE_CUSTOMER_NOT_FOUND":               119501,
		"ERR_CODE_CUSTOMER_ALREADY_EXISTS":          119502,
		"ERR_CODE_INVALID_CUSTOMER_ID":              119503,
		"ERR_CODE_INVALID_CUSTOMER_NAME":            119504,
		"ERR_CODE_CUSTOMER_DELETED":                 119505,
		"ERR_CODE_CREATE_CUSTOMER_FAILED":           119506,
		"ERR_CODE_ADDRESS_NOT_FOUND":                119510,
		"ERR_CODE_INVALID_ADDRESS":                  119511,
		"ERR_CODE_ADDRESS_LIMIT_EXCEEDED":           119512,
		"ERR_CODE_DUPLICATE_SET_PRIMARY_ADDRESS":    119513,
		"ERR_CODE_TASK_NOT_FOUND":                   119520,
		"ERR_CODE_TASK_ALREADY_COMPLETED":           119521,
		"ERR_CODE_INVALID_TASK_STATUS":              119522,
		"ERR_CODE_ACTION_STATE_NAME_EXIST":          119525,
		"ERR_CODE_LIFE_CYCLE_NAME_EXIST":            119526,
		"ERR_CODE_VIEW_NAME_EXIST":                  119527,
		"ERR_CODE_SOURCE_NAME_EXIST":                119528,
		"ERR_CODE_TAG_NAME_EXIST":                   119529,
		"ERR_CODE_CONTACT_NOT_FOUND":                119535,
		"ERR_CODE_CONTACT_ALREADY_EXISTS":           119536,
		"ERR_CODE_INVALID_CONTACT":                  119537,
		"ERR_CODE_UPDATE_CONTACT_CONTENT_NOT_MATCH": 119538,
		"ERR_CODE_LEAD_NOT_FOUND":                   119540,
		"ERR_CODE_CREATE_LEAD_FAILED":               119541,
		"ERR_CODE_CONTACT_TAG_NOT_FOUND":            119545,
		"ERR_CODE_CREATE_CONTACT_TAG_FAILED":        119546,
		"ERR_CODE_CUSTOM_FIELD_NOT_FOUND":           119550,
		"ERR_CODE_CREATE_CUSTOM_FIELD_FAILED":       119551,
		"ERR_CODE_CUSTOM_FIELD_ALREADY_EXISTS":      119552,
		"ERR_CODE_CUSTOM_FIELD_DELETED":             119553,
		"ERR_CODE_CUSTOM_FIELD_OPTION_NOT_FOUND":    119554,
		"ERR_CODE_CUSTOM_FIELD_OPTION_DELETED":      119555,
		"ERR_CODE_CUSTOMER_RELATED_DATA_NOT_FOUND":  119560,
		"ERR_CODE_CREATE_CUSTOMER_AGGREGATE_FAILED": 119561,
	}
)

func (x ErrCode) Enum() *ErrCode {
	p := new(ErrCode)
	*p = x
	return p
}

func (x ErrCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrCode) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_service_proto_enumTypes[0].Descriptor()
}

func (ErrCode) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_service_proto_enumTypes[0]
}

func (x ErrCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrCode.Descriptor instead.
func (ErrCode) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{0}
}

// sorting field
type ListCustomersRequest_Sorting_Field int32

const (
	// 默认排序
	ListCustomersRequest_Sorting_FIELD_UNSPECIFIED ListCustomersRequest_Sorting_Field = 0
	// 客户ID
	ListCustomersRequest_Sorting_ID ListCustomersRequest_Sorting_Field = 1
	// 创建时间
	ListCustomersRequest_Sorting_CREATED_TIME ListCustomersRequest_Sorting_Field = 2
	// 更新时间
	ListCustomersRequest_Sorting_UPDATED_TIME ListCustomersRequest_Sorting_Field = 3
	// 客户名称
	ListCustomersRequest_Sorting_GIVEN_NAME ListCustomersRequest_Sorting_Field = 4
	// 客户姓氏
	ListCustomersRequest_Sorting_FAMILY_NAME ListCustomersRequest_Sorting_Field = 5
)

// Enum value maps for ListCustomersRequest_Sorting_Field.
var (
	ListCustomersRequest_Sorting_Field_name = map[int32]string{
		0: "FIELD_UNSPECIFIED",
		1: "ID",
		2: "CREATED_TIME",
		3: "UPDATED_TIME",
		4: "GIVEN_NAME",
		5: "FAMILY_NAME",
	}
	ListCustomersRequest_Sorting_Field_value = map[string]int32{
		"FIELD_UNSPECIFIED": 0,
		"ID":                1,
		"CREATED_TIME":      2,
		"UPDATED_TIME":      3,
		"GIVEN_NAME":        4,
		"FAMILY_NAME":       5,
	}
)

func (x ListCustomersRequest_Sorting_Field) Enum() *ListCustomersRequest_Sorting_Field {
	p := new(ListCustomersRequest_Sorting_Field)
	*p = x
	return p
}

func (x ListCustomersRequest_Sorting_Field) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListCustomersRequest_Sorting_Field) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_service_proto_enumTypes[1].Descriptor()
}

func (ListCustomersRequest_Sorting_Field) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_service_proto_enumTypes[1]
}

func (x ListCustomersRequest_Sorting_Field) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListCustomersRequest_Sorting_Field.Descriptor instead.
func (ListCustomersRequest_Sorting_Field) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{4, 1, 0}
}

// 排序字段
type ListContactsRequest_Sorting_Field int32

const (
	// 默认排序
	ListContactsRequest_Sorting_FIELD_UNSPECIFIED ListContactsRequest_Sorting_Field = 0
	// 联系人ID
	ListContactsRequest_Sorting_ID ListContactsRequest_Sorting_Field = 1
	// 创建时间
	ListContactsRequest_Sorting_CREATED_TIME ListContactsRequest_Sorting_Field = 2
	// 更新时间
	ListContactsRequest_Sorting_UPDATED_TIME ListContactsRequest_Sorting_Field = 3
)

// Enum value maps for ListContactsRequest_Sorting_Field.
var (
	ListContactsRequest_Sorting_Field_name = map[int32]string{
		0: "FIELD_UNSPECIFIED",
		1: "ID",
		2: "CREATED_TIME",
		3: "UPDATED_TIME",
	}
	ListContactsRequest_Sorting_Field_value = map[string]int32{
		"FIELD_UNSPECIFIED": 0,
		"ID":                1,
		"CREATED_TIME":      2,
		"UPDATED_TIME":      3,
	}
)

func (x ListContactsRequest_Sorting_Field) Enum() *ListContactsRequest_Sorting_Field {
	p := new(ListContactsRequest_Sorting_Field)
	*p = x
	return p
}

func (x ListContactsRequest_Sorting_Field) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListContactsRequest_Sorting_Field) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_service_proto_enumTypes[2].Descriptor()
}

func (ListContactsRequest_Sorting_Field) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_service_proto_enumTypes[2]
}

func (x ListContactsRequest_Sorting_Field) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListContactsRequest_Sorting_Field.Descriptor instead.
func (ListContactsRequest_Sorting_Field) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{16, 1, 0}
}

// 排序字段
type ListCustomerRelatedDataRequest_Sorting_Field int32

const (
	// 默认排序
	ListCustomerRelatedDataRequest_Sorting_FIELD_UNSPECIFIED ListCustomerRelatedDataRequest_Sorting_Field = 0
	// 客户相关数据ID
	ListCustomerRelatedDataRequest_Sorting_ID ListCustomerRelatedDataRequest_Sorting_Field = 1
	// 创建时间
	ListCustomerRelatedDataRequest_Sorting_CREATED_TIME ListCustomerRelatedDataRequest_Sorting_Field = 2
	// 更新时间
	ListCustomerRelatedDataRequest_Sorting_UPDATED_TIME ListCustomerRelatedDataRequest_Sorting_Field = 3
)

// Enum value maps for ListCustomerRelatedDataRequest_Sorting_Field.
var (
	ListCustomerRelatedDataRequest_Sorting_Field_name = map[int32]string{
		0: "FIELD_UNSPECIFIED",
		1: "ID",
		2: "CREATED_TIME",
		3: "UPDATED_TIME",
	}
	ListCustomerRelatedDataRequest_Sorting_Field_value = map[string]int32{
		"FIELD_UNSPECIFIED": 0,
		"ID":                1,
		"CREATED_TIME":      2,
		"UPDATED_TIME":      3,
	}
)

func (x ListCustomerRelatedDataRequest_Sorting_Field) Enum() *ListCustomerRelatedDataRequest_Sorting_Field {
	p := new(ListCustomerRelatedDataRequest_Sorting_Field)
	*p = x
	return p
}

func (x ListCustomerRelatedDataRequest_Sorting_Field) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListCustomerRelatedDataRequest_Sorting_Field) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_service_proto_enumTypes[3].Descriptor()
}

func (ListCustomerRelatedDataRequest_Sorting_Field) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_service_proto_enumTypes[3]
}

func (x ListCustomerRelatedDataRequest_Sorting_Field) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListCustomerRelatedDataRequest_Sorting_Field.Descriptor instead.
func (ListCustomerRelatedDataRequest_Sorting_Field) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{26, 1, 0}
}

// 排序字段
type ListContactTagsRequest_Sorting_Field int32

const (
	// 默认排序
	ListContactTagsRequest_Sorting_FIELD_UNSPECIFIED ListContactTagsRequest_Sorting_Field = 0
	// 标签ID
	ListContactTagsRequest_Sorting_ID ListContactTagsRequest_Sorting_Field = 1
	// 创建时间
	ListContactTagsRequest_Sorting_CREATED_TIME ListContactTagsRequest_Sorting_Field = 2
	// 更新时间
	ListContactTagsRequest_Sorting_UPDATED_TIME ListContactTagsRequest_Sorting_Field = 3
)

// Enum value maps for ListContactTagsRequest_Sorting_Field.
var (
	ListContactTagsRequest_Sorting_Field_name = map[int32]string{
		0: "FIELD_UNSPECIFIED",
		1: "ID",
		2: "CREATED_TIME",
		3: "UPDATED_TIME",
	}
	ListContactTagsRequest_Sorting_Field_value = map[string]int32{
		"FIELD_UNSPECIFIED": 0,
		"ID":                1,
		"CREATED_TIME":      2,
		"UPDATED_TIME":      3,
	}
)

func (x ListContactTagsRequest_Sorting_Field) Enum() *ListContactTagsRequest_Sorting_Field {
	p := new(ListContactTagsRequest_Sorting_Field)
	*p = x
	return p
}

func (x ListContactTagsRequest_Sorting_Field) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListContactTagsRequest_Sorting_Field) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_service_proto_enumTypes[4].Descriptor()
}

func (ListContactTagsRequest_Sorting_Field) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_service_proto_enumTypes[4]
}

func (x ListContactTagsRequest_Sorting_Field) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListContactTagsRequest_Sorting_Field.Descriptor instead.
func (ListContactTagsRequest_Sorting_Field) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{36, 1, 0}
}

// 排序字段
type ListLeadsRequest_Sorting_Field int32

const (
	// 默认排序
	ListLeadsRequest_Sorting_FIELD_UNSPECIFIED ListLeadsRequest_Sorting_Field = 0
	// 线索ID
	ListLeadsRequest_Sorting_ID ListLeadsRequest_Sorting_Field = 1
	// 创建时间
	ListLeadsRequest_Sorting_CREATED_TIME ListLeadsRequest_Sorting_Field = 2
	// 更新时间
	ListLeadsRequest_Sorting_UPDATED_TIME ListLeadsRequest_Sorting_Field = 3
)

// Enum value maps for ListLeadsRequest_Sorting_Field.
var (
	ListLeadsRequest_Sorting_Field_name = map[int32]string{
		0: "FIELD_UNSPECIFIED",
		1: "ID",
		2: "CREATED_TIME",
		3: "UPDATED_TIME",
	}
	ListLeadsRequest_Sorting_Field_value = map[string]int32{
		"FIELD_UNSPECIFIED": 0,
		"ID":                1,
		"CREATED_TIME":      2,
		"UPDATED_TIME":      3,
	}
)

func (x ListLeadsRequest_Sorting_Field) Enum() *ListLeadsRequest_Sorting_Field {
	p := new(ListLeadsRequest_Sorting_Field)
	*p = x
	return p
}

func (x ListLeadsRequest_Sorting_Field) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListLeadsRequest_Sorting_Field) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_service_proto_enumTypes[5].Descriptor()
}

func (ListLeadsRequest_Sorting_Field) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_service_proto_enumTypes[5]
}

func (x ListLeadsRequest_Sorting_Field) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListLeadsRequest_Sorting_Field.Descriptor instead.
func (ListLeadsRequest_Sorting_Field) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{46, 1, 0}
}

// 排序字段
type ListAddressesRequest_Sorting_Field int32

const (
	// 默认排序
	ListAddressesRequest_Sorting_FIELD_UNSPECIFIED ListAddressesRequest_Sorting_Field = 0
	// 地址ID
	ListAddressesRequest_Sorting_ID ListAddressesRequest_Sorting_Field = 1
	// 创建时间
	ListAddressesRequest_Sorting_CREATED_TIME ListAddressesRequest_Sorting_Field = 2
	// 更新时间
	ListAddressesRequest_Sorting_UPDATED_TIME ListAddressesRequest_Sorting_Field = 3
)

// Enum value maps for ListAddressesRequest_Sorting_Field.
var (
	ListAddressesRequest_Sorting_Field_name = map[int32]string{
		0: "FIELD_UNSPECIFIED",
		1: "ID",
		2: "CREATED_TIME",
		3: "UPDATED_TIME",
	}
	ListAddressesRequest_Sorting_Field_value = map[string]int32{
		"FIELD_UNSPECIFIED": 0,
		"ID":                1,
		"CREATED_TIME":      2,
		"UPDATED_TIME":      3,
	}
)

func (x ListAddressesRequest_Sorting_Field) Enum() *ListAddressesRequest_Sorting_Field {
	p := new(ListAddressesRequest_Sorting_Field)
	*p = x
	return p
}

func (x ListAddressesRequest_Sorting_Field) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListAddressesRequest_Sorting_Field) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_service_proto_enumTypes[6].Descriptor()
}

func (ListAddressesRequest_Sorting_Field) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_service_proto_enumTypes[6]
}

func (x ListAddressesRequest_Sorting_Field) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListAddressesRequest_Sorting_Field.Descriptor instead.
func (ListAddressesRequest_Sorting_Field) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{56, 1, 0}
}

// 排序字段
type ListCustomFieldsRequest_Sorting_Field int32

const (
	// 默认排序
	ListCustomFieldsRequest_Sorting_FIELD_UNSPECIFIED ListCustomFieldsRequest_Sorting_Field = 0
	// 自定义字段ID
	ListCustomFieldsRequest_Sorting_ID ListCustomFieldsRequest_Sorting_Field = 1
	// 创建时间
	ListCustomFieldsRequest_Sorting_CREATED_TIME ListCustomFieldsRequest_Sorting_Field = 2
	// 更新时间
	ListCustomFieldsRequest_Sorting_UPDATED_TIME ListCustomFieldsRequest_Sorting_Field = 3
	// 显示顺序
	ListCustomFieldsRequest_Sorting_DISPLAY_ORDER ListCustomFieldsRequest_Sorting_Field = 4
)

// Enum value maps for ListCustomFieldsRequest_Sorting_Field.
var (
	ListCustomFieldsRequest_Sorting_Field_name = map[int32]string{
		0: "FIELD_UNSPECIFIED",
		1: "ID",
		2: "CREATED_TIME",
		3: "UPDATED_TIME",
		4: "DISPLAY_ORDER",
	}
	ListCustomFieldsRequest_Sorting_Field_value = map[string]int32{
		"FIELD_UNSPECIFIED": 0,
		"ID":                1,
		"CREATED_TIME":      2,
		"UPDATED_TIME":      3,
		"DISPLAY_ORDER":     4,
	}
)

func (x ListCustomFieldsRequest_Sorting_Field) Enum() *ListCustomFieldsRequest_Sorting_Field {
	p := new(ListCustomFieldsRequest_Sorting_Field)
	*p = x
	return p
}

func (x ListCustomFieldsRequest_Sorting_Field) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListCustomFieldsRequest_Sorting_Field) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_service_proto_enumTypes[7].Descriptor()
}

func (ListCustomFieldsRequest_Sorting_Field) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_service_proto_enumTypes[7]
}

func (x ListCustomFieldsRequest_Sorting_Field) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListCustomFieldsRequest_Sorting_Field.Descriptor instead.
func (ListCustomFieldsRequest_Sorting_Field) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{66, 1, 0}
}

// sorting field
type ListMetadataRequest_Sorting_Field int32

const (
	// 默认排序
	ListMetadataRequest_Sorting_FIELD_UNSPECIFIED ListMetadataRequest_Sorting_Field = 0
	// 元数据ID
	ListMetadataRequest_Sorting_ID ListMetadataRequest_Sorting_Field = 1
	// 创建时间
	ListMetadataRequest_Sorting_CREATED_TIME ListMetadataRequest_Sorting_Field = 2
	// 更新时间
	ListMetadataRequest_Sorting_UPDATED_TIME ListMetadataRequest_Sorting_Field = 3
)

// Enum value maps for ListMetadataRequest_Sorting_Field.
var (
	ListMetadataRequest_Sorting_Field_name = map[int32]string{
		0: "FIELD_UNSPECIFIED",
		1: "ID",
		2: "CREATED_TIME",
		3: "UPDATED_TIME",
	}
	ListMetadataRequest_Sorting_Field_value = map[string]int32{
		"FIELD_UNSPECIFIED": 0,
		"ID":                1,
		"CREATED_TIME":      2,
		"UPDATED_TIME":      3,
	}
)

func (x ListMetadataRequest_Sorting_Field) Enum() *ListMetadataRequest_Sorting_Field {
	p := new(ListMetadataRequest_Sorting_Field)
	*p = x
	return p
}

func (x ListMetadataRequest_Sorting_Field) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListMetadataRequest_Sorting_Field) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_service_proto_enumTypes[8].Descriptor()
}

func (ListMetadataRequest_Sorting_Field) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_service_proto_enumTypes[8]
}

func (x ListMetadataRequest_Sorting_Field) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListMetadataRequest_Sorting_Field.Descriptor instead.
func (ListMetadataRequest_Sorting_Field) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{78, 1, 0}
}

// ==================== Customer Request/Response Messages ====================
// CreateCustomerRequest 创建客户请求
type CreateCustomerRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 组织引用
	Organization *OrganizationRef `protobuf:"bytes,1,opt,name=organization,proto3" json:"organization,omitempty"`
	// 客户名称
	GivenName string `protobuf:"bytes,2,opt,name=given_name,json=givenName,proto3" json:"given_name,omitempty"`
	// 客户姓氏
	FamilyName string `protobuf:"bytes,3,opt,name=family_name,json=familyName,proto3" json:"family_name,omitempty"`
	// 客户自定义字段
	CustomFields *structpb.Struct `protobuf:"bytes,4,opt,name=custom_fields,json=customFields,proto3,oneof" json:"custom_fields,omitempty"`
	// 客户生命周期ID
	LifecycleId *int64 `protobuf:"varint,5,opt,name=lifecycle_id,json=lifecycleId,proto3,oneof" json:"lifecycle_id,omitempty"`
	// 负责人员工ID
	OwnerStaffId *int64 `protobuf:"varint,6,opt,name=owner_staff_id,json=ownerStaffId,proto3,oneof" json:"owner_staff_id,omitempty"`
	// 行动状态ID
	ActionStateId *int64 `protobuf:"varint,7,opt,name=action_state_id,json=actionStateId,proto3,oneof" json:"action_state_id,omitempty"`
	// 头像路径
	AvatarPath *string `protobuf:"bytes,8,opt,name=avatar_path,json=avatarPath,proto3,oneof" json:"avatar_path,omitempty"`
	// 推荐来源ID
	ReferralSourceId *int64 `protobuf:"varint,9,opt,name=referral_source_id,json=referralSourceId,proto3,oneof" json:"referral_source_id,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CreateCustomerRequest) Reset() {
	*x = CreateCustomerRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCustomerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerRequest) ProtoMessage() {}

func (x *CreateCustomerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerRequest.ProtoReflect.Descriptor instead.
func (*CreateCustomerRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateCustomerRequest) GetOrganization() *OrganizationRef {
	if x != nil {
		return x.Organization
	}
	return nil
}

func (x *CreateCustomerRequest) GetGivenName() string {
	if x != nil {
		return x.GivenName
	}
	return ""
}

func (x *CreateCustomerRequest) GetFamilyName() string {
	if x != nil {
		return x.FamilyName
	}
	return ""
}

func (x *CreateCustomerRequest) GetCustomFields() *structpb.Struct {
	if x != nil {
		return x.CustomFields
	}
	return nil
}

func (x *CreateCustomerRequest) GetLifecycleId() int64 {
	if x != nil && x.LifecycleId != nil {
		return *x.LifecycleId
	}
	return 0
}

func (x *CreateCustomerRequest) GetOwnerStaffId() int64 {
	if x != nil && x.OwnerStaffId != nil {
		return *x.OwnerStaffId
	}
	return 0
}

func (x *CreateCustomerRequest) GetActionStateId() int64 {
	if x != nil && x.ActionStateId != nil {
		return *x.ActionStateId
	}
	return 0
}

func (x *CreateCustomerRequest) GetAvatarPath() string {
	if x != nil && x.AvatarPath != nil {
		return *x.AvatarPath
	}
	return ""
}

func (x *CreateCustomerRequest) GetReferralSourceId() int64 {
	if x != nil && x.ReferralSourceId != nil {
		return *x.ReferralSourceId
	}
	return 0
}

// CreateCustomerResponse 创建客户响应
type CreateCustomerResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户
	Customer      *Customer `protobuf:"bytes,1,opt,name=customer,proto3" json:"customer,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCustomerResponse) Reset() {
	*x = CreateCustomerResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCustomerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerResponse) ProtoMessage() {}

func (x *CreateCustomerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerResponse.ProtoReflect.Descriptor instead.
func (*CreateCustomerResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateCustomerResponse) GetCustomer() *Customer {
	if x != nil {
		return x.Customer
	}
	return nil
}

// GetCustomerRequest 获取客户请求
type GetCustomerRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCustomerRequest) Reset() {
	*x = GetCustomerRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustomerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerRequest) ProtoMessage() {}

func (x *GetCustomerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerRequest.ProtoReflect.Descriptor instead.
func (*GetCustomerRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetCustomerRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// GetCustomerResponse 获取客户响应
type GetCustomerResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户
	Customer      *Customer `protobuf:"bytes,1,opt,name=customer,proto3" json:"customer,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCustomerResponse) Reset() {
	*x = GetCustomerResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustomerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerResponse) ProtoMessage() {}

func (x *GetCustomerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerResponse.ProtoReflect.Descriptor instead.
func (*GetCustomerResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetCustomerResponse) GetCustomer() *Customer {
	if x != nil {
		return x.Customer
	}
	return nil
}

// ListCustomersRequest 列出客户请求
type ListCustomersRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 过滤条件
	Filter *ListCustomersRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序
	Sorting *ListCustomersRequest_Sorting `protobuf:"bytes,2,opt,name=sorting,proto3" json:"sorting,omitempty"`
	// 分页大小
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 分页令牌, 为空则不分页,"","dsadsr321321dsadsa","1"
	PageToken     string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomersRequest) Reset() {
	*x = ListCustomersRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomersRequest) ProtoMessage() {}

func (x *ListCustomersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomersRequest.ProtoReflect.Descriptor instead.
func (*ListCustomersRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{4}
}

func (x *ListCustomersRequest) GetFilter() *ListCustomersRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListCustomersRequest) GetSorting() *ListCustomersRequest_Sorting {
	if x != nil {
		return x.Sorting
	}
	return nil
}

func (x *ListCustomersRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListCustomersRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// ListCustomersResponse 列出客户响应
type ListCustomersResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户列表
	Customers []*Customer `protobuf:"bytes,1,rep,name=customers,proto3" json:"customers,omitempty"`
	// 下一页令牌
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// 总数量, 页码分页会返回
	TotalSize     *int64 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3,oneof" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomersResponse) Reset() {
	*x = ListCustomersResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomersResponse) ProtoMessage() {}

func (x *ListCustomersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomersResponse.ProtoReflect.Descriptor instead.
func (*ListCustomersResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{5}
}

func (x *ListCustomersResponse) GetCustomers() []*Customer {
	if x != nil {
		return x.Customers
	}
	return nil
}

func (x *ListCustomersResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListCustomersResponse) GetTotalSize() int64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

// UpdateCustomerRequest 更新客户请求
type UpdateCustomerRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 客户名称
	GivenName *string `protobuf:"bytes,2,opt,name=given_name,json=givenName,proto3,oneof" json:"given_name,omitempty"`
	// 客户姓氏
	FamilyName *string `protobuf:"bytes,3,opt,name=family_name,json=familyName,proto3,oneof" json:"family_name,omitempty"`
	// 客户自定义字段
	CustomFields *structpb.Struct `protobuf:"bytes,4,opt,name=custom_fields,json=customFields,proto3,oneof" json:"custom_fields,omitempty"`
	// 客户生命周期ID
	LifecycleId *int64 `protobuf:"varint,5,opt,name=lifecycle_id,json=lifecycleId,proto3,oneof" json:"lifecycle_id,omitempty"`
	// 负责人员工ID
	OwnerStaffId *int64 `protobuf:"varint,6,opt,name=owner_staff_id,json=ownerStaffId,proto3,oneof" json:"owner_staff_id,omitempty"`
	// 行动状态ID
	ActionStateId *int64 `protobuf:"varint,7,opt,name=action_state_id,json=actionStateId,proto3,oneof" json:"action_state_id,omitempty"`
	// 头像路径
	AvatarPath *string `protobuf:"bytes,8,opt,name=avatar_path,json=avatarPath,proto3,oneof" json:"avatar_path,omitempty"`
	// 推荐来源ID
	ReferralSourceId *int64 `protobuf:"varint,9,opt,name=referral_source_id,json=referralSourceId,proto3,oneof" json:"referral_source_id,omitempty"`
	// state
	State         *Customer_State `protobuf:"varint,10,opt,name=state,proto3,enum=backend.proto.customer.v2.Customer_State,oneof" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCustomerRequest) Reset() {
	*x = UpdateCustomerRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCustomerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerRequest) ProtoMessage() {}

func (x *UpdateCustomerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerRequest.ProtoReflect.Descriptor instead.
func (*UpdateCustomerRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateCustomerRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateCustomerRequest) GetGivenName() string {
	if x != nil && x.GivenName != nil {
		return *x.GivenName
	}
	return ""
}

func (x *UpdateCustomerRequest) GetFamilyName() string {
	if x != nil && x.FamilyName != nil {
		return *x.FamilyName
	}
	return ""
}

func (x *UpdateCustomerRequest) GetCustomFields() *structpb.Struct {
	if x != nil {
		return x.CustomFields
	}
	return nil
}

func (x *UpdateCustomerRequest) GetLifecycleId() int64 {
	if x != nil && x.LifecycleId != nil {
		return *x.LifecycleId
	}
	return 0
}

func (x *UpdateCustomerRequest) GetOwnerStaffId() int64 {
	if x != nil && x.OwnerStaffId != nil {
		return *x.OwnerStaffId
	}
	return 0
}

func (x *UpdateCustomerRequest) GetActionStateId() int64 {
	if x != nil && x.ActionStateId != nil {
		return *x.ActionStateId
	}
	return 0
}

func (x *UpdateCustomerRequest) GetAvatarPath() string {
	if x != nil && x.AvatarPath != nil {
		return *x.AvatarPath
	}
	return ""
}

func (x *UpdateCustomerRequest) GetReferralSourceId() int64 {
	if x != nil && x.ReferralSourceId != nil {
		return *x.ReferralSourceId
	}
	return 0
}

func (x *UpdateCustomerRequest) GetState() Customer_State {
	if x != nil && x.State != nil {
		return *x.State
	}
	return Customer_STATE_UNSPECIFIED
}

// UpdateCustomerResponse 更新客户响应
type UpdateCustomerResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户
	Customer      *Customer `protobuf:"bytes,1,opt,name=customer,proto3" json:"customer,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCustomerResponse) Reset() {
	*x = UpdateCustomerResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCustomerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerResponse) ProtoMessage() {}

func (x *UpdateCustomerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerResponse.ProtoReflect.Descriptor instead.
func (*UpdateCustomerResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateCustomerResponse) GetCustomer() *Customer {
	if x != nil {
		return x.Customer
	}
	return nil
}

// DeleteCustomerRequest 删除客户请求
type DeleteCustomerRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCustomerRequest) Reset() {
	*x = DeleteCustomerRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCustomerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCustomerRequest) ProtoMessage() {}

func (x *DeleteCustomerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCustomerRequest.ProtoReflect.Descriptor instead.
func (*DeleteCustomerRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteCustomerRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// DeleteCustomerResponse 删除客户响应
type DeleteCustomerResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCustomerResponse) Reset() {
	*x = DeleteCustomerResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCustomerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCustomerResponse) ProtoMessage() {}

func (x *DeleteCustomerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCustomerResponse.ProtoReflect.Descriptor instead.
func (*DeleteCustomerResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{9}
}

// CreateCustomerAggregateRequest 聚合创建客户及相关实体请求
type CreateCustomerAggregateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 组织引用
	Organization *OrganizationRef `protobuf:"bytes,1,opt,name=organization,proto3" json:"organization,omitempty"`
	// 客户信息
	Customer *Customer `protobuf:"bytes,2,opt,name=customer,proto3" json:"customer,omitempty"`
	// 联系人列表
	Contacts []*Contact `protobuf:"bytes,3,rep,name=contacts,proto3" json:"contacts,omitempty"`
	// 地址列表
	Addresses []*Address `protobuf:"bytes,4,rep,name=addresses,proto3" json:"addresses,omitempty"`
	// 客户相关数据
	CustomerRelatedData *CustomerRelatedData `protobuf:"bytes,5,opt,name=customer_related_data,json=customerRelatedData,proto3,oneof" json:"customer_related_data,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *CreateCustomerAggregateRequest) Reset() {
	*x = CreateCustomerAggregateRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCustomerAggregateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerAggregateRequest) ProtoMessage() {}

func (x *CreateCustomerAggregateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerAggregateRequest.ProtoReflect.Descriptor instead.
func (*CreateCustomerAggregateRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{10}
}

func (x *CreateCustomerAggregateRequest) GetOrganization() *OrganizationRef {
	if x != nil {
		return x.Organization
	}
	return nil
}

func (x *CreateCustomerAggregateRequest) GetCustomer() *Customer {
	if x != nil {
		return x.Customer
	}
	return nil
}

func (x *CreateCustomerAggregateRequest) GetContacts() []*Contact {
	if x != nil {
		return x.Contacts
	}
	return nil
}

func (x *CreateCustomerAggregateRequest) GetAddresses() []*Address {
	if x != nil {
		return x.Addresses
	}
	return nil
}

func (x *CreateCustomerAggregateRequest) GetCustomerRelatedData() *CustomerRelatedData {
	if x != nil {
		return x.CustomerRelatedData
	}
	return nil
}

// CreateCustomerAggregateResponse 聚合创建客户及相关实体响应
type CreateCustomerAggregateResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户聚合
	CustomerAggregate *CustomerAggregate `protobuf:"bytes,1,opt,name=customer_aggregate,json=customerAggregate,proto3" json:"customer_aggregate,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CreateCustomerAggregateResponse) Reset() {
	*x = CreateCustomerAggregateResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCustomerAggregateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerAggregateResponse) ProtoMessage() {}

func (x *CreateCustomerAggregateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerAggregateResponse.ProtoReflect.Descriptor instead.
func (*CreateCustomerAggregateResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{11}
}

func (x *CreateCustomerAggregateResponse) GetCustomerAggregate() *CustomerAggregate {
	if x != nil {
		return x.CustomerAggregate
	}
	return nil
}

// ==================== Contact Request/Response Messages ====================
// CreateContactRequest 创建联系人请求
type CreateContactRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 所属客户ID
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 名字
	GivenName string `protobuf:"bytes,2,opt,name=given_name,json=givenName,proto3" json:"given_name,omitempty"`
	// 姓氏
	FamilyName string `protobuf:"bytes,3,opt,name=family_name,json=familyName,proto3" json:"family_name,omitempty"`
	// 邮箱
	Email *string `protobuf:"bytes,4,opt,name=email,proto3,oneof" json:"email,omitempty"`
	// 手机号
	Phone *phone_number.PhoneNumber `protobuf:"bytes,5,opt,name=phone,proto3,oneof" json:"phone,omitempty"`
	// 是否是自己创建的
	IsSelf bool `protobuf:"varint,6,opt,name=is_self,json=isSelf,proto3" json:"is_self,omitempty"`
	// 标签列表
	Tags []*ContactTag `protobuf:"bytes,7,rep,name=tags,proto3" json:"tags,omitempty"`
	// 备注
	Note          *string `protobuf:"bytes,8,opt,name=note,proto3,oneof" json:"note,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateContactRequest) Reset() {
	*x = CreateContactRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateContactRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateContactRequest) ProtoMessage() {}

func (x *CreateContactRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateContactRequest.ProtoReflect.Descriptor instead.
func (*CreateContactRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{12}
}

func (x *CreateContactRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CreateContactRequest) GetGivenName() string {
	if x != nil {
		return x.GivenName
	}
	return ""
}

func (x *CreateContactRequest) GetFamilyName() string {
	if x != nil {
		return x.FamilyName
	}
	return ""
}

func (x *CreateContactRequest) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *CreateContactRequest) GetPhone() *phone_number.PhoneNumber {
	if x != nil {
		return x.Phone
	}
	return nil
}

func (x *CreateContactRequest) GetIsSelf() bool {
	if x != nil {
		return x.IsSelf
	}
	return false
}

func (x *CreateContactRequest) GetTags() []*ContactTag {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *CreateContactRequest) GetNote() string {
	if x != nil && x.Note != nil {
		return *x.Note
	}
	return ""
}

// CreateContactResponse 创建联系人响应
type CreateContactResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 联系人
	Contact       *Contact `protobuf:"bytes,1,opt,name=contact,proto3" json:"contact,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateContactResponse) Reset() {
	*x = CreateContactResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateContactResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateContactResponse) ProtoMessage() {}

func (x *CreateContactResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateContactResponse.ProtoReflect.Descriptor instead.
func (*CreateContactResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{13}
}

func (x *CreateContactResponse) GetContact() *Contact {
	if x != nil {
		return x.Contact
	}
	return nil
}

// GetContactRequest 获取联系人请求
type GetContactRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 联系人ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetContactRequest) Reset() {
	*x = GetContactRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetContactRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetContactRequest) ProtoMessage() {}

func (x *GetContactRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetContactRequest.ProtoReflect.Descriptor instead.
func (*GetContactRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{14}
}

func (x *GetContactRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// GetContactResponse 获取联系人响应
type GetContactResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 联系人
	Contact       *Contact `protobuf:"bytes,1,opt,name=contact,proto3" json:"contact,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetContactResponse) Reset() {
	*x = GetContactResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetContactResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetContactResponse) ProtoMessage() {}

func (x *GetContactResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetContactResponse.ProtoReflect.Descriptor instead.
func (*GetContactResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetContactResponse) GetContact() *Contact {
	if x != nil {
		return x.Contact
	}
	return nil
}

// ListContactsRequest 列出联系人请求
type ListContactsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 过滤条件
	Filter *ListContactsRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序
	Sorting *ListContactsRequest_Sorting `protobuf:"bytes,2,opt,name=sorting,proto3" json:"sorting,omitempty"`
	// 分页大小
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 分页令牌
	PageToken     string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListContactsRequest) Reset() {
	*x = ListContactsRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListContactsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListContactsRequest) ProtoMessage() {}

func (x *ListContactsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListContactsRequest.ProtoReflect.Descriptor instead.
func (*ListContactsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{16}
}

func (x *ListContactsRequest) GetFilter() *ListContactsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListContactsRequest) GetSorting() *ListContactsRequest_Sorting {
	if x != nil {
		return x.Sorting
	}
	return nil
}

func (x *ListContactsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListContactsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// ListContactsResponse 列出联系人响应
type ListContactsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 联系人列表
	Contacts []*Contact `protobuf:"bytes,1,rep,name=contacts,proto3" json:"contacts,omitempty"`
	// 下一页令牌
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// 总数量
	TotalSize     *int64 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3,oneof" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListContactsResponse) Reset() {
	*x = ListContactsResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListContactsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListContactsResponse) ProtoMessage() {}

func (x *ListContactsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListContactsResponse.ProtoReflect.Descriptor instead.
func (*ListContactsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{17}
}

func (x *ListContactsResponse) GetContacts() []*Contact {
	if x != nil {
		return x.Contacts
	}
	return nil
}

func (x *ListContactsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListContactsResponse) GetTotalSize() int64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

// UpdateContactRequest 更新联系人请求
type UpdateContactRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 名字
	GivenName *string `protobuf:"bytes,2,opt,name=given_name,json=givenName,proto3,oneof" json:"given_name,omitempty"`
	// 姓氏
	FamilyName *string `protobuf:"bytes,3,opt,name=family_name,json=familyName,proto3,oneof" json:"family_name,omitempty"`
	// 邮箱
	Email *string `protobuf:"bytes,4,opt,name=email,proto3,oneof" json:"email,omitempty"`
	// 手机号
	Phone *phone_number.PhoneNumber `protobuf:"bytes,5,opt,name=phone,proto3,oneof" json:"phone,omitempty"`
	// 标签列表
	TagIds []int64 `protobuf:"varint,6,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
	// 备注
	Note *string `protobuf:"bytes,7,opt,name=note,proto3,oneof" json:"note,omitempty"`
	// state
	State         *Contact_State `protobuf:"varint,8,opt,name=state,proto3,enum=backend.proto.customer.v2.Contact_State,oneof" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateContactRequest) Reset() {
	*x = UpdateContactRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateContactRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateContactRequest) ProtoMessage() {}

func (x *UpdateContactRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateContactRequest.ProtoReflect.Descriptor instead.
func (*UpdateContactRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{18}
}

func (x *UpdateContactRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateContactRequest) GetGivenName() string {
	if x != nil && x.GivenName != nil {
		return *x.GivenName
	}
	return ""
}

func (x *UpdateContactRequest) GetFamilyName() string {
	if x != nil && x.FamilyName != nil {
		return *x.FamilyName
	}
	return ""
}

func (x *UpdateContactRequest) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *UpdateContactRequest) GetPhone() *phone_number.PhoneNumber {
	if x != nil {
		return x.Phone
	}
	return nil
}

func (x *UpdateContactRequest) GetTagIds() []int64 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

func (x *UpdateContactRequest) GetNote() string {
	if x != nil && x.Note != nil {
		return *x.Note
	}
	return ""
}

func (x *UpdateContactRequest) GetState() Contact_State {
	if x != nil && x.State != nil {
		return *x.State
	}
	return Contact_STATE_UNSPECIFIED
}

// UpdateContactResponse 更新联系人响应
type UpdateContactResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 联系人
	Contact       *Contact `protobuf:"bytes,1,opt,name=contact,proto3" json:"contact,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateContactResponse) Reset() {
	*x = UpdateContactResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateContactResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateContactResponse) ProtoMessage() {}

func (x *UpdateContactResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateContactResponse.ProtoReflect.Descriptor instead.
func (*UpdateContactResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{19}
}

func (x *UpdateContactResponse) GetContact() *Contact {
	if x != nil {
		return x.Contact
	}
	return nil
}

// DeleteContactRequest 删除联系人请求
type DeleteContactRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 联系人ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteContactRequest) Reset() {
	*x = DeleteContactRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteContactRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteContactRequest) ProtoMessage() {}

func (x *DeleteContactRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteContactRequest.ProtoReflect.Descriptor instead.
func (*DeleteContactRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{20}
}

func (x *DeleteContactRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// DeleteContactResponse 删除联系人响应
type DeleteContactResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteContactResponse) Reset() {
	*x = DeleteContactResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteContactResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteContactResponse) ProtoMessage() {}

func (x *DeleteContactResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteContactResponse.ProtoReflect.Descriptor instead.
func (*DeleteContactResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{21}
}

// ==================== CustomerRelatedData Request/Response Messages ====================
// CreateCustomerRelatedDataRequest 创建客户相关数据请求
type CreateCustomerRelatedDataRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 关联的客户ID
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 商家ID
	PreferredBusinessId int64 `protobuf:"varint,2,opt,name=preferred_business_id,json=preferredBusinessId,proto3" json:"preferred_business_id,omitempty"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 客户颜色
	ClientColor string `protobuf:"bytes,4,opt,name=client_color,json=clientColor,proto3" json:"client_color,omitempty"`
	// 是否屏蔽消息 (0-正常 1-屏蔽)
	IsBlockMessage int32 `protobuf:"varint,5,opt,name=is_block_message,json=isBlockMessage,proto3" json:"is_block_message,omitempty"`
	// 是否屏蔽在线预约 (0-正常 1-屏蔽)
	IsBlockOnlineBooking int32 `protobuf:"varint,6,opt,name=is_block_online_booking,json=isBlockOnlineBooking,proto3" json:"is_block_online_booking,omitempty"`
	// 登录邮箱
	LoginEmail string `protobuf:"bytes,7,opt,name=login_email,json=loginEmail,proto3" json:"login_email,omitempty"`
	// 推荐来源ID
	ReferralSourceId int32 `protobuf:"varint,8,opt,name=referral_source_id,json=referralSourceId,proto3" json:"referral_source_id,omitempty"`
	// 推荐来源描述
	ReferralSourceDesc string `protobuf:"bytes,9,opt,name=referral_source_desc,json=referralSourceDesc,proto3" json:"referral_source_desc,omitempty"`
	// 发送自动邮件 (0-不接受 1-接受)
	SendAutoEmail int32 `protobuf:"varint,10,opt,name=send_auto_email,json=sendAutoEmail,proto3" json:"send_auto_email,omitempty"`
	// 发送自动短信 (0-不接受 1-接受)
	SendAutoMessage int32 `protobuf:"varint,11,opt,name=send_auto_message,json=sendAutoMessage,proto3" json:"send_auto_message,omitempty"`
	// 发送app自动短信 (0-不接受 1-接受)
	SendAppAutoMessage int32 `protobuf:"varint,12,opt,name=send_app_auto_message,json=sendAppAutoMessage,proto3" json:"send_app_auto_message,omitempty"`
	// 未确认提醒方式 (1-text 2-email 3-phone call)
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: this is not a preposition --)
	UnconfirmedReminderBy []int32 `protobuf:"varint,13,rep,packed,name=unconfirmed_reminder_by,json=unconfirmedReminderBy,proto3" json:"unconfirmed_reminder_by,omitempty"`
	// 首选美容师ID
	PreferredGroomerId int32 `protobuf:"varint,14,opt,name=preferred_groomer_id,json=preferredGroomerId,proto3" json:"preferred_groomer_id,omitempty"`
	// 首选频率天数
	PreferredFrequencyDay int32 `protobuf:"varint,15,opt,name=preferred_frequency_day,json=preferredFrequencyDay,proto3" json:"preferred_frequency_day,omitempty"`
	// 首选频率类型 (0-by days 1-by weeks)
	PreferredFrequencyType int32 `protobuf:"varint,16,opt,name=preferred_frequency_type,json=preferredFrequencyType,proto3" json:"preferred_frequency_type,omitempty"`
	// 最后服务时间
	// (-- api-linter: core::0142::time-field-type=disabled
	//
	//	aip.dev/not-precedent: this is json string not a time --)
	LastServiceTime string `protobuf:"bytes,17,opt,name=last_service_time,json=lastServiceTime,proto3" json:"last_service_time,omitempty"`
	// 来源
	Source string `protobuf:"bytes,18,opt,name=source,proto3" json:"source,omitempty"`
	// 外部ID
	ExternalId string `protobuf:"bytes,19,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
	// 创建人
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: this is not a preposition --)
	CreateBy int32 `protobuf:"varint,20,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
	// 更新人
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: this is not a preposition --)
	UpdateBy int32 `protobuf:"varint,21,opt,name=update_by,json=updateBy,proto3" json:"update_by,omitempty"`
	// 是否重复 (1-true 2-false)
	IsRecurring *int32 `protobuf:"varint,22,opt,name=is_recurring,json=isRecurring,proto3,oneof" json:"is_recurring,omitempty"`
	// 分享预约状态 (0-all 1-unconfirm 2-confirm 4-finished)
	ShareApptStatus int32 `protobuf:"varint,23,opt,name=share_appt_status,json=shareApptStatus,proto3" json:"share_appt_status,omitempty"`
	// 分享范围类型 (0-all 1-in x days 2-next x appointment 3-manually apptids)
	ShareRangeType int32 `protobuf:"varint,24,opt,name=share_range_type,json=shareRangeType,proto3" json:"share_range_type,omitempty"`
	// 分享范围值
	ShareRangeValue int32 `protobuf:"varint,25,opt,name=share_range_value,json=shareRangeValue,proto3" json:"share_range_value,omitempty"`
	// 分享预约JSON
	ShareApptJson *string `protobuf:"bytes,26,opt,name=share_appt_json,json=shareApptJson,proto3,oneof" json:"share_appt_json,omitempty"`
	// 首选天 (JSON数组格式)
	PreferredDay string `protobuf:"bytes,27,opt,name=preferred_day,json=preferredDay,proto3" json:"preferred_day,omitempty"`
	// 首选时间 (JSON数组格式)
	// (-- api-linter: core::0142::time-field-type=disabled
	//
	//	aip.dev/not-precedent: this is json string not a time --)
	PreferredTime string `protobuf:"bytes,28,opt,name=preferred_time,json=preferredTime,proto3" json:"preferred_time,omitempty"`
	// 账户ID
	AccountId int64 `protobuf:"varint,29,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// 客户编码
	CustomerCode string `protobuf:"bytes,30,opt,name=customer_code,json=customerCode,proto3" json:"customer_code,omitempty"`
	// 是否退订
	IsUnsubscribed bool `protobuf:"varint,31,opt,name=is_unsubscribed,json=isUnsubscribed,proto3" json:"is_unsubscribed,omitempty"`
	// 生日
	// (-- api-linter: core::0142::time-field-names=disabled
	//
	//	aip.dev/not-precedent: this is not a time field --)
	Birthday *timestamppb.Timestamp `protobuf:"bytes,32,opt,name=birthday,proto3,oneof" json:"birthday,omitempty"`
	// 行动状态
	ActionState string `protobuf:"bytes,33,opt,name=action_state,json=actionState,proto3" json:"action_state,omitempty"`
	// 自定义生命周期ID
	CustomizeLifeCycleId int64 `protobuf:"varint,34,opt,name=customize_life_cycle_id,json=customizeLifeCycleId,proto3" json:"customize_life_cycle_id,omitempty"`
	// 自定义行动状态ID
	CustomizeActionStateId int64 `protobuf:"varint,35,opt,name=customize_action_state_id,json=customizeActionStateId,proto3" json:"customize_action_state_id,omitempty"`
	// preferred tip configuration
	// 首选小费是否启用 (0-关闭 1-开启)
	PreferredTipEnable int32 `protobuf:"varint,36,opt,name=preferred_tip_enable,json=preferredTipEnable,proto3" json:"preferred_tip_enable,omitempty"`
	// 首选小费类型 (0-按金额 1-按百分比)
	PreferredTipType int32 `protobuf:"varint,37,opt,name=preferred_tip_type,json=preferredTipType,proto3" json:"preferred_tip_type,omitempty"`
	// 按金额小费
	PreferredTipAmount float64 `protobuf:"fixed64,38,opt,name=preferred_tip_amount,json=preferredTipAmount,proto3" json:"preferred_tip_amount,omitempty"`
	// 按百分比小费 [1,100]
	PreferredTipPercentage int32 `protobuf:"varint,39,opt,name=preferred_tip_percentage,json=preferredTipPercentage,proto3" json:"preferred_tip_percentage,omitempty"`
	// default preferred frequency configuration
	// 默认首选频率类型 (1-grooming)
	DefaultPreferredFrequencyType int32 `protobuf:"varint,40,opt,name=default_preferred_frequency_type,json=defaultPreferredFrequencyType,proto3" json:"default_preferred_frequency_type,omitempty"`
	// 默认首选频率周期 (1-day, 2-week, 4-month)
	DefaultPreferredCalendarPeriod int32 `protobuf:"varint,41,opt,name=default_preferred_calendar_period,json=defaultPreferredCalendarPeriod,proto3" json:"default_preferred_calendar_period,omitempty"`
	// 默认首选频率值
	DefaultPreferredFrequencyValue int32 `protobuf:"varint,42,opt,name=default_preferred_frequency_value,json=defaultPreferredFrequencyValue,proto3" json:"default_preferred_frequency_value,omitempty"`
	unknownFields                  protoimpl.UnknownFields
	sizeCache                      protoimpl.SizeCache
}

func (x *CreateCustomerRelatedDataRequest) Reset() {
	*x = CreateCustomerRelatedDataRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCustomerRelatedDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerRelatedDataRequest) ProtoMessage() {}

func (x *CreateCustomerRelatedDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerRelatedDataRequest.ProtoReflect.Descriptor instead.
func (*CreateCustomerRelatedDataRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{22}
}

func (x *CreateCustomerRelatedDataRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CreateCustomerRelatedDataRequest) GetPreferredBusinessId() int64 {
	if x != nil {
		return x.PreferredBusinessId
	}
	return 0
}

func (x *CreateCustomerRelatedDataRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateCustomerRelatedDataRequest) GetClientColor() string {
	if x != nil {
		return x.ClientColor
	}
	return ""
}

func (x *CreateCustomerRelatedDataRequest) GetIsBlockMessage() int32 {
	if x != nil {
		return x.IsBlockMessage
	}
	return 0
}

func (x *CreateCustomerRelatedDataRequest) GetIsBlockOnlineBooking() int32 {
	if x != nil {
		return x.IsBlockOnlineBooking
	}
	return 0
}

func (x *CreateCustomerRelatedDataRequest) GetLoginEmail() string {
	if x != nil {
		return x.LoginEmail
	}
	return ""
}

func (x *CreateCustomerRelatedDataRequest) GetReferralSourceId() int32 {
	if x != nil {
		return x.ReferralSourceId
	}
	return 0
}

func (x *CreateCustomerRelatedDataRequest) GetReferralSourceDesc() string {
	if x != nil {
		return x.ReferralSourceDesc
	}
	return ""
}

func (x *CreateCustomerRelatedDataRequest) GetSendAutoEmail() int32 {
	if x != nil {
		return x.SendAutoEmail
	}
	return 0
}

func (x *CreateCustomerRelatedDataRequest) GetSendAutoMessage() int32 {
	if x != nil {
		return x.SendAutoMessage
	}
	return 0
}

func (x *CreateCustomerRelatedDataRequest) GetSendAppAutoMessage() int32 {
	if x != nil {
		return x.SendAppAutoMessage
	}
	return 0
}

func (x *CreateCustomerRelatedDataRequest) GetUnconfirmedReminderBy() []int32 {
	if x != nil {
		return x.UnconfirmedReminderBy
	}
	return nil
}

func (x *CreateCustomerRelatedDataRequest) GetPreferredGroomerId() int32 {
	if x != nil {
		return x.PreferredGroomerId
	}
	return 0
}

func (x *CreateCustomerRelatedDataRequest) GetPreferredFrequencyDay() int32 {
	if x != nil {
		return x.PreferredFrequencyDay
	}
	return 0
}

func (x *CreateCustomerRelatedDataRequest) GetPreferredFrequencyType() int32 {
	if x != nil {
		return x.PreferredFrequencyType
	}
	return 0
}

func (x *CreateCustomerRelatedDataRequest) GetLastServiceTime() string {
	if x != nil {
		return x.LastServiceTime
	}
	return ""
}

func (x *CreateCustomerRelatedDataRequest) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *CreateCustomerRelatedDataRequest) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

func (x *CreateCustomerRelatedDataRequest) GetCreateBy() int32 {
	if x != nil {
		return x.CreateBy
	}
	return 0
}

func (x *CreateCustomerRelatedDataRequest) GetUpdateBy() int32 {
	if x != nil {
		return x.UpdateBy
	}
	return 0
}

func (x *CreateCustomerRelatedDataRequest) GetIsRecurring() int32 {
	if x != nil && x.IsRecurring != nil {
		return *x.IsRecurring
	}
	return 0
}

func (x *CreateCustomerRelatedDataRequest) GetShareApptStatus() int32 {
	if x != nil {
		return x.ShareApptStatus
	}
	return 0
}

func (x *CreateCustomerRelatedDataRequest) GetShareRangeType() int32 {
	if x != nil {
		return x.ShareRangeType
	}
	return 0
}

func (x *CreateCustomerRelatedDataRequest) GetShareRangeValue() int32 {
	if x != nil {
		return x.ShareRangeValue
	}
	return 0
}

func (x *CreateCustomerRelatedDataRequest) GetShareApptJson() string {
	if x != nil && x.ShareApptJson != nil {
		return *x.ShareApptJson
	}
	return ""
}

func (x *CreateCustomerRelatedDataRequest) GetPreferredDay() string {
	if x != nil {
		return x.PreferredDay
	}
	return ""
}

func (x *CreateCustomerRelatedDataRequest) GetPreferredTime() string {
	if x != nil {
		return x.PreferredTime
	}
	return ""
}

func (x *CreateCustomerRelatedDataRequest) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *CreateCustomerRelatedDataRequest) GetCustomerCode() string {
	if x != nil {
		return x.CustomerCode
	}
	return ""
}

func (x *CreateCustomerRelatedDataRequest) GetIsUnsubscribed() bool {
	if x != nil {
		return x.IsUnsubscribed
	}
	return false
}

func (x *CreateCustomerRelatedDataRequest) GetBirthday() *timestamppb.Timestamp {
	if x != nil {
		return x.Birthday
	}
	return nil
}

func (x *CreateCustomerRelatedDataRequest) GetActionState() string {
	if x != nil {
		return x.ActionState
	}
	return ""
}

func (x *CreateCustomerRelatedDataRequest) GetCustomizeLifeCycleId() int64 {
	if x != nil {
		return x.CustomizeLifeCycleId
	}
	return 0
}

func (x *CreateCustomerRelatedDataRequest) GetCustomizeActionStateId() int64 {
	if x != nil {
		return x.CustomizeActionStateId
	}
	return 0
}

func (x *CreateCustomerRelatedDataRequest) GetPreferredTipEnable() int32 {
	if x != nil {
		return x.PreferredTipEnable
	}
	return 0
}

func (x *CreateCustomerRelatedDataRequest) GetPreferredTipType() int32 {
	if x != nil {
		return x.PreferredTipType
	}
	return 0
}

func (x *CreateCustomerRelatedDataRequest) GetPreferredTipAmount() float64 {
	if x != nil {
		return x.PreferredTipAmount
	}
	return 0
}

func (x *CreateCustomerRelatedDataRequest) GetPreferredTipPercentage() int32 {
	if x != nil {
		return x.PreferredTipPercentage
	}
	return 0
}

func (x *CreateCustomerRelatedDataRequest) GetDefaultPreferredFrequencyType() int32 {
	if x != nil {
		return x.DefaultPreferredFrequencyType
	}
	return 0
}

func (x *CreateCustomerRelatedDataRequest) GetDefaultPreferredCalendarPeriod() int32 {
	if x != nil {
		return x.DefaultPreferredCalendarPeriod
	}
	return 0
}

func (x *CreateCustomerRelatedDataRequest) GetDefaultPreferredFrequencyValue() int32 {
	if x != nil {
		return x.DefaultPreferredFrequencyValue
	}
	return 0
}

// CreateCustomerRelatedDataResponse 创建客户相关数据响应
type CreateCustomerRelatedDataResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户相关数据
	CustomerRelatedData *CustomerRelatedData `protobuf:"bytes,1,opt,name=customer_related_data,json=customerRelatedData,proto3" json:"customer_related_data,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *CreateCustomerRelatedDataResponse) Reset() {
	*x = CreateCustomerRelatedDataResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCustomerRelatedDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerRelatedDataResponse) ProtoMessage() {}

func (x *CreateCustomerRelatedDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerRelatedDataResponse.ProtoReflect.Descriptor instead.
func (*CreateCustomerRelatedDataResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{23}
}

func (x *CreateCustomerRelatedDataResponse) GetCustomerRelatedData() *CustomerRelatedData {
	if x != nil {
		return x.CustomerRelatedData
	}
	return nil
}

// GetCustomerRelatedDataRequest 获取客户相关数据请求
type GetCustomerRelatedDataRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户相关数据ID
	CustomerId    int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCustomerRelatedDataRequest) Reset() {
	*x = GetCustomerRelatedDataRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustomerRelatedDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerRelatedDataRequest) ProtoMessage() {}

func (x *GetCustomerRelatedDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerRelatedDataRequest.ProtoReflect.Descriptor instead.
func (*GetCustomerRelatedDataRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{24}
}

func (x *GetCustomerRelatedDataRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

// GetCustomerRelatedDataResponse 获取客户相关数据响应
type GetCustomerRelatedDataResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户相关数据
	CustomerRelatedData *CustomerRelatedData `protobuf:"bytes,1,opt,name=customer_related_data,json=customerRelatedData,proto3" json:"customer_related_data,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *GetCustomerRelatedDataResponse) Reset() {
	*x = GetCustomerRelatedDataResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustomerRelatedDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerRelatedDataResponse) ProtoMessage() {}

func (x *GetCustomerRelatedDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerRelatedDataResponse.ProtoReflect.Descriptor instead.
func (*GetCustomerRelatedDataResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{25}
}

func (x *GetCustomerRelatedDataResponse) GetCustomerRelatedData() *CustomerRelatedData {
	if x != nil {
		return x.CustomerRelatedData
	}
	return nil
}

// ListCustomerRelatedDataRequest 列出客户相关数据请求
type ListCustomerRelatedDataRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 过滤条件
	Filter *ListCustomerRelatedDataRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序
	Sorting *ListCustomerRelatedDataRequest_Sorting `protobuf:"bytes,2,opt,name=sorting,proto3" json:"sorting,omitempty"`
	// 分页大小
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 分页令牌
	PageToken     string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomerRelatedDataRequest) Reset() {
	*x = ListCustomerRelatedDataRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomerRelatedDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerRelatedDataRequest) ProtoMessage() {}

func (x *ListCustomerRelatedDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerRelatedDataRequest.ProtoReflect.Descriptor instead.
func (*ListCustomerRelatedDataRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{26}
}

func (x *ListCustomerRelatedDataRequest) GetFilter() *ListCustomerRelatedDataRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListCustomerRelatedDataRequest) GetSorting() *ListCustomerRelatedDataRequest_Sorting {
	if x != nil {
		return x.Sorting
	}
	return nil
}

func (x *ListCustomerRelatedDataRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListCustomerRelatedDataRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// ListCustomerRelatedDataResponse 列出客户相关数据响应
type ListCustomerRelatedDataResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户相关数据列表
	CustomerRelatedData []*CustomerRelatedData `protobuf:"bytes,1,rep,name=customer_related_data,json=customerRelatedData,proto3" json:"customer_related_data,omitempty"`
	// 下一页令牌
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// 总数量
	TotalSize     *int64 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3,oneof" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomerRelatedDataResponse) Reset() {
	*x = ListCustomerRelatedDataResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomerRelatedDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerRelatedDataResponse) ProtoMessage() {}

func (x *ListCustomerRelatedDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerRelatedDataResponse.ProtoReflect.Descriptor instead.
func (*ListCustomerRelatedDataResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{27}
}

func (x *ListCustomerRelatedDataResponse) GetCustomerRelatedData() []*CustomerRelatedData {
	if x != nil {
		return x.CustomerRelatedData
	}
	return nil
}

func (x *ListCustomerRelatedDataResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListCustomerRelatedDataResponse) GetTotalSize() int64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

// UpdateCustomerRelatedDataRequest 更新客户相关数据请求
type UpdateCustomerRelatedDataRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// customer id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 商家ID
	PreferredBusinessId *int64 `protobuf:"varint,2,opt,name=preferred_business_id,json=preferredBusinessId,proto3,oneof" json:"preferred_business_id,omitempty"`
	// 客户颜色
	ClientColor *string `protobuf:"bytes,3,opt,name=client_color,json=clientColor,proto3,oneof" json:"client_color,omitempty"`
	// 是否屏蔽消息 (0-正常 1-屏蔽)
	IsBlockMessage *int32 `protobuf:"varint,4,opt,name=is_block_message,json=isBlockMessage,proto3,oneof" json:"is_block_message,omitempty"`
	// 是否屏蔽在线预约 (0-正常 1-屏蔽)
	IsBlockOnlineBooking *int32 `protobuf:"varint,5,opt,name=is_block_online_booking,json=isBlockOnlineBooking,proto3,oneof" json:"is_block_online_booking,omitempty"`
	// 登录邮箱
	LoginEmail *string `protobuf:"bytes,6,opt,name=login_email,json=loginEmail,proto3,oneof" json:"login_email,omitempty"`
	// 推荐来源ID
	ReferralSourceId *int32 `protobuf:"varint,7,opt,name=referral_source_id,json=referralSourceId,proto3,oneof" json:"referral_source_id,omitempty"`
	// 推荐来源描述
	ReferralSourceDesc *string `protobuf:"bytes,8,opt,name=referral_source_desc,json=referralSourceDesc,proto3,oneof" json:"referral_source_desc,omitempty"`
	// 发送自动邮件 (0-不接受 1-接受)
	SendAutoEmail *int32 `protobuf:"varint,9,opt,name=send_auto_email,json=sendAutoEmail,proto3,oneof" json:"send_auto_email,omitempty"`
	// 发送自动短信 (0-不接受 1-接受)
	SendAutoMessage *int32 `protobuf:"varint,10,opt,name=send_auto_message,json=sendAutoMessage,proto3,oneof" json:"send_auto_message,omitempty"`
	// 发送app自动短信 (0-不接受 1-接受)
	SendAppAutoMessage *int32 `protobuf:"varint,11,opt,name=send_app_auto_message,json=sendAppAutoMessage,proto3,oneof" json:"send_app_auto_message,omitempty"`
	// 未确认提醒方式 (1-text 2-email 3-phone call)
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: this is not a preposition --)
	UnconfirmedReminderBy []int32 `protobuf:"varint,12,rep,packed,name=unconfirmed_reminder_by,json=unconfirmedReminderBy,proto3" json:"unconfirmed_reminder_by,omitempty"`
	// 首选美容师ID
	PreferredGroomerId *int32 `protobuf:"varint,13,opt,name=preferred_groomer_id,json=preferredGroomerId,proto3,oneof" json:"preferred_groomer_id,omitempty"`
	// 首选频率天数
	PreferredFrequencyDay *int32 `protobuf:"varint,14,opt,name=preferred_frequency_day,json=preferredFrequencyDay,proto3,oneof" json:"preferred_frequency_day,omitempty"`
	// 首选频率类型 (0-by days 1-by weeks)
	PreferredFrequencyType *int32 `protobuf:"varint,15,opt,name=preferred_frequency_type,json=preferredFrequencyType,proto3,oneof" json:"preferred_frequency_type,omitempty"`
	// 最后服务时间
	// (-- api-linter: core::0142::time-field-type=disabled
	//
	//	aip.dev/not-precedent: this is json string not a time --)
	LastServiceTime *string `protobuf:"bytes,16,opt,name=last_service_time,json=lastServiceTime,proto3,oneof" json:"last_service_time,omitempty"`
	// 来源
	Source *string `protobuf:"bytes,17,opt,name=source,proto3,oneof" json:"source,omitempty"`
	// 外部ID
	ExternalId *string `protobuf:"bytes,18,opt,name=external_id,json=externalId,proto3,oneof" json:"external_id,omitempty"`
	// 创建人
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: this is not a preposition --)
	CreateBy *int32 `protobuf:"varint,19,opt,name=create_by,json=createBy,proto3,oneof" json:"create_by,omitempty"`
	// 更新人
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: this is not a preposition --)
	UpdateBy *int32 `protobuf:"varint,20,opt,name=update_by,json=updateBy,proto3,oneof" json:"update_by,omitempty"`
	// 是否重复 (1-true 2-false)
	IsRecurring *int32 `protobuf:"varint,21,opt,name=is_recurring,json=isRecurring,proto3,oneof" json:"is_recurring,omitempty"`
	// 分享预约状态 (0-all 1-unconfirm 2-confirm 4-finished)
	ShareApptStatus *int32 `protobuf:"varint,22,opt,name=share_appt_status,json=shareApptStatus,proto3,oneof" json:"share_appt_status,omitempty"`
	// 分享范围类型 (0-all 1-in x days 2-next x appointment 3-manually apptids)
	ShareRangeType *int32 `protobuf:"varint,23,opt,name=share_range_type,json=shareRangeType,proto3,oneof" json:"share_range_type,omitempty"`
	// 分享范围值
	ShareRangeValue *int32 `protobuf:"varint,24,opt,name=share_range_value,json=shareRangeValue,proto3,oneof" json:"share_range_value,omitempty"`
	// 分享预约JSON
	ShareApptJson *string `protobuf:"bytes,25,opt,name=share_appt_json,json=shareApptJson,proto3,oneof" json:"share_appt_json,omitempty"`
	// 首选天 (JSON数组格式)
	PreferredDay *string `protobuf:"bytes,26,opt,name=preferred_day,json=preferredDay,proto3,oneof" json:"preferred_day,omitempty"`
	// 首选时间 (JSON数组格式)
	// (-- api-linter: core::0142::time-field-type=disabled
	//
	//	aip.dev/not-precedent: this is json string not a time --)
	PreferredTime *string `protobuf:"bytes,27,opt,name=preferred_time,json=preferredTime,proto3,oneof" json:"preferred_time,omitempty"`
	// 账户ID
	AccountId *int64 `protobuf:"varint,28,opt,name=account_id,json=accountId,proto3,oneof" json:"account_id,omitempty"`
	// 客户编码
	CustomerCode *string `protobuf:"bytes,29,opt,name=customer_code,json=customerCode,proto3,oneof" json:"customer_code,omitempty"`
	// 是否退订
	IsUnsubscribed *bool `protobuf:"varint,30,opt,name=is_unsubscribed,json=isUnsubscribed,proto3,oneof" json:"is_unsubscribed,omitempty"`
	// 生日
	// (-- api-linter: core::0142::time-field-names=disabled
	//
	//	aip.dev/not-precedent: this is not a time field --)
	Birthday *timestamppb.Timestamp `protobuf:"bytes,31,opt,name=birthday,proto3,oneof" json:"birthday,omitempty"`
	// 行动状态
	ActionState *string `protobuf:"bytes,32,opt,name=action_state,json=actionState,proto3,oneof" json:"action_state,omitempty"`
	// 自定义生命周期ID
	CustomizeLifeCycleId *int64 `protobuf:"varint,33,opt,name=customize_life_cycle_id,json=customizeLifeCycleId,proto3,oneof" json:"customize_life_cycle_id,omitempty"`
	// 自定义行动状态ID
	CustomizeActionStateId *int64 `protobuf:"varint,34,opt,name=customize_action_state_id,json=customizeActionStateId,proto3,oneof" json:"customize_action_state_id,omitempty"`
	// preferred tip configuration
	// 首选小费是否启用 (0-关闭 1-开启)
	PreferredTipEnable *int32 `protobuf:"varint,35,opt,name=preferred_tip_enable,json=preferredTipEnable,proto3,oneof" json:"preferred_tip_enable,omitempty"`
	// 首选小费类型 (0-按金额 1-按百分比)
	PreferredTipType *int32 `protobuf:"varint,36,opt,name=preferred_tip_type,json=preferredTipType,proto3,oneof" json:"preferred_tip_type,omitempty"`
	// 按金额小费
	PreferredTipAmount *float64 `protobuf:"fixed64,37,opt,name=preferred_tip_amount,json=preferredTipAmount,proto3,oneof" json:"preferred_tip_amount,omitempty"`
	// 按百分比小费 [1,100]
	PreferredTipPercentage *int32 `protobuf:"varint,38,opt,name=preferred_tip_percentage,json=preferredTipPercentage,proto3,oneof" json:"preferred_tip_percentage,omitempty"`
	// default preferred frequency configuration
	// 默认首选频率类型 (1-grooming)
	DefaultPreferredFrequencyType *int32 `protobuf:"varint,39,opt,name=default_preferred_frequency_type,json=defaultPreferredFrequencyType,proto3,oneof" json:"default_preferred_frequency_type,omitempty"`
	// 默认首选频率周期 (1-day, 2-week, 4-month)
	DefaultPreferredCalendarPeriod *int32 `protobuf:"varint,40,opt,name=default_preferred_calendar_period,json=defaultPreferredCalendarPeriod,proto3,oneof" json:"default_preferred_calendar_period,omitempty"`
	// 默认首选频率值
	DefaultPreferredFrequencyValue *int32 `protobuf:"varint,41,opt,name=default_preferred_frequency_value,json=defaultPreferredFrequencyValue,proto3,oneof" json:"default_preferred_frequency_value,omitempty"`
	unknownFields                  protoimpl.UnknownFields
	sizeCache                      protoimpl.SizeCache
}

func (x *UpdateCustomerRelatedDataRequest) Reset() {
	*x = UpdateCustomerRelatedDataRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCustomerRelatedDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerRelatedDataRequest) ProtoMessage() {}

func (x *UpdateCustomerRelatedDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerRelatedDataRequest.ProtoReflect.Descriptor instead.
func (*UpdateCustomerRelatedDataRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{28}
}

func (x *UpdateCustomerRelatedDataRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest) GetPreferredBusinessId() int64 {
	if x != nil && x.PreferredBusinessId != nil {
		return *x.PreferredBusinessId
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest) GetClientColor() string {
	if x != nil && x.ClientColor != nil {
		return *x.ClientColor
	}
	return ""
}

func (x *UpdateCustomerRelatedDataRequest) GetIsBlockMessage() int32 {
	if x != nil && x.IsBlockMessage != nil {
		return *x.IsBlockMessage
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest) GetIsBlockOnlineBooking() int32 {
	if x != nil && x.IsBlockOnlineBooking != nil {
		return *x.IsBlockOnlineBooking
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest) GetLoginEmail() string {
	if x != nil && x.LoginEmail != nil {
		return *x.LoginEmail
	}
	return ""
}

func (x *UpdateCustomerRelatedDataRequest) GetReferralSourceId() int32 {
	if x != nil && x.ReferralSourceId != nil {
		return *x.ReferralSourceId
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest) GetReferralSourceDesc() string {
	if x != nil && x.ReferralSourceDesc != nil {
		return *x.ReferralSourceDesc
	}
	return ""
}

func (x *UpdateCustomerRelatedDataRequest) GetSendAutoEmail() int32 {
	if x != nil && x.SendAutoEmail != nil {
		return *x.SendAutoEmail
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest) GetSendAutoMessage() int32 {
	if x != nil && x.SendAutoMessage != nil {
		return *x.SendAutoMessage
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest) GetSendAppAutoMessage() int32 {
	if x != nil && x.SendAppAutoMessage != nil {
		return *x.SendAppAutoMessage
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest) GetUnconfirmedReminderBy() []int32 {
	if x != nil {
		return x.UnconfirmedReminderBy
	}
	return nil
}

func (x *UpdateCustomerRelatedDataRequest) GetPreferredGroomerId() int32 {
	if x != nil && x.PreferredGroomerId != nil {
		return *x.PreferredGroomerId
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest) GetPreferredFrequencyDay() int32 {
	if x != nil && x.PreferredFrequencyDay != nil {
		return *x.PreferredFrequencyDay
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest) GetPreferredFrequencyType() int32 {
	if x != nil && x.PreferredFrequencyType != nil {
		return *x.PreferredFrequencyType
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest) GetLastServiceTime() string {
	if x != nil && x.LastServiceTime != nil {
		return *x.LastServiceTime
	}
	return ""
}

func (x *UpdateCustomerRelatedDataRequest) GetSource() string {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return ""
}

func (x *UpdateCustomerRelatedDataRequest) GetExternalId() string {
	if x != nil && x.ExternalId != nil {
		return *x.ExternalId
	}
	return ""
}

func (x *UpdateCustomerRelatedDataRequest) GetCreateBy() int32 {
	if x != nil && x.CreateBy != nil {
		return *x.CreateBy
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest) GetUpdateBy() int32 {
	if x != nil && x.UpdateBy != nil {
		return *x.UpdateBy
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest) GetIsRecurring() int32 {
	if x != nil && x.IsRecurring != nil {
		return *x.IsRecurring
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest) GetShareApptStatus() int32 {
	if x != nil && x.ShareApptStatus != nil {
		return *x.ShareApptStatus
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest) GetShareRangeType() int32 {
	if x != nil && x.ShareRangeType != nil {
		return *x.ShareRangeType
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest) GetShareRangeValue() int32 {
	if x != nil && x.ShareRangeValue != nil {
		return *x.ShareRangeValue
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest) GetShareApptJson() string {
	if x != nil && x.ShareApptJson != nil {
		return *x.ShareApptJson
	}
	return ""
}

func (x *UpdateCustomerRelatedDataRequest) GetPreferredDay() string {
	if x != nil && x.PreferredDay != nil {
		return *x.PreferredDay
	}
	return ""
}

func (x *UpdateCustomerRelatedDataRequest) GetPreferredTime() string {
	if x != nil && x.PreferredTime != nil {
		return *x.PreferredTime
	}
	return ""
}

func (x *UpdateCustomerRelatedDataRequest) GetAccountId() int64 {
	if x != nil && x.AccountId != nil {
		return *x.AccountId
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest) GetCustomerCode() string {
	if x != nil && x.CustomerCode != nil {
		return *x.CustomerCode
	}
	return ""
}

func (x *UpdateCustomerRelatedDataRequest) GetIsUnsubscribed() bool {
	if x != nil && x.IsUnsubscribed != nil {
		return *x.IsUnsubscribed
	}
	return false
}

func (x *UpdateCustomerRelatedDataRequest) GetBirthday() *timestamppb.Timestamp {
	if x != nil {
		return x.Birthday
	}
	return nil
}

func (x *UpdateCustomerRelatedDataRequest) GetActionState() string {
	if x != nil && x.ActionState != nil {
		return *x.ActionState
	}
	return ""
}

func (x *UpdateCustomerRelatedDataRequest) GetCustomizeLifeCycleId() int64 {
	if x != nil && x.CustomizeLifeCycleId != nil {
		return *x.CustomizeLifeCycleId
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest) GetCustomizeActionStateId() int64 {
	if x != nil && x.CustomizeActionStateId != nil {
		return *x.CustomizeActionStateId
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest) GetPreferredTipEnable() int32 {
	if x != nil && x.PreferredTipEnable != nil {
		return *x.PreferredTipEnable
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest) GetPreferredTipType() int32 {
	if x != nil && x.PreferredTipType != nil {
		return *x.PreferredTipType
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest) GetPreferredTipAmount() float64 {
	if x != nil && x.PreferredTipAmount != nil {
		return *x.PreferredTipAmount
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest) GetPreferredTipPercentage() int32 {
	if x != nil && x.PreferredTipPercentage != nil {
		return *x.PreferredTipPercentage
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest) GetDefaultPreferredFrequencyType() int32 {
	if x != nil && x.DefaultPreferredFrequencyType != nil {
		return *x.DefaultPreferredFrequencyType
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest) GetDefaultPreferredCalendarPeriod() int32 {
	if x != nil && x.DefaultPreferredCalendarPeriod != nil {
		return *x.DefaultPreferredCalendarPeriod
	}
	return 0
}

func (x *UpdateCustomerRelatedDataRequest) GetDefaultPreferredFrequencyValue() int32 {
	if x != nil && x.DefaultPreferredFrequencyValue != nil {
		return *x.DefaultPreferredFrequencyValue
	}
	return 0
}

// UpdateCustomerRelatedDataResponse 更新客户相关数据响应
type UpdateCustomerRelatedDataResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户相关数据
	CustomerRelatedData *CustomerRelatedData `protobuf:"bytes,1,opt,name=customer_related_data,json=customerRelatedData,proto3" json:"customer_related_data,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *UpdateCustomerRelatedDataResponse) Reset() {
	*x = UpdateCustomerRelatedDataResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCustomerRelatedDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerRelatedDataResponse) ProtoMessage() {}

func (x *UpdateCustomerRelatedDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerRelatedDataResponse.ProtoReflect.Descriptor instead.
func (*UpdateCustomerRelatedDataResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{29}
}

func (x *UpdateCustomerRelatedDataResponse) GetCustomerRelatedData() *CustomerRelatedData {
	if x != nil {
		return x.CustomerRelatedData
	}
	return nil
}

// DeleteCustomerRelatedDataRequest 删除客户相关数据请求
type DeleteCustomerRelatedDataRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// customer id
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCustomerRelatedDataRequest) Reset() {
	*x = DeleteCustomerRelatedDataRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCustomerRelatedDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCustomerRelatedDataRequest) ProtoMessage() {}

func (x *DeleteCustomerRelatedDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCustomerRelatedDataRequest.ProtoReflect.Descriptor instead.
func (*DeleteCustomerRelatedDataRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{30}
}

func (x *DeleteCustomerRelatedDataRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// DeleteCustomerRelatedDataResponse 删除客户相关数据响应
type DeleteCustomerRelatedDataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCustomerRelatedDataResponse) Reset() {
	*x = DeleteCustomerRelatedDataResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCustomerRelatedDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCustomerRelatedDataResponse) ProtoMessage() {}

func (x *DeleteCustomerRelatedDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCustomerRelatedDataResponse.ProtoReflect.Descriptor instead.
func (*DeleteCustomerRelatedDataResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{31}
}

// ==================== Contact Tag Request/Response Messages ====================
// CreateContactTagRequest 创建标签请求
type CreateContactTagRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 组织引用
	Organization *OrganizationRef `protobuf:"bytes,1,opt,name=organization,proto3" json:"organization,omitempty"`
	// 标签名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 标签描述
	Description *string `protobuf:"bytes,3,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// 颜色
	Color string `protobuf:"bytes,4,opt,name=color,proto3" json:"color,omitempty"`
	// 排序顺序
	SortOrder *int32 `protobuf:"varint,5,opt,name=sort_order,json=sortOrder,proto3,oneof" json:"sort_order,omitempty"`
	// 状态
	State ContactTag_State `protobuf:"varint,6,opt,name=state,proto3,enum=backend.proto.customer.v2.ContactTag_State" json:"state,omitempty"`
	// 标签类型
	Type          *ContactTag_Type `protobuf:"varint,7,opt,name=type,proto3,enum=backend.proto.customer.v2.ContactTag_Type,oneof" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateContactTagRequest) Reset() {
	*x = CreateContactTagRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateContactTagRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateContactTagRequest) ProtoMessage() {}

func (x *CreateContactTagRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateContactTagRequest.ProtoReflect.Descriptor instead.
func (*CreateContactTagRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{32}
}

func (x *CreateContactTagRequest) GetOrganization() *OrganizationRef {
	if x != nil {
		return x.Organization
	}
	return nil
}

func (x *CreateContactTagRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateContactTagRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *CreateContactTagRequest) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *CreateContactTagRequest) GetSortOrder() int32 {
	if x != nil && x.SortOrder != nil {
		return *x.SortOrder
	}
	return 0
}

func (x *CreateContactTagRequest) GetState() ContactTag_State {
	if x != nil {
		return x.State
	}
	return ContactTag_STATE_UNSPECIFIED
}

func (x *CreateContactTagRequest) GetType() ContactTag_Type {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return ContactTag_TYPE_UNSPECIFIED
}

// CreateContactTagResponse 创建标签响应
type CreateContactTagResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 联系人标签
	ContactTag    *ContactTag `protobuf:"bytes,1,opt,name=contact_tag,json=contactTag,proto3" json:"contact_tag,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateContactTagResponse) Reset() {
	*x = CreateContactTagResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateContactTagResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateContactTagResponse) ProtoMessage() {}

func (x *CreateContactTagResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateContactTagResponse.ProtoReflect.Descriptor instead.
func (*CreateContactTagResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{33}
}

func (x *CreateContactTagResponse) GetContactTag() *ContactTag {
	if x != nil {
		return x.ContactTag
	}
	return nil
}

// GetContactTagRequest 获取标签请求
type GetContactTagRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 标签ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetContactTagRequest) Reset() {
	*x = GetContactTagRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetContactTagRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetContactTagRequest) ProtoMessage() {}

func (x *GetContactTagRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetContactTagRequest.ProtoReflect.Descriptor instead.
func (*GetContactTagRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{34}
}

func (x *GetContactTagRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// GetContactTagResponse 获取标签响应
type GetContactTagResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 联系人标签
	ContactTag    *ContactTag `protobuf:"bytes,1,opt,name=contact_tag,json=contactTag,proto3" json:"contact_tag,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetContactTagResponse) Reset() {
	*x = GetContactTagResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetContactTagResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetContactTagResponse) ProtoMessage() {}

func (x *GetContactTagResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetContactTagResponse.ProtoReflect.Descriptor instead.
func (*GetContactTagResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{35}
}

func (x *GetContactTagResponse) GetContactTag() *ContactTag {
	if x != nil {
		return x.ContactTag
	}
	return nil
}

// ListContactTagsRequest 列出标签请求
type ListContactTagsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 过滤条件
	Filter *ListContactTagsRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序
	Sorting *ListContactTagsRequest_Sorting `protobuf:"bytes,2,opt,name=sorting,proto3" json:"sorting,omitempty"`
	// 分页大小
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 分页令牌
	PageToken     string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListContactTagsRequest) Reset() {
	*x = ListContactTagsRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListContactTagsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListContactTagsRequest) ProtoMessage() {}

func (x *ListContactTagsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListContactTagsRequest.ProtoReflect.Descriptor instead.
func (*ListContactTagsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{36}
}

func (x *ListContactTagsRequest) GetFilter() *ListContactTagsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListContactTagsRequest) GetSorting() *ListContactTagsRequest_Sorting {
	if x != nil {
		return x.Sorting
	}
	return nil
}

func (x *ListContactTagsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListContactTagsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// ListContactTagsResponse 列出标签响应
type ListContactTagsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 标签列表
	ContactTags []*ContactTag `protobuf:"bytes,1,rep,name=contact_tags,json=contactTags,proto3" json:"contact_tags,omitempty"`
	// 下一页令牌
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// 总数量
	TotalSize     *int64 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3,oneof" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListContactTagsResponse) Reset() {
	*x = ListContactTagsResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListContactTagsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListContactTagsResponse) ProtoMessage() {}

func (x *ListContactTagsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListContactTagsResponse.ProtoReflect.Descriptor instead.
func (*ListContactTagsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{37}
}

func (x *ListContactTagsResponse) GetContactTags() []*ContactTag {
	if x != nil {
		return x.ContactTags
	}
	return nil
}

func (x *ListContactTagsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListContactTagsResponse) GetTotalSize() int64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

// UpdateContactTagRequest 更新标签请求
type UpdateContactTagRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name
	Name *string `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// description
	Description *string `protobuf:"bytes,3,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// color
	Color *string `protobuf:"bytes,4,opt,name=color,proto3,oneof" json:"color,omitempty"`
	// sort order
	SortOrder *int32 `protobuf:"varint,5,opt,name=sort_order,json=sortOrder,proto3,oneof" json:"sort_order,omitempty"`
	// state
	State         *ContactTag_State `protobuf:"varint,6,opt,name=state,proto3,enum=backend.proto.customer.v2.ContactTag_State,oneof" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateContactTagRequest) Reset() {
	*x = UpdateContactTagRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateContactTagRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateContactTagRequest) ProtoMessage() {}

func (x *UpdateContactTagRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateContactTagRequest.ProtoReflect.Descriptor instead.
func (*UpdateContactTagRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{38}
}

func (x *UpdateContactTagRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateContactTagRequest) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateContactTagRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *UpdateContactTagRequest) GetColor() string {
	if x != nil && x.Color != nil {
		return *x.Color
	}
	return ""
}

func (x *UpdateContactTagRequest) GetSortOrder() int32 {
	if x != nil && x.SortOrder != nil {
		return *x.SortOrder
	}
	return 0
}

func (x *UpdateContactTagRequest) GetState() ContactTag_State {
	if x != nil && x.State != nil {
		return *x.State
	}
	return ContactTag_STATE_UNSPECIFIED
}

// UpdateContactTagResponse 更新标签响应
type UpdateContactTagResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 联系人标签
	ContactTag    *ContactTag `protobuf:"bytes,1,opt,name=contact_tag,json=contactTag,proto3" json:"contact_tag,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateContactTagResponse) Reset() {
	*x = UpdateContactTagResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateContactTagResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateContactTagResponse) ProtoMessage() {}

func (x *UpdateContactTagResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateContactTagResponse.ProtoReflect.Descriptor instead.
func (*UpdateContactTagResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{39}
}

func (x *UpdateContactTagResponse) GetContactTag() *ContactTag {
	if x != nil {
		return x.ContactTag
	}
	return nil
}

// DeleteContactTagRequest 删除标签请求
type DeleteContactTagRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 标签ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteContactTagRequest) Reset() {
	*x = DeleteContactTagRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteContactTagRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteContactTagRequest) ProtoMessage() {}

func (x *DeleteContactTagRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteContactTagRequest.ProtoReflect.Descriptor instead.
func (*DeleteContactTagRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{40}
}

func (x *DeleteContactTagRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// DeleteContactTagResponse 删除标签响应
type DeleteContactTagResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteContactTagResponse) Reset() {
	*x = DeleteContactTagResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteContactTagResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteContactTagResponse) ProtoMessage() {}

func (x *DeleteContactTagResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteContactTagResponse.ProtoReflect.Descriptor instead.
func (*DeleteContactTagResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{41}
}

// ==================== Lead Request/Response Messages ====================
// CreateLeadRequest 创建线索请求
type CreateLeadRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 组织引用
	Organization *OrganizationRef `protobuf:"bytes,1,opt,name=organization,proto3" json:"organization,omitempty"`
	// 线索名称
	GivenName string `protobuf:"bytes,2,opt,name=given_name,json=givenName,proto3" json:"given_name,omitempty"`
	// 线索姓氏
	FamilyName string `protobuf:"bytes,3,opt,name=family_name,json=familyName,proto3" json:"family_name,omitempty"`
	// 线索自定义字段
	CustomFields *structpb.Struct `protobuf:"bytes,4,opt,name=custom_fields,json=customFields,proto3,oneof" json:"custom_fields,omitempty"`
	// 生命周期ID
	LifecycleId *int64 `protobuf:"varint,5,opt,name=lifecycle_id,json=lifecycleId,proto3,oneof" json:"lifecycle_id,omitempty"`
	// 负责人员工ID
	OwnerStaffId *int64 `protobuf:"varint,6,opt,name=owner_staff_id,json=ownerStaffId,proto3,oneof" json:"owner_staff_id,omitempty"`
	// 行动状态ID
	ActionStateId *int64 `protobuf:"varint,7,opt,name=action_state_id,json=actionStateId,proto3,oneof" json:"action_state_id,omitempty"`
	// 头像路径
	AvatarPath *string `protobuf:"bytes,8,opt,name=avatar_path,json=avatarPath,proto3,oneof" json:"avatar_path,omitempty"`
	// 推荐来源ID
	ReferralSourceId *int64 `protobuf:"varint,9,opt,name=referral_source_id,json=referralSourceId,proto3,oneof" json:"referral_source_id,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CreateLeadRequest) Reset() {
	*x = CreateLeadRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateLeadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLeadRequest) ProtoMessage() {}

func (x *CreateLeadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLeadRequest.ProtoReflect.Descriptor instead.
func (*CreateLeadRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{42}
}

func (x *CreateLeadRequest) GetOrganization() *OrganizationRef {
	if x != nil {
		return x.Organization
	}
	return nil
}

func (x *CreateLeadRequest) GetGivenName() string {
	if x != nil {
		return x.GivenName
	}
	return ""
}

func (x *CreateLeadRequest) GetFamilyName() string {
	if x != nil {
		return x.FamilyName
	}
	return ""
}

func (x *CreateLeadRequest) GetCustomFields() *structpb.Struct {
	if x != nil {
		return x.CustomFields
	}
	return nil
}

func (x *CreateLeadRequest) GetLifecycleId() int64 {
	if x != nil && x.LifecycleId != nil {
		return *x.LifecycleId
	}
	return 0
}

func (x *CreateLeadRequest) GetOwnerStaffId() int64 {
	if x != nil && x.OwnerStaffId != nil {
		return *x.OwnerStaffId
	}
	return 0
}

func (x *CreateLeadRequest) GetActionStateId() int64 {
	if x != nil && x.ActionStateId != nil {
		return *x.ActionStateId
	}
	return 0
}

func (x *CreateLeadRequest) GetAvatarPath() string {
	if x != nil && x.AvatarPath != nil {
		return *x.AvatarPath
	}
	return ""
}

func (x *CreateLeadRequest) GetReferralSourceId() int64 {
	if x != nil && x.ReferralSourceId != nil {
		return *x.ReferralSourceId
	}
	return 0
}

// CreateLeadResponse 创建线索响应
type CreateLeadResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 线索
	Lead          *Lead `protobuf:"bytes,1,opt,name=lead,proto3" json:"lead,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateLeadResponse) Reset() {
	*x = CreateLeadResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateLeadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLeadResponse) ProtoMessage() {}

func (x *CreateLeadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLeadResponse.ProtoReflect.Descriptor instead.
func (*CreateLeadResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{43}
}

func (x *CreateLeadResponse) GetLead() *Lead {
	if x != nil {
		return x.Lead
	}
	return nil
}

// GetLeadRequest 获取线索请求
type GetLeadRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 线索ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLeadRequest) Reset() {
	*x = GetLeadRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLeadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLeadRequest) ProtoMessage() {}

func (x *GetLeadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLeadRequest.ProtoReflect.Descriptor instead.
func (*GetLeadRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{44}
}

func (x *GetLeadRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// GetLeadResponse 获取线索响应
type GetLeadResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 线索
	Lead          *Lead `protobuf:"bytes,1,opt,name=lead,proto3" json:"lead,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLeadResponse) Reset() {
	*x = GetLeadResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLeadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLeadResponse) ProtoMessage() {}

func (x *GetLeadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLeadResponse.ProtoReflect.Descriptor instead.
func (*GetLeadResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{45}
}

func (x *GetLeadResponse) GetLead() *Lead {
	if x != nil {
		return x.Lead
	}
	return nil
}

// ListLeadsRequest 列出线索请求
type ListLeadsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 过滤条件
	Filter *ListLeadsRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序
	Sorting *ListLeadsRequest_Sorting `protobuf:"bytes,2,opt,name=sorting,proto3" json:"sorting,omitempty"`
	// 分页大小
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 分页令牌
	PageToken     string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListLeadsRequest) Reset() {
	*x = ListLeadsRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListLeadsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLeadsRequest) ProtoMessage() {}

func (x *ListLeadsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLeadsRequest.ProtoReflect.Descriptor instead.
func (*ListLeadsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{46}
}

func (x *ListLeadsRequest) GetFilter() *ListLeadsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListLeadsRequest) GetSorting() *ListLeadsRequest_Sorting {
	if x != nil {
		return x.Sorting
	}
	return nil
}

func (x *ListLeadsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListLeadsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// ListLeadsResponse 列出线索响应
type ListLeadsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 线索列表
	Leads []*Lead `protobuf:"bytes,1,rep,name=leads,proto3" json:"leads,omitempty"`
	// 下一页令牌
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// 总数量
	TotalSize     *int64 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3,oneof" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListLeadsResponse) Reset() {
	*x = ListLeadsResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListLeadsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLeadsResponse) ProtoMessage() {}

func (x *ListLeadsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLeadsResponse.ProtoReflect.Descriptor instead.
func (*ListLeadsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{47}
}

func (x *ListLeadsResponse) GetLeads() []*Lead {
	if x != nil {
		return x.Leads
	}
	return nil
}

func (x *ListLeadsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListLeadsResponse) GetTotalSize() int64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

// UpdateLeadRequest 更新线索请求
type UpdateLeadRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 线索名称
	GivenName *string `protobuf:"bytes,2,opt,name=given_name,json=givenName,proto3,oneof" json:"given_name,omitempty"`
	// 线索姓氏
	FamilyName *string `protobuf:"bytes,3,opt,name=family_name,json=familyName,proto3,oneof" json:"family_name,omitempty"`
	// 线索自定义字段
	CustomFields *structpb.Struct `protobuf:"bytes,4,opt,name=custom_fields,json=customFields,proto3,oneof" json:"custom_fields,omitempty"`
	// 生命周期ID
	LifecycleId *int64 `protobuf:"varint,5,opt,name=lifecycle_id,json=lifecycleId,proto3,oneof" json:"lifecycle_id,omitempty"`
	// 负责人员工ID
	OwnerStaffId *int64 `protobuf:"varint,6,opt,name=owner_staff_id,json=ownerStaffId,proto3,oneof" json:"owner_staff_id,omitempty"`
	// 状态
	State *Lead_State `protobuf:"varint,7,opt,name=state,proto3,enum=backend.proto.customer.v2.Lead_State,oneof" json:"state,omitempty"`
	// 头像路径
	AvatarPath    *string `protobuf:"bytes,8,opt,name=avatar_path,json=avatarPath,proto3,oneof" json:"avatar_path,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateLeadRequest) Reset() {
	*x = UpdateLeadRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateLeadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLeadRequest) ProtoMessage() {}

func (x *UpdateLeadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLeadRequest.ProtoReflect.Descriptor instead.
func (*UpdateLeadRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{48}
}

func (x *UpdateLeadRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateLeadRequest) GetGivenName() string {
	if x != nil && x.GivenName != nil {
		return *x.GivenName
	}
	return ""
}

func (x *UpdateLeadRequest) GetFamilyName() string {
	if x != nil && x.FamilyName != nil {
		return *x.FamilyName
	}
	return ""
}

func (x *UpdateLeadRequest) GetCustomFields() *structpb.Struct {
	if x != nil {
		return x.CustomFields
	}
	return nil
}

func (x *UpdateLeadRequest) GetLifecycleId() int64 {
	if x != nil && x.LifecycleId != nil {
		return *x.LifecycleId
	}
	return 0
}

func (x *UpdateLeadRequest) GetOwnerStaffId() int64 {
	if x != nil && x.OwnerStaffId != nil {
		return *x.OwnerStaffId
	}
	return 0
}

func (x *UpdateLeadRequest) GetState() Lead_State {
	if x != nil && x.State != nil {
		return *x.State
	}
	return Lead_STATE_UNSPECIFIED
}

func (x *UpdateLeadRequest) GetAvatarPath() string {
	if x != nil && x.AvatarPath != nil {
		return *x.AvatarPath
	}
	return ""
}

// UpdateLeadResponse 更新线索响应
type UpdateLeadResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 线索
	Lead          *Lead `protobuf:"bytes,1,opt,name=lead,proto3" json:"lead,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateLeadResponse) Reset() {
	*x = UpdateLeadResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateLeadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLeadResponse) ProtoMessage() {}

func (x *UpdateLeadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLeadResponse.ProtoReflect.Descriptor instead.
func (*UpdateLeadResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{49}
}

func (x *UpdateLeadResponse) GetLead() *Lead {
	if x != nil {
		return x.Lead
	}
	return nil
}

// DeleteLeadRequest 删除线索请求
type DeleteLeadRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 线索ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteLeadRequest) Reset() {
	*x = DeleteLeadRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteLeadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLeadRequest) ProtoMessage() {}

func (x *DeleteLeadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLeadRequest.ProtoReflect.Descriptor instead.
func (*DeleteLeadRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{50}
}

func (x *DeleteLeadRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// DeleteLeadResponse 删除线索响应
type DeleteLeadResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteLeadResponse) Reset() {
	*x = DeleteLeadResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteLeadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLeadResponse) ProtoMessage() {}

func (x *DeleteLeadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLeadResponse.ProtoReflect.Descriptor instead.
func (*DeleteLeadResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{51}
}

// ==================== Address Request/Response Messages ====================
// CreateAddressRequest 创建地址请求
type CreateAddressRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 所有者ID
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 地址
	Address *postaladdress.PostalAddress `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	// 经纬度
	Latlng *latlng.LatLng `protobuf:"bytes,3,opt,name=latlng,proto3" json:"latlng,omitempty"`
	// 类型
	Type Address_Type `protobuf:"varint,4,opt,name=type,proto3,enum=backend.proto.customer.v2.Address_Type" json:"type,omitempty"`
	// 组织引用
	Organization  *OrganizationRef `protobuf:"bytes,5,opt,name=organization,proto3" json:"organization,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAddressRequest) Reset() {
	*x = CreateAddressRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAddressRequest) ProtoMessage() {}

func (x *CreateAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAddressRequest.ProtoReflect.Descriptor instead.
func (*CreateAddressRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{52}
}

func (x *CreateAddressRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CreateAddressRequest) GetAddress() *postaladdress.PostalAddress {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *CreateAddressRequest) GetLatlng() *latlng.LatLng {
	if x != nil {
		return x.Latlng
	}
	return nil
}

func (x *CreateAddressRequest) GetType() Address_Type {
	if x != nil {
		return x.Type
	}
	return Address_TYPE_UNSPECIFIED
}

func (x *CreateAddressRequest) GetOrganization() *OrganizationRef {
	if x != nil {
		return x.Organization
	}
	return nil
}

// CreateAddressResponse 创建地址响应
type CreateAddressResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 地址
	Address       *Address `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAddressResponse) Reset() {
	*x = CreateAddressResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAddressResponse) ProtoMessage() {}

func (x *CreateAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAddressResponse.ProtoReflect.Descriptor instead.
func (*CreateAddressResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{53}
}

func (x *CreateAddressResponse) GetAddress() *Address {
	if x != nil {
		return x.Address
	}
	return nil
}

// GetAddressRequest 获取地址请求
type GetAddressRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 地址ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAddressRequest) Reset() {
	*x = GetAddressRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAddressRequest) ProtoMessage() {}

func (x *GetAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAddressRequest.ProtoReflect.Descriptor instead.
func (*GetAddressRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{54}
}

func (x *GetAddressRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// GetAddressResponse 获取地址响应
type GetAddressResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 地址
	Address       *Address `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAddressResponse) Reset() {
	*x = GetAddressResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAddressResponse) ProtoMessage() {}

func (x *GetAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAddressResponse.ProtoReflect.Descriptor instead.
func (*GetAddressResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{55}
}

func (x *GetAddressResponse) GetAddress() *Address {
	if x != nil {
		return x.Address
	}
	return nil
}

// ListAddressesRequest 列出地址请求
type ListAddressesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 过滤条件
	Filter *ListAddressesRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序
	Sorting *ListAddressesRequest_Sorting `protobuf:"bytes,2,opt,name=sorting,proto3" json:"sorting,omitempty"`
	// 分页大小
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 分页令牌
	PageToken     string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAddressesRequest) Reset() {
	*x = ListAddressesRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAddressesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAddressesRequest) ProtoMessage() {}

func (x *ListAddressesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAddressesRequest.ProtoReflect.Descriptor instead.
func (*ListAddressesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{56}
}

func (x *ListAddressesRequest) GetFilter() *ListAddressesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListAddressesRequest) GetSorting() *ListAddressesRequest_Sorting {
	if x != nil {
		return x.Sorting
	}
	return nil
}

func (x *ListAddressesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListAddressesRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// ListAddressesResponse 列出地址响应
type ListAddressesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 地址列表
	Addresses []*Address `protobuf:"bytes,1,rep,name=addresses,proto3" json:"addresses,omitempty"`
	// 下一页令牌
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// 总数量
	TotalSize     *int64 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3,oneof" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAddressesResponse) Reset() {
	*x = ListAddressesResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAddressesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAddressesResponse) ProtoMessage() {}

func (x *ListAddressesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAddressesResponse.ProtoReflect.Descriptor instead.
func (*ListAddressesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{57}
}

func (x *ListAddressesResponse) GetAddresses() []*Address {
	if x != nil {
		return x.Addresses
	}
	return nil
}

func (x *ListAddressesResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListAddressesResponse) GetTotalSize() int64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

// UpdateAddressRequest 更新地址请求
type UpdateAddressRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// address
	Address *postaladdress.PostalAddress `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	// 经纬度
	Latlng *latlng.LatLng `protobuf:"bytes,3,opt,name=latlng,proto3" json:"latlng,omitempty"`
	// type
	Type *Address_Type `protobuf:"varint,4,opt,name=type,proto3,enum=backend.proto.customer.v2.Address_Type,oneof" json:"type,omitempty"`
	// state
	State         *Address_State `protobuf:"varint,5,opt,name=state,proto3,enum=backend.proto.customer.v2.Address_State,oneof" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAddressRequest) Reset() {
	*x = UpdateAddressRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAddressRequest) ProtoMessage() {}

func (x *UpdateAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAddressRequest.ProtoReflect.Descriptor instead.
func (*UpdateAddressRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{58}
}

func (x *UpdateAddressRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateAddressRequest) GetAddress() *postaladdress.PostalAddress {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *UpdateAddressRequest) GetLatlng() *latlng.LatLng {
	if x != nil {
		return x.Latlng
	}
	return nil
}

func (x *UpdateAddressRequest) GetType() Address_Type {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return Address_TYPE_UNSPECIFIED
}

func (x *UpdateAddressRequest) GetState() Address_State {
	if x != nil && x.State != nil {
		return *x.State
	}
	return Address_STATE_UNSPECIFIED
}

// UpdateAddressResponse 更新地址响应
type UpdateAddressResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 地址
	Address       *Address `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAddressResponse) Reset() {
	*x = UpdateAddressResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAddressResponse) ProtoMessage() {}

func (x *UpdateAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAddressResponse.ProtoReflect.Descriptor instead.
func (*UpdateAddressResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{59}
}

func (x *UpdateAddressResponse) GetAddress() *Address {
	if x != nil {
		return x.Address
	}
	return nil
}

// DeleteAddressRequest 删除地址请求
type DeleteAddressRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 地址ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteAddressRequest) Reset() {
	*x = DeleteAddressRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAddressRequest) ProtoMessage() {}

func (x *DeleteAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAddressRequest.ProtoReflect.Descriptor instead.
func (*DeleteAddressRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{60}
}

func (x *DeleteAddressRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// DeleteAddressResponse 删除地址响应
type DeleteAddressResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteAddressResponse) Reset() {
	*x = DeleteAddressResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAddressResponse) ProtoMessage() {}

func (x *DeleteAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAddressResponse.ProtoReflect.Descriptor instead.
func (*DeleteAddressResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{61}
}

// ==================== Custom Field Request/Response Messages ====================
// CreateCustomFieldRequest 创建自定义字段请求
type CreateCustomFieldRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 组织引用
	Organization *OrganizationRef `protobuf:"bytes,1,opt,name=organization,proto3" json:"organization,omitempty"`
	// 字段名称
	Label string `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`
	// 字段类型
	Type CustomField_Type `protobuf:"varint,3,opt,name=type,proto3,enum=backend.proto.customer.v2.CustomField_Type" json:"type,omitempty"`
	// 实体所有者
	AssociationType CustomField_AssociationType `protobuf:"varint,4,opt,name=association_type,json=associationType,proto3,enum=backend.proto.customer.v2.CustomField_AssociationType" json:"association_type,omitempty"`
	// 是否必填
	IsRequired *bool `protobuf:"varint,5,opt,name=is_required,json=isRequired,proto3,oneof" json:"is_required,omitempty"`
	// 默认值
	DefaultValue *CustomField_Value `protobuf:"bytes,6,opt,name=default_value,json=defaultValue,proto3,oneof" json:"default_value,omitempty"`
	// 选项
	Options []*CustomField_Option `protobuf:"bytes,7,rep,name=options,proto3" json:"options,omitempty"`
	// 显示顺序
	DisplayOrder *int32 `protobuf:"varint,8,opt,name=display_order,json=displayOrder,proto3,oneof" json:"display_order,omitempty"`
	// 帮助文本
	HelpText      *string `protobuf:"bytes,9,opt,name=help_text,json=helpText,proto3,oneof" json:"help_text,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCustomFieldRequest) Reset() {
	*x = CreateCustomFieldRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCustomFieldRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomFieldRequest) ProtoMessage() {}

func (x *CreateCustomFieldRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomFieldRequest.ProtoReflect.Descriptor instead.
func (*CreateCustomFieldRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{62}
}

func (x *CreateCustomFieldRequest) GetOrganization() *OrganizationRef {
	if x != nil {
		return x.Organization
	}
	return nil
}

func (x *CreateCustomFieldRequest) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *CreateCustomFieldRequest) GetType() CustomField_Type {
	if x != nil {
		return x.Type
	}
	return CustomField_TYPE_UNSPECIFIED
}

func (x *CreateCustomFieldRequest) GetAssociationType() CustomField_AssociationType {
	if x != nil {
		return x.AssociationType
	}
	return CustomField_ASSOCIATION_TYPE_UNSPECIFIED
}

func (x *CreateCustomFieldRequest) GetIsRequired() bool {
	if x != nil && x.IsRequired != nil {
		return *x.IsRequired
	}
	return false
}

func (x *CreateCustomFieldRequest) GetDefaultValue() *CustomField_Value {
	if x != nil {
		return x.DefaultValue
	}
	return nil
}

func (x *CreateCustomFieldRequest) GetOptions() []*CustomField_Option {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *CreateCustomFieldRequest) GetDisplayOrder() int32 {
	if x != nil && x.DisplayOrder != nil {
		return *x.DisplayOrder
	}
	return 0
}

func (x *CreateCustomFieldRequest) GetHelpText() string {
	if x != nil && x.HelpText != nil {
		return *x.HelpText
	}
	return ""
}

// CreateCustomFieldResponse 创建自定义字段响应
type CreateCustomFieldResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 自定义字段
	CustomField   *CustomField `protobuf:"bytes,1,opt,name=custom_field,json=customField,proto3" json:"custom_field,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCustomFieldResponse) Reset() {
	*x = CreateCustomFieldResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[63]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCustomFieldResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomFieldResponse) ProtoMessage() {}

func (x *CreateCustomFieldResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[63]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomFieldResponse.ProtoReflect.Descriptor instead.
func (*CreateCustomFieldResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{63}
}

func (x *CreateCustomFieldResponse) GetCustomField() *CustomField {
	if x != nil {
		return x.CustomField
	}
	return nil
}

// GetCustomFieldRequest 获取自定义字段请求
type GetCustomFieldRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 自定义字段ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCustomFieldRequest) Reset() {
	*x = GetCustomFieldRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[64]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustomFieldRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomFieldRequest) ProtoMessage() {}

func (x *GetCustomFieldRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[64]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomFieldRequest.ProtoReflect.Descriptor instead.
func (*GetCustomFieldRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{64}
}

func (x *GetCustomFieldRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// GetCustomFieldResponse 获取自定义字段响应
type GetCustomFieldResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 自定义字段
	CustomField   *CustomField `protobuf:"bytes,1,opt,name=custom_field,json=customField,proto3" json:"custom_field,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCustomFieldResponse) Reset() {
	*x = GetCustomFieldResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[65]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustomFieldResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomFieldResponse) ProtoMessage() {}

func (x *GetCustomFieldResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[65]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomFieldResponse.ProtoReflect.Descriptor instead.
func (*GetCustomFieldResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{65}
}

func (x *GetCustomFieldResponse) GetCustomField() *CustomField {
	if x != nil {
		return x.CustomField
	}
	return nil
}

// ListCustomFieldsRequest 列出自定义字段请求
type ListCustomFieldsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 过滤条件
	Filter *ListCustomFieldsRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序
	Sorting *ListCustomFieldsRequest_Sorting `protobuf:"bytes,2,opt,name=sorting,proto3" json:"sorting,omitempty"`
	// 分页大小
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 分页令牌
	PageToken     string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomFieldsRequest) Reset() {
	*x = ListCustomFieldsRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[66]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomFieldsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomFieldsRequest) ProtoMessage() {}

func (x *ListCustomFieldsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[66]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomFieldsRequest.ProtoReflect.Descriptor instead.
func (*ListCustomFieldsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{66}
}

func (x *ListCustomFieldsRequest) GetFilter() *ListCustomFieldsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListCustomFieldsRequest) GetSorting() *ListCustomFieldsRequest_Sorting {
	if x != nil {
		return x.Sorting
	}
	return nil
}

func (x *ListCustomFieldsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListCustomFieldsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// ListCustomFieldsResponse 列出自定义字段响应
type ListCustomFieldsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 自定义字段列表
	CustomFields []*CustomField `protobuf:"bytes,1,rep,name=custom_fields,json=customFields,proto3" json:"custom_fields,omitempty"`
	// 下一页令牌
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// 总数量
	TotalSize     *int64 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3,oneof" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomFieldsResponse) Reset() {
	*x = ListCustomFieldsResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[67]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomFieldsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomFieldsResponse) ProtoMessage() {}

func (x *ListCustomFieldsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[67]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomFieldsResponse.ProtoReflect.Descriptor instead.
func (*ListCustomFieldsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{67}
}

func (x *ListCustomFieldsResponse) GetCustomFields() []*CustomField {
	if x != nil {
		return x.CustomFields
	}
	return nil
}

func (x *ListCustomFieldsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListCustomFieldsResponse) GetTotalSize() int64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

// UpdateCustomFieldRequest 更新自定义字段请求
type UpdateCustomFieldRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 字段名称
	Label *string `protobuf:"bytes,2,opt,name=label,proto3,oneof" json:"label,omitempty"`
	// 是否必填
	IsRequired *bool `protobuf:"varint,3,opt,name=is_required,json=isRequired,proto3,oneof" json:"is_required,omitempty"`
	// 状态
	State *CustomField_State `protobuf:"varint,4,opt,name=state,proto3,enum=backend.proto.customer.v2.CustomField_State,oneof" json:"state,omitempty"`
	// 默认值
	DefaultValue *CustomField_Value `protobuf:"bytes,5,opt,name=default_value,json=defaultValue,proto3,oneof" json:"default_value,omitempty"`
	// options
	Options []*CustomField_Option `protobuf:"bytes,6,rep,name=options,proto3" json:"options,omitempty"`
	// display order
	DisplayOrder *int32 `protobuf:"varint,7,opt,name=display_order,json=displayOrder,proto3,oneof" json:"display_order,omitempty"`
	// help text
	HelpText      *string `protobuf:"bytes,8,opt,name=help_text,json=helpText,proto3,oneof" json:"help_text,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCustomFieldRequest) Reset() {
	*x = UpdateCustomFieldRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[68]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCustomFieldRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomFieldRequest) ProtoMessage() {}

func (x *UpdateCustomFieldRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[68]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomFieldRequest.ProtoReflect.Descriptor instead.
func (*UpdateCustomFieldRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{68}
}

func (x *UpdateCustomFieldRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateCustomFieldRequest) GetLabel() string {
	if x != nil && x.Label != nil {
		return *x.Label
	}
	return ""
}

func (x *UpdateCustomFieldRequest) GetIsRequired() bool {
	if x != nil && x.IsRequired != nil {
		return *x.IsRequired
	}
	return false
}

func (x *UpdateCustomFieldRequest) GetState() CustomField_State {
	if x != nil && x.State != nil {
		return *x.State
	}
	return CustomField_STATE_UNSPECIFIED
}

func (x *UpdateCustomFieldRequest) GetDefaultValue() *CustomField_Value {
	if x != nil {
		return x.DefaultValue
	}
	return nil
}

func (x *UpdateCustomFieldRequest) GetOptions() []*CustomField_Option {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *UpdateCustomFieldRequest) GetDisplayOrder() int32 {
	if x != nil && x.DisplayOrder != nil {
		return *x.DisplayOrder
	}
	return 0
}

func (x *UpdateCustomFieldRequest) GetHelpText() string {
	if x != nil && x.HelpText != nil {
		return *x.HelpText
	}
	return ""
}

// UpdateCustomFieldResponse 更新自定义字段响应
type UpdateCustomFieldResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 自定义字段
	CustomField   *CustomField `protobuf:"bytes,1,opt,name=custom_field,json=customField,proto3" json:"custom_field,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCustomFieldResponse) Reset() {
	*x = UpdateCustomFieldResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[69]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCustomFieldResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomFieldResponse) ProtoMessage() {}

func (x *UpdateCustomFieldResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[69]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomFieldResponse.ProtoReflect.Descriptor instead.
func (*UpdateCustomFieldResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{69}
}

func (x *UpdateCustomFieldResponse) GetCustomField() *CustomField {
	if x != nil {
		return x.CustomField
	}
	return nil
}

// (-- api-linter: core::0234::request-parent-field=disabled
//
//	aip.dev/not-precedent: 无 parent 语义 --)
//
// (-- api-linter: core::0234::request-parent-reference=disabled
//
//	aip.dev/not-precedent: 无 parent 语义 --)
//
// (-- api-linter: core::0234::request-requests-field=disabled
//
//	aip.dev/not-precedent: 不复用 UpdateCustomFieldRequest 结构 --)
//
// BatchUpdateCustomFieldsRequest 批量更新自定义字段请求
type BatchUpdateCustomFieldsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 父级资源路径, 目前为 organization id, 做批量更新时加锁用
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// 批量更新请求列表
	Requests      []*UpdateCustomFieldRequest `protobuf:"bytes,2,rep,name=requests,proto3" json:"requests,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchUpdateCustomFieldsRequest) Reset() {
	*x = BatchUpdateCustomFieldsRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[70]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchUpdateCustomFieldsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateCustomFieldsRequest) ProtoMessage() {}

func (x *BatchUpdateCustomFieldsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[70]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateCustomFieldsRequest.ProtoReflect.Descriptor instead.
func (*BatchUpdateCustomFieldsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{70}
}

func (x *BatchUpdateCustomFieldsRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *BatchUpdateCustomFieldsRequest) GetRequests() []*UpdateCustomFieldRequest {
	if x != nil {
		return x.Requests
	}
	return nil
}

// BatchUpdateCustomFieldsResponse 批量更新自定义字段响应
type BatchUpdateCustomFieldsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 更新后的自定义字段列表
	CustomFields  []*CustomField `protobuf:"bytes,1,rep,name=custom_fields,json=customFields,proto3" json:"custom_fields,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchUpdateCustomFieldsResponse) Reset() {
	*x = BatchUpdateCustomFieldsResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[71]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchUpdateCustomFieldsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateCustomFieldsResponse) ProtoMessage() {}

func (x *BatchUpdateCustomFieldsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[71]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateCustomFieldsResponse.ProtoReflect.Descriptor instead.
func (*BatchUpdateCustomFieldsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{71}
}

func (x *BatchUpdateCustomFieldsResponse) GetCustomFields() []*CustomField {
	if x != nil {
		return x.CustomFields
	}
	return nil
}

// DeleteCustomFieldRequest 删除自定义字段请求
type DeleteCustomFieldRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 自定义字段ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCustomFieldRequest) Reset() {
	*x = DeleteCustomFieldRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[72]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCustomFieldRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCustomFieldRequest) ProtoMessage() {}

func (x *DeleteCustomFieldRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[72]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCustomFieldRequest.ProtoReflect.Descriptor instead.
func (*DeleteCustomFieldRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{72}
}

func (x *DeleteCustomFieldRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// DeleteCustomFieldResponse 删除自定义字段响应
type DeleteCustomFieldResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCustomFieldResponse) Reset() {
	*x = DeleteCustomFieldResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[73]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCustomFieldResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCustomFieldResponse) ProtoMessage() {}

func (x *DeleteCustomFieldResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[73]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCustomFieldResponse.ProtoReflect.Descriptor instead.
func (*DeleteCustomFieldResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{73}
}

// ==================== Unified Metadata Request/Response Messages ====================
// CreateMetadataRequest 创建统一元数据请求
type CreateMetadataRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 组织引用
	Organization *OrganizationRef `protobuf:"bytes,1,opt,name=organization,proto3" json:"organization,omitempty"`
	// 客户类型
	CustomerType CustomerType `protobuf:"varint,2,opt,name=customer_type,json=customerType,proto3,enum=backend.proto.customer.v2.CustomerType" json:"customer_type,omitempty"`
	// 元数据名称
	GivenName string `protobuf:"bytes,3,opt,name=given_name,json=givenName,proto3" json:"given_name,omitempty"`
	// 元数据姓氏
	FamilyName string `protobuf:"bytes,4,opt,name=family_name,json=familyName,proto3" json:"family_name,omitempty"`
	// 元数据自定义字段
	CustomFields *structpb.Struct `protobuf:"bytes,5,opt,name=custom_fields,json=customFields,proto3,oneof" json:"custom_fields,omitempty"`
	// 元数据生命周期ID
	LifecycleId *int64 `protobuf:"varint,6,opt,name=lifecycle_id,json=lifecycleId,proto3,oneof" json:"lifecycle_id,omitempty"`
	// 负责人员工ID
	OwnerStaffId *int64 `protobuf:"varint,7,opt,name=owner_staff_id,json=ownerStaffId,proto3,oneof" json:"owner_staff_id,omitempty"`
	// 行动状态ID
	ActionStateId *int64 `protobuf:"varint,8,opt,name=action_state_id,json=actionStateId,proto3,oneof" json:"action_state_id,omitempty"`
	// 头像路径
	AvatarPath *string `protobuf:"bytes,9,opt,name=avatar_path,json=avatarPath,proto3,oneof" json:"avatar_path,omitempty"`
	// 推荐来源ID
	ReferralSourceId *int64 `protobuf:"varint,10,opt,name=referral_source_id,json=referralSourceId,proto3,oneof" json:"referral_source_id,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CreateMetadataRequest) Reset() {
	*x = CreateMetadataRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[74]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMetadataRequest) ProtoMessage() {}

func (x *CreateMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[74]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMetadataRequest.ProtoReflect.Descriptor instead.
func (*CreateMetadataRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{74}
}

func (x *CreateMetadataRequest) GetOrganization() *OrganizationRef {
	if x != nil {
		return x.Organization
	}
	return nil
}

func (x *CreateMetadataRequest) GetCustomerType() CustomerType {
	if x != nil {
		return x.CustomerType
	}
	return CustomerType_CUSTOMER_TYPE_UNSPECIFIED
}

func (x *CreateMetadataRequest) GetGivenName() string {
	if x != nil {
		return x.GivenName
	}
	return ""
}

func (x *CreateMetadataRequest) GetFamilyName() string {
	if x != nil {
		return x.FamilyName
	}
	return ""
}

func (x *CreateMetadataRequest) GetCustomFields() *structpb.Struct {
	if x != nil {
		return x.CustomFields
	}
	return nil
}

func (x *CreateMetadataRequest) GetLifecycleId() int64 {
	if x != nil && x.LifecycleId != nil {
		return *x.LifecycleId
	}
	return 0
}

func (x *CreateMetadataRequest) GetOwnerStaffId() int64 {
	if x != nil && x.OwnerStaffId != nil {
		return *x.OwnerStaffId
	}
	return 0
}

func (x *CreateMetadataRequest) GetActionStateId() int64 {
	if x != nil && x.ActionStateId != nil {
		return *x.ActionStateId
	}
	return 0
}

func (x *CreateMetadataRequest) GetAvatarPath() string {
	if x != nil && x.AvatarPath != nil {
		return *x.AvatarPath
	}
	return ""
}

func (x *CreateMetadataRequest) GetReferralSourceId() int64 {
	if x != nil && x.ReferralSourceId != nil {
		return *x.ReferralSourceId
	}
	return 0
}

// CreateMetadataResponse 创建统一元数据响应
type CreateMetadataResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 元数据
	Metadata      *Metadata `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateMetadataResponse) Reset() {
	*x = CreateMetadataResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[75]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateMetadataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMetadataResponse) ProtoMessage() {}

func (x *CreateMetadataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[75]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMetadataResponse.ProtoReflect.Descriptor instead.
func (*CreateMetadataResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{75}
}

func (x *CreateMetadataResponse) GetMetadata() *Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// GetMetadataRequest 获取统一元数据请求
type GetMetadataRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 元数据ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMetadataRequest) Reset() {
	*x = GetMetadataRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[76]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMetadataRequest) ProtoMessage() {}

func (x *GetMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[76]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMetadataRequest.ProtoReflect.Descriptor instead.
func (*GetMetadataRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{76}
}

func (x *GetMetadataRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// GetMetadataResponse 获取统一元数据响应
type GetMetadataResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 元数据
	Metadata      *Metadata `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMetadataResponse) Reset() {
	*x = GetMetadataResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[77]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMetadataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMetadataResponse) ProtoMessage() {}

func (x *GetMetadataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[77]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMetadataResponse.ProtoReflect.Descriptor instead.
func (*GetMetadataResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{77}
}

func (x *GetMetadataResponse) GetMetadata() *Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// ListMetadataRequest 列出统一元数据请求
type ListMetadataRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 过滤条件
	Filter *ListMetadataRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序
	Sorting *ListMetadataRequest_Sorting `protobuf:"bytes,2,opt,name=sorting,proto3" json:"sorting,omitempty"`
	// 分页
	// 分页大小
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 分页令牌
	PageToken     string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMetadataRequest) Reset() {
	*x = ListMetadataRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[78]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMetadataRequest) ProtoMessage() {}

func (x *ListMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[78]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMetadataRequest.ProtoReflect.Descriptor instead.
func (*ListMetadataRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{78}
}

func (x *ListMetadataRequest) GetFilter() *ListMetadataRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListMetadataRequest) GetSorting() *ListMetadataRequest_Sorting {
	if x != nil {
		return x.Sorting
	}
	return nil
}

func (x *ListMetadataRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListMetadataRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// ListMetadataResponse 列出统一元数据响应
type ListMetadataResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 元数据列表
	Metadata []*Metadata `protobuf:"bytes,1,rep,name=metadata,proto3" json:"metadata,omitempty"`
	// 下一页令牌
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// 总数量
	TotalSize     *int64 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3,oneof" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMetadataResponse) Reset() {
	*x = ListMetadataResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[79]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMetadataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMetadataResponse) ProtoMessage() {}

func (x *ListMetadataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[79]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMetadataResponse.ProtoReflect.Descriptor instead.
func (*ListMetadataResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{79}
}

func (x *ListMetadataResponse) GetMetadata() []*Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ListMetadataResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListMetadataResponse) GetTotalSize() int64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

// UpdateMetadataRequest 更新统一元数据请求
type UpdateMetadataRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 客户类型 (用于路由到正确的逻辑层)
	CustomerType CustomerType `protobuf:"varint,2,opt,name=customer_type,json=customerType,proto3,enum=backend.proto.customer.v2.CustomerType" json:"customer_type,omitempty"`
	// 元数据名称
	GivenName *string `protobuf:"bytes,3,opt,name=given_name,json=givenName,proto3,oneof" json:"given_name,omitempty"`
	// 元数据姓氏
	FamilyName *string `protobuf:"bytes,4,opt,name=family_name,json=familyName,proto3,oneof" json:"family_name,omitempty"`
	// 元数据自定义字段
	CustomFields *structpb.Struct `protobuf:"bytes,5,opt,name=custom_fields,json=customFields,proto3,oneof" json:"custom_fields,omitempty"`
	// 元数据生命周期ID
	LifecycleId *int64 `protobuf:"varint,6,opt,name=lifecycle_id,json=lifecycleId,proto3,oneof" json:"lifecycle_id,omitempty"`
	// 负责人员工ID
	OwnerStaffId *int64 `protobuf:"varint,7,opt,name=owner_staff_id,json=ownerStaffId,proto3,oneof" json:"owner_staff_id,omitempty"`
	// 行动状态ID
	ActionStateId *int64 `protobuf:"varint,8,opt,name=action_state_id,json=actionStateId,proto3,oneof" json:"action_state_id,omitempty"`
	// 头像路径
	AvatarPath *string `protobuf:"bytes,9,opt,name=avatar_path,json=avatarPath,proto3,oneof" json:"avatar_path,omitempty"`
	// 推荐来源ID
	ReferralSourceId *int64 `protobuf:"varint,10,opt,name=referral_source_id,json=referralSourceId,proto3,oneof" json:"referral_source_id,omitempty"`
	// state
	State         *Metadata_State `protobuf:"varint,11,opt,name=state,proto3,enum=backend.proto.customer.v2.Metadata_State,oneof" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateMetadataRequest) Reset() {
	*x = UpdateMetadataRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[80]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMetadataRequest) ProtoMessage() {}

func (x *UpdateMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[80]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMetadataRequest.ProtoReflect.Descriptor instead.
func (*UpdateMetadataRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{80}
}

func (x *UpdateMetadataRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateMetadataRequest) GetCustomerType() CustomerType {
	if x != nil {
		return x.CustomerType
	}
	return CustomerType_CUSTOMER_TYPE_UNSPECIFIED
}

func (x *UpdateMetadataRequest) GetGivenName() string {
	if x != nil && x.GivenName != nil {
		return *x.GivenName
	}
	return ""
}

func (x *UpdateMetadataRequest) GetFamilyName() string {
	if x != nil && x.FamilyName != nil {
		return *x.FamilyName
	}
	return ""
}

func (x *UpdateMetadataRequest) GetCustomFields() *structpb.Struct {
	if x != nil {
		return x.CustomFields
	}
	return nil
}

func (x *UpdateMetadataRequest) GetLifecycleId() int64 {
	if x != nil && x.LifecycleId != nil {
		return *x.LifecycleId
	}
	return 0
}

func (x *UpdateMetadataRequest) GetOwnerStaffId() int64 {
	if x != nil && x.OwnerStaffId != nil {
		return *x.OwnerStaffId
	}
	return 0
}

func (x *UpdateMetadataRequest) GetActionStateId() int64 {
	if x != nil && x.ActionStateId != nil {
		return *x.ActionStateId
	}
	return 0
}

func (x *UpdateMetadataRequest) GetAvatarPath() string {
	if x != nil && x.AvatarPath != nil {
		return *x.AvatarPath
	}
	return ""
}

func (x *UpdateMetadataRequest) GetReferralSourceId() int64 {
	if x != nil && x.ReferralSourceId != nil {
		return *x.ReferralSourceId
	}
	return 0
}

func (x *UpdateMetadataRequest) GetState() Metadata_State {
	if x != nil && x.State != nil {
		return *x.State
	}
	return Metadata_STATE_UNSPECIFIED
}

// UpdateMetadataResponse 更新统一元数据响应
type UpdateMetadataResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 元数据
	Metadata      *Metadata `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateMetadataResponse) Reset() {
	*x = UpdateMetadataResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[81]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateMetadataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMetadataResponse) ProtoMessage() {}

func (x *UpdateMetadataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[81]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMetadataResponse.ProtoReflect.Descriptor instead.
func (*UpdateMetadataResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{81}
}

func (x *UpdateMetadataResponse) GetMetadata() *Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// DeleteMetadataRequest 删除统一元数据请求
type DeleteMetadataRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 元数据ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 客户类型 (用于路由到正确的逻辑层)
	CustomerType  CustomerType `protobuf:"varint,2,opt,name=customer_type,json=customerType,proto3,enum=backend.proto.customer.v2.CustomerType" json:"customer_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteMetadataRequest) Reset() {
	*x = DeleteMetadataRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[82]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMetadataRequest) ProtoMessage() {}

func (x *DeleteMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[82]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMetadataRequest.ProtoReflect.Descriptor instead.
func (*DeleteMetadataRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{82}
}

func (x *DeleteMetadataRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteMetadataRequest) GetCustomerType() CustomerType {
	if x != nil {
		return x.CustomerType
	}
	return CustomerType_CUSTOMER_TYPE_UNSPECIFIED
}

// DeleteMetadataResponse 删除统一元数据响应
type DeleteMetadataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteMetadataResponse) Reset() {
	*x = DeleteMetadataResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[83]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteMetadataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMetadataResponse) ProtoMessage() {}

func (x *DeleteMetadataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[83]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMetadataResponse.ProtoReflect.Descriptor instead.
func (*DeleteMetadataResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{83}
}

// CreateMetadataAggregateRequest 聚合创建元数据及相关实体请求
type CreateMetadataAggregateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 组织引用
	Organization *OrganizationRef `protobuf:"bytes,1,opt,name=organization,proto3" json:"organization,omitempty"`
	// 客户类型
	CustomerType CustomerType `protobuf:"varint,2,opt,name=customer_type,json=customerType,proto3,enum=backend.proto.customer.v2.CustomerType" json:"customer_type,omitempty"`
	// 元数据信息
	Metadata *Metadata `protobuf:"bytes,3,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// 联系人列表
	Contacts []*Contact `protobuf:"bytes,4,rep,name=contacts,proto3" json:"contacts,omitempty"`
	// 地址列表
	Addresses []*Address `protobuf:"bytes,5,rep,name=addresses,proto3" json:"addresses,omitempty"`
	// 客户相关数据 (仅当customer_type=CUSTOMER时有效)
	CustomerRelatedData *CustomerRelatedData `protobuf:"bytes,6,opt,name=customer_related_data,json=customerRelatedData,proto3,oneof" json:"customer_related_data,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *CreateMetadataAggregateRequest) Reset() {
	*x = CreateMetadataAggregateRequest{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[84]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateMetadataAggregateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMetadataAggregateRequest) ProtoMessage() {}

func (x *CreateMetadataAggregateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[84]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMetadataAggregateRequest.ProtoReflect.Descriptor instead.
func (*CreateMetadataAggregateRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{84}
}

func (x *CreateMetadataAggregateRequest) GetOrganization() *OrganizationRef {
	if x != nil {
		return x.Organization
	}
	return nil
}

func (x *CreateMetadataAggregateRequest) GetCustomerType() CustomerType {
	if x != nil {
		return x.CustomerType
	}
	return CustomerType_CUSTOMER_TYPE_UNSPECIFIED
}

func (x *CreateMetadataAggregateRequest) GetMetadata() *Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *CreateMetadataAggregateRequest) GetContacts() []*Contact {
	if x != nil {
		return x.Contacts
	}
	return nil
}

func (x *CreateMetadataAggregateRequest) GetAddresses() []*Address {
	if x != nil {
		return x.Addresses
	}
	return nil
}

func (x *CreateMetadataAggregateRequest) GetCustomerRelatedData() *CustomerRelatedData {
	if x != nil {
		return x.CustomerRelatedData
	}
	return nil
}

// CreateMetadataAggregateResponse 聚合创建元数据及相关实体响应
type CreateMetadataAggregateResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 元数据聚合
	MetadataAggregate *MetadataAggregate `protobuf:"bytes,1,opt,name=metadata_aggregate,json=metadataAggregate,proto3" json:"metadata_aggregate,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CreateMetadataAggregateResponse) Reset() {
	*x = CreateMetadataAggregateResponse{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[85]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateMetadataAggregateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMetadataAggregateResponse) ProtoMessage() {}

func (x *CreateMetadataAggregateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[85]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMetadataAggregateResponse.ProtoReflect.Descriptor instead.
func (*CreateMetadataAggregateResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{85}
}

func (x *CreateMetadataAggregateResponse) GetMetadataAggregate() *MetadataAggregate {
	if x != nil {
		return x.MetadataAggregate
	}
	return nil
}

// filter
type ListCustomersRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// customer id list
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// 组织引用
	Organizations []*OrganizationRef `protobuf:"bytes,2,rep,name=organizations,proto3" json:"organizations,omitempty"`
	// 状态
	// 如果states为空, 则返回所有状态的客户, 没有默认值
	States []Customer_State `protobuf:"varint,3,rep,packed,name=states,proto3,enum=backend.proto.customer.v2.Customer_State" json:"states,omitempty"`
	// 生命周期ID
	LifecycleIds []int64 `protobuf:"varint,4,rep,packed,name=lifecycle_ids,json=lifecycleIds,proto3" json:"lifecycle_ids,omitempty"`
	// 负责人员工ID
	OwnerStaffIds []int64 `protobuf:"varint,5,rep,packed,name=owner_staff_ids,json=ownerStaffIds,proto3" json:"owner_staff_ids,omitempty"`
	// 推荐来源ID
	ReferralSourceIds []int64 `protobuf:"varint,6,rep,packed,name=referral_source_ids,json=referralSourceIds,proto3" json:"referral_source_ids,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ListCustomersRequest_Filter) Reset() {
	*x = ListCustomersRequest_Filter{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[86]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomersRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomersRequest_Filter) ProtoMessage() {}

func (x *ListCustomersRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[86]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomersRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListCustomersRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{4, 0}
}

func (x *ListCustomersRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListCustomersRequest_Filter) GetOrganizations() []*OrganizationRef {
	if x != nil {
		return x.Organizations
	}
	return nil
}

func (x *ListCustomersRequest_Filter) GetStates() []Customer_State {
	if x != nil {
		return x.States
	}
	return nil
}

func (x *ListCustomersRequest_Filter) GetLifecycleIds() []int64 {
	if x != nil {
		return x.LifecycleIds
	}
	return nil
}

func (x *ListCustomersRequest_Filter) GetOwnerStaffIds() []int64 {
	if x != nil {
		return x.OwnerStaffIds
	}
	return nil
}

func (x *ListCustomersRequest_Filter) GetReferralSourceIds() []int64 {
	if x != nil {
		return x.ReferralSourceIds
	}
	return nil
}

// sorting
type ListCustomersRequest_Sorting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 排序字段
	Field ListCustomersRequest_Sorting_Field `protobuf:"varint,1,opt,name=field,proto3,enum=backend.proto.customer.v2.ListCustomersRequest_Sorting_Field" json:"field,omitempty"`
	// 排序方向
	Direction     Direction `protobuf:"varint,2,opt,name=direction,proto3,enum=backend.proto.customer.v2.Direction" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomersRequest_Sorting) Reset() {
	*x = ListCustomersRequest_Sorting{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[87]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomersRequest_Sorting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomersRequest_Sorting) ProtoMessage() {}

func (x *ListCustomersRequest_Sorting) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[87]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomersRequest_Sorting.ProtoReflect.Descriptor instead.
func (*ListCustomersRequest_Sorting) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{4, 1}
}

func (x *ListCustomersRequest_Sorting) GetField() ListCustomersRequest_Sorting_Field {
	if x != nil {
		return x.Field
	}
	return ListCustomersRequest_Sorting_FIELD_UNSPECIFIED
}

func (x *ListCustomersRequest_Sorting) GetDirection() Direction {
	if x != nil {
		return x.Direction
	}
	return Direction_DIRECTION_UNSPECIFIED
}

// filter
type ListContactsRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 联系人ID列表
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// 所属客户ID列表
	CustomerIds []int64 `protobuf:"varint,2,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	// 状态
	// 如果states为空, 则返回所有状态的联系人, 没有默认值
	States []Contact_State `protobuf:"varint,3,rep,packed,name=states,proto3,enum=backend.proto.customer.v2.Contact_State" json:"states,omitempty"`
	// 电话号码批量搜索
	Phones []*phone_number.PhoneNumber `protobuf:"bytes,4,rep,name=phones,proto3" json:"phones,omitempty"`
	// Emails
	Emails []string `protobuf:"bytes,5,rep,name=emails,proto3" json:"emails,omitempty"`
	// organizations, 非ids/customer_ids查询建议都传，防止扫全表
	Organizations []*OrganizationRef `protobuf:"bytes,6,rep,name=organizations,proto3" json:"organizations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListContactsRequest_Filter) Reset() {
	*x = ListContactsRequest_Filter{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[88]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListContactsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListContactsRequest_Filter) ProtoMessage() {}

func (x *ListContactsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[88]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListContactsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListContactsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{16, 0}
}

func (x *ListContactsRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListContactsRequest_Filter) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

func (x *ListContactsRequest_Filter) GetStates() []Contact_State {
	if x != nil {
		return x.States
	}
	return nil
}

func (x *ListContactsRequest_Filter) GetPhones() []*phone_number.PhoneNumber {
	if x != nil {
		return x.Phones
	}
	return nil
}

func (x *ListContactsRequest_Filter) GetEmails() []string {
	if x != nil {
		return x.Emails
	}
	return nil
}

func (x *ListContactsRequest_Filter) GetOrganizations() []*OrganizationRef {
	if x != nil {
		return x.Organizations
	}
	return nil
}

// sorting
type ListContactsRequest_Sorting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 排序字段
	Field ListContactsRequest_Sorting_Field `protobuf:"varint,1,opt,name=field,proto3,enum=backend.proto.customer.v2.ListContactsRequest_Sorting_Field" json:"field,omitempty"`
	// 排序方向
	Direction     Direction `protobuf:"varint,2,opt,name=direction,proto3,enum=backend.proto.customer.v2.Direction" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListContactsRequest_Sorting) Reset() {
	*x = ListContactsRequest_Sorting{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[89]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListContactsRequest_Sorting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListContactsRequest_Sorting) ProtoMessage() {}

func (x *ListContactsRequest_Sorting) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[89]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListContactsRequest_Sorting.ProtoReflect.Descriptor instead.
func (*ListContactsRequest_Sorting) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{16, 1}
}

func (x *ListContactsRequest_Sorting) GetField() ListContactsRequest_Sorting_Field {
	if x != nil {
		return x.Field
	}
	return ListContactsRequest_Sorting_FIELD_UNSPECIFIED
}

func (x *ListContactsRequest_Sorting) GetDirection() Direction {
	if x != nil {
		return x.Direction
	}
	return Direction_DIRECTION_UNSPECIFIED
}

// filter
type ListCustomerRelatedDataRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户相关数据ID列表
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// 客户ID列表
	CustomerIds []int64 `protobuf:"varint,2,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	// 组织引用
	Organizations []*OrganizationRef `protobuf:"bytes,3,rep,name=organizations,proto3" json:"organizations,omitempty"`
	// 商家ID列表
	BusinessIds []int32 `protobuf:"varint,4,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// 公司ID列表
	CompanyIds []int64 `protobuf:"varint,5,rep,packed,name=company_ids,json=companyIds,proto3" json:"company_ids,omitempty"`
	// 状态列表
	States []CustomerRelatedData_State `protobuf:"varint,6,rep,packed,name=states,proto3,enum=backend.proto.customer.v2.CustomerRelatedData_State" json:"states,omitempty"`
	// CustomerCodes
	CustomerCodes []string `protobuf:"bytes,7,rep,name=customer_codes,json=customerCodes,proto3" json:"customer_codes,omitempty"`
	// AccountIDs
	AccountIds    []int64 `protobuf:"varint,8,rep,packed,name=account_ids,json=accountIds,proto3" json:"account_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomerRelatedDataRequest_Filter) Reset() {
	*x = ListCustomerRelatedDataRequest_Filter{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[90]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomerRelatedDataRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerRelatedDataRequest_Filter) ProtoMessage() {}

func (x *ListCustomerRelatedDataRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[90]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerRelatedDataRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListCustomerRelatedDataRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{26, 0}
}

func (x *ListCustomerRelatedDataRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListCustomerRelatedDataRequest_Filter) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

func (x *ListCustomerRelatedDataRequest_Filter) GetOrganizations() []*OrganizationRef {
	if x != nil {
		return x.Organizations
	}
	return nil
}

func (x *ListCustomerRelatedDataRequest_Filter) GetBusinessIds() []int32 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *ListCustomerRelatedDataRequest_Filter) GetCompanyIds() []int64 {
	if x != nil {
		return x.CompanyIds
	}
	return nil
}

func (x *ListCustomerRelatedDataRequest_Filter) GetStates() []CustomerRelatedData_State {
	if x != nil {
		return x.States
	}
	return nil
}

func (x *ListCustomerRelatedDataRequest_Filter) GetCustomerCodes() []string {
	if x != nil {
		return x.CustomerCodes
	}
	return nil
}

func (x *ListCustomerRelatedDataRequest_Filter) GetAccountIds() []int64 {
	if x != nil {
		return x.AccountIds
	}
	return nil
}

// sorting
type ListCustomerRelatedDataRequest_Sorting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 排序字段
	Field ListCustomerRelatedDataRequest_Sorting_Field `protobuf:"varint,1,opt,name=field,proto3,enum=backend.proto.customer.v2.ListCustomerRelatedDataRequest_Sorting_Field" json:"field,omitempty"`
	// 排序方向
	Direction     Direction `protobuf:"varint,2,opt,name=direction,proto3,enum=backend.proto.customer.v2.Direction" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomerRelatedDataRequest_Sorting) Reset() {
	*x = ListCustomerRelatedDataRequest_Sorting{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[91]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomerRelatedDataRequest_Sorting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerRelatedDataRequest_Sorting) ProtoMessage() {}

func (x *ListCustomerRelatedDataRequest_Sorting) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[91]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerRelatedDataRequest_Sorting.ProtoReflect.Descriptor instead.
func (*ListCustomerRelatedDataRequest_Sorting) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{26, 1}
}

func (x *ListCustomerRelatedDataRequest_Sorting) GetField() ListCustomerRelatedDataRequest_Sorting_Field {
	if x != nil {
		return x.Field
	}
	return ListCustomerRelatedDataRequest_Sorting_FIELD_UNSPECIFIED
}

func (x *ListCustomerRelatedDataRequest_Sorting) GetDirection() Direction {
	if x != nil {
		return x.Direction
	}
	return Direction_DIRECTION_UNSPECIFIED
}

// 过滤条件
type ListContactTagsRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 标签ID列表
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// 组织引用 (必填, 如果为空会报错)
	Organizations []*OrganizationRef `protobuf:"bytes,2,rep,name=organizations,proto3" json:"organizations,omitempty"`
	// name, is fuzzy search
	Name *string `protobuf:"bytes,3,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// states
	States        []ContactTag_State `protobuf:"varint,4,rep,packed,name=states,proto3,enum=backend.proto.customer.v2.ContactTag_State" json:"states,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListContactTagsRequest_Filter) Reset() {
	*x = ListContactTagsRequest_Filter{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[92]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListContactTagsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListContactTagsRequest_Filter) ProtoMessage() {}

func (x *ListContactTagsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[92]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListContactTagsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListContactTagsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{36, 0}
}

func (x *ListContactTagsRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListContactTagsRequest_Filter) GetOrganizations() []*OrganizationRef {
	if x != nil {
		return x.Organizations
	}
	return nil
}

func (x *ListContactTagsRequest_Filter) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *ListContactTagsRequest_Filter) GetStates() []ContactTag_State {
	if x != nil {
		return x.States
	}
	return nil
}

// sorting
type ListContactTagsRequest_Sorting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 排序字段
	Field ListContactTagsRequest_Sorting_Field `protobuf:"varint,1,opt,name=field,proto3,enum=backend.proto.customer.v2.ListContactTagsRequest_Sorting_Field" json:"field,omitempty"`
	// 排序方向
	Direction     Direction `protobuf:"varint,2,opt,name=direction,proto3,enum=backend.proto.customer.v2.Direction" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListContactTagsRequest_Sorting) Reset() {
	*x = ListContactTagsRequest_Sorting{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[93]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListContactTagsRequest_Sorting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListContactTagsRequest_Sorting) ProtoMessage() {}

func (x *ListContactTagsRequest_Sorting) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[93]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListContactTagsRequest_Sorting.ProtoReflect.Descriptor instead.
func (*ListContactTagsRequest_Sorting) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{36, 1}
}

func (x *ListContactTagsRequest_Sorting) GetField() ListContactTagsRequest_Sorting_Field {
	if x != nil {
		return x.Field
	}
	return ListContactTagsRequest_Sorting_FIELD_UNSPECIFIED
}

func (x *ListContactTagsRequest_Sorting) GetDirection() Direction {
	if x != nil {
		return x.Direction
	}
	return Direction_DIRECTION_UNSPECIFIED
}

// filter
type ListLeadsRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 线索ID列表
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// 组织引用
	Organizations []*OrganizationRef `protobuf:"bytes,2,rep,name=organizations,proto3" json:"organizations,omitempty"`
	// 状态
	// 如果states为空, 则返回所有状态的lead, 没有默认值
	States []Lead_State `protobuf:"varint,3,rep,packed,name=states,proto3,enum=backend.proto.customer.v2.Lead_State" json:"states,omitempty"`
	// 生命周期ID
	LifecycleIds []int64 `protobuf:"varint,4,rep,packed,name=lifecycle_ids,json=lifecycleIds,proto3" json:"lifecycle_ids,omitempty"`
	// 负责人员工ID
	OwnerStaffIds []int64 `protobuf:"varint,5,rep,packed,name=owner_staff_ids,json=ownerStaffIds,proto3" json:"owner_staff_ids,omitempty"`
	// Converted Customer IDs
	ConvertedCustomerIds []int64 `protobuf:"varint,6,rep,packed,name=converted_customer_ids,json=convertedCustomerIds,proto3" json:"converted_customer_ids,omitempty"`
	// is already converted
	IsConverted   *bool `protobuf:"varint,7,opt,name=is_converted,json=isConverted,proto3,oneof" json:"is_converted,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListLeadsRequest_Filter) Reset() {
	*x = ListLeadsRequest_Filter{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[94]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListLeadsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLeadsRequest_Filter) ProtoMessage() {}

func (x *ListLeadsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[94]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLeadsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListLeadsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{46, 0}
}

func (x *ListLeadsRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListLeadsRequest_Filter) GetOrganizations() []*OrganizationRef {
	if x != nil {
		return x.Organizations
	}
	return nil
}

func (x *ListLeadsRequest_Filter) GetStates() []Lead_State {
	if x != nil {
		return x.States
	}
	return nil
}

func (x *ListLeadsRequest_Filter) GetLifecycleIds() []int64 {
	if x != nil {
		return x.LifecycleIds
	}
	return nil
}

func (x *ListLeadsRequest_Filter) GetOwnerStaffIds() []int64 {
	if x != nil {
		return x.OwnerStaffIds
	}
	return nil
}

func (x *ListLeadsRequest_Filter) GetConvertedCustomerIds() []int64 {
	if x != nil {
		return x.ConvertedCustomerIds
	}
	return nil
}

func (x *ListLeadsRequest_Filter) GetIsConverted() bool {
	if x != nil && x.IsConverted != nil {
		return *x.IsConverted
	}
	return false
}

// sorting
type ListLeadsRequest_Sorting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 排序字段
	Field ListLeadsRequest_Sorting_Field `protobuf:"varint,1,opt,name=field,proto3,enum=backend.proto.customer.v2.ListLeadsRequest_Sorting_Field" json:"field,omitempty"`
	// 排序方向
	Direction     Direction `protobuf:"varint,2,opt,name=direction,proto3,enum=backend.proto.customer.v2.Direction" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListLeadsRequest_Sorting) Reset() {
	*x = ListLeadsRequest_Sorting{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[95]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListLeadsRequest_Sorting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLeadsRequest_Sorting) ProtoMessage() {}

func (x *ListLeadsRequest_Sorting) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[95]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLeadsRequest_Sorting.ProtoReflect.Descriptor instead.
func (*ListLeadsRequest_Sorting) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{46, 1}
}

func (x *ListLeadsRequest_Sorting) GetField() ListLeadsRequest_Sorting_Field {
	if x != nil {
		return x.Field
	}
	return ListLeadsRequest_Sorting_FIELD_UNSPECIFIED
}

func (x *ListLeadsRequest_Sorting) GetDirection() Direction {
	if x != nil {
		return x.Direction
	}
	return Direction_DIRECTION_UNSPECIFIED
}

// filter
type ListAddressesRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 地址ID列表
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// 所有者ID
	CustomerIds []int64 `protobuf:"varint,2,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	// 如果states为空, 则返回所有状态的地址, 没有默认值
	States []Address_State `protobuf:"varint,3,rep,packed,name=states,proto3,enum=backend.proto.customer.v2.Address_State" json:"states,omitempty"`
	// 组织引用
	Organizations []*OrganizationRef `protobuf:"bytes,4,rep,name=organizations,proto3" json:"organizations,omitempty"`
	// types
	Types         []Address_Type `protobuf:"varint,5,rep,packed,name=types,proto3,enum=backend.proto.customer.v2.Address_Type" json:"types,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAddressesRequest_Filter) Reset() {
	*x = ListAddressesRequest_Filter{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[96]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAddressesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAddressesRequest_Filter) ProtoMessage() {}

func (x *ListAddressesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[96]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAddressesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListAddressesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{56, 0}
}

func (x *ListAddressesRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListAddressesRequest_Filter) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

func (x *ListAddressesRequest_Filter) GetStates() []Address_State {
	if x != nil {
		return x.States
	}
	return nil
}

func (x *ListAddressesRequest_Filter) GetOrganizations() []*OrganizationRef {
	if x != nil {
		return x.Organizations
	}
	return nil
}

func (x *ListAddressesRequest_Filter) GetTypes() []Address_Type {
	if x != nil {
		return x.Types
	}
	return nil
}

// sorting
type ListAddressesRequest_Sorting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 排序字段
	Field ListAddressesRequest_Sorting_Field `protobuf:"varint,1,opt,name=field,proto3,enum=backend.proto.customer.v2.ListAddressesRequest_Sorting_Field" json:"field,omitempty"`
	// 排序方向
	Direction     Direction `protobuf:"varint,2,opt,name=direction,proto3,enum=backend.proto.customer.v2.Direction" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAddressesRequest_Sorting) Reset() {
	*x = ListAddressesRequest_Sorting{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[97]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAddressesRequest_Sorting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAddressesRequest_Sorting) ProtoMessage() {}

func (x *ListAddressesRequest_Sorting) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[97]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAddressesRequest_Sorting.ProtoReflect.Descriptor instead.
func (*ListAddressesRequest_Sorting) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{56, 1}
}

func (x *ListAddressesRequest_Sorting) GetField() ListAddressesRequest_Sorting_Field {
	if x != nil {
		return x.Field
	}
	return ListAddressesRequest_Sorting_FIELD_UNSPECIFIED
}

func (x *ListAddressesRequest_Sorting) GetDirection() Direction {
	if x != nil {
		return x.Direction
	}
	return Direction_DIRECTION_UNSPECIFIED
}

// filter
type ListCustomFieldsRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 自定义字段ID列表
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// organization
	Organizations []*OrganizationRef `protobuf:"bytes,2,rep,name=organizations,proto3" json:"organizations,omitempty"`
	// 实体所有者
	AssociationTypes []CustomField_AssociationType `protobuf:"varint,3,rep,packed,name=association_types,json=associationTypes,proto3,enum=backend.proto.customer.v2.CustomField_AssociationType" json:"association_types,omitempty"`
	// 字段类型
	Types []CustomField_Type `protobuf:"varint,4,rep,packed,name=types,proto3,enum=backend.proto.customer.v2.CustomField_Type" json:"types,omitempty"`
	// 是否必填
	IsRequired *bool `protobuf:"varint,5,opt,name=is_required,json=isRequired,proto3,oneof" json:"is_required,omitempty"`
	// 状态
	States        []CustomField_State `protobuf:"varint,6,rep,packed,name=states,proto3,enum=backend.proto.customer.v2.CustomField_State" json:"states,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomFieldsRequest_Filter) Reset() {
	*x = ListCustomFieldsRequest_Filter{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[98]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomFieldsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomFieldsRequest_Filter) ProtoMessage() {}

func (x *ListCustomFieldsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[98]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomFieldsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListCustomFieldsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{66, 0}
}

func (x *ListCustomFieldsRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListCustomFieldsRequest_Filter) GetOrganizations() []*OrganizationRef {
	if x != nil {
		return x.Organizations
	}
	return nil
}

func (x *ListCustomFieldsRequest_Filter) GetAssociationTypes() []CustomField_AssociationType {
	if x != nil {
		return x.AssociationTypes
	}
	return nil
}

func (x *ListCustomFieldsRequest_Filter) GetTypes() []CustomField_Type {
	if x != nil {
		return x.Types
	}
	return nil
}

func (x *ListCustomFieldsRequest_Filter) GetIsRequired() bool {
	if x != nil && x.IsRequired != nil {
		return *x.IsRequired
	}
	return false
}

func (x *ListCustomFieldsRequest_Filter) GetStates() []CustomField_State {
	if x != nil {
		return x.States
	}
	return nil
}

// sorting
type ListCustomFieldsRequest_Sorting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 排序字段
	Field ListCustomFieldsRequest_Sorting_Field `protobuf:"varint,1,opt,name=field,proto3,enum=backend.proto.customer.v2.ListCustomFieldsRequest_Sorting_Field" json:"field,omitempty"`
	// 排序方向
	Direction     Direction `protobuf:"varint,2,opt,name=direction,proto3,enum=backend.proto.customer.v2.Direction" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomFieldsRequest_Sorting) Reset() {
	*x = ListCustomFieldsRequest_Sorting{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[99]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomFieldsRequest_Sorting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomFieldsRequest_Sorting) ProtoMessage() {}

func (x *ListCustomFieldsRequest_Sorting) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[99]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomFieldsRequest_Sorting.ProtoReflect.Descriptor instead.
func (*ListCustomFieldsRequest_Sorting) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{66, 1}
}

func (x *ListCustomFieldsRequest_Sorting) GetField() ListCustomFieldsRequest_Sorting_Field {
	if x != nil {
		return x.Field
	}
	return ListCustomFieldsRequest_Sorting_FIELD_UNSPECIFIED
}

func (x *ListCustomFieldsRequest_Sorting) GetDirection() Direction {
	if x != nil {
		return x.Direction
	}
	return Direction_DIRECTION_UNSPECIFIED
}

// filter
type ListMetadataRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// metadata id list
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// 组织引用
	Organizations []*OrganizationRef `protobuf:"bytes,2,rep,name=organizations,proto3" json:"organizations,omitempty"`
	// 客户类型 (必填，用于确定查询customer还是lead)
	CustomerTypes []CustomerType `protobuf:"varint,3,rep,packed,name=customer_types,json=customerTypes,proto3,enum=backend.proto.customer.v2.CustomerType" json:"customer_types,omitempty"`
	// 状态
	States []Metadata_State `protobuf:"varint,4,rep,packed,name=states,proto3,enum=backend.proto.customer.v2.Metadata_State" json:"states,omitempty"`
	// 生命周期ID
	LifecycleIds []int64 `protobuf:"varint,5,rep,packed,name=lifecycle_ids,json=lifecycleIds,proto3" json:"lifecycle_ids,omitempty"`
	// 负责人员工ID
	OwnerStaffIds []int64 `protobuf:"varint,6,rep,packed,name=owner_staff_ids,json=ownerStaffIds,proto3" json:"owner_staff_ids,omitempty"`
	// 推荐来源ID
	ReferralSourceIds []int64 `protobuf:"varint,7,rep,packed,name=referral_source_ids,json=referralSourceIds,proto3" json:"referral_source_ids,omitempty"`
	// lead特有：转换的客户ID (仅当customer_type=LEAD时使用)
	ConvertedCustomerIds []int64 `protobuf:"varint,8,rep,packed,name=converted_customer_ids,json=convertedCustomerIds,proto3" json:"converted_customer_ids,omitempty"`
	// lead特有：是否已转换 (仅当customer_type=LEAD时使用)
	IsConverted   *bool `protobuf:"varint,9,opt,name=is_converted,json=isConverted,proto3,oneof" json:"is_converted,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMetadataRequest_Filter) Reset() {
	*x = ListMetadataRequest_Filter{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[100]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMetadataRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMetadataRequest_Filter) ProtoMessage() {}

func (x *ListMetadataRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[100]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMetadataRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListMetadataRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{78, 0}
}

func (x *ListMetadataRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListMetadataRequest_Filter) GetOrganizations() []*OrganizationRef {
	if x != nil {
		return x.Organizations
	}
	return nil
}

func (x *ListMetadataRequest_Filter) GetCustomerTypes() []CustomerType {
	if x != nil {
		return x.CustomerTypes
	}
	return nil
}

func (x *ListMetadataRequest_Filter) GetStates() []Metadata_State {
	if x != nil {
		return x.States
	}
	return nil
}

func (x *ListMetadataRequest_Filter) GetLifecycleIds() []int64 {
	if x != nil {
		return x.LifecycleIds
	}
	return nil
}

func (x *ListMetadataRequest_Filter) GetOwnerStaffIds() []int64 {
	if x != nil {
		return x.OwnerStaffIds
	}
	return nil
}

func (x *ListMetadataRequest_Filter) GetReferralSourceIds() []int64 {
	if x != nil {
		return x.ReferralSourceIds
	}
	return nil
}

func (x *ListMetadataRequest_Filter) GetConvertedCustomerIds() []int64 {
	if x != nil {
		return x.ConvertedCustomerIds
	}
	return nil
}

func (x *ListMetadataRequest_Filter) GetIsConverted() bool {
	if x != nil && x.IsConverted != nil {
		return *x.IsConverted
	}
	return false
}

// sorting
type ListMetadataRequest_Sorting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 排序字段
	Field ListMetadataRequest_Sorting_Field `protobuf:"varint,1,opt,name=field,proto3,enum=backend.proto.customer.v2.ListMetadataRequest_Sorting_Field" json:"field,omitempty"`
	// 排序方向
	Direction     Direction `protobuf:"varint,2,opt,name=direction,proto3,enum=backend.proto.customer.v2.Direction" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMetadataRequest_Sorting) Reset() {
	*x = ListMetadataRequest_Sorting{}
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[101]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMetadataRequest_Sorting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMetadataRequest_Sorting) ProtoMessage() {}

func (x *ListMetadataRequest_Sorting) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_service_proto_msgTypes[101]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMetadataRequest_Sorting.ProtoReflect.Descriptor instead.
func (*ListMetadataRequest_Sorting) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP(), []int{78, 1}
}

func (x *ListMetadataRequest_Sorting) GetField() ListMetadataRequest_Sorting_Field {
	if x != nil {
		return x.Field
	}
	return ListMetadataRequest_Sorting_FIELD_UNSPECIFIED
}

func (x *ListMetadataRequest_Sorting) GetDirection() Direction {
	if x != nil {
		return x.Direction
	}
	return Direction_DIRECTION_UNSPECIFIED
}

var File_backend_proto_customer_v2_metadata_service_proto protoreflect.FileDescriptor

const file_backend_proto_customer_v2_metadata_service_proto_rawDesc = "" +
	"\n" +
	"0backend/proto/customer/v2/metadata_service.proto\x12\x19backend.proto.customer.v2\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a(backend/proto/customer/v2/metadata.proto\x1a&backend/proto/customer/v2/common.proto\x1a\x1bbuf/validate/validate.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1egoogle/type/phone_number.proto\x1a google/type/postal_address.proto\x1a\x18google/type/latlng.proto\"\xcc\x04\n" +
	"\x15CreateCustomerRequest\x12S\n" +
	"\forganization\x18\x01 \x01(\v2*.backend.proto.customer.v2.OrganizationRefB\x03\xe0A\x02R\forganization\x12\x1d\n" +
	"\n" +
	"given_name\x18\x02 \x01(\tR\tgivenName\x12\x1f\n" +
	"\vfamily_name\x18\x03 \x01(\tR\n" +
	"familyName\x12A\n" +
	"\rcustom_fields\x18\x04 \x01(\v2\x17.google.protobuf.StructH\x00R\fcustomFields\x88\x01\x01\x12&\n" +
	"\flifecycle_id\x18\x05 \x01(\x03H\x01R\vlifecycleId\x88\x01\x01\x12)\n" +
	"\x0eowner_staff_id\x18\x06 \x01(\x03H\x02R\fownerStaffId\x88\x01\x01\x12+\n" +
	"\x0faction_state_id\x18\a \x01(\x03H\x03R\ractionStateId\x88\x01\x01\x12.\n" +
	"\vavatar_path\x18\b \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01H\x04R\n" +
	"avatarPath\x88\x01\x01\x12:\n" +
	"\x12referral_source_id\x18\t \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x05R\x10referralSourceId\x88\x01\x01B\x10\n" +
	"\x0e_custom_fieldsB\x0f\n" +
	"\r_lifecycle_idB\x11\n" +
	"\x0f_owner_staff_idB\x12\n" +
	"\x10_action_state_idB\x0e\n" +
	"\f_avatar_pathB\x15\n" +
	"\x13_referral_source_id\"Y\n" +
	"\x16CreateCustomerResponse\x12?\n" +
	"\bcustomer\x18\x01 \x01(\v2#.backend.proto.customer.v2.CustomerR\bcustomer\"-\n" +
	"\x12GetCustomerRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"V\n" +
	"\x13GetCustomerResponse\x12?\n" +
	"\bcustomer\x18\x01 \x01(\v2#.backend.proto.customer.v2.CustomerR\bcustomer\"\xca\x06\n" +
	"\x14ListCustomersRequest\x12V\n" +
	"\x06filter\x18\x01 \x01(\v26.backend.proto.customer.v2.ListCustomersRequest.FilterB\x06\xbaH\x03\xc8\x01\x01R\x06filter\x12Q\n" +
	"\asorting\x18\x02 \x01(\v27.backend.proto.customer.v2.ListCustomersRequest.SortingR\asorting\x12'\n" +
	"\tpage_size\x18\x03 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xe8\a(\x00R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x04 \x01(\tR\tpageToken\x1a\xac\x02\n" +
	"\x06Filter\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\x03R\x03ids\x12P\n" +
	"\rorganizations\x18\x02 \x03(\v2*.backend.proto.customer.v2.OrganizationRefR\rorganizations\x12A\n" +
	"\x06states\x18\x03 \x03(\x0e2).backend.proto.customer.v2.Customer.StateR\x06states\x12#\n" +
	"\rlifecycle_ids\x18\x04 \x03(\x03R\flifecycleIds\x12&\n" +
	"\x0fowner_staff_ids\x18\x05 \x03(\x03R\rownerStaffIds\x12.\n" +
	"\x13referral_source_ids\x18\x06 \x03(\x03R\x11referralSourceIds\x1a\x8f\x02\n" +
	"\aSorting\x12S\n" +
	"\x05field\x18\x01 \x01(\x0e2=.backend.proto.customer.v2.ListCustomersRequest.Sorting.FieldR\x05field\x12B\n" +
	"\tdirection\x18\x02 \x01(\x0e2$.backend.proto.customer.v2.DirectionR\tdirection\"k\n" +
	"\x05Field\x12\x15\n" +
	"\x11FIELD_UNSPECIFIED\x10\x00\x12\x06\n" +
	"\x02ID\x10\x01\x12\x10\n" +
	"\fCREATED_TIME\x10\x02\x12\x10\n" +
	"\fUPDATED_TIME\x10\x03\x12\x0e\n" +
	"\n" +
	"GIVEN_NAME\x10\x04\x12\x0f\n" +
	"\vFAMILY_NAME\x10\x05\"\xb5\x01\n" +
	"\x15ListCustomersResponse\x12A\n" +
	"\tcustomers\x18\x01 \x03(\v2#.backend.proto.customer.v2.CustomerR\tcustomers\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\"\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x03H\x00R\ttotalSize\x88\x01\x01B\r\n" +
	"\v_total_size\"\x89\x05\n" +
	"\x15UpdateCustomerRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\x12\"\n" +
	"\n" +
	"given_name\x18\x02 \x01(\tH\x00R\tgivenName\x88\x01\x01\x12$\n" +
	"\vfamily_name\x18\x03 \x01(\tH\x01R\n" +
	"familyName\x88\x01\x01\x12A\n" +
	"\rcustom_fields\x18\x04 \x01(\v2\x17.google.protobuf.StructH\x02R\fcustomFields\x88\x01\x01\x12&\n" +
	"\flifecycle_id\x18\x05 \x01(\x03H\x03R\vlifecycleId\x88\x01\x01\x12)\n" +
	"\x0eowner_staff_id\x18\x06 \x01(\x03H\x04R\fownerStaffId\x88\x01\x01\x12+\n" +
	"\x0faction_state_id\x18\a \x01(\x03H\x05R\ractionStateId\x88\x01\x01\x12.\n" +
	"\vavatar_path\x18\b \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01H\x06R\n" +
	"avatarPath\x88\x01\x01\x12:\n" +
	"\x12referral_source_id\x18\t \x01(\x03B\a\xbaH\x04\"\x02 \x00H\aR\x10referralSourceId\x88\x01\x01\x12D\n" +
	"\x05state\x18\n" +
	" \x01(\x0e2).backend.proto.customer.v2.Customer.StateH\bR\x05state\x88\x01\x01B\r\n" +
	"\v_given_nameB\x0e\n" +
	"\f_family_nameB\x10\n" +
	"\x0e_custom_fieldsB\x0f\n" +
	"\r_lifecycle_idB\x11\n" +
	"\x0f_owner_staff_idB\x12\n" +
	"\x10_action_state_idB\x0e\n" +
	"\f_avatar_pathB\x15\n" +
	"\x13_referral_source_idB\b\n" +
	"\x06_state\"Y\n" +
	"\x16UpdateCustomerResponse\x12?\n" +
	"\bcustomer\x18\x01 \x01(\v2#.backend.proto.customer.v2.CustomerR\bcustomer\"0\n" +
	"\x15DeleteCustomerRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"\x18\n" +
	"\x16DeleteCustomerResponse\"\xc0\x03\n" +
	"\x1eCreateCustomerAggregateRequest\x12S\n" +
	"\forganization\x18\x01 \x01(\v2*.backend.proto.customer.v2.OrganizationRefB\x03\xe0A\x02R\forganization\x12D\n" +
	"\bcustomer\x18\x02 \x01(\v2#.backend.proto.customer.v2.CustomerB\x03\xe0A\x02R\bcustomer\x12>\n" +
	"\bcontacts\x18\x03 \x03(\v2\".backend.proto.customer.v2.ContactR\bcontacts\x12@\n" +
	"\taddresses\x18\x04 \x03(\v2\".backend.proto.customer.v2.AddressR\taddresses\x12g\n" +
	"\x15customer_related_data\x18\x05 \x01(\v2..backend.proto.customer.v2.CustomerRelatedDataH\x00R\x13customerRelatedData\x88\x01\x01B\x18\n" +
	"\x16_customer_related_data\"~\n" +
	"\x1fCreateCustomerAggregateResponse\x12[\n" +
	"\x12customer_aggregate\x18\x01 \x01(\v2,.backend.proto.customer.v2.CustomerAggregateR\x11customerAggregate\"\xda\x02\n" +
	"\x14CreateContactRequest\x12(\n" +
	"\vcustomer_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"customerId\x12\x1d\n" +
	"\n" +
	"given_name\x18\x02 \x01(\tR\tgivenName\x12\x1f\n" +
	"\vfamily_name\x18\x03 \x01(\tR\n" +
	"familyName\x12\x19\n" +
	"\x05email\x18\x04 \x01(\tH\x00R\x05email\x88\x01\x01\x123\n" +
	"\x05phone\x18\x05 \x01(\v2\x18.google.type.PhoneNumberH\x01R\x05phone\x88\x01\x01\x12\x17\n" +
	"\ais_self\x18\x06 \x01(\bR\x06isSelf\x129\n" +
	"\x04tags\x18\a \x03(\v2%.backend.proto.customer.v2.ContactTagR\x04tags\x12\x17\n" +
	"\x04note\x18\b \x01(\tH\x02R\x04note\x88\x01\x01B\b\n" +
	"\x06_emailB\b\n" +
	"\x06_phoneB\a\n" +
	"\x05_note\"U\n" +
	"\x15CreateContactResponse\x12<\n" +
	"\acontact\x18\x01 \x01(\v2\".backend.proto.customer.v2.ContactR\acontact\",\n" +
	"\x11GetContactRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"R\n" +
	"\x12GetContactResponse\x12<\n" +
	"\acontact\x18\x01 \x01(\v2\".backend.proto.customer.v2.ContactR\acontact\"\x94\x06\n" +
	"\x13ListContactsRequest\x12U\n" +
	"\x06filter\x18\x01 \x01(\v25.backend.proto.customer.v2.ListContactsRequest.FilterB\x06\xbaH\x03\xc8\x01\x01R\x06filter\x12P\n" +
	"\asorting\x18\x02 \x01(\v26.backend.proto.customer.v2.ListContactsRequest.SortingR\asorting\x12'\n" +
	"\tpage_size\x18\x03 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xe8\a(\x01R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x04 \x01(\tR\tpageToken\x1a\x9b\x02\n" +
	"\x06Filter\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\x03R\x03ids\x12!\n" +
	"\fcustomer_ids\x18\x02 \x03(\x03R\vcustomerIds\x12@\n" +
	"\x06states\x18\x03 \x03(\x0e2(.backend.proto.customer.v2.Contact.StateR\x06states\x120\n" +
	"\x06phones\x18\x04 \x03(\v2\x18.google.type.PhoneNumberR\x06phones\x12\x16\n" +
	"\x06emails\x18\x05 \x03(\tR\x06emails\x12P\n" +
	"\rorganizations\x18\x06 \x03(\v2*.backend.proto.customer.v2.OrganizationRefR\rorganizations\x1a\xed\x01\n" +
	"\aSorting\x12R\n" +
	"\x05field\x18\x01 \x01(\x0e2<.backend.proto.customer.v2.ListContactsRequest.Sorting.FieldR\x05field\x12B\n" +
	"\tdirection\x18\x02 \x01(\x0e2$.backend.proto.customer.v2.DirectionR\tdirection\"J\n" +
	"\x05Field\x12\x15\n" +
	"\x11FIELD_UNSPECIFIED\x10\x00\x12\x06\n" +
	"\x02ID\x10\x01\x12\x10\n" +
	"\fCREATED_TIME\x10\x02\x12\x10\n" +
	"\fUPDATED_TIME\x10\x03\"\xb1\x01\n" +
	"\x14ListContactsResponse\x12>\n" +
	"\bcontacts\x18\x01 \x03(\v2\".backend.proto.customer.v2.ContactR\bcontacts\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\"\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x03H\x00R\ttotalSize\x88\x01\x01B\r\n" +
	"\v_total_size\"\x86\x03\n" +
	"\x14UpdateContactRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\x12\"\n" +
	"\n" +
	"given_name\x18\x02 \x01(\tH\x00R\tgivenName\x88\x01\x01\x12$\n" +
	"\vfamily_name\x18\x03 \x01(\tH\x01R\n" +
	"familyName\x88\x01\x01\x12\x19\n" +
	"\x05email\x18\x04 \x01(\tH\x02R\x05email\x88\x01\x01\x123\n" +
	"\x05phone\x18\x05 \x01(\v2\x18.google.type.PhoneNumberH\x03R\x05phone\x88\x01\x01\x12\x17\n" +
	"\atag_ids\x18\x06 \x03(\x03R\x06tagIds\x12\x17\n" +
	"\x04note\x18\a \x01(\tH\x04R\x04note\x88\x01\x01\x12C\n" +
	"\x05state\x18\b \x01(\x0e2(.backend.proto.customer.v2.Contact.StateH\x05R\x05state\x88\x01\x01B\r\n" +
	"\v_given_nameB\x0e\n" +
	"\f_family_nameB\b\n" +
	"\x06_emailB\b\n" +
	"\x06_phoneB\a\n" +
	"\x05_noteB\b\n" +
	"\x06_state\"U\n" +
	"\x15UpdateContactResponse\x12<\n" +
	"\acontact\x18\x01 \x01(\v2\".backend.proto.customer.v2.ContactR\acontact\"/\n" +
	"\x14DeleteContactRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"\x17\n" +
	"\x15DeleteContactResponse\"\xc1\x10\n" +
	" CreateCustomerRelatedDataRequest\x12(\n" +
	"\vcustomer_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"customerId\x122\n" +
	"\x15preferred_business_id\x18\x02 \x01(\x03R\x13preferredBusinessId\x12\x1d\n" +
	"\n" +
	"company_id\x18\x03 \x01(\x03R\tcompanyId\x12*\n" +
	"\fclient_color\x18\x04 \x01(\tB\a\xbaH\x04r\x02\x182R\vclientColor\x12(\n" +
	"\x10is_block_message\x18\x05 \x01(\x05R\x0eisBlockMessage\x125\n" +
	"\x17is_block_online_booking\x18\x06 \x01(\x05R\x14isBlockOnlineBooking\x12(\n" +
	"\vlogin_email\x18\a \x01(\tB\a\xbaH\x04r\x02\x182R\n" +
	"loginEmail\x12,\n" +
	"\x12referral_source_id\x18\b \x01(\x05R\x10referralSourceId\x12:\n" +
	"\x14referral_source_desc\x18\t \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01R\x12referralSourceDesc\x12&\n" +
	"\x0fsend_auto_email\x18\n" +
	" \x01(\x05R\rsendAutoEmail\x12*\n" +
	"\x11send_auto_message\x18\v \x01(\x05R\x0fsendAutoMessage\x121\n" +
	"\x15send_app_auto_message\x18\f \x01(\x05R\x12sendAppAutoMessage\x126\n" +
	"\x17unconfirmed_reminder_by\x18\r \x03(\x05R\x15unconfirmedReminderBy\x120\n" +
	"\x14preferred_groomer_id\x18\x0e \x01(\x05R\x12preferredGroomerId\x126\n" +
	"\x17preferred_frequency_day\x18\x0f \x01(\x05R\x15preferredFrequencyDay\x128\n" +
	"\x18preferred_frequency_type\x18\x10 \x01(\x05R\x16preferredFrequencyType\x123\n" +
	"\x11last_service_time\x18\x11 \x01(\tB\a\xbaH\x04r\x02\x18\x14R\x0flastServiceTime\x12\x1f\n" +
	"\x06source\x18\x12 \x01(\tB\a\xbaH\x04r\x02\x18\x14R\x06source\x12(\n" +
	"\vexternal_id\x18\x13 \x01(\tB\a\xbaH\x04r\x02\x18\x14R\n" +
	"externalId\x12\x1b\n" +
	"\tcreate_by\x18\x14 \x01(\x05R\bcreateBy\x12\x1b\n" +
	"\tupdate_by\x18\x15 \x01(\x05R\bupdateBy\x12&\n" +
	"\fis_recurring\x18\x16 \x01(\x05H\x00R\visRecurring\x88\x01\x01\x12*\n" +
	"\x11share_appt_status\x18\x17 \x01(\x05R\x0fshareApptStatus\x12(\n" +
	"\x10share_range_type\x18\x18 \x01(\x05R\x0eshareRangeType\x12*\n" +
	"\x11share_range_value\x18\x19 \x01(\x05R\x0fshareRangeValue\x12+\n" +
	"\x0fshare_appt_json\x18\x1a \x01(\tH\x01R\rshareApptJson\x88\x01\x01\x12,\n" +
	"\rpreferred_day\x18\x1b \x01(\tB\a\xbaH\x04r\x02\x182R\fpreferredDay\x12.\n" +
	"\x0epreferred_time\x18\x1c \x01(\tB\a\xbaH\x04r\x02\x182R\rpreferredTime\x12\x1d\n" +
	"\n" +
	"account_id\x18\x1d \x01(\x03R\taccountId\x12,\n" +
	"\rcustomer_code\x18\x1e \x01(\tB\a\xbaH\x04r\x02\x18\bR\fcustomerCode\x12'\n" +
	"\x0fis_unsubscribed\x18\x1f \x01(\bR\x0eisUnsubscribed\x12;\n" +
	"\bbirthday\x18  \x01(\v2\x1a.google.protobuf.TimestampH\x02R\bbirthday\x88\x01\x01\x12+\n" +
	"\faction_state\x18! \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01R\vactionState\x125\n" +
	"\x17customize_life_cycle_id\x18\" \x01(\x03R\x14customizeLifeCycleId\x129\n" +
	"\x19customize_action_state_id\x18# \x01(\x03R\x16customizeActionStateId\x120\n" +
	"\x14preferred_tip_enable\x18$ \x01(\x05R\x12preferredTipEnable\x12,\n" +
	"\x12preferred_tip_type\x18% \x01(\x05R\x10preferredTipType\x120\n" +
	"\x14preferred_tip_amount\x18& \x01(\x01R\x12preferredTipAmount\x128\n" +
	"\x18preferred_tip_percentage\x18' \x01(\x05R\x16preferredTipPercentage\x12G\n" +
	" default_preferred_frequency_type\x18( \x01(\x05R\x1ddefaultPreferredFrequencyType\x12I\n" +
	"!default_preferred_calendar_period\x18) \x01(\x05R\x1edefaultPreferredCalendarPeriod\x12I\n" +
	"!default_preferred_frequency_value\x18* \x01(\x05R\x1edefaultPreferredFrequencyValueB\x0f\n" +
	"\r_is_recurringB\x12\n" +
	"\x10_share_appt_jsonB\v\n" +
	"\t_birthday\"\x87\x01\n" +
	"!CreateCustomerRelatedDataResponse\x12b\n" +
	"\x15customer_related_data\x18\x01 \x01(\v2..backend.proto.customer.v2.CustomerRelatedDataR\x13customerRelatedData\"I\n" +
	"\x1dGetCustomerRelatedDataRequest\x12(\n" +
	"\vcustomer_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"customerId\"\x84\x01\n" +
	"\x1eGetCustomerRelatedDataResponse\x12b\n" +
	"\x15customer_related_data\x18\x01 \x01(\v2..backend.proto.customer.v2.CustomerRelatedDataR\x13customerRelatedData\"\x8e\a\n" +
	"\x1eListCustomerRelatedDataRequest\x12`\n" +
	"\x06filter\x18\x01 \x01(\<EMAIL>\x06\xbaH\x03\xc8\x01\x01R\x06filter\x12[\n" +
	"\asorting\x18\x02 \x01(\v2A.backend.proto.customer.v2.ListCustomerRelatedDataRequest.SortingR\asorting\x12'\n" +
	"\tpage_size\x18\x03 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xe8\a(\x01R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x04 \x01(\tR\tpageToken\x1a\xe9\x02\n" +
	"\x06Filter\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\x03R\x03ids\x12!\n" +
	"\fcustomer_ids\x18\x02 \x03(\x03R\vcustomerIds\x12P\n" +
	"\rorganizations\x18\x03 \x03(\v2*.backend.proto.customer.v2.OrganizationRefR\rorganizations\x12!\n" +
	"\fbusiness_ids\x18\x04 \x03(\x05R\vbusinessIds\x12\x1f\n" +
	"\vcompany_ids\x18\x05 \x03(\x03R\n" +
	"companyIds\x12L\n" +
	"\x06states\x18\x06 \x03(\x0e24.backend.proto.customer.v2.CustomerRelatedData.StateR\x06states\x12%\n" +
	"\x0ecustomer_codes\x18\a \x03(\tR\rcustomerCodes\x12\x1f\n" +
	"\vaccount_ids\x18\b \x03(\x03R\n" +
	"accountIds\x1a\xf8\x01\n" +
	"\aSorting\x12]\n" +
	"\x05field\x18\x01 \x01(\x0e2G.backend.proto.customer.v2.ListCustomerRelatedDataRequest.Sorting.FieldR\x05field\x12B\n" +
	"\tdirection\x18\x02 \x01(\x0e2$.backend.proto.customer.v2.DirectionR\tdirection\"J\n" +
	"\x05Field\x12\x15\n" +
	"\x11FIELD_UNSPECIFIED\x10\x00\x12\x06\n" +
	"\x02ID\x10\x01\x12\x10\n" +
	"\fCREATED_TIME\x10\x02\x12\x10\n" +
	"\fUPDATED_TIME\x10\x03\"\xe0\x01\n" +
	"\x1fListCustomerRelatedDataResponse\x12b\n" +
	"\x15customer_related_data\x18\x01 \x03(\v2..backend.proto.customer.v2.CustomerRelatedDataR\x13customerRelatedData\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\"\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x03H\x00R\ttotalSize\x88\x01\x01B\r\n" +
	"\v_total_size\"\x81\x18\n" +
	" UpdateCustomerRelatedDataRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\x127\n" +
	"\x15preferred_business_id\x18\x02 \x01(\x03H\x00R\x13preferredBusinessId\x88\x01\x01\x12/\n" +
	"\fclient_color\x18\x03 \x01(\tB\a\xbaH\x04r\x02\x182H\x01R\vclientColor\x88\x01\x01\x12-\n" +
	"\x10is_block_message\x18\x04 \x01(\x05H\x02R\x0eisBlockMessage\x88\x01\x01\x12:\n" +
	"\x17is_block_online_booking\x18\x05 \x01(\x05H\x03R\x14isBlockOnlineBooking\x88\x01\x01\x12-\n" +
	"\vlogin_email\x18\x06 \x01(\tB\a\xbaH\x04r\x02\x182H\x04R\n" +
	"loginEmail\x88\x01\x01\x121\n" +
	"\x12referral_source_id\x18\a \x01(\x05H\x05R\x10referralSourceId\x88\x01\x01\x12?\n" +
	"\x14referral_source_desc\x18\b \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01H\x06R\x12referralSourceDesc\x88\x01\x01\x12+\n" +
	"\x0fsend_auto_email\x18\t \x01(\x05H\aR\rsendAutoEmail\x88\x01\x01\x12/\n" +
	"\x11send_auto_message\x18\n" +
	" \x01(\x05H\bR\x0fsendAutoMessage\x88\x01\x01\x126\n" +
	"\x15send_app_auto_message\x18\v \x01(\x05H\tR\x12sendAppAutoMessage\x88\x01\x01\x126\n" +
	"\x17unconfirmed_reminder_by\x18\f \x03(\x05R\x15unconfirmedReminderBy\x125\n" +
	"\x14preferred_groomer_id\x18\r \x01(\x05H\n" +
	"R\x12preferredGroomerId\x88\x01\x01\x12;\n" +
	"\x17preferred_frequency_day\x18\x0e \x01(\x05H\vR\x15preferredFrequencyDay\x88\x01\x01\x12=\n" +
	"\x18preferred_frequency_type\x18\x0f \x01(\x05H\fR\x16preferredFrequencyType\x88\x01\x01\x128\n" +
	"\x11last_service_time\x18\x10 \x01(\tB\a\xbaH\x04r\x02\x18\x14H\rR\x0flastServiceTime\x88\x01\x01\x12$\n" +
	"\x06source\x18\x11 \x01(\tB\a\xbaH\x04r\x02\x18\x14H\x0eR\x06source\x88\x01\x01\x12-\n" +
	"\vexternal_id\x18\x12 \x01(\tB\a\xbaH\x04r\x02\x18\x14H\x0fR\n" +
	"externalId\x88\x01\x01\x12 \n" +
	"\tcreate_by\x18\x13 \x01(\x05H\x10R\bcreateBy\x88\x01\x01\x12 \n" +
	"\tupdate_by\x18\x14 \x01(\x05H\x11R\bupdateBy\x88\x01\x01\x12&\n" +
	"\fis_recurring\x18\x15 \x01(\x05H\x12R\visRecurring\x88\x01\x01\x12/\n" +
	"\x11share_appt_status\x18\x16 \x01(\x05H\x13R\x0fshareApptStatus\x88\x01\x01\x12-\n" +
	"\x10share_range_type\x18\x17 \x01(\x05H\x14R\x0eshareRangeType\x88\x01\x01\x12/\n" +
	"\x11share_range_value\x18\x18 \x01(\x05H\x15R\x0fshareRangeValue\x88\x01\x01\x12+\n" +
	"\x0fshare_appt_json\x18\x19 \x01(\tH\x16R\rshareApptJson\x88\x01\x01\x121\n" +
	"\rpreferred_day\x18\x1a \x01(\tB\a\xbaH\x04r\x02\x182H\x17R\fpreferredDay\x88\x01\x01\x123\n" +
	"\x0epreferred_time\x18\x1b \x01(\tB\a\xbaH\x04r\x02\x182H\x18R\rpreferredTime\x88\x01\x01\x12\"\n" +
	"\n" +
	"account_id\x18\x1c \x01(\x03H\x19R\taccountId\x88\x01\x01\x121\n" +
	"\rcustomer_code\x18\x1d \x01(\tB\a\xbaH\x04r\x02\x18\bH\x1aR\fcustomerCode\x88\x01\x01\x12,\n" +
	"\x0fis_unsubscribed\x18\x1e \x01(\bH\x1bR\x0eisUnsubscribed\x88\x01\x01\x12;\n" +
	"\bbirthday\x18\x1f \x01(\v2\x1a.google.protobuf.TimestampH\x1cR\bbirthday\x88\x01\x01\x120\n" +
	"\faction_state\x18  \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01H\x1dR\vactionState\x88\x01\x01\x12:\n" +
	"\x17customize_life_cycle_id\x18! \x01(\x03H\x1eR\x14customizeLifeCycleId\x88\x01\x01\x12>\n" +
	"\x19customize_action_state_id\x18\" \x01(\x03H\x1fR\x16customizeActionStateId\x88\x01\x01\x125\n" +
	"\x14preferred_tip_enable\x18# \x01(\x05H R\x12preferredTipEnable\x88\x01\x01\x121\n" +
	"\x12preferred_tip_type\x18$ \x01(\x05H!R\x10preferredTipType\x88\x01\x01\x125\n" +
	"\x14preferred_tip_amount\x18% \x01(\x01H\"R\x12preferredTipAmount\x88\x01\x01\x12=\n" +
	"\x18preferred_tip_percentage\x18& \x01(\x05H#R\x16preferredTipPercentage\x88\x01\x01\x12L\n" +
	" default_preferred_frequency_type\x18' \x01(\x05H$R\x1ddefaultPreferredFrequencyType\x88\x01\x01\x12N\n" +
	"!default_preferred_calendar_period\x18( \x01(\x05H%R\x1edefaultPreferredCalendarPeriod\x88\x01\x01\x12N\n" +
	"!default_preferred_frequency_value\x18) \x01(\x05H&R\x1edefaultPreferredFrequencyValue\x88\x01\x01B\x18\n" +
	"\x16_preferred_business_idB\x0f\n" +
	"\r_client_colorB\x13\n" +
	"\x11_is_block_messageB\x1a\n" +
	"\x18_is_block_online_bookingB\x0e\n" +
	"\f_login_emailB\x15\n" +
	"\x13_referral_source_idB\x17\n" +
	"\x15_referral_source_descB\x12\n" +
	"\x10_send_auto_emailB\x14\n" +
	"\x12_send_auto_messageB\x18\n" +
	"\x16_send_app_auto_messageB\x17\n" +
	"\x15_preferred_groomer_idB\x1a\n" +
	"\x18_preferred_frequency_dayB\x1b\n" +
	"\x19_preferred_frequency_typeB\x14\n" +
	"\x12_last_service_timeB\t\n" +
	"\a_sourceB\x0e\n" +
	"\f_external_idB\f\n" +
	"\n" +
	"_create_byB\f\n" +
	"\n" +
	"_update_byB\x0f\n" +
	"\r_is_recurringB\x14\n" +
	"\x12_share_appt_statusB\x13\n" +
	"\x11_share_range_typeB\x14\n" +
	"\x12_share_range_valueB\x12\n" +
	"\x10_share_appt_jsonB\x10\n" +
	"\x0e_preferred_dayB\x11\n" +
	"\x0f_preferred_timeB\r\n" +
	"\v_account_idB\x10\n" +
	"\x0e_customer_codeB\x12\n" +
	"\x10_is_unsubscribedB\v\n" +
	"\t_birthdayB\x0f\n" +
	"\r_action_stateB\x1a\n" +
	"\x18_customize_life_cycle_idB\x1c\n" +
	"\x1a_customize_action_state_idB\x17\n" +
	"\x15_preferred_tip_enableB\x15\n" +
	"\x13_preferred_tip_typeB\x17\n" +
	"\x15_preferred_tip_amountB\x1b\n" +
	"\x19_preferred_tip_percentageB#\n" +
	"!_default_preferred_frequency_typeB$\n" +
	"\"_default_preferred_calendar_periodB$\n" +
	"\"_default_preferred_frequency_value\"\x87\x01\n" +
	"!UpdateCustomerRelatedDataResponse\x12b\n" +
	"\x15customer_related_data\x18\x01 \x01(\v2..backend.proto.customer.v2.CustomerRelatedDataR\x13customerRelatedData\";\n" +
	" DeleteCustomerRelatedDataRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"#\n" +
	"!DeleteCustomerRelatedDataResponse\"\x98\x03\n" +
	"\x17CreateContactTagRequest\x12S\n" +
	"\forganization\x18\x01 \x01(\v2*.backend.proto.customer.v2.OrganizationRefB\x03\xe0A\x02R\forganization\x12\x17\n" +
	"\x04name\x18\x02 \x01(\tB\x03\xe0A\x02R\x04name\x12%\n" +
	"\vdescription\x18\x03 \x01(\tH\x00R\vdescription\x88\x01\x01\x12\x14\n" +
	"\x05color\x18\x04 \x01(\tR\x05color\x12\"\n" +
	"\n" +
	"sort_order\x18\x05 \x01(\x05H\x01R\tsortOrder\x88\x01\x01\x12A\n" +
	"\x05state\x18\x06 \x01(\x0e2+.backend.proto.customer.v2.ContactTag.StateR\x05state\x12C\n" +
	"\x04type\x18\a \x01(\x0e2*.backend.proto.customer.v2.ContactTag.TypeH\x02R\x04type\x88\x01\x01B\x0e\n" +
	"\f_descriptionB\r\n" +
	"\v_sort_orderB\a\n" +
	"\x05_type\"b\n" +
	"\x18CreateContactTagResponse\x12F\n" +
	"\vcontact_tag\x18\x01 \x01(\v2%.backend.proto.customer.v2.ContactTagR\n" +
	"contactTag\"/\n" +
	"\x14GetContactTagRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"_\n" +
	"\x15GetContactTagResponse\x12F\n" +
	"\vcontact_tag\x18\x01 \x01(\v2%.backend.proto.customer.v2.ContactTagR\n" +
	"contactTag\"\xd8\x05\n" +
	"\x16ListContactTagsRequest\x12X\n" +
	"\x06filter\x18\x01 \x01(\v28.backend.proto.customer.v2.ListContactTagsRequest.FilterB\x06\xbaH\x03\xc8\x01\x01R\x06filter\x12S\n" +
	"\asorting\x18\x02 \x01(\v29.backend.proto.customer.v2.ListContactTagsRequest.SortingR\asorting\x12'\n" +
	"\tpage_size\x18\x03 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xe8\a(\x01R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x04 \x01(\tR\tpageToken\x1a\xd3\x01\n" +
	"\x06Filter\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\x03R\x03ids\x12P\n" +
	"\rorganizations\x18\x02 \x03(\v2*.backend.proto.customer.v2.OrganizationRefR\rorganizations\x12\x17\n" +
	"\x04name\x18\x03 \x01(\tH\x00R\x04name\x88\x01\x01\x12C\n" +
	"\x06states\x18\x04 \x03(\x0e2+.backend.proto.customer.v2.ContactTag.StateR\x06statesB\a\n" +
	"\x05_name\x1a\xf0\x01\n" +
	"\aSorting\x12U\n" +
	"\x05field\x18\x01 \x01(\x0e2?.backend.proto.customer.v2.ListContactTagsRequest.Sorting.FieldR\x05field\x12B\n" +
	"\tdirection\x18\x02 \x01(\x0e2$.backend.proto.customer.v2.DirectionR\tdirection\"J\n" +
	"\x05Field\x12\x15\n" +
	"\x11FIELD_UNSPECIFIED\x10\x00\x12\x06\n" +
	"\x02ID\x10\x01\x12\x10\n" +
	"\fCREATED_TIME\x10\x02\x12\x10\n" +
	"\fUPDATED_TIME\x10\x03\"\xbe\x01\n" +
	"\x17ListContactTagsResponse\x12H\n" +
	"\fcontact_tags\x18\x01 \x03(\v2%.backend.proto.customer.v2.ContactTagR\vcontactTags\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\"\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x03H\x00R\ttotalSize\x88\x01\x01B\r\n" +
	"\v_total_size\"\xb5\x02\n" +
	"\x17UpdateContactTagRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\x12\x17\n" +
	"\x04name\x18\x02 \x01(\tH\x00R\x04name\x88\x01\x01\x12%\n" +
	"\vdescription\x18\x03 \x01(\tH\x01R\vdescription\x88\x01\x01\x12\x19\n" +
	"\x05color\x18\x04 \x01(\tH\x02R\x05color\x88\x01\x01\x12\"\n" +
	"\n" +
	"sort_order\x18\x05 \x01(\x05H\x03R\tsortOrder\x88\x01\x01\x12F\n" +
	"\x05state\x18\x06 \x01(\x0e2+.backend.proto.customer.v2.ContactTag.StateH\x04R\x05state\x88\x01\x01B\a\n" +
	"\x05_nameB\x0e\n" +
	"\f_descriptionB\b\n" +
	"\x06_colorB\r\n" +
	"\v_sort_orderB\b\n" +
	"\x06_state\"b\n" +
	"\x18UpdateContactTagResponse\x12F\n" +
	"\vcontact_tag\x18\x01 \x01(\v2%.backend.proto.customer.v2.ContactTagR\n" +
	"contactTag\"2\n" +
	"\x17DeleteContactTagRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"\x1a\n" +
	"\x18DeleteContactTagResponse\"\xbf\x04\n" +
	"\x11CreateLeadRequest\x12S\n" +
	"\forganization\x18\x01 \x01(\v2*.backend.proto.customer.v2.OrganizationRefB\x03\xe0A\x02R\forganization\x12\x1d\n" +
	"\n" +
	"given_name\x18\x02 \x01(\tR\tgivenName\x12\x1f\n" +
	"\vfamily_name\x18\x03 \x01(\tR\n" +
	"familyName\x12A\n" +
	"\rcustom_fields\x18\x04 \x01(\v2\x17.google.protobuf.StructH\x00R\fcustomFields\x88\x01\x01\x12&\n" +
	"\flifecycle_id\x18\x05 \x01(\x03H\x01R\vlifecycleId\x88\x01\x01\x12)\n" +
	"\x0eowner_staff_id\x18\x06 \x01(\x03H\x02R\fownerStaffId\x88\x01\x01\x12+\n" +
	"\x0faction_state_id\x18\a \x01(\x03H\x03R\ractionStateId\x88\x01\x01\x12.\n" +
	"\vavatar_path\x18\b \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01H\x04R\n" +
	"avatarPath\x88\x01\x01\x121\n" +
	"\x12referral_source_id\x18\t \x01(\x03H\x05R\x10referralSourceId\x88\x01\x01B\x10\n" +
	"\x0e_custom_fieldsB\x0f\n" +
	"\r_lifecycle_idB\x11\n" +
	"\x0f_owner_staff_idB\x12\n" +
	"\x10_action_state_idB\x0e\n" +
	"\f_avatar_pathB\x15\n" +
	"\x13_referral_source_id\"I\n" +
	"\x12CreateLeadResponse\x123\n" +
	"\x04lead\x18\x01 \x01(\v2\x1f.backend.proto.customer.v2.LeadR\x04lead\")\n" +
	"\x0eGetLeadRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"F\n" +
	"\x0fGetLeadResponse\x123\n" +
	"\x04lead\x18\x01 \x01(\v2\x1f.backend.proto.customer.v2.LeadR\x04lead\"\xd4\x06\n" +
	"\x10ListLeadsRequest\x12R\n" +
	"\x06filter\x18\x01 \x01(\v22.backend.proto.customer.v2.ListLeadsRequest.FilterB\x06\xbaH\x03\xc8\x01\x01R\x06filter\x12M\n" +
	"\asorting\x18\x02 \x01(\v23.backend.proto.customer.v2.ListLeadsRequest.SortingR\asorting\x12'\n" +
	"\tpage_size\x18\x03 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xe8\a(\x01R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x04 \x01(\tR\tpageToken\x1a\xe7\x02\n" +
	"\x06Filter\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\x03R\x03ids\x12P\n" +
	"\rorganizations\x18\x02 \x03(\v2*.backend.proto.customer.v2.OrganizationRefR\rorganizations\x12=\n" +
	"\x06states\x18\x03 \x03(\x0e2%.backend.proto.customer.v2.Lead.StateR\x06states\x12#\n" +
	"\rlifecycle_ids\x18\x04 \x03(\x03R\flifecycleIds\x12&\n" +
	"\x0fowner_staff_ids\x18\x05 \x03(\x03R\rownerStaffIds\x124\n" +
	"\x16converted_customer_ids\x18\x06 \x03(\x03R\x14convertedCustomerIds\x12&\n" +
	"\fis_converted\x18\a \x01(\bH\x00R\visConverted\x88\x01\x01B\x0f\n" +
	"\r_is_converted\x1a\xea\x01\n" +
	"\aSorting\x12O\n" +
	"\x05field\x18\x01 \x01(\x0e29.backend.proto.customer.v2.ListLeadsRequest.Sorting.FieldR\x05field\x12B\n" +
	"\tdirection\x18\x02 \x01(\x0e2$.backend.proto.customer.v2.DirectionR\tdirection\"J\n" +
	"\x05Field\x12\x15\n" +
	"\x11FIELD_UNSPECIFIED\x10\x00\x12\x06\n" +
	"\x02ID\x10\x01\x12\x10\n" +
	"\fCREATED_TIME\x10\x02\x12\x10\n" +
	"\fUPDATED_TIME\x10\x03\"\xa5\x01\n" +
	"\x11ListLeadsResponse\x125\n" +
	"\x05leads\x18\x01 \x03(\v2\x1f.backend.proto.customer.v2.LeadR\x05leads\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\"\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x03H\x00R\ttotalSize\x88\x01\x01B\r\n" +
	"\v_total_size\"\xed\x03\n" +
	"\x11UpdateLeadRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\x12\"\n" +
	"\n" +
	"given_name\x18\x02 \x01(\tH\x00R\tgivenName\x88\x01\x01\x12$\n" +
	"\vfamily_name\x18\x03 \x01(\tH\x01R\n" +
	"familyName\x88\x01\x01\x12A\n" +
	"\rcustom_fields\x18\x04 \x01(\v2\x17.google.protobuf.StructH\x02R\fcustomFields\x88\x01\x01\x12&\n" +
	"\flifecycle_id\x18\x05 \x01(\x03H\x03R\vlifecycleId\x88\x01\x01\x12)\n" +
	"\x0eowner_staff_id\x18\x06 \x01(\x03H\x04R\fownerStaffId\x88\x01\x01\x12@\n" +
	"\x05state\x18\a \x01(\x0e2%.backend.proto.customer.v2.Lead.StateH\x05R\x05state\x88\x01\x01\x12.\n" +
	"\vavatar_path\x18\b \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01H\x06R\n" +
	"avatarPath\x88\x01\x01B\r\n" +
	"\v_given_nameB\x0e\n" +
	"\f_family_nameB\x10\n" +
	"\x0e_custom_fieldsB\x0f\n" +
	"\r_lifecycle_idB\x11\n" +
	"\x0f_owner_staff_idB\b\n" +
	"\x06_stateB\x0e\n" +
	"\f_avatar_path\"I\n" +
	"\x12UpdateLeadResponse\x123\n" +
	"\x04lead\x18\x01 \x01(\v2\x1f.backend.proto.customer.v2.LeadR\x04lead\",\n" +
	"\x11DeleteLeadRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"\x14\n" +
	"\x12DeleteLeadResponse\"\xba\x02\n" +
	"\x14CreateAddressRequest\x12(\n" +
	"\vcustomer_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"customerId\x129\n" +
	"\aaddress\x18\x02 \x01(\v2\x1a.google.type.PostalAddressB\x03\xe0A\x02R\aaddress\x12+\n" +
	"\x06latlng\x18\x03 \x01(\v2\x13.google.type.LatLngR\x06latlng\x12;\n" +
	"\x04type\x18\x04 \x01(\x0e2'.backend.proto.customer.v2.Address.TypeR\x04type\x12S\n" +
	"\forganization\x18\x05 \x01(\v2*.backend.proto.customer.v2.OrganizationRefB\x03\xe0A\x02R\forganization\"U\n" +
	"\x15CreateAddressResponse\x12<\n" +
	"\aaddress\x18\x01 \x01(\v2\".backend.proto.customer.v2.AddressR\aaddress\",\n" +
	"\x11GetAddressRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"R\n" +
	"\x12GetAddressResponse\x12<\n" +
	"\aaddress\x18\x01 \x01(\v2\".backend.proto.customer.v2.AddressR\aaddress\"\x8d\x06\n" +
	"\x14ListAddressesRequest\x12V\n" +
	"\x06filter\x18\x01 \x01(\v26.backend.proto.customer.v2.ListAddressesRequest.FilterB\x06\xbaH\x03\xc8\x01\x01R\x06filter\x12Q\n" +
	"\asorting\x18\x02 \x01(\v27.backend.proto.customer.v2.ListAddressesRequest.SortingR\asorting\x12'\n" +
	"\tpage_size\x18\x03 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xe8\a(\x01R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x04 \x01(\tR\tpageToken\x1a\x90\x02\n" +
	"\x06Filter\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\x03R\x03ids\x12!\n" +
	"\fcustomer_ids\x18\x02 \x03(\x03R\vcustomerIds\x12@\n" +
	"\x06states\x18\x03 \x03(\x0e2(.backend.proto.customer.v2.Address.StateR\x06states\x12P\n" +
	"\rorganizations\x18\x04 \x03(\v2*.backend.proto.customer.v2.OrganizationRefR\rorganizations\x12=\n" +
	"\x05types\x18\x05 \x03(\x0e2'.backend.proto.customer.v2.Address.TypeR\x05types\x1a\xee\x01\n" +
	"\aSorting\x12S\n" +
	"\x05field\x18\x01 \x01(\x0e2=.backend.proto.customer.v2.ListAddressesRequest.Sorting.FieldR\x05field\x12B\n" +
	"\tdirection\x18\x02 \x01(\x0e2$.backend.proto.customer.v2.DirectionR\tdirection\"J\n" +
	"\x05Field\x12\x15\n" +
	"\x11FIELD_UNSPECIFIED\x10\x00\x12\x06\n" +
	"\x02ID\x10\x01\x12\x10\n" +
	"\fCREATED_TIME\x10\x02\x12\x10\n" +
	"\fUPDATED_TIME\x10\x03\"\xb4\x01\n" +
	"\x15ListAddressesResponse\x12@\n" +
	"\taddresses\x18\x01 \x03(\v2\".backend.proto.customer.v2.AddressR\taddresses\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\"\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x03H\x00R\ttotalSize\x88\x01\x01B\r\n" +
	"\v_total_size\"\xac\x02\n" +
	"\x14UpdateAddressRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\x124\n" +
	"\aaddress\x18\x02 \x01(\v2\x1a.google.type.PostalAddressR\aaddress\x12+\n" +
	"\x06latlng\x18\x03 \x01(\v2\x13.google.type.LatLngR\x06latlng\x12@\n" +
	"\x04type\x18\x04 \x01(\x0e2'.backend.proto.customer.v2.Address.TypeH\x00R\x04type\x88\x01\x01\x12C\n" +
	"\x05state\x18\x05 \x01(\x0e2(.backend.proto.customer.v2.Address.StateH\x01R\x05state\x88\x01\x01B\a\n" +
	"\x05_typeB\b\n" +
	"\x06_state\"U\n" +
	"\x15UpdateAddressResponse\x12<\n" +
	"\aaddress\x18\x01 \x01(\v2\".backend.proto.customer.v2.AddressR\aaddress\"/\n" +
	"\x14DeleteAddressRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"\x17\n" +
	"\x15DeleteAddressResponse\"\x8d\x05\n" +
	"\x18CreateCustomFieldRequest\x12S\n" +
	"\forganization\x18\x01 \x01(\v2*.backend.proto.customer.v2.OrganizationRefB\x03\xe0A\x02R\forganization\x12\x19\n" +
	"\x05label\x18\x02 \x01(\tB\x03\xe0A\x02R\x05label\x12D\n" +
	"\x04type\x18\x03 \x01(\x0e2+.backend.proto.customer.v2.CustomField.TypeB\x03\xe0A\x02R\x04type\x12f\n" +
	"\x10association_type\x18\x04 \x01(\x0e26.backend.proto.customer.v2.CustomField.AssociationTypeB\x03\xe0A\x02R\x0fassociationType\x12$\n" +
	"\vis_required\x18\x05 \x01(\bH\x00R\n" +
	"isRequired\x88\x01\x01\x12V\n" +
	"\rdefault_value\x18\x06 \x01(\v2,.backend.proto.customer.v2.CustomField.ValueH\x01R\fdefaultValue\x88\x01\x01\x12G\n" +
	"\aoptions\x18\a \x03(\v2-.backend.proto.customer.v2.CustomField.OptionR\aoptions\x12(\n" +
	"\rdisplay_order\x18\b \x01(\x05H\x02R\fdisplayOrder\x88\x01\x01\x12 \n" +
	"\thelp_text\x18\t \x01(\tH\x03R\bhelpText\x88\x01\x01B\x0e\n" +
	"\f_is_requiredB\x10\n" +
	"\x0e_default_valueB\x10\n" +
	"\x0e_display_orderB\f\n" +
	"\n" +
	"_help_text\"f\n" +
	"\x19CreateCustomFieldResponse\x12I\n" +
	"\fcustom_field\x18\x01 \x01(\v2&.backend.proto.customer.v2.CustomFieldR\vcustomField\"0\n" +
	"\x15GetCustomFieldRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"c\n" +
	"\x16GetCustomFieldResponse\x12I\n" +
	"\fcustom_field\x18\x01 \x01(\v2&.backend.proto.customer.v2.CustomFieldR\vcustomField\"\xac\a\n" +
	"\x17ListCustomFieldsRequest\x12Y\n" +
	"\x06filter\x18\x01 \x01(\v29.backend.proto.customer.v2.ListCustomFieldsRequest.FilterB\x06\xbaH\x03\xc8\x01\x01R\x06filter\x12T\n" +
	"\asorting\x18\x02 \x01(\v2:.backend.proto.customer.v2.ListCustomFieldsRequest.SortingR\asorting\x12'\n" +
	"\tpage_size\x18\x03 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xe8\a(\x01R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x04 \x01(\tR\tpageToken\x1a\x90\x03\n" +
	"\x06Filter\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\x03R\x03ids\x12P\n" +
	"\rorganizations\x18\x02 \x03(\v2*.backend.proto.customer.v2.OrganizationRefR\rorganizations\x12c\n" +
	"\x11association_types\x18\x03 \x03(\x0e26.backend.proto.customer.v2.CustomField.AssociationTypeR\x10associationTypes\x12A\n" +
	"\x05types\x18\x04 \x03(\x0e2+.backend.proto.customer.v2.CustomField.TypeR\x05types\x12$\n" +
	"\vis_required\x18\x05 \x01(\bH\x00R\n" +
	"isRequired\x88\x01\x01\x12D\n" +
	"\x06states\x18\x06 \x03(\x0e2,.backend.proto.customer.v2.CustomField.StateR\x06statesB\x0e\n" +
	"\f_is_required\x1a\x84\x02\n" +
	"\aSorting\x12V\n" +
	"\x05field\x18\x01 \x01(\<EMAIL>\x05field\x12B\n" +
	"\tdirection\x18\x02 \x01(\x0e2$.backend.proto.customer.v2.DirectionR\tdirection\"]\n" +
	"\x05Field\x12\x15\n" +
	"\x11FIELD_UNSPECIFIED\x10\x00\x12\x06\n" +
	"\x02ID\x10\x01\x12\x10\n" +
	"\fCREATED_TIME\x10\x02\x12\x10\n" +
	"\fUPDATED_TIME\x10\x03\x12\x11\n" +
	"\rDISPLAY_ORDER\x10\x04\"\xc2\x01\n" +
	"\x18ListCustomFieldsResponse\x12K\n" +
	"\rcustom_fields\x18\x01 \x03(\v2&.backend.proto.customer.v2.CustomFieldR\fcustomFields\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\"\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x03H\x00R\ttotalSize\x88\x01\x01B\r\n" +
	"\v_total_size\"\x80\x04\n" +
	"\x18UpdateCustomFieldRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\x12\x19\n" +
	"\x05label\x18\x02 \x01(\tH\x00R\x05label\x88\x01\x01\x12$\n" +
	"\vis_required\x18\x03 \x01(\bH\x01R\n" +
	"isRequired\x88\x01\x01\x12G\n" +
	"\x05state\x18\x04 \x01(\x0e2,.backend.proto.customer.v2.CustomField.StateH\x02R\x05state\x88\x01\x01\x12V\n" +
	"\rdefault_value\x18\x05 \x01(\v2,.backend.proto.customer.v2.CustomField.ValueH\x03R\fdefaultValue\x88\x01\x01\x12G\n" +
	"\aoptions\x18\x06 \x03(\v2-.backend.proto.customer.v2.CustomField.OptionR\aoptions\x12(\n" +
	"\rdisplay_order\x18\a \x01(\x05H\x04R\fdisplayOrder\x88\x01\x01\x12 \n" +
	"\thelp_text\x18\b \x01(\tH\x05R\bhelpText\x88\x01\x01B\b\n" +
	"\x06_labelB\x0e\n" +
	"\f_is_requiredB\b\n" +
	"\x06_stateB\x10\n" +
	"\x0e_default_valueB\x10\n" +
	"\x0e_display_orderB\f\n" +
	"\n" +
	"_help_text\"f\n" +
	"\x19UpdateCustomFieldResponse\x12I\n" +
	"\fcustom_field\x18\x01 \x01(\v2&.backend.proto.customer.v2.CustomFieldR\vcustomField\"\xa1\x01\n" +
	"\x1eBatchUpdateCustomFieldsRequest\x12\x1f\n" +
	"\x06parent\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x06parent\x12^\n" +
	"\brequests\x18\x02 \x03(\v23.backend.proto.customer.v2.UpdateCustomFieldRequestB\r\xe0A\x02\xbaH\a\x92\x01\x04\b\x01\x10dR\brequests\"n\n" +
	"\x1fBatchUpdateCustomFieldsResponse\x12K\n" +
	"\rcustom_fields\x18\x01 \x03(\v2&.backend.proto.customer.v2.CustomFieldR\fcustomFields\"3\n" +
	"\x18DeleteCustomFieldRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"\x1b\n" +
	"\x19DeleteCustomFieldResponse\"\x9f\x05\n" +
	"\x15CreateMetadataRequest\x12S\n" +
	"\forganization\x18\x01 \x01(\v2*.backend.proto.customer.v2.OrganizationRefB\x03\xe0A\x02R\forganization\x12Q\n" +
	"\rcustomer_type\x18\x02 \x01(\x0e2'.backend.proto.customer.v2.CustomerTypeB\x03\xe0A\x02R\fcustomerType\x12\x1d\n" +
	"\n" +
	"given_name\x18\x03 \x01(\tR\tgivenName\x12\x1f\n" +
	"\vfamily_name\x18\x04 \x01(\tR\n" +
	"familyName\x12A\n" +
	"\rcustom_fields\x18\x05 \x01(\v2\x17.google.protobuf.StructH\x00R\fcustomFields\x88\x01\x01\x12&\n" +
	"\flifecycle_id\x18\x06 \x01(\x03H\x01R\vlifecycleId\x88\x01\x01\x12)\n" +
	"\x0eowner_staff_id\x18\a \x01(\x03H\x02R\fownerStaffId\x88\x01\x01\x12+\n" +
	"\x0faction_state_id\x18\b \x01(\x03H\x03R\ractionStateId\x88\x01\x01\x12.\n" +
	"\vavatar_path\x18\t \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01H\x04R\n" +
	"avatarPath\x88\x01\x01\x12:\n" +
	"\x12referral_source_id\x18\n" +
	" \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x05R\x10referralSourceId\x88\x01\x01B\x10\n" +
	"\x0e_custom_fieldsB\x0f\n" +
	"\r_lifecycle_idB\x11\n" +
	"\x0f_owner_staff_idB\x12\n" +
	"\x10_action_state_idB\x0e\n" +
	"\f_avatar_pathB\x15\n" +
	"\x13_referral_source_id\"Y\n" +
	"\x16CreateMetadataResponse\x12?\n" +
	"\bmetadata\x18\x01 \x01(\v2#.backend.proto.customer.v2.MetadataR\bmetadata\"-\n" +
	"\x12GetMetadataRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\"V\n" +
	"\x13GetMetadataResponse\x12?\n" +
	"\bmetadata\x18\x01 \x01(\v2#.backend.proto.customer.v2.MetadataR\bmetadata\"\xe4\a\n" +
	"\x13ListMetadataRequest\x12U\n" +
	"\x06filter\x18\x01 \x01(\v25.backend.proto.customer.v2.ListMetadataRequest.FilterB\x06\xbaH\x03\xc8\x01\x01R\x06filter\x12P\n" +
	"\asorting\x18\x02 \x01(\v26.backend.proto.customer.v2.ListMetadataRequest.SortingR\asorting\x12'\n" +
	"\tpage_size\x18\x03 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xe8\a(\x01R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x04 \x01(\tR\tpageToken\x1a\xeb\x03\n" +
	"\x06Filter\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\x03R\x03ids\x12P\n" +
	"\rorganizations\x18\x02 \x03(\v2*.backend.proto.customer.v2.OrganizationRefR\rorganizations\x12N\n" +
	"\x0ecustomer_types\x18\x03 \x03(\x0e2'.backend.proto.customer.v2.CustomerTypeR\rcustomerTypes\x12A\n" +
	"\x06states\x18\x04 \x03(\x0e2).backend.proto.customer.v2.Metadata.StateR\x06states\x12#\n" +
	"\rlifecycle_ids\x18\x05 \x03(\x03R\flifecycleIds\x12&\n" +
	"\x0fowner_staff_ids\x18\x06 \x03(\x03R\rownerStaffIds\x12.\n" +
	"\x13referral_source_ids\x18\a \x03(\x03R\x11referralSourceIds\x124\n" +
	"\x16converted_customer_ids\x18\b \x03(\x03R\x14convertedCustomerIds\x12&\n" +
	"\fis_converted\x18\t \x01(\bH\x00R\visConverted\x88\x01\x01B\x0f\n" +
	"\r_is_converted\x1a\xed\x01\n" +
	"\aSorting\x12R\n" +
	"\x05field\x18\x01 \x01(\x0e2<.backend.proto.customer.v2.ListMetadataRequest.Sorting.FieldR\x05field\x12B\n" +
	"\tdirection\x18\x02 \x01(\x0e2$.backend.proto.customer.v2.DirectionR\tdirection\"J\n" +
	"\x05Field\x12\x15\n" +
	"\x11FIELD_UNSPECIFIED\x10\x00\x12\x06\n" +
	"\x02ID\x10\x01\x12\x10\n" +
	"\fCREATED_TIME\x10\x02\x12\x10\n" +
	"\fUPDATED_TIME\x10\x03\"\xb2\x01\n" +
	"\x14ListMetadataResponse\x12?\n" +
	"\bmetadata\x18\x01 \x03(\v2#.backend.proto.customer.v2.MetadataR\bmetadata\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\"\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x03H\x00R\ttotalSize\x88\x01\x01B\r\n" +
	"\v_total_size\"\xd7\x05\n" +
	"\x15UpdateMetadataRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\x12L\n" +
	"\rcustomer_type\x18\x02 \x01(\x0e2'.backend.proto.customer.v2.CustomerTypeR\fcustomerType\x12\"\n" +
	"\n" +
	"given_name\x18\x03 \x01(\tH\x00R\tgivenName\x88\x01\x01\x12$\n" +
	"\vfamily_name\x18\x04 \x01(\tH\x01R\n" +
	"familyName\x88\x01\x01\x12A\n" +
	"\rcustom_fields\x18\x05 \x01(\v2\x17.google.protobuf.StructH\x02R\fcustomFields\x88\x01\x01\x12&\n" +
	"\flifecycle_id\x18\x06 \x01(\x03H\x03R\vlifecycleId\x88\x01\x01\x12)\n" +
	"\x0eowner_staff_id\x18\a \x01(\x03H\x04R\fownerStaffId\x88\x01\x01\x12+\n" +
	"\x0faction_state_id\x18\b \x01(\x03H\x05R\ractionStateId\x88\x01\x01\x12.\n" +
	"\vavatar_path\x18\t \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01H\x06R\n" +
	"avatarPath\x88\x01\x01\x12:\n" +
	"\x12referral_source_id\x18\n" +
	" \x01(\x03B\a\xbaH\x04\"\x02 \x00H\aR\x10referralSourceId\x88\x01\x01\x12D\n" +
	"\x05state\x18\v \x01(\x0e2).backend.proto.customer.v2.Metadata.StateH\bR\x05state\x88\x01\x01B\r\n" +
	"\v_given_nameB\x0e\n" +
	"\f_family_nameB\x10\n" +
	"\x0e_custom_fieldsB\x0f\n" +
	"\r_lifecycle_idB\x11\n" +
	"\x0f_owner_staff_idB\x12\n" +
	"\x10_action_state_idB\x0e\n" +
	"\f_avatar_pathB\x15\n" +
	"\x13_referral_source_idB\b\n" +
	"\x06_state\"Y\n" +
	"\x16UpdateMetadataResponse\x12?\n" +
	"\bmetadata\x18\x01 \x01(\v2#.backend.proto.customer.v2.MetadataR\bmetadata\"~\n" +
	"\x15DeleteMetadataRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id\x12L\n" +
	"\rcustomer_type\x18\x02 \x01(\x0e2'.backend.proto.customer.v2.CustomerTypeR\fcustomerType\"\x18\n" +
	"\x16DeleteMetadataResponse\"\x93\x04\n" +
	"\x1eCreateMetadataAggregateRequest\x12S\n" +
	"\forganization\x18\x01 \x01(\v2*.backend.proto.customer.v2.OrganizationRefB\x03\xe0A\x02R\forganization\x12Q\n" +
	"\rcustomer_type\x18\x02 \x01(\x0e2'.backend.proto.customer.v2.CustomerTypeB\x03\xe0A\x02R\fcustomerType\x12D\n" +
	"\bmetadata\x18\x03 \x01(\v2#.backend.proto.customer.v2.MetadataB\x03\xe0A\x02R\bmetadata\x12>\n" +
	"\bcontacts\x18\x04 \x03(\v2\".backend.proto.customer.v2.ContactR\bcontacts\x12@\n" +
	"\taddresses\x18\x05 \x03(\v2\".backend.proto.customer.v2.AddressR\taddresses\x12g\n" +
	"\x15customer_related_data\x18\x06 \x01(\v2..backend.proto.customer.v2.CustomerRelatedDataH\x00R\x13customerRelatedData\x88\x01\x01B\x18\n" +
	"\x16_customer_related_data\"~\n" +
	"\x1fCreateMetadataAggregateResponse\x12[\n" +
	"\x12metadata_aggregate\x18\x01 \x01(\v2,.backend.proto.customer.v2.MetadataAggregateR\x11metadataAggregate*\xcd\n" +
	"\n" +
	"\aErrCode\x12\x0f\n" +
	"\vERR_CODE_OK\x10\x00\x12\x1a\n" +
	"\x14ERR_CODE_UNSPECIFIED\x10̥\a\x12!\n" +
	"\x1bERR_CODE_CUSTOMER_NOT_FOUND\x10ͥ\a\x12&\n" +
	" ERR_CODE_CUSTOMER_ALREADY_EXISTS\x10Υ\a\x12\"\n" +
	"\x1cERR_CODE_INVALID_CUSTOMER_ID\x10ϥ\a\x12$\n" +
	"\x1eERR_CODE_INVALID_CUSTOMER_NAME\x10Х\a\x12\x1f\n" +
	"\x19ERR_CODE_CUSTOMER_DELETED\x10ѥ\a\x12%\n" +
	"\x1fERR_CODE_CREATE_CUSTOMER_FAILED\x10ҥ\a\x12 \n" +
	"\x1aERR_CODE_ADDRESS_NOT_FOUND\x10֥\a\x12\x1e\n" +
	"\x18ERR_CODE_INVALID_ADDRESS\x10ץ\a\x12%\n" +
	"\x1fERR_CODE_ADDRESS_LIMIT_EXCEEDED\x10إ\a\x12,\n" +
	"&ERR_CODE_DUPLICATE_SET_PRIMARY_ADDRESS\x10٥\a\x12\x1d\n" +
	"\x17ERR_CODE_TASK_NOT_FOUND\x10\xe0\xa5\a\x12%\n" +
	"\x1fERR_CODE_TASK_ALREADY_COMPLETED\x10\xe1\xa5\a\x12\"\n" +
	"\x1cERR_CODE_INVALID_TASK_STATUS\x10\xe2\xa5\a\x12&\n" +
	" ERR_CODE_ACTION_STATE_NAME_EXIST\x10\xe5\xa5\a\x12$\n" +
	"\x1eERR_CODE_LIFE_CYCLE_NAME_EXIST\x10\xe6\xa5\a\x12\x1e\n" +
	"\x18ERR_CODE_VIEW_NAME_EXIST\x10\xe7\xa5\a\x12 \n" +
	"\x1aERR_CODE_SOURCE_NAME_EXIST\x10\xe8\xa5\a\x12\x1d\n" +
	"\x17ERR_CODE_TAG_NAME_EXIST\x10\xe9\xa5\a\x12 \n" +
	"\x1aERR_CODE_CONTACT_NOT_FOUND\x10\xef\xa5\a\x12%\n" +
	"\x1fERR_CODE_CONTACT_ALREADY_EXISTS\x10\xf0\xa5\a\x12\x1e\n" +
	"\x18ERR_CODE_INVALID_CONTACT\x10\xf1\xa5\a\x12/\n" +
	")ERR_CODE_UPDATE_CONTACT_CONTENT_NOT_MATCH\x10\xf2\xa5\a\x12\x1d\n" +
	"\x17ERR_CODE_LEAD_NOT_FOUND\x10\xf4\xa5\a\x12!\n" +
	"\x1bERR_CODE_CREATE_LEAD_FAILED\x10\xf5\xa5\a\x12$\n" +
	"\x1eERR_CODE_CONTACT_TAG_NOT_FOUND\x10\xf9\xa5\a\x12(\n" +
	"\"ERR_CODE_CREATE_CONTACT_TAG_FAILED\x10\xfa\xa5\a\x12%\n" +
	"\x1fERR_CODE_CUSTOM_FIELD_NOT_FOUND\x10\xfe\xa5\a\x12)\n" +
	"#ERR_CODE_CREATE_CUSTOM_FIELD_FAILED\x10\xff\xa5\a\x12*\n" +
	"$ERR_CODE_CUSTOM_FIELD_ALREADY_EXISTS\x10\x80\xa6\a\x12#\n" +
	"\x1dERR_CODE_CUSTOM_FIELD_DELETED\x10\x81\xa6\a\x12,\n" +
	"&ERR_CODE_CUSTOM_FIELD_OPTION_NOT_FOUND\x10\x82\xa6\a\x12*\n" +
	"$ERR_CODE_CUSTOM_FIELD_OPTION_DELETED\x10\x83\xa6\a\x12.\n" +
	"(ERR_CODE_CUSTOMER_RELATED_DATA_NOT_FOUND\x10\x88\xa6\a\x12/\n" +
	")ERR_CODE_CREATE_CUSTOMER_AGGREGATE_FAILED\x10\x89\xa6\a2\x9a)\n" +
	"\x0fMetadataService\x12u\n" +
	"\x0eCreateCustomer\x120.backend.proto.customer.v2.CreateCustomerRequest\x1a1.backend.proto.customer.v2.CreateCustomerResponse\x12l\n" +
	"\vGetCustomer\x12-.backend.proto.customer.v2.GetCustomerRequest\x1a..backend.proto.customer.v2.GetCustomerResponse\x12r\n" +
	"\rListCustomers\x12/.backend.proto.customer.v2.ListCustomersRequest\x1a0.backend.proto.customer.v2.ListCustomersResponse\x12u\n" +
	"\x0eUpdateCustomer\x120.backend.proto.customer.v2.UpdateCustomerRequest\x1a1.backend.proto.customer.v2.UpdateCustomerResponse\x12u\n" +
	"\x0eDeleteCustomer\x120.backend.proto.customer.v2.DeleteCustomerRequest\x1a1.backend.proto.customer.v2.DeleteCustomerResponse\x12\x90\x01\n" +
	"\x17CreateCustomerAggregate\x129.backend.proto.customer.v2.CreateCustomerAggregateRequest\x1a:.backend.proto.customer.v2.CreateCustomerAggregateResponse\x12r\n" +
	"\rCreateContact\x12/.backend.proto.customer.v2.CreateContactRequest\x1a0.backend.proto.customer.v2.CreateContactResponse\x12i\n" +
	"\n" +
	"GetContact\x12,.backend.proto.customer.v2.GetContactRequest\x1a-.backend.proto.customer.v2.GetContactResponse\x12o\n" +
	"\fListContacts\x12..backend.proto.customer.v2.ListContactsRequest\x1a/.backend.proto.customer.v2.ListContactsResponse\x12r\n" +
	"\rUpdateContact\x12/.backend.proto.customer.v2.UpdateContactRequest\x1a0.backend.proto.customer.v2.UpdateContactResponse\x12r\n" +
	"\rDeleteContact\x12/.backend.proto.customer.v2.DeleteContactRequest\x1a0.backend.proto.customer.v2.DeleteContactResponse\x12\x96\x01\n" +
	"\x19CreateCustomerRelatedData\x12;.backend.proto.customer.v2.CreateCustomerRelatedDataRequest\x1a<.backend.proto.customer.v2.CreateCustomerRelatedDataResponse\x12\x8d\x01\n" +
	"\x16GetCustomerRelatedData\x128.backend.proto.customer.v2.GetCustomerRelatedDataRequest\x1a9.backend.proto.customer.v2.GetCustomerRelatedDataResponse\x12\x90\x01\n" +
	"\x17ListCustomerRelatedData\x129.backend.proto.customer.v2.ListCustomerRelatedDataRequest\x1a:.backend.proto.customer.v2.ListCustomerRelatedDataResponse\x12\x96\x01\n" +
	"\x19UpdateCustomerRelatedData\x12;.backend.proto.customer.v2.UpdateCustomerRelatedDataRequest\x1a<.backend.proto.customer.v2.UpdateCustomerRelatedDataResponse\x12\x96\x01\n" +
	"\x19DeleteCustomerRelatedData\x12;.backend.proto.customer.v2.DeleteCustomerRelatedDataRequest\x1a<.backend.proto.customer.v2.DeleteCustomerRelatedDataResponse\x12{\n" +
	"\x10CreateContactTag\x122.backend.proto.customer.v2.CreateContactTagRequest\x1a3.backend.proto.customer.v2.CreateContactTagResponse\x12r\n" +
	"\rGetContactTag\x12/.backend.proto.customer.v2.GetContactTagRequest\x1a0.backend.proto.customer.v2.GetContactTagResponse\x12x\n" +
	"\x0fListContactTags\x121.backend.proto.customer.v2.ListContactTagsRequest\x1a2.backend.proto.customer.v2.ListContactTagsResponse\x12{\n" +
	"\x10UpdateContactTag\x122.backend.proto.customer.v2.UpdateContactTagRequest\x1a3.backend.proto.customer.v2.UpdateContactTagResponse\x12{\n" +
	"\x10DeleteContactTag\x122.backend.proto.customer.v2.DeleteContactTagRequest\x1a3.backend.proto.customer.v2.DeleteContactTagResponse\x12i\n" +
	"\n" +
	"CreateLead\x12,.backend.proto.customer.v2.CreateLeadRequest\x1a-.backend.proto.customer.v2.CreateLeadResponse\x12`\n" +
	"\aGetLead\x12).backend.proto.customer.v2.GetLeadRequest\x1a*.backend.proto.customer.v2.GetLeadResponse\x12f\n" +
	"\tListLeads\x12+.backend.proto.customer.v2.ListLeadsRequest\x1a,.backend.proto.customer.v2.ListLeadsResponse\x12i\n" +
	"\n" +
	"UpdateLead\x12,.backend.proto.customer.v2.UpdateLeadRequest\x1a-.backend.proto.customer.v2.UpdateLeadResponse\x12i\n" +
	"\n" +
	"DeleteLead\x12,.backend.proto.customer.v2.DeleteLeadRequest\x1a-.backend.proto.customer.v2.DeleteLeadResponse\x12r\n" +
	"\rCreateAddress\x12/.backend.proto.customer.v2.CreateAddressRequest\x1a0.backend.proto.customer.v2.CreateAddressResponse\x12i\n" +
	"\n" +
	"GetAddress\x12,.backend.proto.customer.v2.GetAddressRequest\x1a-.backend.proto.customer.v2.GetAddressResponse\x12r\n" +
	"\rListAddresses\x12/.backend.proto.customer.v2.ListAddressesRequest\x1a0.backend.proto.customer.v2.ListAddressesResponse\x12r\n" +
	"\rUpdateAddress\x12/.backend.proto.customer.v2.UpdateAddressRequest\x1a0.backend.proto.customer.v2.UpdateAddressResponse\x12r\n" +
	"\rDeleteAddress\x12/.backend.proto.customer.v2.DeleteAddressRequest\x1a0.backend.proto.customer.v2.DeleteAddressResponse\x12~\n" +
	"\x11CreateCustomField\x123.backend.proto.customer.v2.CreateCustomFieldRequest\x1a4.backend.proto.customer.v2.CreateCustomFieldResponse\x12u\n" +
	"\x0eGetCustomField\x120.backend.proto.customer.v2.GetCustomFieldRequest\x1a1.backend.proto.customer.v2.GetCustomFieldResponse\x12{\n" +
	"\x10ListCustomFields\x122.backend.proto.customer.v2.ListCustomFieldsRequest\x1a3.backend.proto.customer.v2.ListCustomFieldsResponse\x12~\n" +
	"\x11UpdateCustomField\x123.backend.proto.customer.v2.UpdateCustomFieldRequest\x1a4.backend.proto.customer.v2.UpdateCustomFieldResponse\x12\x90\x01\n" +
	"\x17BatchUpdateCustomFields\x129.backend.proto.customer.v2.BatchUpdateCustomFieldsRequest\x1a:.backend.proto.customer.v2.BatchUpdateCustomFieldsResponse\x12~\n" +
	"\x11DeleteCustomField\x123.backend.proto.customer.v2.DeleteCustomFieldRequest\x1a4.backend.proto.customer.v2.DeleteCustomFieldResponse\x12u\n" +
	"\x0eCreateMetadata\x120.backend.proto.customer.v2.CreateMetadataRequest\x1a1.backend.proto.customer.v2.CreateMetadataResponse\x12l\n" +
	"\vGetMetadata\x12-.backend.proto.customer.v2.GetMetadataRequest\x1a..backend.proto.customer.v2.GetMetadataResponse\x12o\n" +
	"\fListMetadata\x12..backend.proto.customer.v2.ListMetadataRequest\x1a/.backend.proto.customer.v2.ListMetadataResponse\x12u\n" +
	"\x0eUpdateMetadata\x120.backend.proto.customer.v2.UpdateMetadataRequest\x1a1.backend.proto.customer.v2.UpdateMetadataResponse\x12u\n" +
	"\x0eDeleteMetadata\x120.backend.proto.customer.v2.DeleteMetadataRequest\x1a1.backend.proto.customer.v2.DeleteMetadataResponse\x12\x90\x01\n" +
	"\x17CreateMetadataAggregate\x129.backend.proto.customer.v2.CreateMetadataAggregateRequest\x1a:.backend.proto.customer.v2.CreateMetadataAggregateResponseBk\n" +
	"#com.moego.backend.proto.customer.v2P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/customer/v2;customerpbb\x06proto3"

var (
	file_backend_proto_customer_v2_metadata_service_proto_rawDescOnce sync.Once
	file_backend_proto_customer_v2_metadata_service_proto_rawDescData []byte
)

func file_backend_proto_customer_v2_metadata_service_proto_rawDescGZIP() []byte {
	file_backend_proto_customer_v2_metadata_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_customer_v2_metadata_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v2_metadata_service_proto_rawDesc), len(file_backend_proto_customer_v2_metadata_service_proto_rawDesc)))
	})
	return file_backend_proto_customer_v2_metadata_service_proto_rawDescData
}

var file_backend_proto_customer_v2_metadata_service_proto_enumTypes = make([]protoimpl.EnumInfo, 9)
var file_backend_proto_customer_v2_metadata_service_proto_msgTypes = make([]protoimpl.MessageInfo, 102)
var file_backend_proto_customer_v2_metadata_service_proto_goTypes = []any{
	(ErrCode)(0),                                      // 0: backend.proto.customer.v2.ErrCode
	(ListCustomersRequest_Sorting_Field)(0),           // 1: backend.proto.customer.v2.ListCustomersRequest.Sorting.Field
	(ListContactsRequest_Sorting_Field)(0),            // 2: backend.proto.customer.v2.ListContactsRequest.Sorting.Field
	(ListCustomerRelatedDataRequest_Sorting_Field)(0), // 3: backend.proto.customer.v2.ListCustomerRelatedDataRequest.Sorting.Field
	(ListContactTagsRequest_Sorting_Field)(0),         // 4: backend.proto.customer.v2.ListContactTagsRequest.Sorting.Field
	(ListLeadsRequest_Sorting_Field)(0),               // 5: backend.proto.customer.v2.ListLeadsRequest.Sorting.Field
	(ListAddressesRequest_Sorting_Field)(0),           // 6: backend.proto.customer.v2.ListAddressesRequest.Sorting.Field
	(ListCustomFieldsRequest_Sorting_Field)(0),        // 7: backend.proto.customer.v2.ListCustomFieldsRequest.Sorting.Field
	(ListMetadataRequest_Sorting_Field)(0),            // 8: backend.proto.customer.v2.ListMetadataRequest.Sorting.Field
	(*CreateCustomerRequest)(nil),                     // 9: backend.proto.customer.v2.CreateCustomerRequest
	(*CreateCustomerResponse)(nil),                    // 10: backend.proto.customer.v2.CreateCustomerResponse
	(*GetCustomerRequest)(nil),                        // 11: backend.proto.customer.v2.GetCustomerRequest
	(*GetCustomerResponse)(nil),                       // 12: backend.proto.customer.v2.GetCustomerResponse
	(*ListCustomersRequest)(nil),                      // 13: backend.proto.customer.v2.ListCustomersRequest
	(*ListCustomersResponse)(nil),                     // 14: backend.proto.customer.v2.ListCustomersResponse
	(*UpdateCustomerRequest)(nil),                     // 15: backend.proto.customer.v2.UpdateCustomerRequest
	(*UpdateCustomerResponse)(nil),                    // 16: backend.proto.customer.v2.UpdateCustomerResponse
	(*DeleteCustomerRequest)(nil),                     // 17: backend.proto.customer.v2.DeleteCustomerRequest
	(*DeleteCustomerResponse)(nil),                    // 18: backend.proto.customer.v2.DeleteCustomerResponse
	(*CreateCustomerAggregateRequest)(nil),            // 19: backend.proto.customer.v2.CreateCustomerAggregateRequest
	(*CreateCustomerAggregateResponse)(nil),           // 20: backend.proto.customer.v2.CreateCustomerAggregateResponse
	(*CreateContactRequest)(nil),                      // 21: backend.proto.customer.v2.CreateContactRequest
	(*CreateContactResponse)(nil),                     // 22: backend.proto.customer.v2.CreateContactResponse
	(*GetContactRequest)(nil),                         // 23: backend.proto.customer.v2.GetContactRequest
	(*GetContactResponse)(nil),                        // 24: backend.proto.customer.v2.GetContactResponse
	(*ListContactsRequest)(nil),                       // 25: backend.proto.customer.v2.ListContactsRequest
	(*ListContactsResponse)(nil),                      // 26: backend.proto.customer.v2.ListContactsResponse
	(*UpdateContactRequest)(nil),                      // 27: backend.proto.customer.v2.UpdateContactRequest
	(*UpdateContactResponse)(nil),                     // 28: backend.proto.customer.v2.UpdateContactResponse
	(*DeleteContactRequest)(nil),                      // 29: backend.proto.customer.v2.DeleteContactRequest
	(*DeleteContactResponse)(nil),                     // 30: backend.proto.customer.v2.DeleteContactResponse
	(*CreateCustomerRelatedDataRequest)(nil),          // 31: backend.proto.customer.v2.CreateCustomerRelatedDataRequest
	(*CreateCustomerRelatedDataResponse)(nil),         // 32: backend.proto.customer.v2.CreateCustomerRelatedDataResponse
	(*GetCustomerRelatedDataRequest)(nil),             // 33: backend.proto.customer.v2.GetCustomerRelatedDataRequest
	(*GetCustomerRelatedDataResponse)(nil),            // 34: backend.proto.customer.v2.GetCustomerRelatedDataResponse
	(*ListCustomerRelatedDataRequest)(nil),            // 35: backend.proto.customer.v2.ListCustomerRelatedDataRequest
	(*ListCustomerRelatedDataResponse)(nil),           // 36: backend.proto.customer.v2.ListCustomerRelatedDataResponse
	(*UpdateCustomerRelatedDataRequest)(nil),          // 37: backend.proto.customer.v2.UpdateCustomerRelatedDataRequest
	(*UpdateCustomerRelatedDataResponse)(nil),         // 38: backend.proto.customer.v2.UpdateCustomerRelatedDataResponse
	(*DeleteCustomerRelatedDataRequest)(nil),          // 39: backend.proto.customer.v2.DeleteCustomerRelatedDataRequest
	(*DeleteCustomerRelatedDataResponse)(nil),         // 40: backend.proto.customer.v2.DeleteCustomerRelatedDataResponse
	(*CreateContactTagRequest)(nil),                   // 41: backend.proto.customer.v2.CreateContactTagRequest
	(*CreateContactTagResponse)(nil),                  // 42: backend.proto.customer.v2.CreateContactTagResponse
	(*GetContactTagRequest)(nil),                      // 43: backend.proto.customer.v2.GetContactTagRequest
	(*GetContactTagResponse)(nil),                     // 44: backend.proto.customer.v2.GetContactTagResponse
	(*ListContactTagsRequest)(nil),                    // 45: backend.proto.customer.v2.ListContactTagsRequest
	(*ListContactTagsResponse)(nil),                   // 46: backend.proto.customer.v2.ListContactTagsResponse
	(*UpdateContactTagRequest)(nil),                   // 47: backend.proto.customer.v2.UpdateContactTagRequest
	(*UpdateContactTagResponse)(nil),                  // 48: backend.proto.customer.v2.UpdateContactTagResponse
	(*DeleteContactTagRequest)(nil),                   // 49: backend.proto.customer.v2.DeleteContactTagRequest
	(*DeleteContactTagResponse)(nil),                  // 50: backend.proto.customer.v2.DeleteContactTagResponse
	(*CreateLeadRequest)(nil),                         // 51: backend.proto.customer.v2.CreateLeadRequest
	(*CreateLeadResponse)(nil),                        // 52: backend.proto.customer.v2.CreateLeadResponse
	(*GetLeadRequest)(nil),                            // 53: backend.proto.customer.v2.GetLeadRequest
	(*GetLeadResponse)(nil),                           // 54: backend.proto.customer.v2.GetLeadResponse
	(*ListLeadsRequest)(nil),                          // 55: backend.proto.customer.v2.ListLeadsRequest
	(*ListLeadsResponse)(nil),                         // 56: backend.proto.customer.v2.ListLeadsResponse
	(*UpdateLeadRequest)(nil),                         // 57: backend.proto.customer.v2.UpdateLeadRequest
	(*UpdateLeadResponse)(nil),                        // 58: backend.proto.customer.v2.UpdateLeadResponse
	(*DeleteLeadRequest)(nil),                         // 59: backend.proto.customer.v2.DeleteLeadRequest
	(*DeleteLeadResponse)(nil),                        // 60: backend.proto.customer.v2.DeleteLeadResponse
	(*CreateAddressRequest)(nil),                      // 61: backend.proto.customer.v2.CreateAddressRequest
	(*CreateAddressResponse)(nil),                     // 62: backend.proto.customer.v2.CreateAddressResponse
	(*GetAddressRequest)(nil),                         // 63: backend.proto.customer.v2.GetAddressRequest
	(*GetAddressResponse)(nil),                        // 64: backend.proto.customer.v2.GetAddressResponse
	(*ListAddressesRequest)(nil),                      // 65: backend.proto.customer.v2.ListAddressesRequest
	(*ListAddressesResponse)(nil),                     // 66: backend.proto.customer.v2.ListAddressesResponse
	(*UpdateAddressRequest)(nil),                      // 67: backend.proto.customer.v2.UpdateAddressRequest
	(*UpdateAddressResponse)(nil),                     // 68: backend.proto.customer.v2.UpdateAddressResponse
	(*DeleteAddressRequest)(nil),                      // 69: backend.proto.customer.v2.DeleteAddressRequest
	(*DeleteAddressResponse)(nil),                     // 70: backend.proto.customer.v2.DeleteAddressResponse
	(*CreateCustomFieldRequest)(nil),                  // 71: backend.proto.customer.v2.CreateCustomFieldRequest
	(*CreateCustomFieldResponse)(nil),                 // 72: backend.proto.customer.v2.CreateCustomFieldResponse
	(*GetCustomFieldRequest)(nil),                     // 73: backend.proto.customer.v2.GetCustomFieldRequest
	(*GetCustomFieldResponse)(nil),                    // 74: backend.proto.customer.v2.GetCustomFieldResponse
	(*ListCustomFieldsRequest)(nil),                   // 75: backend.proto.customer.v2.ListCustomFieldsRequest
	(*ListCustomFieldsResponse)(nil),                  // 76: backend.proto.customer.v2.ListCustomFieldsResponse
	(*UpdateCustomFieldRequest)(nil),                  // 77: backend.proto.customer.v2.UpdateCustomFieldRequest
	(*UpdateCustomFieldResponse)(nil),                 // 78: backend.proto.customer.v2.UpdateCustomFieldResponse
	(*BatchUpdateCustomFieldsRequest)(nil),            // 79: backend.proto.customer.v2.BatchUpdateCustomFieldsRequest
	(*BatchUpdateCustomFieldsResponse)(nil),           // 80: backend.proto.customer.v2.BatchUpdateCustomFieldsResponse
	(*DeleteCustomFieldRequest)(nil),                  // 81: backend.proto.customer.v2.DeleteCustomFieldRequest
	(*DeleteCustomFieldResponse)(nil),                 // 82: backend.proto.customer.v2.DeleteCustomFieldResponse
	(*CreateMetadataRequest)(nil),                     // 83: backend.proto.customer.v2.CreateMetadataRequest
	(*CreateMetadataResponse)(nil),                    // 84: backend.proto.customer.v2.CreateMetadataResponse
	(*GetMetadataRequest)(nil),                        // 85: backend.proto.customer.v2.GetMetadataRequest
	(*GetMetadataResponse)(nil),                       // 86: backend.proto.customer.v2.GetMetadataResponse
	(*ListMetadataRequest)(nil),                       // 87: backend.proto.customer.v2.ListMetadataRequest
	(*ListMetadataResponse)(nil),                      // 88: backend.proto.customer.v2.ListMetadataResponse
	(*UpdateMetadataRequest)(nil),                     // 89: backend.proto.customer.v2.UpdateMetadataRequest
	(*UpdateMetadataResponse)(nil),                    // 90: backend.proto.customer.v2.UpdateMetadataResponse
	(*DeleteMetadataRequest)(nil),                     // 91: backend.proto.customer.v2.DeleteMetadataRequest
	(*DeleteMetadataResponse)(nil),                    // 92: backend.proto.customer.v2.DeleteMetadataResponse
	(*CreateMetadataAggregateRequest)(nil),            // 93: backend.proto.customer.v2.CreateMetadataAggregateRequest
	(*CreateMetadataAggregateResponse)(nil),           // 94: backend.proto.customer.v2.CreateMetadataAggregateResponse
	(*ListCustomersRequest_Filter)(nil),               // 95: backend.proto.customer.v2.ListCustomersRequest.Filter
	(*ListCustomersRequest_Sorting)(nil),              // 96: backend.proto.customer.v2.ListCustomersRequest.Sorting
	(*ListContactsRequest_Filter)(nil),                // 97: backend.proto.customer.v2.ListContactsRequest.Filter
	(*ListContactsRequest_Sorting)(nil),               // 98: backend.proto.customer.v2.ListContactsRequest.Sorting
	(*ListCustomerRelatedDataRequest_Filter)(nil),     // 99: backend.proto.customer.v2.ListCustomerRelatedDataRequest.Filter
	(*ListCustomerRelatedDataRequest_Sorting)(nil),    // 100: backend.proto.customer.v2.ListCustomerRelatedDataRequest.Sorting
	(*ListContactTagsRequest_Filter)(nil),             // 101: backend.proto.customer.v2.ListContactTagsRequest.Filter
	(*ListContactTagsRequest_Sorting)(nil),            // 102: backend.proto.customer.v2.ListContactTagsRequest.Sorting
	(*ListLeadsRequest_Filter)(nil),                   // 103: backend.proto.customer.v2.ListLeadsRequest.Filter
	(*ListLeadsRequest_Sorting)(nil),                  // 104: backend.proto.customer.v2.ListLeadsRequest.Sorting
	(*ListAddressesRequest_Filter)(nil),               // 105: backend.proto.customer.v2.ListAddressesRequest.Filter
	(*ListAddressesRequest_Sorting)(nil),              // 106: backend.proto.customer.v2.ListAddressesRequest.Sorting
	(*ListCustomFieldsRequest_Filter)(nil),            // 107: backend.proto.customer.v2.ListCustomFieldsRequest.Filter
	(*ListCustomFieldsRequest_Sorting)(nil),           // 108: backend.proto.customer.v2.ListCustomFieldsRequest.Sorting
	(*ListMetadataRequest_Filter)(nil),                // 109: backend.proto.customer.v2.ListMetadataRequest.Filter
	(*ListMetadataRequest_Sorting)(nil),               // 110: backend.proto.customer.v2.ListMetadataRequest.Sorting
	(*OrganizationRef)(nil),                           // 111: backend.proto.customer.v2.OrganizationRef
	(*structpb.Struct)(nil),                           // 112: google.protobuf.Struct
	(*Customer)(nil),                                  // 113: backend.proto.customer.v2.Customer
	(Customer_State)(0),                               // 114: backend.proto.customer.v2.Customer.State
	(*Contact)(nil),                                   // 115: backend.proto.customer.v2.Contact
	(*Address)(nil),                                   // 116: backend.proto.customer.v2.Address
	(*CustomerRelatedData)(nil),                       // 117: backend.proto.customer.v2.CustomerRelatedData
	(*CustomerAggregate)(nil),                         // 118: backend.proto.customer.v2.CustomerAggregate
	(*phone_number.PhoneNumber)(nil),                  // 119: google.type.PhoneNumber
	(*ContactTag)(nil),                                // 120: backend.proto.customer.v2.ContactTag
	(Contact_State)(0),                                // 121: backend.proto.customer.v2.Contact.State
	(*timestamppb.Timestamp)(nil),                     // 122: google.protobuf.Timestamp
	(ContactTag_State)(0),                             // 123: backend.proto.customer.v2.ContactTag.State
	(ContactTag_Type)(0),                              // 124: backend.proto.customer.v2.ContactTag.Type
	(*Lead)(nil),                                      // 125: backend.proto.customer.v2.Lead
	(Lead_State)(0),                                   // 126: backend.proto.customer.v2.Lead.State
	(*postaladdress.PostalAddress)(nil),               // 127: google.type.PostalAddress
	(*latlng.LatLng)(nil),                             // 128: google.type.LatLng
	(Address_Type)(0),                                 // 129: backend.proto.customer.v2.Address.Type
	(Address_State)(0),                                // 130: backend.proto.customer.v2.Address.State
	(CustomField_Type)(0),                             // 131: backend.proto.customer.v2.CustomField.Type
	(CustomField_AssociationType)(0),                  // 132: backend.proto.customer.v2.CustomField.AssociationType
	(*CustomField_Value)(nil),                         // 133: backend.proto.customer.v2.CustomField.Value
	(*CustomField_Option)(nil),                        // 134: backend.proto.customer.v2.CustomField.Option
	(*CustomField)(nil),                               // 135: backend.proto.customer.v2.CustomField
	(CustomField_State)(0),                            // 136: backend.proto.customer.v2.CustomField.State
	(CustomerType)(0),                                 // 137: backend.proto.customer.v2.CustomerType
	(*Metadata)(nil),                                  // 138: backend.proto.customer.v2.Metadata
	(Metadata_State)(0),                               // 139: backend.proto.customer.v2.Metadata.State
	(*MetadataAggregate)(nil),                         // 140: backend.proto.customer.v2.MetadataAggregate
	(Direction)(0),                                    // 141: backend.proto.customer.v2.Direction
	(CustomerRelatedData_State)(0),                    // 142: backend.proto.customer.v2.CustomerRelatedData.State
}
var file_backend_proto_customer_v2_metadata_service_proto_depIdxs = []int32{
	111, // 0: backend.proto.customer.v2.CreateCustomerRequest.organization:type_name -> backend.proto.customer.v2.OrganizationRef
	112, // 1: backend.proto.customer.v2.CreateCustomerRequest.custom_fields:type_name -> google.protobuf.Struct
	113, // 2: backend.proto.customer.v2.CreateCustomerResponse.customer:type_name -> backend.proto.customer.v2.Customer
	113, // 3: backend.proto.customer.v2.GetCustomerResponse.customer:type_name -> backend.proto.customer.v2.Customer
	95,  // 4: backend.proto.customer.v2.ListCustomersRequest.filter:type_name -> backend.proto.customer.v2.ListCustomersRequest.Filter
	96,  // 5: backend.proto.customer.v2.ListCustomersRequest.sorting:type_name -> backend.proto.customer.v2.ListCustomersRequest.Sorting
	113, // 6: backend.proto.customer.v2.ListCustomersResponse.customers:type_name -> backend.proto.customer.v2.Customer
	112, // 7: backend.proto.customer.v2.UpdateCustomerRequest.custom_fields:type_name -> google.protobuf.Struct
	114, // 8: backend.proto.customer.v2.UpdateCustomerRequest.state:type_name -> backend.proto.customer.v2.Customer.State
	113, // 9: backend.proto.customer.v2.UpdateCustomerResponse.customer:type_name -> backend.proto.customer.v2.Customer
	111, // 10: backend.proto.customer.v2.CreateCustomerAggregateRequest.organization:type_name -> backend.proto.customer.v2.OrganizationRef
	113, // 11: backend.proto.customer.v2.CreateCustomerAggregateRequest.customer:type_name -> backend.proto.customer.v2.Customer
	115, // 12: backend.proto.customer.v2.CreateCustomerAggregateRequest.contacts:type_name -> backend.proto.customer.v2.Contact
	116, // 13: backend.proto.customer.v2.CreateCustomerAggregateRequest.addresses:type_name -> backend.proto.customer.v2.Address
	117, // 14: backend.proto.customer.v2.CreateCustomerAggregateRequest.customer_related_data:type_name -> backend.proto.customer.v2.CustomerRelatedData
	118, // 15: backend.proto.customer.v2.CreateCustomerAggregateResponse.customer_aggregate:type_name -> backend.proto.customer.v2.CustomerAggregate
	119, // 16: backend.proto.customer.v2.CreateContactRequest.phone:type_name -> google.type.PhoneNumber
	120, // 17: backend.proto.customer.v2.CreateContactRequest.tags:type_name -> backend.proto.customer.v2.ContactTag
	115, // 18: backend.proto.customer.v2.CreateContactResponse.contact:type_name -> backend.proto.customer.v2.Contact
	115, // 19: backend.proto.customer.v2.GetContactResponse.contact:type_name -> backend.proto.customer.v2.Contact
	97,  // 20: backend.proto.customer.v2.ListContactsRequest.filter:type_name -> backend.proto.customer.v2.ListContactsRequest.Filter
	98,  // 21: backend.proto.customer.v2.ListContactsRequest.sorting:type_name -> backend.proto.customer.v2.ListContactsRequest.Sorting
	115, // 22: backend.proto.customer.v2.ListContactsResponse.contacts:type_name -> backend.proto.customer.v2.Contact
	119, // 23: backend.proto.customer.v2.UpdateContactRequest.phone:type_name -> google.type.PhoneNumber
	121, // 24: backend.proto.customer.v2.UpdateContactRequest.state:type_name -> backend.proto.customer.v2.Contact.State
	115, // 25: backend.proto.customer.v2.UpdateContactResponse.contact:type_name -> backend.proto.customer.v2.Contact
	122, // 26: backend.proto.customer.v2.CreateCustomerRelatedDataRequest.birthday:type_name -> google.protobuf.Timestamp
	117, // 27: backend.proto.customer.v2.CreateCustomerRelatedDataResponse.customer_related_data:type_name -> backend.proto.customer.v2.CustomerRelatedData
	117, // 28: backend.proto.customer.v2.GetCustomerRelatedDataResponse.customer_related_data:type_name -> backend.proto.customer.v2.CustomerRelatedData
	99,  // 29: backend.proto.customer.v2.ListCustomerRelatedDataRequest.filter:type_name -> backend.proto.customer.v2.ListCustomerRelatedDataRequest.Filter
	100, // 30: backend.proto.customer.v2.ListCustomerRelatedDataRequest.sorting:type_name -> backend.proto.customer.v2.ListCustomerRelatedDataRequest.Sorting
	117, // 31: backend.proto.customer.v2.ListCustomerRelatedDataResponse.customer_related_data:type_name -> backend.proto.customer.v2.CustomerRelatedData
	122, // 32: backend.proto.customer.v2.UpdateCustomerRelatedDataRequest.birthday:type_name -> google.protobuf.Timestamp
	117, // 33: backend.proto.customer.v2.UpdateCustomerRelatedDataResponse.customer_related_data:type_name -> backend.proto.customer.v2.CustomerRelatedData
	111, // 34: backend.proto.customer.v2.CreateContactTagRequest.organization:type_name -> backend.proto.customer.v2.OrganizationRef
	123, // 35: backend.proto.customer.v2.CreateContactTagRequest.state:type_name -> backend.proto.customer.v2.ContactTag.State
	124, // 36: backend.proto.customer.v2.CreateContactTagRequest.type:type_name -> backend.proto.customer.v2.ContactTag.Type
	120, // 37: backend.proto.customer.v2.CreateContactTagResponse.contact_tag:type_name -> backend.proto.customer.v2.ContactTag
	120, // 38: backend.proto.customer.v2.GetContactTagResponse.contact_tag:type_name -> backend.proto.customer.v2.ContactTag
	101, // 39: backend.proto.customer.v2.ListContactTagsRequest.filter:type_name -> backend.proto.customer.v2.ListContactTagsRequest.Filter
	102, // 40: backend.proto.customer.v2.ListContactTagsRequest.sorting:type_name -> backend.proto.customer.v2.ListContactTagsRequest.Sorting
	120, // 41: backend.proto.customer.v2.ListContactTagsResponse.contact_tags:type_name -> backend.proto.customer.v2.ContactTag
	123, // 42: backend.proto.customer.v2.UpdateContactTagRequest.state:type_name -> backend.proto.customer.v2.ContactTag.State
	120, // 43: backend.proto.customer.v2.UpdateContactTagResponse.contact_tag:type_name -> backend.proto.customer.v2.ContactTag
	111, // 44: backend.proto.customer.v2.CreateLeadRequest.organization:type_name -> backend.proto.customer.v2.OrganizationRef
	112, // 45: backend.proto.customer.v2.CreateLeadRequest.custom_fields:type_name -> google.protobuf.Struct
	125, // 46: backend.proto.customer.v2.CreateLeadResponse.lead:type_name -> backend.proto.customer.v2.Lead
	125, // 47: backend.proto.customer.v2.GetLeadResponse.lead:type_name -> backend.proto.customer.v2.Lead
	103, // 48: backend.proto.customer.v2.ListLeadsRequest.filter:type_name -> backend.proto.customer.v2.ListLeadsRequest.Filter
	104, // 49: backend.proto.customer.v2.ListLeadsRequest.sorting:type_name -> backend.proto.customer.v2.ListLeadsRequest.Sorting
	125, // 50: backend.proto.customer.v2.ListLeadsResponse.leads:type_name -> backend.proto.customer.v2.Lead
	112, // 51: backend.proto.customer.v2.UpdateLeadRequest.custom_fields:type_name -> google.protobuf.Struct
	126, // 52: backend.proto.customer.v2.UpdateLeadRequest.state:type_name -> backend.proto.customer.v2.Lead.State
	125, // 53: backend.proto.customer.v2.UpdateLeadResponse.lead:type_name -> backend.proto.customer.v2.Lead
	127, // 54: backend.proto.customer.v2.CreateAddressRequest.address:type_name -> google.type.PostalAddress
	128, // 55: backend.proto.customer.v2.CreateAddressRequest.latlng:type_name -> google.type.LatLng
	129, // 56: backend.proto.customer.v2.CreateAddressRequest.type:type_name -> backend.proto.customer.v2.Address.Type
	111, // 57: backend.proto.customer.v2.CreateAddressRequest.organization:type_name -> backend.proto.customer.v2.OrganizationRef
	116, // 58: backend.proto.customer.v2.CreateAddressResponse.address:type_name -> backend.proto.customer.v2.Address
	116, // 59: backend.proto.customer.v2.GetAddressResponse.address:type_name -> backend.proto.customer.v2.Address
	105, // 60: backend.proto.customer.v2.ListAddressesRequest.filter:type_name -> backend.proto.customer.v2.ListAddressesRequest.Filter
	106, // 61: backend.proto.customer.v2.ListAddressesRequest.sorting:type_name -> backend.proto.customer.v2.ListAddressesRequest.Sorting
	116, // 62: backend.proto.customer.v2.ListAddressesResponse.addresses:type_name -> backend.proto.customer.v2.Address
	127, // 63: backend.proto.customer.v2.UpdateAddressRequest.address:type_name -> google.type.PostalAddress
	128, // 64: backend.proto.customer.v2.UpdateAddressRequest.latlng:type_name -> google.type.LatLng
	129, // 65: backend.proto.customer.v2.UpdateAddressRequest.type:type_name -> backend.proto.customer.v2.Address.Type
	130, // 66: backend.proto.customer.v2.UpdateAddressRequest.state:type_name -> backend.proto.customer.v2.Address.State
	116, // 67: backend.proto.customer.v2.UpdateAddressResponse.address:type_name -> backend.proto.customer.v2.Address
	111, // 68: backend.proto.customer.v2.CreateCustomFieldRequest.organization:type_name -> backend.proto.customer.v2.OrganizationRef
	131, // 69: backend.proto.customer.v2.CreateCustomFieldRequest.type:type_name -> backend.proto.customer.v2.CustomField.Type
	132, // 70: backend.proto.customer.v2.CreateCustomFieldRequest.association_type:type_name -> backend.proto.customer.v2.CustomField.AssociationType
	133, // 71: backend.proto.customer.v2.CreateCustomFieldRequest.default_value:type_name -> backend.proto.customer.v2.CustomField.Value
	134, // 72: backend.proto.customer.v2.CreateCustomFieldRequest.options:type_name -> backend.proto.customer.v2.CustomField.Option
	135, // 73: backend.proto.customer.v2.CreateCustomFieldResponse.custom_field:type_name -> backend.proto.customer.v2.CustomField
	135, // 74: backend.proto.customer.v2.GetCustomFieldResponse.custom_field:type_name -> backend.proto.customer.v2.CustomField
	107, // 75: backend.proto.customer.v2.ListCustomFieldsRequest.filter:type_name -> backend.proto.customer.v2.ListCustomFieldsRequest.Filter
	108, // 76: backend.proto.customer.v2.ListCustomFieldsRequest.sorting:type_name -> backend.proto.customer.v2.ListCustomFieldsRequest.Sorting
	135, // 77: backend.proto.customer.v2.ListCustomFieldsResponse.custom_fields:type_name -> backend.proto.customer.v2.CustomField
	136, // 78: backend.proto.customer.v2.UpdateCustomFieldRequest.state:type_name -> backend.proto.customer.v2.CustomField.State
	133, // 79: backend.proto.customer.v2.UpdateCustomFieldRequest.default_value:type_name -> backend.proto.customer.v2.CustomField.Value
	134, // 80: backend.proto.customer.v2.UpdateCustomFieldRequest.options:type_name -> backend.proto.customer.v2.CustomField.Option
	135, // 81: backend.proto.customer.v2.UpdateCustomFieldResponse.custom_field:type_name -> backend.proto.customer.v2.CustomField
	77,  // 82: backend.proto.customer.v2.BatchUpdateCustomFieldsRequest.requests:type_name -> backend.proto.customer.v2.UpdateCustomFieldRequest
	135, // 83: backend.proto.customer.v2.BatchUpdateCustomFieldsResponse.custom_fields:type_name -> backend.proto.customer.v2.CustomField
	111, // 84: backend.proto.customer.v2.CreateMetadataRequest.organization:type_name -> backend.proto.customer.v2.OrganizationRef
	137, // 85: backend.proto.customer.v2.CreateMetadataRequest.customer_type:type_name -> backend.proto.customer.v2.CustomerType
	112, // 86: backend.proto.customer.v2.CreateMetadataRequest.custom_fields:type_name -> google.protobuf.Struct
	138, // 87: backend.proto.customer.v2.CreateMetadataResponse.metadata:type_name -> backend.proto.customer.v2.Metadata
	138, // 88: backend.proto.customer.v2.GetMetadataResponse.metadata:type_name -> backend.proto.customer.v2.Metadata
	109, // 89: backend.proto.customer.v2.ListMetadataRequest.filter:type_name -> backend.proto.customer.v2.ListMetadataRequest.Filter
	110, // 90: backend.proto.customer.v2.ListMetadataRequest.sorting:type_name -> backend.proto.customer.v2.ListMetadataRequest.Sorting
	138, // 91: backend.proto.customer.v2.ListMetadataResponse.metadata:type_name -> backend.proto.customer.v2.Metadata
	137, // 92: backend.proto.customer.v2.UpdateMetadataRequest.customer_type:type_name -> backend.proto.customer.v2.CustomerType
	112, // 93: backend.proto.customer.v2.UpdateMetadataRequest.custom_fields:type_name -> google.protobuf.Struct
	139, // 94: backend.proto.customer.v2.UpdateMetadataRequest.state:type_name -> backend.proto.customer.v2.Metadata.State
	138, // 95: backend.proto.customer.v2.UpdateMetadataResponse.metadata:type_name -> backend.proto.customer.v2.Metadata
	137, // 96: backend.proto.customer.v2.DeleteMetadataRequest.customer_type:type_name -> backend.proto.customer.v2.CustomerType
	111, // 97: backend.proto.customer.v2.CreateMetadataAggregateRequest.organization:type_name -> backend.proto.customer.v2.OrganizationRef
	137, // 98: backend.proto.customer.v2.CreateMetadataAggregateRequest.customer_type:type_name -> backend.proto.customer.v2.CustomerType
	138, // 99: backend.proto.customer.v2.CreateMetadataAggregateRequest.metadata:type_name -> backend.proto.customer.v2.Metadata
	115, // 100: backend.proto.customer.v2.CreateMetadataAggregateRequest.contacts:type_name -> backend.proto.customer.v2.Contact
	116, // 101: backend.proto.customer.v2.CreateMetadataAggregateRequest.addresses:type_name -> backend.proto.customer.v2.Address
	117, // 102: backend.proto.customer.v2.CreateMetadataAggregateRequest.customer_related_data:type_name -> backend.proto.customer.v2.CustomerRelatedData
	140, // 103: backend.proto.customer.v2.CreateMetadataAggregateResponse.metadata_aggregate:type_name -> backend.proto.customer.v2.MetadataAggregate
	111, // 104: backend.proto.customer.v2.ListCustomersRequest.Filter.organizations:type_name -> backend.proto.customer.v2.OrganizationRef
	114, // 105: backend.proto.customer.v2.ListCustomersRequest.Filter.states:type_name -> backend.proto.customer.v2.Customer.State
	1,   // 106: backend.proto.customer.v2.ListCustomersRequest.Sorting.field:type_name -> backend.proto.customer.v2.ListCustomersRequest.Sorting.Field
	141, // 107: backend.proto.customer.v2.ListCustomersRequest.Sorting.direction:type_name -> backend.proto.customer.v2.Direction
	121, // 108: backend.proto.customer.v2.ListContactsRequest.Filter.states:type_name -> backend.proto.customer.v2.Contact.State
	119, // 109: backend.proto.customer.v2.ListContactsRequest.Filter.phones:type_name -> google.type.PhoneNumber
	111, // 110: backend.proto.customer.v2.ListContactsRequest.Filter.organizations:type_name -> backend.proto.customer.v2.OrganizationRef
	2,   // 111: backend.proto.customer.v2.ListContactsRequest.Sorting.field:type_name -> backend.proto.customer.v2.ListContactsRequest.Sorting.Field
	141, // 112: backend.proto.customer.v2.ListContactsRequest.Sorting.direction:type_name -> backend.proto.customer.v2.Direction
	111, // 113: backend.proto.customer.v2.ListCustomerRelatedDataRequest.Filter.organizations:type_name -> backend.proto.customer.v2.OrganizationRef
	142, // 114: backend.proto.customer.v2.ListCustomerRelatedDataRequest.Filter.states:type_name -> backend.proto.customer.v2.CustomerRelatedData.State
	3,   // 115: backend.proto.customer.v2.ListCustomerRelatedDataRequest.Sorting.field:type_name -> backend.proto.customer.v2.ListCustomerRelatedDataRequest.Sorting.Field
	141, // 116: backend.proto.customer.v2.ListCustomerRelatedDataRequest.Sorting.direction:type_name -> backend.proto.customer.v2.Direction
	111, // 117: backend.proto.customer.v2.ListContactTagsRequest.Filter.organizations:type_name -> backend.proto.customer.v2.OrganizationRef
	123, // 118: backend.proto.customer.v2.ListContactTagsRequest.Filter.states:type_name -> backend.proto.customer.v2.ContactTag.State
	4,   // 119: backend.proto.customer.v2.ListContactTagsRequest.Sorting.field:type_name -> backend.proto.customer.v2.ListContactTagsRequest.Sorting.Field
	141, // 120: backend.proto.customer.v2.ListContactTagsRequest.Sorting.direction:type_name -> backend.proto.customer.v2.Direction
	111, // 121: backend.proto.customer.v2.ListLeadsRequest.Filter.organizations:type_name -> backend.proto.customer.v2.OrganizationRef
	126, // 122: backend.proto.customer.v2.ListLeadsRequest.Filter.states:type_name -> backend.proto.customer.v2.Lead.State
	5,   // 123: backend.proto.customer.v2.ListLeadsRequest.Sorting.field:type_name -> backend.proto.customer.v2.ListLeadsRequest.Sorting.Field
	141, // 124: backend.proto.customer.v2.ListLeadsRequest.Sorting.direction:type_name -> backend.proto.customer.v2.Direction
	130, // 125: backend.proto.customer.v2.ListAddressesRequest.Filter.states:type_name -> backend.proto.customer.v2.Address.State
	111, // 126: backend.proto.customer.v2.ListAddressesRequest.Filter.organizations:type_name -> backend.proto.customer.v2.OrganizationRef
	129, // 127: backend.proto.customer.v2.ListAddressesRequest.Filter.types:type_name -> backend.proto.customer.v2.Address.Type
	6,   // 128: backend.proto.customer.v2.ListAddressesRequest.Sorting.field:type_name -> backend.proto.customer.v2.ListAddressesRequest.Sorting.Field
	141, // 129: backend.proto.customer.v2.ListAddressesRequest.Sorting.direction:type_name -> backend.proto.customer.v2.Direction
	111, // 130: backend.proto.customer.v2.ListCustomFieldsRequest.Filter.organizations:type_name -> backend.proto.customer.v2.OrganizationRef
	132, // 131: backend.proto.customer.v2.ListCustomFieldsRequest.Filter.association_types:type_name -> backend.proto.customer.v2.CustomField.AssociationType
	131, // 132: backend.proto.customer.v2.ListCustomFieldsRequest.Filter.types:type_name -> backend.proto.customer.v2.CustomField.Type
	136, // 133: backend.proto.customer.v2.ListCustomFieldsRequest.Filter.states:type_name -> backend.proto.customer.v2.CustomField.State
	7,   // 134: backend.proto.customer.v2.ListCustomFieldsRequest.Sorting.field:type_name -> backend.proto.customer.v2.ListCustomFieldsRequest.Sorting.Field
	141, // 135: backend.proto.customer.v2.ListCustomFieldsRequest.Sorting.direction:type_name -> backend.proto.customer.v2.Direction
	111, // 136: backend.proto.customer.v2.ListMetadataRequest.Filter.organizations:type_name -> backend.proto.customer.v2.OrganizationRef
	137, // 137: backend.proto.customer.v2.ListMetadataRequest.Filter.customer_types:type_name -> backend.proto.customer.v2.CustomerType
	139, // 138: backend.proto.customer.v2.ListMetadataRequest.Filter.states:type_name -> backend.proto.customer.v2.Metadata.State
	8,   // 139: backend.proto.customer.v2.ListMetadataRequest.Sorting.field:type_name -> backend.proto.customer.v2.ListMetadataRequest.Sorting.Field
	141, // 140: backend.proto.customer.v2.ListMetadataRequest.Sorting.direction:type_name -> backend.proto.customer.v2.Direction
	9,   // 141: backend.proto.customer.v2.MetadataService.CreateCustomer:input_type -> backend.proto.customer.v2.CreateCustomerRequest
	11,  // 142: backend.proto.customer.v2.MetadataService.GetCustomer:input_type -> backend.proto.customer.v2.GetCustomerRequest
	13,  // 143: backend.proto.customer.v2.MetadataService.ListCustomers:input_type -> backend.proto.customer.v2.ListCustomersRequest
	15,  // 144: backend.proto.customer.v2.MetadataService.UpdateCustomer:input_type -> backend.proto.customer.v2.UpdateCustomerRequest
	17,  // 145: backend.proto.customer.v2.MetadataService.DeleteCustomer:input_type -> backend.proto.customer.v2.DeleteCustomerRequest
	19,  // 146: backend.proto.customer.v2.MetadataService.CreateCustomerAggregate:input_type -> backend.proto.customer.v2.CreateCustomerAggregateRequest
	21,  // 147: backend.proto.customer.v2.MetadataService.CreateContact:input_type -> backend.proto.customer.v2.CreateContactRequest
	23,  // 148: backend.proto.customer.v2.MetadataService.GetContact:input_type -> backend.proto.customer.v2.GetContactRequest
	25,  // 149: backend.proto.customer.v2.MetadataService.ListContacts:input_type -> backend.proto.customer.v2.ListContactsRequest
	27,  // 150: backend.proto.customer.v2.MetadataService.UpdateContact:input_type -> backend.proto.customer.v2.UpdateContactRequest
	29,  // 151: backend.proto.customer.v2.MetadataService.DeleteContact:input_type -> backend.proto.customer.v2.DeleteContactRequest
	31,  // 152: backend.proto.customer.v2.MetadataService.CreateCustomerRelatedData:input_type -> backend.proto.customer.v2.CreateCustomerRelatedDataRequest
	33,  // 153: backend.proto.customer.v2.MetadataService.GetCustomerRelatedData:input_type -> backend.proto.customer.v2.GetCustomerRelatedDataRequest
	35,  // 154: backend.proto.customer.v2.MetadataService.ListCustomerRelatedData:input_type -> backend.proto.customer.v2.ListCustomerRelatedDataRequest
	37,  // 155: backend.proto.customer.v2.MetadataService.UpdateCustomerRelatedData:input_type -> backend.proto.customer.v2.UpdateCustomerRelatedDataRequest
	39,  // 156: backend.proto.customer.v2.MetadataService.DeleteCustomerRelatedData:input_type -> backend.proto.customer.v2.DeleteCustomerRelatedDataRequest
	41,  // 157: backend.proto.customer.v2.MetadataService.CreateContactTag:input_type -> backend.proto.customer.v2.CreateContactTagRequest
	43,  // 158: backend.proto.customer.v2.MetadataService.GetContactTag:input_type -> backend.proto.customer.v2.GetContactTagRequest
	45,  // 159: backend.proto.customer.v2.MetadataService.ListContactTags:input_type -> backend.proto.customer.v2.ListContactTagsRequest
	47,  // 160: backend.proto.customer.v2.MetadataService.UpdateContactTag:input_type -> backend.proto.customer.v2.UpdateContactTagRequest
	49,  // 161: backend.proto.customer.v2.MetadataService.DeleteContactTag:input_type -> backend.proto.customer.v2.DeleteContactTagRequest
	51,  // 162: backend.proto.customer.v2.MetadataService.CreateLead:input_type -> backend.proto.customer.v2.CreateLeadRequest
	53,  // 163: backend.proto.customer.v2.MetadataService.GetLead:input_type -> backend.proto.customer.v2.GetLeadRequest
	55,  // 164: backend.proto.customer.v2.MetadataService.ListLeads:input_type -> backend.proto.customer.v2.ListLeadsRequest
	57,  // 165: backend.proto.customer.v2.MetadataService.UpdateLead:input_type -> backend.proto.customer.v2.UpdateLeadRequest
	59,  // 166: backend.proto.customer.v2.MetadataService.DeleteLead:input_type -> backend.proto.customer.v2.DeleteLeadRequest
	61,  // 167: backend.proto.customer.v2.MetadataService.CreateAddress:input_type -> backend.proto.customer.v2.CreateAddressRequest
	63,  // 168: backend.proto.customer.v2.MetadataService.GetAddress:input_type -> backend.proto.customer.v2.GetAddressRequest
	65,  // 169: backend.proto.customer.v2.MetadataService.ListAddresses:input_type -> backend.proto.customer.v2.ListAddressesRequest
	67,  // 170: backend.proto.customer.v2.MetadataService.UpdateAddress:input_type -> backend.proto.customer.v2.UpdateAddressRequest
	69,  // 171: backend.proto.customer.v2.MetadataService.DeleteAddress:input_type -> backend.proto.customer.v2.DeleteAddressRequest
	71,  // 172: backend.proto.customer.v2.MetadataService.CreateCustomField:input_type -> backend.proto.customer.v2.CreateCustomFieldRequest
	73,  // 173: backend.proto.customer.v2.MetadataService.GetCustomField:input_type -> backend.proto.customer.v2.GetCustomFieldRequest
	75,  // 174: backend.proto.customer.v2.MetadataService.ListCustomFields:input_type -> backend.proto.customer.v2.ListCustomFieldsRequest
	77,  // 175: backend.proto.customer.v2.MetadataService.UpdateCustomField:input_type -> backend.proto.customer.v2.UpdateCustomFieldRequest
	79,  // 176: backend.proto.customer.v2.MetadataService.BatchUpdateCustomFields:input_type -> backend.proto.customer.v2.BatchUpdateCustomFieldsRequest
	81,  // 177: backend.proto.customer.v2.MetadataService.DeleteCustomField:input_type -> backend.proto.customer.v2.DeleteCustomFieldRequest
	83,  // 178: backend.proto.customer.v2.MetadataService.CreateMetadata:input_type -> backend.proto.customer.v2.CreateMetadataRequest
	85,  // 179: backend.proto.customer.v2.MetadataService.GetMetadata:input_type -> backend.proto.customer.v2.GetMetadataRequest
	87,  // 180: backend.proto.customer.v2.MetadataService.ListMetadata:input_type -> backend.proto.customer.v2.ListMetadataRequest
	89,  // 181: backend.proto.customer.v2.MetadataService.UpdateMetadata:input_type -> backend.proto.customer.v2.UpdateMetadataRequest
	91,  // 182: backend.proto.customer.v2.MetadataService.DeleteMetadata:input_type -> backend.proto.customer.v2.DeleteMetadataRequest
	93,  // 183: backend.proto.customer.v2.MetadataService.CreateMetadataAggregate:input_type -> backend.proto.customer.v2.CreateMetadataAggregateRequest
	10,  // 184: backend.proto.customer.v2.MetadataService.CreateCustomer:output_type -> backend.proto.customer.v2.CreateCustomerResponse
	12,  // 185: backend.proto.customer.v2.MetadataService.GetCustomer:output_type -> backend.proto.customer.v2.GetCustomerResponse
	14,  // 186: backend.proto.customer.v2.MetadataService.ListCustomers:output_type -> backend.proto.customer.v2.ListCustomersResponse
	16,  // 187: backend.proto.customer.v2.MetadataService.UpdateCustomer:output_type -> backend.proto.customer.v2.UpdateCustomerResponse
	18,  // 188: backend.proto.customer.v2.MetadataService.DeleteCustomer:output_type -> backend.proto.customer.v2.DeleteCustomerResponse
	20,  // 189: backend.proto.customer.v2.MetadataService.CreateCustomerAggregate:output_type -> backend.proto.customer.v2.CreateCustomerAggregateResponse
	22,  // 190: backend.proto.customer.v2.MetadataService.CreateContact:output_type -> backend.proto.customer.v2.CreateContactResponse
	24,  // 191: backend.proto.customer.v2.MetadataService.GetContact:output_type -> backend.proto.customer.v2.GetContactResponse
	26,  // 192: backend.proto.customer.v2.MetadataService.ListContacts:output_type -> backend.proto.customer.v2.ListContactsResponse
	28,  // 193: backend.proto.customer.v2.MetadataService.UpdateContact:output_type -> backend.proto.customer.v2.UpdateContactResponse
	30,  // 194: backend.proto.customer.v2.MetadataService.DeleteContact:output_type -> backend.proto.customer.v2.DeleteContactResponse
	32,  // 195: backend.proto.customer.v2.MetadataService.CreateCustomerRelatedData:output_type -> backend.proto.customer.v2.CreateCustomerRelatedDataResponse
	34,  // 196: backend.proto.customer.v2.MetadataService.GetCustomerRelatedData:output_type -> backend.proto.customer.v2.GetCustomerRelatedDataResponse
	36,  // 197: backend.proto.customer.v2.MetadataService.ListCustomerRelatedData:output_type -> backend.proto.customer.v2.ListCustomerRelatedDataResponse
	38,  // 198: backend.proto.customer.v2.MetadataService.UpdateCustomerRelatedData:output_type -> backend.proto.customer.v2.UpdateCustomerRelatedDataResponse
	40,  // 199: backend.proto.customer.v2.MetadataService.DeleteCustomerRelatedData:output_type -> backend.proto.customer.v2.DeleteCustomerRelatedDataResponse
	42,  // 200: backend.proto.customer.v2.MetadataService.CreateContactTag:output_type -> backend.proto.customer.v2.CreateContactTagResponse
	44,  // 201: backend.proto.customer.v2.MetadataService.GetContactTag:output_type -> backend.proto.customer.v2.GetContactTagResponse
	46,  // 202: backend.proto.customer.v2.MetadataService.ListContactTags:output_type -> backend.proto.customer.v2.ListContactTagsResponse
	48,  // 203: backend.proto.customer.v2.MetadataService.UpdateContactTag:output_type -> backend.proto.customer.v2.UpdateContactTagResponse
	50,  // 204: backend.proto.customer.v2.MetadataService.DeleteContactTag:output_type -> backend.proto.customer.v2.DeleteContactTagResponse
	52,  // 205: backend.proto.customer.v2.MetadataService.CreateLead:output_type -> backend.proto.customer.v2.CreateLeadResponse
	54,  // 206: backend.proto.customer.v2.MetadataService.GetLead:output_type -> backend.proto.customer.v2.GetLeadResponse
	56,  // 207: backend.proto.customer.v2.MetadataService.ListLeads:output_type -> backend.proto.customer.v2.ListLeadsResponse
	58,  // 208: backend.proto.customer.v2.MetadataService.UpdateLead:output_type -> backend.proto.customer.v2.UpdateLeadResponse
	60,  // 209: backend.proto.customer.v2.MetadataService.DeleteLead:output_type -> backend.proto.customer.v2.DeleteLeadResponse
	62,  // 210: backend.proto.customer.v2.MetadataService.CreateAddress:output_type -> backend.proto.customer.v2.CreateAddressResponse
	64,  // 211: backend.proto.customer.v2.MetadataService.GetAddress:output_type -> backend.proto.customer.v2.GetAddressResponse
	66,  // 212: backend.proto.customer.v2.MetadataService.ListAddresses:output_type -> backend.proto.customer.v2.ListAddressesResponse
	68,  // 213: backend.proto.customer.v2.MetadataService.UpdateAddress:output_type -> backend.proto.customer.v2.UpdateAddressResponse
	70,  // 214: backend.proto.customer.v2.MetadataService.DeleteAddress:output_type -> backend.proto.customer.v2.DeleteAddressResponse
	72,  // 215: backend.proto.customer.v2.MetadataService.CreateCustomField:output_type -> backend.proto.customer.v2.CreateCustomFieldResponse
	74,  // 216: backend.proto.customer.v2.MetadataService.GetCustomField:output_type -> backend.proto.customer.v2.GetCustomFieldResponse
	76,  // 217: backend.proto.customer.v2.MetadataService.ListCustomFields:output_type -> backend.proto.customer.v2.ListCustomFieldsResponse
	78,  // 218: backend.proto.customer.v2.MetadataService.UpdateCustomField:output_type -> backend.proto.customer.v2.UpdateCustomFieldResponse
	80,  // 219: backend.proto.customer.v2.MetadataService.BatchUpdateCustomFields:output_type -> backend.proto.customer.v2.BatchUpdateCustomFieldsResponse
	82,  // 220: backend.proto.customer.v2.MetadataService.DeleteCustomField:output_type -> backend.proto.customer.v2.DeleteCustomFieldResponse
	84,  // 221: backend.proto.customer.v2.MetadataService.CreateMetadata:output_type -> backend.proto.customer.v2.CreateMetadataResponse
	86,  // 222: backend.proto.customer.v2.MetadataService.GetMetadata:output_type -> backend.proto.customer.v2.GetMetadataResponse
	88,  // 223: backend.proto.customer.v2.MetadataService.ListMetadata:output_type -> backend.proto.customer.v2.ListMetadataResponse
	90,  // 224: backend.proto.customer.v2.MetadataService.UpdateMetadata:output_type -> backend.proto.customer.v2.UpdateMetadataResponse
	92,  // 225: backend.proto.customer.v2.MetadataService.DeleteMetadata:output_type -> backend.proto.customer.v2.DeleteMetadataResponse
	94,  // 226: backend.proto.customer.v2.MetadataService.CreateMetadataAggregate:output_type -> backend.proto.customer.v2.CreateMetadataAggregateResponse
	184, // [184:227] is the sub-list for method output_type
	141, // [141:184] is the sub-list for method input_type
	141, // [141:141] is the sub-list for extension type_name
	141, // [141:141] is the sub-list for extension extendee
	0,   // [0:141] is the sub-list for field type_name
}

func init() { file_backend_proto_customer_v2_metadata_service_proto_init() }
func file_backend_proto_customer_v2_metadata_service_proto_init() {
	if File_backend_proto_customer_v2_metadata_service_proto != nil {
		return
	}
	file_backend_proto_customer_v2_metadata_proto_init()
	file_backend_proto_customer_v2_common_proto_init()
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[0].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[5].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[6].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[10].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[12].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[17].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[18].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[22].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[27].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[28].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[32].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[37].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[38].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[42].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[47].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[48].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[57].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[58].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[62].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[67].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[68].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[74].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[79].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[80].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[84].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[92].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[94].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[98].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_service_proto_msgTypes[100].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v2_metadata_service_proto_rawDesc), len(file_backend_proto_customer_v2_metadata_service_proto_rawDesc)),
			NumEnums:      9,
			NumMessages:   102,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_customer_v2_metadata_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_customer_v2_metadata_service_proto_depIdxs,
		EnumInfos:         file_backend_proto_customer_v2_metadata_service_proto_enumTypes,
		MessageInfos:      file_backend_proto_customer_v2_metadata_service_proto_msgTypes,
	}.Build()
	File_backend_proto_customer_v2_metadata_service_proto = out.File
	file_backend_proto_customer_v2_metadata_service_proto_goTypes = nil
	file_backend_proto_customer_v2_metadata_service_proto_depIdxs = nil
}
