// (-- api-linter: core::0131::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0133::request-required-fields=disabled
//     aip.dev/not-precedent: 使用自定义请求消息 --)
// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: 使用自定义请求消息 --)
// (-- api-linter: core::0133::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0134::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0135::response-message-name=disabled
//     aip.dev/not-precedent: 使用空resp易于拓展 --);
// (-- api-linter: core::0216::state-field-output-only=disabled
//     aip.dev/not-precedent: 使用自定义状态字段 --)
// (-- api-linter: core::0216::synonyms=disabled
//     aip.dev/not-precedent: 对齐 moego 内部使用习惯 --)

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/customer/v2/metadata_service.proto

package customerpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	MetadataService_CreateCustomer_FullMethodName            = "/backend.proto.customer.v2.MetadataService/CreateCustomer"
	MetadataService_GetCustomer_FullMethodName               = "/backend.proto.customer.v2.MetadataService/GetCustomer"
	MetadataService_ListCustomers_FullMethodName             = "/backend.proto.customer.v2.MetadataService/ListCustomers"
	MetadataService_UpdateCustomer_FullMethodName            = "/backend.proto.customer.v2.MetadataService/UpdateCustomer"
	MetadataService_DeleteCustomer_FullMethodName            = "/backend.proto.customer.v2.MetadataService/DeleteCustomer"
	MetadataService_CreateCustomerAggregate_FullMethodName   = "/backend.proto.customer.v2.MetadataService/CreateCustomerAggregate"
	MetadataService_CreateContact_FullMethodName             = "/backend.proto.customer.v2.MetadataService/CreateContact"
	MetadataService_GetContact_FullMethodName                = "/backend.proto.customer.v2.MetadataService/GetContact"
	MetadataService_ListContacts_FullMethodName              = "/backend.proto.customer.v2.MetadataService/ListContacts"
	MetadataService_UpdateContact_FullMethodName             = "/backend.proto.customer.v2.MetadataService/UpdateContact"
	MetadataService_DeleteContact_FullMethodName             = "/backend.proto.customer.v2.MetadataService/DeleteContact"
	MetadataService_CreateCustomerRelatedData_FullMethodName = "/backend.proto.customer.v2.MetadataService/CreateCustomerRelatedData"
	MetadataService_GetCustomerRelatedData_FullMethodName    = "/backend.proto.customer.v2.MetadataService/GetCustomerRelatedData"
	MetadataService_ListCustomerRelatedData_FullMethodName   = "/backend.proto.customer.v2.MetadataService/ListCustomerRelatedData"
	MetadataService_UpdateCustomerRelatedData_FullMethodName = "/backend.proto.customer.v2.MetadataService/UpdateCustomerRelatedData"
	MetadataService_DeleteCustomerRelatedData_FullMethodName = "/backend.proto.customer.v2.MetadataService/DeleteCustomerRelatedData"
	MetadataService_CreateContactTag_FullMethodName          = "/backend.proto.customer.v2.MetadataService/CreateContactTag"
	MetadataService_GetContactTag_FullMethodName             = "/backend.proto.customer.v2.MetadataService/GetContactTag"
	MetadataService_ListContactTags_FullMethodName           = "/backend.proto.customer.v2.MetadataService/ListContactTags"
	MetadataService_UpdateContactTag_FullMethodName          = "/backend.proto.customer.v2.MetadataService/UpdateContactTag"
	MetadataService_DeleteContactTag_FullMethodName          = "/backend.proto.customer.v2.MetadataService/DeleteContactTag"
	MetadataService_CreateLead_FullMethodName                = "/backend.proto.customer.v2.MetadataService/CreateLead"
	MetadataService_GetLead_FullMethodName                   = "/backend.proto.customer.v2.MetadataService/GetLead"
	MetadataService_ListLeads_FullMethodName                 = "/backend.proto.customer.v2.MetadataService/ListLeads"
	MetadataService_UpdateLead_FullMethodName                = "/backend.proto.customer.v2.MetadataService/UpdateLead"
	MetadataService_DeleteLead_FullMethodName                = "/backend.proto.customer.v2.MetadataService/DeleteLead"
	MetadataService_CreateAddress_FullMethodName             = "/backend.proto.customer.v2.MetadataService/CreateAddress"
	MetadataService_GetAddress_FullMethodName                = "/backend.proto.customer.v2.MetadataService/GetAddress"
	MetadataService_ListAddresses_FullMethodName             = "/backend.proto.customer.v2.MetadataService/ListAddresses"
	MetadataService_UpdateAddress_FullMethodName             = "/backend.proto.customer.v2.MetadataService/UpdateAddress"
	MetadataService_DeleteAddress_FullMethodName             = "/backend.proto.customer.v2.MetadataService/DeleteAddress"
	MetadataService_CreateCustomField_FullMethodName         = "/backend.proto.customer.v2.MetadataService/CreateCustomField"
	MetadataService_GetCustomField_FullMethodName            = "/backend.proto.customer.v2.MetadataService/GetCustomField"
	MetadataService_ListCustomFields_FullMethodName          = "/backend.proto.customer.v2.MetadataService/ListCustomFields"
	MetadataService_UpdateCustomField_FullMethodName         = "/backend.proto.customer.v2.MetadataService/UpdateCustomField"
	MetadataService_BatchUpdateCustomFields_FullMethodName   = "/backend.proto.customer.v2.MetadataService/BatchUpdateCustomFields"
	MetadataService_DeleteCustomField_FullMethodName         = "/backend.proto.customer.v2.MetadataService/DeleteCustomField"
	MetadataService_CreateMetadata_FullMethodName            = "/backend.proto.customer.v2.MetadataService/CreateMetadata"
	MetadataService_GetMetadata_FullMethodName               = "/backend.proto.customer.v2.MetadataService/GetMetadata"
	MetadataService_ListMetadata_FullMethodName              = "/backend.proto.customer.v2.MetadataService/ListMetadata"
	MetadataService_UpdateMetadata_FullMethodName            = "/backend.proto.customer.v2.MetadataService/UpdateMetadata"
	MetadataService_DeleteMetadata_FullMethodName            = "/backend.proto.customer.v2.MetadataService/DeleteMetadata"
	MetadataService_CreateMetadataAggregate_FullMethodName   = "/backend.proto.customer.v2.MetadataService/CreateMetadataAggregate"
)

// MetadataServiceClient is the client API for MetadataService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// MetadataService 提供客户元数据管理功能
type MetadataServiceClient interface {
	// ==================== Customer Management ====================
	// CreateCustomer
	CreateCustomer(ctx context.Context, in *CreateCustomerRequest, opts ...grpc.CallOption) (*CreateCustomerResponse, error)
	// GetCustomer
	// get customer by unique identification
	// if customer not found, return error
	GetCustomer(ctx context.Context, in *GetCustomerRequest, opts ...grpc.CallOption) (*GetCustomerResponse, error)
	// ListCustomers
	// list customers by filter
	// if customer not found, return empty list, not return error
	ListCustomers(ctx context.Context, in *ListCustomersRequest, opts ...grpc.CallOption) (*ListCustomersResponse, error)
	// UpdateCustomer 更新客户
	UpdateCustomer(ctx context.Context, in *UpdateCustomerRequest, opts ...grpc.CallOption) (*UpdateCustomerResponse, error)
	// DeleteCustomer 删除客户
	DeleteCustomer(ctx context.Context, in *DeleteCustomerRequest, opts ...grpc.CallOption) (*DeleteCustomerResponse, error)
	// 聚合创建customer及相关实体
	// 原子性创建customer以及可选的address、contact、related data
	CreateCustomerAggregate(ctx context.Context, in *CreateCustomerAggregateRequest, opts ...grpc.CallOption) (*CreateCustomerAggregateResponse, error)
	// ==================== Contact Management ====================
	// CreateContact 创建联系人
	CreateContact(ctx context.Context, in *CreateContactRequest, opts ...grpc.CallOption) (*CreateContactResponse, error)
	// GetContact 获取联系人
	GetContact(ctx context.Context, in *GetContactRequest, opts ...grpc.CallOption) (*GetContactResponse, error)
	// ListContacts 列出联系人
	ListContacts(ctx context.Context, in *ListContactsRequest, opts ...grpc.CallOption) (*ListContactsResponse, error)
	// UpdateContact 更新联系人
	UpdateContact(ctx context.Context, in *UpdateContactRequest, opts ...grpc.CallOption) (*UpdateContactResponse, error)
	// DeleteContact 删除联系人
	DeleteContact(ctx context.Context, in *DeleteContactRequest, opts ...grpc.CallOption) (*DeleteContactResponse, error)
	// ==================== CustomerRelatedData Management ====================
	// CreateCustomerRelatedData 创建客户相关数据
	CreateCustomerRelatedData(ctx context.Context, in *CreateCustomerRelatedDataRequest, opts ...grpc.CallOption) (*CreateCustomerRelatedDataResponse, error)
	// GetCustomerRelatedData 获取客户相关数据
	GetCustomerRelatedData(ctx context.Context, in *GetCustomerRelatedDataRequest, opts ...grpc.CallOption) (*GetCustomerRelatedDataResponse, error)
	// ListCustomerRelatedData 列出客户相关数据
	ListCustomerRelatedData(ctx context.Context, in *ListCustomerRelatedDataRequest, opts ...grpc.CallOption) (*ListCustomerRelatedDataResponse, error)
	// UpdateCustomerRelatedData 更新客户相关数据
	UpdateCustomerRelatedData(ctx context.Context, in *UpdateCustomerRelatedDataRequest, opts ...grpc.CallOption) (*UpdateCustomerRelatedDataResponse, error)
	// DeleteCustomerRelatedData 删除客户相关数据
	DeleteCustomerRelatedData(ctx context.Context, in *DeleteCustomerRelatedDataRequest, opts ...grpc.CallOption) (*DeleteCustomerRelatedDataResponse, error)
	// ==================== Contact Tag Management ====================
	// CreateContactTag 创建标签
	CreateContactTag(ctx context.Context, in *CreateContactTagRequest, opts ...grpc.CallOption) (*CreateContactTagResponse, error)
	// GetContactTag 获取标签
	GetContactTag(ctx context.Context, in *GetContactTagRequest, opts ...grpc.CallOption) (*GetContactTagResponse, error)
	// ListContactTags 列出标签
	ListContactTags(ctx context.Context, in *ListContactTagsRequest, opts ...grpc.CallOption) (*ListContactTagsResponse, error)
	// UpdateContactTag 更新标签
	UpdateContactTag(ctx context.Context, in *UpdateContactTagRequest, opts ...grpc.CallOption) (*UpdateContactTagResponse, error)
	// DeleteContactTag 删除标签
	DeleteContactTag(ctx context.Context, in *DeleteContactTagRequest, opts ...grpc.CallOption) (*DeleteContactTagResponse, error)
	// ==================== Lead Management ====================
	// CreateLead 创建线索
	CreateLead(ctx context.Context, in *CreateLeadRequest, opts ...grpc.CallOption) (*CreateLeadResponse, error)
	// GetLead 获取线索
	GetLead(ctx context.Context, in *GetLeadRequest, opts ...grpc.CallOption) (*GetLeadResponse, error)
	// ListLeads 列出线索
	ListLeads(ctx context.Context, in *ListLeadsRequest, opts ...grpc.CallOption) (*ListLeadsResponse, error)
	// UpdateLead 更新线索
	UpdateLead(ctx context.Context, in *UpdateLeadRequest, opts ...grpc.CallOption) (*UpdateLeadResponse, error)
	// DeleteLead 删除线索
	DeleteLead(ctx context.Context, in *DeleteLeadRequest, opts ...grpc.CallOption) (*DeleteLeadResponse, error)
	// ==================== Address Management ====================
	// CreateAddress 创建地址
	CreateAddress(ctx context.Context, in *CreateAddressRequest, opts ...grpc.CallOption) (*CreateAddressResponse, error)
	// GetAddress 获取地址
	GetAddress(ctx context.Context, in *GetAddressRequest, opts ...grpc.CallOption) (*GetAddressResponse, error)
	// ListAddresses 列出地址
	ListAddresses(ctx context.Context, in *ListAddressesRequest, opts ...grpc.CallOption) (*ListAddressesResponse, error)
	// UpdateAddress 更新地址
	UpdateAddress(ctx context.Context, in *UpdateAddressRequest, opts ...grpc.CallOption) (*UpdateAddressResponse, error)
	// DeleteAddress 删除地址
	DeleteAddress(ctx context.Context, in *DeleteAddressRequest, opts ...grpc.CallOption) (*DeleteAddressResponse, error)
	// ==================== Custom Field Management ====================
	// CreateCustomField 创建自定义字段
	CreateCustomField(ctx context.Context, in *CreateCustomFieldRequest, opts ...grpc.CallOption) (*CreateCustomFieldResponse, error)
	// GetCustomField 获取自定义字段
	GetCustomField(ctx context.Context, in *GetCustomFieldRequest, opts ...grpc.CallOption) (*GetCustomFieldResponse, error)
	// ListCustomFields 列出自定义字段
	ListCustomFields(ctx context.Context, in *ListCustomFieldsRequest, opts ...grpc.CallOption) (*ListCustomFieldsResponse, error)
	// UpdateCustomField 更新自定义字段
	UpdateCustomField(ctx context.Context, in *UpdateCustomFieldRequest, opts ...grpc.CallOption) (*UpdateCustomFieldResponse, error)
	// BatchUpdateCustomFields 批量更新自定义字段
	BatchUpdateCustomFields(ctx context.Context, in *BatchUpdateCustomFieldsRequest, opts ...grpc.CallOption) (*BatchUpdateCustomFieldsResponse, error)
	// DeleteCustomField 删除自定义字段
	DeleteCustomField(ctx context.Context, in *DeleteCustomFieldRequest, opts ...grpc.CallOption) (*DeleteCustomFieldResponse, error)
	// ==================== Unified Metadata Management ====================
	// CreateMetadata 创建统一元数据 (customer 或 lead)
	CreateMetadata(ctx context.Context, in *CreateMetadataRequest, opts ...grpc.CallOption) (*CreateMetadataResponse, error)
	// GetMetadata 获取统一元数据
	GetMetadata(ctx context.Context, in *GetMetadataRequest, opts ...grpc.CallOption) (*GetMetadataResponse, error)
	// ListMetadata 列出统一元数据
	ListMetadata(ctx context.Context, in *ListMetadataRequest, opts ...grpc.CallOption) (*ListMetadataResponse, error)
	// UpdateMetadata 更新统一元数据
	UpdateMetadata(ctx context.Context, in *UpdateMetadataRequest, opts ...grpc.CallOption) (*UpdateMetadataResponse, error)
	// DeleteMetadata 删除统一元数据
	DeleteMetadata(ctx context.Context, in *DeleteMetadataRequest, opts ...grpc.CallOption) (*DeleteMetadataResponse, error)
	// 聚合创建metadata及相关实体
	// 原子性创建metadata以及可选的address、contact、related data
	CreateMetadataAggregate(ctx context.Context, in *CreateMetadataAggregateRequest, opts ...grpc.CallOption) (*CreateMetadataAggregateResponse, error)
}

type metadataServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMetadataServiceClient(cc grpc.ClientConnInterface) MetadataServiceClient {
	return &metadataServiceClient{cc}
}

func (c *metadataServiceClient) CreateCustomer(ctx context.Context, in *CreateCustomerRequest, opts ...grpc.CallOption) (*CreateCustomerResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateCustomerResponse)
	err := c.cc.Invoke(ctx, MetadataService_CreateCustomer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) GetCustomer(ctx context.Context, in *GetCustomerRequest, opts ...grpc.CallOption) (*GetCustomerResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCustomerResponse)
	err := c.cc.Invoke(ctx, MetadataService_GetCustomer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) ListCustomers(ctx context.Context, in *ListCustomersRequest, opts ...grpc.CallOption) (*ListCustomersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCustomersResponse)
	err := c.cc.Invoke(ctx, MetadataService_ListCustomers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) UpdateCustomer(ctx context.Context, in *UpdateCustomerRequest, opts ...grpc.CallOption) (*UpdateCustomerResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateCustomerResponse)
	err := c.cc.Invoke(ctx, MetadataService_UpdateCustomer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) DeleteCustomer(ctx context.Context, in *DeleteCustomerRequest, opts ...grpc.CallOption) (*DeleteCustomerResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteCustomerResponse)
	err := c.cc.Invoke(ctx, MetadataService_DeleteCustomer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) CreateCustomerAggregate(ctx context.Context, in *CreateCustomerAggregateRequest, opts ...grpc.CallOption) (*CreateCustomerAggregateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateCustomerAggregateResponse)
	err := c.cc.Invoke(ctx, MetadataService_CreateCustomerAggregate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) CreateContact(ctx context.Context, in *CreateContactRequest, opts ...grpc.CallOption) (*CreateContactResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateContactResponse)
	err := c.cc.Invoke(ctx, MetadataService_CreateContact_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) GetContact(ctx context.Context, in *GetContactRequest, opts ...grpc.CallOption) (*GetContactResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetContactResponse)
	err := c.cc.Invoke(ctx, MetadataService_GetContact_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) ListContacts(ctx context.Context, in *ListContactsRequest, opts ...grpc.CallOption) (*ListContactsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListContactsResponse)
	err := c.cc.Invoke(ctx, MetadataService_ListContacts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) UpdateContact(ctx context.Context, in *UpdateContactRequest, opts ...grpc.CallOption) (*UpdateContactResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateContactResponse)
	err := c.cc.Invoke(ctx, MetadataService_UpdateContact_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) DeleteContact(ctx context.Context, in *DeleteContactRequest, opts ...grpc.CallOption) (*DeleteContactResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteContactResponse)
	err := c.cc.Invoke(ctx, MetadataService_DeleteContact_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) CreateCustomerRelatedData(ctx context.Context, in *CreateCustomerRelatedDataRequest, opts ...grpc.CallOption) (*CreateCustomerRelatedDataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateCustomerRelatedDataResponse)
	err := c.cc.Invoke(ctx, MetadataService_CreateCustomerRelatedData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) GetCustomerRelatedData(ctx context.Context, in *GetCustomerRelatedDataRequest, opts ...grpc.CallOption) (*GetCustomerRelatedDataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCustomerRelatedDataResponse)
	err := c.cc.Invoke(ctx, MetadataService_GetCustomerRelatedData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) ListCustomerRelatedData(ctx context.Context, in *ListCustomerRelatedDataRequest, opts ...grpc.CallOption) (*ListCustomerRelatedDataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCustomerRelatedDataResponse)
	err := c.cc.Invoke(ctx, MetadataService_ListCustomerRelatedData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) UpdateCustomerRelatedData(ctx context.Context, in *UpdateCustomerRelatedDataRequest, opts ...grpc.CallOption) (*UpdateCustomerRelatedDataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateCustomerRelatedDataResponse)
	err := c.cc.Invoke(ctx, MetadataService_UpdateCustomerRelatedData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) DeleteCustomerRelatedData(ctx context.Context, in *DeleteCustomerRelatedDataRequest, opts ...grpc.CallOption) (*DeleteCustomerRelatedDataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteCustomerRelatedDataResponse)
	err := c.cc.Invoke(ctx, MetadataService_DeleteCustomerRelatedData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) CreateContactTag(ctx context.Context, in *CreateContactTagRequest, opts ...grpc.CallOption) (*CreateContactTagResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateContactTagResponse)
	err := c.cc.Invoke(ctx, MetadataService_CreateContactTag_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) GetContactTag(ctx context.Context, in *GetContactTagRequest, opts ...grpc.CallOption) (*GetContactTagResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetContactTagResponse)
	err := c.cc.Invoke(ctx, MetadataService_GetContactTag_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) ListContactTags(ctx context.Context, in *ListContactTagsRequest, opts ...grpc.CallOption) (*ListContactTagsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListContactTagsResponse)
	err := c.cc.Invoke(ctx, MetadataService_ListContactTags_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) UpdateContactTag(ctx context.Context, in *UpdateContactTagRequest, opts ...grpc.CallOption) (*UpdateContactTagResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateContactTagResponse)
	err := c.cc.Invoke(ctx, MetadataService_UpdateContactTag_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) DeleteContactTag(ctx context.Context, in *DeleteContactTagRequest, opts ...grpc.CallOption) (*DeleteContactTagResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteContactTagResponse)
	err := c.cc.Invoke(ctx, MetadataService_DeleteContactTag_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) CreateLead(ctx context.Context, in *CreateLeadRequest, opts ...grpc.CallOption) (*CreateLeadResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateLeadResponse)
	err := c.cc.Invoke(ctx, MetadataService_CreateLead_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) GetLead(ctx context.Context, in *GetLeadRequest, opts ...grpc.CallOption) (*GetLeadResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetLeadResponse)
	err := c.cc.Invoke(ctx, MetadataService_GetLead_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) ListLeads(ctx context.Context, in *ListLeadsRequest, opts ...grpc.CallOption) (*ListLeadsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListLeadsResponse)
	err := c.cc.Invoke(ctx, MetadataService_ListLeads_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) UpdateLead(ctx context.Context, in *UpdateLeadRequest, opts ...grpc.CallOption) (*UpdateLeadResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateLeadResponse)
	err := c.cc.Invoke(ctx, MetadataService_UpdateLead_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) DeleteLead(ctx context.Context, in *DeleteLeadRequest, opts ...grpc.CallOption) (*DeleteLeadResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteLeadResponse)
	err := c.cc.Invoke(ctx, MetadataService_DeleteLead_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) CreateAddress(ctx context.Context, in *CreateAddressRequest, opts ...grpc.CallOption) (*CreateAddressResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateAddressResponse)
	err := c.cc.Invoke(ctx, MetadataService_CreateAddress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) GetAddress(ctx context.Context, in *GetAddressRequest, opts ...grpc.CallOption) (*GetAddressResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAddressResponse)
	err := c.cc.Invoke(ctx, MetadataService_GetAddress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) ListAddresses(ctx context.Context, in *ListAddressesRequest, opts ...grpc.CallOption) (*ListAddressesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAddressesResponse)
	err := c.cc.Invoke(ctx, MetadataService_ListAddresses_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) UpdateAddress(ctx context.Context, in *UpdateAddressRequest, opts ...grpc.CallOption) (*UpdateAddressResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateAddressResponse)
	err := c.cc.Invoke(ctx, MetadataService_UpdateAddress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) DeleteAddress(ctx context.Context, in *DeleteAddressRequest, opts ...grpc.CallOption) (*DeleteAddressResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteAddressResponse)
	err := c.cc.Invoke(ctx, MetadataService_DeleteAddress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) CreateCustomField(ctx context.Context, in *CreateCustomFieldRequest, opts ...grpc.CallOption) (*CreateCustomFieldResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateCustomFieldResponse)
	err := c.cc.Invoke(ctx, MetadataService_CreateCustomField_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) GetCustomField(ctx context.Context, in *GetCustomFieldRequest, opts ...grpc.CallOption) (*GetCustomFieldResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCustomFieldResponse)
	err := c.cc.Invoke(ctx, MetadataService_GetCustomField_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) ListCustomFields(ctx context.Context, in *ListCustomFieldsRequest, opts ...grpc.CallOption) (*ListCustomFieldsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCustomFieldsResponse)
	err := c.cc.Invoke(ctx, MetadataService_ListCustomFields_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) UpdateCustomField(ctx context.Context, in *UpdateCustomFieldRequest, opts ...grpc.CallOption) (*UpdateCustomFieldResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateCustomFieldResponse)
	err := c.cc.Invoke(ctx, MetadataService_UpdateCustomField_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) BatchUpdateCustomFields(ctx context.Context, in *BatchUpdateCustomFieldsRequest, opts ...grpc.CallOption) (*BatchUpdateCustomFieldsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchUpdateCustomFieldsResponse)
	err := c.cc.Invoke(ctx, MetadataService_BatchUpdateCustomFields_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) DeleteCustomField(ctx context.Context, in *DeleteCustomFieldRequest, opts ...grpc.CallOption) (*DeleteCustomFieldResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteCustomFieldResponse)
	err := c.cc.Invoke(ctx, MetadataService_DeleteCustomField_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) CreateMetadata(ctx context.Context, in *CreateMetadataRequest, opts ...grpc.CallOption) (*CreateMetadataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateMetadataResponse)
	err := c.cc.Invoke(ctx, MetadataService_CreateMetadata_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) GetMetadata(ctx context.Context, in *GetMetadataRequest, opts ...grpc.CallOption) (*GetMetadataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetMetadataResponse)
	err := c.cc.Invoke(ctx, MetadataService_GetMetadata_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) ListMetadata(ctx context.Context, in *ListMetadataRequest, opts ...grpc.CallOption) (*ListMetadataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListMetadataResponse)
	err := c.cc.Invoke(ctx, MetadataService_ListMetadata_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) UpdateMetadata(ctx context.Context, in *UpdateMetadataRequest, opts ...grpc.CallOption) (*UpdateMetadataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateMetadataResponse)
	err := c.cc.Invoke(ctx, MetadataService_UpdateMetadata_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) DeleteMetadata(ctx context.Context, in *DeleteMetadataRequest, opts ...grpc.CallOption) (*DeleteMetadataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteMetadataResponse)
	err := c.cc.Invoke(ctx, MetadataService_DeleteMetadata_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataServiceClient) CreateMetadataAggregate(ctx context.Context, in *CreateMetadataAggregateRequest, opts ...grpc.CallOption) (*CreateMetadataAggregateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateMetadataAggregateResponse)
	err := c.cc.Invoke(ctx, MetadataService_CreateMetadataAggregate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MetadataServiceServer is the server API for MetadataService service.
// All implementations must embed UnimplementedMetadataServiceServer
// for forward compatibility.
//
// MetadataService 提供客户元数据管理功能
type MetadataServiceServer interface {
	// ==================== Customer Management ====================
	// CreateCustomer
	CreateCustomer(context.Context, *CreateCustomerRequest) (*CreateCustomerResponse, error)
	// GetCustomer
	// get customer by unique identification
	// if customer not found, return error
	GetCustomer(context.Context, *GetCustomerRequest) (*GetCustomerResponse, error)
	// ListCustomers
	// list customers by filter
	// if customer not found, return empty list, not return error
	ListCustomers(context.Context, *ListCustomersRequest) (*ListCustomersResponse, error)
	// UpdateCustomer 更新客户
	UpdateCustomer(context.Context, *UpdateCustomerRequest) (*UpdateCustomerResponse, error)
	// DeleteCustomer 删除客户
	DeleteCustomer(context.Context, *DeleteCustomerRequest) (*DeleteCustomerResponse, error)
	// 聚合创建customer及相关实体
	// 原子性创建customer以及可选的address、contact、related data
	CreateCustomerAggregate(context.Context, *CreateCustomerAggregateRequest) (*CreateCustomerAggregateResponse, error)
	// ==================== Contact Management ====================
	// CreateContact 创建联系人
	CreateContact(context.Context, *CreateContactRequest) (*CreateContactResponse, error)
	// GetContact 获取联系人
	GetContact(context.Context, *GetContactRequest) (*GetContactResponse, error)
	// ListContacts 列出联系人
	ListContacts(context.Context, *ListContactsRequest) (*ListContactsResponse, error)
	// UpdateContact 更新联系人
	UpdateContact(context.Context, *UpdateContactRequest) (*UpdateContactResponse, error)
	// DeleteContact 删除联系人
	DeleteContact(context.Context, *DeleteContactRequest) (*DeleteContactResponse, error)
	// ==================== CustomerRelatedData Management ====================
	// CreateCustomerRelatedData 创建客户相关数据
	CreateCustomerRelatedData(context.Context, *CreateCustomerRelatedDataRequest) (*CreateCustomerRelatedDataResponse, error)
	// GetCustomerRelatedData 获取客户相关数据
	GetCustomerRelatedData(context.Context, *GetCustomerRelatedDataRequest) (*GetCustomerRelatedDataResponse, error)
	// ListCustomerRelatedData 列出客户相关数据
	ListCustomerRelatedData(context.Context, *ListCustomerRelatedDataRequest) (*ListCustomerRelatedDataResponse, error)
	// UpdateCustomerRelatedData 更新客户相关数据
	UpdateCustomerRelatedData(context.Context, *UpdateCustomerRelatedDataRequest) (*UpdateCustomerRelatedDataResponse, error)
	// DeleteCustomerRelatedData 删除客户相关数据
	DeleteCustomerRelatedData(context.Context, *DeleteCustomerRelatedDataRequest) (*DeleteCustomerRelatedDataResponse, error)
	// ==================== Contact Tag Management ====================
	// CreateContactTag 创建标签
	CreateContactTag(context.Context, *CreateContactTagRequest) (*CreateContactTagResponse, error)
	// GetContactTag 获取标签
	GetContactTag(context.Context, *GetContactTagRequest) (*GetContactTagResponse, error)
	// ListContactTags 列出标签
	ListContactTags(context.Context, *ListContactTagsRequest) (*ListContactTagsResponse, error)
	// UpdateContactTag 更新标签
	UpdateContactTag(context.Context, *UpdateContactTagRequest) (*UpdateContactTagResponse, error)
	// DeleteContactTag 删除标签
	DeleteContactTag(context.Context, *DeleteContactTagRequest) (*DeleteContactTagResponse, error)
	// ==================== Lead Management ====================
	// CreateLead 创建线索
	CreateLead(context.Context, *CreateLeadRequest) (*CreateLeadResponse, error)
	// GetLead 获取线索
	GetLead(context.Context, *GetLeadRequest) (*GetLeadResponse, error)
	// ListLeads 列出线索
	ListLeads(context.Context, *ListLeadsRequest) (*ListLeadsResponse, error)
	// UpdateLead 更新线索
	UpdateLead(context.Context, *UpdateLeadRequest) (*UpdateLeadResponse, error)
	// DeleteLead 删除线索
	DeleteLead(context.Context, *DeleteLeadRequest) (*DeleteLeadResponse, error)
	// ==================== Address Management ====================
	// CreateAddress 创建地址
	CreateAddress(context.Context, *CreateAddressRequest) (*CreateAddressResponse, error)
	// GetAddress 获取地址
	GetAddress(context.Context, *GetAddressRequest) (*GetAddressResponse, error)
	// ListAddresses 列出地址
	ListAddresses(context.Context, *ListAddressesRequest) (*ListAddressesResponse, error)
	// UpdateAddress 更新地址
	UpdateAddress(context.Context, *UpdateAddressRequest) (*UpdateAddressResponse, error)
	// DeleteAddress 删除地址
	DeleteAddress(context.Context, *DeleteAddressRequest) (*DeleteAddressResponse, error)
	// ==================== Custom Field Management ====================
	// CreateCustomField 创建自定义字段
	CreateCustomField(context.Context, *CreateCustomFieldRequest) (*CreateCustomFieldResponse, error)
	// GetCustomField 获取自定义字段
	GetCustomField(context.Context, *GetCustomFieldRequest) (*GetCustomFieldResponse, error)
	// ListCustomFields 列出自定义字段
	ListCustomFields(context.Context, *ListCustomFieldsRequest) (*ListCustomFieldsResponse, error)
	// UpdateCustomField 更新自定义字段
	UpdateCustomField(context.Context, *UpdateCustomFieldRequest) (*UpdateCustomFieldResponse, error)
	// BatchUpdateCustomFields 批量更新自定义字段
	BatchUpdateCustomFields(context.Context, *BatchUpdateCustomFieldsRequest) (*BatchUpdateCustomFieldsResponse, error)
	// DeleteCustomField 删除自定义字段
	DeleteCustomField(context.Context, *DeleteCustomFieldRequest) (*DeleteCustomFieldResponse, error)
	// ==================== Unified Metadata Management ====================
	// CreateMetadata 创建统一元数据 (customer 或 lead)
	CreateMetadata(context.Context, *CreateMetadataRequest) (*CreateMetadataResponse, error)
	// GetMetadata 获取统一元数据
	GetMetadata(context.Context, *GetMetadataRequest) (*GetMetadataResponse, error)
	// ListMetadata 列出统一元数据
	ListMetadata(context.Context, *ListMetadataRequest) (*ListMetadataResponse, error)
	// UpdateMetadata 更新统一元数据
	UpdateMetadata(context.Context, *UpdateMetadataRequest) (*UpdateMetadataResponse, error)
	// DeleteMetadata 删除统一元数据
	DeleteMetadata(context.Context, *DeleteMetadataRequest) (*DeleteMetadataResponse, error)
	// 聚合创建metadata及相关实体
	// 原子性创建metadata以及可选的address、contact、related data
	CreateMetadataAggregate(context.Context, *CreateMetadataAggregateRequest) (*CreateMetadataAggregateResponse, error)
	mustEmbedUnimplementedMetadataServiceServer()
}

// UnimplementedMetadataServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedMetadataServiceServer struct{}

func (UnimplementedMetadataServiceServer) CreateCustomer(context.Context, *CreateCustomerRequest) (*CreateCustomerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCustomer not implemented")
}
func (UnimplementedMetadataServiceServer) GetCustomer(context.Context, *GetCustomerRequest) (*GetCustomerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomer not implemented")
}
func (UnimplementedMetadataServiceServer) ListCustomers(context.Context, *ListCustomersRequest) (*ListCustomersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCustomers not implemented")
}
func (UnimplementedMetadataServiceServer) UpdateCustomer(context.Context, *UpdateCustomerRequest) (*UpdateCustomerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCustomer not implemented")
}
func (UnimplementedMetadataServiceServer) DeleteCustomer(context.Context, *DeleteCustomerRequest) (*DeleteCustomerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCustomer not implemented")
}
func (UnimplementedMetadataServiceServer) CreateCustomerAggregate(context.Context, *CreateCustomerAggregateRequest) (*CreateCustomerAggregateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCustomerAggregate not implemented")
}
func (UnimplementedMetadataServiceServer) CreateContact(context.Context, *CreateContactRequest) (*CreateContactResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateContact not implemented")
}
func (UnimplementedMetadataServiceServer) GetContact(context.Context, *GetContactRequest) (*GetContactResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetContact not implemented")
}
func (UnimplementedMetadataServiceServer) ListContacts(context.Context, *ListContactsRequest) (*ListContactsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListContacts not implemented")
}
func (UnimplementedMetadataServiceServer) UpdateContact(context.Context, *UpdateContactRequest) (*UpdateContactResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateContact not implemented")
}
func (UnimplementedMetadataServiceServer) DeleteContact(context.Context, *DeleteContactRequest) (*DeleteContactResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteContact not implemented")
}
func (UnimplementedMetadataServiceServer) CreateCustomerRelatedData(context.Context, *CreateCustomerRelatedDataRequest) (*CreateCustomerRelatedDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCustomerRelatedData not implemented")
}
func (UnimplementedMetadataServiceServer) GetCustomerRelatedData(context.Context, *GetCustomerRelatedDataRequest) (*GetCustomerRelatedDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomerRelatedData not implemented")
}
func (UnimplementedMetadataServiceServer) ListCustomerRelatedData(context.Context, *ListCustomerRelatedDataRequest) (*ListCustomerRelatedDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCustomerRelatedData not implemented")
}
func (UnimplementedMetadataServiceServer) UpdateCustomerRelatedData(context.Context, *UpdateCustomerRelatedDataRequest) (*UpdateCustomerRelatedDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCustomerRelatedData not implemented")
}
func (UnimplementedMetadataServiceServer) DeleteCustomerRelatedData(context.Context, *DeleteCustomerRelatedDataRequest) (*DeleteCustomerRelatedDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCustomerRelatedData not implemented")
}
func (UnimplementedMetadataServiceServer) CreateContactTag(context.Context, *CreateContactTagRequest) (*CreateContactTagResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateContactTag not implemented")
}
func (UnimplementedMetadataServiceServer) GetContactTag(context.Context, *GetContactTagRequest) (*GetContactTagResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetContactTag not implemented")
}
func (UnimplementedMetadataServiceServer) ListContactTags(context.Context, *ListContactTagsRequest) (*ListContactTagsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListContactTags not implemented")
}
func (UnimplementedMetadataServiceServer) UpdateContactTag(context.Context, *UpdateContactTagRequest) (*UpdateContactTagResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateContactTag not implemented")
}
func (UnimplementedMetadataServiceServer) DeleteContactTag(context.Context, *DeleteContactTagRequest) (*DeleteContactTagResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteContactTag not implemented")
}
func (UnimplementedMetadataServiceServer) CreateLead(context.Context, *CreateLeadRequest) (*CreateLeadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateLead not implemented")
}
func (UnimplementedMetadataServiceServer) GetLead(context.Context, *GetLeadRequest) (*GetLeadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLead not implemented")
}
func (UnimplementedMetadataServiceServer) ListLeads(context.Context, *ListLeadsRequest) (*ListLeadsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListLeads not implemented")
}
func (UnimplementedMetadataServiceServer) UpdateLead(context.Context, *UpdateLeadRequest) (*UpdateLeadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateLead not implemented")
}
func (UnimplementedMetadataServiceServer) DeleteLead(context.Context, *DeleteLeadRequest) (*DeleteLeadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteLead not implemented")
}
func (UnimplementedMetadataServiceServer) CreateAddress(context.Context, *CreateAddressRequest) (*CreateAddressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAddress not implemented")
}
func (UnimplementedMetadataServiceServer) GetAddress(context.Context, *GetAddressRequest) (*GetAddressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAddress not implemented")
}
func (UnimplementedMetadataServiceServer) ListAddresses(context.Context, *ListAddressesRequest) (*ListAddressesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAddresses not implemented")
}
func (UnimplementedMetadataServiceServer) UpdateAddress(context.Context, *UpdateAddressRequest) (*UpdateAddressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAddress not implemented")
}
func (UnimplementedMetadataServiceServer) DeleteAddress(context.Context, *DeleteAddressRequest) (*DeleteAddressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAddress not implemented")
}
func (UnimplementedMetadataServiceServer) CreateCustomField(context.Context, *CreateCustomFieldRequest) (*CreateCustomFieldResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCustomField not implemented")
}
func (UnimplementedMetadataServiceServer) GetCustomField(context.Context, *GetCustomFieldRequest) (*GetCustomFieldResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomField not implemented")
}
func (UnimplementedMetadataServiceServer) ListCustomFields(context.Context, *ListCustomFieldsRequest) (*ListCustomFieldsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCustomFields not implemented")
}
func (UnimplementedMetadataServiceServer) UpdateCustomField(context.Context, *UpdateCustomFieldRequest) (*UpdateCustomFieldResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCustomField not implemented")
}
func (UnimplementedMetadataServiceServer) BatchUpdateCustomFields(context.Context, *BatchUpdateCustomFieldsRequest) (*BatchUpdateCustomFieldsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchUpdateCustomFields not implemented")
}
func (UnimplementedMetadataServiceServer) DeleteCustomField(context.Context, *DeleteCustomFieldRequest) (*DeleteCustomFieldResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCustomField not implemented")
}
func (UnimplementedMetadataServiceServer) CreateMetadata(context.Context, *CreateMetadataRequest) (*CreateMetadataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMetadata not implemented")
}
func (UnimplementedMetadataServiceServer) GetMetadata(context.Context, *GetMetadataRequest) (*GetMetadataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMetadata not implemented")
}
func (UnimplementedMetadataServiceServer) ListMetadata(context.Context, *ListMetadataRequest) (*ListMetadataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMetadata not implemented")
}
func (UnimplementedMetadataServiceServer) UpdateMetadata(context.Context, *UpdateMetadataRequest) (*UpdateMetadataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMetadata not implemented")
}
func (UnimplementedMetadataServiceServer) DeleteMetadata(context.Context, *DeleteMetadataRequest) (*DeleteMetadataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteMetadata not implemented")
}
func (UnimplementedMetadataServiceServer) CreateMetadataAggregate(context.Context, *CreateMetadataAggregateRequest) (*CreateMetadataAggregateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMetadataAggregate not implemented")
}
func (UnimplementedMetadataServiceServer) mustEmbedUnimplementedMetadataServiceServer() {}
func (UnimplementedMetadataServiceServer) testEmbeddedByValue()                         {}

// UnsafeMetadataServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MetadataServiceServer will
// result in compilation errors.
type UnsafeMetadataServiceServer interface {
	mustEmbedUnimplementedMetadataServiceServer()
}

func RegisterMetadataServiceServer(s grpc.ServiceRegistrar, srv MetadataServiceServer) {
	// If the following call pancis, it indicates UnimplementedMetadataServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&MetadataService_ServiceDesc, srv)
}

func _MetadataService_CreateCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).CreateCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_CreateCustomer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).CreateCustomer(ctx, req.(*CreateCustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_GetCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).GetCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_GetCustomer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).GetCustomer(ctx, req.(*GetCustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_ListCustomers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCustomersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).ListCustomers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_ListCustomers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).ListCustomers(ctx, req.(*ListCustomersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_UpdateCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).UpdateCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_UpdateCustomer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).UpdateCustomer(ctx, req.(*UpdateCustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_DeleteCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).DeleteCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_DeleteCustomer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).DeleteCustomer(ctx, req.(*DeleteCustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_CreateCustomerAggregate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCustomerAggregateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).CreateCustomerAggregate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_CreateCustomerAggregate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).CreateCustomerAggregate(ctx, req.(*CreateCustomerAggregateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_CreateContact_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateContactRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).CreateContact(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_CreateContact_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).CreateContact(ctx, req.(*CreateContactRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_GetContact_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetContactRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).GetContact(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_GetContact_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).GetContact(ctx, req.(*GetContactRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_ListContacts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListContactsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).ListContacts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_ListContacts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).ListContacts(ctx, req.(*ListContactsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_UpdateContact_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateContactRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).UpdateContact(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_UpdateContact_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).UpdateContact(ctx, req.(*UpdateContactRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_DeleteContact_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteContactRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).DeleteContact(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_DeleteContact_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).DeleteContact(ctx, req.(*DeleteContactRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_CreateCustomerRelatedData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCustomerRelatedDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).CreateCustomerRelatedData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_CreateCustomerRelatedData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).CreateCustomerRelatedData(ctx, req.(*CreateCustomerRelatedDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_GetCustomerRelatedData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomerRelatedDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).GetCustomerRelatedData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_GetCustomerRelatedData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).GetCustomerRelatedData(ctx, req.(*GetCustomerRelatedDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_ListCustomerRelatedData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCustomerRelatedDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).ListCustomerRelatedData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_ListCustomerRelatedData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).ListCustomerRelatedData(ctx, req.(*ListCustomerRelatedDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_UpdateCustomerRelatedData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCustomerRelatedDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).UpdateCustomerRelatedData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_UpdateCustomerRelatedData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).UpdateCustomerRelatedData(ctx, req.(*UpdateCustomerRelatedDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_DeleteCustomerRelatedData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCustomerRelatedDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).DeleteCustomerRelatedData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_DeleteCustomerRelatedData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).DeleteCustomerRelatedData(ctx, req.(*DeleteCustomerRelatedDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_CreateContactTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateContactTagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).CreateContactTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_CreateContactTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).CreateContactTag(ctx, req.(*CreateContactTagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_GetContactTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetContactTagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).GetContactTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_GetContactTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).GetContactTag(ctx, req.(*GetContactTagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_ListContactTags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListContactTagsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).ListContactTags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_ListContactTags_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).ListContactTags(ctx, req.(*ListContactTagsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_UpdateContactTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateContactTagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).UpdateContactTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_UpdateContactTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).UpdateContactTag(ctx, req.(*UpdateContactTagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_DeleteContactTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteContactTagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).DeleteContactTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_DeleteContactTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).DeleteContactTag(ctx, req.(*DeleteContactTagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_CreateLead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateLeadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).CreateLead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_CreateLead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).CreateLead(ctx, req.(*CreateLeadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_GetLead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLeadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).GetLead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_GetLead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).GetLead(ctx, req.(*GetLeadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_ListLeads_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListLeadsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).ListLeads(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_ListLeads_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).ListLeads(ctx, req.(*ListLeadsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_UpdateLead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLeadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).UpdateLead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_UpdateLead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).UpdateLead(ctx, req.(*UpdateLeadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_DeleteLead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteLeadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).DeleteLead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_DeleteLead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).DeleteLead(ctx, req.(*DeleteLeadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_CreateAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).CreateAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_CreateAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).CreateAddress(ctx, req.(*CreateAddressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_GetAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).GetAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_GetAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).GetAddress(ctx, req.(*GetAddressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_ListAddresses_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAddressesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).ListAddresses(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_ListAddresses_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).ListAddresses(ctx, req.(*ListAddressesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_UpdateAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).UpdateAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_UpdateAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).UpdateAddress(ctx, req.(*UpdateAddressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_DeleteAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).DeleteAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_DeleteAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).DeleteAddress(ctx, req.(*DeleteAddressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_CreateCustomField_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCustomFieldRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).CreateCustomField(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_CreateCustomField_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).CreateCustomField(ctx, req.(*CreateCustomFieldRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_GetCustomField_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomFieldRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).GetCustomField(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_GetCustomField_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).GetCustomField(ctx, req.(*GetCustomFieldRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_ListCustomFields_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCustomFieldsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).ListCustomFields(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_ListCustomFields_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).ListCustomFields(ctx, req.(*ListCustomFieldsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_UpdateCustomField_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCustomFieldRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).UpdateCustomField(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_UpdateCustomField_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).UpdateCustomField(ctx, req.(*UpdateCustomFieldRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_BatchUpdateCustomFields_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpdateCustomFieldsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).BatchUpdateCustomFields(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_BatchUpdateCustomFields_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).BatchUpdateCustomFields(ctx, req.(*BatchUpdateCustomFieldsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_DeleteCustomField_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCustomFieldRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).DeleteCustomField(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_DeleteCustomField_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).DeleteCustomField(ctx, req.(*DeleteCustomFieldRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_CreateMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateMetadataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).CreateMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_CreateMetadata_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).CreateMetadata(ctx, req.(*CreateMetadataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_GetMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMetadataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).GetMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_GetMetadata_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).GetMetadata(ctx, req.(*GetMetadataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_ListMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMetadataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).ListMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_ListMetadata_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).ListMetadata(ctx, req.(*ListMetadataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_UpdateMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMetadataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).UpdateMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_UpdateMetadata_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).UpdateMetadata(ctx, req.(*UpdateMetadataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_DeleteMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteMetadataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).DeleteMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_DeleteMetadata_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).DeleteMetadata(ctx, req.(*DeleteMetadataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataService_CreateMetadataAggregate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateMetadataAggregateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataServiceServer).CreateMetadataAggregate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetadataService_CreateMetadataAggregate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataServiceServer).CreateMetadataAggregate(ctx, req.(*CreateMetadataAggregateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MetadataService_ServiceDesc is the grpc.ServiceDesc for MetadataService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MetadataService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.customer.v2.MetadataService",
	HandlerType: (*MetadataServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateCustomer",
			Handler:    _MetadataService_CreateCustomer_Handler,
		},
		{
			MethodName: "GetCustomer",
			Handler:    _MetadataService_GetCustomer_Handler,
		},
		{
			MethodName: "ListCustomers",
			Handler:    _MetadataService_ListCustomers_Handler,
		},
		{
			MethodName: "UpdateCustomer",
			Handler:    _MetadataService_UpdateCustomer_Handler,
		},
		{
			MethodName: "DeleteCustomer",
			Handler:    _MetadataService_DeleteCustomer_Handler,
		},
		{
			MethodName: "CreateCustomerAggregate",
			Handler:    _MetadataService_CreateCustomerAggregate_Handler,
		},
		{
			MethodName: "CreateContact",
			Handler:    _MetadataService_CreateContact_Handler,
		},
		{
			MethodName: "GetContact",
			Handler:    _MetadataService_GetContact_Handler,
		},
		{
			MethodName: "ListContacts",
			Handler:    _MetadataService_ListContacts_Handler,
		},
		{
			MethodName: "UpdateContact",
			Handler:    _MetadataService_UpdateContact_Handler,
		},
		{
			MethodName: "DeleteContact",
			Handler:    _MetadataService_DeleteContact_Handler,
		},
		{
			MethodName: "CreateCustomerRelatedData",
			Handler:    _MetadataService_CreateCustomerRelatedData_Handler,
		},
		{
			MethodName: "GetCustomerRelatedData",
			Handler:    _MetadataService_GetCustomerRelatedData_Handler,
		},
		{
			MethodName: "ListCustomerRelatedData",
			Handler:    _MetadataService_ListCustomerRelatedData_Handler,
		},
		{
			MethodName: "UpdateCustomerRelatedData",
			Handler:    _MetadataService_UpdateCustomerRelatedData_Handler,
		},
		{
			MethodName: "DeleteCustomerRelatedData",
			Handler:    _MetadataService_DeleteCustomerRelatedData_Handler,
		},
		{
			MethodName: "CreateContactTag",
			Handler:    _MetadataService_CreateContactTag_Handler,
		},
		{
			MethodName: "GetContactTag",
			Handler:    _MetadataService_GetContactTag_Handler,
		},
		{
			MethodName: "ListContactTags",
			Handler:    _MetadataService_ListContactTags_Handler,
		},
		{
			MethodName: "UpdateContactTag",
			Handler:    _MetadataService_UpdateContactTag_Handler,
		},
		{
			MethodName: "DeleteContactTag",
			Handler:    _MetadataService_DeleteContactTag_Handler,
		},
		{
			MethodName: "CreateLead",
			Handler:    _MetadataService_CreateLead_Handler,
		},
		{
			MethodName: "GetLead",
			Handler:    _MetadataService_GetLead_Handler,
		},
		{
			MethodName: "ListLeads",
			Handler:    _MetadataService_ListLeads_Handler,
		},
		{
			MethodName: "UpdateLead",
			Handler:    _MetadataService_UpdateLead_Handler,
		},
		{
			MethodName: "DeleteLead",
			Handler:    _MetadataService_DeleteLead_Handler,
		},
		{
			MethodName: "CreateAddress",
			Handler:    _MetadataService_CreateAddress_Handler,
		},
		{
			MethodName: "GetAddress",
			Handler:    _MetadataService_GetAddress_Handler,
		},
		{
			MethodName: "ListAddresses",
			Handler:    _MetadataService_ListAddresses_Handler,
		},
		{
			MethodName: "UpdateAddress",
			Handler:    _MetadataService_UpdateAddress_Handler,
		},
		{
			MethodName: "DeleteAddress",
			Handler:    _MetadataService_DeleteAddress_Handler,
		},
		{
			MethodName: "CreateCustomField",
			Handler:    _MetadataService_CreateCustomField_Handler,
		},
		{
			MethodName: "GetCustomField",
			Handler:    _MetadataService_GetCustomField_Handler,
		},
		{
			MethodName: "ListCustomFields",
			Handler:    _MetadataService_ListCustomFields_Handler,
		},
		{
			MethodName: "UpdateCustomField",
			Handler:    _MetadataService_UpdateCustomField_Handler,
		},
		{
			MethodName: "BatchUpdateCustomFields",
			Handler:    _MetadataService_BatchUpdateCustomFields_Handler,
		},
		{
			MethodName: "DeleteCustomField",
			Handler:    _MetadataService_DeleteCustomField_Handler,
		},
		{
			MethodName: "CreateMetadata",
			Handler:    _MetadataService_CreateMetadata_Handler,
		},
		{
			MethodName: "GetMetadata",
			Handler:    _MetadataService_GetMetadata_Handler,
		},
		{
			MethodName: "ListMetadata",
			Handler:    _MetadataService_ListMetadata_Handler,
		},
		{
			MethodName: "UpdateMetadata",
			Handler:    _MetadataService_UpdateMetadata_Handler,
		},
		{
			MethodName: "DeleteMetadata",
			Handler:    _MetadataService_DeleteMetadata_Handler,
		},
		{
			MethodName: "CreateMetadataAggregate",
			Handler:    _MetadataService_CreateMetadataAggregate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/customer/v2/metadata_service.proto",
}
