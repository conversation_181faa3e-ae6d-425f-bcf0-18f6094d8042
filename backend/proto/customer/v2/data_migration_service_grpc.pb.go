// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/customer/v2/data_migration_service.proto

package customerpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	DataMigrationService_ImportCustomers_FullMethodName     = "/backend.proto.customer.v2.DataMigrationService/ImportCustomers"
	DataMigrationService_ListImportHistories_FullMethodName = "/backend.proto.customer.v2.DataMigrationService/ListImportHistories"
)

// DataMigrationServiceClient is the client API for DataMigrationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// DataMigrationService 提供数据迁移服务
type DataMigrationServiceClient interface {
	// ImportCustomers 导入客户
	ImportCustomers(ctx context.Context, in *ImportCustomersRequest, opts ...grpc.CallOption) (*ImportCustomersResponse, error)
	// ListImportHistories 列出导入历史
	ListImportHistories(ctx context.Context, in *ListImportHistoriesRequest, opts ...grpc.CallOption) (*ListImportHistoriesResponse, error)
}

type dataMigrationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDataMigrationServiceClient(cc grpc.ClientConnInterface) DataMigrationServiceClient {
	return &dataMigrationServiceClient{cc}
}

func (c *dataMigrationServiceClient) ImportCustomers(ctx context.Context, in *ImportCustomersRequest, opts ...grpc.CallOption) (*ImportCustomersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ImportCustomersResponse)
	err := c.cc.Invoke(ctx, DataMigrationService_ImportCustomers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataMigrationServiceClient) ListImportHistories(ctx context.Context, in *ListImportHistoriesRequest, opts ...grpc.CallOption) (*ListImportHistoriesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListImportHistoriesResponse)
	err := c.cc.Invoke(ctx, DataMigrationService_ListImportHistories_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DataMigrationServiceServer is the server API for DataMigrationService service.
// All implementations must embed UnimplementedDataMigrationServiceServer
// for forward compatibility.
//
// DataMigrationService 提供数据迁移服务
type DataMigrationServiceServer interface {
	// ImportCustomers 导入客户
	ImportCustomers(context.Context, *ImportCustomersRequest) (*ImportCustomersResponse, error)
	// ListImportHistories 列出导入历史
	ListImportHistories(context.Context, *ListImportHistoriesRequest) (*ListImportHistoriesResponse, error)
	mustEmbedUnimplementedDataMigrationServiceServer()
}

// UnimplementedDataMigrationServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedDataMigrationServiceServer struct{}

func (UnimplementedDataMigrationServiceServer) ImportCustomers(context.Context, *ImportCustomersRequest) (*ImportCustomersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImportCustomers not implemented")
}
func (UnimplementedDataMigrationServiceServer) ListImportHistories(context.Context, *ListImportHistoriesRequest) (*ListImportHistoriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListImportHistories not implemented")
}
func (UnimplementedDataMigrationServiceServer) mustEmbedUnimplementedDataMigrationServiceServer() {}
func (UnimplementedDataMigrationServiceServer) testEmbeddedByValue()                              {}

// UnsafeDataMigrationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DataMigrationServiceServer will
// result in compilation errors.
type UnsafeDataMigrationServiceServer interface {
	mustEmbedUnimplementedDataMigrationServiceServer()
}

func RegisterDataMigrationServiceServer(s grpc.ServiceRegistrar, srv DataMigrationServiceServer) {
	// If the following call pancis, it indicates UnimplementedDataMigrationServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&DataMigrationService_ServiceDesc, srv)
}

func _DataMigrationService_ImportCustomers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImportCustomersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataMigrationServiceServer).ImportCustomers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataMigrationService_ImportCustomers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataMigrationServiceServer).ImportCustomers(ctx, req.(*ImportCustomersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataMigrationService_ListImportHistories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListImportHistoriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataMigrationServiceServer).ListImportHistories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataMigrationService_ListImportHistories_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataMigrationServiceServer).ListImportHistories(ctx, req.(*ListImportHistoriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DataMigrationService_ServiceDesc is the grpc.ServiceDesc for DataMigrationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DataMigrationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.customer.v2.DataMigrationService",
	HandlerType: (*DataMigrationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ImportCustomers",
			Handler:    _DataMigrationService_ImportCustomers_Handler,
		},
		{
			MethodName: "ListImportHistories",
			Handler:    _DataMigrationService_ListImportHistories_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/customer/v2/data_migration_service.proto",
}
