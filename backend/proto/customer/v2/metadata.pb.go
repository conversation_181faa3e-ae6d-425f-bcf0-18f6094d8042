// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.8
// 	protoc        (unknown)
// source: backend/proto/customer/v2/metadata.proto

package customerpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	money "google.golang.org/genproto/googleapis/type/money"
	phone_number "google.golang.org/genproto/googleapis/type/phone_number"
	postaladdress "google.golang.org/genproto/googleapis/type/postaladdress"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// customer type
type CustomerType int32

const (
	// 0 is reserved for unspecified
	CustomerType_CUSTOMER_TYPE_UNSPECIFIED CustomerType = 0
	// lead
	CustomerType_LEAD CustomerType = 1
	// customer
	CustomerType_CUSTOMER CustomerType = 2
)

// Enum value maps for CustomerType.
var (
	CustomerType_name = map[int32]string{
		0: "CUSTOMER_TYPE_UNSPECIFIED",
		1: "LEAD",
		2: "CUSTOMER",
	}
	CustomerType_value = map[string]int32{
		"CUSTOMER_TYPE_UNSPECIFIED": 0,
		"LEAD":                      1,
		"CUSTOMER":                  2,
	}
)

func (x CustomerType) Enum() *CustomerType {
	p := new(CustomerType)
	*p = x
	return p
}

func (x CustomerType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustomerType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_proto_enumTypes[0].Descriptor()
}

func (CustomerType) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_proto_enumTypes[0]
}

func (x CustomerType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustomerType.Descriptor instead.
func (CustomerType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{0}
}

// state enumeration
type Customer_State int32

const (
	// 0 is reserved for unspecified
	Customer_STATE_UNSPECIFIED Customer_State = 0
	// active
	Customer_ACTIVE Customer_State = 1
	// inactive
	Customer_INACTIVE Customer_State = 2
	// deleted
	Customer_DELETED Customer_State = 3
)

// Enum value maps for Customer_State.
var (
	Customer_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "ACTIVE",
		2: "INACTIVE",
		3: "DELETED",
	}
	Customer_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"ACTIVE":            1,
		"INACTIVE":          2,
		"DELETED":           3,
	}
)

func (x Customer_State) Enum() *Customer_State {
	p := new(Customer_State)
	*p = x
	return p
}

func (x Customer_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Customer_State) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_proto_enumTypes[1].Descriptor()
}

func (Customer_State) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_proto_enumTypes[1]
}

func (x Customer_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Customer_State.Descriptor instead.
func (Customer_State) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{0, 0}
}

// state enumeration
type CustomerRelatedData_State int32

const (
	// 0 is reserved for unspecified
	CustomerRelatedData_STATE_UNSPECIFIED CustomerRelatedData_State = 0
	// active
	CustomerRelatedData_ACTIVE CustomerRelatedData_State = 1
	// inactive
	CustomerRelatedData_INACTIVE CustomerRelatedData_State = 2
	// deleted
	CustomerRelatedData_DELETED CustomerRelatedData_State = 3
)

// Enum value maps for CustomerRelatedData_State.
var (
	CustomerRelatedData_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "ACTIVE",
		2: "INACTIVE",
		3: "DELETED",
	}
	CustomerRelatedData_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"ACTIVE":            1,
		"INACTIVE":          2,
		"DELETED":           3,
	}
)

func (x CustomerRelatedData_State) Enum() *CustomerRelatedData_State {
	p := new(CustomerRelatedData_State)
	*p = x
	return p
}

func (x CustomerRelatedData_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustomerRelatedData_State) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_proto_enumTypes[2].Descriptor()
}

func (CustomerRelatedData_State) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_proto_enumTypes[2]
}

func (x CustomerRelatedData_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustomerRelatedData_State.Descriptor instead.
func (CustomerRelatedData_State) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{1, 0}
}

// state enumeration
type Lead_State int32

const (
	// 0 is reserved for unspecified
	Lead_STATE_UNSPECIFIED Lead_State = 0
	// active
	Lead_ACTIVE Lead_State = 1
	// inactive
	Lead_INACTIVE Lead_State = 2
	// deleted
	Lead_DELETED Lead_State = 3
)

// Enum value maps for Lead_State.
var (
	Lead_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "ACTIVE",
		2: "INACTIVE",
		3: "DELETED",
	}
	Lead_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"ACTIVE":            1,
		"INACTIVE":          2,
		"DELETED":           3,
	}
)

func (x Lead_State) Enum() *Lead_State {
	p := new(Lead_State)
	*p = x
	return p
}

func (x Lead_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Lead_State) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_proto_enumTypes[3].Descriptor()
}

func (Lead_State) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_proto_enumTypes[3]
}

func (x Lead_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Lead_State.Descriptor instead.
func (Lead_State) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{2, 0}
}

// state enumeration
type Contact_State int32

const (
	// 0 is reserved for unspecified
	Contact_STATE_UNSPECIFIED Contact_State = 0
	// active
	Contact_ACTIVE Contact_State = 1
	// inactive
	Contact_INACTIVE Contact_State = 2
	// deleted
	Contact_DELETED Contact_State = 3
)

// Enum value maps for Contact_State.
var (
	Contact_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "ACTIVE",
		2: "INACTIVE",
		3: "DELETED",
	}
	Contact_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"ACTIVE":            1,
		"INACTIVE":          2,
		"DELETED":           3,
	}
)

func (x Contact_State) Enum() *Contact_State {
	p := new(Contact_State)
	*p = x
	return p
}

func (x Contact_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Contact_State) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_proto_enumTypes[4].Descriptor()
}

func (Contact_State) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_proto_enumTypes[4]
}

func (x Contact_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Contact_State.Descriptor instead.
func (Contact_State) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{3, 0}
}

// state enumeration
type ContactTag_State int32

const (
	// 0 is reserved for unspecified
	ContactTag_STATE_UNSPECIFIED ContactTag_State = 0
	// active
	ContactTag_ACTIVE ContactTag_State = 1
	// inactive
	ContactTag_INACTIVE ContactTag_State = 2
	// deleted
	ContactTag_DELETED ContactTag_State = 3
)

// Enum value maps for ContactTag_State.
var (
	ContactTag_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "ACTIVE",
		2: "INACTIVE",
		3: "DELETED",
	}
	ContactTag_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"ACTIVE":            1,
		"INACTIVE":          2,
		"DELETED":           3,
	}
)

func (x ContactTag_State) Enum() *ContactTag_State {
	p := new(ContactTag_State)
	*p = x
	return p
}

func (x ContactTag_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ContactTag_State) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_proto_enumTypes[5].Descriptor()
}

func (ContactTag_State) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_proto_enumTypes[5]
}

func (x ContactTag_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ContactTag_State.Descriptor instead.
func (ContactTag_State) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{4, 0}
}

// type
type ContactTag_Type int32

const (
	// 0 is reserved for unspecified
	ContactTag_TYPE_UNSPECIFIED ContactTag_Type = 0
	// custom
	ContactTag_CUSTOM ContactTag_Type = 1
	// 以下都是内置标签
	// EMERGENCY
	ContactTag_EMERGENCY ContactTag_Type = 2
	// PICKUP
	ContactTag_PICKUP ContactTag_Type = 3
	// COMMUNICATION
	ContactTag_COMMUNICATION ContactTag_Type = 4
	// PRIMARY
	ContactTag_PRIMARY ContactTag_Type = 5
)

// Enum value maps for ContactTag_Type.
var (
	ContactTag_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "CUSTOM",
		2: "EMERGENCY",
		3: "PICKUP",
		4: "COMMUNICATION",
		5: "PRIMARY",
	}
	ContactTag_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"CUSTOM":           1,
		"EMERGENCY":        2,
		"PICKUP":           3,
		"COMMUNICATION":    4,
		"PRIMARY":          5,
	}
)

func (x ContactTag_Type) Enum() *ContactTag_Type {
	p := new(ContactTag_Type)
	*p = x
	return p
}

func (x ContactTag_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ContactTag_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_proto_enumTypes[6].Descriptor()
}

func (ContactTag_Type) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_proto_enumTypes[6]
}

func (x ContactTag_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ContactTag_Type.Descriptor instead.
func (ContactTag_Type) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{4, 1}
}

// state enumeration
type Address_State int32

const (
	// 0 is reserved for unspecified
	Address_STATE_UNSPECIFIED Address_State = 0
	// active
	Address_ACTIVE Address_State = 1
	// inactive
	Address_INACTIVE Address_State = 2
	// deleted
	Address_DELETED Address_State = 3
)

// Enum value maps for Address_State.
var (
	Address_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "ACTIVE",
		2: "INACTIVE",
		3: "DELETED",
	}
	Address_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"ACTIVE":            1,
		"INACTIVE":          2,
		"DELETED":           3,
	}
)

func (x Address_State) Enum() *Address_State {
	p := new(Address_State)
	*p = x
	return p
}

func (x Address_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Address_State) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_proto_enumTypes[7].Descriptor()
}

func (Address_State) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_proto_enumTypes[7]
}

func (x Address_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Address_State.Descriptor instead.
func (Address_State) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{5, 0}
}

// address type
type Address_Type int32

const (
	// 0 is reserved for unspecified
	Address_TYPE_UNSPECIFIED Address_Type = 0
	// primary
	Address_PRIMARY Address_Type = 1
	// additional
	Address_ADDITIONAL Address_Type = 2
)

// Enum value maps for Address_Type.
var (
	Address_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "PRIMARY",
		2: "ADDITIONAL",
	}
	Address_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"PRIMARY":          1,
		"ADDITIONAL":       2,
	}
)

func (x Address_Type) Enum() *Address_Type {
	p := new(Address_Type)
	*p = x
	return p
}

func (x Address_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Address_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_proto_enumTypes[8].Descriptor()
}

func (Address_Type) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_proto_enumTypes[8]
}

func (x Address_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Address_Type.Descriptor instead.
func (Address_Type) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{5, 1}
}

// field state enumeration
type CustomField_State int32

const (
	// 0 is reserved for unspecified
	CustomField_STATE_UNSPECIFIED CustomField_State = 0
	// active
	CustomField_ACTIVE CustomField_State = 1
	// deleted
	CustomField_DELETED CustomField_State = 2
)

// Enum value maps for CustomField_State.
var (
	CustomField_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "ACTIVE",
		2: "DELETED",
	}
	CustomField_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"ACTIVE":            1,
		"DELETED":           2,
	}
)

func (x CustomField_State) Enum() *CustomField_State {
	p := new(CustomField_State)
	*p = x
	return p
}

func (x CustomField_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustomField_State) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_proto_enumTypes[9].Descriptor()
}

func (CustomField_State) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_proto_enumTypes[9]
}

func (x CustomField_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustomField_State.Descriptor instead.
func (CustomField_State) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{6, 0}
}

// field type enumeration
type CustomField_Type int32

const (
	// 0 is reserved for unspecified
	CustomField_TYPE_UNSPECIFIED CustomField_Type = 0
	// short text
	CustomField_SHORT_TEXT CustomField_Type = 1
	// long text
	CustomField_LONG_TEXT CustomField_Type = 2
	// number
	CustomField_NUMBER CustomField_Type = 3
	// date
	CustomField_DATE CustomField_Type = 4
	// boolean
	CustomField_BOOLEAN CustomField_Type = 5
	// currency
	CustomField_CURRENCY CustomField_Type = 6
	// select
	CustomField_SELECT CustomField_Type = 7
	// multi select
	CustomField_MULTI_SELECT CustomField_Type = 8
	// relation
	CustomField_RELATION CustomField_Type = 9
	// money
	CustomField_MONEY CustomField_Type = 10
	// time
	CustomField_TIME CustomField_Type = 11
	// datetime
	CustomField_DATETIME CustomField_Type = 12
)

// Enum value maps for CustomField_Type.
var (
	CustomField_Type_name = map[int32]string{
		0:  "TYPE_UNSPECIFIED",
		1:  "SHORT_TEXT",
		2:  "LONG_TEXT",
		3:  "NUMBER",
		4:  "DATE",
		5:  "BOOLEAN",
		6:  "CURRENCY",
		7:  "SELECT",
		8:  "MULTI_SELECT",
		9:  "RELATION",
		10: "MONEY",
		11: "TIME",
		12: "DATETIME",
	}
	CustomField_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"SHORT_TEXT":       1,
		"LONG_TEXT":        2,
		"NUMBER":           3,
		"DATE":             4,
		"BOOLEAN":          5,
		"CURRENCY":         6,
		"SELECT":           7,
		"MULTI_SELECT":     8,
		"RELATION":         9,
		"MONEY":            10,
		"TIME":             11,
		"DATETIME":         12,
	}
)

func (x CustomField_Type) Enum() *CustomField_Type {
	p := new(CustomField_Type)
	*p = x
	return p
}

func (x CustomField_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustomField_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_proto_enumTypes[10].Descriptor()
}

func (CustomField_Type) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_proto_enumTypes[10]
}

func (x CustomField_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustomField_Type.Descriptor instead.
func (CustomField_Type) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{6, 1}
}

// custom field association type
type CustomField_AssociationType int32

const (
	// 0 is reserved for unspecified
	CustomField_ASSOCIATION_TYPE_UNSPECIFIED CustomField_AssociationType = 0
	// customer
	CustomField_CUSTOMER CustomField_AssociationType = 1
	// lead
	CustomField_LEAD CustomField_AssociationType = 2
)

// Enum value maps for CustomField_AssociationType.
var (
	CustomField_AssociationType_name = map[int32]string{
		0: "ASSOCIATION_TYPE_UNSPECIFIED",
		1: "CUSTOMER",
		2: "LEAD",
	}
	CustomField_AssociationType_value = map[string]int32{
		"ASSOCIATION_TYPE_UNSPECIFIED": 0,
		"CUSTOMER":                     1,
		"LEAD":                         2,
	}
)

func (x CustomField_AssociationType) Enum() *CustomField_AssociationType {
	p := new(CustomField_AssociationType)
	*p = x
	return p
}

func (x CustomField_AssociationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustomField_AssociationType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_proto_enumTypes[11].Descriptor()
}

func (CustomField_AssociationType) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_proto_enumTypes[11]
}

func (x CustomField_AssociationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustomField_AssociationType.Descriptor instead.
func (CustomField_AssociationType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{6, 2}
}

// 来源
type CustomField_Source int32

const (
	// 0 is reserved for unspecified
	CustomField_SOURCE_UNSPECIFIED CustomField_Source = 0
	// 自定义
	CustomField_CUSTOM CustomField_Source = 1
	// 系统
	CustomField_SYSTEM CustomField_Source = 2
)

// Enum value maps for CustomField_Source.
var (
	CustomField_Source_name = map[int32]string{
		0: "SOURCE_UNSPECIFIED",
		1: "CUSTOM",
		2: "SYSTEM",
	}
	CustomField_Source_value = map[string]int32{
		"SOURCE_UNSPECIFIED": 0,
		"CUSTOM":             1,
		"SYSTEM":             2,
	}
)

func (x CustomField_Source) Enum() *CustomField_Source {
	p := new(CustomField_Source)
	*p = x
	return p
}

func (x CustomField_Source) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustomField_Source) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_proto_enumTypes[12].Descriptor()
}

func (CustomField_Source) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_proto_enumTypes[12]
}

func (x CustomField_Source) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustomField_Source.Descriptor instead.
func (CustomField_Source) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{6, 3}
}

// 实体类型
type CustomField_Value_Relation_Entity int32

const (
	// 0 is reserved for unspecified
	CustomField_Value_Relation_ENTITY_UNSPECIFIED CustomField_Value_Relation_Entity = 0
	// customer
	CustomField_Value_Relation_CUSTOMER CustomField_Value_Relation_Entity = 1
	// lead
	CustomField_Value_Relation_LEAD CustomField_Value_Relation_Entity = 2
)

// Enum value maps for CustomField_Value_Relation_Entity.
var (
	CustomField_Value_Relation_Entity_name = map[int32]string{
		0: "ENTITY_UNSPECIFIED",
		1: "CUSTOMER",
		2: "LEAD",
	}
	CustomField_Value_Relation_Entity_value = map[string]int32{
		"ENTITY_UNSPECIFIED": 0,
		"CUSTOMER":           1,
		"LEAD":               2,
	}
)

func (x CustomField_Value_Relation_Entity) Enum() *CustomField_Value_Relation_Entity {
	p := new(CustomField_Value_Relation_Entity)
	*p = x
	return p
}

func (x CustomField_Value_Relation_Entity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustomField_Value_Relation_Entity) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_proto_enumTypes[13].Descriptor()
}

func (CustomField_Value_Relation_Entity) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_proto_enumTypes[13]
}

func (x CustomField_Value_Relation_Entity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustomField_Value_Relation_Entity.Descriptor instead.
func (CustomField_Value_Relation_Entity) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{6, 0, 0, 0}
}

// state enumeration for unified metadata
type Metadata_State int32

const (
	// 0 is reserved for unspecified
	Metadata_STATE_UNSPECIFIED Metadata_State = 0
	// active
	Metadata_ACTIVE Metadata_State = 1
	// inactive
	Metadata_INACTIVE Metadata_State = 2
	// deleted
	Metadata_DELETED Metadata_State = 3
)

// Enum value maps for Metadata_State.
var (
	Metadata_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "ACTIVE",
		2: "INACTIVE",
		3: "DELETED",
	}
	Metadata_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"ACTIVE":            1,
		"INACTIVE":          2,
		"DELETED":           3,
	}
)

func (x Metadata_State) Enum() *Metadata_State {
	p := new(Metadata_State)
	*p = x
	return p
}

func (x Metadata_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Metadata_State) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_metadata_proto_enumTypes[14].Descriptor()
}

func (Metadata_State) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_metadata_proto_enumTypes[14]
}

func (x Metadata_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Metadata_State.Descriptor instead.
func (Metadata_State) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{8, 0}
}

// customer
type Customer struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 联系人ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// organization reference
	Organization *OrganizationRef `protobuf:"bytes,2,opt,name=organization,proto3" json:"organization,omitempty"`
	// core identifier information
	// 名 (最大长度255字符)
	GivenName string `protobuf:"bytes,3,opt,name=given_name,json=givenName,proto3" json:"given_name,omitempty"`
	// 姓 (最大长度255字符)
	FamilyName string `protobuf:"bytes,4,opt,name=family_name,json=familyName,proto3" json:"family_name,omitempty"`
	// basic classification
	// 实体状态（活跃/非活跃/删除）
	State Customer_State `protobuf:"varint,11,opt,name=state,proto3,enum=backend.proto.customer.v2.Customer_State" json:"state,omitempty"`
	// lifecycle
	LifecycleId int64 `protobuf:"varint,12,opt,name=lifecycle_id,json=lifecycleId,proto3" json:"lifecycle_id,omitempty"`
	// relationship
	// 负责人 id (staff id)
	OwnerStaffId int64 `protobuf:"varint,13,opt,name=owner_staff_id,json=ownerStaffId,proto3" json:"owner_staff_id,omitempty"`
	// extended fields
	// 自定义字段
	CustomFields *structpb.Struct `protobuf:"bytes,14,opt,name=custom_fields,json=customFields,proto3" json:"custom_fields,omitempty"`
	// audit information
	// 创建时间
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// 删除时间
	DeleteTime *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=delete_time,json=deleteTime,proto3" json:"delete_time,omitempty"`
	// additional fields
	// action state id
	ActionStateId int64 `protobuf:"varint,18,opt,name=action_state_id,json=actionStateId,proto3" json:"action_state_id,omitempty"`
	// avatar path
	AvatarPath string `protobuf:"bytes,19,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// customer code
	CustomerCode string `protobuf:"bytes,20,opt,name=customer_code,json=customerCode,proto3" json:"customer_code,omitempty"`
	// referral source id
	ReferralSourceId int64 `protobuf:"varint,21,opt,name=referral_source_id,json=referralSourceId,proto3" json:"referral_source_id,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *Customer) Reset() {
	*x = Customer{}
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Customer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Customer) ProtoMessage() {}

func (x *Customer) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Customer.ProtoReflect.Descriptor instead.
func (*Customer) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{0}
}

func (x *Customer) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Customer) GetOrganization() *OrganizationRef {
	if x != nil {
		return x.Organization
	}
	return nil
}

func (x *Customer) GetGivenName() string {
	if x != nil {
		return x.GivenName
	}
	return ""
}

func (x *Customer) GetFamilyName() string {
	if x != nil {
		return x.FamilyName
	}
	return ""
}

func (x *Customer) GetState() Customer_State {
	if x != nil {
		return x.State
	}
	return Customer_STATE_UNSPECIFIED
}

func (x *Customer) GetLifecycleId() int64 {
	if x != nil {
		return x.LifecycleId
	}
	return 0
}

func (x *Customer) GetOwnerStaffId() int64 {
	if x != nil {
		return x.OwnerStaffId
	}
	return 0
}

func (x *Customer) GetCustomFields() *structpb.Struct {
	if x != nil {
		return x.CustomFields
	}
	return nil
}

func (x *Customer) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Customer) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Customer) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

func (x *Customer) GetActionStateId() int64 {
	if x != nil {
		return x.ActionStateId
	}
	return 0
}

func (x *Customer) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *Customer) GetCustomerCode() string {
	if x != nil {
		return x.CustomerCode
	}
	return ""
}

func (x *Customer) GetReferralSourceId() int64 {
	if x != nil {
		return x.ReferralSourceId
	}
	return 0
}

// customer related data
type CustomerRelatedData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户相关数据ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 关联的客户ID
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 商家ID
	PreferredBusinessId int64 `protobuf:"varint,3,opt,name=preferred_business_id,json=preferredBusinessId,proto3" json:"preferred_business_id,omitempty"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,4,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 客户颜色
	ClientColor string `protobuf:"bytes,5,opt,name=client_color,json=clientColor,proto3" json:"client_color,omitempty"`
	// 是否屏蔽消息 (0-正常 1-屏蔽)
	IsBlockMessage int32 `protobuf:"varint,6,opt,name=is_block_message,json=isBlockMessage,proto3" json:"is_block_message,omitempty"`
	// 是否屏蔽在线预约 (0-正常 1-屏蔽)
	IsBlockOnlineBooking int32 `protobuf:"varint,7,opt,name=is_block_online_booking,json=isBlockOnlineBooking,proto3" json:"is_block_online_booking,omitempty"`
	// 登录邮箱
	LoginEmail string `protobuf:"bytes,8,opt,name=login_email,json=loginEmail,proto3" json:"login_email,omitempty"`
	// 推荐来源ID
	ReferralSourceId int32 `protobuf:"varint,9,opt,name=referral_source_id,json=referralSourceId,proto3" json:"referral_source_id,omitempty"`
	// 推荐来源描述
	ReferralSourceDesc string `protobuf:"bytes,10,opt,name=referral_source_desc,json=referralSourceDesc,proto3" json:"referral_source_desc,omitempty"`
	// 发送自动邮件 (0-不接受 1-接受)
	SendAutoEmail int32 `protobuf:"varint,11,opt,name=send_auto_email,json=sendAutoEmail,proto3" json:"send_auto_email,omitempty"`
	// 发送自动短信 (0-不接受 1-接受)
	SendAutoMessage int32 `protobuf:"varint,12,opt,name=send_auto_message,json=sendAutoMessage,proto3" json:"send_auto_message,omitempty"`
	// 发送app自动短信 (0-不接受 1-接受)
	SendAppAutoMessage int32 `protobuf:"varint,13,opt,name=send_app_auto_message,json=sendAppAutoMessage,proto3" json:"send_app_auto_message,omitempty"`
	// 未确认提醒方式 (1-text 2-email 3-phone call)
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: this is not a preposition --)
	UnconfirmedReminderBy []int32 `protobuf:"varint,14,rep,packed,name=unconfirmed_reminder_by,json=unconfirmedReminderBy,proto3" json:"unconfirmed_reminder_by,omitempty"`
	// 首选美容师ID
	PreferredGroomerId int32 `protobuf:"varint,15,opt,name=preferred_groomer_id,json=preferredGroomerId,proto3" json:"preferred_groomer_id,omitempty"`
	// 首选频率天数
	PreferredFrequencyDay int32 `protobuf:"varint,16,opt,name=preferred_frequency_day,json=preferredFrequencyDay,proto3" json:"preferred_frequency_day,omitempty"`
	// 首选频率类型 (0-by days 1-by weeks)
	PreferredFrequencyType int32 `protobuf:"varint,17,opt,name=preferred_frequency_type,json=preferredFrequencyType,proto3" json:"preferred_frequency_type,omitempty"`
	// 最后服务时间
	// (-- api-linter: core::0142::time-field-type=disabled
	//
	//	aip.dev/not-precedent: this is json string not a time --)
	LastServiceTime string `protobuf:"bytes,18,opt,name=last_service_time,json=lastServiceTime,proto3" json:"last_service_time,omitempty"`
	// 来源
	Source string `protobuf:"bytes,19,opt,name=source,proto3" json:"source,omitempty"`
	// 外部ID
	ExternalId string `protobuf:"bytes,20,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
	// 创建人
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: this is not a preposition --)
	CreateBy int32 `protobuf:"varint,21,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
	// 更新人
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: this is not a preposition --)
	UpdateBy int32 `protobuf:"varint,22,opt,name=update_by,json=updateBy,proto3" json:"update_by,omitempty"`
	// 是否重复 (1-true 2-false)
	IsRecurring *int32 `protobuf:"varint,23,opt,name=is_recurring,json=isRecurring,proto3,oneof" json:"is_recurring,omitempty"`
	// 分享预约状态 (0-all 1-unconfirm 2-confirm 4-finished)
	ShareApptStatus int32 `protobuf:"varint,24,opt,name=share_appt_status,json=shareApptStatus,proto3" json:"share_appt_status,omitempty"`
	// 分享范围类型 (0-all 1-in x days 2-next x appointment 3-manually apptids)
	ShareRangeType int32 `protobuf:"varint,25,opt,name=share_range_type,json=shareRangeType,proto3" json:"share_range_type,omitempty"`
	// 分享范围值
	ShareRangeValue int32 `protobuf:"varint,26,opt,name=share_range_value,json=shareRangeValue,proto3" json:"share_range_value,omitempty"`
	// 分享预约JSON
	ShareApptJson *string `protobuf:"bytes,27,opt,name=share_appt_json,json=shareApptJson,proto3,oneof" json:"share_appt_json,omitempty"`
	// 首选天 (JSON数组格式)
	PreferredDay string `protobuf:"bytes,28,opt,name=preferred_day,json=preferredDay,proto3" json:"preferred_day,omitempty"`
	// 首选时间 (JSON数组格式)
	// (-- api-linter: core::0142::time-field-type=disabled
	//
	//	aip.dev/not-precedent: this is json string not a time --)
	PreferredTime string `protobuf:"bytes,29,opt,name=preferred_time,json=preferredTime,proto3" json:"preferred_time,omitempty"`
	// 账户ID
	AccountId int64 `protobuf:"varint,30,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// 客户编码
	CustomerCode string `protobuf:"bytes,31,opt,name=customer_code,json=customerCode,proto3" json:"customer_code,omitempty"`
	// 是否退订
	IsUnsubscribed bool `protobuf:"varint,32,opt,name=is_unsubscribed,json=isUnsubscribed,proto3" json:"is_unsubscribed,omitempty"`
	// 生日
	// (-- api-linter: core::0142::time-field-names=disabled
	//
	//	aip.dev/not-precedent: this is not a time field --)
	Birthday *timestamppb.Timestamp `protobuf:"bytes,33,opt,name=birthday,proto3,oneof" json:"birthday,omitempty"`
	// 行动状态
	ActionState string `protobuf:"bytes,34,opt,name=action_state,json=actionState,proto3" json:"action_state,omitempty"`
	// 自定义生命周期ID
	CustomizeLifeCycleId int64 `protobuf:"varint,35,opt,name=customize_life_cycle_id,json=customizeLifeCycleId,proto3" json:"customize_life_cycle_id,omitempty"`
	// 自定义行动状态ID
	CustomizeActionStateId int64 `protobuf:"varint,36,opt,name=customize_action_state_id,json=customizeActionStateId,proto3" json:"customize_action_state_id,omitempty"`
	// state information
	State CustomerRelatedData_State `protobuf:"varint,37,opt,name=state,proto3,enum=backend.proto.customer.v2.CustomerRelatedData_State" json:"state,omitempty"`
	// preferred tip configuration
	// 首选小费是否启用 (0-关闭 1-开启)
	PreferredTipEnable int32 `protobuf:"varint,38,opt,name=preferred_tip_enable,json=preferredTipEnable,proto3" json:"preferred_tip_enable,omitempty"`
	// 首选小费类型 (0-按金额 1-按百分比)
	PreferredTipType int32 `protobuf:"varint,39,opt,name=preferred_tip_type,json=preferredTipType,proto3" json:"preferred_tip_type,omitempty"`
	// 按金额小费
	PreferredTipAmount float64 `protobuf:"fixed64,40,opt,name=preferred_tip_amount,json=preferredTipAmount,proto3" json:"preferred_tip_amount,omitempty"`
	// 按百分比小费 [1,100]
	PreferredTipPercentage int32 `protobuf:"varint,41,opt,name=preferred_tip_percentage,json=preferredTipPercentage,proto3" json:"preferred_tip_percentage,omitempty"`
	// default preferred frequency configuration
	// 默认首选频率类型 (1-grooming)
	DefaultPreferredFrequencyType int32 `protobuf:"varint,42,opt,name=default_preferred_frequency_type,json=defaultPreferredFrequencyType,proto3" json:"default_preferred_frequency_type,omitempty"`
	// 默认首选频率周期 (1-day, 2-week, 4-month)
	DefaultPreferredCalendarPeriod int32 `protobuf:"varint,43,opt,name=default_preferred_calendar_period,json=defaultPreferredCalendarPeriod,proto3" json:"default_preferred_calendar_period,omitempty"`
	// 默认首选频率值
	DefaultPreferredFrequencyValue int32 `protobuf:"varint,44,opt,name=default_preferred_frequency_value,json=defaultPreferredFrequencyValue,proto3" json:"default_preferred_frequency_value,omitempty"`
	// audit information
	// 删除时间
	DeleteTime *timestamppb.Timestamp `protobuf:"bytes,45,opt,name=delete_time,json=deleteTime,proto3,oneof" json:"delete_time,omitempty"`
	// 创建时间
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,46,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间
	UpdateTime    *timestamppb.Timestamp `protobuf:"bytes,47,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerRelatedData) Reset() {
	*x = CustomerRelatedData{}
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerRelatedData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerRelatedData) ProtoMessage() {}

func (x *CustomerRelatedData) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerRelatedData.ProtoReflect.Descriptor instead.
func (*CustomerRelatedData) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{1}
}

func (x *CustomerRelatedData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CustomerRelatedData) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CustomerRelatedData) GetPreferredBusinessId() int64 {
	if x != nil {
		return x.PreferredBusinessId
	}
	return 0
}

func (x *CustomerRelatedData) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CustomerRelatedData) GetClientColor() string {
	if x != nil {
		return x.ClientColor
	}
	return ""
}

func (x *CustomerRelatedData) GetIsBlockMessage() int32 {
	if x != nil {
		return x.IsBlockMessage
	}
	return 0
}

func (x *CustomerRelatedData) GetIsBlockOnlineBooking() int32 {
	if x != nil {
		return x.IsBlockOnlineBooking
	}
	return 0
}

func (x *CustomerRelatedData) GetLoginEmail() string {
	if x != nil {
		return x.LoginEmail
	}
	return ""
}

func (x *CustomerRelatedData) GetReferralSourceId() int32 {
	if x != nil {
		return x.ReferralSourceId
	}
	return 0
}

func (x *CustomerRelatedData) GetReferralSourceDesc() string {
	if x != nil {
		return x.ReferralSourceDesc
	}
	return ""
}

func (x *CustomerRelatedData) GetSendAutoEmail() int32 {
	if x != nil {
		return x.SendAutoEmail
	}
	return 0
}

func (x *CustomerRelatedData) GetSendAutoMessage() int32 {
	if x != nil {
		return x.SendAutoMessage
	}
	return 0
}

func (x *CustomerRelatedData) GetSendAppAutoMessage() int32 {
	if x != nil {
		return x.SendAppAutoMessage
	}
	return 0
}

func (x *CustomerRelatedData) GetUnconfirmedReminderBy() []int32 {
	if x != nil {
		return x.UnconfirmedReminderBy
	}
	return nil
}

func (x *CustomerRelatedData) GetPreferredGroomerId() int32 {
	if x != nil {
		return x.PreferredGroomerId
	}
	return 0
}

func (x *CustomerRelatedData) GetPreferredFrequencyDay() int32 {
	if x != nil {
		return x.PreferredFrequencyDay
	}
	return 0
}

func (x *CustomerRelatedData) GetPreferredFrequencyType() int32 {
	if x != nil {
		return x.PreferredFrequencyType
	}
	return 0
}

func (x *CustomerRelatedData) GetLastServiceTime() string {
	if x != nil {
		return x.LastServiceTime
	}
	return ""
}

func (x *CustomerRelatedData) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *CustomerRelatedData) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

func (x *CustomerRelatedData) GetCreateBy() int32 {
	if x != nil {
		return x.CreateBy
	}
	return 0
}

func (x *CustomerRelatedData) GetUpdateBy() int32 {
	if x != nil {
		return x.UpdateBy
	}
	return 0
}

func (x *CustomerRelatedData) GetIsRecurring() int32 {
	if x != nil && x.IsRecurring != nil {
		return *x.IsRecurring
	}
	return 0
}

func (x *CustomerRelatedData) GetShareApptStatus() int32 {
	if x != nil {
		return x.ShareApptStatus
	}
	return 0
}

func (x *CustomerRelatedData) GetShareRangeType() int32 {
	if x != nil {
		return x.ShareRangeType
	}
	return 0
}

func (x *CustomerRelatedData) GetShareRangeValue() int32 {
	if x != nil {
		return x.ShareRangeValue
	}
	return 0
}

func (x *CustomerRelatedData) GetShareApptJson() string {
	if x != nil && x.ShareApptJson != nil {
		return *x.ShareApptJson
	}
	return ""
}

func (x *CustomerRelatedData) GetPreferredDay() string {
	if x != nil {
		return x.PreferredDay
	}
	return ""
}

func (x *CustomerRelatedData) GetPreferredTime() string {
	if x != nil {
		return x.PreferredTime
	}
	return ""
}

func (x *CustomerRelatedData) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *CustomerRelatedData) GetCustomerCode() string {
	if x != nil {
		return x.CustomerCode
	}
	return ""
}

func (x *CustomerRelatedData) GetIsUnsubscribed() bool {
	if x != nil {
		return x.IsUnsubscribed
	}
	return false
}

func (x *CustomerRelatedData) GetBirthday() *timestamppb.Timestamp {
	if x != nil {
		return x.Birthday
	}
	return nil
}

func (x *CustomerRelatedData) GetActionState() string {
	if x != nil {
		return x.ActionState
	}
	return ""
}

func (x *CustomerRelatedData) GetCustomizeLifeCycleId() int64 {
	if x != nil {
		return x.CustomizeLifeCycleId
	}
	return 0
}

func (x *CustomerRelatedData) GetCustomizeActionStateId() int64 {
	if x != nil {
		return x.CustomizeActionStateId
	}
	return 0
}

func (x *CustomerRelatedData) GetState() CustomerRelatedData_State {
	if x != nil {
		return x.State
	}
	return CustomerRelatedData_STATE_UNSPECIFIED
}

func (x *CustomerRelatedData) GetPreferredTipEnable() int32 {
	if x != nil {
		return x.PreferredTipEnable
	}
	return 0
}

func (x *CustomerRelatedData) GetPreferredTipType() int32 {
	if x != nil {
		return x.PreferredTipType
	}
	return 0
}

func (x *CustomerRelatedData) GetPreferredTipAmount() float64 {
	if x != nil {
		return x.PreferredTipAmount
	}
	return 0
}

func (x *CustomerRelatedData) GetPreferredTipPercentage() int32 {
	if x != nil {
		return x.PreferredTipPercentage
	}
	return 0
}

func (x *CustomerRelatedData) GetDefaultPreferredFrequencyType() int32 {
	if x != nil {
		return x.DefaultPreferredFrequencyType
	}
	return 0
}

func (x *CustomerRelatedData) GetDefaultPreferredCalendarPeriod() int32 {
	if x != nil {
		return x.DefaultPreferredCalendarPeriod
	}
	return 0
}

func (x *CustomerRelatedData) GetDefaultPreferredFrequencyValue() int32 {
	if x != nil {
		return x.DefaultPreferredFrequencyValue
	}
	return 0
}

func (x *CustomerRelatedData) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

func (x *CustomerRelatedData) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *CustomerRelatedData) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// lead
type Lead struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// lead ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// organization reference
	Organization *OrganizationRef `protobuf:"bytes,2,opt,name=organization,proto3" json:"organization,omitempty"`
	// core identifier information
	// 名 (最大长度255字符)
	GivenName string `protobuf:"bytes,3,opt,name=given_name,json=givenName,proto3" json:"given_name,omitempty"`
	// 姓 (最大长度255字符)
	FamilyName string `protobuf:"bytes,4,opt,name=family_name,json=familyName,proto3" json:"family_name,omitempty"`
	// 实体状态（活跃/非活跃/删除）
	State Lead_State `protobuf:"varint,11,opt,name=state,proto3,enum=backend.proto.customer.v2.Lead_State" json:"state,omitempty"`
	// lifecycle
	LifecycleId int64 `protobuf:"varint,12,opt,name=lifecycle_id,json=lifecycleId,proto3" json:"lifecycle_id,omitempty"`
	// relationship
	// 负责人 id (staff id)
	OwnerStaffId int64 `protobuf:"varint,13,opt,name=owner_staff_id,json=ownerStaffId,proto3" json:"owner_staff_id,omitempty"`
	// extended fields
	// 自定义字段
	CustomFields *structpb.Struct `protobuf:"bytes,14,opt,name=custom_fields,json=customFields,proto3" json:"custom_fields,omitempty"`
	// audit information
	// 创建时间
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// 删除时间
	DeleteTime *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=delete_time,json=deleteTime,proto3" json:"delete_time,omitempty"`
	// additional fields
	// action state id
	ActionStateId int64 `protobuf:"varint,18,opt,name=action_state_id,json=actionStateId,proto3" json:"action_state_id,omitempty"`
	// avatar path
	AvatarPath string `protobuf:"bytes,19,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// customer code
	CustomerCode string `protobuf:"bytes,20,opt,name=customer_code,json=customerCode,proto3" json:"customer_code,omitempty"`
	// referral source id
	ReferralSourceId int64 `protobuf:"varint,21,opt,name=referral_source_id,json=referralSourceId,proto3" json:"referral_source_id,omitempty"`
	// convert to customer id
	ConvertedCustomerId *int64 `protobuf:"varint,22,opt,name=converted_customer_id,json=convertedCustomerId,proto3,oneof" json:"converted_customer_id,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *Lead) Reset() {
	*x = Lead{}
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Lead) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Lead) ProtoMessage() {}

func (x *Lead) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Lead.ProtoReflect.Descriptor instead.
func (*Lead) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{2}
}

func (x *Lead) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Lead) GetOrganization() *OrganizationRef {
	if x != nil {
		return x.Organization
	}
	return nil
}

func (x *Lead) GetGivenName() string {
	if x != nil {
		return x.GivenName
	}
	return ""
}

func (x *Lead) GetFamilyName() string {
	if x != nil {
		return x.FamilyName
	}
	return ""
}

func (x *Lead) GetState() Lead_State {
	if x != nil {
		return x.State
	}
	return Lead_STATE_UNSPECIFIED
}

func (x *Lead) GetLifecycleId() int64 {
	if x != nil {
		return x.LifecycleId
	}
	return 0
}

func (x *Lead) GetOwnerStaffId() int64 {
	if x != nil {
		return x.OwnerStaffId
	}
	return 0
}

func (x *Lead) GetCustomFields() *structpb.Struct {
	if x != nil {
		return x.CustomFields
	}
	return nil
}

func (x *Lead) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Lead) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Lead) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

func (x *Lead) GetActionStateId() int64 {
	if x != nil {
		return x.ActionStateId
	}
	return 0
}

func (x *Lead) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *Lead) GetCustomerCode() string {
	if x != nil {
		return x.CustomerCode
	}
	return ""
}

func (x *Lead) GetReferralSourceId() int64 {
	if x != nil {
		return x.ReferralSourceId
	}
	return 0
}

func (x *Lead) GetConvertedCustomerId() int64 {
	if x != nil && x.ConvertedCustomerId != nil {
		return *x.ConvertedCustomerId
	}
	return 0
}

// contact
type Contact struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 联系人ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// basic information
	// 名字
	GivenName string `protobuf:"bytes,3,opt,name=given_name,json=givenName,proto3" json:"given_name,omitempty"`
	// 姓
	FamilyName string `protobuf:"bytes,4,opt,name=family_name,json=familyName,proto3" json:"family_name,omitempty"`
	// 邮箱地址 (必须是有效的邮箱格式)
	Email string `protobuf:"bytes,5,opt,name=email,proto3" json:"email,omitempty"`
	// 电话号码
	Phone *phone_number.PhoneNumber `protobuf:"bytes,6,opt,name=phone,proto3" json:"phone,omitempty"`
	// 是否是自己创建的
	IsSelf bool `protobuf:"varint,7,opt,name=is_self,json=isSelf,proto3" json:"is_self,omitempty"`
	// state information
	State Contact_State `protobuf:"varint,10,opt,name=state,proto3,enum=backend.proto.customer.v2.Contact_State" json:"state,omitempty"`
	// 关联的标签列表
	// 1.如果创建时没有指定, 就是默认空列表
	// 2. 如果创建时仅指定id, 会根据id查询标签列表, 并做关联
	// 3. 如果创建时传完整Tag, 会以传入的Tag创建新Tag, 并做关联
	// 注: 如果创建时传id和完整Tag, 会以id查询内容为准
	// --
	// 所以, 如果创建Contact时绑定已有标签, 这里应该只传id
	// 如果创建Contact时还需要同时创建新标签, 这里应该传完整Tag
	Tags []*ContactTag `protobuf:"bytes,11,rep,name=tags,proto3" json:"tags,omitempty"`
	// 备注
	Note string `protobuf:"bytes,12,opt,name=note,proto3" json:"note,omitempty"`
	// audit information
	// 创建时间
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// 删除时间
	DeleteTime    *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=delete_time,json=deleteTime,proto3" json:"delete_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Contact) Reset() {
	*x = Contact{}
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Contact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Contact) ProtoMessage() {}

func (x *Contact) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Contact.ProtoReflect.Descriptor instead.
func (*Contact) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{3}
}

func (x *Contact) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Contact) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *Contact) GetGivenName() string {
	if x != nil {
		return x.GivenName
	}
	return ""
}

func (x *Contact) GetFamilyName() string {
	if x != nil {
		return x.FamilyName
	}
	return ""
}

func (x *Contact) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *Contact) GetPhone() *phone_number.PhoneNumber {
	if x != nil {
		return x.Phone
	}
	return nil
}

func (x *Contact) GetIsSelf() bool {
	if x != nil {
		return x.IsSelf
	}
	return false
}

func (x *Contact) GetState() Contact_State {
	if x != nil {
		return x.State
	}
	return Contact_STATE_UNSPECIFIED
}

func (x *Contact) GetTags() []*ContactTag {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Contact) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

func (x *Contact) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Contact) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Contact) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// contact tag
type ContactTag struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 标签ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// organization reference
	Organization *OrganizationRef `protobuf:"bytes,2,opt,name=organization,proto3" json:"organization,omitempty"`
	// 标签名称 (最大长度100字符)
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 标签描述 (最大长度500字符)
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// 标签颜色 (十六进制颜色代码，如 #FF5733)
	Color string `protobuf:"bytes,5,opt,name=color,proto3" json:"color,omitempty"`
	// 排序顺序
	SortOrder int32 `protobuf:"varint,6,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty"`
	// 实体状态（活跃/非活跃/删除）
	State ContactTag_State `protobuf:"varint,7,opt,name=state,proto3,enum=backend.proto.customer.v2.ContactTag_State" json:"state,omitempty"`
	// 标签类型
	Type ContactTag_Type `protobuf:"varint,8,opt,name=type,proto3,enum=backend.proto.customer.v2.ContactTag_Type" json:"type,omitempty"`
	// audit information
	// 创建时间
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// 删除时间
	DeleteTime    *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=delete_time,json=deleteTime,proto3" json:"delete_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ContactTag) Reset() {
	*x = ContactTag{}
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContactTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContactTag) ProtoMessage() {}

func (x *ContactTag) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContactTag.ProtoReflect.Descriptor instead.
func (*ContactTag) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{4}
}

func (x *ContactTag) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ContactTag) GetOrganization() *OrganizationRef {
	if x != nil {
		return x.Organization
	}
	return nil
}

func (x *ContactTag) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ContactTag) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ContactTag) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *ContactTag) GetSortOrder() int32 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

func (x *ContactTag) GetState() ContactTag_State {
	if x != nil {
		return x.State
	}
	return ContactTag_STATE_UNSPECIFIED
}

func (x *ContactTag) GetType() ContactTag_Type {
	if x != nil {
		return x.Type
	}
	return ContactTag_TYPE_UNSPECIFIED
}

func (x *ContactTag) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *ContactTag) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *ContactTag) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// address
type Address struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 地址ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// address information
	// 完整地址信息
	// 解释字段:
	// "postal_address": {
	// "revision": 1, // 版本号, 一般用不上
	// "regionCode": "US", // 国家代码, 必填
	// "languageCode": "en-US", // 语言代码
	// "postalCode": "94043", // 邮编
	// "administrativeArea": "CA", // 省
	// "locality": "Mountain View", // 城市
	// "addressLines": [ // 地址行
	//
	//	"1600 Amphitheatre Parkway",
	//	"Building 43, Floor 2"
	//
	// ],
	// "recipients": ["John Smith"], // 收件人
	// "organization": "Google LLC" // 组织
	// }
	Address *postaladdress.PostalAddress `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	// geographic coordinates
	// 纬度
	// lat 纬度
	// lng 经度
	Latlng *latlng.LatLng `protobuf:"bytes,5,opt,name=latlng,proto3" json:"latlng,omitempty"`
	// 地址类型
	Type Address_Type `protobuf:"varint,6,opt,name=type,proto3,enum=backend.proto.customer.v2.Address_Type" json:"type,omitempty"`
	// state information
	State Address_State `protobuf:"varint,18,opt,name=state,proto3,enum=backend.proto.customer.v2.Address_State" json:"state,omitempty"`
	// organization reference
	Organization *OrganizationRef `protobuf:"bytes,19,opt,name=organization,proto3" json:"organization,omitempty"`
	// audit information
	// 创建时间
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// 删除时间
	DeleteTime    *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=delete_time,json=deleteTime,proto3" json:"delete_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Address) Reset() {
	*x = Address{}
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Address) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Address) ProtoMessage() {}

func (x *Address) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Address.ProtoReflect.Descriptor instead.
func (*Address) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{5}
}

func (x *Address) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Address) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *Address) GetAddress() *postaladdress.PostalAddress {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *Address) GetLatlng() *latlng.LatLng {
	if x != nil {
		return x.Latlng
	}
	return nil
}

func (x *Address) GetType() Address_Type {
	if x != nil {
		return x.Type
	}
	return Address_TYPE_UNSPECIFIED
}

func (x *Address) GetState() Address_State {
	if x != nil {
		return x.State
	}
	return Address_STATE_UNSPECIFIED
}

func (x *Address) GetOrganization() *OrganizationRef {
	if x != nil {
		return x.Organization
	}
	return nil
}

func (x *Address) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Address) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Address) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// custom field definition
type CustomField struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 自定义字段定义ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// field basic information
	// 主体
	Organization *OrganizationRef `protobuf:"bytes,2,opt,name=organization,proto3" json:"organization,omitempty"`
	// 实体类型
	AssociationType CustomField_AssociationType `protobuf:"varint,3,opt,name=association_type,json=associationType,proto3,enum=backend.proto.customer.v2.CustomField_AssociationType" json:"association_type,omitempty"`
	// 字段标识, 唯一标识的机器码, 由系统生成
	Code string `protobuf:"bytes,4,opt,name=code,proto3" json:"code,omitempty"`
	// 字段标签
	Label string `protobuf:"bytes,5,opt,name=label,proto3" json:"label,omitempty"`
	// 字段类型
	Type CustomField_Type `protobuf:"varint,6,opt,name=type,proto3,enum=backend.proto.customer.v2.CustomField_Type" json:"type,omitempty"`
	// field configuration
	// 是否必填
	IsRequired bool `protobuf:"varint,10,opt,name=is_required,json=isRequired,proto3" json:"is_required,omitempty"`
	// 实体状态（活跃/删除）
	State CustomField_State `protobuf:"varint,11,opt,name=state,proto3,enum=backend.proto.customer.v2.CustomField_State" json:"state,omitempty"`
	// 默认值
	DefaultValue *CustomField_Value `protobuf:"bytes,12,opt,name=default_value,json=defaultValue,proto3" json:"default_value,omitempty"`
	// option configuration
	// 字段选项列表
	Options []*CustomField_Option `protobuf:"bytes,15,rep,name=options,proto3" json:"options,omitempty"`
	// validation rules
	// 验证规则
	ValidationRules *structpb.Struct `protobuf:"bytes,18,opt,name=validation_rules,json=validationRules,proto3" json:"validation_rules,omitempty"`
	// source
	Source CustomField_Source `protobuf:"varint,19,opt,name=source,proto3,enum=backend.proto.customer.v2.CustomField_Source" json:"source,omitempty"`
	// display configuration
	// 显示顺序
	DisplayOrder int32 `protobuf:"varint,21,opt,name=display_order,json=displayOrder,proto3" json:"display_order,omitempty"`
	// 帮助文本
	HelpText string `protobuf:"bytes,22,opt,name=help_text,json=helpText,proto3" json:"help_text,omitempty"`
	// audit information
	// 创建时间
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,25,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,26,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// 删除时间
	DeleteTime    *timestamppb.Timestamp `protobuf:"bytes,27,opt,name=delete_time,json=deleteTime,proto3" json:"delete_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomField) Reset() {
	*x = CustomField{}
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomField) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomField) ProtoMessage() {}

func (x *CustomField) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomField.ProtoReflect.Descriptor instead.
func (*CustomField) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{6}
}

func (x *CustomField) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CustomField) GetOrganization() *OrganizationRef {
	if x != nil {
		return x.Organization
	}
	return nil
}

func (x *CustomField) GetAssociationType() CustomField_AssociationType {
	if x != nil {
		return x.AssociationType
	}
	return CustomField_ASSOCIATION_TYPE_UNSPECIFIED
}

func (x *CustomField) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *CustomField) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *CustomField) GetType() CustomField_Type {
	if x != nil {
		return x.Type
	}
	return CustomField_TYPE_UNSPECIFIED
}

func (x *CustomField) GetIsRequired() bool {
	if x != nil {
		return x.IsRequired
	}
	return false
}

func (x *CustomField) GetState() CustomField_State {
	if x != nil {
		return x.State
	}
	return CustomField_STATE_UNSPECIFIED
}

func (x *CustomField) GetDefaultValue() *CustomField_Value {
	if x != nil {
		return x.DefaultValue
	}
	return nil
}

func (x *CustomField) GetOptions() []*CustomField_Option {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *CustomField) GetValidationRules() *structpb.Struct {
	if x != nil {
		return x.ValidationRules
	}
	return nil
}

func (x *CustomField) GetSource() CustomField_Source {
	if x != nil {
		return x.Source
	}
	return CustomField_SOURCE_UNSPECIFIED
}

func (x *CustomField) GetDisplayOrder() int32 {
	if x != nil {
		return x.DisplayOrder
	}
	return 0
}

func (x *CustomField) GetHelpText() string {
	if x != nil {
		return x.HelpText
	}
	return ""
}

func (x *CustomField) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *CustomField) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *CustomField) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// CustomerAggregate 客户聚合资源 - 包含客户及所有相关实体
type CustomerAggregate struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 创建的客户信息
	Customer *Customer `protobuf:"bytes,1,opt,name=customer,proto3" json:"customer,omitempty"`
	// 创建的地址列表
	Addresses []*Address `protobuf:"bytes,2,rep,name=addresses,proto3" json:"addresses,omitempty"`
	// 创建的联系人列表
	Contacts []*Contact `protobuf:"bytes,3,rep,name=contacts,proto3" json:"contacts,omitempty"`
	// 创建的客户相关数据
	RelatedData   *CustomerRelatedData `protobuf:"bytes,4,opt,name=related_data,json=relatedData,proto3,oneof" json:"related_data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerAggregate) Reset() {
	*x = CustomerAggregate{}
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerAggregate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerAggregate) ProtoMessage() {}

func (x *CustomerAggregate) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerAggregate.ProtoReflect.Descriptor instead.
func (*CustomerAggregate) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{7}
}

func (x *CustomerAggregate) GetCustomer() *Customer {
	if x != nil {
		return x.Customer
	}
	return nil
}

func (x *CustomerAggregate) GetAddresses() []*Address {
	if x != nil {
		return x.Addresses
	}
	return nil
}

func (x *CustomerAggregate) GetContacts() []*Contact {
	if x != nil {
		return x.Contacts
	}
	return nil
}

func (x *CustomerAggregate) GetRelatedData() *CustomerRelatedData {
	if x != nil {
		return x.RelatedData
	}
	return nil
}

// Unified metadata for both customer and lead
type Metadata struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// metadata ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// organization reference
	Organization *OrganizationRef `protobuf:"bytes,2,opt,name=organization,proto3" json:"organization,omitempty"`
	// customer type to differentiate between customer and lead
	CustomerType CustomerType `protobuf:"varint,3,opt,name=customer_type,json=customerType,proto3,enum=backend.proto.customer.v2.CustomerType" json:"customer_type,omitempty"`
	// core identifier information
	// 名 (最大长度255字符)
	GivenName string `protobuf:"bytes,4,opt,name=given_name,json=givenName,proto3" json:"given_name,omitempty"`
	// 姓 (最大长度255字符)
	FamilyName string `protobuf:"bytes,5,opt,name=family_name,json=familyName,proto3" json:"family_name,omitempty"`
	// 实体状态（活跃/非活跃/删除）
	State Metadata_State `protobuf:"varint,11,opt,name=state,proto3,enum=backend.proto.customer.v2.Metadata_State" json:"state,omitempty"`
	// lifecycle
	LifecycleId int64 `protobuf:"varint,12,opt,name=lifecycle_id,json=lifecycleId,proto3" json:"lifecycle_id,omitempty"`
	// relationship
	// 负责人 id (staff id)
	OwnerStaffId int64 `protobuf:"varint,13,opt,name=owner_staff_id,json=ownerStaffId,proto3" json:"owner_staff_id,omitempty"`
	// extended fields
	// 自定义字段
	CustomFields *structpb.Struct `protobuf:"bytes,14,opt,name=custom_fields,json=customFields,proto3" json:"custom_fields,omitempty"`
	// audit information
	// 创建时间
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// 删除时间
	DeleteTime *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=delete_time,json=deleteTime,proto3" json:"delete_time,omitempty"`
	// additional fields
	// action state id
	ActionStateId int64 `protobuf:"varint,18,opt,name=action_state_id,json=actionStateId,proto3" json:"action_state_id,omitempty"`
	// avatar path
	AvatarPath string `protobuf:"bytes,19,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// customer code
	CustomerCode string `protobuf:"bytes,20,opt,name=customer_code,json=customerCode,proto3" json:"customer_code,omitempty"`
	// referral source id
	ReferralSourceId int64 `protobuf:"varint,21,opt,name=referral_source_id,json=referralSourceId,proto3" json:"referral_source_id,omitempty"`
	// lead-specific field (only used when customer_type = LEAD)
	// convert to customer id
	ConvertedCustomerId *int64 `protobuf:"varint,22,opt,name=converted_customer_id,json=convertedCustomerId,proto3,oneof" json:"converted_customer_id,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *Metadata) Reset() {
	*x = Metadata{}
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Metadata) ProtoMessage() {}

func (x *Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Metadata.ProtoReflect.Descriptor instead.
func (*Metadata) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{8}
}

func (x *Metadata) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Metadata) GetOrganization() *OrganizationRef {
	if x != nil {
		return x.Organization
	}
	return nil
}

func (x *Metadata) GetCustomerType() CustomerType {
	if x != nil {
		return x.CustomerType
	}
	return CustomerType_CUSTOMER_TYPE_UNSPECIFIED
}

func (x *Metadata) GetGivenName() string {
	if x != nil {
		return x.GivenName
	}
	return ""
}

func (x *Metadata) GetFamilyName() string {
	if x != nil {
		return x.FamilyName
	}
	return ""
}

func (x *Metadata) GetState() Metadata_State {
	if x != nil {
		return x.State
	}
	return Metadata_STATE_UNSPECIFIED
}

func (x *Metadata) GetLifecycleId() int64 {
	if x != nil {
		return x.LifecycleId
	}
	return 0
}

func (x *Metadata) GetOwnerStaffId() int64 {
	if x != nil {
		return x.OwnerStaffId
	}
	return 0
}

func (x *Metadata) GetCustomFields() *structpb.Struct {
	if x != nil {
		return x.CustomFields
	}
	return nil
}

func (x *Metadata) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Metadata) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Metadata) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

func (x *Metadata) GetActionStateId() int64 {
	if x != nil {
		return x.ActionStateId
	}
	return 0
}

func (x *Metadata) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *Metadata) GetCustomerCode() string {
	if x != nil {
		return x.CustomerCode
	}
	return ""
}

func (x *Metadata) GetReferralSourceId() int64 {
	if x != nil {
		return x.ReferralSourceId
	}
	return 0
}

func (x *Metadata) GetConvertedCustomerId() int64 {
	if x != nil && x.ConvertedCustomerId != nil {
		return *x.ConvertedCustomerId
	}
	return 0
}

// MetadataAggregate 元数据聚合资源 - 包含元数据及所有相关实体
type MetadataAggregate struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 创建的元数据信息
	Metadata *Metadata `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// 创建的地址列表
	Addresses []*Address `protobuf:"bytes,2,rep,name=addresses,proto3" json:"addresses,omitempty"`
	// 创建的联系人列表
	Contacts []*Contact `protobuf:"bytes,3,rep,name=contacts,proto3" json:"contacts,omitempty"`
	// customer related data
	RelatedData   *CustomerRelatedData `protobuf:"bytes,4,opt,name=related_data,json=relatedData,proto3,oneof" json:"related_data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MetadataAggregate) Reset() {
	*x = MetadataAggregate{}
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MetadataAggregate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetadataAggregate) ProtoMessage() {}

func (x *MetadataAggregate) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetadataAggregate.ProtoReflect.Descriptor instead.
func (*MetadataAggregate) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{9}
}

func (x *MetadataAggregate) GetMetadata() *Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *MetadataAggregate) GetAddresses() []*Address {
	if x != nil {
		return x.Addresses
	}
	return nil
}

func (x *MetadataAggregate) GetContacts() []*Contact {
	if x != nil {
		return x.Contacts
	}
	return nil
}

func (x *MetadataAggregate) GetRelatedData() *CustomerRelatedData {
	if x != nil {
		return x.RelatedData
	}
	return nil
}

// Custom field value, can be of any supported type.
type CustomField_Value struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 实际的值，oneof 只会有一个生效
	//
	// Types that are valid to be assigned to Value:
	//
	//	*CustomField_Value_String_
	//	*CustomField_Value_DoubleValue
	//	*CustomField_Value_Int64
	//	*CustomField_Value_Bool
	//	*CustomField_Value_Money
	//	*CustomField_Value_TimestampTime
	//	*CustomField_Value_Relation_
	//	*CustomField_Value_StringList_
	Value         isCustomField_Value_Value `protobuf_oneof:"value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomField_Value) Reset() {
	*x = CustomField_Value{}
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomField_Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomField_Value) ProtoMessage() {}

func (x *CustomField_Value) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomField_Value.ProtoReflect.Descriptor instead.
func (*CustomField_Value) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{6, 0}
}

func (x *CustomField_Value) GetValue() isCustomField_Value_Value {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *CustomField_Value) GetString_() string {
	if x != nil {
		if x, ok := x.Value.(*CustomField_Value_String_); ok {
			return x.String_
		}
	}
	return ""
}

func (x *CustomField_Value) GetDoubleValue() float64 {
	if x != nil {
		if x, ok := x.Value.(*CustomField_Value_DoubleValue); ok {
			return x.DoubleValue
		}
	}
	return 0
}

func (x *CustomField_Value) GetInt64() int64 {
	if x != nil {
		if x, ok := x.Value.(*CustomField_Value_Int64); ok {
			return x.Int64
		}
	}
	return 0
}

func (x *CustomField_Value) GetBool() bool {
	if x != nil {
		if x, ok := x.Value.(*CustomField_Value_Bool); ok {
			return x.Bool
		}
	}
	return false
}

func (x *CustomField_Value) GetMoney() *money.Money {
	if x != nil {
		if x, ok := x.Value.(*CustomField_Value_Money); ok {
			return x.Money
		}
	}
	return nil
}

func (x *CustomField_Value) GetTimestampTime() *timestamppb.Timestamp {
	if x != nil {
		if x, ok := x.Value.(*CustomField_Value_TimestampTime); ok {
			return x.TimestampTime
		}
	}
	return nil
}

func (x *CustomField_Value) GetRelation() *CustomField_Value_Relation {
	if x != nil {
		if x, ok := x.Value.(*CustomField_Value_Relation_); ok {
			return x.Relation
		}
	}
	return nil
}

func (x *CustomField_Value) GetStringList() *CustomField_Value_StringList {
	if x != nil {
		if x, ok := x.Value.(*CustomField_Value_StringList_); ok {
			return x.StringList
		}
	}
	return nil
}

type isCustomField_Value_Value interface {
	isCustomField_Value_Value()
}

type CustomField_Value_String_ struct {
	// 字符串类型
	String_ string `protobuf:"bytes,2,opt,name=string,proto3,oneof"`
}

type CustomField_Value_DoubleValue struct {
	// 浮点数类型
	DoubleValue float64 `protobuf:"fixed64,3,opt,name=double_value,json=doubleValue,proto3,oneof"`
}

type CustomField_Value_Int64 struct {
	// 整型类型
	Int64 int64 `protobuf:"varint,4,opt,name=int64,proto3,oneof"`
}

type CustomField_Value_Bool struct {
	// 布尔类型
	Bool bool `protobuf:"varint,6,opt,name=bool,proto3,oneof"`
}

type CustomField_Value_Money struct {
	// 金额类型
	Money *money.Money `protobuf:"bytes,7,opt,name=money,proto3,oneof"`
}

type CustomField_Value_TimestampTime struct {
	// 时间戳类型
	TimestampTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=timestamp_time,json=timestampTime,proto3,oneof"`
}

type CustomField_Value_Relation_ struct {
	// 关联关系类型
	Relation *CustomField_Value_Relation `protobuf:"bytes,9,opt,name=relation,proto3,oneof"`
}

type CustomField_Value_StringList_ struct {
	// 字符串列表类型 (用于multi-select默认值)
	StringList *CustomField_Value_StringList `protobuf:"bytes,10,opt,name=string_list,json=stringList,proto3,oneof"`
}

func (*CustomField_Value_String_) isCustomField_Value_Value() {}

func (*CustomField_Value_DoubleValue) isCustomField_Value_Value() {}

func (*CustomField_Value_Int64) isCustomField_Value_Value() {}

func (*CustomField_Value_Bool) isCustomField_Value_Value() {}

func (*CustomField_Value_Money) isCustomField_Value_Value() {}

func (*CustomField_Value_TimestampTime) isCustomField_Value_Value() {}

func (*CustomField_Value_Relation_) isCustomField_Value_Value() {}

func (*CustomField_Value_StringList_) isCustomField_Value_Value() {}

// 字段选项
type CustomField_Option struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 字段值
	Value *CustomField_Value `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	// 选项标签
	Label string `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`
	// 排序顺序
	SortOrder int32 `protobuf:"varint,3,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty"`
	// 实体状态（活跃/删除）
	State         CustomField_State `protobuf:"varint,4,opt,name=state,proto3,enum=backend.proto.customer.v2.CustomField_State" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomField_Option) Reset() {
	*x = CustomField_Option{}
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomField_Option) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomField_Option) ProtoMessage() {}

func (x *CustomField_Option) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomField_Option.ProtoReflect.Descriptor instead.
func (*CustomField_Option) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{6, 1}
}

func (x *CustomField_Option) GetValue() *CustomField_Value {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *CustomField_Option) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *CustomField_Option) GetSortOrder() int32 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

func (x *CustomField_Option) GetState() CustomField_State {
	if x != nil {
		return x.State
	}
	return CustomField_STATE_UNSPECIFIED
}

// Relation to another entity (customer/lead)
type CustomField_Value_Relation struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 关联实体类型
	Entity CustomField_Value_Relation_Entity `protobuf:"varint,1,opt,name=entity,proto3,enum=backend.proto.customer.v2.CustomField_Value_Relation_Entity" json:"entity,omitempty"`
	// 关联实体ID
	Id            int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomField_Value_Relation) Reset() {
	*x = CustomField_Value_Relation{}
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomField_Value_Relation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomField_Value_Relation) ProtoMessage() {}

func (x *CustomField_Value_Relation) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomField_Value_Relation.ProtoReflect.Descriptor instead.
func (*CustomField_Value_Relation) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{6, 0, 0}
}

func (x *CustomField_Value_Relation) GetEntity() CustomField_Value_Relation_Entity {
	if x != nil {
		return x.Entity
	}
	return CustomField_Value_Relation_ENTITY_UNSPECIFIED
}

func (x *CustomField_Value_Relation) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// String list for multi-select values
type CustomField_Value_StringList struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 字符串列表
	Values        []string `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomField_Value_StringList) Reset() {
	*x = CustomField_Value_StringList{}
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomField_Value_StringList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomField_Value_StringList) ProtoMessage() {}

func (x *CustomField_Value_StringList) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_metadata_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomField_Value_StringList.ProtoReflect.Descriptor instead.
func (*CustomField_Value_StringList) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_metadata_proto_rawDescGZIP(), []int{6, 0, 1}
}

func (x *CustomField_Value_StringList) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

var File_backend_proto_customer_v2_metadata_proto protoreflect.FileDescriptor

const file_backend_proto_customer_v2_metadata_proto_rawDesc = "" +
	"\n" +
	"(backend/proto/customer/v2/metadata.proto\x12\x19backend.proto.customer.v2\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17google/type/money.proto\x1a\x1egoogle/type/phone_number.proto\x1a google/type/postal_address.proto\x1a\x18google/type/latlng.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1bbuf/validate/validate.proto\x1a&backend/proto/customer/v2/common.proto\"\xc2\x06\n" +
	"\bCustomer\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12N\n" +
	"\forganization\x18\x02 \x01(\v2*.backend.proto.customer.v2.OrganizationRefR\forganization\x12'\n" +
	"\n" +
	"given_name\x18\x03 \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01R\tgivenName\x12)\n" +
	"\vfamily_name\x18\x04 \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01R\n" +
	"familyName\x12D\n" +
	"\x05state\x18\v \x01(\x0e2).backend.proto.customer.v2.Customer.StateB\x03\xe0A\x03R\x05state\x12!\n" +
	"\flifecycle_id\x18\f \x01(\x03R\vlifecycleId\x12$\n" +
	"\x0eowner_staff_id\x18\r \x01(\x03R\fownerStaffId\x12<\n" +
	"\rcustom_fields\x18\x0e \x01(\v2\x17.google.protobuf.StructR\fcustomFields\x12;\n" +
	"\vcreate_time\x18\x0f \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x10 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12;\n" +
	"\vdelete_time\x18\x11 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"deleteTime\x12&\n" +
	"\x0faction_state_id\x18\x12 \x01(\x03R\ractionStateId\x12)\n" +
	"\vavatar_path\x18\x13 \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01R\n" +
	"avatarPath\x12-\n" +
	"\rcustomer_code\x18\x14 \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01R\fcustomerCode\x125\n" +
	"\x12referral_source_id\x18\x15 \x01(\x03B\a\xbaH\x04\"\x02(\x00R\x10referralSourceId\"E\n" +
	"\x05State\x12\x15\n" +
	"\x11STATE_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06ACTIVE\x10\x01\x12\f\n" +
	"\bINACTIVE\x10\x02\x12\v\n" +
	"\aDELETED\x10\x03\"\xa9\x13\n" +
	"\x13CustomerRelatedData\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vcustomer_id\x18\x02 \x01(\x03R\n" +
	"customerId\x122\n" +
	"\x15preferred_business_id\x18\x03 \x01(\x03R\x13preferredBusinessId\x12\x1d\n" +
	"\n" +
	"company_id\x18\x04 \x01(\x03R\tcompanyId\x12*\n" +
	"\fclient_color\x18\x05 \x01(\tB\a\xbaH\x04r\x02\x182R\vclientColor\x12(\n" +
	"\x10is_block_message\x18\x06 \x01(\x05R\x0eisBlockMessage\x125\n" +
	"\x17is_block_online_booking\x18\a \x01(\x05R\x14isBlockOnlineBooking\x12(\n" +
	"\vlogin_email\x18\b \x01(\tB\a\xbaH\x04r\x02\x182R\n" +
	"loginEmail\x12,\n" +
	"\x12referral_source_id\x18\t \x01(\x05R\x10referralSourceId\x12:\n" +
	"\x14referral_source_desc\x18\n" +
	" \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01R\x12referralSourceDesc\x12&\n" +
	"\x0fsend_auto_email\x18\v \x01(\x05R\rsendAutoEmail\x12*\n" +
	"\x11send_auto_message\x18\f \x01(\x05R\x0fsendAutoMessage\x121\n" +
	"\x15send_app_auto_message\x18\r \x01(\x05R\x12sendAppAutoMessage\x126\n" +
	"\x17unconfirmed_reminder_by\x18\x0e \x03(\x05R\x15unconfirmedReminderBy\x120\n" +
	"\x14preferred_groomer_id\x18\x0f \x01(\x05R\x12preferredGroomerId\x126\n" +
	"\x17preferred_frequency_day\x18\x10 \x01(\x05R\x15preferredFrequencyDay\x128\n" +
	"\x18preferred_frequency_type\x18\x11 \x01(\x05R\x16preferredFrequencyType\x123\n" +
	"\x11last_service_time\x18\x12 \x01(\tB\a\xbaH\x04r\x02\x18\x14R\x0flastServiceTime\x12\x1f\n" +
	"\x06source\x18\x13 \x01(\tB\a\xbaH\x04r\x02\x18\x14R\x06source\x12(\n" +
	"\vexternal_id\x18\x14 \x01(\tB\a\xbaH\x04r\x02\x18\x14R\n" +
	"externalId\x12\x1b\n" +
	"\tcreate_by\x18\x15 \x01(\x05R\bcreateBy\x12\x1b\n" +
	"\tupdate_by\x18\x16 \x01(\x05R\bupdateBy\x12&\n" +
	"\fis_recurring\x18\x17 \x01(\x05H\x00R\visRecurring\x88\x01\x01\x12*\n" +
	"\x11share_appt_status\x18\x18 \x01(\x05R\x0fshareApptStatus\x12(\n" +
	"\x10share_range_type\x18\x19 \x01(\x05R\x0eshareRangeType\x12*\n" +
	"\x11share_range_value\x18\x1a \x01(\x05R\x0fshareRangeValue\x12+\n" +
	"\x0fshare_appt_json\x18\x1b \x01(\tH\x01R\rshareApptJson\x88\x01\x01\x12,\n" +
	"\rpreferred_day\x18\x1c \x01(\tB\a\xbaH\x04r\x02\x182R\fpreferredDay\x12.\n" +
	"\x0epreferred_time\x18\x1d \x01(\tB\a\xbaH\x04r\x02\x182R\rpreferredTime\x12\x1d\n" +
	"\n" +
	"account_id\x18\x1e \x01(\x03R\taccountId\x12,\n" +
	"\rcustomer_code\x18\x1f \x01(\tB\a\xbaH\x04r\x02\x18\bR\fcustomerCode\x12'\n" +
	"\x0fis_unsubscribed\x18  \x01(\bR\x0eisUnsubscribed\x12;\n" +
	"\bbirthday\x18! \x01(\v2\x1a.google.protobuf.TimestampH\x02R\bbirthday\x88\x01\x01\x12+\n" +
	"\faction_state\x18\" \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01R\vactionState\x125\n" +
	"\x17customize_life_cycle_id\x18# \x01(\x03R\x14customizeLifeCycleId\x129\n" +
	"\x19customize_action_state_id\x18$ \x01(\x03R\x16customizeActionStateId\x12O\n" +
	"\x05state\x18% \x01(\x0e24.backend.proto.customer.v2.CustomerRelatedData.StateB\x03\xe0A\x03R\x05state\x120\n" +
	"\x14preferred_tip_enable\x18& \x01(\x05R\x12preferredTipEnable\x12,\n" +
	"\x12preferred_tip_type\x18' \x01(\x05R\x10preferredTipType\x120\n" +
	"\x14preferred_tip_amount\x18( \x01(\x01R\x12preferredTipAmount\x128\n" +
	"\x18preferred_tip_percentage\x18) \x01(\x05R\x16preferredTipPercentage\x12G\n" +
	" default_preferred_frequency_type\x18* \x01(\x05R\x1ddefaultPreferredFrequencyType\x12I\n" +
	"!default_preferred_calendar_period\x18+ \x01(\x05R\x1edefaultPreferredCalendarPeriod\x12I\n" +
	"!default_preferred_frequency_value\x18, \x01(\x05R\x1edefaultPreferredFrequencyValue\x12@\n" +
	"\vdelete_time\x18- \x01(\v2\x1a.google.protobuf.TimestampH\x03R\n" +
	"deleteTime\x88\x01\x01\x12@\n" +
	"\vcreate_time\x18. \x01(\v2\x1a.google.protobuf.TimestampB\x03\xe0A\x03R\n" +
	"createTime\x12@\n" +
	"\vupdate_time\x18/ \x01(\v2\x1a.google.protobuf.TimestampB\x03\xe0A\x03R\n" +
	"updateTime\"E\n" +
	"\x05State\x12\x15\n" +
	"\x11STATE_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06ACTIVE\x10\x01\x12\f\n" +
	"\bINACTIVE\x10\x02\x12\v\n" +
	"\aDELETED\x10\x03B\x0f\n" +
	"\r_is_recurringB\x12\n" +
	"\x10_share_appt_jsonB\v\n" +
	"\t_birthdayB\x0e\n" +
	"\f_delete_time\"\x8d\a\n" +
	"\x04Lead\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12N\n" +
	"\forganization\x18\x02 \x01(\v2*.backend.proto.customer.v2.OrganizationRefR\forganization\x12'\n" +
	"\n" +
	"given_name\x18\x03 \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01R\tgivenName\x12)\n" +
	"\vfamily_name\x18\x04 \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01R\n" +
	"familyName\x12@\n" +
	"\x05state\x18\v \x01(\x0e2%.backend.proto.customer.v2.Lead.StateB\x03\xe0A\x03R\x05state\x12!\n" +
	"\flifecycle_id\x18\f \x01(\x03R\vlifecycleId\x12$\n" +
	"\x0eowner_staff_id\x18\r \x01(\x03R\fownerStaffId\x12<\n" +
	"\rcustom_fields\x18\x0e \x01(\v2\x17.google.protobuf.StructR\fcustomFields\x12;\n" +
	"\vcreate_time\x18\x0f \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x10 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12;\n" +
	"\vdelete_time\x18\x11 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"deleteTime\x12&\n" +
	"\x0faction_state_id\x18\x12 \x01(\x03R\ractionStateId\x12)\n" +
	"\vavatar_path\x18\x13 \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01R\n" +
	"avatarPath\x12-\n" +
	"\rcustomer_code\x18\x14 \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01R\fcustomerCode\x125\n" +
	"\x12referral_source_id\x18\x15 \x01(\x03B\a\xbaH\x04\"\x02(\x00R\x10referralSourceId\x127\n" +
	"\x15converted_customer_id\x18\x16 \x01(\x03H\x00R\x13convertedCustomerId\x88\x01\x01\"E\n" +
	"\x05State\x12\x15\n" +
	"\x11STATE_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06ACTIVE\x10\x01\x12\f\n" +
	"\bINACTIVE\x10\x02\x12\v\n" +
	"\aDELETED\x10\x03B\x18\n" +
	"\x16_converted_customer_id\"\xff\x04\n" +
	"\aContact\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vcustomer_id\x18\x02 \x01(\x03R\n" +
	"customerId\x12'\n" +
	"\n" +
	"given_name\x18\x03 \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01R\tgivenName\x12)\n" +
	"\vfamily_name\x18\x04 \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01R\n" +
	"familyName\x12\x14\n" +
	"\x05email\x18\x05 \x01(\tR\x05email\x12.\n" +
	"\x05phone\x18\x06 \x01(\v2\x18.google.type.PhoneNumberR\x05phone\x12\x17\n" +
	"\ais_self\x18\a \x01(\bR\x06isSelf\x12C\n" +
	"\x05state\x18\n" +
	" \x01(\x0e2(.backend.proto.customer.v2.Contact.StateB\x03\xe0A\x03R\x05state\x129\n" +
	"\x04tags\x18\v \x03(\v2%.backend.proto.customer.v2.ContactTagR\x04tags\x12\x12\n" +
	"\x04note\x18\f \x01(\tR\x04note\x12;\n" +
	"\vcreate_time\x18\x0f \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x10 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12;\n" +
	"\vdelete_time\x18\x11 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"deleteTime\"E\n" +
	"\x05State\x12\x15\n" +
	"\x11STATE_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06ACTIVE\x10\x01\x12\f\n" +
	"\bINACTIVE\x10\x02\x12\v\n" +
	"\aDELETED\x10\x03\"\xde\x05\n" +
	"\n" +
	"ContactTag\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12N\n" +
	"\forganization\x18\x02 \x01(\v2*.backend.proto.customer.v2.OrganizationRefR\forganization\x12\x1b\n" +
	"\x04name\x18\x03 \x01(\tB\a\xbaH\x04r\x02\x18dR\x04name\x12*\n" +
	"\vdescription\x18\x04 \x01(\tB\b\xbaH\x05r\x03\x18\xf4\x03R\vdescription\x12\x1d\n" +
	"\x05color\x18\x05 \x01(\tB\a\xbaH\x04r\x02\x18\aR\x05color\x12\x1d\n" +
	"\n" +
	"sort_order\x18\x06 \x01(\x05R\tsortOrder\x12F\n" +
	"\x05state\x18\a \x01(\x0e2+.backend.proto.customer.v2.ContactTag.StateB\x03\xe0A\x03R\x05state\x12>\n" +
	"\x04type\x18\b \x01(\x0e2*.backend.proto.customer.v2.ContactTag.TypeR\x04type\x12;\n" +
	"\vcreate_time\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\v \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12;\n" +
	"\vdelete_time\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"deleteTime\"E\n" +
	"\x05State\x12\x15\n" +
	"\x11STATE_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06ACTIVE\x10\x01\x12\f\n" +
	"\bINACTIVE\x10\x02\x12\v\n" +
	"\aDELETED\x10\x03\"c\n" +
	"\x04Type\x12\x14\n" +
	"\x10TYPE_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06CUSTOM\x10\x01\x12\r\n" +
	"\tEMERGENCY\x10\x02\x12\n" +
	"\n" +
	"\x06PICKUP\x10\x03\x12\x11\n" +
	"\rCOMMUNICATION\x10\x04\x12\v\n" +
	"\aPRIMARY\x10\x05\"\xa8\x05\n" +
	"\aAddress\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vcustomer_id\x18\x02 \x01(\x03R\n" +
	"customerId\x124\n" +
	"\aaddress\x18\x04 \x01(\v2\x1a.google.type.PostalAddressR\aaddress\x12+\n" +
	"\x06latlng\x18\x05 \x01(\v2\x13.google.type.LatLngR\x06latlng\x12;\n" +
	"\x04type\x18\x06 \x01(\x0e2'.backend.proto.customer.v2.Address.TypeR\x04type\x12C\n" +
	"\x05state\x18\x12 \x01(\x0e2(.backend.proto.customer.v2.Address.StateB\x03\xe0A\x03R\x05state\x12N\n" +
	"\forganization\x18\x13 \x01(\v2*.backend.proto.customer.v2.OrganizationRefR\forganization\x12;\n" +
	"\vcreate_time\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12;\n" +
	"\vdelete_time\x18\v \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"deleteTime\"E\n" +
	"\x05State\x12\x15\n" +
	"\x11STATE_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06ACTIVE\x10\x01\x12\f\n" +
	"\bINACTIVE\x10\x02\x12\v\n" +
	"\aDELETED\x10\x03\"9\n" +
	"\x04Type\x12\x14\n" +
	"\x10TYPE_UNSPECIFIED\x10\x00\x12\v\n" +
	"\aPRIMARY\x10\x01\x12\x0e\n" +
	"\n" +
	"ADDITIONAL\x10\x02\"\x85\x11\n" +
	"\vCustomField\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12N\n" +
	"\forganization\x18\x02 \x01(\v2*.backend.proto.customer.v2.OrganizationRefR\forganization\x12a\n" +
	"\x10association_type\x18\x03 \x01(\x0e26.backend.proto.customer.v2.CustomField.AssociationTypeR\x0fassociationType\x12\x12\n" +
	"\x04code\x18\x04 \x01(\tR\x04code\x12\x14\n" +
	"\x05label\x18\x05 \x01(\tR\x05label\x12?\n" +
	"\x04type\x18\x06 \x01(\x0e2+.backend.proto.customer.v2.CustomField.TypeR\x04type\x12\x1f\n" +
	"\vis_required\x18\n" +
	" \x01(\bR\n" +
	"isRequired\x12G\n" +
	"\x05state\x18\v \x01(\x0e2,.backend.proto.customer.v2.CustomField.StateB\x03\xe0A\x03R\x05state\x12Q\n" +
	"\rdefault_value\x18\f \x01(\v2,.backend.proto.customer.v2.CustomField.ValueR\fdefaultValue\x12G\n" +
	"\aoptions\x18\x0f \x03(\v2-.backend.proto.customer.v2.CustomField.OptionR\aoptions\x12B\n" +
	"\x10validation_rules\x18\x12 \x01(\v2\x17.google.protobuf.StructR\x0fvalidationRules\x12E\n" +
	"\x06source\x18\x13 \x01(\x0e2-.backend.proto.customer.v2.CustomField.SourceR\x06source\x12#\n" +
	"\rdisplay_order\x18\x15 \x01(\x05R\fdisplayOrder\x12\x1b\n" +
	"\thelp_text\x18\x16 \x01(\tR\bhelpText\x12;\n" +
	"\vcreate_time\x18\x19 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x1a \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12;\n" +
	"\vdelete_time\x18\x1b \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"deleteTime\x1a\xf2\x04\n" +
	"\x05Value\x12\x18\n" +
	"\x06string\x18\x02 \x01(\tH\x00R\x06string\x12#\n" +
	"\fdouble_value\x18\x03 \x01(\x01H\x00R\vdoubleValue\x12\x16\n" +
	"\x05int64\x18\x04 \x01(\x03H\x00R\x05int64\x12\x14\n" +
	"\x04bool\x18\x06 \x01(\bH\x00R\x04bool\x12*\n" +
	"\x05money\x18\a \x01(\v2\x12.google.type.MoneyH\x00R\x05money\x12C\n" +
	"\x0etimestamp_time\x18\b \x01(\v2\x1a.google.protobuf.TimestampH\x00R\rtimestampTime\x12S\n" +
	"\brelation\x18\t \x01(\v25.backend.proto.customer.v2.CustomField.Value.RelationH\x00R\brelation\x12Z\n" +
	"\vstring_list\x18\n" +
	" \x01(\v27.backend.proto.customer.v2.CustomField.Value.StringListH\x00R\n" +
	"stringList\x1a\xaa\x01\n" +
	"\bRelation\x12T\n" +
	"\x06entity\x18\x01 \x01(\x0e2<.backend.proto.customer.v2.CustomField.Value.Relation.EntityR\x06entity\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\x03R\x02id\"8\n" +
	"\x06Entity\x12\x16\n" +
	"\x12ENTITY_UNSPECIFIED\x10\x00\x12\f\n" +
	"\bCUSTOMER\x10\x01\x12\b\n" +
	"\x04LEAD\x10\x02\x1a$\n" +
	"\n" +
	"StringList\x12\x16\n" +
	"\x06values\x18\x01 \x03(\tR\x06valuesB\a\n" +
	"\x05value\x1a\xca\x01\n" +
	"\x06Option\x12B\n" +
	"\x05value\x18\x01 \x01(\v2,.backend.proto.customer.v2.CustomField.ValueR\x05value\x12\x14\n" +
	"\x05label\x18\x02 \x01(\tR\x05label\x12\x1d\n" +
	"\n" +
	"sort_order\x18\x03 \x01(\x05R\tsortOrder\x12G\n" +
	"\x05state\x18\x04 \x01(\x0e2,.backend.proto.customer.v2.CustomField.StateB\x03\xe0A\x03R\x05state\"7\n" +
	"\x05State\x12\x15\n" +
	"\x11STATE_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06ACTIVE\x10\x01\x12\v\n" +
	"\aDELETED\x10\x02\"\xbb\x01\n" +
	"\x04Type\x12\x14\n" +
	"\x10TYPE_UNSPECIFIED\x10\x00\x12\x0e\n" +
	"\n" +
	"SHORT_TEXT\x10\x01\x12\r\n" +
	"\tLONG_TEXT\x10\x02\x12\n" +
	"\n" +
	"\x06NUMBER\x10\x03\x12\b\n" +
	"\x04DATE\x10\x04\x12\v\n" +
	"\aBOOLEAN\x10\x05\x12\f\n" +
	"\bCURRENCY\x10\x06\x12\n" +
	"\n" +
	"\x06SELECT\x10\a\x12\x10\n" +
	"\fMULTI_SELECT\x10\b\x12\f\n" +
	"\bRELATION\x10\t\x12\t\n" +
	"\x05MONEY\x10\n" +
	"\x12\b\n" +
	"\x04TIME\x10\v\x12\f\n" +
	"\bDATETIME\x10\f\"K\n" +
	"\x0fAssociationType\x12 \n" +
	"\x1cASSOCIATION_TYPE_UNSPECIFIED\x10\x00\x12\f\n" +
	"\bCUSTOMER\x10\x01\x12\b\n" +
	"\x04LEAD\x10\x02\"8\n" +
	"\x06Source\x12\x16\n" +
	"\x12SOURCE_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06CUSTOM\x10\x01\x12\n" +
	"\n" +
	"\x06SYSTEM\x10\x02\"\xbf\x02\n" +
	"\x11CustomerAggregate\x12?\n" +
	"\bcustomer\x18\x01 \x01(\v2#.backend.proto.customer.v2.CustomerR\bcustomer\x12@\n" +
	"\taddresses\x18\x02 \x03(\v2\".backend.proto.customer.v2.AddressR\taddresses\x12>\n" +
	"\bcontacts\x18\x03 \x03(\v2\".backend.proto.customer.v2.ContactR\bcontacts\x12V\n" +
	"\frelated_data\x18\x04 \x01(\v2..backend.proto.customer.v2.CustomerRelatedDataH\x00R\vrelatedData\x88\x01\x01B\x0f\n" +
	"\r_related_data\"\xe8\a\n" +
	"\bMetadata\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12N\n" +
	"\forganization\x18\x02 \x01(\v2*.backend.proto.customer.v2.OrganizationRefR\forganization\x12Q\n" +
	"\rcustomer_type\x18\x03 \x01(\x0e2'.backend.proto.customer.v2.CustomerTypeB\x03\xe0A\x02R\fcustomerType\x12'\n" +
	"\n" +
	"given_name\x18\x04 \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01R\tgivenName\x12)\n" +
	"\vfamily_name\x18\x05 \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01R\n" +
	"familyName\x12D\n" +
	"\x05state\x18\v \x01(\x0e2).backend.proto.customer.v2.Metadata.StateB\x03\xe0A\x03R\x05state\x12!\n" +
	"\flifecycle_id\x18\f \x01(\x03R\vlifecycleId\x12$\n" +
	"\x0eowner_staff_id\x18\r \x01(\x03R\fownerStaffId\x12<\n" +
	"\rcustom_fields\x18\x0e \x01(\v2\x17.google.protobuf.StructR\fcustomFields\x12;\n" +
	"\vcreate_time\x18\x0f \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x10 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12;\n" +
	"\vdelete_time\x18\x11 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"deleteTime\x12&\n" +
	"\x0faction_state_id\x18\x12 \x01(\x03R\ractionStateId\x12)\n" +
	"\vavatar_path\x18\x13 \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01R\n" +
	"avatarPath\x12-\n" +
	"\rcustomer_code\x18\x14 \x01(\tB\b\xbaH\x05r\x03\x18\xff\x01R\fcustomerCode\x125\n" +
	"\x12referral_source_id\x18\x15 \x01(\x03B\a\xbaH\x04\"\x02(\x00R\x10referralSourceId\x127\n" +
	"\x15converted_customer_id\x18\x16 \x01(\x03H\x00R\x13convertedCustomerId\x88\x01\x01\"E\n" +
	"\x05State\x12\x15\n" +
	"\x11STATE_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06ACTIVE\x10\x01\x12\f\n" +
	"\bINACTIVE\x10\x02\x12\v\n" +
	"\aDELETED\x10\x03B\x18\n" +
	"\x16_converted_customer_id\"\xbf\x02\n" +
	"\x11MetadataAggregate\x12?\n" +
	"\bmetadata\x18\x01 \x01(\v2#.backend.proto.customer.v2.MetadataR\bmetadata\x12@\n" +
	"\taddresses\x18\x02 \x03(\v2\".backend.proto.customer.v2.AddressR\taddresses\x12>\n" +
	"\bcontacts\x18\x03 \x03(\v2\".backend.proto.customer.v2.ContactR\bcontacts\x12V\n" +
	"\frelated_data\x18\x04 \x01(\v2..backend.proto.customer.v2.CustomerRelatedDataH\x00R\vrelatedData\x88\x01\x01B\x0f\n" +
	"\r_related_data*E\n" +
	"\fCustomerType\x12\x1d\n" +
	"\x19CUSTOMER_TYPE_UNSPECIFIED\x10\x00\x12\b\n" +
	"\x04LEAD\x10\x01\x12\f\n" +
	"\bCUSTOMER\x10\x02Bk\n" +
	"#com.moego.backend.proto.customer.v2P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/customer/v2;customerpbb\x06proto3"

var (
	file_backend_proto_customer_v2_metadata_proto_rawDescOnce sync.Once
	file_backend_proto_customer_v2_metadata_proto_rawDescData []byte
)

func file_backend_proto_customer_v2_metadata_proto_rawDescGZIP() []byte {
	file_backend_proto_customer_v2_metadata_proto_rawDescOnce.Do(func() {
		file_backend_proto_customer_v2_metadata_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v2_metadata_proto_rawDesc), len(file_backend_proto_customer_v2_metadata_proto_rawDesc)))
	})
	return file_backend_proto_customer_v2_metadata_proto_rawDescData
}

var file_backend_proto_customer_v2_metadata_proto_enumTypes = make([]protoimpl.EnumInfo, 15)
var file_backend_proto_customer_v2_metadata_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_backend_proto_customer_v2_metadata_proto_goTypes = []any{
	(CustomerType)(0),                      // 0: backend.proto.customer.v2.CustomerType
	(Customer_State)(0),                    // 1: backend.proto.customer.v2.Customer.State
	(CustomerRelatedData_State)(0),         // 2: backend.proto.customer.v2.CustomerRelatedData.State
	(Lead_State)(0),                        // 3: backend.proto.customer.v2.Lead.State
	(Contact_State)(0),                     // 4: backend.proto.customer.v2.Contact.State
	(ContactTag_State)(0),                  // 5: backend.proto.customer.v2.ContactTag.State
	(ContactTag_Type)(0),                   // 6: backend.proto.customer.v2.ContactTag.Type
	(Address_State)(0),                     // 7: backend.proto.customer.v2.Address.State
	(Address_Type)(0),                      // 8: backend.proto.customer.v2.Address.Type
	(CustomField_State)(0),                 // 9: backend.proto.customer.v2.CustomField.State
	(CustomField_Type)(0),                  // 10: backend.proto.customer.v2.CustomField.Type
	(CustomField_AssociationType)(0),       // 11: backend.proto.customer.v2.CustomField.AssociationType
	(CustomField_Source)(0),                // 12: backend.proto.customer.v2.CustomField.Source
	(CustomField_Value_Relation_Entity)(0), // 13: backend.proto.customer.v2.CustomField.Value.Relation.Entity
	(Metadata_State)(0),                    // 14: backend.proto.customer.v2.Metadata.State
	(*Customer)(nil),                       // 15: backend.proto.customer.v2.Customer
	(*CustomerRelatedData)(nil),            // 16: backend.proto.customer.v2.CustomerRelatedData
	(*Lead)(nil),                           // 17: backend.proto.customer.v2.Lead
	(*Contact)(nil),                        // 18: backend.proto.customer.v2.Contact
	(*ContactTag)(nil),                     // 19: backend.proto.customer.v2.ContactTag
	(*Address)(nil),                        // 20: backend.proto.customer.v2.Address
	(*CustomField)(nil),                    // 21: backend.proto.customer.v2.CustomField
	(*CustomerAggregate)(nil),              // 22: backend.proto.customer.v2.CustomerAggregate
	(*Metadata)(nil),                       // 23: backend.proto.customer.v2.Metadata
	(*MetadataAggregate)(nil),              // 24: backend.proto.customer.v2.MetadataAggregate
	(*CustomField_Value)(nil),              // 25: backend.proto.customer.v2.CustomField.Value
	(*CustomField_Option)(nil),             // 26: backend.proto.customer.v2.CustomField.Option
	(*CustomField_Value_Relation)(nil),     // 27: backend.proto.customer.v2.CustomField.Value.Relation
	(*CustomField_Value_StringList)(nil),   // 28: backend.proto.customer.v2.CustomField.Value.StringList
	(*OrganizationRef)(nil),                // 29: backend.proto.customer.v2.OrganizationRef
	(*structpb.Struct)(nil),                // 30: google.protobuf.Struct
	(*timestamppb.Timestamp)(nil),          // 31: google.protobuf.Timestamp
	(*phone_number.PhoneNumber)(nil),       // 32: google.type.PhoneNumber
	(*postaladdress.PostalAddress)(nil),    // 33: google.type.PostalAddress
	(*latlng.LatLng)(nil),                  // 34: google.type.LatLng
	(*money.Money)(nil),                    // 35: google.type.Money
}
var file_backend_proto_customer_v2_metadata_proto_depIdxs = []int32{
	29, // 0: backend.proto.customer.v2.Customer.organization:type_name -> backend.proto.customer.v2.OrganizationRef
	1,  // 1: backend.proto.customer.v2.Customer.state:type_name -> backend.proto.customer.v2.Customer.State
	30, // 2: backend.proto.customer.v2.Customer.custom_fields:type_name -> google.protobuf.Struct
	31, // 3: backend.proto.customer.v2.Customer.create_time:type_name -> google.protobuf.Timestamp
	31, // 4: backend.proto.customer.v2.Customer.update_time:type_name -> google.protobuf.Timestamp
	31, // 5: backend.proto.customer.v2.Customer.delete_time:type_name -> google.protobuf.Timestamp
	31, // 6: backend.proto.customer.v2.CustomerRelatedData.birthday:type_name -> google.protobuf.Timestamp
	2,  // 7: backend.proto.customer.v2.CustomerRelatedData.state:type_name -> backend.proto.customer.v2.CustomerRelatedData.State
	31, // 8: backend.proto.customer.v2.CustomerRelatedData.delete_time:type_name -> google.protobuf.Timestamp
	31, // 9: backend.proto.customer.v2.CustomerRelatedData.create_time:type_name -> google.protobuf.Timestamp
	31, // 10: backend.proto.customer.v2.CustomerRelatedData.update_time:type_name -> google.protobuf.Timestamp
	29, // 11: backend.proto.customer.v2.Lead.organization:type_name -> backend.proto.customer.v2.OrganizationRef
	3,  // 12: backend.proto.customer.v2.Lead.state:type_name -> backend.proto.customer.v2.Lead.State
	30, // 13: backend.proto.customer.v2.Lead.custom_fields:type_name -> google.protobuf.Struct
	31, // 14: backend.proto.customer.v2.Lead.create_time:type_name -> google.protobuf.Timestamp
	31, // 15: backend.proto.customer.v2.Lead.update_time:type_name -> google.protobuf.Timestamp
	31, // 16: backend.proto.customer.v2.Lead.delete_time:type_name -> google.protobuf.Timestamp
	32, // 17: backend.proto.customer.v2.Contact.phone:type_name -> google.type.PhoneNumber
	4,  // 18: backend.proto.customer.v2.Contact.state:type_name -> backend.proto.customer.v2.Contact.State
	19, // 19: backend.proto.customer.v2.Contact.tags:type_name -> backend.proto.customer.v2.ContactTag
	31, // 20: backend.proto.customer.v2.Contact.create_time:type_name -> google.protobuf.Timestamp
	31, // 21: backend.proto.customer.v2.Contact.update_time:type_name -> google.protobuf.Timestamp
	31, // 22: backend.proto.customer.v2.Contact.delete_time:type_name -> google.protobuf.Timestamp
	29, // 23: backend.proto.customer.v2.ContactTag.organization:type_name -> backend.proto.customer.v2.OrganizationRef
	5,  // 24: backend.proto.customer.v2.ContactTag.state:type_name -> backend.proto.customer.v2.ContactTag.State
	6,  // 25: backend.proto.customer.v2.ContactTag.type:type_name -> backend.proto.customer.v2.ContactTag.Type
	31, // 26: backend.proto.customer.v2.ContactTag.create_time:type_name -> google.protobuf.Timestamp
	31, // 27: backend.proto.customer.v2.ContactTag.update_time:type_name -> google.protobuf.Timestamp
	31, // 28: backend.proto.customer.v2.ContactTag.delete_time:type_name -> google.protobuf.Timestamp
	33, // 29: backend.proto.customer.v2.Address.address:type_name -> google.type.PostalAddress
	34, // 30: backend.proto.customer.v2.Address.latlng:type_name -> google.type.LatLng
	8,  // 31: backend.proto.customer.v2.Address.type:type_name -> backend.proto.customer.v2.Address.Type
	7,  // 32: backend.proto.customer.v2.Address.state:type_name -> backend.proto.customer.v2.Address.State
	29, // 33: backend.proto.customer.v2.Address.organization:type_name -> backend.proto.customer.v2.OrganizationRef
	31, // 34: backend.proto.customer.v2.Address.create_time:type_name -> google.protobuf.Timestamp
	31, // 35: backend.proto.customer.v2.Address.update_time:type_name -> google.protobuf.Timestamp
	31, // 36: backend.proto.customer.v2.Address.delete_time:type_name -> google.protobuf.Timestamp
	29, // 37: backend.proto.customer.v2.CustomField.organization:type_name -> backend.proto.customer.v2.OrganizationRef
	11, // 38: backend.proto.customer.v2.CustomField.association_type:type_name -> backend.proto.customer.v2.CustomField.AssociationType
	10, // 39: backend.proto.customer.v2.CustomField.type:type_name -> backend.proto.customer.v2.CustomField.Type
	9,  // 40: backend.proto.customer.v2.CustomField.state:type_name -> backend.proto.customer.v2.CustomField.State
	25, // 41: backend.proto.customer.v2.CustomField.default_value:type_name -> backend.proto.customer.v2.CustomField.Value
	26, // 42: backend.proto.customer.v2.CustomField.options:type_name -> backend.proto.customer.v2.CustomField.Option
	30, // 43: backend.proto.customer.v2.CustomField.validation_rules:type_name -> google.protobuf.Struct
	12, // 44: backend.proto.customer.v2.CustomField.source:type_name -> backend.proto.customer.v2.CustomField.Source
	31, // 45: backend.proto.customer.v2.CustomField.create_time:type_name -> google.protobuf.Timestamp
	31, // 46: backend.proto.customer.v2.CustomField.update_time:type_name -> google.protobuf.Timestamp
	31, // 47: backend.proto.customer.v2.CustomField.delete_time:type_name -> google.protobuf.Timestamp
	15, // 48: backend.proto.customer.v2.CustomerAggregate.customer:type_name -> backend.proto.customer.v2.Customer
	20, // 49: backend.proto.customer.v2.CustomerAggregate.addresses:type_name -> backend.proto.customer.v2.Address
	18, // 50: backend.proto.customer.v2.CustomerAggregate.contacts:type_name -> backend.proto.customer.v2.Contact
	16, // 51: backend.proto.customer.v2.CustomerAggregate.related_data:type_name -> backend.proto.customer.v2.CustomerRelatedData
	29, // 52: backend.proto.customer.v2.Metadata.organization:type_name -> backend.proto.customer.v2.OrganizationRef
	0,  // 53: backend.proto.customer.v2.Metadata.customer_type:type_name -> backend.proto.customer.v2.CustomerType
	14, // 54: backend.proto.customer.v2.Metadata.state:type_name -> backend.proto.customer.v2.Metadata.State
	30, // 55: backend.proto.customer.v2.Metadata.custom_fields:type_name -> google.protobuf.Struct
	31, // 56: backend.proto.customer.v2.Metadata.create_time:type_name -> google.protobuf.Timestamp
	31, // 57: backend.proto.customer.v2.Metadata.update_time:type_name -> google.protobuf.Timestamp
	31, // 58: backend.proto.customer.v2.Metadata.delete_time:type_name -> google.protobuf.Timestamp
	23, // 59: backend.proto.customer.v2.MetadataAggregate.metadata:type_name -> backend.proto.customer.v2.Metadata
	20, // 60: backend.proto.customer.v2.MetadataAggregate.addresses:type_name -> backend.proto.customer.v2.Address
	18, // 61: backend.proto.customer.v2.MetadataAggregate.contacts:type_name -> backend.proto.customer.v2.Contact
	16, // 62: backend.proto.customer.v2.MetadataAggregate.related_data:type_name -> backend.proto.customer.v2.CustomerRelatedData
	35, // 63: backend.proto.customer.v2.CustomField.Value.money:type_name -> google.type.Money
	31, // 64: backend.proto.customer.v2.CustomField.Value.timestamp_time:type_name -> google.protobuf.Timestamp
	27, // 65: backend.proto.customer.v2.CustomField.Value.relation:type_name -> backend.proto.customer.v2.CustomField.Value.Relation
	28, // 66: backend.proto.customer.v2.CustomField.Value.string_list:type_name -> backend.proto.customer.v2.CustomField.Value.StringList
	25, // 67: backend.proto.customer.v2.CustomField.Option.value:type_name -> backend.proto.customer.v2.CustomField.Value
	9,  // 68: backend.proto.customer.v2.CustomField.Option.state:type_name -> backend.proto.customer.v2.CustomField.State
	13, // 69: backend.proto.customer.v2.CustomField.Value.Relation.entity:type_name -> backend.proto.customer.v2.CustomField.Value.Relation.Entity
	70, // [70:70] is the sub-list for method output_type
	70, // [70:70] is the sub-list for method input_type
	70, // [70:70] is the sub-list for extension type_name
	70, // [70:70] is the sub-list for extension extendee
	0,  // [0:70] is the sub-list for field type_name
}

func init() { file_backend_proto_customer_v2_metadata_proto_init() }
func file_backend_proto_customer_v2_metadata_proto_init() {
	if File_backend_proto_customer_v2_metadata_proto != nil {
		return
	}
	file_backend_proto_customer_v2_common_proto_init()
	file_backend_proto_customer_v2_metadata_proto_msgTypes[1].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_proto_msgTypes[2].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_proto_msgTypes[7].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_proto_msgTypes[8].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_proto_msgTypes[9].OneofWrappers = []any{}
	file_backend_proto_customer_v2_metadata_proto_msgTypes[10].OneofWrappers = []any{
		(*CustomField_Value_String_)(nil),
		(*CustomField_Value_DoubleValue)(nil),
		(*CustomField_Value_Int64)(nil),
		(*CustomField_Value_Bool)(nil),
		(*CustomField_Value_Money)(nil),
		(*CustomField_Value_TimestampTime)(nil),
		(*CustomField_Value_Relation_)(nil),
		(*CustomField_Value_StringList_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v2_metadata_proto_rawDesc), len(file_backend_proto_customer_v2_metadata_proto_rawDesc)),
			NumEnums:      15,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_customer_v2_metadata_proto_goTypes,
		DependencyIndexes: file_backend_proto_customer_v2_metadata_proto_depIdxs,
		EnumInfos:         file_backend_proto_customer_v2_metadata_proto_enumTypes,
		MessageInfos:      file_backend_proto_customer_v2_metadata_proto_msgTypes,
	}.Build()
	File_backend_proto_customer_v2_metadata_proto = out.File
	file_backend_proto_customer_v2_metadata_proto_goTypes = nil
	file_backend_proto_customer_v2_metadata_proto_depIdxs = nil
}
