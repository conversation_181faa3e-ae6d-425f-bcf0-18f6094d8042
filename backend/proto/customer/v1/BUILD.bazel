load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "customerpb_proto",
    srcs = [
        "customer.proto",
        "customer_query_service.proto",
        "customer_service.proto",
        "customer_view_models.proto",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_bufbuild_protovalidate//proto/protovalidate/buf/validate:validate_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@googleapis//google/api:annotations_proto",
        "@googleapis//google/api:field_behavior_proto",
    ],
)

go_proto_library(
    name = "customerpb_go_proto",
    compilers = [
        "@io_bazel_rules_go//proto:go_grpc_v2",
        "@io_bazel_rules_go//proto:go_proto",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/customer/v1",
    proto = ":customerpb_proto",
    visibility = ["//visibility:public"],
    deps = [
        "@build_buf_gen_go_bufbuild_protovalidate_protocolbuffers_go//buf/validate:go_default_library",
        "@googleapis//google/api:annotations_go_proto",
        "@googleapis//google/api:field_behavior_go_proto",
    ],
)

go_library(
    name = "customer",
    embed = [":customerpb_go_proto"],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/customer/v1",
    visibility = ["//visibility:public"],
)
