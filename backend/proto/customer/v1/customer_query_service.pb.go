// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: ID检索接口不需要分页 --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 无游标分页需求 --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 响应不包含分页 --)
// (-- api-linter: core::0158::response-repeated-first-field=disabled
//     aip.dev/not-precedent: 自定义响应结构 --)
// (-- api-linter: core::0158::response-plural-first-field=disabled
//     aip.dev/not-precedent: 自定义响应结构 --)

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.8
// 	protoc        (unknown)
// source: backend/proto/customer/v1/customer_query_service.proto

package customerpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 排序字段
type CustomerOrderField int32

const (
	// 未指定排序字段
	CustomerOrderField_CUSTOMER_ORDER_FIELD_UNSPECIFIED CustomerOrderField = 0
	// 按客户ID排序
	CustomerOrderField_CLIENT_ID CustomerOrderField = 1
	// 按名字排序
	CustomerOrderField_FIRST_NAME CustomerOrderField = 2
	// 按姓氏排序
	CustomerOrderField_LAST_NAME CustomerOrderField = 3
)

// Enum value maps for CustomerOrderField.
var (
	CustomerOrderField_name = map[int32]string{
		0: "CUSTOMER_ORDER_FIELD_UNSPECIFIED",
		1: "CLIENT_ID",
		2: "FIRST_NAME",
		3: "LAST_NAME",
	}
	CustomerOrderField_value = map[string]int32{
		"CUSTOMER_ORDER_FIELD_UNSPECIFIED": 0,
		"CLIENT_ID":                        1,
		"FIRST_NAME":                       2,
		"LAST_NAME":                        3,
	}
)

func (x CustomerOrderField) Enum() *CustomerOrderField {
	p := new(CustomerOrderField)
	*p = x
	return p
}

func (x CustomerOrderField) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustomerOrderField) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v1_customer_query_service_proto_enumTypes[0].Descriptor()
}

func (CustomerOrderField) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v1_customer_query_service_proto_enumTypes[0]
}

func (x CustomerOrderField) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustomerOrderField.Descriptor instead.
func (CustomerOrderField) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{0}
}

// 排序方向
type OrderDirection int32

const (
	// 未指定排序方向
	OrderDirection_ORDER_DIRECTION_UNSPECIFIED OrderDirection = 0
	// 升序
	OrderDirection_ASC OrderDirection = 1
	// 降序
	OrderDirection_DESC OrderDirection = 2
)

// Enum value maps for OrderDirection.
var (
	OrderDirection_name = map[int32]string{
		0: "ORDER_DIRECTION_UNSPECIFIED",
		1: "ASC",
		2: "DESC",
	}
	OrderDirection_value = map[string]int32{
		"ORDER_DIRECTION_UNSPECIFIED": 0,
		"ASC":                         1,
		"DESC":                        2,
	}
)

func (x OrderDirection) Enum() *OrderDirection {
	p := new(OrderDirection)
	*p = x
	return p
}

func (x OrderDirection) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderDirection) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v1_customer_query_service_proto_enumTypes[1].Descriptor()
}

func (OrderDirection) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v1_customer_query_service_proto_enumTypes[1]
}

func (x OrderDirection) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderDirection.Descriptor instead.
func (OrderDirection) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{1}
}

// IN/NOT IN 操作符
type InNotInOperator int32

const (
	// 未指定操作符
	InNotInOperator_IN_NOT_IN_OPERATOR_UNSPECIFIED InNotInOperator = 0
	// 包含
	InNotInOperator_IN InNotInOperator = 1
	// 不包含
	InNotInOperator_NOT_IN InNotInOperator = 2
)

// Enum value maps for InNotInOperator.
var (
	InNotInOperator_name = map[int32]string{
		0: "IN_NOT_IN_OPERATOR_UNSPECIFIED",
		1: "IN",
		2: "NOT_IN",
	}
	InNotInOperator_value = map[string]int32{
		"IN_NOT_IN_OPERATOR_UNSPECIFIED": 0,
		"IN":                             1,
		"NOT_IN":                         2,
	}
)

func (x InNotInOperator) Enum() *InNotInOperator {
	p := new(InNotInOperator)
	*p = x
	return p
}

func (x InNotInOperator) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InNotInOperator) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v1_customer_query_service_proto_enumTypes[2].Descriptor()
}

func (InNotInOperator) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v1_customer_query_service_proto_enumTypes[2]
}

func (x InNotInOperator) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InNotInOperator.Descriptor instead.
func (InNotInOperator) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{2}
}

// 过滤操作符
type FilterOperator int32

const (
	// 未指定操作符
	FilterOperator_FILTER_OPERATOR_UNSPECIFIED FilterOperator = 0
	// 等于
	FilterOperator_EQUAL FilterOperator = 1
	// 不等于
	FilterOperator_NOT_EQUAL FilterOperator = 2
	// 小于
	FilterOperator_LESS_THAN FilterOperator = 3
	// 小于等于
	FilterOperator_LESS_THAN_OR_EQUAL FilterOperator = 4
	// 大于
	FilterOperator_GREATER_THAN FilterOperator = 5
	// 大于等于
	FilterOperator_GREATER_THAN_OR_EQUAL FilterOperator = 6
	// 包含
	FilterOperator_FILTER_IN FilterOperator = 7
	// 不包含
	FilterOperator_FILTER_NOT_IN FilterOperator = 8
	// 模糊匹配
	FilterOperator_LIKE FilterOperator = 9
	// 为空
	FilterOperator_IS_NULL FilterOperator = 10
	// 不为空
	FilterOperator_IS_NOT_NULL FilterOperator = 11
	// 区间
	FilterOperator_BETWEEN FilterOperator = 12
)

// Enum value maps for FilterOperator.
var (
	FilterOperator_name = map[int32]string{
		0:  "FILTER_OPERATOR_UNSPECIFIED",
		1:  "EQUAL",
		2:  "NOT_EQUAL",
		3:  "LESS_THAN",
		4:  "LESS_THAN_OR_EQUAL",
		5:  "GREATER_THAN",
		6:  "GREATER_THAN_OR_EQUAL",
		7:  "FILTER_IN",
		8:  "FILTER_NOT_IN",
		9:  "LIKE",
		10: "IS_NULL",
		11: "IS_NOT_NULL",
		12: "BETWEEN",
	}
	FilterOperator_value = map[string]int32{
		"FILTER_OPERATOR_UNSPECIFIED": 0,
		"EQUAL":                       1,
		"NOT_EQUAL":                   2,
		"LESS_THAN":                   3,
		"LESS_THAN_OR_EQUAL":          4,
		"GREATER_THAN":                5,
		"GREATER_THAN_OR_EQUAL":       6,
		"FILTER_IN":                   7,
		"FILTER_NOT_IN":               8,
		"LIKE":                        9,
		"IS_NULL":                     10,
		"IS_NOT_NULL":                 11,
		"BETWEEN":                     12,
	}
)

func (x FilterOperator) Enum() *FilterOperator {
	p := new(FilterOperator)
	*p = x
	return p
}

func (x FilterOperator) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FilterOperator) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v1_customer_query_service_proto_enumTypes[3].Descriptor()
}

func (FilterOperator) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v1_customer_query_service_proto_enumTypes[3]
}

func (x FilterOperator) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FilterOperator.Descriptor instead.
func (FilterOperator) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{3}
}

// 通用筛选表达式
type FilterCondition struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 允许的字段名，服务端会做白名单校验
	Field string `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`
	// 操作符
	Op FilterOperator `protobuf:"varint,2,opt,name=op,proto3,enum=backend.proto.customer.v1.FilterOperator" json:"op,omitempty"`
	// 单值
	Value *string `protobuf:"bytes,3,opt,name=value,proto3,oneof" json:"value,omitempty"`
	// 多值
	Values []string `protobuf:"bytes,4,rep,name=values,proto3" json:"values,omitempty"`
	// 区间 [start, end]
	Start *string `protobuf:"bytes,5,opt,name=start,proto3,oneof" json:"start,omitempty"`
	// 区间结束值
	End           *string `protobuf:"bytes,6,opt,name=end,proto3,oneof" json:"end,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterCondition) Reset() {
	*x = FilterCondition{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterCondition) ProtoMessage() {}

func (x *FilterCondition) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterCondition.ProtoReflect.Descriptor instead.
func (*FilterCondition) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{0}
}

func (x *FilterCondition) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *FilterCondition) GetOp() FilterOperator {
	if x != nil {
		return x.Op
	}
	return FilterOperator_FILTER_OPERATOR_UNSPECIFIED
}

func (x *FilterCondition) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

func (x *FilterCondition) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

func (x *FilterCondition) GetStart() string {
	if x != nil && x.Start != nil {
		return *x.Start
	}
	return ""
}

func (x *FilterCondition) GetEnd() string {
	if x != nil && x.End != nil {
		return *x.End
	}
	return ""
}

// 过滤组，支持组内 AND/OR 组合
type FilterGroup struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 组内连接关系: AND | OR
	Combiner string `protobuf:"bytes,1,opt,name=combiner,proto3" json:"combiner,omitempty"`
	// 原子条件
	Conditions []*FilterCondition `protobuf:"bytes,2,rep,name=conditions,proto3" json:"conditions,omitempty"`
	// 子组
	Groups        []*FilterGroup `protobuf:"bytes,3,rep,name=groups,proto3" json:"groups,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterGroup) Reset() {
	*x = FilterGroup{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterGroup) ProtoMessage() {}

func (x *FilterGroup) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterGroup.ProtoReflect.Descriptor instead.
func (*FilterGroup) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{1}
}

func (x *FilterGroup) GetCombiner() string {
	if x != nil {
		return x.Combiner
	}
	return ""
}

func (x *FilterGroup) GetConditions() []*FilterCondition {
	if x != nil {
		return x.Conditions
	}
	return nil
}

func (x *FilterGroup) GetGroups() []*FilterGroup {
	if x != nil {
		return x.Groups
	}
	return nil
}

// 通用分页
// 偏移量+限制
type OffsetLimit struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 偏移量，从0开始
	Offset int32 `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	// 条数上限，必须为正
	Limit         int32 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OffsetLimit) Reset() {
	*x = OffsetLimit{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OffsetLimit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OffsetLimit) ProtoMessage() {}

func (x *OffsetLimit) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OffsetLimit.ProtoReflect.Descriptor instead.
func (*OffsetLimit) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{2}
}

func (x *OffsetLimit) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *OffsetLimit) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

// 通用作用域
// 作用域（公司/门店/客户集）
type Scope struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 门店ID集
	BusinessIds []int64 `protobuf:"varint,2,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// 客户ID集
	CustomerIds   []int64 `protobuf:"varint,3,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Scope) Reset() {
	*x = Scope{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Scope) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Scope) ProtoMessage() {}

func (x *Scope) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Scope.ProtoReflect.Descriptor instead.
func (*Scope) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{3}
}

func (x *Scope) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *Scope) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *Scope) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

// ====== 输出模型 ======
// 客户ID集合
type CustomerIds struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID列表
	Ids           []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerIds) Reset() {
	*x = CustomerIds{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerIds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerIds) ProtoMessage() {}

func (x *CustomerIds) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerIds.ProtoReflect.Descriptor instead.
func (*CustomerIds) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{4}
}

func (x *CustomerIds) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// 客户主号码
type CustomerPhone struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 电话号码
	PhoneNumber   string `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerPhone) Reset() {
	*x = CustomerPhone{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPhone) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPhone) ProtoMessage() {}

func (x *CustomerPhone) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPhone.ProtoReflect.Descriptor instead.
func (*CustomerPhone) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{5}
}

func (x *CustomerPhone) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CustomerPhone) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

// 客户主号码列表
type PrimaryPhones struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户电话列表
	Items         []*CustomerPhone `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PrimaryPhones) Reset() {
	*x = PrimaryPhones{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PrimaryPhones) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrimaryPhones) ProtoMessage() {}

func (x *PrimaryPhones) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrimaryPhones.ProtoReflect.Descriptor instead.
func (*PrimaryPhones) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{6}
}

func (x *PrimaryPhones) GetItems() []*CustomerPhone {
	if x != nil {
		return x.Items
	}
	return nil
}

// 客户基础信息（用于列表装配）
type CustomerBasicInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 门店ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 客户名
	First string `protobuf:"bytes,3,opt,name=first,proto3" json:"first,omitempty"`
	// 客户姓
	Last string `protobuf:"bytes,4,opt,name=last,proto3" json:"last,omitempty"`
	// 头像路径
	AvatarPath string `protobuf:"bytes,5,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// 客户颜色标识
	ClientColor string `protobuf:"bytes,6,opt,name=client_color,json=clientColor,proto3" json:"client_color,omitempty"`
	// 邮箱地址
	Email string `protobuf:"bytes,7,opt,name=email,proto3" json:"email,omitempty"`
	// 偏好频率类型
	PreferredFrequencyType int64 `protobuf:"varint,8,opt,name=preferred_frequency_type,json=preferredFrequencyType,proto3" json:"preferred_frequency_type,omitempty"`
	// 偏好频率日期
	PreferredFrequencyDay int64 `protobuf:"varint,9,opt,name=preferred_frequency_day,json=preferredFrequencyDay,proto3" json:"preferred_frequency_day,omitempty"`
	// 是否非活跃
	Inactive int32 `protobuf:"varint,10,opt,name=inactive,proto3" json:"inactive,omitempty"`
	// 是否取消订阅
	IsUnsubscribed int32 `protobuf:"varint,11,opt,name=is_unsubscribed,json=isUnsubscribed,proto3" json:"is_unsubscribed,omitempty"`
	// 客户来源
	Source string `protobuf:"bytes,12,opt,name=source,proto3" json:"source,omitempty"`
	// 账户ID
	AccountId int64 `protobuf:"varint,13,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// 公司ID
	CompanyId     int64 `protobuf:"varint,14,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerBasicInfo) Reset() {
	*x = CustomerBasicInfo{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerBasicInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerBasicInfo) ProtoMessage() {}

func (x *CustomerBasicInfo) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerBasicInfo.ProtoReflect.Descriptor instead.
func (*CustomerBasicInfo) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{7}
}

func (x *CustomerBasicInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CustomerBasicInfo) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CustomerBasicInfo) GetFirst() string {
	if x != nil {
		return x.First
	}
	return ""
}

func (x *CustomerBasicInfo) GetLast() string {
	if x != nil {
		return x.Last
	}
	return ""
}

func (x *CustomerBasicInfo) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *CustomerBasicInfo) GetClientColor() string {
	if x != nil {
		return x.ClientColor
	}
	return ""
}

func (x *CustomerBasicInfo) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CustomerBasicInfo) GetPreferredFrequencyType() int64 {
	if x != nil {
		return x.PreferredFrequencyType
	}
	return 0
}

func (x *CustomerBasicInfo) GetPreferredFrequencyDay() int64 {
	if x != nil {
		return x.PreferredFrequencyDay
	}
	return 0
}

func (x *CustomerBasicInfo) GetInactive() int32 {
	if x != nil {
		return x.Inactive
	}
	return 0
}

func (x *CustomerBasicInfo) GetIsUnsubscribed() int32 {
	if x != nil {
		return x.IsUnsubscribed
	}
	return 0
}

func (x *CustomerBasicInfo) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *CustomerBasicInfo) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *CustomerBasicInfo) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// 客户基础信息列表
type CustomerBasicInfos struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户基础信息列表
	Items         []*CustomerBasicInfo `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerBasicInfos) Reset() {
	*x = CustomerBasicInfos{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerBasicInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerBasicInfos) ProtoMessage() {}

func (x *CustomerBasicInfos) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerBasicInfos.ProtoReflect.Descriptor instead.
func (*CustomerBasicInfos) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{8}
}

func (x *CustomerBasicInfos) GetItems() []*CustomerBasicInfo {
	if x != nil {
		return x.Items
	}
	return nil
}

// 宠物记录（用于FULLTEXT）
type PetRecord struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 宠物ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 客户ID
	CustomerId    int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PetRecord) Reset() {
	*x = PetRecord{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetRecord) ProtoMessage() {}

func (x *PetRecord) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetRecord.ProtoReflect.Descriptor instead.
func (*PetRecord) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{9}
}

func (x *PetRecord) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PetRecord) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

// 宠物记录列表
type PetRecords struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 宠物记录列表
	Items         []*PetRecord `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PetRecords) Reset() {
	*x = PetRecords{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetRecords) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetRecords) ProtoMessage() {}

func (x *PetRecords) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetRecords.ProtoReflect.Descriptor instead.
func (*PetRecords) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{10}
}

func (x *PetRecords) GetItems() []*PetRecord {
	if x != nil {
		return x.Items
	}
	return nil
}

// 宠物类型计数
type PetTypeCount struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 宠物类型ID
	PetTypeId int64 `protobuf:"varint,1,opt,name=pet_type_id,json=petTypeId,proto3" json:"pet_type_id,omitempty"`
	// 数量
	Count         int64 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PetTypeCount) Reset() {
	*x = PetTypeCount{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetTypeCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetTypeCount) ProtoMessage() {}

func (x *PetTypeCount) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetTypeCount.ProtoReflect.Descriptor instead.
func (*PetTypeCount) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{11}
}

func (x *PetTypeCount) GetPetTypeId() int64 {
	if x != nil {
		return x.PetTypeId
	}
	return 0
}

func (x *PetTypeCount) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

// 宠物类型计数列表
type PetTypeCounts struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 宠物类型计数列表
	Items         []*PetTypeCount `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PetTypeCounts) Reset() {
	*x = PetTypeCounts{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetTypeCounts) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetTypeCounts) ProtoMessage() {}

func (x *PetTypeCounts) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetTypeCounts.ProtoReflect.Descriptor instead.
func (*PetTypeCounts) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{12}
}

func (x *PetTypeCounts) GetItems() []*PetTypeCount {
	if x != nil {
		return x.Items
	}
	return nil
}

// 宠物简要信息（列表装配）
type PetInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 宠物ID
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// 客户ID
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 宠物名称
	Pet string `protobuf:"bytes,3,opt,name=pet,proto3" json:"pet,omitempty"`
	// 品种
	Breed string `protobuf:"bytes,4,opt,name=breed,proto3" json:"breed,omitempty"`
	// 生命状态
	LifeStatus int32 `protobuf:"varint,5,opt,name=life_status,json=lifeStatus,proto3" json:"life_status,omitempty"`
	// 评估状态
	EvaluationStatus int32 `protobuf:"varint,6,opt,name=evaluation_status,json=evaluationStatus,proto3" json:"evaluation_status,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *PetInfo) Reset() {
	*x = PetInfo{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetInfo) ProtoMessage() {}

func (x *PetInfo) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetInfo.ProtoReflect.Descriptor instead.
func (*PetInfo) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{13}
}

func (x *PetInfo) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PetInfo) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *PetInfo) GetPet() string {
	if x != nil {
		return x.Pet
	}
	return ""
}

func (x *PetInfo) GetBreed() string {
	if x != nil {
		return x.Breed
	}
	return ""
}

func (x *PetInfo) GetLifeStatus() int32 {
	if x != nil {
		return x.LifeStatus
	}
	return 0
}

func (x *PetInfo) GetEvaluationStatus() int32 {
	if x != nil {
		return x.EvaluationStatus
	}
	return 0
}

// 宠物简要信息列表
type PetInfos struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 宠物信息列表
	Items         []*PetInfo `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PetInfos) Reset() {
	*x = PetInfos{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetInfos) ProtoMessage() {}

func (x *PetInfos) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetInfos.ProtoReflect.Descriptor instead.
func (*PetInfos) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{14}
}

func (x *PetInfos) GetItems() []*PetInfo {
	if x != nil {
		return x.Items
	}
	return nil
}

// ====== Customer ======
// 客户：按关键词（邮箱/姓名）模糊匹配
// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: 本查询无需分页 --)
//
// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: 本查询无需分页 --)
type SearchCustomerIdsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 查询作用域
	Scope *Scope `protobuf:"bytes,1,opt,name=scope,proto3" json:"scope,omitempty"`
	// 匹配 email 或 full name
	Keyword       string `protobuf:"bytes,2,opt,name=keyword,proto3" json:"keyword,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchCustomerIdsRequest) Reset() {
	*x = SearchCustomerIdsRequest{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchCustomerIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCustomerIdsRequest) ProtoMessage() {}

func (x *SearchCustomerIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCustomerIdsRequest.ProtoReflect.Descriptor instead.
func (*SearchCustomerIdsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{15}
}

func (x *SearchCustomerIdsRequest) GetScope() *Scope {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *SearchCustomerIdsRequest) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

// (-- api-linter: core::0158::response-plural-first-field=disabled
//
//	aip.dev/not-precedent: 返回ID集合容器即可 --)
//
// 标准搜索响应
type SearchCustomerIdsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID列表
	CustomerIds []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	// 下一页令牌
	NextPageToken *string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3,oneof" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchCustomerIdsResponse) Reset() {
	*x = SearchCustomerIdsResponse{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchCustomerIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCustomerIdsResponse) ProtoMessage() {}

func (x *SearchCustomerIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCustomerIdsResponse.ProtoReflect.Descriptor instead.
func (*SearchCustomerIdsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{16}
}

func (x *SearchCustomerIdsResponse) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

func (x *SearchCustomerIdsResponse) GetNextPageToken() string {
	if x != nil && x.NextPageToken != nil {
		return *x.NextPageToken
	}
	return ""
}

// 客户：last_name 全文检索
// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: 本查询无需分页 --)
//
// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: 本查询无需分页 --)
type SearchCustomerIdsByLastNameRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 查询作用域
	Scope *Scope `protobuf:"bytes,1,opt,name=scope,proto3" json:"scope,omitempty"`
	// 搜索关键词
	Keyword       string `protobuf:"bytes,2,opt,name=keyword,proto3" json:"keyword,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchCustomerIdsByLastNameRequest) Reset() {
	*x = SearchCustomerIdsByLastNameRequest{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchCustomerIdsByLastNameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCustomerIdsByLastNameRequest) ProtoMessage() {}

func (x *SearchCustomerIdsByLastNameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCustomerIdsByLastNameRequest.ProtoReflect.Descriptor instead.
func (*SearchCustomerIdsByLastNameRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{17}
}

func (x *SearchCustomerIdsByLastNameRequest) GetScope() *Scope {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *SearchCustomerIdsByLastNameRequest) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

// (-- api-linter: core::0158::response-plural-first-field=disabled
//
//	aip.dev/not-precedent: 返回ID集合容器即可 --)
//
// 按姓氏搜索客户ID响应
type SearchCustomerIdsByLastNameResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID列表
	CustomerIds []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	// 下一页令牌
	NextPageToken *string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3,oneof" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchCustomerIdsByLastNameResponse) Reset() {
	*x = SearchCustomerIdsByLastNameResponse{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchCustomerIdsByLastNameResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCustomerIdsByLastNameResponse) ProtoMessage() {}

func (x *SearchCustomerIdsByLastNameResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCustomerIdsByLastNameResponse.ProtoReflect.Descriptor instead.
func (*SearchCustomerIdsByLastNameResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{18}
}

func (x *SearchCustomerIdsByLastNameResponse) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

func (x *SearchCustomerIdsByLastNameResponse) GetNextPageToken() string {
	if x != nil && x.NextPageToken != nil {
		return *x.NextPageToken
	}
	return ""
}

// 客户：动态条件过滤
type FilterCustomerIdsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 查询作用域
	Scope *Scope `protobuf:"bytes,1,opt,name=scope,proto3" json:"scope,omitempty"`
	// 过滤条件组
	Filter        *FilterGroup `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterCustomerIdsRequest) Reset() {
	*x = FilterCustomerIdsRequest{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterCustomerIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterCustomerIdsRequest) ProtoMessage() {}

func (x *FilterCustomerIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterCustomerIdsRequest.ProtoReflect.Descriptor instead.
func (*FilterCustomerIdsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{19}
}

func (x *FilterCustomerIdsRequest) GetScope() *Scope {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *FilterCustomerIdsRequest) GetFilter() *FilterGroup {
	if x != nil {
		return x.Filter
	}
	return nil
}

// (-- api-linter: core::0158::response-plural-first-field=disabled
//
//	aip.dev/not-precedent: 返回ID集合容器即可 --)
//
// 过滤客户ID响应
type FilterCustomerIdsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID列表
	CustomerIds   []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterCustomerIdsResponse) Reset() {
	*x = FilterCustomerIdsResponse{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterCustomerIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterCustomerIdsResponse) ProtoMessage() {}

func (x *FilterCustomerIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterCustomerIdsResponse.ProtoReflect.Descriptor instead.
func (*FilterCustomerIdsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{20}
}

func (x *FilterCustomerIdsResponse) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

// 客户：校验有效客户ID
type ValidateActiveCustomerIdsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 客户ID列表
	CustomerIds   []int64 `protobuf:"varint,2,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateActiveCustomerIdsRequest) Reset() {
	*x = ValidateActiveCustomerIdsRequest{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateActiveCustomerIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateActiveCustomerIdsRequest) ProtoMessage() {}

func (x *ValidateActiveCustomerIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateActiveCustomerIdsRequest.ProtoReflect.Descriptor instead.
func (*ValidateActiveCustomerIdsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{21}
}

func (x *ValidateActiveCustomerIdsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ValidateActiveCustomerIdsRequest) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

// (-- api-linter: core::0158::response-plural-first-field=disabled
//
//	aip.dev/not-precedent: 返回ID集合容器即可 --)
//
// 验证活跃客户ID响应
type ValidateActiveCustomerIdsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 有效的客户ID列表
	CustomerIds   []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateActiveCustomerIdsResponse) Reset() {
	*x = ValidateActiveCustomerIdsResponse{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateActiveCustomerIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateActiveCustomerIdsResponse) ProtoMessage() {}

func (x *ValidateActiveCustomerIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateActiveCustomerIdsResponse.ProtoReflect.Descriptor instead.
func (*ValidateActiveCustomerIdsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{22}
}

func (x *ValidateActiveCustomerIdsResponse) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

// 客户：获取基础信息列表
type GetCustomersBasicInfoRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID列表
	CustomerIds   []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCustomersBasicInfoRequest) Reset() {
	*x = GetCustomersBasicInfoRequest{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustomersBasicInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomersBasicInfoRequest) ProtoMessage() {}

func (x *GetCustomersBasicInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomersBasicInfoRequest.ProtoReflect.Descriptor instead.
func (*GetCustomersBasicInfoRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{23}
}

func (x *GetCustomersBasicInfoRequest) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

// 获取客户基础信息响应
type GetCustomersBasicInfoResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户基础信息列表
	Customers     []*CustomerBasicInfo `protobuf:"bytes,1,rep,name=customers,proto3" json:"customers,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCustomersBasicInfoResponse) Reset() {
	*x = GetCustomersBasicInfoResponse{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustomersBasicInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomersBasicInfoResponse) ProtoMessage() {}

func (x *GetCustomersBasicInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomersBasicInfoResponse.ProtoReflect.Descriptor instead.
func (*GetCustomersBasicInfoResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{24}
}

func (x *GetCustomersBasicInfoResponse) GetCustomers() []*CustomerBasicInfo {
	if x != nil {
		return x.Customers
	}
	return nil
}

// 客户：排序+分页
type ListCustomerIdsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// (-- api-linter: core::0132::request-parent-required=disabled
	//
	//	aip.dev/not-precedent: 使用 Scope 指定 company_id --)
	//
	// 查询作用域
	Scope *Scope `protobuf:"bytes,1,opt,name=scope,proto3" json:"scope,omitempty"`
	// 排序字段
	OrderBy CustomerOrderField `protobuf:"varint,2,opt,name=order_by,json=orderBy,proto3,enum=backend.proto.customer.v1.CustomerOrderField" json:"order_by,omitempty"`
	// 排序方向
	Direction OrderDirection `protobuf:"varint,3,opt,name=direction,proto3,enum=backend.proto.customer.v1.OrderDirection" json:"direction,omitempty"`
	// 分页参数
	Page *OffsetLimit `protobuf:"bytes,4,opt,name=page,proto3" json:"page,omitempty"`
	// for linter compatibility, not used
	PageSize *int32 `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3,oneof" json:"page_size,omitempty"`
	// 页面令牌
	PageToken     *string `protobuf:"bytes,6,opt,name=page_token,json=pageToken,proto3,oneof" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomerIdsRequest) Reset() {
	*x = ListCustomerIdsRequest{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomerIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerIdsRequest) ProtoMessage() {}

func (x *ListCustomerIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerIdsRequest.ProtoReflect.Descriptor instead.
func (*ListCustomerIdsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{25}
}

func (x *ListCustomerIdsRequest) GetScope() *Scope {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *ListCustomerIdsRequest) GetOrderBy() CustomerOrderField {
	if x != nil {
		return x.OrderBy
	}
	return CustomerOrderField_CUSTOMER_ORDER_FIELD_UNSPECIFIED
}

func (x *ListCustomerIdsRequest) GetDirection() OrderDirection {
	if x != nil {
		return x.Direction
	}
	return OrderDirection_ORDER_DIRECTION_UNSPECIFIED
}

func (x *ListCustomerIdsRequest) GetPage() *OffsetLimit {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *ListCustomerIdsRequest) GetPageSize() int32 {
	if x != nil && x.PageSize != nil {
		return *x.PageSize
	}
	return 0
}

func (x *ListCustomerIdsRequest) GetPageToken() string {
	if x != nil && x.PageToken != nil {
		return *x.PageToken
	}
	return ""
}

// (-- api-linter: core::0132::response-unknown-fields=disabled
//
//	aip.dev/not-precedent: 内部查询接口返回简化结构 --)
//
// 列出客户ID响应
type ListCustomerIdsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID列表
	CustomerIds []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	// 下一页令牌
	NextPageToken *string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3,oneof" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomerIdsResponse) Reset() {
	*x = ListCustomerIdsResponse{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomerIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerIdsResponse) ProtoMessage() {}

func (x *ListCustomerIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerIdsResponse.ProtoReflect.Descriptor instead.
func (*ListCustomerIdsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{26}
}

func (x *ListCustomerIdsResponse) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

func (x *ListCustomerIdsResponse) GetNextPageToken() string {
	if x != nil && x.NextPageToken != nil {
		return *x.NextPageToken
	}
	return ""
}

// ====== Contact ======
// 联系方式：按电话/邮箱/姓名关键词
type SearchContactCustomerIdsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 查询作用域
	Scope *Scope `protobuf:"bytes,1,opt,name=scope,proto3" json:"scope,omitempty"`
	// email 和 电话(自动提取数字)
	Keyword string `protobuf:"bytes,2,opt,name=keyword,proto3" json:"keyword,omitempty"`
	// 可选，first_name + last_name
	NameKeyword string `protobuf:"bytes,3,opt,name=name_keyword,json=nameKeyword,proto3" json:"name_keyword,omitempty"`
	// optional paging fields (unused)
	PageSize *int32 `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3,oneof" json:"page_size,omitempty"`
	// 页面令牌
	PageToken     *string `protobuf:"bytes,5,opt,name=page_token,json=pageToken,proto3,oneof" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchContactCustomerIdsRequest) Reset() {
	*x = SearchContactCustomerIdsRequest{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchContactCustomerIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchContactCustomerIdsRequest) ProtoMessage() {}

func (x *SearchContactCustomerIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchContactCustomerIdsRequest.ProtoReflect.Descriptor instead.
func (*SearchContactCustomerIdsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{27}
}

func (x *SearchContactCustomerIdsRequest) GetScope() *Scope {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *SearchContactCustomerIdsRequest) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *SearchContactCustomerIdsRequest) GetNameKeyword() string {
	if x != nil {
		return x.NameKeyword
	}
	return ""
}

func (x *SearchContactCustomerIdsRequest) GetPageSize() int32 {
	if x != nil && x.PageSize != nil {
		return *x.PageSize
	}
	return 0
}

func (x *SearchContactCustomerIdsRequest) GetPageToken() string {
	if x != nil && x.PageToken != nil {
		return *x.PageToken
	}
	return ""
}

// 搜索联系方式客户ID响应
type SearchContactCustomerIdsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID列表
	CustomerIds []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	// 下一页令牌
	NextPageToken *string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3,oneof" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchContactCustomerIdsResponse) Reset() {
	*x = SearchContactCustomerIdsResponse{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchContactCustomerIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchContactCustomerIdsResponse) ProtoMessage() {}

func (x *SearchContactCustomerIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchContactCustomerIdsResponse.ProtoReflect.Descriptor instead.
func (*SearchContactCustomerIdsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{28}
}

func (x *SearchContactCustomerIdsResponse) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

func (x *SearchContactCustomerIdsResponse) GetNextPageToken() string {
	if x != nil && x.NextPageToken != nil {
		return *x.NextPageToken
	}
	return ""
}

// 按联系方式过滤客户ID请求
type FilterContactCustomerIdsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 查询作用域
	Scope *Scope `protobuf:"bytes,1,opt,name=scope,proto3" json:"scope,omitempty"`
	// 过滤条件组
	Filter        *FilterGroup `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterContactCustomerIdsRequest) Reset() {
	*x = FilterContactCustomerIdsRequest{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterContactCustomerIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterContactCustomerIdsRequest) ProtoMessage() {}

func (x *FilterContactCustomerIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterContactCustomerIdsRequest.ProtoReflect.Descriptor instead.
func (*FilterContactCustomerIdsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{29}
}

func (x *FilterContactCustomerIdsRequest) GetScope() *Scope {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *FilterContactCustomerIdsRequest) GetFilter() *FilterGroup {
	if x != nil {
		return x.Filter
	}
	return nil
}

// 按联系方式过滤客户ID响应
type FilterContactCustomerIdsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID列表
	CustomerIds   []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterContactCustomerIdsResponse) Reset() {
	*x = FilterContactCustomerIdsResponse{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterContactCustomerIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterContactCustomerIdsResponse) ProtoMessage() {}

func (x *FilterContactCustomerIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterContactCustomerIdsResponse.ProtoReflect.Descriptor instead.
func (*FilterContactCustomerIdsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{30}
}

func (x *FilterContactCustomerIdsResponse) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

// 联系方式：计数型过滤（如 email_cnt > 0）
type CountFilterContactCustomerIdsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 查询作用域
	Scope *Scope `protobuf:"bytes,1,opt,name=scope,proto3" json:"scope,omitempty"`
	// HAVING 条件，服务端支持: contact_count, email_count, phone_count
	Having        []*FilterCondition `protobuf:"bytes,2,rep,name=having,proto3" json:"having,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CountFilterContactCustomerIdsRequest) Reset() {
	*x = CountFilterContactCustomerIdsRequest{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountFilterContactCustomerIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountFilterContactCustomerIdsRequest) ProtoMessage() {}

func (x *CountFilterContactCustomerIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountFilterContactCustomerIdsRequest.ProtoReflect.Descriptor instead.
func (*CountFilterContactCustomerIdsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{31}
}

func (x *CountFilterContactCustomerIdsRequest) GetScope() *Scope {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *CountFilterContactCustomerIdsRequest) GetHaving() []*FilterCondition {
	if x != nil {
		return x.Having
	}
	return nil
}

// 按联系方式计数过滤客户ID响应
type CountFilterContactCustomerIdsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID列表
	CustomerIds   []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CountFilterContactCustomerIdsResponse) Reset() {
	*x = CountFilterContactCustomerIdsResponse{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountFilterContactCustomerIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountFilterContactCustomerIdsResponse) ProtoMessage() {}

func (x *CountFilterContactCustomerIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountFilterContactCustomerIdsResponse.ProtoReflect.Descriptor instead.
func (*CountFilterContactCustomerIdsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{32}
}

func (x *CountFilterContactCustomerIdsResponse) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

// 获取主要电话号码请求
type GetPrimaryPhonesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 查询作用域
	Scope *Scope `protobuf:"bytes,1,opt,name=scope,proto3" json:"scope,omitempty"`
	// 客户ID列表
	CustomerIds   []int64 `protobuf:"varint,2,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPrimaryPhonesRequest) Reset() {
	*x = GetPrimaryPhonesRequest{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPrimaryPhonesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPrimaryPhonesRequest) ProtoMessage() {}

func (x *GetPrimaryPhonesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPrimaryPhonesRequest.ProtoReflect.Descriptor instead.
func (*GetPrimaryPhonesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{33}
}

func (x *GetPrimaryPhonesRequest) GetScope() *Scope {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *GetPrimaryPhonesRequest) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

// 获取主要电话号码响应
type GetPrimaryPhonesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户电话列表
	Phones        []*CustomerPhone `protobuf:"bytes,1,rep,name=phones,proto3" json:"phones,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPrimaryPhonesResponse) Reset() {
	*x = GetPrimaryPhonesResponse{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPrimaryPhonesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPrimaryPhonesResponse) ProtoMessage() {}

func (x *GetPrimaryPhonesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPrimaryPhonesResponse.ProtoReflect.Descriptor instead.
func (*GetPrimaryPhonesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{34}
}

func (x *GetPrimaryPhonesResponse) GetPhones() []*CustomerPhone {
	if x != nil {
		return x.Phones
	}
	return nil
}

// ====== Address ======
// 地址：整行地址拼接模糊
type SearchAddressCustomerIdsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 查询作用域
	Scope *Scope `protobuf:"bytes,1,opt,name=scope,proto3" json:"scope,omitempty"`
	// 地址关键词
	Keyword string `protobuf:"bytes,2,opt,name=keyword,proto3" json:"keyword,omitempty"`
	// optional paging fields (unused)
	PageSize *int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3,oneof" json:"page_size,omitempty"`
	// 页面令牌
	PageToken     *string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3,oneof" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchAddressCustomerIdsRequest) Reset() {
	*x = SearchAddressCustomerIdsRequest{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchAddressCustomerIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchAddressCustomerIdsRequest) ProtoMessage() {}

func (x *SearchAddressCustomerIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchAddressCustomerIdsRequest.ProtoReflect.Descriptor instead.
func (*SearchAddressCustomerIdsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{35}
}

func (x *SearchAddressCustomerIdsRequest) GetScope() *Scope {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *SearchAddressCustomerIdsRequest) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *SearchAddressCustomerIdsRequest) GetPageSize() int32 {
	if x != nil && x.PageSize != nil {
		return *x.PageSize
	}
	return 0
}

func (x *SearchAddressCustomerIdsRequest) GetPageToken() string {
	if x != nil && x.PageToken != nil {
		return *x.PageToken
	}
	return ""
}

// 搜索地址客户ID响应
type SearchAddressCustomerIdsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID列表
	CustomerIds []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	// 下一页令牌
	NextPageToken *string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3,oneof" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchAddressCustomerIdsResponse) Reset() {
	*x = SearchAddressCustomerIdsResponse{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchAddressCustomerIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchAddressCustomerIdsResponse) ProtoMessage() {}

func (x *SearchAddressCustomerIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchAddressCustomerIdsResponse.ProtoReflect.Descriptor instead.
func (*SearchAddressCustomerIdsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{36}
}

func (x *SearchAddressCustomerIdsResponse) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

func (x *SearchAddressCustomerIdsResponse) GetNextPageToken() string {
	if x != nil && x.NextPageToken != nil {
		return *x.NextPageToken
	}
	return ""
}

// 地址：zipcode IN / NOT IN 过滤
type FilterCustomerIdsByZipRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 查询作用域
	Scope *Scope `protobuf:"bytes,1,opt,name=scope,proto3" json:"scope,omitempty"`
	// 邮编列表
	ZipList []string `protobuf:"bytes,2,rep,name=zip_list,json=zipList,proto3" json:"zip_list,omitempty"`
	// 操作符
	Operator      InNotInOperator `protobuf:"varint,3,opt,name=operator,proto3,enum=backend.proto.customer.v1.InNotInOperator" json:"operator,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterCustomerIdsByZipRequest) Reset() {
	*x = FilterCustomerIdsByZipRequest{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterCustomerIdsByZipRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterCustomerIdsByZipRequest) ProtoMessage() {}

func (x *FilterCustomerIdsByZipRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterCustomerIdsByZipRequest.ProtoReflect.Descriptor instead.
func (*FilterCustomerIdsByZipRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{37}
}

func (x *FilterCustomerIdsByZipRequest) GetScope() *Scope {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *FilterCustomerIdsByZipRequest) GetZipList() []string {
	if x != nil {
		return x.ZipList
	}
	return nil
}

func (x *FilterCustomerIdsByZipRequest) GetOperator() InNotInOperator {
	if x != nil {
		return x.Operator
	}
	return InNotInOperator_IN_NOT_IN_OPERATOR_UNSPECIFIED
}

// 按邮编过滤客户ID响应
type FilterCustomerIdsByZipResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID列表
	CustomerIds   []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterCustomerIdsByZipResponse) Reset() {
	*x = FilterCustomerIdsByZipResponse{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterCustomerIdsByZipResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterCustomerIdsByZipResponse) ProtoMessage() {}

func (x *FilterCustomerIdsByZipResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterCustomerIdsByZipResponse.ProtoReflect.Descriptor instead.
func (*FilterCustomerIdsByZipResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{38}
}

func (x *FilterCustomerIdsByZipResponse) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

// 按地址计数过滤客户ID请求
type CountFilterAddressCustomerIdsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 查询作用域
	Scope *Scope `protobuf:"bytes,1,opt,name=scope,proto3" json:"scope,omitempty"`
	// HAVING 条件
	Having        []*FilterCondition `protobuf:"bytes,2,rep,name=having,proto3" json:"having,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CountFilterAddressCustomerIdsRequest) Reset() {
	*x = CountFilterAddressCustomerIdsRequest{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountFilterAddressCustomerIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountFilterAddressCustomerIdsRequest) ProtoMessage() {}

func (x *CountFilterAddressCustomerIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountFilterAddressCustomerIdsRequest.ProtoReflect.Descriptor instead.
func (*CountFilterAddressCustomerIdsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{39}
}

func (x *CountFilterAddressCustomerIdsRequest) GetScope() *Scope {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *CountFilterAddressCustomerIdsRequest) GetHaving() []*FilterCondition {
	if x != nil {
		return x.Having
	}
	return nil
}

// 按地址计数过滤客户ID响应
type CountFilterAddressCustomerIdsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID列表
	CustomerIds   []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CountFilterAddressCustomerIdsResponse) Reset() {
	*x = CountFilterAddressCustomerIdsResponse{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountFilterAddressCustomerIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountFilterAddressCustomerIdsResponse) ProtoMessage() {}

func (x *CountFilterAddressCustomerIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountFilterAddressCustomerIdsResponse.ProtoReflect.Descriptor instead.
func (*CountFilterAddressCustomerIdsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{40}
}

func (x *CountFilterAddressCustomerIdsResponse) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

// ====== Pet ======
// 宠物：宠物名/品种模糊
type SearchPetCustomerIdsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 查询作用域
	Scope *Scope `protobuf:"bytes,1,opt,name=scope,proto3" json:"scope,omitempty"`
	// 宠物关键词
	Keyword string `protobuf:"bytes,2,opt,name=keyword,proto3" json:"keyword,omitempty"`
	// optional paging fields (unused)
	PageSize *int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3,oneof" json:"page_size,omitempty"`
	// 页面令牌
	PageToken     *string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3,oneof" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchPetCustomerIdsRequest) Reset() {
	*x = SearchPetCustomerIdsRequest{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchPetCustomerIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPetCustomerIdsRequest) ProtoMessage() {}

func (x *SearchPetCustomerIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPetCustomerIdsRequest.ProtoReflect.Descriptor instead.
func (*SearchPetCustomerIdsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{41}
}

func (x *SearchPetCustomerIdsRequest) GetScope() *Scope {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *SearchPetCustomerIdsRequest) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *SearchPetCustomerIdsRequest) GetPageSize() int32 {
	if x != nil && x.PageSize != nil {
		return *x.PageSize
	}
	return 0
}

func (x *SearchPetCustomerIdsRequest) GetPageToken() string {
	if x != nil && x.PageToken != nil {
		return *x.PageToken
	}
	return ""
}

// 搜索宠物客户ID响应
type SearchPetCustomerIdsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID列表
	CustomerIds []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	// 下一页令牌
	NextPageToken *string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3,oneof" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchPetCustomerIdsResponse) Reset() {
	*x = SearchPetCustomerIdsResponse{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchPetCustomerIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPetCustomerIdsResponse) ProtoMessage() {}

func (x *SearchPetCustomerIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPetCustomerIdsResponse.ProtoReflect.Descriptor instead.
func (*SearchPetCustomerIdsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{42}
}

func (x *SearchPetCustomerIdsResponse) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

func (x *SearchPetCustomerIdsResponse) GetNextPageToken() string {
	if x != nil && x.NextPageToken != nil {
		return *x.NextPageToken
	}
	return ""
}

// 宠物：pet_name FULLTEXT
type SearchPetsFulltextRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 查询作用域
	Scope *Scope `protobuf:"bytes,1,opt,name=scope,proto3" json:"scope,omitempty"`
	// 搜索术语
	Term string `protobuf:"bytes,2,opt,name=term,proto3" json:"term,omitempty"`
	// 页面大小
	PageSize *int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3,oneof" json:"page_size,omitempty"`
	// 页面令牌
	PageToken     *string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3,oneof" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchPetsFulltextRequest) Reset() {
	*x = SearchPetsFulltextRequest{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchPetsFulltextRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPetsFulltextRequest) ProtoMessage() {}

func (x *SearchPetsFulltextRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPetsFulltextRequest.ProtoReflect.Descriptor instead.
func (*SearchPetsFulltextRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{43}
}

func (x *SearchPetsFulltextRequest) GetScope() *Scope {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *SearchPetsFulltextRequest) GetTerm() string {
	if x != nil {
		return x.Term
	}
	return ""
}

func (x *SearchPetsFulltextRequest) GetPageSize() int32 {
	if x != nil && x.PageSize != nil {
		return *x.PageSize
	}
	return 0
}

func (x *SearchPetsFulltextRequest) GetPageToken() string {
	if x != nil && x.PageToken != nil {
		return *x.PageToken
	}
	return ""
}

// 宠物全文搜索响应
type SearchPetsFulltextResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 宠物记录列表
	Pets []*PetRecord `protobuf:"bytes,1,rep,name=pets,proto3" json:"pets,omitempty"`
	// 下一页令牌
	NextPageToken *string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3,oneof" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchPetsFulltextResponse) Reset() {
	*x = SearchPetsFulltextResponse{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchPetsFulltextResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPetsFulltextResponse) ProtoMessage() {}

func (x *SearchPetsFulltextResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPetsFulltextResponse.ProtoReflect.Descriptor instead.
func (*SearchPetsFulltextResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{44}
}

func (x *SearchPetsFulltextResponse) GetPets() []*PetRecord {
	if x != nil {
		return x.Pets
	}
	return nil
}

func (x *SearchPetsFulltextResponse) GetNextPageToken() string {
	if x != nil && x.NextPageToken != nil {
		return *x.NextPageToken
	}
	return ""
}

// 宠物：条件过滤
type FilterPetCustomerIdsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 查询作用域
	Scope *Scope `protobuf:"bytes,1,opt,name=scope,proto3" json:"scope,omitempty"`
	// 过滤条件组
	Filter        *FilterGroup `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterPetCustomerIdsRequest) Reset() {
	*x = FilterPetCustomerIdsRequest{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterPetCustomerIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterPetCustomerIdsRequest) ProtoMessage() {}

func (x *FilterPetCustomerIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterPetCustomerIdsRequest.ProtoReflect.Descriptor instead.
func (*FilterPetCustomerIdsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{45}
}

func (x *FilterPetCustomerIdsRequest) GetScope() *Scope {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *FilterPetCustomerIdsRequest) GetFilter() *FilterGroup {
	if x != nil {
		return x.Filter
	}
	return nil
}

// 按宠物过滤客户ID响应
type FilterPetCustomerIdsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID列表
	CustomerIds   []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterPetCustomerIdsResponse) Reset() {
	*x = FilterPetCustomerIdsResponse{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterPetCustomerIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterPetCustomerIdsResponse) ProtoMessage() {}

func (x *FilterPetCustomerIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterPetCustomerIdsResponse.ProtoReflect.Descriptor instead.
func (*FilterPetCustomerIdsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{46}
}

func (x *FilterPetCustomerIdsResponse) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

// 宠物：计数型过滤
type CountFilterPetCustomerIdsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 查询作用域
	Scope *Scope `protobuf:"bytes,1,opt,name=scope,proto3" json:"scope,omitempty"`
	// HAVING 条件
	Having        []*FilterCondition `protobuf:"bytes,2,rep,name=having,proto3" json:"having,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CountFilterPetCustomerIdsRequest) Reset() {
	*x = CountFilterPetCustomerIdsRequest{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountFilterPetCustomerIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountFilterPetCustomerIdsRequest) ProtoMessage() {}

func (x *CountFilterPetCustomerIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountFilterPetCustomerIdsRequest.ProtoReflect.Descriptor instead.
func (*CountFilterPetCustomerIdsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{47}
}

func (x *CountFilterPetCustomerIdsRequest) GetScope() *Scope {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *CountFilterPetCustomerIdsRequest) GetHaving() []*FilterCondition {
	if x != nil {
		return x.Having
	}
	return nil
}

// 按宠物计数过滤客户ID响应
type CountFilterPetCustomerIdsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID列表
	CustomerIds   []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CountFilterPetCustomerIdsResponse) Reset() {
	*x = CountFilterPetCustomerIdsResponse{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountFilterPetCustomerIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountFilterPetCustomerIdsResponse) ProtoMessage() {}

func (x *CountFilterPetCustomerIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountFilterPetCustomerIdsResponse.ProtoReflect.Descriptor instead.
func (*CountFilterPetCustomerIdsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{48}
}

func (x *CountFilterPetCustomerIdsResponse) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

// 宠物：数量分布（按类型）
type GetPetTypeDistributionRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 查询作用域
	Scope *Scope `protobuf:"bytes,1,opt,name=scope,proto3" json:"scope,omitempty"`
	// 客户ID列表
	CustomerIds   []int64 `protobuf:"varint,2,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPetTypeDistributionRequest) Reset() {
	*x = GetPetTypeDistributionRequest{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPetTypeDistributionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetTypeDistributionRequest) ProtoMessage() {}

func (x *GetPetTypeDistributionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetTypeDistributionRequest.ProtoReflect.Descriptor instead.
func (*GetPetTypeDistributionRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{49}
}

func (x *GetPetTypeDistributionRequest) GetScope() *Scope {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *GetPetTypeDistributionRequest) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

// 获取宠物类型分布响应
type PetTypeDistribution struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 宠物类型计数列表
	Items         []*PetTypeCount `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PetTypeDistribution) Reset() {
	*x = PetTypeDistribution{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetTypeDistribution) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetTypeDistribution) ProtoMessage() {}

func (x *PetTypeDistribution) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetTypeDistribution.ProtoReflect.Descriptor instead.
func (*PetTypeDistribution) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{50}
}

func (x *PetTypeDistribution) GetItems() []*PetTypeCount {
	if x != nil {
		return x.Items
	}
	return nil
}

// 宠物：列表装配
// (-- api-linter: core::0132::request-parent-required=disabled
//
//	aip.dev/not-precedent: 使用 Scope 指定 company_id --)
type ListPetsForCustomersRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 查询作用域
	Scope *Scope `protobuf:"bytes,1,opt,name=scope,proto3" json:"scope,omitempty"`
	// 客户ID列表
	CustomerIds []int64 `protobuf:"varint,2,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	// optional paging fields (unused)
	PageSize *int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3,oneof" json:"page_size,omitempty"`
	// 页面令牌
	PageToken     *string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3,oneof" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPetsForCustomersRequest) Reset() {
	*x = ListPetsForCustomersRequest{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPetsForCustomersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetsForCustomersRequest) ProtoMessage() {}

func (x *ListPetsForCustomersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetsForCustomersRequest.ProtoReflect.Descriptor instead.
func (*ListPetsForCustomersRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{51}
}

func (x *ListPetsForCustomersRequest) GetScope() *Scope {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *ListPetsForCustomersRequest) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

func (x *ListPetsForCustomersRequest) GetPageSize() int32 {
	if x != nil && x.PageSize != nil {
		return *x.PageSize
	}
	return 0
}

func (x *ListPetsForCustomersRequest) GetPageToken() string {
	if x != nil && x.PageToken != nil {
		return *x.PageToken
	}
	return ""
}

// 列出客户宠物响应
// (-- api-linter: core::0132::response-unknown-fields=disabled
//
//	aip.dev/not-precedent: 内部查询接口返回简化结构 --)
type ListPetsForCustomersResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 宠物信息列表
	Pets []*PetInfo `protobuf:"bytes,1,rep,name=pets,proto3" json:"pets,omitempty"`
	// 下一页令牌
	NextPageToken *string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3,oneof" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPetsForCustomersResponse) Reset() {
	*x = ListPetsForCustomersResponse{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPetsForCustomersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetsForCustomersResponse) ProtoMessage() {}

func (x *ListPetsForCustomersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetsForCustomersResponse.ProtoReflect.Descriptor instead.
func (*ListPetsForCustomersResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{52}
}

func (x *ListPetsForCustomersResponse) GetPets() []*PetInfo {
	if x != nil {
		return x.Pets
	}
	return nil
}

func (x *ListPetsForCustomersResponse) GetNextPageToken() string {
	if x != nil && x.NextPageToken != nil {
		return *x.NextPageToken
	}
	return ""
}

// ====== Tag Binding ======
// 标签绑定：IN / NOT IN
type FilterCustomerIdsByTagRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 查询作用域
	Scope *Scope `protobuf:"bytes,1,opt,name=scope,proto3" json:"scope,omitempty"`
	// 标签ID列表
	TagIds []int64 `protobuf:"varint,2,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
	// 操作符
	Operator      InNotInOperator `protobuf:"varint,3,opt,name=operator,proto3,enum=backend.proto.customer.v1.InNotInOperator" json:"operator,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterCustomerIdsByTagRequest) Reset() {
	*x = FilterCustomerIdsByTagRequest{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterCustomerIdsByTagRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterCustomerIdsByTagRequest) ProtoMessage() {}

func (x *FilterCustomerIdsByTagRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterCustomerIdsByTagRequest.ProtoReflect.Descriptor instead.
func (*FilterCustomerIdsByTagRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{53}
}

func (x *FilterCustomerIdsByTagRequest) GetScope() *Scope {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *FilterCustomerIdsByTagRequest) GetTagIds() []int64 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

func (x *FilterCustomerIdsByTagRequest) GetOperator() InNotInOperator {
	if x != nil {
		return x.Operator
	}
	return InNotInOperator_IN_NOT_IN_OPERATOR_UNSPECIFIED
}

// 按标签绑定过滤客户ID响应
type FilterCustomerIdsByTagResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID列表
	CustomerIds   []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterCustomerIdsByTagResponse) Reset() {
	*x = FilterCustomerIdsByTagResponse{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterCustomerIdsByTagResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterCustomerIdsByTagResponse) ProtoMessage() {}

func (x *FilterCustomerIdsByTagResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterCustomerIdsByTagResponse.ProtoReflect.Descriptor instead.
func (*FilterCustomerIdsByTagResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{54}
}

func (x *FilterCustomerIdsByTagResponse) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

// ====== Pet Code Binding ======
// 宠物编码/芯片绑定：IN / NOT IN
type FilterCustomerIdsByPetCodeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 查询作用域
	Scope *Scope `protobuf:"bytes,1,opt,name=scope,proto3" json:"scope,omitempty"`
	// 编码ID列表
	CodeIds []int64 `protobuf:"varint,2,rep,packed,name=code_ids,json=codeIds,proto3" json:"code_ids,omitempty"`
	// 操作符
	Operator      InNotInOperator `protobuf:"varint,3,opt,name=operator,proto3,enum=backend.proto.customer.v1.InNotInOperator" json:"operator,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterCustomerIdsByPetCodeRequest) Reset() {
	*x = FilterCustomerIdsByPetCodeRequest{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterCustomerIdsByPetCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterCustomerIdsByPetCodeRequest) ProtoMessage() {}

func (x *FilterCustomerIdsByPetCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterCustomerIdsByPetCodeRequest.ProtoReflect.Descriptor instead.
func (*FilterCustomerIdsByPetCodeRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{55}
}

func (x *FilterCustomerIdsByPetCodeRequest) GetScope() *Scope {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *FilterCustomerIdsByPetCodeRequest) GetCodeIds() []int64 {
	if x != nil {
		return x.CodeIds
	}
	return nil
}

func (x *FilterCustomerIdsByPetCodeRequest) GetOperator() InNotInOperator {
	if x != nil {
		return x.Operator
	}
	return InNotInOperator_IN_NOT_IN_OPERATOR_UNSPECIFIED
}

// 按宠物编码绑定过滤客户ID响应
type FilterCustomerIdsByPetCodeResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID列表
	CustomerIds   []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterCustomerIdsByPetCodeResponse) Reset() {
	*x = FilterCustomerIdsByPetCodeResponse{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterCustomerIdsByPetCodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterCustomerIdsByPetCodeResponse) ProtoMessage() {}

func (x *FilterCustomerIdsByPetCodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterCustomerIdsByPetCodeResponse.ProtoReflect.Descriptor instead.
func (*FilterCustomerIdsByPetCodeResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{56}
}

func (x *FilterCustomerIdsByPetCodeResponse) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

// ====== Pet Vaccine Binding ======
// 宠物疫苗绑定：计数型过滤（过期数等）
type CountFilterCustomerIdsByVaccineRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 查询作用域
	Scope *Scope `protobuf:"bytes,1,opt,name=scope,proto3" json:"scope,omitempty"`
	// 比较 vb.expiration_date < current_date
	CurrentTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=current_time,json=currentTime,proto3" json:"current_time,omitempty"`
	// HAVING 条件，支持: expired_vaccine_count
	Having        []*FilterCondition `protobuf:"bytes,3,rep,name=having,proto3" json:"having,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CountFilterCustomerIdsByVaccineRequest) Reset() {
	*x = CountFilterCustomerIdsByVaccineRequest{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountFilterCustomerIdsByVaccineRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountFilterCustomerIdsByVaccineRequest) ProtoMessage() {}

func (x *CountFilterCustomerIdsByVaccineRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountFilterCustomerIdsByVaccineRequest.ProtoReflect.Descriptor instead.
func (*CountFilterCustomerIdsByVaccineRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{57}
}

func (x *CountFilterCustomerIdsByVaccineRequest) GetScope() *Scope {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *CountFilterCustomerIdsByVaccineRequest) GetCurrentTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CurrentTime
	}
	return nil
}

func (x *CountFilterCustomerIdsByVaccineRequest) GetHaving() []*FilterCondition {
	if x != nil {
		return x.Having
	}
	return nil
}

// 按疫苗计数过滤客户ID响应
type CountFilterCustomerIdsByVaccineResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID列表
	CustomerIds   []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CountFilterCustomerIdsByVaccineResponse) Reset() {
	*x = CountFilterCustomerIdsByVaccineResponse{}
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountFilterCustomerIdsByVaccineResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountFilterCustomerIdsByVaccineResponse) ProtoMessage() {}

func (x *CountFilterCustomerIdsByVaccineResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountFilterCustomerIdsByVaccineResponse.ProtoReflect.Descriptor instead.
func (*CountFilterCustomerIdsByVaccineResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP(), []int{58}
}

func (x *CountFilterCustomerIdsByVaccineResponse) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

var File_backend_proto_customer_v1_customer_query_service_proto protoreflect.FileDescriptor

const file_backend_proto_customer_v1_customer_query_service_proto_rawDesc = "" +
	"\n" +
	"6backend/proto/customer/v1/customer_query_service.proto\x12\x19backend.proto.customer.v1\x1a\x1fgoogle/protobuf/timestamp.proto\"\xe3\x01\n" +
	"\x0fFilterCondition\x12\x14\n" +
	"\x05field\x18\x01 \x01(\tR\x05field\x129\n" +
	"\x02op\x18\x02 \x01(\x0e2).backend.proto.customer.v1.FilterOperatorR\x02op\x12\x19\n" +
	"\x05value\x18\x03 \x01(\tH\x00R\x05value\x88\x01\x01\x12\x16\n" +
	"\x06values\x18\x04 \x03(\tR\x06values\x12\x19\n" +
	"\x05start\x18\x05 \x01(\tH\x01R\x05start\x88\x01\x01\x12\x15\n" +
	"\x03end\x18\x06 \x01(\tH\x02R\x03end\x88\x01\x01B\b\n" +
	"\x06_valueB\b\n" +
	"\x06_startB\x06\n" +
	"\x04_end\"\xb5\x01\n" +
	"\vFilterGroup\x12\x1a\n" +
	"\bcombiner\x18\x01 \x01(\tR\bcombiner\x12J\n" +
	"\n" +
	"conditions\x18\x02 \x03(\v2*.backend.proto.customer.v1.FilterConditionR\n" +
	"conditions\x12>\n" +
	"\x06groups\x18\x03 \x03(\v2&.backend.proto.customer.v1.FilterGroupR\x06groups\";\n" +
	"\vOffsetLimit\x12\x16\n" +
	"\x06offset\x18\x01 \x01(\x05R\x06offset\x12\x14\n" +
	"\x05limit\x18\x02 \x01(\x05R\x05limit\"l\n" +
	"\x05Scope\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\x12!\n" +
	"\fbusiness_ids\x18\x02 \x03(\x03R\vbusinessIds\x12!\n" +
	"\fcustomer_ids\x18\x03 \x03(\x03R\vcustomerIds\"\x1f\n" +
	"\vCustomerIds\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\x03R\x03ids\"S\n" +
	"\rCustomerPhone\x12\x1f\n" +
	"\vcustomer_id\x18\x01 \x01(\x03R\n" +
	"customerId\x12!\n" +
	"\fphone_number\x18\x02 \x01(\tR\vphoneNumber\"O\n" +
	"\rPrimaryPhones\x12>\n" +
	"\x05items\x18\x01 \x03(\v2(.backend.proto.customer.v1.CustomerPhoneR\x05items\"\xd5\x03\n" +
	"\x11CustomerBasicInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vbusiness_id\x18\x02 \x01(\x03R\n" +
	"businessId\x12\x14\n" +
	"\x05first\x18\x03 \x01(\tR\x05first\x12\x12\n" +
	"\x04last\x18\x04 \x01(\tR\x04last\x12\x1f\n" +
	"\vavatar_path\x18\x05 \x01(\tR\n" +
	"avatarPath\x12!\n" +
	"\fclient_color\x18\x06 \x01(\tR\vclientColor\x12\x14\n" +
	"\x05email\x18\a \x01(\tR\x05email\x128\n" +
	"\x18preferred_frequency_type\x18\b \x01(\x03R\x16preferredFrequencyType\x126\n" +
	"\x17preferred_frequency_day\x18\t \x01(\x03R\x15preferredFrequencyDay\x12\x1a\n" +
	"\binactive\x18\n" +
	" \x01(\x05R\binactive\x12'\n" +
	"\x0fis_unsubscribed\x18\v \x01(\x05R\x0eisUnsubscribed\x12\x16\n" +
	"\x06source\x18\f \x01(\tR\x06source\x12\x1d\n" +
	"\n" +
	"account_id\x18\r \x01(\x03R\taccountId\x12\x1d\n" +
	"\n" +
	"company_id\x18\x0e \x01(\x03R\tcompanyId\"X\n" +
	"\x12CustomerBasicInfos\x12B\n" +
	"\x05items\x18\x01 \x03(\v2,.backend.proto.customer.v1.CustomerBasicInfoR\x05items\"<\n" +
	"\tPetRecord\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vcustomer_id\x18\x02 \x01(\x03R\n" +
	"customerId\"H\n" +
	"\n" +
	"PetRecords\x12:\n" +
	"\x05items\x18\x01 \x03(\v2$.backend.proto.customer.v1.PetRecordR\x05items\"D\n" +
	"\fPetTypeCount\x12\x1e\n" +
	"\vpet_type_id\x18\x01 \x01(\x03R\tpetTypeId\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x03R\x05count\"N\n" +
	"\rPetTypeCounts\x12=\n" +
	"\x05items\x18\x01 \x03(\v2'.backend.proto.customer.v1.PetTypeCountR\x05items\"\xb7\x01\n" +
	"\aPetInfo\x12\x15\n" +
	"\x06pet_id\x18\x01 \x01(\x03R\x05petId\x12\x1f\n" +
	"\vcustomer_id\x18\x02 \x01(\x03R\n" +
	"customerId\x12\x10\n" +
	"\x03pet\x18\x03 \x01(\tR\x03pet\x12\x14\n" +
	"\x05breed\x18\x04 \x01(\tR\x05breed\x12\x1f\n" +
	"\vlife_status\x18\x05 \x01(\x05R\n" +
	"lifeStatus\x12+\n" +
	"\x11evaluation_status\x18\x06 \x01(\x05R\x10evaluationStatus\"D\n" +
	"\bPetInfos\x128\n" +
	"\x05items\x18\x01 \x03(\v2\".backend.proto.customer.v1.PetInfoR\x05items\"l\n" +
	"\x18SearchCustomerIdsRequest\x126\n" +
	"\x05scope\x18\x01 \x01(\v2 .backend.proto.customer.v1.ScopeR\x05scope\x12\x18\n" +
	"\akeyword\x18\x02 \x01(\tR\akeyword\"\x7f\n" +
	"\x19SearchCustomerIdsResponse\x12!\n" +
	"\fcustomer_ids\x18\x01 \x03(\x03R\vcustomerIds\x12+\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tH\x00R\rnextPageToken\x88\x01\x01B\x12\n" +
	"\x10_next_page_token\"v\n" +
	"\"SearchCustomerIdsByLastNameRequest\x126\n" +
	"\x05scope\x18\x01 \x01(\v2 .backend.proto.customer.v1.ScopeR\x05scope\x12\x18\n" +
	"\akeyword\x18\x02 \x01(\tR\akeyword\"\x89\x01\n" +
	"#SearchCustomerIdsByLastNameResponse\x12!\n" +
	"\fcustomer_ids\x18\x01 \x03(\x03R\vcustomerIds\x12+\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tH\x00R\rnextPageToken\x88\x01\x01B\x12\n" +
	"\x10_next_page_token\"\x92\x01\n" +
	"\x18FilterCustomerIdsRequest\x126\n" +
	"\x05scope\x18\x01 \x01(\v2 .backend.proto.customer.v1.ScopeR\x05scope\x12>\n" +
	"\x06filter\x18\x02 \x01(\v2&.backend.proto.customer.v1.FilterGroupR\x06filter\">\n" +
	"\x19FilterCustomerIdsResponse\x12!\n" +
	"\fcustomer_ids\x18\x01 \x03(\x03R\vcustomerIds\"d\n" +
	" ValidateActiveCustomerIdsRequest\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\x12!\n" +
	"\fcustomer_ids\x18\x02 \x03(\x03R\vcustomerIds\"F\n" +
	"!ValidateActiveCustomerIdsResponse\x12!\n" +
	"\fcustomer_ids\x18\x01 \x03(\x03R\vcustomerIds\"A\n" +
	"\x1cGetCustomersBasicInfoRequest\x12!\n" +
	"\fcustomer_ids\x18\x01 \x03(\x03R\vcustomerIds\"k\n" +
	"\x1dGetCustomersBasicInfoResponse\x12J\n" +
	"\tcustomers\x18\x01 \x03(\v2,.backend.proto.customer.v1.CustomerBasicInfoR\tcustomers\"\x82\x03\n" +
	"\x16ListCustomerIdsRequest\x126\n" +
	"\x05scope\x18\x01 \x01(\v2 .backend.proto.customer.v1.ScopeR\x05scope\x12H\n" +
	"\border_by\x18\x02 \x01(\x0e2-.backend.proto.customer.v1.CustomerOrderFieldR\aorderBy\x12G\n" +
	"\tdirection\x18\x03 \x01(\x0e2).backend.proto.customer.v1.OrderDirectionR\tdirection\x12:\n" +
	"\x04page\x18\x04 \x01(\v2&.backend.proto.customer.v1.OffsetLimitR\x04page\x12 \n" +
	"\tpage_size\x18\x05 \x01(\x05H\x00R\bpageSize\x88\x01\x01\x12\"\n" +
	"\n" +
	"page_token\x18\x06 \x01(\tH\x01R\tpageToken\x88\x01\x01B\f\n" +
	"\n" +
	"_page_sizeB\r\n" +
	"\v_page_token\"}\n" +
	"\x17ListCustomerIdsResponse\x12!\n" +
	"\fcustomer_ids\x18\x01 \x03(\x03R\vcustomerIds\x12+\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tH\x00R\rnextPageToken\x88\x01\x01B\x12\n" +
	"\x10_next_page_token\"\xf9\x01\n" +
	"\x1fSearchContactCustomerIdsRequest\x126\n" +
	"\x05scope\x18\x01 \x01(\v2 .backend.proto.customer.v1.ScopeR\x05scope\x12\x18\n" +
	"\akeyword\x18\x02 \x01(\tR\akeyword\x12!\n" +
	"\fname_keyword\x18\x03 \x01(\tR\vnameKeyword\x12 \n" +
	"\tpage_size\x18\x04 \x01(\x05H\x00R\bpageSize\x88\x01\x01\x12\"\n" +
	"\n" +
	"page_token\x18\x05 \x01(\tH\x01R\tpageToken\x88\x01\x01B\f\n" +
	"\n" +
	"_page_sizeB\r\n" +
	"\v_page_token\"\x86\x01\n" +
	" SearchContactCustomerIdsResponse\x12!\n" +
	"\fcustomer_ids\x18\x01 \x03(\x03R\vcustomerIds\x12+\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tH\x00R\rnextPageToken\x88\x01\x01B\x12\n" +
	"\x10_next_page_token\"\x99\x01\n" +
	"\x1fFilterContactCustomerIdsRequest\x126\n" +
	"\x05scope\x18\x01 \x01(\v2 .backend.proto.customer.v1.ScopeR\x05scope\x12>\n" +
	"\x06filter\x18\x02 \x01(\v2&.backend.proto.customer.v1.FilterGroupR\x06filter\"E\n" +
	" FilterContactCustomerIdsResponse\x12!\n" +
	"\fcustomer_ids\x18\x01 \x03(\x03R\vcustomerIds\"\xa2\x01\n" +
	"$CountFilterContactCustomerIdsRequest\x126\n" +
	"\x05scope\x18\x01 \x01(\v2 .backend.proto.customer.v1.ScopeR\x05scope\x12B\n" +
	"\x06having\x18\x02 \x03(\v2*.backend.proto.customer.v1.FilterConditionR\x06having\"J\n" +
	"%CountFilterContactCustomerIdsResponse\x12!\n" +
	"\fcustomer_ids\x18\x01 \x03(\x03R\vcustomerIds\"t\n" +
	"\x17GetPrimaryPhonesRequest\x126\n" +
	"\x05scope\x18\x01 \x01(\v2 .backend.proto.customer.v1.ScopeR\x05scope\x12!\n" +
	"\fcustomer_ids\x18\x02 \x03(\x03R\vcustomerIds\"\\\n" +
	"\x18GetPrimaryPhonesResponse\x12@\n" +
	"\x06phones\x18\x01 \x03(\v2(.backend.proto.customer.v1.CustomerPhoneR\x06phones\"\xd6\x01\n" +
	"\x1fSearchAddressCustomerIdsRequest\x126\n" +
	"\x05scope\x18\x01 \x01(\v2 .backend.proto.customer.v1.ScopeR\x05scope\x12\x18\n" +
	"\akeyword\x18\x02 \x01(\tR\akeyword\x12 \n" +
	"\tpage_size\x18\x03 \x01(\x05H\x00R\bpageSize\x88\x01\x01\x12\"\n" +
	"\n" +
	"page_token\x18\x04 \x01(\tH\x01R\tpageToken\x88\x01\x01B\f\n" +
	"\n" +
	"_page_sizeB\r\n" +
	"\v_page_token\"\x86\x01\n" +
	" SearchAddressCustomerIdsResponse\x12!\n" +
	"\fcustomer_ids\x18\x01 \x03(\x03R\vcustomerIds\x12+\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tH\x00R\rnextPageToken\x88\x01\x01B\x12\n" +
	"\x10_next_page_token\"\xba\x01\n" +
	"\x1dFilterCustomerIdsByZipRequest\x126\n" +
	"\x05scope\x18\x01 \x01(\v2 .backend.proto.customer.v1.ScopeR\x05scope\x12\x19\n" +
	"\bzip_list\x18\x02 \x03(\tR\azipList\x12F\n" +
	"\boperator\x18\x03 \x01(\x0e2*.backend.proto.customer.v1.InNotInOperatorR\boperator\"C\n" +
	"\x1eFilterCustomerIdsByZipResponse\x12!\n" +
	"\fcustomer_ids\x18\x01 \x03(\x03R\vcustomerIds\"\xa2\x01\n" +
	"$CountFilterAddressCustomerIdsRequest\x126\n" +
	"\x05scope\x18\x01 \x01(\v2 .backend.proto.customer.v1.ScopeR\x05scope\x12B\n" +
	"\x06having\x18\x02 \x03(\v2*.backend.proto.customer.v1.FilterConditionR\x06having\"J\n" +
	"%CountFilterAddressCustomerIdsResponse\x12!\n" +
	"\fcustomer_ids\x18\x01 \x03(\x03R\vcustomerIds\"\xd2\x01\n" +
	"\x1bSearchPetCustomerIdsRequest\x126\n" +
	"\x05scope\x18\x01 \x01(\v2 .backend.proto.customer.v1.ScopeR\x05scope\x12\x18\n" +
	"\akeyword\x18\x02 \x01(\tR\akeyword\x12 \n" +
	"\tpage_size\x18\x03 \x01(\x05H\x00R\bpageSize\x88\x01\x01\x12\"\n" +
	"\n" +
	"page_token\x18\x04 \x01(\tH\x01R\tpageToken\x88\x01\x01B\f\n" +
	"\n" +
	"_page_sizeB\r\n" +
	"\v_page_token\"\x82\x01\n" +
	"\x1cSearchPetCustomerIdsResponse\x12!\n" +
	"\fcustomer_ids\x18\x01 \x03(\x03R\vcustomerIds\x12+\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tH\x00R\rnextPageToken\x88\x01\x01B\x12\n" +
	"\x10_next_page_token\"\xca\x01\n" +
	"\x19SearchPetsFulltextRequest\x126\n" +
	"\x05scope\x18\x01 \x01(\v2 .backend.proto.customer.v1.ScopeR\x05scope\x12\x12\n" +
	"\x04term\x18\x02 \x01(\tR\x04term\x12 \n" +
	"\tpage_size\x18\x03 \x01(\x05H\x00R\bpageSize\x88\x01\x01\x12\"\n" +
	"\n" +
	"page_token\x18\x04 \x01(\tH\x01R\tpageToken\x88\x01\x01B\f\n" +
	"\n" +
	"_page_sizeB\r\n" +
	"\v_page_token\"\x97\x01\n" +
	"\x1aSearchPetsFulltextResponse\x128\n" +
	"\x04pets\x18\x01 \x03(\v2$.backend.proto.customer.v1.PetRecordR\x04pets\x12+\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tH\x00R\rnextPageToken\x88\x01\x01B\x12\n" +
	"\x10_next_page_token\"\x95\x01\n" +
	"\x1bFilterPetCustomerIdsRequest\x126\n" +
	"\x05scope\x18\x01 \x01(\v2 .backend.proto.customer.v1.ScopeR\x05scope\x12>\n" +
	"\x06filter\x18\x02 \x01(\v2&.backend.proto.customer.v1.FilterGroupR\x06filter\"A\n" +
	"\x1cFilterPetCustomerIdsResponse\x12!\n" +
	"\fcustomer_ids\x18\x01 \x03(\x03R\vcustomerIds\"\x9e\x01\n" +
	" CountFilterPetCustomerIdsRequest\x126\n" +
	"\x05scope\x18\x01 \x01(\v2 .backend.proto.customer.v1.ScopeR\x05scope\x12B\n" +
	"\x06having\x18\x02 \x03(\v2*.backend.proto.customer.v1.FilterConditionR\x06having\"F\n" +
	"!CountFilterPetCustomerIdsResponse\x12!\n" +
	"\fcustomer_ids\x18\x01 \x03(\x03R\vcustomerIds\"z\n" +
	"\x1dGetPetTypeDistributionRequest\x126\n" +
	"\x05scope\x18\x01 \x01(\v2 .backend.proto.customer.v1.ScopeR\x05scope\x12!\n" +
	"\fcustomer_ids\x18\x02 \x03(\x03R\vcustomerIds\"T\n" +
	"\x13PetTypeDistribution\x12=\n" +
	"\x05items\x18\x01 \x03(\v2'.backend.proto.customer.v1.PetTypeCountR\x05items\"\xdb\x01\n" +
	"\x1bListPetsForCustomersRequest\x126\n" +
	"\x05scope\x18\x01 \x01(\v2 .backend.proto.customer.v1.ScopeR\x05scope\x12!\n" +
	"\fcustomer_ids\x18\x02 \x03(\x03R\vcustomerIds\x12 \n" +
	"\tpage_size\x18\x03 \x01(\x05H\x00R\bpageSize\x88\x01\x01\x12\"\n" +
	"\n" +
	"page_token\x18\x04 \x01(\tH\x01R\tpageToken\x88\x01\x01B\f\n" +
	"\n" +
	"_page_sizeB\r\n" +
	"\v_page_token\"\x97\x01\n" +
	"\x1cListPetsForCustomersResponse\x126\n" +
	"\x04pets\x18\x01 \x03(\v2\".backend.proto.customer.v1.PetInfoR\x04pets\x12+\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tH\x00R\rnextPageToken\x88\x01\x01B\x12\n" +
	"\x10_next_page_token\"\xb8\x01\n" +
	"\x1dFilterCustomerIdsByTagRequest\x126\n" +
	"\x05scope\x18\x01 \x01(\v2 .backend.proto.customer.v1.ScopeR\x05scope\x12\x17\n" +
	"\atag_ids\x18\x02 \x03(\x03R\x06tagIds\x12F\n" +
	"\boperator\x18\x03 \x01(\x0e2*.backend.proto.customer.v1.InNotInOperatorR\boperator\"C\n" +
	"\x1eFilterCustomerIdsByTagResponse\x12!\n" +
	"\fcustomer_ids\x18\x01 \x03(\x03R\vcustomerIds\"\xbe\x01\n" +
	"!FilterCustomerIdsByPetCodeRequest\x126\n" +
	"\x05scope\x18\x01 \x01(\v2 .backend.proto.customer.v1.ScopeR\x05scope\x12\x19\n" +
	"\bcode_ids\x18\x02 \x03(\x03R\acodeIds\x12F\n" +
	"\boperator\x18\x03 \x01(\x0e2*.backend.proto.customer.v1.InNotInOperatorR\boperator\"G\n" +
	"\"FilterCustomerIdsByPetCodeResponse\x12!\n" +
	"\fcustomer_ids\x18\x01 \x03(\x03R\vcustomerIds\"\xe3\x01\n" +
	"&CountFilterCustomerIdsByVaccineRequest\x126\n" +
	"\x05scope\x18\x01 \x01(\v2 .backend.proto.customer.v1.ScopeR\x05scope\x12=\n" +
	"\fcurrent_time\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\vcurrentTime\x12B\n" +
	"\x06having\x18\x03 \x03(\v2*.backend.proto.customer.v1.FilterConditionR\x06having\"L\n" +
	"'CountFilterCustomerIdsByVaccineResponse\x12!\n" +
	"\fcustomer_ids\x18\x01 \x03(\x03R\vcustomerIds*h\n" +
	"\x12CustomerOrderField\x12$\n" +
	" CUSTOMER_ORDER_FIELD_UNSPECIFIED\x10\x00\x12\r\n" +
	"\tCLIENT_ID\x10\x01\x12\x0e\n" +
	"\n" +
	"FIRST_NAME\x10\x02\x12\r\n" +
	"\tLAST_NAME\x10\x03*D\n" +
	"\x0eOrderDirection\x12\x1f\n" +
	"\x1bORDER_DIRECTION_UNSPECIFIED\x10\x00\x12\a\n" +
	"\x03ASC\x10\x01\x12\b\n" +
	"\x04DESC\x10\x02*I\n" +
	"\x0fInNotInOperator\x12\"\n" +
	"\x1eIN_NOT_IN_OPERATOR_UNSPECIFIED\x10\x00\x12\x06\n" +
	"\x02IN\x10\x01\x12\n" +
	"\n" +
	"\x06NOT_IN\x10\x02*\xf6\x01\n" +
	"\x0eFilterOperator\x12\x1f\n" +
	"\x1bFILTER_OPERATOR_UNSPECIFIED\x10\x00\x12\t\n" +
	"\x05EQUAL\x10\x01\x12\r\n" +
	"\tNOT_EQUAL\x10\x02\x12\r\n" +
	"\tLESS_THAN\x10\x03\x12\x16\n" +
	"\x12LESS_THAN_OR_EQUAL\x10\x04\x12\x10\n" +
	"\fGREATER_THAN\x10\x05\x12\x19\n" +
	"\x15GREATER_THAN_OR_EQUAL\x10\x06\x12\r\n" +
	"\tFILTER_IN\x10\a\x12\x11\n" +
	"\rFILTER_NOT_IN\x10\b\x12\b\n" +
	"\x04LIKE\x10\t\x12\v\n" +
	"\aIS_NULL\x10\n" +
	"\x12\x0f\n" +
	"\vIS_NOT_NULL\x10\v\x12\v\n" +
	"\aBETWEEN\x10\f2\xf3\x0f\n" +
	"\x14CustomerQueryService\x12~\n" +
	"\x11SearchCustomerIds\x123.backend.proto.customer.v1.SearchCustomerIdsRequest\x1a4.backend.proto.customer.v1.SearchCustomerIdsResponse\x12\x9c\x01\n" +
	"\x1bSearchCustomerIdsByLastName\x12=.backend.proto.customer.v1.SearchCustomerIdsByLastNameRequest\x1a>.backend.proto.customer.v1.SearchCustomerIdsByLastNameResponse\x12~\n" +
	"\x11FilterCustomerIds\x123.backend.proto.customer.v1.FilterCustomerIdsRequest\x1a4.backend.proto.customer.v1.FilterCustomerIdsResponse\x12x\n" +
	"\x0fListCustomerIds\x121.backend.proto.customer.v1.ListCustomerIdsRequest\x1a2.backend.proto.customer.v1.ListCustomerIdsResponse\x12\x96\x01\n" +
	"\x19ValidateActiveCustomerIds\x12;.backend.proto.customer.v1.ValidateActiveCustomerIdsRequest\x1a<.backend.proto.customer.v1.ValidateActiveCustomerIdsResponse\x12\x8a\x01\n" +
	"\x15GetCustomersBasicInfo\x127.backend.proto.customer.v1.GetCustomersBasicInfoRequest\x1a8.backend.proto.customer.v1.GetCustomersBasicInfoResponse\x12\x93\x01\n" +
	"\x18SearchContactCustomerIds\x12:.backend.proto.customer.v1.SearchContactCustomerIdsRequest\x1a;.backend.proto.customer.v1.SearchContactCustomerIdsResponse\x12\x93\x01\n" +
	"\x18FilterContactCustomerIds\x12:.backend.proto.customer.v1.FilterContactCustomerIdsRequest\x1a;.backend.proto.customer.v1.FilterContactCustomerIdsResponse\x12\xa2\x01\n" +
	"\x1dCountFilterContactCustomerIds\x12?.backend.proto.customer.v1.CountFilterContactCustomerIdsRequest\<EMAIL>\x12p\n" +
	"\x10GetPrimaryPhones\x122.backend.proto.customer.v1.GetPrimaryPhonesRequest\x1a(.backend.proto.customer.v1.PrimaryPhones\x12\x93\x01\n" +
	"\x18SearchAddressCustomerIds\x12:.backend.proto.customer.v1.SearchAddressCustomerIdsRequest\x1a;.backend.proto.customer.v1.SearchAddressCustomerIdsResponse\x12\x8d\x01\n" +
	"\x16FilterCustomerIdsByZip\x128.backend.proto.customer.v1.FilterCustomerIdsByZipRequest\x1a9.backend.proto.customer.v1.FilterCustomerIdsByZipResponse\x12\xa2\x01\n" +
	"\x1dCountFilterAddressCustomerIds\x12?.backend.proto.customer.v1.CountFilterAddressCustomerIdsRequest\<EMAIL>\x12\x8d\x01\n" +
	"\x16FilterCustomerIdsByTag\x128.backend.proto.customer.v1.FilterCustomerIdsByTagRequest\x1a9.backend.proto.customer.v1.FilterCustomerIdsByTagResponseBk\n" +
	"#com.moego.backend.proto.customer.v1P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/customer/v1;customerpbb\x06proto3"

var (
	file_backend_proto_customer_v1_customer_query_service_proto_rawDescOnce sync.Once
	file_backend_proto_customer_v1_customer_query_service_proto_rawDescData []byte
)

func file_backend_proto_customer_v1_customer_query_service_proto_rawDescGZIP() []byte {
	file_backend_proto_customer_v1_customer_query_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_customer_v1_customer_query_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v1_customer_query_service_proto_rawDesc), len(file_backend_proto_customer_v1_customer_query_service_proto_rawDesc)))
	})
	return file_backend_proto_customer_v1_customer_query_service_proto_rawDescData
}

var file_backend_proto_customer_v1_customer_query_service_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_backend_proto_customer_v1_customer_query_service_proto_msgTypes = make([]protoimpl.MessageInfo, 59)
var file_backend_proto_customer_v1_customer_query_service_proto_goTypes = []any{
	(CustomerOrderField)(0),                         // 0: backend.proto.customer.v1.CustomerOrderField
	(OrderDirection)(0),                             // 1: backend.proto.customer.v1.OrderDirection
	(InNotInOperator)(0),                            // 2: backend.proto.customer.v1.InNotInOperator
	(FilterOperator)(0),                             // 3: backend.proto.customer.v1.FilterOperator
	(*FilterCondition)(nil),                         // 4: backend.proto.customer.v1.FilterCondition
	(*FilterGroup)(nil),                             // 5: backend.proto.customer.v1.FilterGroup
	(*OffsetLimit)(nil),                             // 6: backend.proto.customer.v1.OffsetLimit
	(*Scope)(nil),                                   // 7: backend.proto.customer.v1.Scope
	(*CustomerIds)(nil),                             // 8: backend.proto.customer.v1.CustomerIds
	(*CustomerPhone)(nil),                           // 9: backend.proto.customer.v1.CustomerPhone
	(*PrimaryPhones)(nil),                           // 10: backend.proto.customer.v1.PrimaryPhones
	(*CustomerBasicInfo)(nil),                       // 11: backend.proto.customer.v1.CustomerBasicInfo
	(*CustomerBasicInfos)(nil),                      // 12: backend.proto.customer.v1.CustomerBasicInfos
	(*PetRecord)(nil),                               // 13: backend.proto.customer.v1.PetRecord
	(*PetRecords)(nil),                              // 14: backend.proto.customer.v1.PetRecords
	(*PetTypeCount)(nil),                            // 15: backend.proto.customer.v1.PetTypeCount
	(*PetTypeCounts)(nil),                           // 16: backend.proto.customer.v1.PetTypeCounts
	(*PetInfo)(nil),                                 // 17: backend.proto.customer.v1.PetInfo
	(*PetInfos)(nil),                                // 18: backend.proto.customer.v1.PetInfos
	(*SearchCustomerIdsRequest)(nil),                // 19: backend.proto.customer.v1.SearchCustomerIdsRequest
	(*SearchCustomerIdsResponse)(nil),               // 20: backend.proto.customer.v1.SearchCustomerIdsResponse
	(*SearchCustomerIdsByLastNameRequest)(nil),      // 21: backend.proto.customer.v1.SearchCustomerIdsByLastNameRequest
	(*SearchCustomerIdsByLastNameResponse)(nil),     // 22: backend.proto.customer.v1.SearchCustomerIdsByLastNameResponse
	(*FilterCustomerIdsRequest)(nil),                // 23: backend.proto.customer.v1.FilterCustomerIdsRequest
	(*FilterCustomerIdsResponse)(nil),               // 24: backend.proto.customer.v1.FilterCustomerIdsResponse
	(*ValidateActiveCustomerIdsRequest)(nil),        // 25: backend.proto.customer.v1.ValidateActiveCustomerIdsRequest
	(*ValidateActiveCustomerIdsResponse)(nil),       // 26: backend.proto.customer.v1.ValidateActiveCustomerIdsResponse
	(*GetCustomersBasicInfoRequest)(nil),            // 27: backend.proto.customer.v1.GetCustomersBasicInfoRequest
	(*GetCustomersBasicInfoResponse)(nil),           // 28: backend.proto.customer.v1.GetCustomersBasicInfoResponse
	(*ListCustomerIdsRequest)(nil),                  // 29: backend.proto.customer.v1.ListCustomerIdsRequest
	(*ListCustomerIdsResponse)(nil),                 // 30: backend.proto.customer.v1.ListCustomerIdsResponse
	(*SearchContactCustomerIdsRequest)(nil),         // 31: backend.proto.customer.v1.SearchContactCustomerIdsRequest
	(*SearchContactCustomerIdsResponse)(nil),        // 32: backend.proto.customer.v1.SearchContactCustomerIdsResponse
	(*FilterContactCustomerIdsRequest)(nil),         // 33: backend.proto.customer.v1.FilterContactCustomerIdsRequest
	(*FilterContactCustomerIdsResponse)(nil),        // 34: backend.proto.customer.v1.FilterContactCustomerIdsResponse
	(*CountFilterContactCustomerIdsRequest)(nil),    // 35: backend.proto.customer.v1.CountFilterContactCustomerIdsRequest
	(*CountFilterContactCustomerIdsResponse)(nil),   // 36: backend.proto.customer.v1.CountFilterContactCustomerIdsResponse
	(*GetPrimaryPhonesRequest)(nil),                 // 37: backend.proto.customer.v1.GetPrimaryPhonesRequest
	(*GetPrimaryPhonesResponse)(nil),                // 38: backend.proto.customer.v1.GetPrimaryPhonesResponse
	(*SearchAddressCustomerIdsRequest)(nil),         // 39: backend.proto.customer.v1.SearchAddressCustomerIdsRequest
	(*SearchAddressCustomerIdsResponse)(nil),        // 40: backend.proto.customer.v1.SearchAddressCustomerIdsResponse
	(*FilterCustomerIdsByZipRequest)(nil),           // 41: backend.proto.customer.v1.FilterCustomerIdsByZipRequest
	(*FilterCustomerIdsByZipResponse)(nil),          // 42: backend.proto.customer.v1.FilterCustomerIdsByZipResponse
	(*CountFilterAddressCustomerIdsRequest)(nil),    // 43: backend.proto.customer.v1.CountFilterAddressCustomerIdsRequest
	(*CountFilterAddressCustomerIdsResponse)(nil),   // 44: backend.proto.customer.v1.CountFilterAddressCustomerIdsResponse
	(*SearchPetCustomerIdsRequest)(nil),             // 45: backend.proto.customer.v1.SearchPetCustomerIdsRequest
	(*SearchPetCustomerIdsResponse)(nil),            // 46: backend.proto.customer.v1.SearchPetCustomerIdsResponse
	(*SearchPetsFulltextRequest)(nil),               // 47: backend.proto.customer.v1.SearchPetsFulltextRequest
	(*SearchPetsFulltextResponse)(nil),              // 48: backend.proto.customer.v1.SearchPetsFulltextResponse
	(*FilterPetCustomerIdsRequest)(nil),             // 49: backend.proto.customer.v1.FilterPetCustomerIdsRequest
	(*FilterPetCustomerIdsResponse)(nil),            // 50: backend.proto.customer.v1.FilterPetCustomerIdsResponse
	(*CountFilterPetCustomerIdsRequest)(nil),        // 51: backend.proto.customer.v1.CountFilterPetCustomerIdsRequest
	(*CountFilterPetCustomerIdsResponse)(nil),       // 52: backend.proto.customer.v1.CountFilterPetCustomerIdsResponse
	(*GetPetTypeDistributionRequest)(nil),           // 53: backend.proto.customer.v1.GetPetTypeDistributionRequest
	(*PetTypeDistribution)(nil),                     // 54: backend.proto.customer.v1.PetTypeDistribution
	(*ListPetsForCustomersRequest)(nil),             // 55: backend.proto.customer.v1.ListPetsForCustomersRequest
	(*ListPetsForCustomersResponse)(nil),            // 56: backend.proto.customer.v1.ListPetsForCustomersResponse
	(*FilterCustomerIdsByTagRequest)(nil),           // 57: backend.proto.customer.v1.FilterCustomerIdsByTagRequest
	(*FilterCustomerIdsByTagResponse)(nil),          // 58: backend.proto.customer.v1.FilterCustomerIdsByTagResponse
	(*FilterCustomerIdsByPetCodeRequest)(nil),       // 59: backend.proto.customer.v1.FilterCustomerIdsByPetCodeRequest
	(*FilterCustomerIdsByPetCodeResponse)(nil),      // 60: backend.proto.customer.v1.FilterCustomerIdsByPetCodeResponse
	(*CountFilterCustomerIdsByVaccineRequest)(nil),  // 61: backend.proto.customer.v1.CountFilterCustomerIdsByVaccineRequest
	(*CountFilterCustomerIdsByVaccineResponse)(nil), // 62: backend.proto.customer.v1.CountFilterCustomerIdsByVaccineResponse
	(*timestamppb.Timestamp)(nil),                   // 63: google.protobuf.Timestamp
}
var file_backend_proto_customer_v1_customer_query_service_proto_depIdxs = []int32{
	3,  // 0: backend.proto.customer.v1.FilterCondition.op:type_name -> backend.proto.customer.v1.FilterOperator
	4,  // 1: backend.proto.customer.v1.FilterGroup.conditions:type_name -> backend.proto.customer.v1.FilterCondition
	5,  // 2: backend.proto.customer.v1.FilterGroup.groups:type_name -> backend.proto.customer.v1.FilterGroup
	9,  // 3: backend.proto.customer.v1.PrimaryPhones.items:type_name -> backend.proto.customer.v1.CustomerPhone
	11, // 4: backend.proto.customer.v1.CustomerBasicInfos.items:type_name -> backend.proto.customer.v1.CustomerBasicInfo
	13, // 5: backend.proto.customer.v1.PetRecords.items:type_name -> backend.proto.customer.v1.PetRecord
	15, // 6: backend.proto.customer.v1.PetTypeCounts.items:type_name -> backend.proto.customer.v1.PetTypeCount
	17, // 7: backend.proto.customer.v1.PetInfos.items:type_name -> backend.proto.customer.v1.PetInfo
	7,  // 8: backend.proto.customer.v1.SearchCustomerIdsRequest.scope:type_name -> backend.proto.customer.v1.Scope
	7,  // 9: backend.proto.customer.v1.SearchCustomerIdsByLastNameRequest.scope:type_name -> backend.proto.customer.v1.Scope
	7,  // 10: backend.proto.customer.v1.FilterCustomerIdsRequest.scope:type_name -> backend.proto.customer.v1.Scope
	5,  // 11: backend.proto.customer.v1.FilterCustomerIdsRequest.filter:type_name -> backend.proto.customer.v1.FilterGroup
	11, // 12: backend.proto.customer.v1.GetCustomersBasicInfoResponse.customers:type_name -> backend.proto.customer.v1.CustomerBasicInfo
	7,  // 13: backend.proto.customer.v1.ListCustomerIdsRequest.scope:type_name -> backend.proto.customer.v1.Scope
	0,  // 14: backend.proto.customer.v1.ListCustomerIdsRequest.order_by:type_name -> backend.proto.customer.v1.CustomerOrderField
	1,  // 15: backend.proto.customer.v1.ListCustomerIdsRequest.direction:type_name -> backend.proto.customer.v1.OrderDirection
	6,  // 16: backend.proto.customer.v1.ListCustomerIdsRequest.page:type_name -> backend.proto.customer.v1.OffsetLimit
	7,  // 17: backend.proto.customer.v1.SearchContactCustomerIdsRequest.scope:type_name -> backend.proto.customer.v1.Scope
	7,  // 18: backend.proto.customer.v1.FilterContactCustomerIdsRequest.scope:type_name -> backend.proto.customer.v1.Scope
	5,  // 19: backend.proto.customer.v1.FilterContactCustomerIdsRequest.filter:type_name -> backend.proto.customer.v1.FilterGroup
	7,  // 20: backend.proto.customer.v1.CountFilterContactCustomerIdsRequest.scope:type_name -> backend.proto.customer.v1.Scope
	4,  // 21: backend.proto.customer.v1.CountFilterContactCustomerIdsRequest.having:type_name -> backend.proto.customer.v1.FilterCondition
	7,  // 22: backend.proto.customer.v1.GetPrimaryPhonesRequest.scope:type_name -> backend.proto.customer.v1.Scope
	9,  // 23: backend.proto.customer.v1.GetPrimaryPhonesResponse.phones:type_name -> backend.proto.customer.v1.CustomerPhone
	7,  // 24: backend.proto.customer.v1.SearchAddressCustomerIdsRequest.scope:type_name -> backend.proto.customer.v1.Scope
	7,  // 25: backend.proto.customer.v1.FilterCustomerIdsByZipRequest.scope:type_name -> backend.proto.customer.v1.Scope
	2,  // 26: backend.proto.customer.v1.FilterCustomerIdsByZipRequest.operator:type_name -> backend.proto.customer.v1.InNotInOperator
	7,  // 27: backend.proto.customer.v1.CountFilterAddressCustomerIdsRequest.scope:type_name -> backend.proto.customer.v1.Scope
	4,  // 28: backend.proto.customer.v1.CountFilterAddressCustomerIdsRequest.having:type_name -> backend.proto.customer.v1.FilterCondition
	7,  // 29: backend.proto.customer.v1.SearchPetCustomerIdsRequest.scope:type_name -> backend.proto.customer.v1.Scope
	7,  // 30: backend.proto.customer.v1.SearchPetsFulltextRequest.scope:type_name -> backend.proto.customer.v1.Scope
	13, // 31: backend.proto.customer.v1.SearchPetsFulltextResponse.pets:type_name -> backend.proto.customer.v1.PetRecord
	7,  // 32: backend.proto.customer.v1.FilterPetCustomerIdsRequest.scope:type_name -> backend.proto.customer.v1.Scope
	5,  // 33: backend.proto.customer.v1.FilterPetCustomerIdsRequest.filter:type_name -> backend.proto.customer.v1.FilterGroup
	7,  // 34: backend.proto.customer.v1.CountFilterPetCustomerIdsRequest.scope:type_name -> backend.proto.customer.v1.Scope
	4,  // 35: backend.proto.customer.v1.CountFilterPetCustomerIdsRequest.having:type_name -> backend.proto.customer.v1.FilterCondition
	7,  // 36: backend.proto.customer.v1.GetPetTypeDistributionRequest.scope:type_name -> backend.proto.customer.v1.Scope
	15, // 37: backend.proto.customer.v1.PetTypeDistribution.items:type_name -> backend.proto.customer.v1.PetTypeCount
	7,  // 38: backend.proto.customer.v1.ListPetsForCustomersRequest.scope:type_name -> backend.proto.customer.v1.Scope
	17, // 39: backend.proto.customer.v1.ListPetsForCustomersResponse.pets:type_name -> backend.proto.customer.v1.PetInfo
	7,  // 40: backend.proto.customer.v1.FilterCustomerIdsByTagRequest.scope:type_name -> backend.proto.customer.v1.Scope
	2,  // 41: backend.proto.customer.v1.FilterCustomerIdsByTagRequest.operator:type_name -> backend.proto.customer.v1.InNotInOperator
	7,  // 42: backend.proto.customer.v1.FilterCustomerIdsByPetCodeRequest.scope:type_name -> backend.proto.customer.v1.Scope
	2,  // 43: backend.proto.customer.v1.FilterCustomerIdsByPetCodeRequest.operator:type_name -> backend.proto.customer.v1.InNotInOperator
	7,  // 44: backend.proto.customer.v1.CountFilterCustomerIdsByVaccineRequest.scope:type_name -> backend.proto.customer.v1.Scope
	63, // 45: backend.proto.customer.v1.CountFilterCustomerIdsByVaccineRequest.current_time:type_name -> google.protobuf.Timestamp
	4,  // 46: backend.proto.customer.v1.CountFilterCustomerIdsByVaccineRequest.having:type_name -> backend.proto.customer.v1.FilterCondition
	19, // 47: backend.proto.customer.v1.CustomerQueryService.SearchCustomerIds:input_type -> backend.proto.customer.v1.SearchCustomerIdsRequest
	21, // 48: backend.proto.customer.v1.CustomerQueryService.SearchCustomerIdsByLastName:input_type -> backend.proto.customer.v1.SearchCustomerIdsByLastNameRequest
	23, // 49: backend.proto.customer.v1.CustomerQueryService.FilterCustomerIds:input_type -> backend.proto.customer.v1.FilterCustomerIdsRequest
	29, // 50: backend.proto.customer.v1.CustomerQueryService.ListCustomerIds:input_type -> backend.proto.customer.v1.ListCustomerIdsRequest
	25, // 51: backend.proto.customer.v1.CustomerQueryService.ValidateActiveCustomerIds:input_type -> backend.proto.customer.v1.ValidateActiveCustomerIdsRequest
	27, // 52: backend.proto.customer.v1.CustomerQueryService.GetCustomersBasicInfo:input_type -> backend.proto.customer.v1.GetCustomersBasicInfoRequest
	31, // 53: backend.proto.customer.v1.CustomerQueryService.SearchContactCustomerIds:input_type -> backend.proto.customer.v1.SearchContactCustomerIdsRequest
	33, // 54: backend.proto.customer.v1.CustomerQueryService.FilterContactCustomerIds:input_type -> backend.proto.customer.v1.FilterContactCustomerIdsRequest
	35, // 55: backend.proto.customer.v1.CustomerQueryService.CountFilterContactCustomerIds:input_type -> backend.proto.customer.v1.CountFilterContactCustomerIdsRequest
	37, // 56: backend.proto.customer.v1.CustomerQueryService.GetPrimaryPhones:input_type -> backend.proto.customer.v1.GetPrimaryPhonesRequest
	39, // 57: backend.proto.customer.v1.CustomerQueryService.SearchAddressCustomerIds:input_type -> backend.proto.customer.v1.SearchAddressCustomerIdsRequest
	41, // 58: backend.proto.customer.v1.CustomerQueryService.FilterCustomerIdsByZip:input_type -> backend.proto.customer.v1.FilterCustomerIdsByZipRequest
	43, // 59: backend.proto.customer.v1.CustomerQueryService.CountFilterAddressCustomerIds:input_type -> backend.proto.customer.v1.CountFilterAddressCustomerIdsRequest
	57, // 60: backend.proto.customer.v1.CustomerQueryService.FilterCustomerIdsByTag:input_type -> backend.proto.customer.v1.FilterCustomerIdsByTagRequest
	20, // 61: backend.proto.customer.v1.CustomerQueryService.SearchCustomerIds:output_type -> backend.proto.customer.v1.SearchCustomerIdsResponse
	22, // 62: backend.proto.customer.v1.CustomerQueryService.SearchCustomerIdsByLastName:output_type -> backend.proto.customer.v1.SearchCustomerIdsByLastNameResponse
	24, // 63: backend.proto.customer.v1.CustomerQueryService.FilterCustomerIds:output_type -> backend.proto.customer.v1.FilterCustomerIdsResponse
	30, // 64: backend.proto.customer.v1.CustomerQueryService.ListCustomerIds:output_type -> backend.proto.customer.v1.ListCustomerIdsResponse
	26, // 65: backend.proto.customer.v1.CustomerQueryService.ValidateActiveCustomerIds:output_type -> backend.proto.customer.v1.ValidateActiveCustomerIdsResponse
	28, // 66: backend.proto.customer.v1.CustomerQueryService.GetCustomersBasicInfo:output_type -> backend.proto.customer.v1.GetCustomersBasicInfoResponse
	32, // 67: backend.proto.customer.v1.CustomerQueryService.SearchContactCustomerIds:output_type -> backend.proto.customer.v1.SearchContactCustomerIdsResponse
	34, // 68: backend.proto.customer.v1.CustomerQueryService.FilterContactCustomerIds:output_type -> backend.proto.customer.v1.FilterContactCustomerIdsResponse
	36, // 69: backend.proto.customer.v1.CustomerQueryService.CountFilterContactCustomerIds:output_type -> backend.proto.customer.v1.CountFilterContactCustomerIdsResponse
	10, // 70: backend.proto.customer.v1.CustomerQueryService.GetPrimaryPhones:output_type -> backend.proto.customer.v1.PrimaryPhones
	40, // 71: backend.proto.customer.v1.CustomerQueryService.SearchAddressCustomerIds:output_type -> backend.proto.customer.v1.SearchAddressCustomerIdsResponse
	42, // 72: backend.proto.customer.v1.CustomerQueryService.FilterCustomerIdsByZip:output_type -> backend.proto.customer.v1.FilterCustomerIdsByZipResponse
	44, // 73: backend.proto.customer.v1.CustomerQueryService.CountFilterAddressCustomerIds:output_type -> backend.proto.customer.v1.CountFilterAddressCustomerIdsResponse
	58, // 74: backend.proto.customer.v1.CustomerQueryService.FilterCustomerIdsByTag:output_type -> backend.proto.customer.v1.FilterCustomerIdsByTagResponse
	61, // [61:75] is the sub-list for method output_type
	47, // [47:61] is the sub-list for method input_type
	47, // [47:47] is the sub-list for extension type_name
	47, // [47:47] is the sub-list for extension extendee
	0,  // [0:47] is the sub-list for field type_name
}

func init() { file_backend_proto_customer_v1_customer_query_service_proto_init() }
func file_backend_proto_customer_v1_customer_query_service_proto_init() {
	if File_backend_proto_customer_v1_customer_query_service_proto != nil {
		return
	}
	file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[0].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[16].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[18].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[25].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[26].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[27].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[28].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[35].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[36].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[41].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[42].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[43].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[44].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[51].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_query_service_proto_msgTypes[52].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v1_customer_query_service_proto_rawDesc), len(file_backend_proto_customer_v1_customer_query_service_proto_rawDesc)),
			NumEnums:      4,
			NumMessages:   59,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_customer_v1_customer_query_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_customer_v1_customer_query_service_proto_depIdxs,
		EnumInfos:         file_backend_proto_customer_v1_customer_query_service_proto_enumTypes,
		MessageInfos:      file_backend_proto_customer_v1_customer_query_service_proto_msgTypes,
	}.Build()
	File_backend_proto_customer_v1_customer_query_service_proto = out.File
	file_backend_proto_customer_v1_customer_query_service_proto_goTypes = nil
	file_backend_proto_customer_v1_customer_query_service_proto_depIdxs = nil
}
