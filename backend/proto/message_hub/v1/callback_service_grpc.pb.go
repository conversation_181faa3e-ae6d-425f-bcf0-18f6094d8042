// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/message_hub/v1/callback_service.proto

package messagehubpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	CallbackService_HandleState_FullMethodName = "/backend.proto.message_hub.v1.CallbackService/HandleState"
	CallbackService_HandleReply_FullMethodName = "/backend.proto.message_hub.v1.CallbackService/HandleReply"
)

// CallbackServiceClient is the client API for CallbackService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// CallbackService
// 客户端可以实现这个服务，以接收来自 Message Hub 的回调。
type CallbackServiceClient interface {
	// HandleState 用于处理消息发送状态的回调。
	// 如果你不需要接收状态回调，可以不实现这个接口。
	HandleState(ctx context.Context, in *HandleStateRequest, opts ...grpc.CallOption) (*HandleStateResponse, error)
	// HandleReply 用于处理消息回复的回调。
	// 目前只有 `call` 类型的消息可能会有回复。
	// 如果你不需要接收回复回调，可以不实现这个接口。
	HandleReply(ctx context.Context, in *HandleReplyRequest, opts ...grpc.CallOption) (*HandleReplyResponse, error)
}

type callbackServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCallbackServiceClient(cc grpc.ClientConnInterface) CallbackServiceClient {
	return &callbackServiceClient{cc}
}

func (c *callbackServiceClient) HandleState(ctx context.Context, in *HandleStateRequest, opts ...grpc.CallOption) (*HandleStateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HandleStateResponse)
	err := c.cc.Invoke(ctx, CallbackService_HandleState_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *callbackServiceClient) HandleReply(ctx context.Context, in *HandleReplyRequest, opts ...grpc.CallOption) (*HandleReplyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HandleReplyResponse)
	err := c.cc.Invoke(ctx, CallbackService_HandleReply_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CallbackServiceServer is the server API for CallbackService service.
// All implementations must embed UnimplementedCallbackServiceServer
// for forward compatibility.
//
// CallbackService
// 客户端可以实现这个服务，以接收来自 Message Hub 的回调。
type CallbackServiceServer interface {
	// HandleState 用于处理消息发送状态的回调。
	// 如果你不需要接收状态回调，可以不实现这个接口。
	HandleState(context.Context, *HandleStateRequest) (*HandleStateResponse, error)
	// HandleReply 用于处理消息回复的回调。
	// 目前只有 `call` 类型的消息可能会有回复。
	// 如果你不需要接收回复回调，可以不实现这个接口。
	HandleReply(context.Context, *HandleReplyRequest) (*HandleReplyResponse, error)
	mustEmbedUnimplementedCallbackServiceServer()
}

// UnimplementedCallbackServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCallbackServiceServer struct{}

func (UnimplementedCallbackServiceServer) HandleState(context.Context, *HandleStateRequest) (*HandleStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandleState not implemented")
}
func (UnimplementedCallbackServiceServer) HandleReply(context.Context, *HandleReplyRequest) (*HandleReplyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandleReply not implemented")
}
func (UnimplementedCallbackServiceServer) mustEmbedUnimplementedCallbackServiceServer() {}
func (UnimplementedCallbackServiceServer) testEmbeddedByValue()                         {}

// UnsafeCallbackServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CallbackServiceServer will
// result in compilation errors.
type UnsafeCallbackServiceServer interface {
	mustEmbedUnimplementedCallbackServiceServer()
}

func RegisterCallbackServiceServer(s grpc.ServiceRegistrar, srv CallbackServiceServer) {
	// If the following call pancis, it indicates UnimplementedCallbackServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&CallbackService_ServiceDesc, srv)
}

func _CallbackService_HandleState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallbackServiceServer).HandleState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CallbackService_HandleState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallbackServiceServer).HandleState(ctx, req.(*HandleStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CallbackService_HandleReply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleReplyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallbackServiceServer).HandleReply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CallbackService_HandleReply_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallbackServiceServer).HandleReply(ctx, req.(*HandleReplyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CallbackService_ServiceDesc is the grpc.ServiceDesc for CallbackService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CallbackService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.message_hub.v1.CallbackService",
	HandlerType: (*CallbackServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HandleState",
			Handler:    _CallbackService_HandleState_Handler,
		},
		{
			MethodName: "HandleReply",
			Handler:    _CallbackService_HandleReply_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/message_hub/v1/callback_service.proto",
}
