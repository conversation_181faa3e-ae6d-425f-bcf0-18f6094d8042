// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/message_hub/v1/callback_service.proto

package messagehubpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// HandleStateRequest
type HandleStateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 消息的唯一标识符。
	MessageId string `protobuf:"bytes,1,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	// 发送状态。
	// (-- api-linter: core::0216::state-field-output-only=disabled
	//
	//	aip.dev/not-precedent: This field is in a response message,
	//	which is inherently output-only. --)
	State SendState `protobuf:"varint,2,opt,name=state,proto3,enum=backend.proto.message_hub.v1.SendState" json:"state,omitempty"`
	// 发送失败时的错误码（第三方系统返回的错误码）
	ErrorCode int32 `protobuf:"varint,3,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	// 发送失败时的错误信息
	ErrorMessage  string `protobuf:"bytes,4,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandleStateRequest) Reset() {
	*x = HandleStateRequest{}
	mi := &file_backend_proto_message_hub_v1_callback_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandleStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleStateRequest) ProtoMessage() {}

func (x *HandleStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_message_hub_v1_callback_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleStateRequest.ProtoReflect.Descriptor instead.
func (*HandleStateRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_message_hub_v1_callback_service_proto_rawDescGZIP(), []int{0}
}

func (x *HandleStateRequest) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

func (x *HandleStateRequest) GetState() SendState {
	if x != nil {
		return x.State
	}
	return SendState_SEND_STATE_UNSPECIFIED
}

func (x *HandleStateRequest) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *HandleStateRequest) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

// HandleStateResponse
type HandleStateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandleStateResponse) Reset() {
	*x = HandleStateResponse{}
	mi := &file_backend_proto_message_hub_v1_callback_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandleStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleStateResponse) ProtoMessage() {}

func (x *HandleStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_message_hub_v1_callback_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleStateResponse.ProtoReflect.Descriptor instead.
func (*HandleStateResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_message_hub_v1_callback_service_proto_rawDescGZIP(), []int{1}
}

// HandleReplyRequest
type HandleReplyRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 消息的唯一标识符。
	MessageId string `protobuf:"bytes,1,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	// 回复的消息内容。
	Reply         string `protobuf:"bytes,2,opt,name=reply,proto3" json:"reply,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandleReplyRequest) Reset() {
	*x = HandleReplyRequest{}
	mi := &file_backend_proto_message_hub_v1_callback_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandleReplyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleReplyRequest) ProtoMessage() {}

func (x *HandleReplyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_message_hub_v1_callback_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleReplyRequest.ProtoReflect.Descriptor instead.
func (*HandleReplyRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_message_hub_v1_callback_service_proto_rawDescGZIP(), []int{2}
}

func (x *HandleReplyRequest) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

func (x *HandleReplyRequest) GetReply() string {
	if x != nil {
		return x.Reply
	}
	return ""
}

// HandleReplyResponse
type HandleReplyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandleReplyResponse) Reset() {
	*x = HandleReplyResponse{}
	mi := &file_backend_proto_message_hub_v1_callback_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandleReplyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleReplyResponse) ProtoMessage() {}

func (x *HandleReplyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_message_hub_v1_callback_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleReplyResponse.ProtoReflect.Descriptor instead.
func (*HandleReplyResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_message_hub_v1_callback_service_proto_rawDescGZIP(), []int{3}
}

var File_backend_proto_message_hub_v1_callback_service_proto protoreflect.FileDescriptor

const file_backend_proto_message_hub_v1_callback_service_proto_rawDesc = "" +
	"\n" +
	"3backend/proto/message_hub/v1/callback_service.proto\x12\x1cbackend.proto.message_hub.v1\x1a-backend/proto/message_hub/v1/send_state.proto\"\xb6\x01\n" +
	"\x12HandleStateRequest\x12\x1d\n" +
	"\n" +
	"message_id\x18\x01 \x01(\tR\tmessageId\x12=\n" +
	"\x05state\x18\x02 \x01(\x0e2'.backend.proto.message_hub.v1.SendStateR\x05state\x12\x1d\n" +
	"\n" +
	"error_code\x18\x03 \x01(\x05R\terrorCode\x12#\n" +
	"\rerror_message\x18\x04 \x01(\tR\ferrorMessage\"\x15\n" +
	"\x13HandleStateResponse\"I\n" +
	"\x12HandleReplyRequest\x12\x1d\n" +
	"\n" +
	"message_id\x18\x01 \x01(\tR\tmessageId\x12\x14\n" +
	"\x05reply\x18\x02 \x01(\tR\x05reply\"\x15\n" +
	"\x13HandleReplyResponse2\xf9\x01\n" +
	"\x0fCallbackService\x12r\n" +
	"\vHandleState\x120.backend.proto.message_hub.v1.HandleStateRequest\x1a1.backend.proto.message_hub.v1.HandleStateResponse\x12r\n" +
	"\vHandleReply\x120.backend.proto.message_hub.v1.HandleReplyRequest\x1a1.backend.proto.message_hub.v1.HandleReplyResponseBs\n" +
	"&com.moego.backend.proto.message_hub.v1P\x01ZGgithub.com/MoeGolibrary/moego/backend/proto/message_hub/v1;messagehubpbb\x06proto3"

var (
	file_backend_proto_message_hub_v1_callback_service_proto_rawDescOnce sync.Once
	file_backend_proto_message_hub_v1_callback_service_proto_rawDescData []byte
)

func file_backend_proto_message_hub_v1_callback_service_proto_rawDescGZIP() []byte {
	file_backend_proto_message_hub_v1_callback_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_message_hub_v1_callback_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_message_hub_v1_callback_service_proto_rawDesc), len(file_backend_proto_message_hub_v1_callback_service_proto_rawDesc)))
	})
	return file_backend_proto_message_hub_v1_callback_service_proto_rawDescData
}

var file_backend_proto_message_hub_v1_callback_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_backend_proto_message_hub_v1_callback_service_proto_goTypes = []any{
	(*HandleStateRequest)(nil),  // 0: backend.proto.message_hub.v1.HandleStateRequest
	(*HandleStateResponse)(nil), // 1: backend.proto.message_hub.v1.HandleStateResponse
	(*HandleReplyRequest)(nil),  // 2: backend.proto.message_hub.v1.HandleReplyRequest
	(*HandleReplyResponse)(nil), // 3: backend.proto.message_hub.v1.HandleReplyResponse
	(SendState)(0),              // 4: backend.proto.message_hub.v1.SendState
}
var file_backend_proto_message_hub_v1_callback_service_proto_depIdxs = []int32{
	4, // 0: backend.proto.message_hub.v1.HandleStateRequest.state:type_name -> backend.proto.message_hub.v1.SendState
	0, // 1: backend.proto.message_hub.v1.CallbackService.HandleState:input_type -> backend.proto.message_hub.v1.HandleStateRequest
	2, // 2: backend.proto.message_hub.v1.CallbackService.HandleReply:input_type -> backend.proto.message_hub.v1.HandleReplyRequest
	1, // 3: backend.proto.message_hub.v1.CallbackService.HandleState:output_type -> backend.proto.message_hub.v1.HandleStateResponse
	3, // 4: backend.proto.message_hub.v1.CallbackService.HandleReply:output_type -> backend.proto.message_hub.v1.HandleReplyResponse
	3, // [3:5] is the sub-list for method output_type
	1, // [1:3] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_backend_proto_message_hub_v1_callback_service_proto_init() }
func file_backend_proto_message_hub_v1_callback_service_proto_init() {
	if File_backend_proto_message_hub_v1_callback_service_proto != nil {
		return
	}
	file_backend_proto_message_hub_v1_send_state_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_message_hub_v1_callback_service_proto_rawDesc), len(file_backend_proto_message_hub_v1_callback_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_message_hub_v1_callback_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_message_hub_v1_callback_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_message_hub_v1_callback_service_proto_msgTypes,
	}.Build()
	File_backend_proto_message_hub_v1_callback_service_proto = out.File
	file_backend_proto_message_hub_v1_callback_service_proto_goTypes = nil
	file_backend_proto_message_hub_v1_callback_service_proto_depIdxs = nil
}
