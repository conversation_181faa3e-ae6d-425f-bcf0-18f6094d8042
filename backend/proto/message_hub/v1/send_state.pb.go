// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.8
// 	protoc        (unknown)
// source: backend/proto/message_hub/v1/send_state.proto

package messagehubpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// SendState 定义了消息发送的各种状态。
type SendState int32

const (
	// 默认未指定状态。
	SendState_SEND_STATE_UNSPECIFIED SendState = 0
	// 消息已从服务发送，但尚未确认送达。
	SendState_SEND_STATE_SENT SendState = 1
	// 发送尝试失败。
	SendState_SEND_STATE_FAILED SendState = 2
	// 消息已成功送达接收方。
	SendState_SEND_STATE_DELIVERED SendState = 3
)

// Enum value maps for SendState.
var (
	SendState_name = map[int32]string{
		0: "SEND_STATE_UNSPECIFIED",
		1: "SEND_STATE_SENT",
		2: "SEND_STATE_FAILED",
		3: "SEND_STATE_DELIVERED",
	}
	SendState_value = map[string]int32{
		"SEND_STATE_UNSPECIFIED": 0,
		"SEND_STATE_SENT":        1,
		"SEND_STATE_FAILED":      2,
		"SEND_STATE_DELIVERED":   3,
	}
)

func (x SendState) Enum() *SendState {
	p := new(SendState)
	*p = x
	return p
}

func (x SendState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SendState) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_message_hub_v1_send_state_proto_enumTypes[0].Descriptor()
}

func (SendState) Type() protoreflect.EnumType {
	return &file_backend_proto_message_hub_v1_send_state_proto_enumTypes[0]
}

func (x SendState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SendState.Descriptor instead.
func (SendState) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_message_hub_v1_send_state_proto_rawDescGZIP(), []int{0}
}

var File_backend_proto_message_hub_v1_send_state_proto protoreflect.FileDescriptor

const file_backend_proto_message_hub_v1_send_state_proto_rawDesc = "" +
	"\n" +
	"-backend/proto/message_hub/v1/send_state.proto\x12\x1cbackend.proto.message_hub.v1*m\n" +
	"\tSendState\x12\x1a\n" +
	"\x16SEND_STATE_UNSPECIFIED\x10\x00\x12\x13\n" +
	"\x0fSEND_STATE_SENT\x10\x01\x12\x15\n" +
	"\x11SEND_STATE_FAILED\x10\x02\x12\x18\n" +
	"\x14SEND_STATE_DELIVERED\x10\x03Bs\n" +
	"&com.moego.backend.proto.message_hub.v1P\x01ZGgithub.com/MoeGolibrary/moego/backend/proto/message_hub/v1;messagehubpbb\x06proto3"

var (
	file_backend_proto_message_hub_v1_send_state_proto_rawDescOnce sync.Once
	file_backend_proto_message_hub_v1_send_state_proto_rawDescData []byte
)

func file_backend_proto_message_hub_v1_send_state_proto_rawDescGZIP() []byte {
	file_backend_proto_message_hub_v1_send_state_proto_rawDescOnce.Do(func() {
		file_backend_proto_message_hub_v1_send_state_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_message_hub_v1_send_state_proto_rawDesc), len(file_backend_proto_message_hub_v1_send_state_proto_rawDesc)))
	})
	return file_backend_proto_message_hub_v1_send_state_proto_rawDescData
}

var file_backend_proto_message_hub_v1_send_state_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_backend_proto_message_hub_v1_send_state_proto_goTypes = []any{
	(SendState)(0), // 0: backend.proto.message_hub.v1.SendState
}
var file_backend_proto_message_hub_v1_send_state_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_backend_proto_message_hub_v1_send_state_proto_init() }
func file_backend_proto_message_hub_v1_send_state_proto_init() {
	if File_backend_proto_message_hub_v1_send_state_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_message_hub_v1_send_state_proto_rawDesc), len(file_backend_proto_message_hub_v1_send_state_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_message_hub_v1_send_state_proto_goTypes,
		DependencyIndexes: file_backend_proto_message_hub_v1_send_state_proto_depIdxs,
		EnumInfos:         file_backend_proto_message_hub_v1_send_state_proto_enumTypes,
	}.Build()
	File_backend_proto_message_hub_v1_send_state_proto = out.File
	file_backend_proto_message_hub_v1_send_state_proto_goTypes = nil
	file_backend_proto_message_hub_v1_send_state_proto_depIdxs = nil
}
