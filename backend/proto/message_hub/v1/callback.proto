syntax = "proto3";

package backend.proto.message_hub.v1;

option go_package="github.com/MoeGolibrary/moego/backend/proto/message_hub/v1;messagehubpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.message_hub.v1";

// Callback 定义了接收回调通知的方式。
// 客户端可以选择通过 Kafka 或 gRPC 接收回调。
message Callback {
  // 选择一种接收回调的方式。
  oneof method {
    // 通过 Kafka 接收回调。
    KafkaCallback kafka = 6;
    // 通过 gRPC 接收回调。
    GrpcCallback grpc = 7;
  }
}

// KafkaCallback 定义了通过 Kafka 接收回调时所需的参数。
message KafkaCallback {
  // 用于接收回调的 Kafka topic。
  string topic = 1;
}

// GrpcCallback 定义了通过 gRPC 接收回调时所需的参数。
message GrpcCallback {
  // gRPC 服务的地址，包括主机和端口，例如：moego-customer:9090。
  string server = 1;
}
