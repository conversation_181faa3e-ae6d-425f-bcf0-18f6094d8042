syntax = "proto3";

package backend.proto.message_hub.v1;

option go_package="github.com/MoeGolibrary/moego/backend/proto/message_hub/v1;messagehubpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.message_hub.v1";

import "backend/proto/message_hub/v1/send_state.proto";

// CallbackService
// 客户端可以实现这个服务，以接收来自 Message Hub 的回调。
service CallbackService {
  // HandleState 用于处理消息发送状态的回调。
  // 如果你不需要接收状态回调，可以不实现这个接口。
  rpc HandleState(HandleStateRequest) returns (HandleStateResponse);

  // HandleReply 用于处理消息回复的回调。
  // 目前只有 `call` 类型的消息可能会有回复。
  // 如果你不需要接收回复回调，可以不实现这个接口。
  rpc HandleReply(HandleReplyRequest) returns (HandleReplyResponse);
}

// HandleStateRequest
message HandleStateRequest {
  // 消息的唯一标识符。
  string message_id = 1;

  // 发送状态。
  // (-- api-linter: core::0216::state-field-output-only=disabled
  //     aip.dev/not-precedent: This field is in a response message,
  //     which is inherently output-only. --)
  SendState state = 2;
  // 发送失败时的错误码（第三方系统返回的错误码）
  int32 error_code = 3;
  // 发送失败时的错误信息
  string error_message = 4;
}

// HandleStateResponse
message HandleStateResponse {}

// HandleReplyRequest
message HandleReplyRequest {
  // 消息的唯一标识符。
  string message_id = 1;
  // 回复的消息内容。
  string reply = 2;
}

// HandleReplyResponse
message HandleReplyResponse {}
