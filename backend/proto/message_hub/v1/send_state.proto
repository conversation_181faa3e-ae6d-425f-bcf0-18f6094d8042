syntax = "proto3";

package backend.proto.message_hub.v1;

option go_package="github.com/MoeGolibrary/moego/backend/proto/message_hub/v1;messagehubpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.message_hub.v1";

// SendState 定义了消息发送的各种状态。
enum SendState {
  // 默认未指定状态。
  SEND_STATE_UNSPECIFIED = 0;
  // 消息已从服务发送，但尚未确认送达。
  SEND_STATE_SENT = 1;
  // 发送尝试失败。
  SEND_STATE_FAILED = 2;
  // 消息已成功送达接收方。
  SEND_STATE_DELIVERED = 3;
}