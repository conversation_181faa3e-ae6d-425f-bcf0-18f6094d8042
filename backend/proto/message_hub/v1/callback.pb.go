// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/message_hub/v1/callback.proto

package messagehubpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Callback 定义了接收回调通知的方式。
// 客户端可以选择通过 Kafka 或 gRPC 接收回调。
type Callback struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 选择一种接收回调的方式。
	//
	// Types that are valid to be assigned to Method:
	//
	//	*Callback_Kafka
	//	*Callback_Grpc
	Method        isCallback_Method `protobuf_oneof:"method"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Callback) Reset() {
	*x = Callback{}
	mi := &file_backend_proto_message_hub_v1_callback_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Callback) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Callback) ProtoMessage() {}

func (x *Callback) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_message_hub_v1_callback_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Callback.ProtoReflect.Descriptor instead.
func (*Callback) Descriptor() ([]byte, []int) {
	return file_backend_proto_message_hub_v1_callback_proto_rawDescGZIP(), []int{0}
}

func (x *Callback) GetMethod() isCallback_Method {
	if x != nil {
		return x.Method
	}
	return nil
}

func (x *Callback) GetKafka() *KafkaCallback {
	if x != nil {
		if x, ok := x.Method.(*Callback_Kafka); ok {
			return x.Kafka
		}
	}
	return nil
}

func (x *Callback) GetGrpc() *GrpcCallback {
	if x != nil {
		if x, ok := x.Method.(*Callback_Grpc); ok {
			return x.Grpc
		}
	}
	return nil
}

type isCallback_Method interface {
	isCallback_Method()
}

type Callback_Kafka struct {
	// 通过 Kafka 接收回调。
	Kafka *KafkaCallback `protobuf:"bytes,6,opt,name=kafka,proto3,oneof"`
}

type Callback_Grpc struct {
	// 通过 gRPC 接收回调。
	Grpc *GrpcCallback `protobuf:"bytes,7,opt,name=grpc,proto3,oneof"`
}

func (*Callback_Kafka) isCallback_Method() {}

func (*Callback_Grpc) isCallback_Method() {}

// KafkaCallback 定义了通过 Kafka 接收回调时所需的参数。
type KafkaCallback struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用于接收回调的 Kafka topic。
	Topic         string `protobuf:"bytes,1,opt,name=topic,proto3" json:"topic,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *KafkaCallback) Reset() {
	*x = KafkaCallback{}
	mi := &file_backend_proto_message_hub_v1_callback_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KafkaCallback) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KafkaCallback) ProtoMessage() {}

func (x *KafkaCallback) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_message_hub_v1_callback_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KafkaCallback.ProtoReflect.Descriptor instead.
func (*KafkaCallback) Descriptor() ([]byte, []int) {
	return file_backend_proto_message_hub_v1_callback_proto_rawDescGZIP(), []int{1}
}

func (x *KafkaCallback) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

// GrpcCallback 定义了通过 gRPC 接收回调时所需的参数。
type GrpcCallback struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// gRPC 服务的地址，包括主机和端口，例如：moego-customer:9090。
	Server        string `protobuf:"bytes,1,opt,name=server,proto3" json:"server,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GrpcCallback) Reset() {
	*x = GrpcCallback{}
	mi := &file_backend_proto_message_hub_v1_callback_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GrpcCallback) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GrpcCallback) ProtoMessage() {}

func (x *GrpcCallback) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_message_hub_v1_callback_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GrpcCallback.ProtoReflect.Descriptor instead.
func (*GrpcCallback) Descriptor() ([]byte, []int) {
	return file_backend_proto_message_hub_v1_callback_proto_rawDescGZIP(), []int{2}
}

func (x *GrpcCallback) GetServer() string {
	if x != nil {
		return x.Server
	}
	return ""
}

var File_backend_proto_message_hub_v1_callback_proto protoreflect.FileDescriptor

const file_backend_proto_message_hub_v1_callback_proto_rawDesc = "" +
	"\n" +
	"+backend/proto/message_hub/v1/callback.proto\x12\x1cbackend.proto.message_hub.v1\"\x9b\x01\n" +
	"\bCallback\x12C\n" +
	"\x05kafka\x18\x06 \x01(\v2+.backend.proto.message_hub.v1.KafkaCallbackH\x00R\x05kafka\x12@\n" +
	"\x04grpc\x18\a \x01(\v2*.backend.proto.message_hub.v1.GrpcCallbackH\x00R\x04grpcB\b\n" +
	"\x06method\"%\n" +
	"\rKafkaCallback\x12\x14\n" +
	"\x05topic\x18\x01 \x01(\tR\x05topic\"&\n" +
	"\fGrpcCallback\x12\x16\n" +
	"\x06server\x18\x01 \x01(\tR\x06serverBs\n" +
	"&com.moego.backend.proto.message_hub.v1P\x01ZGgithub.com/MoeGolibrary/moego/backend/proto/message_hub/v1;messagehubpbb\x06proto3"

var (
	file_backend_proto_message_hub_v1_callback_proto_rawDescOnce sync.Once
	file_backend_proto_message_hub_v1_callback_proto_rawDescData []byte
)

func file_backend_proto_message_hub_v1_callback_proto_rawDescGZIP() []byte {
	file_backend_proto_message_hub_v1_callback_proto_rawDescOnce.Do(func() {
		file_backend_proto_message_hub_v1_callback_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_message_hub_v1_callback_proto_rawDesc), len(file_backend_proto_message_hub_v1_callback_proto_rawDesc)))
	})
	return file_backend_proto_message_hub_v1_callback_proto_rawDescData
}

var file_backend_proto_message_hub_v1_callback_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_backend_proto_message_hub_v1_callback_proto_goTypes = []any{
	(*Callback)(nil),      // 0: backend.proto.message_hub.v1.Callback
	(*KafkaCallback)(nil), // 1: backend.proto.message_hub.v1.KafkaCallback
	(*GrpcCallback)(nil),  // 2: backend.proto.message_hub.v1.GrpcCallback
}
var file_backend_proto_message_hub_v1_callback_proto_depIdxs = []int32{
	1, // 0: backend.proto.message_hub.v1.Callback.kafka:type_name -> backend.proto.message_hub.v1.KafkaCallback
	2, // 1: backend.proto.message_hub.v1.Callback.grpc:type_name -> backend.proto.message_hub.v1.GrpcCallback
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_backend_proto_message_hub_v1_callback_proto_init() }
func file_backend_proto_message_hub_v1_callback_proto_init() {
	if File_backend_proto_message_hub_v1_callback_proto != nil {
		return
	}
	file_backend_proto_message_hub_v1_callback_proto_msgTypes[0].OneofWrappers = []any{
		(*Callback_Kafka)(nil),
		(*Callback_Grpc)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_message_hub_v1_callback_proto_rawDesc), len(file_backend_proto_message_hub_v1_callback_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_message_hub_v1_callback_proto_goTypes,
		DependencyIndexes: file_backend_proto_message_hub_v1_callback_proto_depIdxs,
		MessageInfos:      file_backend_proto_message_hub_v1_callback_proto_msgTypes,
	}.Build()
	File_backend_proto_message_hub_v1_callback_proto = out.File
	file_backend_proto_message_hub_v1_callback_proto_goTypes = nil
	file_backend_proto_message_hub_v1_callback_proto_depIdxs = nil
}
