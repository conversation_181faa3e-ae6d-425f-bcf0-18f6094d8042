// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/message_hub/v1/message_hub_service.proto

package messagehubpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	MessageHubService_SendPing_FullMethodName = "/backend.proto.message_hub.v1.MessageHubService/SendPing"
)

// MessageHubServiceClient is the client API for MessageHubService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// MessageHubService
type MessageHubServiceClient interface {
	// SendPing sends a ping request and returns a pong response
	SendPing(ctx context.Context, in *SendPingRequest, opts ...grpc.CallOption) (*SendPingResponse, error)
}

type messageHubServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMessageHubServiceClient(cc grpc.ClientConnInterface) MessageHubServiceClient {
	return &messageHubServiceClient{cc}
}

func (c *messageHubServiceClient) SendPing(ctx context.Context, in *SendPingRequest, opts ...grpc.CallOption) (*SendPingResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendPingResponse)
	err := c.cc.Invoke(ctx, MessageHubService_SendPing_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MessageHubServiceServer is the server API for MessageHubService service.
// All implementations must embed UnimplementedMessageHubServiceServer
// for forward compatibility.
//
// MessageHubService
type MessageHubServiceServer interface {
	// SendPing sends a ping request and returns a pong response
	SendPing(context.Context, *SendPingRequest) (*SendPingResponse, error)
	mustEmbedUnimplementedMessageHubServiceServer()
}

// UnimplementedMessageHubServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedMessageHubServiceServer struct{}

func (UnimplementedMessageHubServiceServer) SendPing(context.Context, *SendPingRequest) (*SendPingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendPing not implemented")
}
func (UnimplementedMessageHubServiceServer) mustEmbedUnimplementedMessageHubServiceServer() {}
func (UnimplementedMessageHubServiceServer) testEmbeddedByValue()                           {}

// UnsafeMessageHubServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MessageHubServiceServer will
// result in compilation errors.
type UnsafeMessageHubServiceServer interface {
	mustEmbedUnimplementedMessageHubServiceServer()
}

func RegisterMessageHubServiceServer(s grpc.ServiceRegistrar, srv MessageHubServiceServer) {
	// If the following call pancis, it indicates UnimplementedMessageHubServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&MessageHubService_ServiceDesc, srv)
}

func _MessageHubService_SendPing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendPingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageHubServiceServer).SendPing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageHubService_SendPing_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageHubServiceServer).SendPing(ctx, req.(*SendPingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MessageHubService_ServiceDesc is the grpc.ServiceDesc for MessageHubService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MessageHubService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.message_hub.v1.MessageHubService",
	HandlerType: (*MessageHubServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendPing",
			Handler:    _MessageHubService_SendPing_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/message_hub/v1/message_hub_service.proto",
}
