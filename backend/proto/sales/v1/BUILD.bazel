load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "salespb_proto",
    srcs = [
        "annual_contract_service.proto",
        "moego_pay_contract_service.proto",
        "moego_pay_custom_fee_approval_service.proto",
        "sales_enums.proto",
        "sales_service.proto",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_bufbuild_protovalidate//proto/protovalidate/buf/validate:validate_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@googleapis//google/type:decimal_proto",
        "@googleapis//google/type:money_proto",
    ],
)

go_proto_library(
    name = "salespb_go_proto",
    compilers = [
        "@io_bazel_rules_go//proto:go_grpc_v2",
        "@io_bazel_rules_go//proto:go_proto",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/sales/v1",
    proto = ":salespb_proto",
    visibility = ["//visibility:public"],
    deps = [
        "@build_buf_gen_go_bufbuild_protovalidate_protocolbuffers_go//buf/validate:go_default_library",
        "@org_golang_google_genproto//googleapis/type/decimal",
        "@org_golang_google_genproto//googleapis/type/money",
    ],
)

go_library(
    name = "sales",
    embed = [":salespb_go_proto"],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/sales/v1",
    visibility = ["//visibility:public"],
)
