// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/sales/v1/sales_service.proto

package salespb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	SalesService_SyncOpportunity_FullMethodName        = "/backend.proto.sales.v1.SalesService/SyncOpportunity"
	SalesService_SyncSalesSubscription_FullMethodName  = "/backend.proto.sales.v1.SalesService/SyncSalesSubscription"
	SalesService_SyncSalesHardware_FullMethodName      = "/backend.proto.sales.v1.SalesService/SyncSalesHardware"
	SalesService_CheckOpportunityExists_FullMethodName = "/backend.proto.sales.v1.SalesService/CheckOpportunityExists"
)

// SalesServiceClient is the client API for SalesService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// SalesService
type SalesServiceClient interface {
	// SyncOpportunity updates an opportunity.
	SyncOpportunity(ctx context.Context, in *SyncOpportunityRequest, opts ...grpc.CallOption) (*SyncOpportunityResponse, error)
	// SyncSalesSubscription add subscription line items to an opportunity.
	SyncSalesSubscription(ctx context.Context, in *SyncSalesSubscriptionRequest, opts ...grpc.CallOption) (*SyncSalesSubscriptionResponse, error)
	// SyncSalesHardware add hardware line items to an opportunity.
	SyncSalesHardware(ctx context.Context, in *SyncSalesHardwareRequest, opts ...grpc.CallOption) (*SyncSalesHardwareResponse, error)
	// CheckOpportunityExists checks if an opportunity exists.
	CheckOpportunityExists(ctx context.Context, in *CheckOpportunityExistsRequest, opts ...grpc.CallOption) (*CheckOpportunityExistsResponse, error)
}

type salesServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSalesServiceClient(cc grpc.ClientConnInterface) SalesServiceClient {
	return &salesServiceClient{cc}
}

func (c *salesServiceClient) SyncOpportunity(ctx context.Context, in *SyncOpportunityRequest, opts ...grpc.CallOption) (*SyncOpportunityResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncOpportunityResponse)
	err := c.cc.Invoke(ctx, SalesService_SyncOpportunity_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesServiceClient) SyncSalesSubscription(ctx context.Context, in *SyncSalesSubscriptionRequest, opts ...grpc.CallOption) (*SyncSalesSubscriptionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncSalesSubscriptionResponse)
	err := c.cc.Invoke(ctx, SalesService_SyncSalesSubscription_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesServiceClient) SyncSalesHardware(ctx context.Context, in *SyncSalesHardwareRequest, opts ...grpc.CallOption) (*SyncSalesHardwareResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncSalesHardwareResponse)
	err := c.cc.Invoke(ctx, SalesService_SyncSalesHardware_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesServiceClient) CheckOpportunityExists(ctx context.Context, in *CheckOpportunityExistsRequest, opts ...grpc.CallOption) (*CheckOpportunityExistsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckOpportunityExistsResponse)
	err := c.cc.Invoke(ctx, SalesService_CheckOpportunityExists_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SalesServiceServer is the server API for SalesService service.
// All implementations must embed UnimplementedSalesServiceServer
// for forward compatibility.
//
// SalesService
type SalesServiceServer interface {
	// SyncOpportunity updates an opportunity.
	SyncOpportunity(context.Context, *SyncOpportunityRequest) (*SyncOpportunityResponse, error)
	// SyncSalesSubscription add subscription line items to an opportunity.
	SyncSalesSubscription(context.Context, *SyncSalesSubscriptionRequest) (*SyncSalesSubscriptionResponse, error)
	// SyncSalesHardware add hardware line items to an opportunity.
	SyncSalesHardware(context.Context, *SyncSalesHardwareRequest) (*SyncSalesHardwareResponse, error)
	// CheckOpportunityExists checks if an opportunity exists.
	CheckOpportunityExists(context.Context, *CheckOpportunityExistsRequest) (*CheckOpportunityExistsResponse, error)
	mustEmbedUnimplementedSalesServiceServer()
}

// UnimplementedSalesServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSalesServiceServer struct{}

func (UnimplementedSalesServiceServer) SyncOpportunity(context.Context, *SyncOpportunityRequest) (*SyncOpportunityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncOpportunity not implemented")
}
func (UnimplementedSalesServiceServer) SyncSalesSubscription(context.Context, *SyncSalesSubscriptionRequest) (*SyncSalesSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncSalesSubscription not implemented")
}
func (UnimplementedSalesServiceServer) SyncSalesHardware(context.Context, *SyncSalesHardwareRequest) (*SyncSalesHardwareResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncSalesHardware not implemented")
}
func (UnimplementedSalesServiceServer) CheckOpportunityExists(context.Context, *CheckOpportunityExistsRequest) (*CheckOpportunityExistsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckOpportunityExists not implemented")
}
func (UnimplementedSalesServiceServer) mustEmbedUnimplementedSalesServiceServer() {}
func (UnimplementedSalesServiceServer) testEmbeddedByValue()                      {}

// UnsafeSalesServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SalesServiceServer will
// result in compilation errors.
type UnsafeSalesServiceServer interface {
	mustEmbedUnimplementedSalesServiceServer()
}

func RegisterSalesServiceServer(s grpc.ServiceRegistrar, srv SalesServiceServer) {
	// If the following call pancis, it indicates UnimplementedSalesServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&SalesService_ServiceDesc, srv)
}

func _SalesService_SyncOpportunity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncOpportunityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesServiceServer).SyncOpportunity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SalesService_SyncOpportunity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesServiceServer).SyncOpportunity(ctx, req.(*SyncOpportunityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesService_SyncSalesSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncSalesSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesServiceServer).SyncSalesSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SalesService_SyncSalesSubscription_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesServiceServer).SyncSalesSubscription(ctx, req.(*SyncSalesSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesService_SyncSalesHardware_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncSalesHardwareRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesServiceServer).SyncSalesHardware(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SalesService_SyncSalesHardware_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesServiceServer).SyncSalesHardware(ctx, req.(*SyncSalesHardwareRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesService_CheckOpportunityExists_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckOpportunityExistsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesServiceServer).CheckOpportunityExists(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SalesService_CheckOpportunityExists_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesServiceServer).CheckOpportunityExists(ctx, req.(*CheckOpportunityExistsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SalesService_ServiceDesc is the grpc.ServiceDesc for SalesService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SalesService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.sales.v1.SalesService",
	HandlerType: (*SalesServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SyncOpportunity",
			Handler:    _SalesService_SyncOpportunity_Handler,
		},
		{
			MethodName: "SyncSalesSubscription",
			Handler:    _SalesService_SyncSalesSubscription_Handler,
		},
		{
			MethodName: "SyncSalesHardware",
			Handler:    _SalesService_SyncSalesHardware_Handler,
		},
		{
			MethodName: "CheckOpportunityExists",
			Handler:    _SalesService_CheckOpportunityExists_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/sales/v1/sales_service.proto",
}
