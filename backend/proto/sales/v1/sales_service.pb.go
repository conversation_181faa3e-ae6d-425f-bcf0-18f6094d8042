// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.8
// 	protoc        (unknown)
// source: backend/proto/sales/v1/sales_service.proto

package salespb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	decimal "google.golang.org/genproto/googleapis/type/decimal"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ErrCode 定义错误码枚举
//
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
type ErrCode int32

const (
	// (-- api-linter: core::0126::unspecified=disabled
	//
	//	aip.dev/not-precedent: We need to do this because
	//	the content of the error code is automatically generated by
	//	the script and is exclusive to each service.
	//	Please do not turn off this linter for the rest of the enum --)
	//
	// 成功
	ErrCode_ERR_CODE_OK ErrCode = 0
	// 本服务自动分配的全局唯一的起始错误码
	ErrCode_ERR_CODE_UNSPECIFIED ErrCode = 993900
)

// Enum value maps for ErrCode.
var (
	ErrCode_name = map[int32]string{
		0:      "ERR_CODE_OK",
		993900: "ERR_CODE_UNSPECIFIED",
	}
	ErrCode_value = map[string]int32{
		"ERR_CODE_OK":          0,
		"ERR_CODE_UNSPECIFIED": 993900,
	}
)

func (x ErrCode) Enum() *ErrCode {
	p := new(ErrCode)
	*p = x
	return p
}

func (x ErrCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrCode) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_sales_v1_sales_service_proto_enumTypes[0].Descriptor()
}

func (ErrCode) Type() protoreflect.EnumType {
	return &file_backend_proto_sales_v1_sales_service_proto_enumTypes[0]
}

func (x ErrCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrCode.Descriptor instead.
func (ErrCode) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_sales_service_proto_rawDescGZIP(), []int{0}
}

// SyncOpportunityRequest
type SyncOpportunityRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// opportunity id
	OpportunityId string `protobuf:"bytes,1,opt,name=opportunity_id,json=opportunityId,proto3" json:"opportunity_id,omitempty"`
	// email
	Email *string `protobuf:"bytes,2,opt,name=email,proto3,oneof" json:"email,omitempty"`
	// tier
	Tier *string `protobuf:"bytes,3,opt,name=tier,proto3,oneof" json:"tier,omitempty"`
	// terminal percentage
	TerminalPercentage *decimal.Decimal `protobuf:"bytes,4,opt,name=terminal_percentage,json=terminalPercentage,proto3,oneof" json:"terminal_percentage,omitempty"`
	// terminal fixed
	TerminalFixed *decimal.Decimal `protobuf:"bytes,5,opt,name=terminal_fixed,json=terminalFixed,proto3,oneof" json:"terminal_fixed,omitempty"`
	// non terminal percentage
	NonTerminalPercentage *decimal.Decimal `protobuf:"bytes,6,opt,name=non_terminal_percentage,json=nonTerminalPercentage,proto3,oneof" json:"non_terminal_percentage,omitempty"`
	// non terminal fixed
	NonTerminalFixed *decimal.Decimal `protobuf:"bytes,7,opt,name=non_terminal_fixed,json=nonTerminalFixed,proto3,oneof" json:"non_terminal_fixed,omitempty"`
	// min_volume
	MinVolume *decimal.Decimal `protobuf:"bytes,8,opt,name=min_volume,json=minVolume,proto3,oneof" json:"min_volume,omitempty"`
	// spif
	Spif          *decimal.Decimal `protobuf:"bytes,9,opt,name=spif,proto3,oneof" json:"spif,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncOpportunityRequest) Reset() {
	*x = SyncOpportunityRequest{}
	mi := &file_backend_proto_sales_v1_sales_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncOpportunityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncOpportunityRequest) ProtoMessage() {}

func (x *SyncOpportunityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_sales_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncOpportunityRequest.ProtoReflect.Descriptor instead.
func (*SyncOpportunityRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_sales_service_proto_rawDescGZIP(), []int{0}
}

func (x *SyncOpportunityRequest) GetOpportunityId() string {
	if x != nil {
		return x.OpportunityId
	}
	return ""
}

func (x *SyncOpportunityRequest) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *SyncOpportunityRequest) GetTier() string {
	if x != nil && x.Tier != nil {
		return *x.Tier
	}
	return ""
}

func (x *SyncOpportunityRequest) GetTerminalPercentage() *decimal.Decimal {
	if x != nil {
		return x.TerminalPercentage
	}
	return nil
}

func (x *SyncOpportunityRequest) GetTerminalFixed() *decimal.Decimal {
	if x != nil {
		return x.TerminalFixed
	}
	return nil
}

func (x *SyncOpportunityRequest) GetNonTerminalPercentage() *decimal.Decimal {
	if x != nil {
		return x.NonTerminalPercentage
	}
	return nil
}

func (x *SyncOpportunityRequest) GetNonTerminalFixed() *decimal.Decimal {
	if x != nil {
		return x.NonTerminalFixed
	}
	return nil
}

func (x *SyncOpportunityRequest) GetMinVolume() *decimal.Decimal {
	if x != nil {
		return x.MinVolume
	}
	return nil
}

func (x *SyncOpportunityRequest) GetSpif() *decimal.Decimal {
	if x != nil {
		return x.Spif
	}
	return nil
}

// SyncOpportunityResponse
type SyncOpportunityResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncOpportunityResponse) Reset() {
	*x = SyncOpportunityResponse{}
	mi := &file_backend_proto_sales_v1_sales_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncOpportunityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncOpportunityResponse) ProtoMessage() {}

func (x *SyncOpportunityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_sales_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncOpportunityResponse.ProtoReflect.Descriptor instead.
func (*SyncOpportunityResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_sales_service_proto_rawDescGZIP(), []int{1}
}

// SyncSalesSubscriptionRequest
type SyncSalesSubscriptionRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// sales code
	SalesCode string `protobuf:"bytes,1,opt,name=sales_code,json=salesCode,proto3" json:"sales_code,omitempty"`
	// opportunity id
	OpportunityId string `protobuf:"bytes,2,opt,name=opportunity_id,json=opportunityId,proto3" json:"opportunity_id,omitempty"`
	// subscription plan
	SubscriptionPlan SubscriptionPlan `protobuf:"varint,3,opt,name=subscription_plan,json=subscriptionPlan,proto3,enum=backend.proto.sales.v1.SubscriptionPlan" json:"subscription_plan,omitempty"`
	// salon
	Salon *LineItem `protobuf:"bytes,5,opt,name=salon,proto3,oneof" json:"salon,omitempty"`
	// van
	Van           *LineItem `protobuf:"bytes,6,opt,name=van,proto3,oneof" json:"van,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncSalesSubscriptionRequest) Reset() {
	*x = SyncSalesSubscriptionRequest{}
	mi := &file_backend_proto_sales_v1_sales_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncSalesSubscriptionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncSalesSubscriptionRequest) ProtoMessage() {}

func (x *SyncSalesSubscriptionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_sales_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncSalesSubscriptionRequest.ProtoReflect.Descriptor instead.
func (*SyncSalesSubscriptionRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_sales_service_proto_rawDescGZIP(), []int{2}
}

func (x *SyncSalesSubscriptionRequest) GetSalesCode() string {
	if x != nil {
		return x.SalesCode
	}
	return ""
}

func (x *SyncSalesSubscriptionRequest) GetOpportunityId() string {
	if x != nil {
		return x.OpportunityId
	}
	return ""
}

func (x *SyncSalesSubscriptionRequest) GetSubscriptionPlan() SubscriptionPlan {
	if x != nil {
		return x.SubscriptionPlan
	}
	return SubscriptionPlan_SUBSCRIPTION_PLAN_UNSPECIFIED
}

func (x *SyncSalesSubscriptionRequest) GetSalon() *LineItem {
	if x != nil {
		return x.Salon
	}
	return nil
}

func (x *SyncSalesSubscriptionRequest) GetVan() *LineItem {
	if x != nil {
		return x.Van
	}
	return nil
}

// SyncSalesSubscriptionResponse
type SyncSalesSubscriptionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncSalesSubscriptionResponse) Reset() {
	*x = SyncSalesSubscriptionResponse{}
	mi := &file_backend_proto_sales_v1_sales_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncSalesSubscriptionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncSalesSubscriptionResponse) ProtoMessage() {}

func (x *SyncSalesSubscriptionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_sales_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncSalesSubscriptionResponse.ProtoReflect.Descriptor instead.
func (*SyncSalesSubscriptionResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_sales_service_proto_rawDescGZIP(), []int{3}
}

// SyncSalesHardwareRequest
type SyncSalesHardwareRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// sales code
	SalesCode string `protobuf:"bytes,1,opt,name=sales_code,json=salesCode,proto3" json:"sales_code,omitempty"`
	// opportunity id
	OpportunityId string `protobuf:"bytes,2,opt,name=opportunity_id,json=opportunityId,proto3" json:"opportunity_id,omitempty"`
	// reader m2
	ReaderM2 *LineItem `protobuf:"bytes,3,opt,name=reader_m2,json=readerM2,proto3,oneof" json:"reader_m2,omitempty"`
	// bbpos
	Bbpos         *LineItem `protobuf:"bytes,4,opt,name=bbpos,proto3,oneof" json:"bbpos,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncSalesHardwareRequest) Reset() {
	*x = SyncSalesHardwareRequest{}
	mi := &file_backend_proto_sales_v1_sales_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncSalesHardwareRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncSalesHardwareRequest) ProtoMessage() {}

func (x *SyncSalesHardwareRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_sales_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncSalesHardwareRequest.ProtoReflect.Descriptor instead.
func (*SyncSalesHardwareRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_sales_service_proto_rawDescGZIP(), []int{4}
}

func (x *SyncSalesHardwareRequest) GetSalesCode() string {
	if x != nil {
		return x.SalesCode
	}
	return ""
}

func (x *SyncSalesHardwareRequest) GetOpportunityId() string {
	if x != nil {
		return x.OpportunityId
	}
	return ""
}

func (x *SyncSalesHardwareRequest) GetReaderM2() *LineItem {
	if x != nil {
		return x.ReaderM2
	}
	return nil
}

func (x *SyncSalesHardwareRequest) GetBbpos() *LineItem {
	if x != nil {
		return x.Bbpos
	}
	return nil
}

// SyncSalesHardwareResponse
type SyncSalesHardwareResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncSalesHardwareResponse) Reset() {
	*x = SyncSalesHardwareResponse{}
	mi := &file_backend_proto_sales_v1_sales_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncSalesHardwareResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncSalesHardwareResponse) ProtoMessage() {}

func (x *SyncSalesHardwareResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_sales_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncSalesHardwareResponse.ProtoReflect.Descriptor instead.
func (*SyncSalesHardwareResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_sales_service_proto_rawDescGZIP(), []int{5}
}

// CheckOpportunityExistsRequest
type CheckOpportunityExistsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// opportunity id
	OpportunityId string `protobuf:"bytes,1,opt,name=opportunity_id,json=opportunityId,proto3" json:"opportunity_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckOpportunityExistsRequest) Reset() {
	*x = CheckOpportunityExistsRequest{}
	mi := &file_backend_proto_sales_v1_sales_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckOpportunityExistsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckOpportunityExistsRequest) ProtoMessage() {}

func (x *CheckOpportunityExistsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_sales_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckOpportunityExistsRequest.ProtoReflect.Descriptor instead.
func (*CheckOpportunityExistsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_sales_service_proto_rawDescGZIP(), []int{6}
}

func (x *CheckOpportunityExistsRequest) GetOpportunityId() string {
	if x != nil {
		return x.OpportunityId
	}
	return ""
}

// CheckOpportunityExistsResponse
type CheckOpportunityExistsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// exists
	Exists        bool `protobuf:"varint,1,opt,name=exists,proto3" json:"exists,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckOpportunityExistsResponse) Reset() {
	*x = CheckOpportunityExistsResponse{}
	mi := &file_backend_proto_sales_v1_sales_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckOpportunityExistsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckOpportunityExistsResponse) ProtoMessage() {}

func (x *CheckOpportunityExistsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_sales_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckOpportunityExistsResponse.ProtoReflect.Descriptor instead.
func (*CheckOpportunityExistsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_sales_service_proto_rawDescGZIP(), []int{7}
}

func (x *CheckOpportunityExistsResponse) GetExists() bool {
	if x != nil {
		return x.Exists
	}
	return false
}

// line item
type LineItem struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// quantity
	Quantity int32 `protobuf:"varint,1,opt,name=quantity,proto3" json:"quantity,omitempty"`
	// unit price (before discount)
	UnitPrice *money.Money `protobuf:"bytes,2,opt,name=unit_price,json=unitPrice,proto3" json:"unit_price,omitempty"`
	// discount percentage
	DiscountPercentage *decimal.Decimal `protobuf:"bytes,3,opt,name=discount_percentage,json=discountPercentage,proto3,oneof" json:"discount_percentage,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *LineItem) Reset() {
	*x = LineItem{}
	mi := &file_backend_proto_sales_v1_sales_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LineItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LineItem) ProtoMessage() {}

func (x *LineItem) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_sales_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LineItem.ProtoReflect.Descriptor instead.
func (*LineItem) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_sales_service_proto_rawDescGZIP(), []int{8}
}

func (x *LineItem) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *LineItem) GetUnitPrice() *money.Money {
	if x != nil {
		return x.UnitPrice
	}
	return nil
}

func (x *LineItem) GetDiscountPercentage() *decimal.Decimal {
	if x != nil {
		return x.DiscountPercentage
	}
	return nil
}

var File_backend_proto_sales_v1_sales_service_proto protoreflect.FileDescriptor

const file_backend_proto_sales_v1_sales_service_proto_rawDesc = "" +
	"\n" +
	"*backend/proto/sales/v1/sales_service.proto\x12\x16backend.proto.sales.v1\x1a(backend/proto/sales/v1/sales_enums.proto\x1a\x1bbuf/validate/validate.proto\x1a\x19google/type/decimal.proto\x1a\x17google/type/money.proto\"\xaa\x05\n" +
	"\x16SyncOpportunityRequest\x12.\n" +
	"\x0eopportunity_id\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\ropportunityId\x12\"\n" +
	"\x05email\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x10\x01H\x00R\x05email\x88\x01\x01\x12 \n" +
	"\x04tier\x18\x03 \x01(\tB\a\xbaH\x04r\x02\x10\x01H\x01R\x04tier\x88\x01\x01\x12J\n" +
	"\x13terminal_percentage\x18\x04 \x01(\v2\x14.google.type.DecimalH\x02R\x12terminalPercentage\x88\x01\x01\x12@\n" +
	"\x0eterminal_fixed\x18\x05 \x01(\v2\x14.google.type.DecimalH\x03R\rterminalFixed\x88\x01\x01\x12Q\n" +
	"\x17non_terminal_percentage\x18\x06 \x01(\v2\x14.google.type.DecimalH\x04R\x15nonTerminalPercentage\x88\x01\x01\x12G\n" +
	"\x12non_terminal_fixed\x18\a \x01(\v2\x14.google.type.DecimalH\x05R\x10nonTerminalFixed\x88\x01\x01\x128\n" +
	"\n" +
	"min_volume\x18\b \x01(\v2\x14.google.type.DecimalH\x06R\tminVolume\x88\x01\x01\x12-\n" +
	"\x04spif\x18\t \x01(\v2\x14.google.type.DecimalH\aR\x04spif\x88\x01\x01B\b\n" +
	"\x06_emailB\a\n" +
	"\x05_tierB\x16\n" +
	"\x14_terminal_percentageB\x11\n" +
	"\x0f_terminal_fixedB\x1a\n" +
	"\x18_non_terminal_percentageB\x15\n" +
	"\x13_non_terminal_fixedB\r\n" +
	"\v_min_volumeB\a\n" +
	"\x05_spif\"\x19\n" +
	"\x17SyncOpportunityResponse\"\xd5\x02\n" +
	"\x1cSyncSalesSubscriptionRequest\x12&\n" +
	"\n" +
	"sales_code\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\tsalesCode\x12.\n" +
	"\x0eopportunity_id\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\ropportunityId\x12U\n" +
	"\x11subscription_plan\x18\x03 \x01(\x0e2(.backend.proto.sales.v1.SubscriptionPlanR\x10subscriptionPlan\x12;\n" +
	"\x05salon\x18\x05 \x01(\v2 .backend.proto.sales.v1.LineItemH\x00R\x05salon\x88\x01\x01\x127\n" +
	"\x03van\x18\x06 \x01(\v2 .backend.proto.sales.v1.LineItemH\x01R\x03van\x88\x01\x01B\b\n" +
	"\x06_salonB\x06\n" +
	"\x04_van\"\x1f\n" +
	"\x1dSyncSalesSubscriptionResponse\"\x8b\x02\n" +
	"\x18SyncSalesHardwareRequest\x12&\n" +
	"\n" +
	"sales_code\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\tsalesCode\x12.\n" +
	"\x0eopportunity_id\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\ropportunityId\x12B\n" +
	"\treader_m2\x18\x03 \x01(\v2 .backend.proto.sales.v1.LineItemH\x00R\breaderM2\x88\x01\x01\x12;\n" +
	"\x05bbpos\x18\x04 \x01(\v2 .backend.proto.sales.v1.LineItemH\x01R\x05bbpos\x88\x01\x01B\f\n" +
	"\n" +
	"_reader_m2B\b\n" +
	"\x06_bbpos\"\x1b\n" +
	"\x19SyncSalesHardwareResponse\"O\n" +
	"\x1dCheckOpportunityExistsRequest\x12.\n" +
	"\x0eopportunity_id\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\ropportunityId\"8\n" +
	"\x1eCheckOpportunityExistsResponse\x12\x16\n" +
	"\x06exists\x18\x01 \x01(\bR\x06exists\"\xc6\x01\n" +
	"\bLineItem\x12#\n" +
	"\bquantity\x18\x01 \x01(\x05B\a\xbaH\x04\x1a\x02 \x00R\bquantity\x121\n" +
	"\n" +
	"unit_price\x18\x02 \x01(\v2\x12.google.type.MoneyR\tunitPrice\x12J\n" +
	"\x13discount_percentage\x18\x03 \x01(\v2\x14.google.type.DecimalH\x00R\x12discountPercentage\x88\x01\x01B\x16\n" +
	"\x14_discount_percentage*6\n" +
	"\aErrCode\x12\x0f\n" +
	"\vERR_CODE_OK\x10\x00\x12\x1a\n" +
	"\x14ERR_CODE_UNSPECIFIED\x10\xec\xd4<2\x8d\x04\n" +
	"\fSalesService\x12r\n" +
	"\x0fSyncOpportunity\x12..backend.proto.sales.v1.SyncOpportunityRequest\x1a/.backend.proto.sales.v1.SyncOpportunityResponse\x12\x84\x01\n" +
	"\x15SyncSalesSubscription\x124.backend.proto.sales.v1.SyncSalesSubscriptionRequest\x1a5.backend.proto.sales.v1.SyncSalesSubscriptionResponse\x12x\n" +
	"\x11SyncSalesHardware\x120.backend.proto.sales.v1.SyncSalesHardwareRequest\x1a1.backend.proto.sales.v1.SyncSalesHardwareResponse\x12\x87\x01\n" +
	"\x16CheckOpportunityExists\x125.backend.proto.sales.v1.CheckOpportunityExistsRequest\x1a6.backend.proto.sales.v1.CheckOpportunityExistsResponseBb\n" +
	" com.moego.backend.proto.sales.v1P\x01Z<github.com/MoeGolibrary/moego/backend/proto/sales/v1;salespbb\x06proto3"

var (
	file_backend_proto_sales_v1_sales_service_proto_rawDescOnce sync.Once
	file_backend_proto_sales_v1_sales_service_proto_rawDescData []byte
)

func file_backend_proto_sales_v1_sales_service_proto_rawDescGZIP() []byte {
	file_backend_proto_sales_v1_sales_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_sales_v1_sales_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_sales_v1_sales_service_proto_rawDesc), len(file_backend_proto_sales_v1_sales_service_proto_rawDesc)))
	})
	return file_backend_proto_sales_v1_sales_service_proto_rawDescData
}

var file_backend_proto_sales_v1_sales_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_backend_proto_sales_v1_sales_service_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_backend_proto_sales_v1_sales_service_proto_goTypes = []any{
	(ErrCode)(0),                           // 0: backend.proto.sales.v1.ErrCode
	(*SyncOpportunityRequest)(nil),         // 1: backend.proto.sales.v1.SyncOpportunityRequest
	(*SyncOpportunityResponse)(nil),        // 2: backend.proto.sales.v1.SyncOpportunityResponse
	(*SyncSalesSubscriptionRequest)(nil),   // 3: backend.proto.sales.v1.SyncSalesSubscriptionRequest
	(*SyncSalesSubscriptionResponse)(nil),  // 4: backend.proto.sales.v1.SyncSalesSubscriptionResponse
	(*SyncSalesHardwareRequest)(nil),       // 5: backend.proto.sales.v1.SyncSalesHardwareRequest
	(*SyncSalesHardwareResponse)(nil),      // 6: backend.proto.sales.v1.SyncSalesHardwareResponse
	(*CheckOpportunityExistsRequest)(nil),  // 7: backend.proto.sales.v1.CheckOpportunityExistsRequest
	(*CheckOpportunityExistsResponse)(nil), // 8: backend.proto.sales.v1.CheckOpportunityExistsResponse
	(*LineItem)(nil),                       // 9: backend.proto.sales.v1.LineItem
	(*decimal.Decimal)(nil),                // 10: google.type.Decimal
	(SubscriptionPlan)(0),                  // 11: backend.proto.sales.v1.SubscriptionPlan
	(*money.Money)(nil),                    // 12: google.type.Money
}
var file_backend_proto_sales_v1_sales_service_proto_depIdxs = []int32{
	10, // 0: backend.proto.sales.v1.SyncOpportunityRequest.terminal_percentage:type_name -> google.type.Decimal
	10, // 1: backend.proto.sales.v1.SyncOpportunityRequest.terminal_fixed:type_name -> google.type.Decimal
	10, // 2: backend.proto.sales.v1.SyncOpportunityRequest.non_terminal_percentage:type_name -> google.type.Decimal
	10, // 3: backend.proto.sales.v1.SyncOpportunityRequest.non_terminal_fixed:type_name -> google.type.Decimal
	10, // 4: backend.proto.sales.v1.SyncOpportunityRequest.min_volume:type_name -> google.type.Decimal
	10, // 5: backend.proto.sales.v1.SyncOpportunityRequest.spif:type_name -> google.type.Decimal
	11, // 6: backend.proto.sales.v1.SyncSalesSubscriptionRequest.subscription_plan:type_name -> backend.proto.sales.v1.SubscriptionPlan
	9,  // 7: backend.proto.sales.v1.SyncSalesSubscriptionRequest.salon:type_name -> backend.proto.sales.v1.LineItem
	9,  // 8: backend.proto.sales.v1.SyncSalesSubscriptionRequest.van:type_name -> backend.proto.sales.v1.LineItem
	9,  // 9: backend.proto.sales.v1.SyncSalesHardwareRequest.reader_m2:type_name -> backend.proto.sales.v1.LineItem
	9,  // 10: backend.proto.sales.v1.SyncSalesHardwareRequest.bbpos:type_name -> backend.proto.sales.v1.LineItem
	12, // 11: backend.proto.sales.v1.LineItem.unit_price:type_name -> google.type.Money
	10, // 12: backend.proto.sales.v1.LineItem.discount_percentage:type_name -> google.type.Decimal
	1,  // 13: backend.proto.sales.v1.SalesService.SyncOpportunity:input_type -> backend.proto.sales.v1.SyncOpportunityRequest
	3,  // 14: backend.proto.sales.v1.SalesService.SyncSalesSubscription:input_type -> backend.proto.sales.v1.SyncSalesSubscriptionRequest
	5,  // 15: backend.proto.sales.v1.SalesService.SyncSalesHardware:input_type -> backend.proto.sales.v1.SyncSalesHardwareRequest
	7,  // 16: backend.proto.sales.v1.SalesService.CheckOpportunityExists:input_type -> backend.proto.sales.v1.CheckOpportunityExistsRequest
	2,  // 17: backend.proto.sales.v1.SalesService.SyncOpportunity:output_type -> backend.proto.sales.v1.SyncOpportunityResponse
	4,  // 18: backend.proto.sales.v1.SalesService.SyncSalesSubscription:output_type -> backend.proto.sales.v1.SyncSalesSubscriptionResponse
	6,  // 19: backend.proto.sales.v1.SalesService.SyncSalesHardware:output_type -> backend.proto.sales.v1.SyncSalesHardwareResponse
	8,  // 20: backend.proto.sales.v1.SalesService.CheckOpportunityExists:output_type -> backend.proto.sales.v1.CheckOpportunityExistsResponse
	17, // [17:21] is the sub-list for method output_type
	13, // [13:17] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_backend_proto_sales_v1_sales_service_proto_init() }
func file_backend_proto_sales_v1_sales_service_proto_init() {
	if File_backend_proto_sales_v1_sales_service_proto != nil {
		return
	}
	file_backend_proto_sales_v1_sales_enums_proto_init()
	file_backend_proto_sales_v1_sales_service_proto_msgTypes[0].OneofWrappers = []any{}
	file_backend_proto_sales_v1_sales_service_proto_msgTypes[2].OneofWrappers = []any{}
	file_backend_proto_sales_v1_sales_service_proto_msgTypes[4].OneofWrappers = []any{}
	file_backend_proto_sales_v1_sales_service_proto_msgTypes[8].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_sales_v1_sales_service_proto_rawDesc), len(file_backend_proto_sales_v1_sales_service_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_sales_v1_sales_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_sales_v1_sales_service_proto_depIdxs,
		EnumInfos:         file_backend_proto_sales_v1_sales_service_proto_enumTypes,
		MessageInfos:      file_backend_proto_sales_v1_sales_service_proto_msgTypes,
	}.Build()
	File_backend_proto_sales_v1_sales_service_proto = out.File
	file_backend_proto_sales_v1_sales_service_proto_goTypes = nil
	file_backend_proto_sales_v1_sales_service_proto_depIdxs = nil
}
