syntax = "proto3";

package backend.proto.sales.v1;

option go_package="github.com/MoeGolibrary/moego/backend/proto/sales/v1;salespb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.sales.v1";

import "buf/validate/validate.proto";

// PlatformSalesService
service PlatformSalesService {
  // SyncPlatformSales
  rpc SyncPlatformSales(SyncPlatformSalesRequest) returns (SyncPlatformSalesResponse);

}

// SyncPlatformSalesRequest
message SyncPlatformSalesRequest {
  // sales code
  string sales_code = 1 [(buf.validate.field).string.min_len = 1];
}

// SyncPlatformSalesResponse
message SyncPlatformSalesResponse {}
