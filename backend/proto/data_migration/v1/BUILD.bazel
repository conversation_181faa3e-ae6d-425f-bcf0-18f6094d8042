load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "datamigrationpb_proto",
    srcs = ["data_import_service.proto"],
    visibility = ["//visibility:public"],
    deps = ["@com_github_bufbuild_protovalidate//proto/protovalidate/buf/validate:validate_proto"],
)

go_proto_library(
    name = "datamigrationpb_go_proto",
    compilers = [
        "@io_bazel_rules_go//proto:go_grpc_v2",
        "@io_bazel_rules_go//proto:go_proto",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/data_migration/v1",
    proto = ":datamigrationpb_proto",
    visibility = ["//visibility:public"],
    deps = ["@build_buf_gen_go_bufbuild_protovalidate_protocolbuffers_go//buf/validate:go_default_library"],
)

go_library(
    name = "data_migration",
    embed = [":datamigrationpb_go_proto"],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/data_migration/v1",
    visibility = ["//visibility:public"],
)
