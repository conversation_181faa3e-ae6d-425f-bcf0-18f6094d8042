// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/data_migration/v1/data_import_service.proto

package datamigrationpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	DataImportService_SendPing_FullMethodName   = "/backend.proto.data_migration.v1.DataImportService/SendPing"
	DataImportService_ImportData_FullMethodName = "/backend.proto.data_migration.v1.DataImportService/ImportData"
)

// DataImportServiceClient is the client API for DataImportService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// DataImportService
type DataImportServiceClient interface {
	// SendPing sends a ping request and returns a pong response
	SendPing(ctx context.Context, in *SendPingRequest, opts ...grpc.CallOption) (*SendPingResponse, error)
	// ImportData
	ImportData(ctx context.Context, in *ImportDataRequest, opts ...grpc.CallOption) (*ImportDataResponse, error)
}

type dataImportServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDataImportServiceClient(cc grpc.ClientConnInterface) DataImportServiceClient {
	return &dataImportServiceClient{cc}
}

func (c *dataImportServiceClient) SendPing(ctx context.Context, in *SendPingRequest, opts ...grpc.CallOption) (*SendPingResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendPingResponse)
	err := c.cc.Invoke(ctx, DataImportService_SendPing_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataImportServiceClient) ImportData(ctx context.Context, in *ImportDataRequest, opts ...grpc.CallOption) (*ImportDataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ImportDataResponse)
	err := c.cc.Invoke(ctx, DataImportService_ImportData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DataImportServiceServer is the server API for DataImportService service.
// All implementations must embed UnimplementedDataImportServiceServer
// for forward compatibility.
//
// DataImportService
type DataImportServiceServer interface {
	// SendPing sends a ping request and returns a pong response
	SendPing(context.Context, *SendPingRequest) (*SendPingResponse, error)
	// ImportData
	ImportData(context.Context, *ImportDataRequest) (*ImportDataResponse, error)
	mustEmbedUnimplementedDataImportServiceServer()
}

// UnimplementedDataImportServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedDataImportServiceServer struct{}

func (UnimplementedDataImportServiceServer) SendPing(context.Context, *SendPingRequest) (*SendPingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendPing not implemented")
}
func (UnimplementedDataImportServiceServer) ImportData(context.Context, *ImportDataRequest) (*ImportDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImportData not implemented")
}
func (UnimplementedDataImportServiceServer) mustEmbedUnimplementedDataImportServiceServer() {}
func (UnimplementedDataImportServiceServer) testEmbeddedByValue()                           {}

// UnsafeDataImportServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DataImportServiceServer will
// result in compilation errors.
type UnsafeDataImportServiceServer interface {
	mustEmbedUnimplementedDataImportServiceServer()
}

func RegisterDataImportServiceServer(s grpc.ServiceRegistrar, srv DataImportServiceServer) {
	// If the following call pancis, it indicates UnimplementedDataImportServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&DataImportService_ServiceDesc, srv)
}

func _DataImportService_SendPing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendPingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataImportServiceServer).SendPing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataImportService_SendPing_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataImportServiceServer).SendPing(ctx, req.(*SendPingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataImportService_ImportData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImportDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataImportServiceServer).ImportData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataImportService_ImportData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataImportServiceServer).ImportData(ctx, req.(*ImportDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DataImportService_ServiceDesc is the grpc.ServiceDesc for DataImportService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DataImportService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.data_migration.v1.DataImportService",
	HandlerType: (*DataImportServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendPing",
			Handler:    _DataImportService_SendPing_Handler,
		},
		{
			MethodName: "ImportData",
			Handler:    _DataImportService_ImportData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/data_migration/v1/data_import_service.proto",
}
