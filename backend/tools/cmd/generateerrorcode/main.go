package main

import (
	"flag"
	"fmt"
	"hash/fnv"
	"os"
	"path"
	"strconv"

	yaml "gopkg.in/yaml.v3"

	"github.com/MoeGolibrary/moego/backend/tools/cmd/findsameerrorcode"
)

const (
	commonProtoAPIPath = "backend/proto"
	maxRetry           = 100
	hashModulo         = 100
	dirPermission      = 0777
	filePermission     = 0666
)

var (
	moduleName          = flag.String("moduleName", "", "moduleName")
	serviceName         = flag.String("serviceName", "", "serviceName")
	moduleYmlConfigPath = flag.String("moduleYmlConfigPath", "./backend/.modules.yml", "moduleYmlConfigPath")
)

// ModuleConfig 定义 .module.yml的结构体
type ModuleConfig struct {
	Module []*Module `yaml:"modules"`
}

// Module 包含 module对应的信息
type Module struct {
	Name  string `yaml:"name"`
	Desc  string `yaml:"desc"`
	Owner string `yaml:"owner"`
	Code  string `yaml:"code"`
}

func hash(moduleServiceName string) uint32 {
	h := fnv.New32a()
	_, err := h.Write([]byte(moduleServiceName))
	if err != nil {
		fmt.Printf("memory write error: %v\n", err)
		os.Exit(1)
	}
	return h.Sum32() % hashModulo
}

func trans2ModuleCode() (string, string) {
	var err error
	var yamlBs []byte
	if yamlBs, err = os.ReadFile(*moduleYmlConfigPath); err != nil {
		fmt.Printf("os.ReadFile error: %v\n", err)
		os.Exit(1)
	}
	conf := &ModuleConfig{}
	if err = yaml.Unmarshal(yamlBs, conf); err != nil {
		fmt.Printf("yaml.Unmarshal error: %v\n", err)
		os.Exit(1)
	}
	moduleCode := ""
	moduleOwner := ""
	for _, v := range conf.Module {
		if v.Name == *moduleName {
			moduleCode = v.Code
			moduleOwner = v.Owner
			break
		}
	}
	if moduleCode == "" {
		fmt.Println("no module found:" + *moduleName + ", module name must be in the following list:")
		for _, v := range conf.Module {
			fmt.Println("module:" + v.Name + " owner:" + v.Owner)
		}
		os.Exit(1)
	}
	return moduleCode, moduleOwner
}

func generateErrCode(moduleCode string, hashCode string) string {
	var errCode string
	var isGenerated = false
	for i := 0; i < maxRetry; i++ {
		if len(hashCode) == 1 {
			hashCode = "0" + hashCode
		}
		errCode = moduleCode + hashCode + "00"
		isExit, err := findsameerrorcode.Duplicate(commonProtoAPIPath, errCode)
		if err != nil {
			fmt.Println("find errCode error" + err.Error())
			os.Exit(1)
		}

		if isExit {
			tmp, _ := strconv.Atoi(hashCode)
			tmp = (tmp + 1) % hashModulo
			hashCode = strconv.FormatInt(int64(tmp), 10)
		} else {
			isGenerated = true
			break
		}
	}
	if !isGenerated {
		fmt.Println("system generated error code segment error, please contact jett/ark")
		os.Exit(1)
	}
	return errCode
}

func main() {
	flag.Parse()
	moduleCode, moduleOwner := trans2ModuleCode()
	hashCode := fmt.Sprint(hash(*moduleName + "-" + *serviceName))
	errCode := generateErrCode(moduleCode, hashCode)
	data := []byte(errCode)
	dir := "test_dir"
	if err := os.Mkdir(dir, dirPermission); err != nil {
		fmt.Println("failed to create temp directory" + err.Error())
		os.Exit(1)
	}
	msecFileName := path.Join(dir, "msec.txt")
	if err := os.WriteFile(msecFileName, data, filePermission); err != nil {
		fmt.Println("failed to write to file" + err.Error())
		os.Exit(1)
	}
	mownerData := []byte(moduleOwner)
	mownerFileName := path.Join(dir, "mowner.txt")
	if err := os.WriteFile(mownerFileName, mownerData, filePermission); err != nil {
		fmt.Println("failed to write to file" + err.Error())
		os.Exit(1)
	}
	fmt.Println("error code: " + errCode + " generated")
	os.Exit(0)
}
