#!/usr/bin/env bash

set -e

# Fetch origin/main to get the latest changes
echo "Fetching from origin/main..."
git fetch origin main

# Check if the current branch is behind origin/main
# This counts the number of commits in origin/main that are not in the current branch.
# If this number is greater than 0, it means the local branch is behind or has diverged.
if [[ $(git rev-list --count HEAD..origin/main) -gt 0 ]]; then
    echo "Error: Your branch is behind origin/main. Please merge or rebase to incorporate the latest changes before pushing."
    exit 1
fi
