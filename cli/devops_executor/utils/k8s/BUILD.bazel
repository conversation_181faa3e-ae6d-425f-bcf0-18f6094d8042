load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "k8s",
    srcs = ["client.go"],
    importpath = "github.com/MoeGolibrary/moego/cli/devops_executor/utils/k8s",
    visibility = ["//visibility:public"],
    deps = [
        "//cli/devops_executor/utils",
        "@io_k8s_client_go//kubernetes",
        "@io_k8s_client_go//rest",
        "@io_k8s_client_go//tools/clientcmd",
        "@io_k8s_client_go//util/homedir",
    ],
)
