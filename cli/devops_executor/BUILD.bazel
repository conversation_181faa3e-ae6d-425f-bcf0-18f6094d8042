load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "devops_executor_lib",
    srcs = ["main.go"],
    importpath = "github.com/MoeGolibrary/moego/cli/devops_executor",
    visibility = ["//visibility:private"],
    deps = ["//cli/devops_executor/cmd"],
)

go_binary(
    name = "devops_executor",
    embed = [":devops_executor_lib"],
    visibility = ["//visibility:public"],
    x_defs = {
        # "github.com/MoeGolibrary/moego/cli/devops_executor/cmd.Author": "{STABLE_USER_NAME}",
        "github.com/MoeGolibrary/moego/cli/devops_executor/cmd.GitCommit": "{STABLE_GIT_COMMIT}",
        "github.com/MoeGolibrary/moego/cli/devops_executor/cmd.BuildDate": "{STABLE_DATE}",
    },
)
