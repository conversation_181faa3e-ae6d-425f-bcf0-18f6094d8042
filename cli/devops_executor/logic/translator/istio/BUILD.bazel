load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "istio",
    srcs = [
        "grpc_transcoder.go",
        "virtual_service.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/cli/devops_executor/logic/translator/istio",
    visibility = ["//visibility:public"],
    deps = [
        "//cli/devops_executor/config",
        "//cli/devops_executor/utils",
        "@com_github_samber_lo//:lo",
        "@io_k8s_sigs_kustomize_kyaml//yaml",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/descriptorpb",
    ],
)
