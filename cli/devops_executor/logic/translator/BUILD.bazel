load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "translator",
    srcs = [
        "file.go",
        "kustomization.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/cli/devops_executor/logic/translator",
    visibility = ["//visibility:public"],
    deps = [
        "//cli/devops_executor/config",
        "//cli/devops_executor/utils",
        "//cli/devops_executor/utils/aws",
        "//cli/devops_executor/utils/fs",
        "//cli/devops_executor/utils/k8s",
        "//cli/devops_executor/utils/reader",
        "@com_github_aws_aws_sdk_go_v2_service_secretsmanager//:secretsmanager",
        "@com_github_samber_lo//:lo",
        "@io_k8s_client_go//kubernetes",
        "@io_k8s_sigs_kustomize_kyaml//yaml",
    ],
)

go_test(
    name = "translator_test",
    srcs = [
        "file_test.go",
        "kustomization_test.go",
    ],
    data = glob(["testdata/**"]),
    deps = [
        ":translator",
        "//cli/devops_executor/utils",
        "//cli/devops_executor/utils/aws",
        "//cli/devops_executor/utils/fs",
        "//cli/devops_executor/utils/k8s",
        "//cli/devops_executor/utils/reader",
        "@com_github_aws_aws_sdk_go_v2_service_secretsmanager//:secretsmanager",
        "@com_github_stretchr_testify//require",
        "@io_k8s_client_go//kubernetes",
        "@io_k8s_sigs_yaml//goyaml.v3:goyaml_v3",
    ],
)
